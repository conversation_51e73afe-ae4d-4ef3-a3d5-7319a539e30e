﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: LabMaestroBulkSmsService.cs 1466 2014-10-15 15:27:54Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
#if THREADING_TIMER
using System.Threading;
#else
using System.Timers;
#endif
using LabMaestro.BulkSms.Core;
using NLog;
using Topshelf;

namespace LabMaestro.BulkSms.Service;

public sealed class LabMaestroBulkSmsService : IDisposable, ServiceControl
{
    private readonly Logger _logger = LogManager.GetCurrentClassLogger();
    private const int SECOND = 1000;
    private const int StartupDelay = 5 * SECOND;
    private const int PollingInterval = 30 * SECOND;
    private readonly SmsQueueRunner _queueRunner;
    private int _runCounter;
    private bool _stopped;
    private Timer _timer;
    private bool _disposed;

    public LabMaestroBulkSmsService() => _queueRunner = new SmsQueueRunner(true);

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    public bool Start(HostControl hostControl) => Start();

    public bool Stop(HostControl hostControl) => Stop();

#if THREADING_TIMER
    private void changeTimer(int ms)
    {
        _timer.Change(ms, Timeout.Infinite);
    }

    private void onThreadingTimerElapsed(object state)
    {
        _logger.Debug("timer run");
        changeTimer(-1); //Timeout.Infinite
        if (_stopped) return;

        _queueRunner.Run();
        _runCounter++;

        // retry failed SMS' every 5 minutes
        if (_runCounter % 10 == 0)
            _queueRunner.RetryBads();

        if (_stopped) return;

        changeTimer(PollingInterval);
    }
#endif

    public bool Start()
    {
        _logger.Info("Firing up LabMaestro Bulk SMS Service");
        //GlobalSettingsHelper.BulkSmsPickupFolder = Settings.Default.bulkSmsPickupFolder;

        _queueRunner.SetRootFolderIfNotSet();
        _queueRunner.AssertPickupFolder();
        _queueRunner.AssertBadFolder();
        _logger.Info("SMS Queue Params:\n\tRoot:{0}\n\tPickup:{1}\n\tBad:{2}\n\tThreads:{3}\n\tGateway:{4}",
            _queueRunner.ServiceConfig.SmsQueue.RootFolder,
            _queueRunner.ServiceConfig.SmsQueue.GetPickupFolderPath(),
            _queueRunner.ServiceConfig.SmsQueue.GetBadFolderPath(),
            _queueRunner.ServiceConfig.MaxThreadCount,
            _queueRunner.ServiceConfig.SmsQueue.SmsGateway
        );
        _stopped = false;
#if THREADING_TIMER
        _timer = new Timer(onThreadingTimerElapsed);
        changeTimer(StartupDelay);
#else
        _timer = new Timer(PollingInterval);
        _timer.Elapsed += onTimerElapsed;
        _timer.AutoReset = false;
        _timer.Start();
#endif

        return true;
    }

    private void onTimerElapsed(object sender, ElapsedEventArgs e)
    {
        _logger.Debug("timer run");
        if (_stopped) return;

        _timer.Stop();

        _queueRunner.Run();
        _runCounter++;

        // retry failed SMS' every 5 minutes
        if (_runCounter % 10 == 0)
            _queueRunner.RetryBads();

        if (_stopped) return;

        _timer.Start();
    }

    public bool Stop()
    {
        _logger.Info("Shutting down LabMaestro Bulk SMS Service");
        _stopped = true;
#if THREADING_TIMER
        changeTimer(StartupDelay);
#else
        _timer.Stop();
#endif

        _timer.Dispose();
        _timer = null;
        return true;
    }

    public bool Pause()
    {
        _stopped = true;
#if THREADING_TIMER
        changeTimer(Timeout.Infinite);
#else
        _timer.Stop();
#endif
        return true;
    }

    public bool Continue()
    {
        _stopped = false;
#if THREADING_TIMER
        changeTimer(StartupDelay);
#else
        _timer.Start();
#endif
        return true;
    }

    private void Dispose(bool disposing)
    {
        if (_disposed) return;

        // Dispose managed resources.
        if (disposing)
            _timer?.Dispose();

        // Dispose unmanaged managed resources.
        _disposed = true;
    }
}