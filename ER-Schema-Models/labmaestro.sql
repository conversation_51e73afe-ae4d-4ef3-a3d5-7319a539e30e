/*
 * ER/Studio 8.0 SQL Code Generation
 * Company :      <PERSON><PERSON><PERSON><PERSON>
 * Project :      labmaestro.dm1
 * Author :       <PERSON><PERSON><PERSON><PERSON>
 *
 * Date Created : Wednesday, November 26, 2014 13:05:44
 * Target DBMS : Microsoft SQL Server 2008
 */


/****** 
 * $Id: labmaestro.sql 1519 2014-11-26 07:06:21Z <EMAIL> $ 
******/

USE [master]
GO

/****** Object:  Database [LabMaestro]    Script Date: 11/3/2013 11:55:06 AM ******/
CREATE DATABASE [LabMaestro]
CONTAINMENT = NONE
ON  PRIMARY 
  ( NAME = N'LabMaestro_data', FILENAME = N'F:\LABMAESTRO_OLTP\lm_oltp_data.mdf' , SIZE = 4096KB , MAXSIZE = UNLIMITED, FILEGROWTH = 4096KB ), 
FILEGROUP [FG_Data_13H1] 
  ( NAME = N'LabMaestro_data_13H1', FILENAME = N'F:\LABMAESTRO_OLTP\lm_oltp_data_13H1.ndf' , SIZE = 4096KB , MAXSIZE = UNLIMITED, FILEGROWTH = 4096KB ), 
FILEGROUP [FG_Data_13H2] 
  ( NAME = N'LabMaestro_data_13H2', FILENAME = N'F:\LABMAESTRO_OLTP\lm_oltp_data_13H2.ndf' , SIZE = 4096KB , MAXSIZE = UNLIMITED, FILEGROWTH = 4096KB ), 
FILEGROUP [FG_Data_14H1] 
  ( NAME = N'LabMaestro_data_14H1', FILENAME = N'F:\LABMAESTRO_OLTP\lm_oltp_data_14H1.ndf' , SIZE = 4096KB , MAXSIZE = UNLIMITED, FILEGROWTH = 4096KB ), 
FILEGROUP [FG_Data_14H2] 
  ( NAME = N'LabMaestro_data_14H2', FILENAME = N'F:\LABMAESTRO_OLTP\lm_oltp_data_14H2.ndf' , SIZE = 4096KB , MAXSIZE = UNLIMITED, FILEGROWTH = 4096KB ), 
FILEGROUP [FG_Data_15H1] 
  ( NAME = N'LabMaestro_data_15H1', FILENAME = N'F:\LABMAESTRO_OLTP\lm_oltp_data_15H1.ndf' , SIZE = 4096KB , MAXSIZE = UNLIMITED, FILEGROWTH = 4096KB ), 
FILEGROUP [FG_Data_15H2] 
  ( NAME = N'LabMaestro_data_15H2', FILENAME = N'F:\LABMAESTRO_OLTP\lm_oltp_data_15H2.ndf' , SIZE = 4096KB , MAXSIZE = UNLIMITED, FILEGROWTH = 4096KB ), 
FILEGROUP [FG_Data_16H1] 
  ( NAME = N'LabMaestro_data_16H1', FILENAME = N'F:\LABMAESTRO_OLTP\lm_oltp_data_16H1.ndf' , SIZE = 4096KB , MAXSIZE = UNLIMITED, FILEGROWTH = 4096KB ), 
FILEGROUP [FG_Data_16H2] 
  ( NAME = N'LabMaestro_data_16H2', FILENAME = N'F:\LABMAESTRO_OLTP\lm_oltp_data_16H2.ndf' , SIZE = 4096KB , MAXSIZE = UNLIMITED, FILEGROWTH = 4096KB ), 
FILEGROUP [FG_Data_17H1] 
  ( NAME = N'LabMaestro_data_17H1', FILENAME = N'F:\LABMAESTRO_OLTP\lm_oltp_data_17H1.ndf' , SIZE = 4096KB , MAXSIZE = UNLIMITED, FILEGROWTH = 4096KB ), 
FILEGROUP [FG_Data_17H2] 
  ( NAME = N'LabMaestro_data_17H2', FILENAME = N'F:\LABMAESTRO_OLTP\lm_oltp_data_17H2.ndf' , SIZE = 4096KB , MAXSIZE = UNLIMITED, FILEGROWTH = 4096KB ), 
FILEGROUP [FG_Data_18H1] 
  ( NAME = N'LabMaestro_data_18H1', FILENAME = N'F:\LABMAESTRO_OLTP\lm_oltp_data_18H1.ndf' , SIZE = 4096KB , MAXSIZE = UNLIMITED, FILEGROWTH = 4096KB ), 
FILEGROUP [FG_Data_18H2] 
  ( NAME = N'LabMaestro_data_18H2', FILENAME = N'F:\LABMAESTRO_OLTP\lm_oltp_data_18H2.ndf' , SIZE = 4096KB , MAXSIZE = UNLIMITED, FILEGROWTH = 4096KB ), 
FILEGROUP [FG_Data_19H1] 
  ( NAME = N'LabMaestro_data_19H1', FILENAME = N'F:\LABMAESTRO_OLTP\lm_oltp_data_19H1.ndf' , SIZE = 4096KB , MAXSIZE = UNLIMITED, FILEGROWTH = 4096KB ), 
FILEGROUP [FG_Data_19H2] 
  ( NAME = N'LabMaestro_data_19H2', FILENAME = N'F:\LABMAESTRO_OLTP\lm_oltp_data_19H2.ndf' , SIZE = 4096KB , MAXSIZE = UNLIMITED, FILEGROWTH = 4096KB ), 
FILEGROUP [FG_Data_20H1] 
  ( NAME = N'LabMaestro_data_20H1', FILENAME = N'F:\LABMAESTRO_OLTP\lm_oltp_data_20H1.ndf' , SIZE = 4096KB , MAXSIZE = UNLIMITED, FILEGROWTH = 4096KB ), 
FILEGROUP [FG_Data_20H2] 
  ( NAME = N'LabMaestro_data_20H2', FILENAME = N'F:\LABMAESTRO_OLTP\lm_oltp_data_20H2.ndf' , SIZE = 4096KB , MAXSIZE = UNLIMITED, FILEGROWTH = 4096KB ), 
FILEGROUP [FG_Data_21H1] 
  ( NAME = N'LabMaestro_data_21H1', FILENAME = N'F:\LABMAESTRO_OLTP\lm_oltp_data_21H1.ndf' , SIZE = 4096KB , MAXSIZE = UNLIMITED, FILEGROWTH = 4096KB ), 
FILEGROUP [FG_Data_21H2] 
  ( NAME = N'LabMaestro_data_21H2', FILENAME = N'F:\LABMAESTRO_OLTP\lm_oltp_data_21H2.ndf' , SIZE = 4096KB , MAXSIZE = UNLIMITED, FILEGROWTH = 4096KB ), 
FILEGROUP [FG_Data_22H1] 
  ( NAME = N'LabMaestro_data_22H1', FILENAME = N'F:\LABMAESTRO_OLTP\lm_oltp_data_22H1.ndf' , SIZE = 4096KB , MAXSIZE = UNLIMITED, FILEGROWTH = 4096KB ), 
FILEGROUP [FG_Data_22H2] 
  ( NAME = N'LabMaestro_data_22H2', FILENAME = N'F:\LABMAESTRO_OLTP\lm_oltp_data_22H2.ndf' , SIZE = 4096KB , MAXSIZE = UNLIMITED, FILEGROWTH = 4096KB ), 
FILEGROUP [FG_Data_23H1] 
  ( NAME = N'LabMaestro_data_23H1', FILENAME = N'F:\LABMAESTRO_OLTP\lm_oltp_data_23H1.ndf' , SIZE = 4096KB , MAXSIZE = UNLIMITED, FILEGROWTH = 4096KB ), 
FILEGROUP [FG_Data_23H2] 
  ( NAME = N'LabMaestro_data_23H2', FILENAME = N'F:\LABMAESTRO_OLTP\lm_oltp_data_23H2.ndf' , SIZE = 4096KB , MAXSIZE = UNLIMITED, FILEGROWTH = 4096KB ), 
FILEGROUP [FG_Indices] 
  ( NAME = N'LabMaestro_indices', FILENAME = N'F:\LABMAESTRO_OLTP\lm_oltp_indices.ndf' , SIZE = 4096KB , MAXSIZE = UNLIMITED, FILEGROWTH = 4096KB ), 
FILEGROUP [FG_Static] 
  ( NAME = N'LabMaestro_static', FILENAME = N'F:\LABMAESTRO_OLTP\lm_oltp_static.ndf' , SIZE = 4096KB , MAXSIZE = UNLIMITED, FILEGROWTH = 4096KB )
LOG ON 
  ( NAME = N'LabMaestro_log', FILENAME = N'F:\LABMAESTRO_OLTP\lm_oltp_log.ldf' , SIZE = 4224KB , MAXSIZE = 2048GB , FILEGROWTH = 10%)
GO

ALTER DATABASE [LabMaestro] SET COMPATIBILITY_LEVEL = 110
GO

IF (1 = FULLTEXTSERVICEPROPERTY('IsFullTextInstalled'))
begin
	EXEC [LabMaestro].[dbo].[sp_fulltext_database] @action = 'enable'
end
GO

ALTER DATABASE [LabMaestro] SET  READ_WRITE 
GO

USE [LabMaestro]
GO

BEGIN TRANSACTION

CREATE PARTITION FUNCTION [PF_DateRange](smalldatetime) AS RANGE RIGHT FOR VALUES (
												N'2013-07-01T00:00:00', N'2014-01-01T00:00:00',
												N'2014-07-01T00:00:00', N'2015-01-01T00:00:00',
												N'2015-07-01T00:00:00', N'2016-01-01T00:00:00',
												N'2016-07-01T00:00:00', N'2017-01-01T00:00:00',
												N'2017-07-01T00:00:00', N'2018-01-01T00:00:00',
												N'2018-07-01T00:00:00', N'2019-01-01T00:00:00',
												N'2019-07-01T00:00:00', N'2020-01-01T00:00:00',
												N'2020-07-01T00:00:00', N'2021-01-01T00:00:00',
												N'2021-07-01T00:00:00', N'2022-01-01T00:00:00',
												N'2022-07-01T00:00:00', N'2023-01-01T00:00:00',
												N'2023-07-01T00:00:00', N'2024-01-01T00:00:00')

CREATE PARTITION SCHEME [PS_DateRange] AS PARTITION [PF_DateRange] TO (
												 [FG_Data_13H1],[FG_Data_13H2]
												,[FG_Data_14H1],[FG_Data_14H2]
												,[FG_Data_15H1],[FG_Data_15H2]
												,[FG_Data_16H1],[FG_Data_16H2]
												,[FG_Data_17H1],[FG_Data_17H2]
												,[FG_Data_18H1],[FG_Data_18H2]
												,[FG_Data_19H1],[FG_Data_19H2]
												,[FG_Data_20H1],[FG_Data_20H2]
												,[FG_Data_21H1],[FG_Data_21H2]
												,[FG_Data_22H1],[FG_Data_22H2]
												,[FG_Data_23H1],[FG_Data_23H2]
												,[PRIMARY])

COMMIT TRANSACTION
GO

CREATE FULLTEXT CATALOG [FTCatalog] 
WITH ACCENT_SENSITIVITY = OFF
AS DEFAULT
AUTHORIZATION [dbo]

GO

/* 
 * SCHEMA: APP_SYS 
 */

CREATE SCHEMA [APP_SYS] AUTHORIZATION dbo
go

/* 
 * SCHEMA: Catalog 
 */

CREATE SCHEMA [Catalog] AUTHORIZATION dbo
go

/* 
 * SCHEMA: Finances 
 */

CREATE SCHEMA [Finances] AUTHORIZATION dbo
go

/* 
 * SCHEMA: Marketing 
 */

CREATE SCHEMA [Marketing] AUTHORIZATION dbo
go

/* 
 * SCHEMA: PROE 
 */

CREATE SCHEMA [PROE] AUTHORIZATION dbo
go

/* 
 * SCHEMA: RBAC 
 */

CREATE SCHEMA [RBAC] AUTHORIZATION dbo
go

/* 
 * SCHEMA: Staff 
 */

CREATE SCHEMA [Staff] AUTHORIZATION dbo
go

/* 
 * SCHEMA: SubContract 
 */

CREATE SCHEMA [SubContract] AUTHORIZATION dbo
go

/* 
 * SCHEMA: TestResults 
 */

CREATE SCHEMA [TestResults] AUTHORIZATION dbo
go

/* 
 * TABLE: [APP_SYS].[AppFaults] 
 */

CREATE TABLE [APP_SYS].[AppFaults](
    [Id]                      int                 IDENTITY(1,1),
    [CorrelationId]           uniqueidentifier    NOT NULL,
    [SubmissionTime]          datetime            DEFAULT GETDATE() NOT NULL,
    [EventTime]               datetime            NULL,
    [ItemStatus]              tinyint             DEFAULT 0 NOT NULL,
    [CLRVersion]              varchar(max)        NULL,
    [HostApplication]         varchar(max)        NULL,
    [HostApplicationVersion]  varchar(160)        NULL,
    [ThreadIdentity]          varchar(max)        NULL,
    [WindowsIdentity]         varchar(max)        NULL,
    [MachineName]             varchar(80)         NULL,
    [IPAddresses]             varchar(max)        NULL,
    [ExceptionType]           varchar(max)        NULL,
    [ExceptionMessage]        varchar(max)        NULL,
    [ExceptionSource]         varchar(max)        NULL,
    [ExceptionDetail]         varchar(max)        NULL,
    [ExceptionFormattedData]  varchar(max)        NULL,
    [StackFrames]             varchar(max)        NULL,
    [ScreenCap]               varbinary(max)      NULL,
    CONSTRAINT [PK_APP_FAULTS] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Indices],
    CONSTRAINT [AK_APP_FAULTS]  UNIQUE ([CorrelationId])
    ON [FG_Indices]
)
go



IF OBJECT_ID('APP_SYS.AppFaults') IS NOT NULL
    PRINT '<<< CREATED TABLE APP_SYS.AppFaults >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE APP_SYS.AppFaults >>>'
go

/* 
 * TABLE: [APP_SYS].[AuditTrails] 
 */

CREATE TABLE [APP_SYS].[AuditTrails](
    [Id]                 bigint           IDENTITY(1,1) NOT FOR REPLICATION,
    [PerformingUserId]   smallint         NULL,
    [WorkShiftId]        int              NULL,
    [PatientLabOrderId]  bigint           NULL,
    [OrderedTestId]      bigint           NULL,
    [ResultBundleId]     bigint           NULL,
    [EventTime]          smalldatetime    DEFAULT GETDATE() NOT NULL,
    [EventCategory]      tinyint          NOT NULL,
    [EventType]          smallint         NOT NULL,
    [WorkflowStage]      tinyint          NOT NULL,
    [UserIpAddress]      int              DEFAULT 0 NOT NULL,
    [Note]               varchar(max)     NULL,
    CONSTRAINT [PK_AUDIT_TRAIL] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Indices]
)ON [PS_DateRange]([EventTime])
go



IF OBJECT_ID('APP_SYS.AuditTrails') IS NOT NULL
    PRINT '<<< CREATED TABLE APP_SYS.AuditTrails >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE APP_SYS.AuditTrails >>>'
go

/* 
 * TABLE: [APP_SYS].[ErrorLog] 
 */

CREATE TABLE [APP_SYS].[ErrorLog](
    [Id]             int              IDENTITY(1,1),
    [ErrorTime]      smalldatetime    DEFAULT GETDATE() NOT NULL,
    [Username]       varchar(40)      NOT NULL,
    [UserIpAddress]  int              DEFAULT 0 NOT NULL,
    [ErrorCode]      smallint         NOT NULL,
    [ErrorSeverity]  tinyint          NOT NULL,
    [ErrorMessage]   varchar(max)     NOT NULL,
    CONSTRAINT [PK_ERROR_LOG] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Indices]
)ON [PS_DateRange]([ErrorTime])
go



IF OBJECT_ID('APP_SYS.ErrorLog') IS NOT NULL
    PRINT '<<< CREATED TABLE APP_SYS.ErrorLog >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE APP_SYS.ErrorLog >>>'
go

/* 
 * TABLE: [APP_SYS].[GlobalSettings] 
 */

CREATE TABLE [APP_SYS].[GlobalSettings](
    [Id]           smallint        IDENTITY(1,1),
    [SettingsKey]  varchar(160)    NOT NULL,
    [IntValue]     int             DEFAULT -1 NOT NULL,
    [StrValue]     varchar(max)    NULL,
    CONSTRAINT [PK_GLOBAL_SETTINGS] PRIMARY KEY NONCLUSTERED ([Id])
    ON [FG_Static],
    CONSTRAINT [AK_GLOBAL_SETTINGS]  UNIQUE ([SettingsKey])
    ON [FG_Static]
)ON [FG_Static]
go



IF OBJECT_ID('APP_SYS.GlobalSettings') IS NOT NULL
    PRINT '<<< CREATED TABLE APP_SYS.GlobalSettings >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE APP_SYS.GlobalSettings >>>'
go

/* 
 * TABLE: [APP_SYS].[InvoiceNotifications] 
 */

CREATE TABLE [APP_SYS].[InvoiceNotifications](
    [Id]                bigint      IDENTITY(1,1),
    [InvoiceId]         bigint      NOT NULL,
    [NotificationType]  tinyint     NOT NULL,
    [NotificationCode]  tinyint     NOT NULL,
    [CreatedOn]         datetime    DEFAULT GETDATE() NOT NULL,
    CONSTRAINT [PK_INVOICE_NOTIFICATIONS] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Indices],
    CONSTRAINT [AK_INVOICE_NOTIFICATIONS]  UNIQUE ([NotificationType], [NotificationCode], [InvoiceId])
    ON [FG_Indices]
)
go



IF OBJECT_ID('APP_SYS.InvoiceNotifications') IS NOT NULL
    PRINT '<<< CREATED TABLE APP_SYS.InvoiceNotifications >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE APP_SYS.InvoiceNotifications >>>'
go

/* 
 * TABLE: [APP_SYS].[WorkstationsRegistry] 
 */

CREATE TABLE [APP_SYS].[WorkstationsRegistry](
    [Id]           bigint           IDENTITY(1,1),
    [MACAddress]   varchar(24)      NOT NULL,
    [IPAddress]    int              NOT NULL,
    [LastSeen]     smalldatetime    DEFAULT GETDATE() NOT NULL,
    [AppVersion]   varchar(24)      NOT NULL,
    [CurrentUser]  varchar(24)      NULL,
    [OSVersion]    varchar(max)     NULL,
    [Remarks]      text             NULL,
    CONSTRAINT [PK_WKS_REG] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Indices],
    CONSTRAINT [AK_WKS_REG]  UNIQUE ([MACAddress], [IPAddress])
    ON [FG_Indices]
)
go



IF OBJECT_ID('APP_SYS.WorkstationsRegistry') IS NOT NULL
    PRINT '<<< CREATED TABLE APP_SYS.WorkstationsRegistry >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE APP_SYS.WorkstationsRegistry >>>'
go

/* 
 * TABLE: [Catalog].[AppReportTemplates] 
 */

CREATE TABLE [Catalog].[AppReportTemplates](
    [Id]             smallint            IDENTITY(1,1),
    [ReportCode]     varchar(80)         NOT NULL,
    [ReportContent]  varbinary(max)      NOT NULL,
    [RowGuid]        uniqueidentifier    ROWGUIDCOL DEFAULT NEWSEQUENTIALID() NOT NULL,
    [LastModified]   smalldatetime       DEFAULT GETDATE() NOT NULL,
    CONSTRAINT [PK_SYSTEM_REPORTS] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Static],
    CONSTRAINT [AK_SYSTEM_REPORTS]  UNIQUE ([ReportCode])
    ON [FG_Static]
)ON [FG_Static]
go



IF OBJECT_ID('Catalog.AppReportTemplates') IS NOT NULL
    PRINT '<<< CREATED TABLE Catalog.AppReportTemplates >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE Catalog.AppReportTemplates >>>'
go

/* 
 * TABLE: [Catalog].[BatteryComponents] 
 */

CREATE TABLE [Catalog].[BatteryComponents](
    [Id]               int                 IDENTITY(1,1),
    [BatteryMasterId]  int                 NOT NULL,
    [LabTestId]        smallint            NOT NULL,
    [RowGuid]          uniqueidentifier    ROWGUIDCOL DEFAULT NEWSEQUENTIALID() NOT NULL,
    CONSTRAINT [PK_BATTERYCOMPONENTS] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Static],
    CONSTRAINT [AK_BATTERYCOMPONENTS]  UNIQUE ([BatteryMasterId], [LabTestId])
    ON [FG_Static]
)
go



IF OBJECT_ID('Catalog.BatteryComponents') IS NOT NULL
    PRINT '<<< CREATED TABLE Catalog.BatteryComponents >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE Catalog.BatteryComponents >>>'
go

/* 
 * TABLE: [Catalog].[BatteryMaster] 
 */

CREATE TABLE [Catalog].[BatteryMaster](
    [Id]        int                 IDENTITY(1,1),
    [Name]      varchar(124)        NOT NULL,
    [IsActive]  bit                 DEFAULT 0 NOT NULL,
    [RowGuid]   uniqueidentifier    ROWGUIDCOL DEFAULT NEWSEQUENTIALID() NOT NULL,
    CONSTRAINT [PK_BATTERY] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Static]
)
go



IF OBJECT_ID('Catalog.BatteryMaster') IS NOT NULL
    PRINT '<<< CREATED TABLE Catalog.BatteryMaster >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE Catalog.BatteryMaster >>>'
go

/* 
 * TABLE: [Catalog].[BillableItems] 
 */

CREATE TABLE [Catalog].[BillableItems](
    [Id]         smallint            IDENTITY(1000,10) NOT FOR REPLICATION,
    [Name]       varchar(80)         NOT NULL,
    [IsActive]   bit                 NOT NULL,
    [UnitPrice]  smallmoney          NOT NULL,
    [RowGuid]    uniqueidentifier    ROWGUIDCOL DEFAULT NEWSEQUENTIALID() NOT NULL,
    CHECK ([UnitPrice] >= 0.00),
    CONSTRAINT [PK_BILLABLEITEMS] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Static],
    CONSTRAINT [AK_BILLABLEITEMS_NAME]  UNIQUE ([Name])
    ON [FG_Static],
    CONSTRAINT [AK_BILLABLEITEMS_ROWGUID]  UNIQUE ([RowGuid])
    ON [FG_Static]
)ON [FG_Static]
go



IF OBJECT_ID('Catalog.BillableItems') IS NOT NULL
    PRINT '<<< CREATED TABLE Catalog.BillableItems >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE Catalog.BillableItems >>>'
go

/* 
 * TABLE: [Catalog].[BillOfMaterials] 
 */

CREATE TABLE [Catalog].[BillOfMaterials](
    [Id]            smallint            NOT NULL,
    [Name]          varchar(80)         NOT NULL,
    [UnitPrice]     smallmoney          DEFAULT 0 NOT NULL,
    [Description]   varchar(max)        NULL,
    [IsActive]      bit                 DEFAULT 1 NOT NULL,
    [LastModified]  smalldatetime       DEFAULT GETDATE() NOT NULL,
    [RowGuid]       uniqueidentifier    ROWGUIDCOL DEFAULT NEWSEQUENTIALID() NOT NULL,
    CONSTRAINT [PK_BOM] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_STATIC],
    CONSTRAINT [AK_BOM_ROWGUID]  UNIQUE ([RowGuid])
    ON [FG_STATIC]
)ON [FG_STATIC]
go



IF OBJECT_ID('Catalog.BillOfMaterials') IS NOT NULL
    PRINT '<<< CREATED TABLE Catalog.BillOfMaterials >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE Catalog.BillOfMaterials >>>'
go

/* 
 * TABLE: [Catalog].[CatalogSnapshots] 
 */

CREATE TABLE [Catalog].[CatalogSnapshots](
    [Id]              smallint            IDENTITY(1,1),
    [DateCreated]     smalldatetime       DEFAULT GETDATE() NOT NULL,
    [CreatingUserId]  smallint            NULL,
    [CatalogType]     tinyint             NOT NULL,
    [Remarks]         varchar(max)        NULL,
    [Snapshot]        varbinary(max)      NOT NULL,
    [RowGuid]         uniqueidentifier    ROWGUIDCOL DEFAULT NEWSEQUENTIALID() NOT NULL,
    CONSTRAINT [PK_CATALOG_SNAPSHOTS] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Static],
    CONSTRAINT [AK_CATALOG_SNAPSHOTS_ROWGUID]  UNIQUE ([RowGuid])
    ON [FG_Static]
)ON [FG_Static]
go



IF OBJECT_ID('Catalog.CatalogSnapshots') IS NOT NULL
    PRINT '<<< CREATED TABLE Catalog.CatalogSnapshots >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE Catalog.CatalogSnapshots >>>'
go

/* 
 * TABLE: [Catalog].[DiscountLevels] 
 */

CREATE TABLE [Catalog].[DiscountLevels](
    [Id]               smallint            IDENTITY(1,1),
    [Name]             varchar(40)         NOT NULL,
    [DiscountMode]     tinyint             NOT NULL,
    [DiscountPercent]  float               NOT NULL,
    [DiscountAmount]   smallmoney          NOT NULL,
    [RowGuid]          uniqueidentifier    ROWGUIDCOL DEFAULT NEWSEQUENTIALID() NOT NULL,
    CONSTRAINT [PK_DISCOUNT_LEVELS] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Static],
    CONSTRAINT [AK_DISCOUNT_LEVELS_NAME]  UNIQUE ([Name])
    ON [FG_Static],
    CONSTRAINT [AK_DISCOUNT_LEVELS_ROWGUID]  UNIQUE ([RowGuid])
    ON [FG_Static]
)ON [FG_Static]
go



IF OBJECT_ID('Catalog.DiscountLevels') IS NOT NULL
    PRINT '<<< CREATED TABLE Catalog.DiscountLevels >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE Catalog.DiscountLevels >>>'
go

/* 
 * TABLE: [Catalog].[DiscreteReportLineItems] 
 */

CREATE TABLE [Catalog].[DiscreteReportLineItems](
    [Id]                int                 IDENTITY(100,1) NOT FOR REPLICATION,
    [LabTestId]         smallint            NOT NULL,
    [SortOrder]         tinyint             DEFAULT 0 NOT NULL,
    [IsResultableItem]  bit                 DEFAULT 0 NOT NULL,
    [IndentLevel]       tinyint             DEFAULT 0 NOT NULL,
    [Parameter]         varchar(max)        NOT NULL,
    [DefaultResult]     varchar(max)        NULL,
    [Units]             varchar(max)        NULL,
    [ReferenceRange]    varchar(max)        NULL,
    [RowGuid]           uniqueidentifier    ROWGUIDCOL DEFAULT NEWSEQUENTIALID() NOT NULL,
    CHECK ([IndentLevel] >= (0) AND [IndentLevel] < (10)),
    CONSTRAINT [PK_DISCRETE_REPORT_ITEMS] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Static],
    CONSTRAINT [AK_DISCRETE_REPORT_ITEM_ROWGUID]  UNIQUE ([RowGuid])
    ON [FG_Static]
)ON [FG_Static]
go



IF OBJECT_ID('Catalog.DiscreteReportLineItems') IS NOT NULL
    PRINT '<<< CREATED TABLE Catalog.DiscreteReportLineItems >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE Catalog.DiscreteReportLineItems >>>'
go

/* 
 * TABLE: [Catalog].[LabReportHeaders] 
 */

CREATE TABLE [Catalog].[LabReportHeaders](
    [Id]            int                 IDENTITY(10,10),
    [LabId]         smallint            NOT NULL,
    [IsActive]      bit                 NOT NULL,
    [Name]          varchar(80)         NOT NULL,
    [SortPriority]  tinyint             DEFAULT 0 NOT NULL,
    [RowGuid]       uniqueidentifier    ROWGUIDCOL DEFAULT NEWSEQUENTIALID() NOT NULL,
    [ReportHeader]  varbinary(max)      NOT NULL,
    CONSTRAINT [PK_LAB_REPORT_HEADERS] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Static],
    CONSTRAINT [AK_LAB_REPORT_HEADER_ROWGUID]  UNIQUE ([RowGuid])
    ON [FG_Static],
    CONSTRAINT [AK_LAB_REPORT_HEADER_NAME]  UNIQUE ([Name])
    ON [FG_Static]
)
go



IF OBJECT_ID('Catalog.LabReportHeaders') IS NOT NULL
    PRINT '<<< CREATED TABLE Catalog.LabReportHeaders >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE Catalog.LabReportHeaders >>>'
go

/* 
 * TABLE: [Catalog].[Labs] 
 */

CREATE TABLE [Catalog].[Labs](
    [Id]                                   smallint            IDENTITY(100,10),
    [IsActive]                             bit                 NOT NULL,
    [LabCode]                              varchar(16)         NOT NULL,
    [Name]                                 varchar(80)         NOT NULL,
    [ReqPrintName]                         varchar(40)         NULL,
    [ReqPrintCanonicalTestName]            bit                 NOT NULL,
    [TestResultType]                       tinyint             NOT NULL,
    [IsAuxProcedure]                       bit                 NOT NULL,
    [PostOrderEntryWorkflowStage]          tinyint             NULL,
    [PostResultEntryWorkflowStage]         tinyint             NULL,
    [PostResultVerificationWorkflowStage]  tinyint             NULL,
    [PostResultFinalizationWorkflowStage]  tinyint             NULL,
    [PostReportCollationWorkflowStage]     tinyint             NULL,
    [AccountingGroupDisplayName]           varchar(40)         NULL,
    [ReferralGroupDisplayName]             varchar(40)         NULL,
    [ReferralGroupId]                      smallint            NULL,
    [DiscountLevelId]                      smallint            NULL,
    [TaxLevelId]                           smallint            NULL,
    [SurchargeLevelId]                     smallint            NULL,
    [DefaultReportHeaderId]                int                 NULL,
    [RowGuid]                              uniqueidentifier    ROWGUIDCOL DEFAULT NEWSEQUENTIALID() NOT NULL,
    CONSTRAINT [PK_LABS] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Static],
    CONSTRAINT [AK_LABS_NAME]  UNIQUE ([Name])
    ON [FG_Static],
    CONSTRAINT [AK_LABS_SHORTCODE]  UNIQUE ([LabCode])
    ON [FG_Static],
    CONSTRAINT [AK_LABS_ROWGUID]  UNIQUE ([RowGuid])
    ON [FG_Static]
)ON [FG_Static]
go



IF OBJECT_ID('Catalog.Labs') IS NOT NULL
    PRINT '<<< CREATED TABLE Catalog.Labs >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE Catalog.Labs >>>'
go

/* 
 * TABLE: [Catalog].[LabTests] 
 */

CREATE TABLE [Catalog].[LabTests](
    [Id]                     smallint            IDENTITY(101,1) NOT FOR REPLICATION,
    [IsActive]               bit                 DEFAULT 1 NOT NULL,
    [TestSKU]                varchar(24)         NOT NULL,
    [ShortName]              varchar(160)        NOT NULL,
    [CanonicalName]          varchar(160)        NULL,
    [ListPrice]              smallmoney          NOT NULL,
    [SubOrderPrice]          smallmoney          DEFAULT 0 NOT NULL,
    [CostBasis]              smallmoney          DEFAULT 0 NOT NULL,
    [ReqSlipPrintOption]     tinyint             DEFAULT 0 NOT NULL,
    [HL7AutomationOption]    tinyint             DEFAULT 0 NOT NULL,
    [ReportSortPriority]     tinyint             DEFAULT 0 NOT NULL,
    [DateCreated]            smalldatetime       DEFAULT GETDATE() NOT NULL,
    [LastModified]           smalldatetime       DEFAULT GETDATE() NOT NULL,
    [InactiveDate]           smalldatetime       NULL,
    [Mnemonics]              varchar(160)        NULL,
    [RowGuid]                uniqueidentifier    ROWGUIDCOL DEFAULT NEWSEQUENTIALID() NOT NULL,
    [PerformingLabId]        smallint            NOT NULL,
    [ResultingLabId]         smallint            NOT NULL,
    [DefaultTemplateId]      smallint            NULL,
    [TATGroupId]             smallint            NULL,
    [TemplateGroupId]        smallint            NULL,
    [ReportLineGroupingTag]  varchar(16)         NULL,
    CHECK ([ListPrice] >= (0.00)),
    CONSTRAINT [PK_LABTESTS] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Static],
    CONSTRAINT [AK_LABTEST_TESTSKU]  UNIQUE ([TestSKU])
    ON [FG_Static],
    CONSTRAINT [AK_LABTEST_SHORTNAME]  UNIQUE ([ShortName])
    ON [FG_Static],
    CONSTRAINT [AK_LABTEST_ROWGUID]  UNIQUE ([RowGuid])
    ON [FG_Static]
)ON [FG_Static]
go



IF OBJECT_ID('Catalog.LabTests') IS NOT NULL
    PRINT '<<< CREATED TABLE Catalog.LabTests >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE Catalog.LabTests >>>'
go

/* 
 * TABLE: [Catalog].[ReferrerAddresses] 
 */

CREATE TABLE [Catalog].[ReferrerAddresses](
    [Id]                int                 IDENTITY(1,1) NOT FOR REPLICATION,
    [ReferrerId]        int                 NOT NULL,
    [AddressType]       tinyint             NOT NULL,
    [IsMailingAddress]  bit                 NOT NULL,
    [Address]           varchar(max)        NOT NULL,
    [Notes]             varchar(max)        NULL,
    [RowGuid]           uniqueidentifier    ROWGUIDCOL DEFAULT NEWSEQUENTIALID() NOT NULL,
    CONSTRAINT [PK_REFERRER_ADDRESS] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Static],
    CONSTRAINT [AK_REFERRER_ADDRESS_ROWGUID]  UNIQUE ([RowGuid])
    ON [FG_Static]
)ON [FG_Static]
go



IF OBJECT_ID('Catalog.ReferrerAddresses') IS NOT NULL
    PRINT '<<< CREATED TABLE Catalog.ReferrerAddresses >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE Catalog.ReferrerAddresses >>>'
go

/* 
 * TABLE: [Catalog].[ReferrerCategories] 
 */

CREATE TABLE [Catalog].[ReferrerCategories](
    [Id]        smallint            IDENTITY(1,1),
    [Name]      varchar(40)         NOT NULL,
    [IsActive]  bit                 NOT NULL,
    [RowGuid]   uniqueidentifier    ROWGUIDCOL DEFAULT NEWSEQUENTIALID() NOT NULL,
    CONSTRAINT [PK_REFERRER_CATEGORIES] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Static],
    CONSTRAINT [AK_REFERRER_CATEGORIES_ROWGUID]  UNIQUE ([RowGuid])
    ON FG_Static
)
go



IF OBJECT_ID('Catalog.ReferrerCategories') IS NOT NULL
    PRINT '<<< CREATED TABLE Catalog.ReferrerCategories >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE Catalog.ReferrerCategories >>>'
go

/* 
 * TABLE: [Catalog].[ReferrerCategoryLink] 
 */

CREATE TABLE [Catalog].[ReferrerCategoryLink](
    [Id]           int                 IDENTITY(1,1),
    [RowGuid]      uniqueidentifier    ROWGUIDCOL DEFAULT NEWSEQUENTIALID() NOT NULL,
    [ReferrerId]   int                 NOT NULL,
    [CatergoryId]  smallint            NOT NULL,
    CONSTRAINT [PK_REFERRER_CATEGORY_LINK] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Static],
    CONSTRAINT [AK_REFERRER_CATEGORY_LINK_ROWGUID]  UNIQUE ([RowGuid])
    ON FG_Static,
    CONSTRAINT [AK_REFERRER_CATEGORY_LINK]  UNIQUE ([ReferrerId], [CatergoryId])
    ON FG_Static
)
go



IF OBJECT_ID('Catalog.ReferrerCategoryLink') IS NOT NULL
    PRINT '<<< CREATED TABLE Catalog.ReferrerCategoryLink >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE Catalog.ReferrerCategoryLink >>>'
go

/* 
 * TABLE: [Catalog].[ReferrerPhoneNumbers] 
 */

CREATE TABLE [Catalog].[ReferrerPhoneNumbers](
    [Id]               int                 IDENTITY(1,1),
    [ReferrerId]       int                 NOT NULL,
    [PhoneNumberType]  tinyint             NOT NULL,
    [PhoneNumber]      varchar(40)         NOT NULL,
    [Notes]            varchar(max)        NULL,
    [RowGuid]          uniqueidentifier    ROWGUIDCOL DEFAULT NEWSEQUENTIALID() NOT NULL,
    CONSTRAINT [PK_REFERRER_PHONES] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Static],
    CONSTRAINT [AK_REFERRER_PHONE_ROWGUID]  UNIQUE ([RowGuid])
    ON [FG_Static]
)
go



IF OBJECT_ID('Catalog.ReferrerPhoneNumbers') IS NOT NULL
    PRINT '<<< CREATED TABLE Catalog.ReferrerPhoneNumbers >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE Catalog.ReferrerPhoneNumbers >>>'
go

/* 
 * TABLE: [Catalog].[Referrers] 
 */

CREATE TABLE [Catalog].[Referrers](
    [Id]                   int                 IDENTITY(1,1) NOT FOR REPLICATION,
    [IsActive]             bit                 NOT NULL,
    [Prefix]               varchar(40)         NULL,
    [Name]                 varchar(160)        NOT NULL,
    [Suffix]               varchar(160)        NULL,
    [IdentifyingTag]       varchar(40)         NULL,
    [MobilePhone]          varchar(20)         NULL,
    [Email]                varchar(40)         NULL,
    [SuppressNetReferral]  bit                 DEFAULT 0 NOT NULL,
    [WebLoginEnabled]      bit                 DEFAULT 0 NOT NULL,
    [WebLoginId]           varchar(40)         DEFAULT ' ' NOT NULL,
    [WebPassKey]           varchar(40)         NULL,
    [LastUpdated]          smalldatetime       DEFAULT GETDATE() NOT NULL,
    [LastUpdatedByUserId]  smallint            NULL,
    [RowGuid]              uniqueidentifier    ROWGUIDCOL DEFAULT NEWSEQUENTIALID() NOT NULL,
    [MarketingExecId]      smallint            NULL,
    [ReferralClassId]      smallint            NULL,
    [FullName]             AS                  RTRIM(LTRIM(COALESCE([Prefix], ' ') + ' ' + COALESCE([Name], ' ') + ' ' + COALESCE([Suffix], ' '))),
    CONSTRAINT [PK_REFERRERS] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Static],
    CONSTRAINT [AK_REFERRER_ROWGUID]  UNIQUE ([RowGuid])
    ON [FG_Static],
    CONSTRAINT [AK_REFERRER_WEBLOGINID]  UNIQUE ([WebLoginId])
    ON [FG_STATIC]
)ON [FG_Static]
go



IF OBJECT_ID('Catalog.Referrers') IS NOT NULL
    PRINT '<<< CREATED TABLE Catalog.Referrers >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE Catalog.Referrers >>>'
go

/* 
 * TABLE: [Catalog].[ReqParameters] 
 */

CREATE TABLE [Catalog].[ReqParameters](
    [Id]           int                 IDENTITY(1,1) NOT FOR REPLICATION,
    [SortOrder]    tinyint             NOT NULL,
    [Parameter]    varchar(160)        NOT NULL,
    [IndentLevel]  tinyint             NOT NULL,
    [LabTestId]    smallint            NOT NULL,
    [RowGuid]      uniqueidentifier    ROWGUIDCOL DEFAULT NEWSEQUENTIALID() NOT NULL,
    CHECK ([IndentLevel] >= (0) AND [IndentLevel] < (10)),
    CONSTRAINT [PK_REQ_PARAMETERS] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Static],
    CONSTRAINT [AK_REQ_PARAMETER_ROWGUID]  UNIQUE ([RowGuid])
    ON [FG_Static]
)ON [FG_Static]
go



IF OBJECT_ID('Catalog.ReqParameters') IS NOT NULL
    PRINT '<<< CREATED TABLE Catalog.ReqParameters >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE Catalog.ReqParameters >>>'
go

/* 
 * TABLE: [Catalog].[SandboxedGroupLabsLink] 
 */

CREATE TABLE [Catalog].[SandboxedGroupLabsLink](
    [Id]       smallint            IDENTITY(1,1),
    [GroupId]  smallint            NOT NULL,
    [LabId]    smallint            NOT NULL,
    [RowGuid]  uniqueidentifier    ROWGUIDCOL DEFAULT NEWSEQUENTIALID() NOT NULL,
    CONSTRAINT [PK_SANDBOX_LAB_LINK] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Static]
)ON [FG_Static]
go



IF OBJECT_ID('Catalog.SandboxedGroupLabsLink') IS NOT NULL
    PRINT '<<< CREATED TABLE Catalog.SandboxedGroupLabsLink >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE Catalog.SandboxedGroupLabsLink >>>'
go

/* 
 * TABLE: [Catalog].[SandboxedLabGroups] 
 */

CREATE TABLE [Catalog].[SandboxedLabGroups](
    [Id]       smallint            IDENTITY(1,1) NOT FOR REPLICATION,
    [Name]     varchar(40)         NOT NULL,
    [RowGuid]  uniqueidentifier    ROWGUIDCOL DEFAULT NEWSEQUENTIALID() NOT NULL,
    CONSTRAINT [PK_SANDBOXED_LABGROUPS] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Static],
    CONSTRAINT [AK_SANDBOXED_LABGROUPS]  UNIQUE ([Name])
    ON [FG_Static],
    CONSTRAINT [AK_SANDBOXED_LABGROUPS_ROWGUID]  UNIQUE ([RowGuid])
    ON [FG_Static]
)ON [FG_Static]
go



IF OBJECT_ID('Catalog.SandboxedLabGroups') IS NOT NULL
    PRINT '<<< CREATED TABLE Catalog.SandboxedLabGroups >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE Catalog.SandboxedLabGroups >>>'
go

/* 
 * TABLE: [Catalog].[SurchargeLevels] 
 */

CREATE TABLE [Catalog].[SurchargeLevels](
    [Id]             smallint            IDENTITY(1,1),
    [Name]           varchar(40)         NOT NULL,
    [IsActive]       bit                 NOT NULL,
    [SurchargeRate]  float               NOT NULL,
    [LastModified]   smalldatetime       DEFAULT GETDATE() NOT NULL,
    [RowGuid]        uniqueidentifier    ROWGUIDCOL DEFAULT NEWSEQUENTIALID() NOT NULL,
    CONSTRAINT [PK_SURCHARGE_LEVELS] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Static]
)ON [FG_Static]
go



IF OBJECT_ID('Catalog.SurchargeLevels') IS NOT NULL
    PRINT '<<< CREATED TABLE Catalog.SurchargeLevels >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE Catalog.SurchargeLevels >>>'
go

/* 
 * TABLE: [Catalog].[TATGroups] 
 */

CREATE TABLE [Catalog].[TATGroups](
    [Id]                   smallint            IDENTITY(1,1) NOT FOR REPLICATION,
    [Name]                 varchar(20)         NOT NULL,
    [TATRank]              tinyint             DEFAULT 0 NOT NULL,
    [DaysRequired]         smallint            DEFAULT 0 NOT NULL,
    [HoursRequired]        smallint            DEFAULT 0 NOT NULL,
    [MorningSlabBegin]     float               DEFAULT 8 NOT NULL,
    [MorningSlabEnd]       float               DEFAULT 13 NOT NULL,
    [MorningDeliveryHour]  float               DEFAULT 10 NOT NULL,
    [NoonSlabBegin]        float               DEFAULT 13 NOT NULL,
    [NoonSlabEnd]          float               DEFAULT 18 NOT NULL,
    [NoonDeliveryHour]     float               DEFAULT 16 NOT NULL,
    [EveningSlabBegin]     float               DEFAULT 19 NOT NULL,
    [EveningSlabEnd]       float               DEFAULT 22 NOT NULL,
    [EveningDeliveryHour]  float               DEFAULT 21 NOT NULL,
    [RowGuid]              uniqueidentifier    ROWGUIDCOL DEFAULT NEWSEQUENTIALID() NOT NULL,
    CHECK ([MorningSlabBegin] >= 6 AND [MorningSlabBegin] < [MorningSlabEnd]),
    CHECK ([MorningSlabEnd] > [MorningSlabBegin] AND [MorningSlabEnd] < [NoonSlabBegin]),
    CHECK ([MorningDeliveryHour] >= [MorningSlabBegin] AND [MorningDeliveryHour] <= [MorningSlabEnd]),
    CHECK ([NoonSlabBegin] > [MorningSlabEnd] AND [NoonSlabBegin] < [NoonSlabEnd]),
    CHECK ([NoonSlabEnd] > [NoonSlabBegin] AND [NoonSlabEnd] < [EveningSlabBegin]),
    CHECK ([NoonDeliveryHour] >= NoonSlabBegin AND [NoonDeliveryHour] <= NoonSlabEnd),
    CHECK ([EveningSlabBegin] > [NoonSlabEnd] AND [EveningSlabBegin] < [EveningSlabEnd]),
    CHECK ([EveningSlabEnd] > [EveningSlabBegin] AND [EveningSlabEnd] <= 24),
    CHECK ([EveningDeliveryHour] >= [EveningSlabBegin] AND [EveningDeliveryHour] <= [EveningSlabEnd]),
    CONSTRAINT [PK_TAT_GROUPS] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Static],
    CONSTRAINT [AK_TAT_GROUP_ROWGUID]  UNIQUE ([RowGuid])
    ON [FG_Static],
    CONSTRAINT [AK_TAT_GROUP_NAME]  UNIQUE ([Name])
    ON [FG_Static]
)ON [FG_Static]
go



IF OBJECT_ID('Catalog.TATGroups') IS NOT NULL
    PRINT '<<< CREATED TABLE Catalog.TATGroups >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE Catalog.TATGroups >>>'
go

/* 
 * TABLE: [Catalog].[TaxLevels] 
 */

CREATE TABLE [Catalog].[TaxLevels](
    [Id]            smallint            IDENTITY(1,1),
    [Name]          varchar(40)         NOT NULL,
    [IsActive]      bit                 NOT NULL,
    [TaxRate]       float               NOT NULL,
    [LastModified]  smalldatetime       DEFAULT GETDATE() NOT NULL,
    [RowGuid]       uniqueidentifier    ROWGUIDCOL DEFAULT NEWSEQUENTIALID() NOT NULL,
    CONSTRAINT [PK_TAX_LEVELS] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Static]
)ON [FG_Static]
go



IF OBJECT_ID('Catalog.TaxLevels') IS NOT NULL
    PRINT '<<< CREATED TABLE Catalog.TaxLevels >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE Catalog.TaxLevels >>>'
go

/* 
 * TABLE: [Catalog].[TemplateGroupLinks] 
 */

CREATE TABLE [Catalog].[TemplateGroupLinks](
    [Id]          smallint            IDENTITY(1,1),
    [GroupId]     smallint            NOT NULL,
    [TemplateId]  smallint            NOT NULL,
    [RowGuid]     uniqueidentifier    ROWGUIDCOL DEFAULT NEWSEQUENTIALID() NOT NULL,
    CONSTRAINT [PK_TEMPLATE_GROUP] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Static],
    CONSTRAINT [AK_TEMPLATE_GROUP_LINKS]  UNIQUE ([GroupId], [TemplateId])
    ON [FG_Static],
    CONSTRAINT [AK_TEMPLATE_GROUP_LINK_ROWGUID]  UNIQUE ([RowGuid])
    ON [FG_Static]
)
go



IF OBJECT_ID('Catalog.TemplateGroupLinks') IS NOT NULL
    PRINT '<<< CREATED TABLE Catalog.TemplateGroupLinks >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE Catalog.TemplateGroupLinks >>>'
go

/* 
 * TABLE: [Catalog].[TemplateGroups] 
 */

CREATE TABLE [Catalog].[TemplateGroups](
    [Id]        smallint            IDENTITY(1,1),
    [IsActive]  bit                 NOT NULL,
    [Name]      varchar(40)         NOT NULL,
    [RowGuid]   uniqueidentifier    ROWGUIDCOL DEFAULT NEWSEQUENTIALID() NOT NULL,
    [LabId]     smallint            NOT NULL,
    CONSTRAINT [PK_TEMPLATE_GROUPS] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Static],
    CONSTRAINT [AK_TEMPLATE_GROUP_NAME]  UNIQUE ([Name])
    ON [FG_Static],
    CONSTRAINT [AK_TEMPLATE_GROUP_ROWGUID]  UNIQUE ([RowGuid])
    ON [FG_Static]
)ON [FG_Static]
go



IF OBJECT_ID('Catalog.TemplateGroups') IS NOT NULL
    PRINT '<<< CREATED TABLE Catalog.TemplateGroups >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE Catalog.TemplateGroups >>>'
go

/* 
 * TABLE: [Catalog].[TemplateReports] 
 */

CREATE TABLE [Catalog].[TemplateReports](
    [Id]            smallint            IDENTITY(1,1) NOT FOR REPLICATION,
    [IsActive]      bit                 NOT NULL,
    [Name]          varchar(160)        NOT NULL,
    [SortPriority]  tinyint             NOT NULL,
    [Tags]          varchar(160)        NULL,
    [RowGuid]       uniqueidentifier    ROWGUIDCOL DEFAULT NEWSEQUENTIALID() NOT NULL,
    [Content]       varbinary(max)      NOT NULL,
    CONSTRAINT [PK_TEMPLATE_REPORTS] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Static],
    CONSTRAINT [AK_TEMPLATE_REPORTS]  UNIQUE ([RowGuid])
    ON [FG_Static]
)ON [FG_Static]
go



IF OBJECT_ID('Catalog.TemplateReports') IS NOT NULL
    PRINT '<<< CREATED TABLE Catalog.TemplateReports >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE Catalog.TemplateReports >>>'
go

/* 
 * TABLE: [Catalog].[TestBILink] 
 */

CREATE TABLE [Catalog].[TestBILink](
    [Id]                 int                 IDENTITY(1,1) NOT FOR REPLICATION,
    [TestId]             smallint            NOT NULL,
    [BillableItemId]     smallint            NOT NULL,
    [Quantity]           smallint            NOT NULL,
    [OptimizationLevel]  tinyint             NOT NULL,
    [RowGuid]            uniqueidentifier    ROWGUIDCOL DEFAULT NEWSEQUENTIALID() NOT NULL,
    CHECK ([Quantity] >= (0)),
    CONSTRAINT [PK_TEST_BI_LINK] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Static],
    CONSTRAINT [AK_TEST_BI_LINK]  UNIQUE ([TestId], [BillableItemId], [OptimizationLevel])
    ON [FG_Static],
    CONSTRAINT [AK_TEST_BI_LINK_ROWGUID]  UNIQUE ([RowGuid])
    ON [FG_Static]
)ON [FG_Static]
go



IF OBJECT_ID('Catalog.TestBILink') IS NOT NULL
    PRINT '<<< CREATED TABLE Catalog.TestBILink >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE Catalog.TestBILink >>>'
go

/* 
 * TABLE: [Catalog].[TestBoMLink] 
 */

CREATE TABLE [Catalog].[TestBoMLink](
    [Id]         int                 IDENTITY(1,1),
    [LabTestId]  smallint            NOT NULL,
    [BoMId]      smallint            NOT NULL,
    [Quantity]   smallint            DEFAULT 0 NOT NULL,
    [RowGuid]    uniqueidentifier    ROWGUIDCOL DEFAULT NEWSEQUENTIALID() NOT NULL,
    CONSTRAINT [PK_TESTBOM] PRIMARY KEY NONCLUSTERED ([Id])
    ON [FG_STATIC],
    CONSTRAINT [AK_TESTBOM_ROWGUID]  UNIQUE ([RowGuid])
    ON [FG_STATIC]
)ON [FG_STATIC]
go



IF OBJECT_ID('Catalog.TestBoMLink') IS NOT NULL
    PRINT '<<< CREATED TABLE Catalog.TestBoMLink >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE Catalog.TestBoMLink >>>'
go

/* 
 * TABLE: [Catalog].[UserTemplateGroupLinks] 
 */

CREATE TABLE [Catalog].[UserTemplateGroupLinks](
    [Id]               smallint            IDENTITY(1,1),
    [UserId]           smallint            NOT NULL,
    [TemplateGroupId]  smallint            NOT NULL,
    [RowGuid]          uniqueidentifier    ROWGUIDCOL DEFAULT NEWSEQUENTIALID() NOT NULL,
    CONSTRAINT [PK_USER_TEMPLATEGROUP_LINKS] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Static],
    CONSTRAINT [AK_USER_TEMPLATEGROUP_LINKS]  UNIQUE ([UserId], [TemplateGroupId])
    ON [FG_Static],
    CONSTRAINT [AK_USER_TEMPLATEGROUP_ROWGUID]  UNIQUE ([RowGuid])
    ON [FG_Static]
)ON [FG_Static]
go



IF OBJECT_ID('Catalog.UserTemplateGroupLinks') IS NOT NULL
    PRINT '<<< CREATED TABLE Catalog.UserTemplateGroupLinks >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE Catalog.UserTemplateGroupLinks >>>'
go

/* 
 * TABLE: [Catalog].[UserTemplateReports] 
 */

CREATE TABLE [Catalog].[UserTemplateReports](
    [Id]                   smallint            NOT NULL,
    [TemplateGroupLinkId]  smallint            NOT NULL,
    [Name]                 varchar(160)        NOT NULL,
    [IsActive]             bit                 NOT NULL,
    [SortPriority]         tinyint             NOT NULL,
    [RowGuid]              uniqueidentifier    ROWGUIDCOL DEFAULT NEWSEQUENTIALID() NOT NULL,
    [Tags]                 varchar(160)        NULL,
    [Content]              varbinary(max)      NOT NULL,
    CONSTRAINT [PK_USER_TEMPLATE_REPORTS] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Static],
    CONSTRAINT [AK_USER_TEMPLATE_REPORTS]  UNIQUE ([TemplateGroupLinkId], [Name])
    ON [FG_Static],
    CONSTRAINT [AK_USER_TEMPLATE_REPORTS_ROWGUID]  UNIQUE ([RowGuid])
    ON [FG_Static]
)ON [FG_Static]
go



IF OBJECT_ID('Catalog.UserTemplateReports') IS NOT NULL
    PRINT '<<< CREATED TABLE Catalog.UserTemplateReports >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE Catalog.UserTemplateReports >>>'
go

/* 
 * TABLE: [Finances].[InvoiceMaster] 
 */

CREATE TABLE [Finances].[InvoiceMaster](
    [InvoiceId]        bigint           NOT NULL,
    [DateCreated]      smalldatetime    DEFAULT GETDATE() NOT NULL,
    [PaymentStatus]    tinyint          DEFAULT 0 NOT NULL,
    [GrossPayable]     money            DEFAULT 0 NOT NULL,
    [DiscountAmount]   money            DEFAULT 0 NOT NULL,
    [TaxAmount]        money            DEFAULT 0 NOT NULL,
    [SurchargeAmount]  money            DEFAULT 0 NOT NULL,
    [NetPayable]       money            DEFAULT 0 NOT NULL,
    [PaidAmount]       money            DEFAULT 0 NOT NULL,
    [DueAmount]        money            DEFAULT 0 NOT NULL,
    [RefundAmount]     money            DEFAULT 0 NOT NULL,
    CHECK ([GrossPayable] >= (0.00)),
    CHECK ([DiscountAmount] >= (0.00)),
    CHECK ([TaxAmount] >= (0.00)),
    CHECK ([NetPayable] >= (0.00)),
    CHECK ([PaidAmount] >= (0.00)),
    CHECK ([DueAmount] >= (0.00)),
    CHECK ([SurchargeAmount] >= (0.00)),
    CHECK ([RefundAmount] >= (0.00)),
    CONSTRAINT [PK_INVOICE_MASTER] PRIMARY KEY CLUSTERED ([InvoiceId])
    ON [FG_Indices]
)ON [PS_DateRange]([DateCreated])
go



IF OBJECT_ID('Finances.InvoiceMaster') IS NOT NULL
    PRINT '<<< CREATED TABLE Finances.InvoiceMaster >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE Finances.InvoiceMaster >>>'
go

/* 
 * TABLE: [Finances].[InvoicePrimal] 
 */

CREATE TABLE [Finances].[InvoicePrimal](
    [InvoiceId]        bigint           NOT NULL,
    [DateCreated]      smalldatetime    DEFAULT GETDATE() NOT NULL,
    [GrossPayable]     money            DEFAULT 0 NOT NULL,
    [DiscountAmount]   money            DEFAULT 0 NOT NULL,
    [TaxAmount]        money            DEFAULT 0 NOT NULL,
    [SurchargeAmount]  money            DEFAULT 0 NOT NULL,
    [NetPayable]       money            DEFAULT 0 NOT NULL,
    [PaidAmount]       money            DEFAULT 0 NOT NULL,
    [DueAmount]        money            DEFAULT 0 NOT NULL,
    [RefundAmount]     money            DEFAULT 0 NOT NULL,
    CONSTRAINT [PK_INVOICE_PRIMAL] PRIMARY KEY CLUSTERED ([InvoiceId])
    ON [FG_Indices]
)ON [PS_DateRange]([DateCreated])
go



IF OBJECT_ID('Finances.InvoicePrimal') IS NOT NULL
    PRINT '<<< CREATED TABLE Finances.InvoicePrimal >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE Finances.InvoicePrimal >>>'
go

/* 
 * TABLE: [Finances].[InvoiceTransactions] 
 */

CREATE TABLE [Finances].[InvoiceTransactions](
    [Id]                 bigint           IDENTITY(1,1) NOT FOR REPLICATION,
    [InvoiceId]          bigint           NOT NULL,
    [PerformingUserId]   smallint         NULL,
    [WorkShiftId]        int              NULL,
    [AuthorizingUserId]  smallint         NULL,
    [TxTime]             smalldatetime    DEFAULT GETDATE() NOT NULL,
    [TxType]             tinyint          NOT NULL,
    [TxFlag]             tinyint          DEFAULT 0 NOT NULL,
    [TxAmount]           money            NOT NULL,
    [UserIpAddress]      int              NULL,
    [UserRemarks]        varchar(160)     NULL,
    CHECK ([TxAmount] >= (0.00)),
    CONSTRAINT [PK_INVOICE_TRANSACTIONS] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Indices]
)ON [PS_DateRange]([TxTime])
go



IF OBJECT_ID('Finances.InvoiceTransactions') IS NOT NULL
    PRINT '<<< CREATED TABLE Finances.InvoiceTransactions >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE Finances.InvoiceTransactions >>>'
go

/* 
 * TABLE: [Finances].[WorkShifts] 
 */

CREATE TABLE [Finances].[WorkShifts](
    [Id]                    int              IDENTITY(1,1) NOT FOR REPLICATION,
    [UserId]                smallint         NOT NULL,
    [IsClosed]              bit              NOT NULL,
    [StartTime]             smalldatetime    DEFAULT GETDATE() NOT NULL,
    [EndTime]               smalldatetime    NULL,
    [LastUpdated]           smalldatetime    DEFAULT GETDATE() NOT NULL,
    [NumOrders]             smallint         NOT NULL,
    [AdditionalBalance]     money            NOT NULL,
    [ReceiveAmount]         money            NOT NULL,
    [DiscountAmount]        money            NOT NULL,
    [DiscountRebateAmount]  money            NOT NULL,
    [RefundAmount]          money            NOT NULL,
    [FinalBalance]          money            NOT NULL,
    [UserNotes]             varchar(max)     NULL,
    CHECK ([EndTime] >= [StartTime] OR [EndTime] IS NULL),
    CHECK ([NumOrders] >= (0)),
    CHECK ([AdditionalBalance] >= (0.00)),
    CHECK ([ReceiveAmount] >= (0.00)),
    CHECK ([DiscountAmount] >= (0.00)),
    CHECK ([RefundAmount] >= (0.00)),
    CHECK ([FinalBalance] >= (0.00)),
    CHECK ([DiscountRebateAmount] >= (0.00)),
    CONSTRAINT [PK_WORKSHIFTS] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Indices]
)ON [PS_DateRange]([StartTime])
go



IF OBJECT_ID('Finances.WorkShifts') IS NOT NULL
    PRINT '<<< CREATED TABLE Finances.WorkShifts >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE Finances.WorkShifts >>>'
go

/* 
 * TABLE: [Marketing].[MarketingExecs] 
 */

CREATE TABLE [Marketing].[MarketingExecs](
    [Id]       smallint            NOT NULL,
    [RowGuid]  uniqueidentifier    ROWGUIDCOL DEFAULT NEWSEQUENTIALID() NOT NULL,
    CONSTRAINT [PK_MARKETING_EXEC] PRIMARY KEY NONCLUSTERED ([Id])
    ON [FG_Static]
)
go



IF OBJECT_ID('Marketing.MarketingExecs') IS NOT NULL
    PRINT '<<< CREATED TABLE Marketing.MarketingExecs >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE Marketing.MarketingExecs >>>'
go

/* 
 * TABLE: [Marketing].[ReferralClasses] 
 */

CREATE TABLE [Marketing].[ReferralClasses](
    [Id]                smallint            IDENTITY(1,1),
    [Name]              varchar(40)         NOT NULL,
    [ReferralEligible]  bit                 DEFAULT 0 NOT NULL,
    [RowGuid]           uniqueidentifier    ROWGUIDCOL DEFAULT NEWSEQUENTIALID() NOT NULL,
    CONSTRAINT [PK_REFERRAL_CLASSES] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Static],
    CONSTRAINT [AK_REFERRAL_CLASS_NAME]  UNIQUE ([Name])
    ON [FG_Static],
    CONSTRAINT [AK_REFERRAL_CLASS_ROWGUID]  UNIQUE ([RowGuid])
    ON [FG_Static]
)
go



IF OBJECT_ID('Marketing.ReferralClasses') IS NOT NULL
    PRINT '<<< CREATED TABLE Marketing.ReferralClasses >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE Marketing.ReferralClasses >>>'
go

/* 
 * TABLE: [Marketing].[ReferralGroups] 
 */

CREATE TABLE [Marketing].[ReferralGroups](
    [Id]               smallint            IDENTITY(100,10) NOT FOR REPLICATION,
    [IsActive]         bit                 NOT NULL,
    [Name]             varchar(40)         NOT NULL,
    [ReferralMode]     tinyint             NOT NULL,
    [ReferralPercent]  float               NOT NULL,
    [ReferralAmount]   smallmoney          NOT NULL,
    [RowGuid]          uniqueidentifier    ROWGUIDCOL DEFAULT NEWSEQUENTIALID() NOT NULL,
    CONSTRAINT [CK_REF_PCT] CHECK ([ReferralPercent] BETWEEN 0 AND 100),
    CONSTRAINT [PK_REFERRAL_GROUPS] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Static],
    CONSTRAINT [AK_REFERRAL_GROUPS]  UNIQUE ([Name])
    ON [FG_Static],
    CONSTRAINT [AK_REFERRAL_GROUP_ROWGUID]  UNIQUE ([RowGuid])
    ON [FG_Static]
)ON [FG_Static]
go



IF OBJECT_ID('Marketing.ReferralGroups') IS NOT NULL
    PRINT '<<< CREATED TABLE Marketing.ReferralGroups >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE Marketing.ReferralGroups >>>'
go

/* 
 * TABLE: [Marketing].[ReferralPeriods] 
 */

CREATE TABLE [Marketing].[ReferralPeriods](
    [Id]              int              IDENTITY(1,1),
    [PeriodStart]     smalldatetime    NOT NULL,
    [PeriodEnd]       smalldatetime    NOT NULL,
    [PeriodYYMM]      smallint         DEFAULT 0 NOT NULL,
    [NumOrders]       int              DEFAULT 0 NOT NULL,
    [GrossBill]       money            DEFAULT 0 NOT NULL,
    [Discount]        money            DEFAULT 0 NOT NULL,
    [NetBill]         money            DEFAULT 0 NOT NULL,
    [ReferralAmount]  money            DEFAULT 0 NOT NULL,
    CONSTRAINT [PK_REFERRAL_PERIODS] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Indices]
)
go



IF OBJECT_ID('Marketing.ReferralPeriods') IS NOT NULL
    PRINT '<<< CREATED TABLE Marketing.ReferralPeriods >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE Marketing.ReferralPeriods >>>'
go

/* 
 * TABLE: [PROE].[HeldLabOrders] 
 */

CREATE TABLE [PROE].[HeldLabOrders](
    [Id]                  bigint           IDENTITY(1,1) NOT FOR REPLICATION,
    [DateCreated]         smalldatetime    DEFAULT GETDATE() NOT NULL,
    [Title]               varchar(20)      NULL,
    [FirstName]           varchar(120)     NULL,
    [LastName]            varchar(120)     NULL,
    [Sex]                 tinyint          NOT NULL,
    [Age]                 varchar(20)      NULL,
    [DoB]                 date             NULL,
    [IsReferrerUnknown]   bit              NOT NULL,
    [PhoneNumber]         varchar(20)      NULL,
    [ReferrerCustomName]  varchar(160)     NULL,
    [Notes]               varchar(160)     NULL,
    [WorkShiftId]         int              NOT NULL,
    [ReferrerId]          int              NULL,
    [FullName]            AS               LTRIM(RTRIM(COALESCE([FirstName], ' ') + ' ' + COALESCE([LastName], ' '))),
    CONSTRAINT [PK_HELD_ORDERS] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Indices]
)ON [PS_DateRange]([DateCreated])
go



IF OBJECT_ID('PROE.HeldLabOrders') IS NOT NULL
    PRINT '<<< CREATED TABLE PROE.HeldLabOrders >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE PROE.HeldLabOrders >>>'
go

/* 
 * TABLE: [PROE].[HeldLabOrderTests] 
 */

CREATE TABLE [PROE].[HeldLabOrderTests](
    [Id]              bigint           IDENTITY(1,1),
    [HeldLabOrderId]  bigint           NOT NULL,
    [WorkShiftId]     int              NOT NULL,
    [LabTestId]       smallint         NOT NULL,
    [ResultsETA]      smalldatetime    NULL,
    [DateCreated]     smalldatetime    DEFAULT GETDATE() NOT NULL,
    CONSTRAINT [PK_HELDLABORDER_TESTS] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Indices]
)ON [PS_DateRange]([DateCreated])
go



IF OBJECT_ID('PROE.HeldLabOrderTests') IS NOT NULL
    PRINT '<<< CREATED TABLE PROE.HeldLabOrderTests >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE PROE.HeldLabOrderTests >>>'
go

/* 
 * TABLE: [PROE].[OrderedBillableItems] 
 */

CREATE TABLE [PROE].[OrderedBillableItems](
    [Id]              bigint           IDENTITY(1,1) NOT FOR REPLICATION,
    [InvoiceId]       bigint           NOT NULL,
    [BillableItemId]  smallint         NOT NULL,
    [UnitPrice]       smallmoney       NOT NULL,
    [Quantity]        smallint         NOT NULL,
    [DateCreated]     smalldatetime    DEFAULT GETDATE() NOT NULL,
    [IsCancelled]     bit              NOT NULL,
    CONSTRAINT [PK_BILLABLE_ITEMS_ORDERED] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Indices],
    CONSTRAINT [AX_BILLABLE_ITEMS_ORDERED]  UNIQUE ([InvoiceId], [BillableItemId], [IsCancelled], [DateCreated])
    ON [FG_Indices]
)ON [PS_DateRange]([DateCreated])
go



IF OBJECT_ID('PROE.OrderedBillableItems') IS NOT NULL
    PRINT '<<< CREATED TABLE PROE.OrderedBillableItems >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE PROE.OrderedBillableItems >>>'
go

/* 
 * TABLE: [PROE].[OrderedTests] 
 */

CREATE TABLE [PROE].[OrderedTests](
    [Id]              bigint           IDENTITY(1,1) NOT FOR REPLICATION,
    [InvoiceId]       bigint           NOT NULL,
    [LabTestId]       smallint         NOT NULL,
    [ResultBundleId]  bigint           NULL,
    [IsCancelled]     bit              NOT NULL,
    [UnitPrice]       smallmoney       NOT NULL,
    [WorkflowStage]   tinyint          NOT NULL,
    [DateCreated]     smalldatetime    DEFAULT GETDATE() NOT NULL,
    [LastModified]    smalldatetime    DEFAULT GETDATE() NOT NULL,
    [ResultsETA]      smalldatetime    NULL,
    CONSTRAINT [PK_ORDERED_TESTS] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Indices]
)ON [PS_DateRange]([DateCreated])
go



IF OBJECT_ID('PROE.OrderedTests') IS NOT NULL
    PRINT '<<< CREATED TABLE PROE.OrderedTests >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE PROE.OrderedTests >>>'
go

/* 
 * TABLE: [PROE].[PatientLabOrders] 
 */

CREATE TABLE [PROE].[PatientLabOrders](
    [InvoiceId]           bigint           IDENTITY(1001,1) NOT FOR REPLICATION,
    [OrderId]             varchar(8)       NOT NULL,
    [OrderDateTime]       smalldatetime    DEFAULT getdate() NOT NULL,
    [WorkflowStage]       tinyint          NOT NULL,
    [LastModified]        smalldatetime    DEFAULT GETDATE() NOT NULL,
    [IsCancelled]         bit              NOT NULL,
    [ReferrerId]          int              NULL,
    [DisallowReferral]    bit              DEFAULT 0 NOT NULL,
    [OrderingUserId]      smallint         NULL,
    [WorkShiftId]         int              NULL,
    [RequestingLabId]     int              NULL,
    [Title]               varchar(20)      NULL,
    [FirstName]           varchar(120)     NOT NULL,
    [LastName]            varchar(120)     NULL,
    [Sex]                 tinyint          NOT NULL,
    [Age]                 varchar(20)      NULL,
    [DoB]                 date             NULL,
    [PhoneNumber]         varchar(20)      NULL,
    [EmailAddress]        varchar(40)      NULL,
    [EmailTestResults]    bit              DEFAULT 0 NOT NULL,
    [IsReferrerUnknown]   bit              NOT NULL,
    [ReferrerCustomName]  varchar(160)     NULL,
    [OrderNotes]          varchar(160)     NULL,
    [WebAccessToken]      varchar(8)       NULL,
    [RegisteredMemberId]  bigint           NULL,
    [IsExternalSubOrder]  bit              DEFAULT 0 NOT NULL,
    [SubOrderTrackingId]  varchar(40)      NULL,
    [MirrorFlag]          tinyint          DEFAULT 0 NOT NULL,
    [FullName]            AS               LTRIM(RTRIM(COALESCE([FirstName], ' ') + ' ' + COALESCE([LastName], ' '))),
    CONSTRAINT [PK_PATIENT_LABORDERS] PRIMARY KEY CLUSTERED ([InvoiceId])
    ON [FG_Indices]
)ON [PS_DateRange]([OrderDateTime])
go



IF OBJECT_ID('PROE.PatientLabOrders') IS NOT NULL
    PRINT '<<< CREATED TABLE PROE.PatientLabOrders >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE PROE.PatientLabOrders >>>'
go

CREATE FULLTEXT INDEX ON [PROE].[PatientLabOrders]([FullName]) 
	KEY INDEX PK_PATIENT_LABORDERS;
	
GO
/* 
 * TABLE: [RBAC].[Permissions] 
 */

CREATE TABLE [RBAC].[Permissions](
    [Id]           smallint            IDENTITY(1,1) NOT FOR REPLICATION,
    [PermCode]     varchar(32)         NOT NULL,
    [IsActive]     bit                 NOT NULL,
    [Name]         varchar(40)         NOT NULL,
    [Category]     varchar(40)         NULL,
    [Description]  varchar(max)        NULL,
    [RowGuid]      uniqueidentifier    ROWGUIDCOL DEFAULT NEWSEQUENTIALID() NOT NULL,
    CONSTRAINT [PK_PERMISSIONS] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Static],
    CONSTRAINT [AK_PERMISSIONS]  UNIQUE ([PermCode])
    ON [FG_Static],
    CONSTRAINT [AK_PERMISSION_ROWGUID]  UNIQUE ([RowGuid])
    ON [FG_Static]
)ON [FG_Static]
go



IF OBJECT_ID('RBAC.Permissions') IS NOT NULL
    PRINT '<<< CREATED TABLE RBAC.Permissions >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE RBAC.Permissions >>>'
go

/* 
 * TABLE: [RBAC].[RolePermissions] 
 */

CREATE TABLE [RBAC].[RolePermissions](
    [Id]            int                 IDENTITY(1,1),
    [RoleId]        smallint            NOT NULL,
    [PermissionId]  smallint            NOT NULL,
    [RowGuid]       uniqueidentifier    ROWGUIDCOL DEFAULT NEWSEQUENTIALID() NOT NULL,
    CONSTRAINT [PK_ROLE_PERMISSIONS] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Static],
    CONSTRAINT [AK_ROLE_PERMISSION_ROWGUID]  UNIQUE ([RowGuid])
    ON [FG_Static]
)ON [FG_Static]
go



IF OBJECT_ID('RBAC.RolePermissions') IS NOT NULL
    PRINT '<<< CREATED TABLE RBAC.RolePermissions >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE RBAC.RolePermissions >>>'
go

/* 
 * TABLE: [RBAC].[Roles] 
 */

CREATE TABLE [RBAC].[Roles](
    [Id]           smallint            IDENTITY(1,1) NOT FOR REPLICATION,
    [IsActive]     bit                 NOT NULL,
    [IsAdmin]      bit                 NOT NULL,
    [RoleCode]     varchar(40)         NOT NULL,
    [Name]         varchar(40)         NOT NULL,
    [Description]  varchar(max)        NULL,
    [RowGuid]      uniqueidentifier    ROWGUIDCOL DEFAULT NEWSEQUENTIALID() NOT NULL,
    CONSTRAINT [PK_USER_ROLES] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Static],
    CONSTRAINT [AK_USER_ROLES]  UNIQUE ([RoleCode])
    ON [FG_Static],
    CONSTRAINT [AK_USER_ROLE_ROWGUID]  UNIQUE ([RowGuid])
    ON [FG_Static]
)ON [FG_Static]
go



IF OBJECT_ID('RBAC.Roles') IS NOT NULL
    PRINT '<<< CREATED TABLE RBAC.Roles >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE RBAC.Roles >>>'
go

/* 
 * TABLE: [Staff].[Consultants] 
 */

CREATE TABLE [Staff].[Consultants](
    [UserId]          smallint            NOT NULL,
    [RowGuid]         uniqueidentifier    ROWGUIDCOL DEFAULT NEWSEQUENTIALID() NOT NULL,
    [LastModified]    smalldatetime       DEFAULT GETDATE() NOT NULL,
    [SignatureImage]  varbinary(max)      NULL,
    [SignatureText]   varchar(max)        NULL,
    CONSTRAINT [PK_CONSULTANTS] PRIMARY KEY CLUSTERED ([UserId])
    ON [FG_Static],
    CONSTRAINT [AK_CONSULTANTS_ROWGUID]  UNIQUE ([RowGuid])
    ON [FG_Static]
)ON [FG_Static]
go



IF OBJECT_ID('Staff.Consultants') IS NOT NULL
    PRINT '<<< CREATED TABLE Staff.Consultants >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE Staff.Consultants >>>'
go

/* 
 * TABLE: [Staff].[ConsultantSandboxedGroupsLink] 
 */

CREATE TABLE [Staff].[ConsultantSandboxedGroupsLink](
    [Id]       smallint            IDENTITY(1,1),
    [UserId]   smallint            NOT NULL,
    [GroupId]  smallint            NOT NULL,
    [RowGuid]  uniqueidentifier    ROWGUIDCOL DEFAULT NEWSEQUENTIALID() NOT NULL,
    CONSTRAINT [PK_CONSULTANT_SANDBOX_LINK] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Static],
    CONSTRAINT [AK_CONSULTANT_SANDBOX_LINK]  UNIQUE ([UserId], [GroupId])
    ON [FG_Static],
    CONSTRAINT [AK_CONSULTANT_SANDBOX_LINK_ROWGUID]  UNIQUE ([RowGuid])
    ON [FG_Static]
)ON [FG_Static]
go



IF OBJECT_ID('Staff.ConsultantSandboxedGroupsLink') IS NOT NULL
    PRINT '<<< CREATED TABLE Staff.ConsultantSandboxedGroupsLink >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE Staff.ConsultantSandboxedGroupsLink >>>'
go

/* 
 * TABLE: [Staff].[Departments] 
 */

CREATE TABLE [Staff].[Departments](
    [Id]           smallint            IDENTITY(1,1) NOT FOR REPLICATION,
    [Name]         varchar(40)         NOT NULL,
    [DateCreated]  smalldatetime       DEFAULT GETDATE() NOT NULL,
    [RowGuid]      uniqueidentifier    ROWGUIDCOL DEFAULT NEWSEQUENTIALID() NOT NULL,
    CONSTRAINT [PK_DEPARTMENTS] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Static],
    CONSTRAINT [AK_DEPARTMENTS]  UNIQUE ([Name])
    ON [FG_Static],
    CONSTRAINT [AK_DEPARTMENT_ROWGUID]  UNIQUE ([RowGuid])
    ON [FG_Static]
)ON [FG_Static]
go



IF OBJECT_ID('Staff.Departments') IS NOT NULL
    PRINT '<<< CREATED TABLE Staff.Departments >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE Staff.Departments >>>'
go

/* 
 * TABLE: [Staff].[UserMessages] 
 */

CREATE TABLE [Staff].[UserMessages](
    [Id]                     int              IDENTITY(1,1),
    [DateCreated]            smalldatetime    DEFAULT GETDATE() NOT NULL,
    [ExpiryDate]             smalldatetime    NULL,
    [IsRead]                 bit              DEFAULT 0 NOT NULL,
    [IsImportant]            bit              DEFAULT 0 NOT NULL,
    [BroadcastToAllUsers]    bit              DEFAULT 0 NOT NULL,
    [Message]                varchar(max)     NOT NULL,
    [SendingUserId]          smallint         NULL,
    [ReceivingUserId]        smallint         NULL,
    [ReceivingDepartmentId]  smallint         NULL,
    CONSTRAINT [PK_USER_MESSAGES] PRIMARY KEY NONCLUSTERED ([Id])
    ON [FG_Indices]
)ON [PS_DateRange]([DateCreated])
go



IF OBJECT_ID('Staff.UserMessages') IS NOT NULL
    PRINT '<<< CREATED TABLE Staff.UserMessages >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE Staff.UserMessages >>>'
go

/* 
 * TABLE: [Staff].[Users] 
 */

CREATE TABLE [Staff].[Users](
    [Id]                  smallint            IDENTITY(1,1),
    [IsActive]            bit                 NOT NULL,
    [UserName]            varchar(20)         NOT NULL,
    [PassHash]            char(32)            NOT NULL,
    [Title]               varchar(20)         NULL,
    [FirstName]           varchar(80)         NOT NULL,
    [LastName]            varchar(80)         NOT NULL,
    [DisplayName]         varchar(40)         NOT NULL,
    [Suffix]              varchar(40)         NULL,
    [MustChangePassword]  bit                 DEFAULT 0 NOT NULL,
    [LastModified]        datetime            DEFAULT GETDATE() NULL,
    [DateCreated]         datetime            DEFAULT GETDATE() NULL,
    [LastLogin]           datetime            NULL,
    [RowGuid]             uniqueidentifier    ROWGUIDCOL DEFAULT NEWSEQUENTIALID() NOT NULL,
    [RoleId]              smallint            NULL,
    [DepartmentId]        smallint            NULL,
    CONSTRAINT [PK_USERS] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Static],
    CONSTRAINT [AK_USERS_USERNAME]  UNIQUE ([UserName])
    ON [FG_Static],
    CONSTRAINT [AK_USERS_ROWGUID]  UNIQUE ([RowGuid])
    ON [FG_Static]
)ON [FG_Static]
go



IF OBJECT_ID('Staff.Users') IS NOT NULL
    PRINT '<<< CREATED TABLE Staff.Users >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE Staff.Users >>>'
go

/* 
 * TABLE: [Staff].[UserSettings] 
 */

CREATE TABLE [Staff].[UserSettings](
    [Id]           int             IDENTITY(1,1) NOT FOR REPLICATION,
    [UserId]       smallint        NOT NULL,
    [SettingsKey]  varchar(160)    NOT NULL,
    [IntValue]     int             DEFAULT -1 NOT NULL,
    [StrValue]     varchar(max)    NULL,
    CONSTRAINT [PK_USER_SETTINGS] PRIMARY KEY NONCLUSTERED ([Id])
    ON [FG_Static]
)ON [FG_Static]
go



IF OBJECT_ID('Staff.UserSettings') IS NOT NULL
    PRINT '<<< CREATED TABLE Staff.UserSettings >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE Staff.UserSettings >>>'
go

/* 
 * TABLE: [SubContract].[RequestingLabAuditTrails] 
 */

CREATE TABLE [SubContract].[RequestingLabAuditTrails](
    [Id]               bigint           IDENTITY(1,1),
    [RequestingLabId]  int              NULL,
    [UserId]           int              NULL,
    [InvoiceId]        bigint           NULL,
    [ResultBundleId]   bigint           NULL,
    [EventTime]        smalldatetime    DEFAULT GETDATE() NOT NULL,
    [Action]           smallint         NOT NULL,
    [IpAddress]        int              NOT NULL,
    [Notes]            varchar(max)     NULL,
    CONSTRAINT [PK_REQLAB_AUDITTRAILS] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Indices]
)ON [PS_DateRange]([EventTime])
go



IF OBJECT_ID('SubContract.RequestingLabAuditTrails') IS NOT NULL
    PRINT '<<< CREATED TABLE SubContract.RequestingLabAuditTrails >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE SubContract.RequestingLabAuditTrails >>>'
go

/* 
 * TABLE: [SubContract].[RequestingLabs] 
 */

CREATE TABLE [SubContract].[RequestingLabs](
    [Id]            int                 IDENTITY(1001,1),
    [Name]          varchar(160)        NOT NULL,
    [IsActive]      bit                 NOT NULL,
    [Email]         varchar(40)         NULL,
    [LastModified]  datetime            DEFAULT GETDATE() NOT NULL,
    [RowGuid]       uniqueidentifier    ROWGUIDCOL DEFAULT NEWSEQUENTIALID() NOT NULL,
    CONSTRAINT [PK_REQLABS] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Static],
    CONSTRAINT [AK_REQLABS]  UNIQUE ([Name])
    ON [FG_Static]
)ON [FG_Static]
go



IF OBJECT_ID('SubContract.RequestingLabs') IS NOT NULL
    PRINT '<<< CREATED TABLE SubContract.RequestingLabs >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE SubContract.RequestingLabs >>>'
go

/* 
 * TABLE: [SubContract].[RequestingLabUsers] 
 */

CREATE TABLE [SubContract].[RequestingLabUsers](
    [Id]               int                 IDENTITY(101,1),
    [IsActive]         bit                 DEFAULT 0 NOT NULL,
    [UserName]         varchar(20)         NOT NULL,
    [PassHash]         char(32)            NULL,
    [FullName]         varchar(80)         NULL,
    [DateCreated]      datetime            DEFAULT GETDATE() NOT NULL,
    [LastModified]     datetime            DEFAULT GETDATE() NOT NULL,
    [LastLogin]        datetime            NULL,
    [RequestingLabId]  int                 NOT NULL,
    [RowGuid]          uniqueidentifier    ROWGUIDCOL DEFAULT NEWSEQUENTIALID() NOT NULL,
    CONSTRAINT [PK_REQLABUSERS] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Static],
    CONSTRAINT [AK_REQLABUSERS]  UNIQUE ([UserName])
    ON [FG_Static]
)ON [FG_Static]
go



IF OBJECT_ID('SubContract.RequestingLabUsers') IS NOT NULL
    PRINT '<<< CREATED TABLE SubContract.RequestingLabUsers >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE SubContract.RequestingLabUsers >>>'
go

/* 
 * TABLE: [TestResults].[DiscreteResultLineItems] 
 */

CREATE TABLE [TestResults].[DiscreteResultLineItems](
    [Id]                bigint           IDENTITY(1,1) NOT FOR REPLICATION,
    [OrderedTestId]     bigint           NULL,
    [ResultBundleId]    bigint           NULL,
    [DateCreated]       smalldatetime    DEFAULT GETDATE() NOT NULL,
    [Parameter]         varchar(max)     NOT NULL,
    [Result]            varchar(max)     NULL,
    [Units]             varchar(max)     NULL,
    [ReferenceRange]    varchar(max)     NULL,
    [Flag]              varchar(40)      NULL,
    [SortOrder]         tinyint          NOT NULL,
    [IndentLevel]       tinyint          NOT NULL,
    [IsResultableItem]  bit              NOT NULL,
    CHECK ([IndentLevel] >= (0) AND [IndentLevel] < (10)),
    CONSTRAINT [PK_DISCRETE_RESULTS] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Indices]
)ON [PS_DateRange]([DateCreated])
go



IF OBJECT_ID('TestResults.DiscreteResultLineItems') IS NOT NULL
    PRINT '<<< CREATED TABLE TestResults.DiscreteResultLineItems >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE TestResults.DiscreteResultLineItems >>>'
go

/* 
 * TABLE: [TestResults].[PhlebotomySpecimens] 
 */

CREATE TABLE [TestResults].[PhlebotomySpecimens](
    [Id]                      bigint           IDENTITY(1001,1),
    [HL7SpecimentId]          varchar(24)      NOT NULL,
    [InvoiceId]               bigint           NULL,
    [ResultBundleId]          bigint           NULL,
    [Quantity]                smallint         DEFAULT 0 NOT NULL,
    [LastModified]            smalldatetime    DEFAULT GETDATE() NOT NULL,
    [CreatedOn]               smalldatetime    DEFAULT GETDATE() NOT NULL,
    [CreatedBy]               smallint         NULL,
    [ResultBundleAssignedOn]  smalldatetime    NULL,
    [ResultBundleAssignedBy]  smallint         NULL,
    [VacutainerType]          varchar(8)       NULL,
    [Remarks]                 varchar(64)      NULL,
    CONSTRAINT [AK_PHLEBOTOMY_SPECS]  UNIQUE ([HL7SpecimentId])
    ON [FG_Indices]
)ON [PS_DateRange]([CreatedOn])
go



IF OBJECT_ID('TestResults.PhlebotomySpecimens') IS NOT NULL
    PRINT '<<< CREATED TABLE TestResults.PhlebotomySpecimens >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE TestResults.PhlebotomySpecimens >>>'
go

/* 
 * TABLE: [TestResults].[RecentlyUpdatedResultBundles] 
 */

CREATE TABLE [TestResults].[RecentlyUpdatedResultBundles](
    [Id]              bigint           IDENTITY(1,1) NOT FOR REPLICATION,
    [ResultBundleId]  bigint           NULL,
    [InvoiceId]       bigint           NULL,
    [SortPriority]    tinyint          DEFAULT 0 NOT NULL,
    [LastUpdated]     smalldatetime    DEFAULT GETDATE() NOT NULL,
    [WorkflowStage]   tinyint          NOT NULL,
    CONSTRAINT [PK_RECENT_RESULT_BUNDLE] PRIMARY KEY NONCLUSTERED ([Id])
    ON [FG_Indices]
)ON [PS_DateRange]([LastUpdated])
go



IF OBJECT_ID('TestResults.RecentlyUpdatedResultBundles') IS NOT NULL
    PRINT '<<< CREATED TABLE TestResults.RecentlyUpdatedResultBundles >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE TestResults.RecentlyUpdatedResultBundles >>>'
go

/* 
 * TABLE: [TestResults].[ResultBundles] 
 */

CREATE TABLE [TestResults].[ResultBundles](
    [Id]                        bigint            IDENTITY(1,1) NOT FOR REPLICATION,
    [InvoiceId]                 bigint            NULL,
    [LabId]                     smallint          NOT NULL,
    [ReportHeaderId]            int               NULL,
    [IsActive]                  bit               NOT NULL,
    [TestResultType]            tinyint           NOT NULL,
    [DisplayTitle]              varchar(max)      NOT NULL,
    [ComponentLabTests]         varchar(max)      NULL,
    [DateCreated]               smalldatetime     DEFAULT GETDATE() NOT NULL,
    [LastUpdated]               smalldatetime     DEFAULT GETDATE() NOT NULL,
    [TATRank]                   tinyint           NOT NULL,
    [WorkflowStage]             tinyint           NOT NULL,
    [FinalizingConsultantId]    smallint          NULL,
    [FinalizingConsultantName]  varchar(160)      NULL,
    [CreatingUserId]            smallint          NULL,
    [ResultNotes]               varbinary(max)    NULL,
    CONSTRAINT [PK_RESULT_BUNDLES] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Indices]
)ON [PS_DateRange]([DateCreated])
go



IF OBJECT_ID('TestResults.ResultBundles') IS NOT NULL
    PRINT '<<< CREATED TABLE TestResults.ResultBundles >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE TestResults.ResultBundles >>>'
go

/* 
 * TABLE: [TestResults].[TemplateResults] 
 */

CREATE TABLE [TestResults].[TemplateResults](
    [Id]              bigint            IDENTITY(1,1) NOT FOR REPLICATION,
    [ResultBundleId]  bigint            NOT NULL,
    [OrderedTestId]   bigint            NOT NULL,
    [DateCreated]     smalldatetime     DEFAULT GETDATE() NOT NULL,
    [Content]         varbinary(max)    NOT NULL,
    CONSTRAINT [PK_TEMPLATE_RESULTS] PRIMARY KEY CLUSTERED ([Id])
    ON [FG_Indices]
)ON [PS_DateRange]([DateCreated])
go



IF OBJECT_ID('TestResults.TemplateResults') IS NOT NULL
    PRINT '<<< CREATED TABLE TestResults.TemplateResults >>>'
ELSE
    PRINT '<<< FAILED CREATING TABLE TestResults.TemplateResults >>>'
go

/* 
 * INDEX: [IX_APP_FAULTS] 
 */

CREATE INDEX [IX_APP_FAULTS] ON [APP_SYS].[AppFaults]([SubmissionTime], [ItemStatus])
ON [FG_Indices]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('APP_SYS.AppFaults') AND name='IX_APP_FAULTS')
    PRINT '<<< CREATED INDEX APP_SYS.AppFaults.IX_APP_FAULTS >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX APP_SYS.AppFaults.IX_APP_FAULTS >>>'
go

/* 
 * INDEX: [IX_AUDIT_TRAIL_INVOICE] 
 */

CREATE INDEX [IX_AUDIT_TRAIL_INVOICE] ON [APP_SYS].[AuditTrails]([PatientLabOrderId])
ON [FG_Indices]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('APP_SYS.AuditTrails') AND name='IX_AUDIT_TRAIL_INVOICE')
    PRINT '<<< CREATED INDEX APP_SYS.AuditTrails.IX_AUDIT_TRAIL_INVOICE >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX APP_SYS.AuditTrails.IX_AUDIT_TRAIL_INVOICE >>>'
go

/* 
 * INDEX: [IX_AUDIT_TRAIL_BUNDLE] 
 */

CREATE INDEX [IX_AUDIT_TRAIL_BUNDLE] ON [APP_SYS].[AuditTrails]([ResultBundleId])
ON [FG_Indices]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('APP_SYS.AuditTrails') AND name='IX_AUDIT_TRAIL_BUNDLE')
    PRINT '<<< CREATED INDEX APP_SYS.AuditTrails.IX_AUDIT_TRAIL_BUNDLE >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX APP_SYS.AuditTrails.IX_AUDIT_TRAIL_BUNDLE >>>'
go

/* 
 * INDEX: [IX_AUDIT_TRAIL_BUNDLE_STAGE] 
 */

CREATE INDEX [IX_AUDIT_TRAIL_BUNDLE_STAGE] ON [APP_SYS].[AuditTrails]([ResultBundleId], [WorkflowStage])
ON [FG_Indices]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('APP_SYS.AuditTrails') AND name='IX_AUDIT_TRAIL_BUNDLE_STAGE')
    PRINT '<<< CREATED INDEX APP_SYS.AuditTrails.IX_AUDIT_TRAIL_BUNDLE_STAGE >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX APP_SYS.AuditTrails.IX_AUDIT_TRAIL_BUNDLE_STAGE >>>'
go

/* 
 * INDEX: [IX_ERROR_LOG] 
 */

CREATE INDEX [IX_ERROR_LOG] ON [APP_SYS].[ErrorLog]([ErrorTime], [ErrorCode], [ErrorSeverity], [UserIpAddress], [Username])
ON [FG_Indices]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('APP_SYS.ErrorLog') AND name='IX_ERROR_LOG')
    PRINT '<<< CREATED INDEX APP_SYS.ErrorLog.IX_ERROR_LOG >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX APP_SYS.ErrorLog.IX_ERROR_LOG >>>'
go

/* 
 * INDEX: [AK_SYSTEM_REPORTS_ROWGUID] 
 */

CREATE UNIQUE INDEX [AK_SYSTEM_REPORTS_ROWGUID] ON [Catalog].[AppReportTemplates]([RowGuid])
ON [FG_Static]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('Catalog.AppReportTemplates') AND name='AK_SYSTEM_REPORTS_ROWGUID')
    PRINT '<<< CREATED INDEX Catalog.AppReportTemplates.AK_SYSTEM_REPORTS_ROWGUID >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX Catalog.AppReportTemplates.AK_SYSTEM_REPORTS_ROWGUID >>>'
go

/* 
 * INDEX: [IX_BATTERYCOMPONENTS] 
 */

CREATE INDEX [IX_BATTERYCOMPONENTS] ON [Catalog].[BatteryComponents]([BatteryMasterId])
ON [FG_Static]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('Catalog.BatteryComponents') AND name='IX_BATTERYCOMPONENTS')
    PRINT '<<< CREATED INDEX Catalog.BatteryComponents.IX_BATTERYCOMPONENTS >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX Catalog.BatteryComponents.IX_BATTERYCOMPONENTS >>>'
go

/* 
 * INDEX: [IX_BOM] 
 */

CREATE INDEX [IX_BOM] ON [Catalog].[BillOfMaterials]([Name], [IsActive])
ON [FG_STATIC]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('Catalog.BillOfMaterials') AND name='IX_BOM')
    PRINT '<<< CREATED INDEX Catalog.BillOfMaterials.IX_BOM >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX Catalog.BillOfMaterials.IX_BOM >>>'
go

/* 
 * INDEX: [IX_CATALOG_SNAPSHOTS] 
 */

CREATE INDEX [IX_CATALOG_SNAPSHOTS] ON [Catalog].[CatalogSnapshots]([DateCreated], [CreatingUserId], [CatalogType])
ON [FG_Static]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('Catalog.CatalogSnapshots') AND name='IX_CATALOG_SNAPSHOTS')
    PRINT '<<< CREATED INDEX Catalog.CatalogSnapshots.IX_CATALOG_SNAPSHOTS >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX Catalog.CatalogSnapshots.IX_CATALOG_SNAPSHOTS >>>'
go

/* 
 * INDEX: [IX_DISCRETE_REPORT_ITEMS] 
 */

CREATE INDEX [IX_DISCRETE_REPORT_ITEMS] ON [Catalog].[DiscreteReportLineItems]([LabTestId], [SortOrder])
ON [FG_Static]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('Catalog.DiscreteReportLineItems') AND name='IX_DISCRETE_REPORT_ITEMS')
    PRINT '<<< CREATED INDEX Catalog.DiscreteReportLineItems.IX_DISCRETE_REPORT_ITEMS >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX Catalog.DiscreteReportLineItems.IX_DISCRETE_REPORT_ITEMS >>>'
go

/* 
 * INDEX: [IX_LAB_REPORT_HEADERS] 
 */

CREATE INDEX [IX_LAB_REPORT_HEADERS] ON [Catalog].[LabReportHeaders]([LabId], [SortPriority], [IsActive])
ON [FG_Static]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('Catalog.LabReportHeaders') AND name='IX_LAB_REPORT_HEADERS')
    PRINT '<<< CREATED INDEX Catalog.LabReportHeaders.IX_LAB_REPORT_HEADERS >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX Catalog.LabReportHeaders.IX_LAB_REPORT_HEADERS >>>'
go

/* 
 * INDEX: [IX_LABS] 
 */

CREATE UNIQUE INDEX [IX_LABS] ON [Catalog].[Labs]([IsActive], [Name], [ReferralGroupId])
ON [FG_Static]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('Catalog.Labs') AND name='IX_LABS')
    PRINT '<<< CREATED INDEX Catalog.Labs.IX_LABS >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX Catalog.Labs.IX_LABS >>>'
go

/* 
 * INDEX: [IX_LABTESTS] 
 */

CREATE INDEX [IX_LABTESTS] ON [Catalog].[LabTests]([PerformingLabId], [ResultingLabId], [IsActive], [ReportSortPriority], [TATGroupId], [ShortName], [DateCreated], [DefaultTemplateId], [TemplateGroupId])
ON [FG_Static]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('Catalog.LabTests') AND name='IX_LABTESTS')
    PRINT '<<< CREATED INDEX Catalog.LabTests.IX_LABTESTS >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX Catalog.LabTests.IX_LABTESTS >>>'
go

/* 
 * INDEX: [IX_REFERRER_ADDRESS] 
 */

CREATE INDEX [IX_REFERRER_ADDRESS] ON [Catalog].[ReferrerAddresses]([ReferrerId], [IsMailingAddress], [AddressType])
ON [FG_Static]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('Catalog.ReferrerAddresses') AND name='IX_REFERRER_ADDRESS')
    PRINT '<<< CREATED INDEX Catalog.ReferrerAddresses.IX_REFERRER_ADDRESS >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX Catalog.ReferrerAddresses.IX_REFERRER_ADDRESS >>>'
go

/* 
 * INDEX: [IX_REFERRER_CATEGORIES] 
 */

CREATE UNIQUE INDEX [IX_REFERRER_CATEGORIES] ON [Catalog].[ReferrerCategories]([IsActive], [Name])
ON FG_Static
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('Catalog.ReferrerCategories') AND name='IX_REFERRER_CATEGORIES')
    PRINT '<<< CREATED INDEX Catalog.ReferrerCategories.IX_REFERRER_CATEGORIES >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX Catalog.ReferrerCategories.IX_REFERRER_CATEGORIES >>>'
go

/* 
 * INDEX: [AK_REFERRER_CATEGORIES] 
 */

CREATE UNIQUE INDEX [AK_REFERRER_CATEGORIES] ON [Catalog].[ReferrerCategories]([Name])
ON FG_Static
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('Catalog.ReferrerCategories') AND name='AK_REFERRER_CATEGORIES')
    PRINT '<<< CREATED INDEX Catalog.ReferrerCategories.AK_REFERRER_CATEGORIES >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX Catalog.ReferrerCategories.AK_REFERRER_CATEGORIES >>>'
go

/* 
 * INDEX: [IX_REFERRER_PHONES] 
 */

CREATE INDEX [IX_REFERRER_PHONES] ON [Catalog].[ReferrerPhoneNumbers]([ReferrerId], [PhoneNumberType])
ON [FG_Static]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('Catalog.ReferrerPhoneNumbers') AND name='IX_REFERRER_PHONES')
    PRINT '<<< CREATED INDEX Catalog.ReferrerPhoneNumbers.IX_REFERRER_PHONES >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX Catalog.ReferrerPhoneNumbers.IX_REFERRER_PHONES >>>'
go

/* 
 * INDEX: [IX_REFERRERS] 
 */

CREATE INDEX [IX_REFERRERS] ON [Catalog].[Referrers]([IsActive], [Prefix], [Name], [ReferralClassId], [MarketingExecId], [WebLoginId])
ON [FG_Static]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('Catalog.Referrers') AND name='IX_REFERRERS')
    PRINT '<<< CREATED INDEX Catalog.Referrers.IX_REFERRERS >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX Catalog.Referrers.IX_REFERRERS >>>'
go

/* 
 * INDEX: [IX_REQ_PARAMETERS] 
 */

CREATE INDEX [IX_REQ_PARAMETERS] ON [Catalog].[ReqParameters]([SortOrder], [LabTestId])
ON [FG_Static]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('Catalog.ReqParameters') AND name='IX_REQ_PARAMETERS')
    PRINT '<<< CREATED INDEX Catalog.ReqParameters.IX_REQ_PARAMETERS >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX Catalog.ReqParameters.IX_REQ_PARAMETERS >>>'
go

/* 
 * INDEX: [IX_SURCHARGE_LEVELS] 
 */

CREATE INDEX [IX_SURCHARGE_LEVELS] ON [Catalog].[SurchargeLevels]([Name], [IsActive])
ON [FG_Static]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('Catalog.SurchargeLevels') AND name='IX_SURCHARGE_LEVELS')
    PRINT '<<< CREATED INDEX Catalog.SurchargeLevels.IX_SURCHARGE_LEVELS >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX Catalog.SurchargeLevels.IX_SURCHARGE_LEVELS >>>'
go

/* 
 * INDEX: [IX_TAT_GROUPS] 
 */

CREATE INDEX [IX_TAT_GROUPS] ON [Catalog].[TATGroups]([DaysRequired], [HoursRequired], [MorningSlabBegin], [MorningSlabEnd], [MorningDeliveryHour], [NoonSlabBegin], [NoonSlabEnd], [NoonDeliveryHour], [EveningSlabBegin], [EveningSlabEnd], [EveningDeliveryHour], [TATRank])
ON [FG_Static]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('Catalog.TATGroups') AND name='IX_TAT_GROUPS')
    PRINT '<<< CREATED INDEX Catalog.TATGroups.IX_TAT_GROUPS >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX Catalog.TATGroups.IX_TAT_GROUPS >>>'
go

/* 
 * INDEX: [IX_TAX_LEVELS] 
 */

CREATE INDEX [IX_TAX_LEVELS] ON [Catalog].[TaxLevels]([Name], [IsActive])
ON [FG_Static]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('Catalog.TaxLevels') AND name='IX_TAX_LEVELS')
    PRINT '<<< CREATED INDEX Catalog.TaxLevels.IX_TAX_LEVELS >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX Catalog.TaxLevels.IX_TAX_LEVELS >>>'
go

/* 
 * INDEX: [IX_TEMPLATE_GROUPS] 
 */

CREATE INDEX [IX_TEMPLATE_GROUPS] ON [Catalog].[TemplateGroups]([LabId], [IsActive])
ON [FG_Static]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('Catalog.TemplateGroups') AND name='IX_TEMPLATE_GROUPS')
    PRINT '<<< CREATED INDEX Catalog.TemplateGroups.IX_TEMPLATE_GROUPS >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX Catalog.TemplateGroups.IX_TEMPLATE_GROUPS >>>'
go

/* 
 * INDEX: [IX_TEMPLATE_REPORTS] 
 */

CREATE INDEX [IX_TEMPLATE_REPORTS] ON [Catalog].[TemplateReports]([SortPriority], [Name], [IsActive])
ON [FG_Static]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('Catalog.TemplateReports') AND name='IX_TEMPLATE_REPORTS')
    PRINT '<<< CREATED INDEX Catalog.TemplateReports.IX_TEMPLATE_REPORTS >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX Catalog.TemplateReports.IX_TEMPLATE_REPORTS >>>'
go

/* 
 * INDEX: [IX_USER_TEMPLATE_REPORTS] 
 */

CREATE UNIQUE INDEX [IX_USER_TEMPLATE_REPORTS] ON [Catalog].[UserTemplateReports]([Name], [IsActive], [SortPriority])
ON [FG_Static]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('Catalog.UserTemplateReports') AND name='IX_USER_TEMPLATE_REPORTS')
    PRINT '<<< CREATED INDEX Catalog.UserTemplateReports.IX_USER_TEMPLATE_REPORTS >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX Catalog.UserTemplateReports.IX_USER_TEMPLATE_REPORTS >>>'
go

/* 
 * INDEX: [IX_INVOICE_MASTER] 
 */

CREATE INDEX [IX_INVOICE_MASTER] ON [Finances].[InvoiceMaster]([PaymentStatus])
ON [FG_Indices]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('Finances.InvoiceMaster') AND name='IX_INVOICE_MASTER')
    PRINT '<<< CREATED INDEX Finances.InvoiceMaster.IX_INVOICE_MASTER >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX Finances.InvoiceMaster.IX_INVOICE_MASTER >>>'
go

/* 
 * INDEX: [IX_INVOICE_TRANSACTIONS] 
 */

CREATE INDEX [IX_INVOICE_TRANSACTIONS] ON [Finances].[InvoiceTransactions]([TxTime], [InvoiceId], [PerformingUserId], [WorkShiftId], [TxType])
ON [FG_Indices]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('Finances.InvoiceTransactions') AND name='IX_INVOICE_TRANSACTIONS')
    PRINT '<<< CREATED INDEX Finances.InvoiceTransactions.IX_INVOICE_TRANSACTIONS >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX Finances.InvoiceTransactions.IX_INVOICE_TRANSACTIONS >>>'
go

/* 
 * INDEX: [IX_WORKSHIFTS] 
 */

CREATE INDEX [IX_WORKSHIFTS] ON [Finances].[WorkShifts]([UserId], [IsClosed], [StartTime], [EndTime])
ON [FG_Indices]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('Finances.WorkShifts') AND name='IX_WORKSHIFTS')
    PRINT '<<< CREATED INDEX Finances.WorkShifts.IX_WORKSHIFTS >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX Finances.WorkShifts.IX_WORKSHIFTS >>>'
go

/* 
 * INDEX: [IX_REFERRAL_PERIODS] 
 */

CREATE UNIQUE INDEX [IX_REFERRAL_PERIODS] ON [Marketing].[ReferralPeriods]([PeriodYYMM], [PeriodStart], [PeriodEnd])
ON [FG_Indices]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('Marketing.ReferralPeriods') AND name='IX_REFERRAL_PERIODS')
    PRINT '<<< CREATED INDEX Marketing.ReferralPeriods.IX_REFERRAL_PERIODS >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX Marketing.ReferralPeriods.IX_REFERRAL_PERIODS >>>'
go

/* 
 * INDEX: [AK_HELD_ORDERS] 
 */

CREATE UNIQUE INDEX [AK_HELD_ORDERS] ON [PROE].[HeldLabOrders]([DateCreated], [FirstName])
ON [FG_Indices]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('PROE.HeldLabOrders') AND name='AK_HELD_ORDERS')
    PRINT '<<< CREATED INDEX PROE.HeldLabOrders.AK_HELD_ORDERS >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX PROE.HeldLabOrders.AK_HELD_ORDERS >>>'
go

/* 
 * INDEX: [AK_HELDLABORDER_TESTS] 
 */

CREATE UNIQUE INDEX [AK_HELDLABORDER_TESTS] ON [PROE].[HeldLabOrderTests]([HeldLabOrderId], [LabTestId])
ON [FG_Indices]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('PROE.HeldLabOrderTests') AND name='AK_HELDLABORDER_TESTS')
    PRINT '<<< CREATED INDEX PROE.HeldLabOrderTests.AK_HELDLABORDER_TESTS >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX PROE.HeldLabOrderTests.AK_HELDLABORDER_TESTS >>>'
go

/* 
 * INDEX: [IX_TESTS_ORDERED] 
 */

CREATE INDEX [IX_TESTS_ORDERED] ON [PROE].[OrderedTests]([WorkflowStage], [IsCancelled], [ResultBundleId], [InvoiceId], [LabTestId])
ON [FG_Indices]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('PROE.OrderedTests') AND name='IX_TESTS_ORDERED')
    PRINT '<<< CREATED INDEX PROE.OrderedTests.IX_TESTS_ORDERED >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX PROE.OrderedTests.IX_TESTS_ORDERED >>>'
go

/* 
 * INDEX: [AX_TESTS_ORDERED] 
 */

CREATE UNIQUE INDEX [AX_TESTS_ORDERED] ON [PROE].[OrderedTests]([InvoiceId], [LabTestId], [DateCreated])
ON [FG_Indices]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('PROE.OrderedTests') AND name='AX_TESTS_ORDERED')
    PRINT '<<< CREATED INDEX PROE.OrderedTests.AX_TESTS_ORDERED >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX PROE.OrderedTests.AX_TESTS_ORDERED >>>'
go

/* 
 * INDEX: [IX_PATIENT_LABORDERS] 
 */

CREATE INDEX [IX_PATIENT_LABORDERS] ON [PROE].[PatientLabOrders]([OrderId], [OrderDateTime], [ReferrerId], [OrderingUserId], [WorkflowStage], [WorkShiftId], [MirrorFlag], [RegisteredMemberId])
ON [FG_Indices]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('PROE.PatientLabOrders') AND name='IX_PATIENT_LABORDERS')
    PRINT '<<< CREATED INDEX PROE.PatientLabOrders.IX_PATIENT_LABORDERS >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX PROE.PatientLabOrders.IX_PATIENT_LABORDERS >>>'
go

/* 
 * INDEX: [IX_PATIENT_LABORDERS_ORDERID] 
 */

CREATE INDEX [IX_PATIENT_LABORDERS_ORDERID] ON [PROE].[PatientLabOrders]([OrderId], [OrderDateTime])
ON [FG_Indices]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('PROE.PatientLabOrders') AND name='IX_PATIENT_LABORDERS_ORDERID')
    PRINT '<<< CREATED INDEX PROE.PatientLabOrders.IX_PATIENT_LABORDERS_ORDERID >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX PROE.PatientLabOrders.IX_PATIENT_LABORDERS_ORDERID >>>'
go

/* 
 * INDEX: [IX_PATIENT_LABORDERS_REFERRERID] 
 */

CREATE INDEX [IX_PATIENT_LABORDERS_REFERRERID] ON [PROE].[PatientLabOrders]([ReferrerId], [OrderDateTime])
ON [FG_Indices]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('PROE.PatientLabOrders') AND name='IX_PATIENT_LABORDERS_REFERRERID')
    PRINT '<<< CREATED INDEX PROE.PatientLabOrders.IX_PATIENT_LABORDERS_REFERRERID >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX PROE.PatientLabOrders.IX_PATIENT_LABORDERS_REFERRERID >>>'
go

/* 
 * INDEX: [IX_PATIENT_LABORDERS_MIRRORFLAG] 
 */

CREATE INDEX [IX_PATIENT_LABORDERS_MIRRORFLAG] ON [PROE].[PatientLabOrders]([MirrorFlag], [OrderDateTime])
ON [FG_Indices]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('PROE.PatientLabOrders') AND name='IX_PATIENT_LABORDERS_MIRRORFLAG')
    PRINT '<<< CREATED INDEX PROE.PatientLabOrders.IX_PATIENT_LABORDERS_MIRRORFLAG >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX PROE.PatientLabOrders.IX_PATIENT_LABORDERS_MIRRORFLAG >>>'
go

/* 
 * INDEX: [IX_PATIENT_LABORDERS_CANCELLED] 
 */

CREATE INDEX [IX_PATIENT_LABORDERS_CANCELLED] ON [PROE].[PatientLabOrders]([OrderDateTime], [IsCancelled])
ON [FG_Indices]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('PROE.PatientLabOrders') AND name='IX_PATIENT_LABORDERS_CANCELLED')
    PRINT '<<< CREATED INDEX PROE.PatientLabOrders.IX_PATIENT_LABORDERS_CANCELLED >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX PROE.PatientLabOrders.IX_PATIENT_LABORDERS_CANCELLED >>>'
go

/* 
 * INDEX: [IX_PATIENT_LABORDERS_EXTERNAL_DATE] 
 */

CREATE INDEX [IX_PATIENT_LABORDERS_EXTERNAL_DATE] ON [PROE].[PatientLabOrders]([OrderDateTime], [IsExternalSubOrder])
ON [FG_Indices]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('PROE.PatientLabOrders') AND name='IX_PATIENT_LABORDERS_EXTERNAL_DATE')
    PRINT '<<< CREATED INDEX PROE.PatientLabOrders.IX_PATIENT_LABORDERS_EXTERNAL_DATE >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX PROE.PatientLabOrders.IX_PATIENT_LABORDERS_EXTERNAL_DATE >>>'
go

/* 
 * INDEX: [IX_PATIENT_LABORDERS_WEBACCESS] 
 */

CREATE INDEX [IX_PATIENT_LABORDERS_WEBACCESS] ON [PROE].[PatientLabOrders]([InvoiceId], [WebAccessToken])
ON [FG_Indices]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('PROE.PatientLabOrders') AND name='IX_PATIENT_LABORDERS_WEBACCESS')
    PRINT '<<< CREATED INDEX PROE.PatientLabOrders.IX_PATIENT_LABORDERS_WEBACCESS >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX PROE.PatientLabOrders.IX_PATIENT_LABORDERS_WEBACCESS >>>'
go

/* 
 * INDEX: [IX_PERMISSIONS] 
 */

CREATE INDEX [IX_PERMISSIONS] ON [RBAC].[Permissions]([IsActive], [Category])
ON [FG_Static]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('RBAC.Permissions') AND name='IX_PERMISSIONS')
    PRINT '<<< CREATED INDEX RBAC.Permissions.IX_PERMISSIONS >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX RBAC.Permissions.IX_PERMISSIONS >>>'
go

/* 
 * INDEX: [IX_USER_MESSAGES] 
 */

CREATE UNIQUE INDEX [IX_USER_MESSAGES] ON [Staff].[UserMessages]([IsRead], [BroadcastToAllUsers], [ExpiryDate], [ReceivingUserId], [ReceivingDepartmentId], [DateCreated])
ON [FG_Indices]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('Staff.UserMessages') AND name='IX_USER_MESSAGES')
    PRINT '<<< CREATED INDEX Staff.UserMessages.IX_USER_MESSAGES >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX Staff.UserMessages.IX_USER_MESSAGES >>>'
go

/* 
 * INDEX: [IX_USERS_ACTIVE_USER] 
 */

CREATE INDEX [IX_USERS_ACTIVE_USER] ON [Staff].[Users]([PassHash], [IsActive], [RoleId], [DepartmentId])
ON [FG_Static]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('Staff.Users') AND name='IX_USERS_ACTIVE_USER')
    PRINT '<<< CREATED INDEX Staff.Users.IX_USERS_ACTIVE_USER >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX Staff.Users.IX_USERS_ACTIVE_USER >>>'
go

/* 
 * INDEX: [IX_USER_SETTINGS] 
 */

CREATE UNIQUE CLUSTERED INDEX [IX_USER_SETTINGS] ON [Staff].[UserSettings]([SettingsKey], [UserId])
ON [FG_Static]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('Staff.UserSettings') AND name='IX_USER_SETTINGS')
    PRINT '<<< CREATED INDEX Staff.UserSettings.IX_USER_SETTINGS >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX Staff.UserSettings.IX_USER_SETTINGS >>>'
go

/* 
 * INDEX: [IX_REQLAB_AUDITTRAILS] 
 */

CREATE INDEX [IX_REQLAB_AUDITTRAILS] ON [SubContract].[RequestingLabAuditTrails]([InvoiceId], [ResultBundleId])
ON [FG_Indices]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('SubContract.RequestingLabAuditTrails') AND name='IX_REQLAB_AUDITTRAILS')
    PRINT '<<< CREATED INDEX SubContract.RequestingLabAuditTrails.IX_REQLAB_AUDITTRAILS >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX SubContract.RequestingLabAuditTrails.IX_REQLAB_AUDITTRAILS >>>'
go

/* 
 * INDEX: [IX_REQLABS] 
 */

CREATE INDEX [IX_REQLABS] ON [SubContract].[RequestingLabs]([Name], [IsActive])
ON [FG_Static]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('SubContract.RequestingLabs') AND name='IX_REQLABS')
    PRINT '<<< CREATED INDEX SubContract.RequestingLabs.IX_REQLABS >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX SubContract.RequestingLabs.IX_REQLABS >>>'
go

/* 
 * INDEX: [IX_REQLABUSERS] 
 */

CREATE INDEX [IX_REQLABUSERS] ON [SubContract].[RequestingLabUsers]([IsActive], [UserName], [RequestingLabId])
ON [FG_Static]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('SubContract.RequestingLabUsers') AND name='IX_REQLABUSERS')
    PRINT '<<< CREATED INDEX SubContract.RequestingLabUsers.IX_REQLABUSERS >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX SubContract.RequestingLabUsers.IX_REQLABUSERS >>>'
go

/* 
 * INDEX: [IX_DISCRETE_RESULTS] 
 */

CREATE INDEX [IX_DISCRETE_RESULTS] ON [TestResults].[DiscreteResultLineItems]([ResultBundleId], [SortOrder], [OrderedTestId])
ON [FG_Indices]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('TestResults.DiscreteResultLineItems') AND name='IX_DISCRETE_RESULTS')
    PRINT '<<< CREATED INDEX TestResults.DiscreteResultLineItems.IX_DISCRETE_RESULTS >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX TestResults.DiscreteResultLineItems.IX_DISCRETE_RESULTS >>>'
go

/* 
 * INDEX: [PK_PHLEBOTOMY_SPECS] 
 */

CREATE CLUSTERED INDEX [PK_PHLEBOTOMY_SPECS] ON [TestResults].[PhlebotomySpecimens]([Id])
ON [FG_Indices]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('TestResults.PhlebotomySpecimens') AND name='PK_PHLEBOTOMY_SPECS')
    PRINT '<<< CREATED INDEX TestResults.PhlebotomySpecimens.PK_PHLEBOTOMY_SPECS >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX TestResults.PhlebotomySpecimens.PK_PHLEBOTOMY_SPECS >>>'
go

/* 
 * INDEX: [IX_PHLEBOTOMY_SPECS_BUNDLE] 
 */

CREATE INDEX [IX_PHLEBOTOMY_SPECS_BUNDLE] ON [TestResults].[PhlebotomySpecimens]([ResultBundleId])
ON [FG_Indices]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('TestResults.PhlebotomySpecimens') AND name='IX_PHLEBOTOMY_SPECS_BUNDLE')
    PRINT '<<< CREATED INDEX TestResults.PhlebotomySpecimens.IX_PHLEBOTOMY_SPECS_BUNDLE >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX TestResults.PhlebotomySpecimens.IX_PHLEBOTOMY_SPECS_BUNDLE >>>'
go

/* 
 * INDEX: [IX_PHLEBOTOMY_SPECS_INVOICE] 
 */

CREATE INDEX [IX_PHLEBOTOMY_SPECS_INVOICE] ON [TestResults].[PhlebotomySpecimens]([InvoiceId])
ON [FG_Indices]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('TestResults.PhlebotomySpecimens') AND name='IX_PHLEBOTOMY_SPECS_INVOICE')
    PRINT '<<< CREATED INDEX TestResults.PhlebotomySpecimens.IX_PHLEBOTOMY_SPECS_INVOICE >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX TestResults.PhlebotomySpecimens.IX_PHLEBOTOMY_SPECS_INVOICE >>>'
go

/* 
 * INDEX: [IX_RECENT_RESULT_BUNDLE] 
 */

CREATE UNIQUE INDEX [IX_RECENT_RESULT_BUNDLE] ON [TestResults].[RecentlyUpdatedResultBundles]([ResultBundleId], [InvoiceId], [LastUpdated], [WorkflowStage], [SortPriority])
ON [FG_Indices]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('TestResults.RecentlyUpdatedResultBundles') AND name='IX_RECENT_RESULT_BUNDLE')
    PRINT '<<< CREATED INDEX TestResults.RecentlyUpdatedResultBundles.IX_RECENT_RESULT_BUNDLE >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX TestResults.RecentlyUpdatedResultBundles.IX_RECENT_RESULT_BUNDLE >>>'
go

/* 
 * INDEX: [IX_RESULT_BUNDLES] 
 */

CREATE INDEX [IX_RESULT_BUNDLES] ON [TestResults].[ResultBundles]([LabId], [InvoiceId], [TATRank], [WorkflowStage], [IsActive], [TestResultType], [DateCreated])
ON [FG_Indices]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('TestResults.ResultBundles') AND name='IX_RESULT_BUNDLES')
    PRINT '<<< CREATED INDEX TestResults.ResultBundles.IX_RESULT_BUNDLES >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX TestResults.ResultBundles.IX_RESULT_BUNDLES >>>'
go

/* 
 * INDEX: [IX_TEMPLATE_RESULTS] 
 */

CREATE INDEX [IX_TEMPLATE_RESULTS] ON [TestResults].[TemplateResults]([OrderedTestId], [ResultBundleId])
ON [FG_Indices]
go
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID('TestResults.TemplateResults') AND name='IX_TEMPLATE_RESULTS')
    PRINT '<<< CREATED INDEX TestResults.TemplateResults.IX_TEMPLATE_RESULTS >>>'
ELSE
    PRINT '<<< FAILED CREATING INDEX TestResults.TemplateResults.IX_TEMPLATE_RESULTS >>>'
go

/* 
 * TABLE: [Catalog].[BatteryComponents] 
 */

ALTER TABLE [Catalog].[BatteryComponents] ADD CONSTRAINT [FK_BATTERY_MASTER_DETAIL] 
    FOREIGN KEY ([BatteryMasterId])
    REFERENCES [Catalog].[BatteryMaster]([Id])
go

ALTER TABLE [Catalog].[BatteryComponents] ADD CONSTRAINT [FK_BATTERY_TEST] 
    FOREIGN KEY ([LabTestId])
    REFERENCES [Catalog].[LabTests]([Id])
go


/* 
 * TABLE: [Catalog].[CatalogSnapshots] 
 */

ALTER TABLE [Catalog].[CatalogSnapshots] ADD CONSTRAINT [FK_USER_CATALOG_SNAPSHOT] 
    FOREIGN KEY ([CreatingUserId])
    REFERENCES [Staff].[Users]([Id]) ON DELETE SET NULL
go


/* 
 * TABLE: [Catalog].[DiscreteReportLineItems] 
 */

ALTER TABLE [Catalog].[DiscreteReportLineItems] ADD CONSTRAINT [FK_LABTEST_DISCRETE] 
    FOREIGN KEY ([LabTestId])
    REFERENCES [Catalog].[LabTests]([Id])
go


/* 
 * TABLE: [Catalog].[LabReportHeaders] 
 */

ALTER TABLE [Catalog].[LabReportHeaders] ADD CONSTRAINT [FK_LAB_LAB_REPORT_HEADERS] 
    FOREIGN KEY ([LabId])
    REFERENCES [Catalog].[Labs]([Id]) ON DELETE CASCADE
go


/* 
 * TABLE: [Catalog].[Labs] 
 */

ALTER TABLE [Catalog].[Labs] ADD CONSTRAINT [FK_DISCOUNT_LEVEL_LABS] 
    FOREIGN KEY ([DiscountLevelId])
    REFERENCES [Catalog].[DiscountLevels]([Id]) ON DELETE SET NULL
go

ALTER TABLE [Catalog].[Labs] ADD CONSTRAINT [FK_LAB_DEFAULT_REPORT_HEADER] 
    FOREIGN KEY ([DefaultReportHeaderId])
    REFERENCES [Catalog].[LabReportHeaders]([Id])
go

ALTER TABLE [Catalog].[Labs] ADD CONSTRAINT [FK_MARKETINGCATEGORY_LABS] 
    FOREIGN KEY ([ReferralGroupId])
    REFERENCES [Marketing].[ReferralGroups]([Id]) ON DELETE SET NULL
go

ALTER TABLE [Catalog].[Labs] ADD CONSTRAINT [FK_SURCHARGE_LEVEL_LABS] 
    FOREIGN KEY ([SurchargeLevelId])
    REFERENCES [Catalog].[SurchargeLevels]([Id]) ON DELETE SET NULL
go

ALTER TABLE [Catalog].[Labs] ADD CONSTRAINT [FK_TAX_LEVEL_LABS] 
    FOREIGN KEY ([TaxLevelId])
    REFERENCES [Catalog].[TaxLevels]([Id]) ON DELETE SET NULL
go


/* 
 * TABLE: [Catalog].[LabTests] 
 */

ALTER TABLE [Catalog].[LabTests] ADD CONSTRAINT [FK_TATLEVEL_LABTEST] 
    FOREIGN KEY ([TATGroupId])
    REFERENCES [Catalog].[TATGroups]([Id])
go

ALTER TABLE [Catalog].[LabTests] ADD CONSTRAINT [FK_TEMPLATE_GROUP_LAB_TESTS] 
    FOREIGN KEY ([TemplateGroupId])
    REFERENCES [Catalog].[TemplateGroups]([Id]) ON DELETE SET NULL
go

ALTER TABLE [Catalog].[LabTests] ADD CONSTRAINT [FK_TEST_PERFORMING_LAB] 
    FOREIGN KEY ([PerformingLabId])
    REFERENCES [Catalog].[Labs]([Id])
go

ALTER TABLE [Catalog].[LabTests] ADD CONSTRAINT [FK_TEST_REPORTING_LAB] 
    FOREIGN KEY ([ResultingLabId])
    REFERENCES [Catalog].[Labs]([Id])
go


/* 
 * TABLE: [Catalog].[ReferrerAddresses] 
 */

ALTER TABLE [Catalog].[ReferrerAddresses] ADD CONSTRAINT [FK_REFERRER_ADDRESS] 
    FOREIGN KEY ([ReferrerId])
    REFERENCES [Catalog].[Referrers]([Id]) ON DELETE CASCADE
go


/* 
 * TABLE: [Catalog].[ReferrerCategoryLink] 
 */

ALTER TABLE [Catalog].[ReferrerCategoryLink] ADD CONSTRAINT [FK_REFERRER_CATEGORY_LINK_CATEGORY] 
    FOREIGN KEY ([CatergoryId])
    REFERENCES [Catalog].[ReferrerCategories]([Id]) ON DELETE CASCADE
go

ALTER TABLE [Catalog].[ReferrerCategoryLink] ADD CONSTRAINT [FK_REFERRER_CATEGORY_LINK_REFERRER] 
    FOREIGN KEY ([ReferrerId])
    REFERENCES [Catalog].[Referrers]([Id]) ON DELETE CASCADE
go


/* 
 * TABLE: [Catalog].[ReferrerPhoneNumbers] 
 */

ALTER TABLE [Catalog].[ReferrerPhoneNumbers] ADD CONSTRAINT [FK_REFERRER_PHONE] 
    FOREIGN KEY ([ReferrerId])
    REFERENCES [Catalog].[Referrers]([Id]) ON DELETE CASCADE
go


/* 
 * TABLE: [Catalog].[Referrers] 
 */

ALTER TABLE [Catalog].[Referrers] ADD CONSTRAINT [FK_MARKETING_EXEC_REFERRERS] 
    FOREIGN KEY ([MarketingExecId])
    REFERENCES [Staff].[Users]([Id]) ON DELETE SET NULL
go

ALTER TABLE [Catalog].[Referrers] ADD CONSTRAINT [FK_REFERRAL_CLASSES] 
    FOREIGN KEY ([ReferralClassId])
    REFERENCES [Marketing].[ReferralClasses]([Id]) ON DELETE SET NULL
go


/* 
 * TABLE: [Catalog].[ReqParameters] 
 */

ALTER TABLE [Catalog].[ReqParameters] ADD CONSTRAINT [FK_LABTEST_REQUISION] 
    FOREIGN KEY ([LabTestId])
    REFERENCES [Catalog].[LabTests]([Id]) ON DELETE CASCADE
go


/* 
 * TABLE: [Catalog].[SandboxedGroupLabsLink] 
 */

ALTER TABLE [Catalog].[SandboxedGroupLabsLink] ADD CONSTRAINT [FK_SANDBOXLINK_GROUP] 
    FOREIGN KEY ([GroupId])
    REFERENCES [Catalog].[SandboxedLabGroups]([Id]) ON DELETE CASCADE
go

ALTER TABLE [Catalog].[SandboxedGroupLabsLink] ADD CONSTRAINT [FK_SANDBOXLINK_LAB] 
    FOREIGN KEY ([LabId])
    REFERENCES [Catalog].[Labs]([Id]) ON DELETE CASCADE
go


/* 
 * TABLE: [Catalog].[TemplateGroupLinks] 
 */

ALTER TABLE [Catalog].[TemplateGroupLinks] ADD CONSTRAINT [FK_TEMPLATE_GROUP_LINKS] 
    FOREIGN KEY ([GroupId])
    REFERENCES [Catalog].[TemplateGroups]([Id]) ON DELETE CASCADE
go

ALTER TABLE [Catalog].[TemplateGroupLinks] ADD CONSTRAINT [FK_TEMPLATE_REPORT_LINK] 
    FOREIGN KEY ([TemplateId])
    REFERENCES [Catalog].[TemplateReports]([Id]) ON DELETE CASCADE
go


/* 
 * TABLE: [Catalog].[TemplateGroups] 
 */

ALTER TABLE [Catalog].[TemplateGroups] ADD CONSTRAINT [FK_LAB_TEMPLATE_GROUPS] 
    FOREIGN KEY ([LabId])
    REFERENCES [Catalog].[Labs]([Id]) ON DELETE CASCADE
go


/* 
 * TABLE: [Catalog].[TestBILink] 
 */

ALTER TABLE [Catalog].[TestBILink] ADD CONSTRAINT [FK_BI_TEST_LINK] 
    FOREIGN KEY ([BillableItemId])
    REFERENCES [Catalog].[BillableItems]([Id]) ON DELETE CASCADE
go

ALTER TABLE [Catalog].[TestBILink] ADD CONSTRAINT [FK_TEST_BI_LINK] 
    FOREIGN KEY ([TestId])
    REFERENCES [Catalog].[LabTests]([Id]) ON DELETE CASCADE
go


/* 
 * TABLE: [Catalog].[TestBoMLink] 
 */

ALTER TABLE [Catalog].[TestBoMLink] ADD CONSTRAINT [FK_BOM_TEST_LINK] 
    FOREIGN KEY ([BoMId])
    REFERENCES [Catalog].[BillOfMaterials]([Id])
go

ALTER TABLE [Catalog].[TestBoMLink] ADD CONSTRAINT [FK_TEST_BOM_LINK] 
    FOREIGN KEY ([LabTestId])
    REFERENCES [Catalog].[LabTests]([Id])
go


/* 
 * TABLE: [Catalog].[UserTemplateGroupLinks] 
 */

ALTER TABLE [Catalog].[UserTemplateGroupLinks] ADD CONSTRAINT [FK_TEMPLATEGROUP_USERTEMPLATEGROUP] 
    FOREIGN KEY ([TemplateGroupId])
    REFERENCES [Catalog].[TemplateGroups]([Id])
go

ALTER TABLE [Catalog].[UserTemplateGroupLinks] ADD CONSTRAINT [FK_USER_USERTEMPLATES] 
    FOREIGN KEY ([UserId])
    REFERENCES [Staff].[Users]([Id])
go


/* 
 * TABLE: [Catalog].[UserTemplateReports] 
 */

ALTER TABLE [Catalog].[UserTemplateReports] ADD CONSTRAINT [FK_USERTEMPLATEGROUP_TEMPLATE] 
    FOREIGN KEY ([TemplateGroupLinkId])
    REFERENCES [Catalog].[UserTemplateGroupLinks]([Id])
go


/* 
 * TABLE: [Finances].[InvoiceMaster] 
 */

ALTER TABLE [Finances].[InvoiceMaster] ADD CONSTRAINT [FK_PATIENTORDER_INVOICE] 
    FOREIGN KEY ([InvoiceId])
    REFERENCES [PROE].[PatientLabOrders]([InvoiceId]) ON DELETE CASCADE
go


/* 
 * TABLE: [Finances].[InvoicePrimal] 
 */

ALTER TABLE [Finances].[InvoicePrimal] ADD CONSTRAINT [FK_PATIENTORDER_INVOICE_PRIMAL] 
    FOREIGN KEY ([InvoiceId])
    REFERENCES [PROE].[PatientLabOrders]([InvoiceId]) ON DELETE CASCADE
go


/* 
 * TABLE: [Finances].[InvoiceTransactions] 
 */

ALTER TABLE [Finances].[InvoiceTransactions] ADD CONSTRAINT [FK_INVOICE_TRANSACTIONS] 
    FOREIGN KEY ([InvoiceId])
    REFERENCES [Finances].[InvoiceMaster]([InvoiceId])
go

ALTER TABLE [Finances].[InvoiceTransactions] ADD CONSTRAINT [FK_USER_AUTHORIZED_TRANSACTIONS] 
    FOREIGN KEY ([AuthorizingUserId])
    REFERENCES [Staff].[Users]([Id])
go

ALTER TABLE [Finances].[InvoiceTransactions] ADD CONSTRAINT [FK_USER_TRANSACTIONS] 
    FOREIGN KEY ([PerformingUserId])
    REFERENCES [Staff].[Users]([Id])
go

ALTER TABLE [Finances].[InvoiceTransactions] ADD CONSTRAINT [FK_WORKSHIFT_TRANSACTIONS] 
    FOREIGN KEY ([WorkShiftId])
    REFERENCES [Finances].[WorkShifts]([Id]) ON DELETE SET NULL
go


/* 
 * TABLE: [Finances].[WorkShifts] 
 */

ALTER TABLE [Finances].[WorkShifts] ADD CONSTRAINT [FK_USER_SHIFT] 
    FOREIGN KEY ([UserId])
    REFERENCES [Staff].[Users]([Id])
go


/* 
 * TABLE: [Marketing].[MarketingExecs] 
 */

ALTER TABLE [Marketing].[MarketingExecs] ADD CONSTRAINT [FK_USER_MARKETING_EXEC] 
    FOREIGN KEY ([Id])
    REFERENCES [Staff].[Users]([Id]) ON DELETE CASCADE
go


/* 
 * TABLE: [PROE].[HeldLabOrders] 
 */

ALTER TABLE [PROE].[HeldLabOrders] ADD CONSTRAINT [FK_REFERRER_ORDERS_ON_HOLD] 
    FOREIGN KEY ([ReferrerId])
    REFERENCES [Catalog].[Referrers]([Id]) ON DELETE SET NULL
go

ALTER TABLE [PROE].[HeldLabOrders] ADD CONSTRAINT [FK_WORKSHIFT_ORDERS_ON_HOLD] 
    FOREIGN KEY ([WorkShiftId])
    REFERENCES [Finances].[WorkShifts]([Id]) ON DELETE CASCADE
go


/* 
 * TABLE: [PROE].[HeldLabOrderTests] 
 */

ALTER TABLE [PROE].[HeldLabOrderTests] ADD CONSTRAINT [FK_HELDLABORDER_TESTS] 
    FOREIGN KEY ([HeldLabOrderId])
    REFERENCES [PROE].[HeldLabOrders]([Id]) ON DELETE CASCADE
go

ALTER TABLE [PROE].[HeldLabOrderTests] ADD CONSTRAINT [FK_LABTEST_HELDORDER] 
    FOREIGN KEY ([LabTestId])
    REFERENCES [Catalog].[LabTests]([Id]) ON DELETE CASCADE
go


/* 
 * TABLE: [PROE].[OrderedBillableItems] 
 */

ALTER TABLE [PROE].[OrderedBillableItems] ADD CONSTRAINT [FK_BILLABLEITEM_ORDERED] 
    FOREIGN KEY ([BillableItemId])
    REFERENCES [Catalog].[BillableItems]([Id]) ON DELETE CASCADE
go

ALTER TABLE [PROE].[OrderedBillableItems] ADD CONSTRAINT [FK_PATIENT_BILLABLEITEMS] 
    FOREIGN KEY ([InvoiceId])
    REFERENCES [PROE].[PatientLabOrders]([InvoiceId]) ON DELETE CASCADE
go


/* 
 * TABLE: [PROE].[OrderedTests] 
 */

ALTER TABLE [PROE].[OrderedTests] ADD CONSTRAINT [FK_PATIENT_ORDEREDTESTS] 
    FOREIGN KEY ([InvoiceId])
    REFERENCES [PROE].[PatientLabOrders]([InvoiceId])
go

ALTER TABLE [PROE].[OrderedTests] ADD CONSTRAINT [FK_RESULTBUNDLE_TESTS] 
    FOREIGN KEY ([ResultBundleId])
    REFERENCES [TestResults].[ResultBundles]([Id]) ON DELETE SET NULL
go

ALTER TABLE [PROE].[OrderedTests] ADD CONSTRAINT [FK_TEST_ORDERED] 
    FOREIGN KEY ([LabTestId])
    REFERENCES [Catalog].[LabTests]([Id])
go


/* 
 * TABLE: [PROE].[PatientLabOrders] 
 */

ALTER TABLE [PROE].[PatientLabOrders] ADD CONSTRAINT [FK_REFERRER_PATIENTS] 
    FOREIGN KEY ([ReferrerId])
    REFERENCES [Catalog].[Referrers]([Id]) ON DELETE SET NULL
go

ALTER TABLE [PROE].[PatientLabOrders] ADD CONSTRAINT [FK_REQLABS_ORDERS] 
    FOREIGN KEY ([RequestingLabId])
    REFERENCES [SubContract].[RequestingLabs]([Id]) ON DELETE SET NULL
go

ALTER TABLE [PROE].[PatientLabOrders] ADD CONSTRAINT [FK_USER_PATIENTORDERS] 
    FOREIGN KEY ([OrderingUserId])
    REFERENCES [Staff].[Users]([Id]) ON DELETE SET NULL
go

ALTER TABLE [PROE].[PatientLabOrders] ADD CONSTRAINT [FK_WORKSHIFT_ORDERS] 
    FOREIGN KEY ([WorkShiftId])
    REFERENCES [Finances].[WorkShifts]([Id]) ON DELETE SET NULL
go


/* 
 * TABLE: [RBAC].[RolePermissions] 
 */

ALTER TABLE [RBAC].[RolePermissions] ADD CONSTRAINT [FK_ROLEPERMISSIONS_PERMISSION] 
    FOREIGN KEY ([PermissionId])
    REFERENCES [RBAC].[Permissions]([Id]) ON DELETE CASCADE
go

ALTER TABLE [RBAC].[RolePermissions] ADD CONSTRAINT [FK_ROLEPERMISSIONS_ROLE] 
    FOREIGN KEY ([RoleId])
    REFERENCES [RBAC].[Roles]([Id]) ON DELETE CASCADE
go


/* 
 * TABLE: [Staff].[Consultants] 
 */

ALTER TABLE [Staff].[Consultants] ADD CONSTRAINT [FK_USER_CONSULTANT] 
    FOREIGN KEY ([UserId])
    REFERENCES [Staff].[Users]([Id]) ON DELETE CASCADE
go


/* 
 * TABLE: [Staff].[ConsultantSandboxedGroupsLink] 
 */

ALTER TABLE [Staff].[ConsultantSandboxedGroupsLink] ADD CONSTRAINT [FK_SANDBOX_LINK_CONSULTANT] 
    FOREIGN KEY ([UserId])
    REFERENCES [Staff].[Users]([Id]) ON DELETE CASCADE
go

ALTER TABLE [Staff].[ConsultantSandboxedGroupsLink] ADD CONSTRAINT [FK_SANDBOX_LINK_GROUP] 
    FOREIGN KEY ([GroupId])
    REFERENCES [Catalog].[SandboxedLabGroups]([Id]) ON DELETE CASCADE
go


/* 
 * TABLE: [Staff].[UserMessages] 
 */

ALTER TABLE [Staff].[UserMessages] ADD CONSTRAINT [FK_DEPARTMENT_MESSAGE_RECEIVER] 
    FOREIGN KEY ([ReceivingDepartmentId])
    REFERENCES [Staff].[Departments]([Id]) ON DELETE SET NULL
go

ALTER TABLE [Staff].[UserMessages] ADD CONSTRAINT [FK_USER_MESSAGE_RECEIVER] 
    FOREIGN KEY ([ReceivingUserId])
    REFERENCES [Staff].[Users]([Id]) ON DELETE SET NULL
go

ALTER TABLE [Staff].[UserMessages] ADD CONSTRAINT [FK_USER_MESSAGE_SENDER] 
    FOREIGN KEY ([SendingUserId])
    REFERENCES [Staff].[Users]([Id])
go


/* 
 * TABLE: [Staff].[Users] 
 */

ALTER TABLE [Staff].[Users] ADD CONSTRAINT [FK_USER_DEPARTMENT] 
    FOREIGN KEY ([DepartmentId])
    REFERENCES [Staff].[Departments]([Id])
go

ALTER TABLE [Staff].[Users] ADD CONSTRAINT [FK_USER_ROLE] 
    FOREIGN KEY ([RoleId])
    REFERENCES [RBAC].[Roles]([Id])
go


/* 
 * TABLE: [Staff].[UserSettings] 
 */

ALTER TABLE [Staff].[UserSettings] ADD CONSTRAINT [FK_USER_PREFERENCES] 
    FOREIGN KEY ([UserId])
    REFERENCES [Staff].[Users]([Id]) ON DELETE CASCADE
go


/* 
 * TABLE: [SubContract].[RequestingLabUsers] 
 */

ALTER TABLE [SubContract].[RequestingLabUsers] ADD CONSTRAINT [FK_REQLABS_USERS] 
    FOREIGN KEY ([RequestingLabId])
    REFERENCES [SubContract].[RequestingLabs]([Id])
go


/* 
 * TABLE: [TestResults].[DiscreteResultLineItems] 
 */

ALTER TABLE [TestResults].[DiscreteResultLineItems] ADD CONSTRAINT [FK_RESULTBUNDLE_DISCRETE] 
    FOREIGN KEY ([ResultBundleId])
    REFERENCES [TestResults].[ResultBundles]([Id])
go

ALTER TABLE [TestResults].[DiscreteResultLineItems] ADD CONSTRAINT [FK_RESULTMASTER_DISCRETE] 
    FOREIGN KEY ([OrderedTestId])
    REFERENCES [PROE].[OrderedTests]([Id]) ON DELETE CASCADE
go


/* 
 * TABLE: [TestResults].[RecentlyUpdatedResultBundles] 
 */

ALTER TABLE [TestResults].[RecentlyUpdatedResultBundles] ADD CONSTRAINT [FK_PATIENT_LAB_ORDER_RECENTLY_UPDATED] 
    FOREIGN KEY ([InvoiceId])
    REFERENCES [PROE].[PatientLabOrders]([InvoiceId]) ON DELETE SET NULL
go

ALTER TABLE [TestResults].[RecentlyUpdatedResultBundles] ADD CONSTRAINT [FK_RESULTBUNDLE_UPDATED] 
    FOREIGN KEY ([ResultBundleId])
    REFERENCES [TestResults].[ResultBundles]([Id]) ON DELETE SET NULL
go


/* 
 * TABLE: [TestResults].[ResultBundles] 
 */

ALTER TABLE [TestResults].[ResultBundles] ADD CONSTRAINT [FK_BUNDLE_REPORT_HEADER] 
    FOREIGN KEY ([ReportHeaderId])
    REFERENCES [Catalog].[LabReportHeaders]([Id]) ON DELETE SET NULL
go

ALTER TABLE [TestResults].[ResultBundles] ADD CONSTRAINT [FK_LAB_REPORTPAGES] 
    FOREIGN KEY ([LabId])
    REFERENCES [Catalog].[Labs]([Id])
go

ALTER TABLE [TestResults].[ResultBundles] ADD CONSTRAINT [FK_LABORDER_RESULT_BUNDLE] 
    FOREIGN KEY ([InvoiceId])
    REFERENCES [PROE].[PatientLabOrders]([InvoiceId]) ON DELETE SET NULL
go

ALTER TABLE [TestResults].[ResultBundles] ADD CONSTRAINT [FK_USER_APPROVE_RESULT_BUNDLE] 
    FOREIGN KEY ([FinalizingConsultantId])
    REFERENCES [Staff].[Users]([Id]) ON DELETE SET NULL
go

ALTER TABLE [TestResults].[ResultBundles] ADD CONSTRAINT [FK_USER_CREATE_RESULT_BUNDLE] 
    FOREIGN KEY ([CreatingUserId])
    REFERENCES [Staff].[Users]([Id])
go


/* 
 * TABLE: [TestResults].[TemplateResults] 
 */

ALTER TABLE [TestResults].[TemplateResults] ADD CONSTRAINT [FK_ORDERED_TEST_TEMPLATE] 
    FOREIGN KEY ([OrderedTestId])
    REFERENCES [PROE].[OrderedTests]([Id]) ON DELETE CASCADE
go

ALTER TABLE [TestResults].[TemplateResults] ADD CONSTRAINT [FK_RESULTBUNDLE_TEMPLATE] 
    FOREIGN KEY ([ResultBundleId])
    REFERENCES [TestResults].[ResultBundles]([Id]) ON DELETE CASCADE
go


/* 
 * FUNCTION: [PROE].[SP_SearchLabOrderByInvoiceIdWebToken] 
 */

CREATE PROCEDURE [PROE].[SP_SearchLabOrderByInvoiceIdWebToken]
  @invoice BIGINT,
  @token   VARCHAR(8)
AS
  SET  NOCOUNT ON

  SELECT TOP 1
    ord.InvoiceId
   ,ord.OrderId
   ,ord.OrderDateTime
   ,ord.WorkflowStage
   ,ord.IsCancelled
   ,ord.IsExternalSubOrder
   ,ord.DisallowReferral
   ,ord.IsReferrerUnknown
   ,ord.FullName AS PatientName
   ,ord.Sex
   ,ord.Age
   ,ord.DoB
   ,COALESCE(phy.FullName,ord.ReferrerCustomName,'') AS ReferrerName
   ,inv.GrossPayable
   ,inv.DiscountAmount
   ,inv.NetPayable
   ,inv.PaidAmount
   ,inv.DueAmount
   ,inv.RefundAmount
   ,ord.ReferrerId
  FROM
    PROE.PatientLabOrders ord
    LEFT OUTER JOIN Catalog.Referrers phy ON ord.ReferrerId = phy.id
    INNER JOIN Finances.InvoiceMaster inv ON inv.InvoiceId = ord.InvoiceId
  WHERE
    ord.InvoiceId = @invoice AND
    ord.WebAccessToken = @token
go
IF OBJECT_ID('PROE.SP_SearchLabOrderByInvoiceIdWebToken') IS NOT NULL
    PRINT '<<< CREATED FUNCTION PROE.SP_SearchLabOrderByInvoiceIdWebToken >>>'
ELSE
    PRINT '<<< FAILED CREATING FUNCTION PROE.SP_SearchLabOrderByInvoiceIdWebToken >>>'
go


/* 
 * FUNCTION: [dbo].[Split] 
 */

CREATE FUNCTION [dbo].[Split]
(
  @text VARCHAR(MAX),
  @delim CHAR(1)
)
RETURNS 
  @Return TABLE (ItemValue VARCHAR(1) NULL)
AS
BEGIN
  DECLARE @Xml XML;
  SELECT @Xml = CAST('<r>' + REPLACE(@text, @delim, '</r><r>') + '</r>' AS XML);
  
  INSERT INTO @Return
  SELECT 
    ItemValue = item.value('text()[1]', 'varchar(1)')
  FROM
    @Xml.nodes('//r') R (item);
  
  RETURN;

END;
go
IF OBJECT_ID('dbo.Split') IS NOT NULL
    PRINT '<<< CREATED FUNCTION dbo.Split >>>'
ELSE
    PRINT '<<< FAILED CREATING FUNCTION dbo.Split >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_AutoUpdateLabOrderWorkflowStageFromOrderedTests] 
 */

CREATE PROCEDURE [PROE].[SP_AutoUpdateLabOrderWorkflowStageFromOrderedTests]
  @invoiceId bigint,
  @wfHighWaterMark tinyint
AS
  SET XACT_ABORT ON
  SET NOCOUNT ON 		

  BEGIN TRANSACTION
    DECLARE curTests CURSOR FAST_FORWARD LOCAL FOR
      SELECT DISTINCT [WorkflowStage]
      FROM
        [PROE].[OrderedTests]
      WHERE
        [InvoiceId] = @invoiceId AND
        [IsCancelled] = 0
    
    OPEN curTests
    
    DECLARE @wfOrderStage TINYINT = @wfHighWaterMark
    DECLARE @wfTestStage TINYINT
    
    FETCH NEXT FROM curTests INTO @wfTestStage

    WHILE @@FETCH_STATUS = 0
    BEGIN
        IF @wfOrderStage > @wfTestStage
          SET @wfOrderStage = @wfTestStage
        
        FETCH NEXT FROM curTests INTO @wfTestStage
    END
    
    CLOSE curTests
    DEALLOCATE curTests

    UPDATE [PROE].[PatientLabOrders]
    SET
      [WorkflowStage] = @wfOrderStage,
      [LastModified] = GETDATE()
    WHERE
      [InvoiceId] = @invoiceId
      
  COMMIT TRANSACTION
go
IF OBJECT_ID('PROE.SP_AutoUpdateLabOrderWorkflowStageFromOrderedTests') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_AutoUpdateLabOrderWorkflowStageFromOrderedTests >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_AutoUpdateLabOrderWorkflowStageFromOrderedTests >>>'
go


/* 
 * PROCEDURE: [Finances].[SP_BeginWorkShift] 
 */

CREATE PROCEDURE [Finances].[SP_BeginWorkShift]
	@userId smallint, 
	@additionalBalance money, 
	@userNote varchar(MAX), 
	@newShiftId int OUTPUT
AS
  	SET XACT_ABORT ON
 	
 	SET NOCOUNT ON 		
 	
	BEGIN TRANSACTION
	
	INSERT INTO [Finances].[WorkShifts]
    	([UserId],[IsClosed],[StartTime],[AdditionalBalance],[UserNotes],
     	 [NumOrders],[ReceiveAmount],[DiscountAmount],[DiscountRebateAmount],
     	 [RefundAmount],[FinalBalance])
	VALUES
    	(@userId,0,CURRENT_TIMESTAMP,@additionalBalance,@userNote, 0, 0, 0, 0, 0, 0)
	
	SELECT @newShiftId = SCOPE_IDENTITY()
			
	COMMIT TRANSACTION
go
IF OBJECT_ID('Finances.SP_BeginWorkShift') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Finances.SP_BeginWorkShift >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Finances.SP_BeginWorkShift >>>'
go


/* 
 * PROCEDURE: [TestResults].[SP_CancelAllResultBundlesForInvoice] 
 */

CREATE PROCEDURE [TestResults].[SP_CancelAllResultBundlesForInvoice]
	@invoiceId bigint
AS
  	SET XACT_ABORT ON	
	SET NOCOUNT ON 		
	
	BEGIN TRANSACTION	
	
    	UPDATE TestResults.ResultBundles
    	SET
    		IsActive = 0
    		, LastUpdated = GETDATE()
    	WHERE
    		InvoiceId = @invoiceId
	
	COMMIT TRANSACTION
go
IF OBJECT_ID('TestResults.SP_CancelAllResultBundlesForInvoice') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE TestResults.SP_CancelAllResultBundlesForInvoice >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE TestResults.SP_CancelAllResultBundlesForInvoice >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_CancelLabOrder] 
 */


CREATE PROCEDURE [PROE].[SP_CancelLabOrder]
  @invoiceId bigint, 
  @wfStage tinyint
AS
  SET XACT_ABORT ON

  SET NOCOUNT ON

  BEGIN TRANSACTION
  
    UPDATE 
      [PROE].[PatientLabOrders]
    SET 
      [IsCancelled] = 1,
      [LastModified] = GETDATE(),
      [WorkflowStage] = @wfStage
    WHERE
      [InvoiceId] = @invoiceId
    
    UPDATE 
      [PROE].[OrderedTests]
    SET 
      [IsCancelled] = 1,
      [LastModified] = GETDATE(),
      [WorkflowStage] = @wfStage
    WHERE
      [InvoiceId] = @invoiceId

    UPDATE 
      [TestResults].[ResultBundles]
    SET 
      [IsActive] = 0,
      [LastUpdated] = GETDATE(),
      [WorkflowStage] = @wfStage
    WHERE
      [InvoiceId] = @invoiceId
      
    DELETE FROM 
      [TestResults].[RecentlyUpdatedResultBundles]
    WHERE
      [InvoiceId] = @invoiceId
      
  COMMIT TRANSACTION
go
IF OBJECT_ID('PROE.SP_CancelLabOrder') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_CancelLabOrder >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_CancelLabOrder >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_CancelOrderedTest] 
 */

CREATE PROCEDURE [PROE].[SP_CancelOrderedTest]
	@invoiceId bigint, 
	@ordTestId bigint, 
	@wfStage tinyint
AS
	SET XACT_ABORT ON
	
	SET NOCOUNT ON
	
	BEGIN TRANSACTION
	
		UPDATE 
			[PROE].[OrderedTests]
		SET 
			[IsCancelled] = 1,
			[LastModified] = GETDATE(),
			[WorkflowStage] = @wfStage
		WHERE
			[Id] = @ordTestId
		
		DELETE
		FROM
			[TestResults].[DiscreteResultLineItems]
		WHERE
			[OrderedTestId] = @ordTestId
		
		DELETE
		FROM
			[TestResults].[TemplateResults]
		WHERE
			[OrderedTestId] = @ordTestId      
		
		UPDATE 
			[PROE].[PatientLabOrders]
		SET 
			[LastModified] = GETDATE()
		WHERE
			[InvoiceId] = @invoiceId
	
	COMMIT TRANSACTION
go
IF OBJECT_ID('PROE.SP_CancelOrderedTest') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_CancelOrderedTest >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_CancelOrderedTest >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_CheckLabOrderIsCanceled] 
 */

CREATE PROCEDURE [PROE].[SP_CheckLabOrderIsCanceled]
	@invoiceId bigint
AS
	SET NOCOUNT ON
	
	SELECT 
		TOP 1 [IsCancelled]
	FROM
		[PROE].[PatientLabOrders]
	WHERE
		[InvoiceId] = @invoiceId
go
IF OBJECT_ID('PROE.SP_CheckLabOrderIsCanceled') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_CheckLabOrderIsCanceled >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_CheckLabOrderIsCanceled >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_CheckOrderedTestWorkflowStageIsModified] 
 */

CREATE PROCEDURE [PROE].[SP_CheckOrderedTestWorkflowStageIsModified]
  @ordTestId bigint
AS
  SET NOCOUNT ON
    
  SELECT TOP 1
    IIF(rb.WorkflowStage = lab.PostOrderEntryWorkflowStage, 1, 0) AS [Result]
  FROM
    PROE.OrderedTests ot
    INNER JOIN TestResults.ResultBundles rb
      ON ot.ResultBundleId = rb.Id
    INNER JOIN Catalog.Labs lab
      ON rb.LabId = lab.Id
  WHERE
    ot.Id = @ordTestId

go
IF OBJECT_ID('PROE.SP_CheckOrderedTestWorkflowStageIsModified') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_CheckOrderedTestWorkflowStageIsModified >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_CheckOrderedTestWorkflowStageIsModified >>>'
go


/* 
 * PROCEDURE: [SP_CreateNewAppReportTemplate] 
 */

CREATE PROCEDURE [Catalog].[SP_CreateNewAppReportTemplate]
	@reportCode varchar(80), 
	@lobContent varbinary(MAX)
AS
	SET XACT_ABORT ON
	
	SET NOCOUNT ON 		
	
	BEGIN TRANSACTION
	
		INSERT INTO Catalog.AppReportTemplates
			(ReportCode, ReportContent)
		VALUES
			(@reportCode, @lobContent)
		
	COMMIT TRANSACTION
go
IF OBJECT_ID('SP_CreateNewAppReportTemplate') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE SP_CreateNewAppReportTemplate >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE SP_CreateNewAppReportTemplate >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_CreateNewAppReportTemplate] 
 */

CREATE PROCEDURE [Catalog].[SP_CreateNewAppReportTemplate]
	@reportCode varchar(80),
	@lobContent varbinary(MAX)
AS
	SET XACT_ABORT ON
	
	SET NOCOUNT ON 		
	
	BEGIN TRANSACTION
	
		INSERT INTO Catalog.AppReportTemplates
			(ReportCode, ReportContent, LastModified)
		VALUES
			(@reportCode, @lobContent, GETDATE())
		
	COMMIT TRANSACTION
go
IF OBJECT_ID('Catalog.SP_CreateNewAppReportTemplate') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_CreateNewAppReportTemplate >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_CreateNewAppReportTemplate >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_CreateNewBillableItem] 
 */

CREATE PROCEDURE [Catalog].[SP_CreateNewBillableItem]
	@name varchar(80),
	@isActive bit,
	@unitPrice smallmoney,
	@newItemId smallint OUT
AS
	SET XACT_ABORT ON
	
	SET NOCOUNT ON 		
	
	BEGIN TRANSACTION	
	
		INSERT INTO [Catalog].[BillableItems]
			([IsActive], [Name], [UnitPrice])
		VALUES
			(@isActive, @name, @unitPrice)
		
		SELECT @newItemId = SCOPE_IDENTITY()
	
	COMMIT TRANSACTION
go
IF OBJECT_ID('Catalog.SP_CreateNewBillableItem') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_CreateNewBillableItem >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_CreateNewBillableItem >>>'
go


/* 
 * PROCEDURE: [Finances].[SP_CreateNewInvoiceMaster] 
 */

CREATE PROCEDURE [Finances].[SP_CreateNewInvoiceMaster]
	@invoiceId     BIGINT,
	@status        TINYINT,
	@gross         MONEY,
	@discount      MONEY,
	@tax           MONEY,
	@surch         MONEY,
	@net           MONEY,
	@paid          MONEY,
	@due           MONEY,
	@refund        MONEY,
	@newId         BIGINT OUT
AS
   SET  XACT_ABORT ON
   SET  NOCOUNT ON

   BEGIN TRANSACTION

   	INSERT INTO Finances.InvoiceMaster (InvoiceId,
                                       	DateCreated,
                                       	PaymentStatus,
                                       	GrossPayable,
                                       	DiscountAmount,
                                       	TaxAmount,
                                       	SurchargeAmount,
                                       	NetPayable,
                                       	PaidAmount,
                                       	DueAmount,
                                       	RefundAmount)
   	VALUES (@invoiceId,
           	GETDATE (),
           	@status,
           	@gross,
           	@discount,
           	@tax,
           	@surch,
           	@net,
           	@paid,
           	@due,
           	@refund)
	
   	SELECT @newId = SCOPE_IDENTITY ()
	
   	INSERT INTO Finances.InvoicePrimal (InvoiceId,
                                       	DateCreated,
                                       	GrossPayable,
                                       	DiscountAmount,
                                       	TaxAmount,
                                       	SurchargeAmount,
                                       	NetPayable,
                                       	PaidAmount,
                                       	DueAmount,
                                       	RefundAmount)
   	VALUES (@invoiceId,
           	GETDATE (),
           	@gross,
           	@discount,
           	@tax,
           	@surch,
           	@net,
           	@paid,
           	@due,
           	@refund)

   COMMIT TRANSACTION
go
IF OBJECT_ID('Finances.SP_CreateNewInvoiceMaster') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Finances.SP_CreateNewInvoiceMaster >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Finances.SP_CreateNewInvoiceMaster >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_CreateNewLab] 
 */

CREATE PROCEDURE [Catalog].[SP_CreateNewLab]
	@isActive bit, 
	@code varchar(16), 
	@name varchar(40), 
	@printName varchar(40), 
	@printCanon bit, 
	@resType tinyint, 
	@isAux bit, 
	@postOE tinyint, 
	@postRE tinyint, 
	@postRV tinyint, 
	@postRF tinyint, 
	@postRC tinyint,   
	@refId smallint, 
	@lvlId smallint,
	@hdrId int
AS
	SET XACT_ABORT ON
	
	SET NOCOUNT ON 		
	
	BEGIN TRANSACTION
		
		DECLARE @newLabId SMALLINT
		
		INSERT INTO [Catalog].[Labs]
			([IsActive], [LabCode], [Name], [ReqPrintName], [ReqPrintCanonicalTestName], 
			[TestResultType], [IsAuxProcedure], [PostOrderEntryWorkflowStage], 
			[PostResultEntryWorkflowStage], [PostResultVerificationWorkflowStage],
         	[PostResultFinalizationWorkflowStage], [PostReportCollationWorkflowStage],         			
			[ReferralGroupId], [DiscountLevelId], [DefaultReportHeaderId])
		VALUES
        	(@isActive, @code, @name, @printName, @printCanon, @resType, @isAux, 
        	@postOE, @postRE, @postRV, @postRF, @postRC, @refId, @lvlId, @hdrId)
        		
		SELECT @newLabId = SCOPE_IDENTITY()
		
	COMMIT TRANSACTION
go
IF OBJECT_ID('Catalog.SP_CreateNewLab') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_CreateNewLab >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_CreateNewLab >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_CreateNewLabOrder] 
 */

/****** Object:  Sequence [PROE].[SEQ_LabOrders]    ******/
CREATE SEQUENCE [PROE].[SEQ_LabOrders]
	AS INT
		START WITH 0
		INCREMENT BY 1
		MINVALUE 0
		MAXVALUE 99999
		CACHE 
		CYCLE
GO

/****** Object:  Sequence [PROE].[SEQ_HL7AccessionNum]    ******/
CREATE SEQUENCE [PROE].[SEQ_HL7AccessionNum]
	AS SMALLINT
		START WITH 1
		INCREMENT BY 1
		MINVALUE 1
		MAXVALUE 9999
		CACHE
		CYCLE
GO

CREATE PROCEDURE [PROE].[SP_CreateNewLabOrder]
	@firstName varchar(120), 
	@sex tinyint, 
	@isReferrerUnknown bit,
	@newOrderId bigint OUTPUT
AS
BEGIN
	SET XACT_ABORT ON 	
 	SET NOCOUNT ON 		 	
	BEGIN TRANSACTION
		DECLARE @seqCounter INT
		SET @seqCounter = NEXT VALUE FOR [SEQ_LabOrders]
		
		DECLARE @cutoff INT
		SET @cutoff = 75
		
		DECLARE @letter CHAR(1)
		DECLARE @num INT

		SET @letter = CHAR(ASCII('A') + ((@seqCounter / @cutoff) % 26))
		SET @num = (@seqCounter % @cutoff) + 1
		
		DECLARE @orderNum VARCHAR(8)
		SET @orderNum = RTRIM(@letter + CAST(@num AS CHAR(4)))
			
		INSERT INTO [PROE].[PatientLabOrders]
			([OrderId], [WorkflowStage], [IsCancelled], [FirstName], [Sex], [IsReferrerUnknown])
		VALUES
			(@orderNum, 0, 0, @firstName, @sex, @isReferrerUnknown) 	
		
		SELECT @newOrderId = SCOPE_IDENTITY()				
	COMMIT TRANSACTION
END
go
IF OBJECT_ID('PROE.SP_CreateNewLabOrder') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_CreateNewLabOrder >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_CreateNewLabOrder >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_CreateNewLabOrderFull] 
 */

CREATE PROCEDURE [PROE].[SP_CreateNewLabOrderFull]
	@wfStage             TINYINT,
	@refId               INT = NULL,
	@refDisallow         BIT,
	@uid                 SMALLINT,
	@shiftId             INT,
	@reqLabId            INT = NULL,
	@title               VARCHAR (20) = NULL,
	@fname               VARCHAR (120),
	@lname               VARCHAR (120) = NULL,
	@sex                 TINYINT,
	@age                 VARCHAR (20) = NULL,
	@dob                 DATE = NULL,
	@phone               VARCHAR (20) = NULL,
	@email               VARCHAR (40) = NULL,
	@emailResult         BIT,
	@refUnk              BIT,
	@refCustName         VARCHAR (160) = NULL,
	@notes               VARCHAR (160) = NULL,
	@webToken            VARCHAR (8) = NULL,
	@regMemberId         BIGINT = NULL,
	@subTrackingId       VARCHAR (40) = NULL,
	@subIsExternal       BIT,
	@newOrderId          BIGINT OUTPUT
AS
BEGIN
	SET  XACT_ABORT ON
	SET  NOCOUNT ON

	BEGIN TRANSACTION
		DECLARE @seqCounter   INT
		SET @seqCounter = NEXT VALUE FOR [SEQ_LabOrders]
		
		DECLARE @cutoff   INT
		SET @cutoff = 75
		
		DECLARE @letter   CHAR (1)
		DECLARE @num   INT
		
		SET @letter = CHAR (ASCII ('A') + ( (@seqCounter / @cutoff) % 26))
		SET @num = (@seqCounter % @cutoff) + 1
		
		DECLARE @orderNum   VARCHAR (8)
		SET @orderNum = RTRIM (@letter + CAST (@num AS CHAR (4)))		
		
		INSERT INTO PROE.PatientLabOrders(
		  	  OrderId
			, OrderDateTime
			, WorkflowStage
			, LastModified
			, IsCancelled
			, ReferrerId
			, DisallowReferral
			, OrderingUserId
			, WorkShiftId
			, RequestingLabId
			, Title
			, FirstName
			, LastName
			, Sex
			, Age
			, DoB
			, PhoneNumber
			, EmailAddress
			, EmailTestResults
			, IsReferrerUnknown
			, ReferrerCustomName
			, OrderNotes
			, WebAccessToken
			, RegisteredMemberId
			, SubOrderTrackingId
			, IsExternalSubOrder
			, MirrorFlag)
		VALUES  (
		  	  @orderNum
			, GETDATE()
			, @wfStage
			, GETDATE()
			, 0
			, @refId
			, @refDisallow
			, @uid
			, @shiftId
			, @reqLabId
			, @title
			, @fname
			, @lname
			, @sex
			, @age
			, @dob
			, @phone
			, @email
			, @emailResult
			, @refUnk
			, @refCustName
			, @notes
			, @webToken
			, @regMemberId
			, @subTrackingId
			, @subIsExternal
			, 0)              
		
		SELECT @newOrderId = SCOPE_IDENTITY()
	COMMIT TRANSACTION
END
go
IF OBJECT_ID('PROE.SP_CreateNewLabOrderFull') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_CreateNewLabOrderFull >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_CreateNewLabOrderFull >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_CreateNewLabReportHeader] 
 */

CREATE PROCEDURE [Catalog].[SP_CreateNewLabReportHeader]
	@name varchar(80), 
	@labId smallint, 
	@isActive bit, 
	@sortPriority tinyint, 
	@headerContent varbinary(max), 
	@newItemId int OUT
AS
	SET XACT_ABORT ON
	
	SET NOCOUNT ON 		
	
	BEGIN TRANSACTION
	
		INSERT INTO Catalog.LabReportHeaders 
			(LabId
			, IsActive
			, Name
			, SortPriority
			, ReportHeader)
		VALUES
			(@labId
			, @isActive
			, @name
			, @sortPriority
			, @headerContent)
		
		SELECT @newItemId = SCOPE_IDENTITY()
	
	COMMIT TRANSACTION
go
IF OBJECT_ID('Catalog.SP_CreateNewLabReportHeader') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_CreateNewLabReportHeader >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_CreateNewLabReportHeader >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_CreateNewMarketingExec] 
 */

CREATE PROCEDURE [Catalog].[SP_CreateNewMarketingExec]
	@uid smallint
AS
	SET XACT_ABORT ON
	
	SET NOCOUNT ON 		
	
	BEGIN TRANSACTION	
	
		INSERT INTO [Marketing].[MarketingExecs]
			(Id)
		VALUES
			(@uid)
	
	COMMIT TRANSACTION
go
IF OBJECT_ID('Catalog.SP_CreateNewMarketingExec') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_CreateNewMarketingExec >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_CreateNewMarketingExec >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_CreateNewOrderedBillableItem] 
 */

CREATE PROCEDURE [PROE].[SP_CreateNewOrderedBillableItem]
	@invoiceId bigint
	, @itemId smallint
	, @price smallmoney
	, @quantity smallint
AS
	SET XACT_ABORT ON
	SET NOCOUNT ON
	BEGIN TRANSACTION
		INSERT INTO [PROE].[OrderedBillableItems]([InvoiceId]
												, [BillableItemId]
												, [UnitPrice]
												, [Quantity]
												, [DateCreated]
												, [IsCancelled])
		VALUES
			( @invoiceId
			, @itemId
			, @price
			, @quantity
			, GETDATE()
			, 0)
	COMMIT TRANSACTION
go
IF OBJECT_ID('PROE.SP_CreateNewOrderedBillableItem') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_CreateNewOrderedBillableItem >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_CreateNewOrderedBillableItem >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_CreateNewOrderedTest] 
 */

CREATE PROCEDURE [PROE].[SP_CreateNewOrderedTest]
	@invoiceId bigint
	, @testId smallint
	, @price smallmoney
	, @wfStage tinyint
	, @eta smalldatetime = NULL
	, @newId bigint OUTPUT
AS
	SET XACT_ABORT ON
	SET NOCOUNT ON
	BEGIN TRANSACTION
	
		INSERT INTO [PROE].[OrderedTests](InvoiceId
			, LabTestId
			, IsCancelled
			, UnitPrice
			, WorkflowStage
			, ResultsETA
			, DateCreated
			, LastModified)
		VALUES
			(@invoiceId
			, @testId
			, 0
			, @price
			, @wfStage
			, @eta
			, GETDATE()
			, GETDATE())
			
		SELECT @newId = SCOPE_IDENTITY()
		
COMMIT TRANSACTION
go
IF OBJECT_ID('PROE.SP_CreateNewOrderedTest') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_CreateNewOrderedTest >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_CreateNewOrderedTest >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_CreateNewReferrerCategory] 
 */

CREATE PROCEDURE [Catalog].[SP_CreateNewReferrerCategory]
	@name varchar(40), 
	@isActive bit
AS
	SET XACT_ABORT ON
	
	SET NOCOUNT ON 		
	
	BEGIN TRANSACTION	
	
		INSERT INTO [Catalog].[ReferrerCategories]
			(IsActive, Name)
		VALUES
			(@isActive, @name)
	
	COMMIT TRANSACTION
go
IF OBJECT_ID('Catalog.SP_CreateNewReferrerCategory') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_CreateNewReferrerCategory >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_CreateNewReferrerCategory >>>'
go


/* 
 * PROCEDURE: [TestResults].[SP_CreateNewSpecimenLabel] 
 */

CREATE PROCEDURE [TestResults].[SP_CreateNewSpecimenLabel]
  @invoiceId bigint, 
  @quantity smallint, 
  @vacType varchar(8), 
  @userId smallint
AS
BEGIN
	SET XACT_ABORT ON 	
 	SET NOCOUNT ON 		 	
	
	BEGIN TRANSACTION
    DECLARE @seqCounter INT
    SET @seqCounter = NEXT VALUE FOR  [PROE].[SEQ_HL7AccessionNum] 

    DECLARE @datePart VARCHAR(8)
    SET @datePart = CONVERT(VARCHAR(8),GETDATE(), 12)

    DECLARE @seqPart VARCHAR(4)
    SET @seqPart = REPLACE(STR(@seqCounter, 4, 0), ' ', '0')

    DECLARE @specId VARCHAR(12)
    SET @specId = @datePart + @seqPart			
    
    INSERT INTO TestResults.PhlebotomySpecimens (HL7SpecimentId,
                                                 InvoiceId,
                                                 Quantity,
                                                 CreatedBy,
                                                 VacutainerType)
    VALUES (@specId,
            @invoiceId,
            @quantity,
            @userId,
            @vacType)		
		
	COMMIT TRANSACTION
END
go
IF OBJECT_ID('TestResults.SP_CreateNewSpecimenLabel') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE TestResults.SP_CreateNewSpecimenLabel >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE TestResults.SP_CreateNewSpecimenLabel >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_CreateNewTemplateReport] 
 */

CREATE PROCEDURE [Catalog].[SP_CreateNewTemplateReport]
	@name varchar(160), 
	@tags varchar(160), 
	@sortPriority tinyint, 
	@lobContent varbinary(max), 
	@newItemId smallint OUT
AS
	SET XACT_ABORT ON
	
	SET NOCOUNT ON 		
	
	BEGIN TRANSACTION
	
		INSERT INTO [Catalog].[TemplateReports]
			([IsActive], [Name], [Tags], [SortPriority], [Content])
		VALUES
			(1, @name, @tags, @sortPriority, @lobContent)
	
		SELECT @newItemId = SCOPE_IDENTITY()
	
	COMMIT TRANSACTION
go
IF OBJECT_ID('Catalog.SP_CreateNewTemplateReport') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_CreateNewTemplateReport >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_CreateNewTemplateReport >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_CreateNewTemplateReportGroupLink] 
 */

CREATE PROCEDURE [Catalog].[SP_CreateNewTemplateReportGroupLink]
	@reportId smallint, 
	@groupId smallint
AS
	SET XACT_ABORT ON
	
	SET NOCOUNT ON 		
	
	BEGIN TRANSACTION	
	
		INSERT INTO 
			[Catalog].[TemplateGroupLinks](GroupId, TemplateId)
		VALUES
			(@groupId, @reportId)
	
	COMMIT TRANSACTION
go
IF OBJECT_ID('Catalog.SP_CreateNewTemplateReportGroupLink') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_CreateNewTemplateReportGroupLink >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_CreateNewTemplateReportGroupLink >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_CreateNewTestBILink] 
 */

CREATE PROCEDURE [Catalog].[SP_CreateNewTestBILink]
	@testId smallint, 
	@itemId smallint, 
	@optLevel tinyint, 
	@qty smallint
AS
	SET XACT_ABORT ON
	
	SET NOCOUNT ON 		
	
	BEGIN TRANSACTION	
	
	INSERT INTO Catalog.TestBILink (TestId
		, BillableItemId
		, OptimizationLevel
		, Quantity)
	VALUES
		(@testId, @itemId, @optLevel, @qty)
	
	COMMIT TRANSACTION
go
IF OBJECT_ID('Catalog.SP_CreateNewTestBILink') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_CreateNewTestBILink >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_CreateNewTestBILink >>>'
go


/* 
 * PROCEDURE: [Finances].[SP_CreateNewTransaction] 
 */

CREATE PROCEDURE [Finances].[SP_CreateNewTransaction]
	@invoiceId          BIGINT,
	@userId             SMALLINT = NULL,
	@authorizerId       SMALLINT = NULL,
	@shiftId            INT = NULL,
	@txType             TINYINT,
	@txFlag             TINYINT,
	@txAmount           MONEY,
	@ipAddr             INT = NULL,
	@remarks            VARCHAR (160) = NULL,
	@txId               BIGINT OUTPUT
AS
	SET XACT_ABORT ON

	SET NOCOUNT ON 		

	BEGIN TRANSACTION

		INSERT INTO [Finances].[InvoiceTransactions]
			([InvoiceId], [PerformingUserId], [AuthorizingUserId], [WorkShiftId], [TxTime], [TxType], [TxFlag], [TxAmount], [UserIpAddress], [UserRemarks])
		VALUES
			(@invoiceId, @userId, @authorizerId, @shiftId, CURRENT_TIMESTAMP, @txType, @txFlag, @txAmount, @ipAddr, @remarks)
	
		SELECT @txId = SCOPE_IDENTITY()
		
	COMMIT TRANSACTION
go
IF OBJECT_ID('Finances.SP_CreateNewTransaction') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Finances.SP_CreateNewTransaction >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Finances.SP_CreateNewTransaction >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_CreateNewUserTemplateGroupLink] 
 */

CREATE PROCEDURE [Catalog].[SP_CreateNewUserTemplateGroupLink]
	@userId smallint, 
	@tplGroupId smallint, 
	@newLinkId smallint OUT
AS
	SET XACT_ABORT ON
	
	BEGIN TRANSACTION
	
		INSERT INTO [Catalog].[UserTemplateGroupLinks]
			([UserId], [TemplateGroupId])
		VALUES
			(@userId, @tplGroupId)
		
		SELECT @newLinkId = SCOPE_IDENTITY()
	
	COMMIT TRANSACTION
go
IF OBJECT_ID('Catalog.SP_CreateNewUserTemplateGroupLink') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_CreateNewUserTemplateGroupLink >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_CreateNewUserTemplateGroupLink >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_CreateNewUserTemplateReport] 
 */

CREATE PROCEDURE [Catalog].[SP_CreateNewUserTemplateReport]
	@name varchar(160), 
	@tags varchar(160), 
	@sortPriority tinyint, 
	@tplGroupId smallint, 
	@lobContent varbinary(max), 
	@newItemId smallint OUT
AS
	SET XACT_ABORT ON
	
	SET NOCOUNT ON 		
	
	BEGIN TRANSACTION
	
		INSERT INTO [Catalog].[UserTemplateReports]
			([IsActive], [Name], [Tags], [SortPriority], [Content], [TemplateGroupLinkId])
		VALUES
			(1, @name, @tags, @sortPriority, @lobContent, @tplGroupId)
		
		SELECT @newItemId = SCOPE_IDENTITY()
	
	COMMIT TRANSACTION
go
IF OBJECT_ID('Catalog.SP_CreateNewUserTemplateReport') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_CreateNewUserTemplateReport >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_CreateNewUserTemplateReport >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_CreateReferrerCategoryLink] 
 */

CREATE PROCEDURE [Catalog].[SP_CreateReferrerCategoryLink]
  @catId  SMALLINT,
  @refId  INT
AS
  SET  XACT_ABORT ON

  DECLARE @n   INT

  SELECT
  	@n = COUNT( *)
  FROM
  	[Catalog].[ReferrerCategoryLink]
  WHERE
  	ReferrerId = @refId AND
  	CatergoryId = @catId


  IF @n = 0
    BEGIN
      BEGIN TRANSACTION

        INSERT INTO[Catalog].[ReferrerCategoryLink]
          (ReferrerId, CatergoryId)
        VALUES
          (@refId, @catId)

      COMMIT TRANSACTION
    END
go
IF OBJECT_ID('Catalog.SP_CreateReferrerCategoryLink') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_CreateReferrerCategoryLink >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_CreateReferrerCategoryLink >>>'
go


/* 
 * PROCEDURE: [Finances].[SP_EndWorkShift] 
 */


-- Create procedures section -------------------------------------------------

CREATE PROCEDURE [Finances].[SP_EndWorkShift]
	@ShiftId int
AS
	SET XACT_ABORT ON
	
 	SET NOCOUNT ON 		
 	
	BEGIN TRANSACTION

	UPDATE 
		[Finances].[WorkShifts]
	SET 
		[IsClosed] = 1, 
		[EndTime] = CURRENT_TIMESTAMP
	WHERE 
		[Id] = @ShiftId
		
	COMMIT TRANSACTION
go
IF OBJECT_ID('Finances.SP_EndWorkShift') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Finances.SP_EndWorkShift >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Finances.SP_EndWorkShift >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_FetchLabOrderDetails] 
 */

CREATE PROCEDURE [PROE].[SP_FetchLabOrderDetails]
	@invoiceId bigint
AS
	SELECT TOP 1
	      ord.[InvoiceId]
		, ord.[OrderId]
     	, ord.[OrderDateTime]
     	, ord.[IsCancelled]
     	, ord.[WorkflowStage]
     	, inv.[PaymentStatus]
     	, inv.[GrossPayable]
     	, inv.[TaxAmount]
     	, inv.[DiscountAmount]
     	, inv.[NetPayable]
     	, inv.[PaidAmount]
     	, inv.[DueAmount]
     	, ord.[PhoneNumber]
     	, ord.[Title]
     	, ord.[FirstName]
     	, ord.[LastName]
     	, ord.[DoB]
     	, ord.[Age]
     	, ord.[Sex]
     	, ord.[DisallowReferral]
     	, ord.[IsReferrerUnknown]
     	, ord.[ReferrerId]
     	, COALESCE(phy.[FullName], ord.[ReferrerCustomName], '') AS [ReferrerName]
     	, ord.[OrderingUserId]
     	, usr.[DisplayName] AS [OrderingUserName]
     	, ord.[OrderNotes]
	FROM
  		[Finances].[InvoiceMaster] inv
  		INNER JOIN [PROE].[PatientLabOrders] ord
    		ON inv.[InvoiceId] = ord.[InvoiceId]
  		LEFT OUTER JOIN [Catalog].[Referrers] phy
    		ON ord.[ReferrerId] = phy.[Id]
  		INNER JOIN [Staff].[Users] usr
    		ON ord.[OrderingUserId] = usr.[Id]
	WHERE
  		ord.[InvoiceId] = @invoiceId
go
IF OBJECT_ID('PROE.SP_FetchLabOrderDetails') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_FetchLabOrderDetails >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_FetchLabOrderDetails >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_FilterActiveLabOrdersByDateRange] 
 */

CREATE PROCEDURE [PROE].[SP_FilterActiveLabOrdersByDateRange]
	@dtFrom datetime
	, @dtTo datetime
	, @prefixArray varchar(60)
AS
-- split the string into SQL wild-card patterns
	DECLARE @charCursor CURSOR
	
	SET @charCursor = CURSOR FOR
	SELECT ItemValue + '%'
	FROM
		[dbo].[Split](@prefixArray, ',')
	
	DECLARE @tmpFilterOrder TABLE (
		InvoiceId BIGINT NOT NULL,
		OrderId VARCHAR(8) NOT NULL,
		OrderDateTime SMALLDATETIME NOT NULL,
		WorkflowStage TINYINT NOT NULL,
    IsExternalSubOrder BIT NOT NULL,
		PatientName VARCHAR(MAX) NOT NULL,
		Sex TINYINT NOT NULL,
		Age VARCHAR(20),
		DoB DATE,
		ReferrerName VARCHAR(MAX)
	)  
	
	DECLARE @pattern VARCHAR(2)
	
	OPEN @charCursor
	
	FETCH NEXT FROM @charCursor INTO @pattern
	WHILE @@FETCH_STATUS = 0
	BEGIN
		--SELECT * INTO #tmpTable FROM [MyTable] WHERE [MyId] LIKE @pattern   
		INSERT INTO @tmpFilterOrder
		SELECT ord.[InvoiceId]
     		, ord.[OrderId]
     		, ord.[OrderDateTime]
     		, ord.[WorkflowStage]
        , ord.[IsExternalSubOrder]
     		, ord.[FullName] AS [PatientName]
     		, ord.[Sex]
     		, ord.[Age]
     		, ord.[DoB]
     		, COALESCE(phy.[FullName], ord.[ReferrerCustomName], '') AS [ReferrerName]
		FROM
  			[PROE].[PatientLabOrders] ord
  			LEFT OUTER JOIN [Catalog].[Referrers] phy
    			ON ord.[ReferrerId] = phy.[Id]
		WHERE
  			ord.[IsCancelled] = 0
  			AND ord.[OrderDateTime] BETWEEN @dtFrom AND @dtTo
  			AND ord.[OrderId] LIKE @pattern
		ORDER BY
  			ord.[InvoiceId]
		
		FETCH NEXT FROM @charCursor INTO @pattern
	END
	
	CLOSE @charCursor
	
	DEALLOCATE @charCursor
	
	-- return the values
	SELECT *
	FROM
	@tmpFilterOrder
go
IF OBJECT_ID('PROE.SP_FilterActiveLabOrdersByDateRange') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_FilterActiveLabOrdersByDateRange >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_FilterActiveLabOrdersByDateRange >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_FilterActiveLabOrdersByWorkflowAndDateRange] 
 */

CREATE PROCEDURE [PROE].[SP_FilterActiveLabOrdersByWorkflowAndDateRange]
	@wfStage tinyint,
	@dtFrom datetime,
	@dtTo datetime, 
	@prefixArray varchar(60)
AS
	-- split the string into SQL wild-card patterns
	DECLARE @charCursor CURSOR
	
	SET @charCursor = CURSOR FOR
	SELECT ItemValue + '%'
	FROM
		[dbo].[Split](@prefixArray, ',')
	
	DECLARE @tmpFilterOrder TABLE (
		InvoiceId BIGINT NOT NULL,
		OrderId VARCHAR(8) NOT NULL,
		OrderDateTime SMALLDATETIME NOT NULL,
		WorkflowStage TINYINT NOT NULL,
		IsExternalSubOrder BIT NOT NULL,
		PatientName VARCHAR(MAX) NOT NULL,
		Sex TINYINT NOT NULL,
		Age VARCHAR(20),
		DoB DATE,
		ReferrerName VARCHAR(MAX)
	)
	
	DECLARE @pattern VARCHAR(2)
	
	OPEN @charCursor
	
	FETCH NEXT FROM @charCursor INTO @pattern
	WHILE @@FETCH_STATUS = 0
	BEGIN
		--SELECT * INTO #tmpTable FROM [MyTable] WHERE [MyId] LIKE @pattern   
		INSERT INTO @tmpFilterOrder
		SELECT ord.[InvoiceId]
     		, ord.[OrderId]
     		, ord.[OrderDateTime]
     		, ord.[WorkflowStage]
     		, ord.[IsExternalSubOrder]
     		, ord.[FullName] AS [PatientName]
     		, ord.[Sex]
     		, ord.[Age]
     		, ord.[DoB]
     		, COALESCE(phy.[FullName], ord.[ReferrerCustomName], '') AS [ReferrerName]
		FROM
  			[PROE].[PatientLabOrders] ord
  			LEFT OUTER JOIN [Catalog].[Referrers] phy
    			ON ord.[ReferrerId] = phy.[Id]
		WHERE
  			ord.[IsCancelled] = 0
  			AND ord.[WorkflowStage] <= @wfStage
  			AND ord.[OrderDateTime] BETWEEN @dtFrom AND @dtTo
  			AND ord.[OrderId] LIKE @pattern
		ORDER BY
  			ord.[InvoiceId]
  					
		FETCH NEXT FROM @charCursor INTO @pattern
	END
	
	CLOSE @charCursor
	
	DEALLOCATE @charCursor
	
	-- return the values
	SELECT *
	FROM
	@tmpFilterOrder
go
IF OBJECT_ID('PROE.SP_FilterActiveLabOrdersByWorkflowAndDateRange') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_FilterActiveLabOrdersByWorkflowAndDateRange >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_FilterActiveLabOrdersByWorkflowAndDateRange >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_FilterLabOrdersByDateRange] 
 */

CREATE PROCEDURE [PROE].[SP_FilterLabOrdersByDateRange]
  @beginDate  DATE,
  @endDate    DATE
AS
  SET  NOCOUNT ON

  SELECT
    inv.InvoiceId,
    ord.OrderId,
    ord.OrderDateTime,
    ord.IsCancelled,
    ord.IsExternalSubOrder,
    ord.FullName AS PatientName,
    COALESCE(
      ref.FullName,
      ord.ReferrerCustomName,
      '')
      AS ReferrerName,
    inv.GrossPayable,
    inv.DiscountAmount,
    inv.TaxAmount,
    inv.SurchargeAmount,
    inv.NetPayable,
    inv.PaidAmount,
    inv.DueAmount,
    inv.RefundAmount,
    ord.IsReferrerUnknown,
    ord.DisallowReferral
  FROM
    Finances.InvoiceMaster inv
    INNER JOIN PROE.PatientLabOrders ord ON ord.InvoiceId = inv.InvoiceId
    LEFT OUTER JOIN Catalog.Referrers ref ON ord.ReferrerId = ref.Id
  WHERE
    CONVERT(
      DATE,
      ord.OrderDateTime) BETWEEN @beginDate
                             AND @endDate
  ORDER BY
    inv.InvoiceId
go
IF OBJECT_ID('PROE.SP_FilterLabOrdersByDateRange') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_FilterLabOrdersByDateRange >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_FilterLabOrdersByDateRange >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_FilterLabOrdersByExcludePerformingLabDateRange] 
 */

CREATE TYPE [PROE].SmallIntListTableType AS TABLE (n SMALLINT NOT NULL PRIMARY KEY)
GO

CREATE PROCEDURE [PROE].[SP_FilterLabOrdersByExcludePerformingLabDateRange]
	@beginDate date
	, @endDate date
	, @labIds PROE.SmallIntListTableType READONLY
AS
  SET NOCOUNT ON
  
  SELECT inv.InvoiceId
      , ord.OrderId
      , ord.OrderDateTime
      , ord.IsCancelled
      , ord.IsExternalSubOrder
      , ord.FullName AS PatientName
      , COALESCE(ref.FullName, ord.ReferrerCustomName, '') AS ReferrerName
      , inv.GrossPayable
      , inv.DiscountAmount
      , inv.TaxAmount
      , inv.SurchargeAmount
      , inv.NetPayable
      , inv.PaidAmount
      , inv.DueAmount
      , inv.RefundAmount
      , ord.IsReferrerUnknown
      , ord.DisallowReferral
  FROM Finances.InvoiceMaster inv
    INNER JOIN PROE.PatientLabOrders ord ON ord.InvoiceId = inv.InvoiceId
    LEFT OUTER JOIN Catalog.Referrers ref ON ord.ReferrerId = ref.Id
  WHERE
    CONVERT(DATE, ord.OrderDateTime) BETWEEN @beginDate AND @endDate
    AND 0 <> (
      SELECT COUNT(ot.Id)
      FROM
        PROE.OrderedTests ot
        INNER JOIN Catalog.LabTests lt ON ot.LabTestId = lt.Id
      WHERE
        ot.InvoiceId = inv.InvoiceId
        AND lt.PerformingLabId NOT IN (SELECT n FROM @labIds)    
    )
  ORDER BY
    inv.InvoiceId
go
IF OBJECT_ID('PROE.SP_FilterLabOrdersByExcludePerformingLabDateRange') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_FilterLabOrdersByExcludePerformingLabDateRange >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_FilterLabOrdersByExcludePerformingLabDateRange >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_FilterLabOrdersByExcludePerformingLabDateRangeReferrerCategory] 
 */

CREATE PROCEDURE [PROE].[SP_FilterLabOrdersByExcludePerformingLabDateRangeReferrerCategory]
	@beginDate DATE, 
	@endDate DATE, 
	@catId SMALLINT, 
	@labIds [PROE].[SmallIntListTableType] READONLY
AS
SET NOCOUNT ON

SELECT
  inv.InvoiceId,
  ord.OrderId,
  ord.OrderDateTime,
  ord.IsCancelled,
  ord.IsExternalSubOrder,
  ord.FullName AS PatientName,
  COALESCE(ref.FullName, ord.ReferrerCustomName, '') AS ReferrerName,  inv.GrossPayable,
  inv.DiscountAmount,
  inv.TaxAmount,
  inv.SurchargeAmount,
  inv.NetPayable,
  inv.PaidAmount,
  inv.DueAmount,
  inv.RefundAmount
  , ord.IsReferrerUnknown
  , ord.DisallowReferral
FROM
  Finances.InvoiceMaster inv
  INNER JOIN PROE.PatientLabOrders ord ON ord.InvoiceId = inv.InvoiceId
  INNER JOIN Catalog.Referrers ref ON ord.ReferrerId = ref.Id
  INNER JOIN Catalog.ReferrerCategoryLink rclnk ON rclnk.ReferrerId = ref.Id
  INNER JOIN Catalog.ReferrerCategories cat ON rclnk.CatergoryId = cat.Id
WHERE
  CONVERT(DATE, ord.OrderDateTime) BETWEEN @beginDate AND @endDate AND
  cat.Id = @catId AND
  0 <>
    (SELECT
       COUNT( ot.Id)
     FROM
       PROE.OrderedTests ot
       INNER JOIN Catalog.LabTests lt ON ot.LabTestId = lt.Id
     WHERE
       ot.InvoiceId = inv.InvoiceId AND
       lt.PerformingLabId NOT IN (SELECT n FROM @labIds))
ORDER BY
  inv.InvoiceId
go
IF OBJECT_ID('PROE.SP_FilterLabOrdersByExcludePerformingLabDateRangeReferrerCategory') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_FilterLabOrdersByExcludePerformingLabDateRangeReferrerCategory >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_FilterLabOrdersByExcludePerformingLabDateRangeReferrerCategory >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_FilterLabOrdersByIncludePerformingLabDateRange] 
 */

CREATE PROCEDURE [PROE].[SP_FilterLabOrdersByIncludePerformingLabDateRange]
	@beginDate date
	, @endDate date
	, @labIds PROE.SmallIntListTableType READONLY
AS
  SET NOCOUNT ON
  SELECT inv.InvoiceId
      , ord.OrderId
      , ord.OrderDateTime
      , ord.IsCancelled
      , ord.IsExternalSubOrder
      , ord.FullName AS PatientName
      , COALESCE(ref.FullName, ord.ReferrerCustomName, '') AS ReferrerName
      , inv.GrossPayable
      , inv.DiscountAmount
      , inv.TaxAmount
      , inv.SurchargeAmount
      , inv.NetPayable
      , inv.PaidAmount
      , inv.DueAmount
      , inv.RefundAmount
      , ord.IsReferrerUnknown
      , ord.DisallowReferral
  FROM Finances.InvoiceMaster inv
    INNER JOIN PROE.PatientLabOrders ord ON ord.InvoiceId = inv.InvoiceId
    LEFT OUTER JOIN Catalog.Referrers ref ON ord.ReferrerId = ref.Id
  WHERE
    CONVERT(DATE, ord.OrderDateTime) BETWEEN @beginDate AND @endDate
    AND 0 <> (
      SELECT COUNT(ot.Id)
      FROM
        PROE.OrderedTests ot
        INNER JOIN Catalog.LabTests lt ON ot.LabTestId = lt.Id
      WHERE
        ot.InvoiceId = inv.InvoiceId
        AND lt.PerformingLabId IN (SELECT n FROM @labIds)    
    )
  ORDER BY
    inv.InvoiceId
go
IF OBJECT_ID('PROE.SP_FilterLabOrdersByIncludePerformingLabDateRange') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_FilterLabOrdersByIncludePerformingLabDateRange >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_FilterLabOrdersByIncludePerformingLabDateRange >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_FilterLabOrdersByIncludePerformingLabDateRangeReferrerCategory] 
 */

CREATE PROCEDURE [PROE].[SP_FilterLabOrdersByIncludePerformingLabDateRangeReferrerCategory]
	@beginDate DATE, 
	@endDate DATE, 
	@catId SMALLINT, 
	@labIds [PROE].[SmallIntListTableType] READONLY
AS
SET NOCOUNT ON

SELECT
  inv.InvoiceId,
  ord.OrderId,
  ord.OrderDateTime,
  ord.IsCancelled,
  ord.IsExternalSubOrder,
  ord.FullName AS PatientName,
  COALESCE(ref.FullName, ord.ReferrerCustomName, '') AS ReferrerName,  inv.GrossPayable,
  inv.DiscountAmount,
  inv.TaxAmount,
  inv.SurchargeAmount,
  inv.NetPayable,
  inv.PaidAmount,
  inv.DueAmount,
  inv.RefundAmount
  , ord.IsReferrerUnknown
  , ord.DisallowReferral
FROM
  Finances.InvoiceMaster inv
  INNER JOIN PROE.PatientLabOrders ord ON ord.InvoiceId = inv.InvoiceId
  INNER JOIN Catalog.Referrers ref ON ord.ReferrerId = ref.Id
  INNER JOIN Catalog.ReferrerCategoryLink rclnk ON rclnk.ReferrerId = ref.Id
  INNER JOIN Catalog.ReferrerCategories cat ON rclnk.CatergoryId = cat.Id
WHERE
  CONVERT(DATE, ord.OrderDateTime) BETWEEN @beginDate AND @endDate AND
  cat.Id = @catId AND
  0 <>
    (SELECT
       COUNT( ot.Id)
     FROM
       PROE.OrderedTests ot
       INNER JOIN Catalog.LabTests lt ON ot.LabTestId = lt.Id
     WHERE
       ot.InvoiceId = inv.InvoiceId AND
       lt.PerformingLabId IN (SELECT n FROM @labIds))
ORDER BY
  inv.InvoiceId
go
IF OBJECT_ID('PROE.SP_FilterLabOrdersByIncludePerformingLabDateRangeReferrerCategory') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_FilterLabOrdersByIncludePerformingLabDateRangeReferrerCategory >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_FilterLabOrdersByIncludePerformingLabDateRangeReferrerCategory >>>'
go


/* 
 * PROCEDURE: [Finances].[SP_FindWorkshiftByDateRange] 
 */

CREATE PROCEDURE [Finances].[SP_FindWorkshiftByDateRange]
	@dtStart smalldatetime
	, @dtEnd smalldatetime
AS
	SET NOCOUNT ON
	
	SELECT s.Id
		, s.UserId
		, u.DisplayName AS UserName
		, r.Name AS RoleName
		, s.IsClosed
		, s.StartTime
		, s.EndTime
		, s.NumOrders
		, s.AdditionalBalance
		, s.ReceiveAmount
		, s.DiscountAmount
		, s.DiscountRebateAmount
		, s.RefundAmount
		, s.FinalBalance
		, s.UserNotes
	FROM
		Finances.WorkShifts s
		INNER JOIN Staff.Users u
			ON s.UserId = u.Id
		INNER JOIN RBAC.Roles r
			ON u.RoleId = r.Id
	WHERE
		s.StartTime BETWEEN @dtStart AND @dtEnd
go
IF OBJECT_ID('Finances.SP_FindWorkshiftByDateRange') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Finances.SP_FindWorkshiftByDateRange >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Finances.SP_FindWorkshiftByDateRange >>>'
go


/* 
 * PROCEDURE: [Finances].[SP_FindWorkshiftByDateRangeRoles] 
 */

CREATE PROCEDURE [Finances].[SP_FindWorkshiftByDateRangeRoles]
	@dtStart smalldatetime
	, @dtEnd smalldatetime
	, @roleIds [PROE].[SmallIntListTableType] READONLY
AS
	SET NOCOUNT ON
	
	SELECT s.Id
		, s.UserId
		, u.DisplayName AS UserName
		, r.Name AS RoleName
		, s.IsClosed
		, s.StartTime
		, s.EndTime
		, s.NumOrders
		, s.AdditionalBalance
		, s.ReceiveAmount
		, s.DiscountAmount
		, s.DiscountRebateAmount
		, s.RefundAmount
		, s.FinalBalance
		, s.UserNotes
	FROM
		Finances.WorkShifts s
		INNER JOIN Staff.Users u
			ON s.UserId = u.Id
		INNER JOIN RBAC.Roles r
			ON u.RoleId = r.Id
	WHERE
		s.StartTime BETWEEN @dtStart AND @dtEnd
		AND r.Id IN (SELECT n FROM @roleIds)
go
IF OBJECT_ID('Finances.SP_FindWorkshiftByDateRangeRoles') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Finances.SP_FindWorkshiftByDateRangeRoles >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Finances.SP_FindWorkshiftByDateRangeRoles >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_GenerateHL7AccessionNumber] 
 */

CREATE PROCEDURE [PROE].[SP_GenerateHL7AccessionNumber]
AS
	DECLARE @seqCounter SMALLINT = NEXT VALUE FOR [PROE].[SEQ_HL7AccessionNum]

	SELECT CONCAT(CONVERT(CHAR(6), GETDATE(), 12), REPLACE(STR(@seqCounter, 6, 0), ' ', '0')) AS AccessionNum
go
IF OBJECT_ID('PROE.SP_GenerateHL7AccessionNumber') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_GenerateHL7AccessionNumber >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_GenerateHL7AccessionNumber >>>'
go


/* 
 * PROCEDURE: [Marketing].[SP_GetActiveLabOrderTestsWithReferralByDateRange] 
 */

CREATE PROCEDURE [Marketing].[SP_GetActiveLabOrderTestsWithReferralByDateRange]
  @dtFrom  SMALLDATETIME,
  @dtTo    SMALLDATETIME
AS
  SET  NOCOUNT ON

  SELECT
    ord.InvoiceId,
    ord.OrderId,
    ord.OrderDateTime,
    ord.IsCancelled AS OrderIsCancelled,
    ord.FullName AS PatientName,
    ord.Sex,
    ord.Age,
    ord.ReferrerId,
    ord.DisallowReferral,
    ord.IsExternalSubOrder,
    ord.IsReferrerUnknown,
    ord.ReferrerCustomName,
    ord.OrderNotes,
    inv.GrossPayable,
    inv.DiscountAmount,
    inv.TaxAmount,
    inv.SurchargeAmount,
    inv.NetPayable,
    inv.PaidAmount,
    inv.DueAmount,
    inv.RefundAmount,
    lt.ShortName AS TestName,
    ot.IsCancelled AS TestIsCancelled,
    ot.UnitPrice AS TestPrice,
    lab.Name AS LabName,
    lab.IsAuxProcedure,
    lab.ReferralGroupDisplayName,
    rg.Name AS ReferralGroupName,
    rg.IsActive AS ReferralGroupIsActive,
    rg.ReferralMode,
    rg.ReferralPercent,
    rg.ReferralAmount,
    phy.FullName AS ReferrerName,
    phy.IsActive AS ReferrerIsActive,
    cls.Name AS ReferralClassName,
    cls.ReferralEligible,
    phycat.Name AS ReferrerCategoryName,
    phycat.IsActive AS ReferrerCategoryIsActive
  FROM
    PROE.OrderedTests ot
    INNER JOIN PROE.PatientLabOrders ord ON ot.InvoiceId = ord.InvoiceId
    INNER JOIN Catalog.LabTests lt ON ot.LabTestId = lt.Id
    INNER JOIN Catalog.Labs lab ON lt.PerformingLabId = lab.Id
    INNER JOIN Marketing.ReferralGroups rg ON lab.ReferralGroupId = rg.Id
    INNER JOIN Finances.InvoiceMaster inv ON inv.InvoiceId = ord.InvoiceId
    LEFT OUTER JOIN Catalog.Referrers phy ON ord.ReferrerId = phy.Id
    INNER JOIN Marketing.ReferralClasses cls ON phy.ReferralClassId = cls.Id
    LEFT OUTER JOIN Catalog.ReferrerCategoryLink phy_cat_lnk
      ON phy_cat_lnk.ReferrerId = phy.Id
    LEFT OUTER JOIN Catalog.ReferrerCategories phycat
      ON phy_cat_lnk.CatergoryId = phycat.Id
  WHERE
    ord.OrderDateTime BETWEEN @dtFrom AND @dtTo AND
    ord.IsCancelled = 0 AND
    ot.IsCancelled = 0 AND
    lt.IsActive = 1 AND
    lab.IsActive = 1 AND
    phy.IsActive = 1 AND
    rg.IsActive = 1

go
IF OBJECT_ID('Marketing.SP_GetActiveLabOrderTestsWithReferralByDateRange') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Marketing.SP_GetActiveLabOrderTestsWithReferralByDateRange >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Marketing.SP_GetActiveLabOrderTestsWithReferralByDateRange >>>'
go


/* 
 * PROCEDURE: [Marketing].[SP_GetActiveLabOrderTestsWithReferralByDateRangeReferrer] 
 */

CREATE PROCEDURE [Marketing].[SP_GetActiveLabOrderTestsWithReferralByDateRangeReferrer]
  @dtFrom  SMALLDATETIME,
  @dtTo    SMALLDATETIME,
  @refId   INT
  WITH
  EXEC AS CALLER
AS
  SET  NOCOUNT ON

  SELECT
    ord.InvoiceId,
    ord.OrderId,
    ord.OrderDateTime,
    ord.IsCancelled AS OrderIsCancelled,
    ord.FullName AS PatientName,
    ord.Sex,
    ord.Age,
    ord.ReferrerId,
    ord.DisallowReferral,
    ord.IsExternalSubOrder,
    ord.IsReferrerUnknown,
    ord.ReferrerCustomName,
    ord.OrderNotes,
    inv.GrossPayable,
    inv.DiscountAmount,
    inv.TaxAmount,
    inv.SurchargeAmount,
    inv.NetPayable,
    inv.PaidAmount,
    inv.DueAmount,
    inv.RefundAmount,
    lt.ShortName AS TestName,
    ot.IsCancelled AS TestIsCancelled,
    ot.UnitPrice AS TestPrice,
    lab.Name AS LabName,
    lab.IsAuxProcedure,
    lab.ReferralGroupDisplayName,
    rg.Name AS ReferralGroupName,
    rg.IsActive AS ReferralGroupIsActive,
    rg.ReferralMode,
    rg.ReferralPercent,
    rg.ReferralAmount,
    phy.FullName AS ReferrerName,
    phy.IsActive AS ReferrerIsActive,
    cls.Name AS ReferralClassName,
    cls.ReferralEligible,
    phycat.Name AS ReferrerCategoryName,
    phycat.IsActive AS ReferrerCategoryIsActive
  FROM
    PROE.OrderedTests ot
    INNER JOIN PROE.PatientLabOrders ord ON ot.InvoiceId = ord.InvoiceId
    INNER JOIN Catalog.LabTests lt ON ot.LabTestId = lt.Id
    INNER JOIN Catalog.Labs lab ON lt.PerformingLabId = lab.Id
    INNER JOIN Marketing.ReferralGroups rg ON lab.ReferralGroupId = rg.Id
    INNER JOIN Finances.InvoiceMaster inv ON inv.InvoiceId = ord.InvoiceId
    LEFT OUTER JOIN Catalog.Referrers phy ON ord.ReferrerId = phy.Id
    INNER JOIN Marketing.ReferralClasses cls ON phy.ReferralClassId = cls.Id
    LEFT OUTER JOIN Catalog.ReferrerCategoryLink phy_cat_lnk
      ON phy_cat_lnk.ReferrerId = phy.Id
    LEFT OUTER JOIN Catalog.ReferrerCategories phycat
      ON phy_cat_lnk.CatergoryId = phycat.Id
  WHERE
    ord.OrderDateTime BETWEEN @dtFrom AND @dtTo AND
    ord.IsCancelled = 0 AND
    ot.IsCancelled = 0 AND
    lt.IsActive = 1 AND
    lab.IsActive = 1 AND
    phy.IsActive = 1 AND
    rg.IsActive = 1 AND
    phy.Id = @refId

go
IF OBJECT_ID('Marketing.SP_GetActiveLabOrderTestsWithReferralByDateRangeReferrer') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Marketing.SP_GetActiveLabOrderTestsWithReferralByDateRangeReferrer >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Marketing.SP_GetActiveLabOrderTestsWithReferralByDateRangeReferrer >>>'
go


/* 
 * PROCEDURE: [Marketing].[SP_GetActiveLabOrderTestsWithReferralByDateRangeReferrerCategory] 
 */

CREATE PROCEDURE [Marketing].[SP_GetActiveLabOrderTestsWithReferralByDateRangeReferrerCategory]
  @dtFrom  SMALLDATETIME,
  @dtTo    SMALLDATETIME,
  @catId   SMALLINT
  WITH
  EXEC AS CALLER
AS
  SET  NOCOUNT ON

  SELECT
    ord.InvoiceId,
    ord.OrderId,
    ord.OrderDateTime,
    ord.IsCancelled AS OrderIsCancelled,
    ord.FullName AS PatientName,
    ord.Sex,
    ord.Age,
    ord.ReferrerId,
    ord.DisallowReferral,
    ord.IsExternalSubOrder,
    ord.IsReferrerUnknown,
    ord.ReferrerCustomName,
    ord.OrderNotes,
    inv.GrossPayable,
    inv.DiscountAmount,
    inv.TaxAmount,
    inv.SurchargeAmount,
    inv.NetPayable,
    inv.PaidAmount,
    inv.DueAmount,
    inv.RefundAmount,
    lt.ShortName AS TestName,
    ot.IsCancelled AS TestIsCancelled,
    ot.UnitPrice AS TestPrice,
    lab.Name AS LabName,
    lab.IsAuxProcedure,
    lab.ReferralGroupDisplayName,
    rg.Name AS ReferralGroupName,
    rg.IsActive AS ReferralGroupIsActive,
    rg.ReferralMode,
    rg.ReferralPercent,
    rg.ReferralAmount,
    phy.FullName AS ReferrerName,
    phy.IsActive AS ReferrerIsActive,
    cls.Name AS ReferralClassName,
    cls.ReferralEligible,
    phycat.Name AS ReferrerCategoryName,
    phycat.IsActive AS ReferrerCategoryIsActive
  FROM
    PROE.OrderedTests ot
    INNER JOIN PROE.PatientLabOrders ord ON ot.InvoiceId = ord.InvoiceId
    INNER JOIN Catalog.LabTests lt ON ot.LabTestId = lt.Id
    INNER JOIN Catalog.Labs lab ON lt.PerformingLabId = lab.Id
    INNER JOIN Marketing.ReferralGroups rg ON lab.ReferralGroupId = rg.Id
    INNER JOIN Finances.InvoiceMaster inv ON inv.InvoiceId = ord.InvoiceId
    LEFT OUTER JOIN Catalog.Referrers phy ON ord.ReferrerId = phy.Id
    INNER JOIN Marketing.ReferralClasses cls ON phy.ReferralClassId = cls.Id
    LEFT OUTER JOIN Catalog.ReferrerCategoryLink phy_cat_lnk
      ON phy_cat_lnk.ReferrerId = phy.Id
    LEFT OUTER JOIN Catalog.ReferrerCategories phycat
      ON phy_cat_lnk.CatergoryId = phycat.Id
  WHERE
    ord.OrderDateTime BETWEEN @dtFrom AND @dtTo AND
    ord.IsCancelled = 0 AND
    ot.IsCancelled = 0 AND
    lt.IsActive = 1 AND
    lab.IsActive = 1 AND
    phy.IsActive = 1 AND
    rg.IsActive = 1 AND
    phycat.Id = @catId
go
IF OBJECT_ID('Marketing.SP_GetActiveLabOrderTestsWithReferralByDateRangeReferrerCategory') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Marketing.SP_GetActiveLabOrderTestsWithReferralByDateRangeReferrerCategory >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Marketing.SP_GetActiveLabOrderTestsWithReferralByDateRangeReferrerCategory >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_GetActiveLabReportHeadersListForLab] 
 */

CREATE PROCEDURE [Catalog].[SP_GetActiveLabReportHeadersListForLab]
	@labId smallint
AS
	SET NOCOUNT ON
	
	SELECT [Id]
		, [Name]
		, [SortPriority]
	FROM
		[Catalog].[LabReportHeaders]
	WHERE
		[LabId] = @labId
		AND [IsActive] = 1
	ORDER BY
		[SortPriority]

go
IF OBJECT_ID('Catalog.SP_GetActiveLabReportHeadersListForLab') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_GetActiveLabReportHeadersListForLab >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_GetActiveLabReportHeadersListForLab >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_GetActiveLabs] 
 */

CREATE PROCEDURE [Catalog].[SP_GetActiveLabs]
AS
	SET NOCOUNT ON
	
	SELECT [Id] 
		, [IsActive] 
		, [Name] 
		, [LabCode] 
		, [ReqPrintName] 
		, [ReqPrintCanonicalTestName] 
		, [TestResultType] 
		, [IsAuxProcedure] 
		, [PostOrderEntryWorkflowStage] 
		, [PostResultEntryWorkflowStage] 
		, [PostResultVerificationWorkflowStage] 
		, [PostResultFinalizationWorkflowStage] 
		, [PostReportCollationWorkflowStage] 
		, [DiscountLevelId] 
		, [ReferralGroupId]
		, [ReferralGroupDisplayName]
		, [AccountingGroupDisplayName]
		, [DefaultReportHeaderId]
	FROM
		[Catalog].[Labs]
	WHERE
		[IsActive] = 1
go
IF OBJECT_ID('Catalog.SP_GetActiveLabs') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_GetActiveLabs >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_GetActiveLabs >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_GetActiveOrderedItemsInInvoice] 
 */

CREATE PROCEDURE [PROE].[SP_GetActiveOrderedItemsInInvoice]
	@InvoiceId bigint
AS
	SET NOCOUNT ON

	SELECT
  		bi.[Name],
  		obi.[Quantity],
  		obi.[UnitPrice] * obi.[Quantity] AS Price
	FROM
  		[PROE].[OrderedBillableItems] obi
  		INNER JOIN [PROE].[PatientLabOrders] ord
    		ON obi.InvoiceId = ord.InvoiceId
  		INNER JOIN [Catalog].[BillableItems] bi
    		ON obi.BillableItemId = bi.Id
	WHERE
  		bi.IsActive = 1 AND
  		obi.IsCancelled = 0 AND
  		ord.IsCancelled = 0 AND
  		ord.InvoiceId = @InvoiceId
go
IF OBJECT_ID('PROE.SP_GetActiveOrderedItemsInInvoice') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_GetActiveOrderedItemsInInvoice >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_GetActiveOrderedItemsInInvoice >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_GetActiveOrderedTestsCountInInvoice] 
 */

CREATE PROCEDURE [PROE].[SP_GetActiveOrderedTestsCountInInvoice]
  @invoiceId BIGINT
AS
  SET NOCOUNT ON

  SELECT
    COUNT(*) AS Count
  FROM
    [PROE].[OrderedTests]
  WHERE
    [IsCancelled] = 0 AND
    [InvoiceId] = @invoiceId
go
IF OBJECT_ID('PROE.SP_GetActiveOrderedTestsCountInInvoice') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_GetActiveOrderedTestsCountInInvoice >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_GetActiveOrderedTestsCountInInvoice >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_GetActiveOrderedTestsInInvoice] 
 */

CREATE PROCEDURE [PROE].[SP_GetActiveOrderedTestsInInvoice]
	@invoiceId bigint
AS
	SET NOCOUNT ON
	
    SELECT
    	ot.[Id] as OrderedTestId,
        test.[Id] as TestId,
  		test.[ShortName],
  		test.[CanonicalName],
    	ot.[IsCancelled],
    	ot.[WorkflowStage],
    	tatg.TATRank,
    	ot.[ResultsETA],
  		test.[ReqSlipPrintOption],
  		test.[ReportSortPriority],
  		plab.[Id] AS LabId,
  		plab.[ReqPrintName] AS LabName,
  		plab.[ReqPrintCanonicalTestName],
  		rlab.[TestResultType],
  		rlab.[IsAuxProcedure],
  		ot.[UnitPrice]
	FROM
  		[PROE].[OrderedTests] ot
  		INNER JOIN [PROE].[PatientLabOrders] ord
    		ON ot.[InvoiceId] = ord.[InvoiceId]
  		INNER JOIN [Catalog].[LabTests] test
    		ON ot.[LabTestId] = test.[Id]
  		INNER JOIN [Catalog].[Labs] plab
    		ON test.[PerformingLabId] = plab.[Id]
  		INNER JOIN [Catalog].[Labs] rlab
    		ON test.[ResultingLabId] = rlab.[Id]
  		INNER JOIN [Catalog].[TATGroups] tatg
      		ON test.[TATGroupId] = tatg.[Id]
	WHERE
  		ot.[IsCancelled] = 0 AND
  		ord.[IsCancelled] = 0 AND
  		plab.[IsActive] = 1 AND
  		rlab.[IsActive] = 1 AND
  		test.[IsActive] = 1 AND
  		ord.[InvoiceId] = @invoiceId
	ORDER BY
      	test.[ReportSortPriority] DESC
go
IF OBJECT_ID('PROE.SP_GetActiveOrderedTestsInInvoice') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_GetActiveOrderedTestsInInvoice >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_GetActiveOrderedTestsInInvoice >>>'
go


/* 
 * PROCEDURE: [TestResults].[SP_GetActiveResultBundlesCountForInvoice] 
 */

CREATE PROCEDURE [TestResults].[SP_GetActiveResultBundlesCountForInvoice]
	@invoiceId bigint
AS
	SET NOCOUNT ON
	
	SELECT COUNT(*) 
	FROM
		[TestResults].[ResultBundles]
	WHERE
		[InvoiceId] = @invoiceId
		AND [IsActive] = 1
go
IF OBJECT_ID('TestResults.SP_GetActiveResultBundlesCountForInvoice') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE TestResults.SP_GetActiveResultBundlesCountForInvoice >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE TestResults.SP_GetActiveResultBundlesCountForInvoice >>>'
go


/* 
 * PROCEDURE: [TestResults].[SP_GetActiveResultBundlesCountForInvoiceLab] 
 */

CREATE PROCEDURE [TestResults].[SP_GetActiveResultBundlesCountForInvoiceLab]
	@invoiceId bigint, 
	@labId smallint
AS
	SET NOCOUNT ON
	
	SELECT
        COUNT(*) 
	FROM
  		[TestResults].[ResultBundles] bun
  		INNER JOIN [PROE].[PatientLabOrders] ord ON (bun.InvoiceId = ord.InvoiceId)
  		INNER JOIN [Catalog].[Labs] lab ON (bun.LabId = lab.Id)
	WHERE  
  		bun.[IsActive] = 1 AND
  		lab.[IsActive] = 1 AND
  		lab.[IsAuxProcedure] = 0 AND
  		ord.[IsCancelled] = 0 AND
  		ord.[InvoiceId] = @invoiceId AND
  		lab.[Id] = @labId
go
IF OBJECT_ID('TestResults.SP_GetActiveResultBundlesCountForInvoiceLab') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE TestResults.SP_GetActiveResultBundlesCountForInvoiceLab >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE TestResults.SP_GetActiveResultBundlesCountForInvoiceLab >>>'
go


/* 
 * PROCEDURE: [TestResults].[SP_GetActiveResultBundlesForInvoice] 
 */

CREATE PROCEDURE [TestResults].[SP_GetActiveResultBundlesForInvoice]
	@invoiceId bigint
AS
	SET NOCOUNT ON	

	SELECT 
    	bun.[Id],
    	lab.[Id] AS LabId,
    	bun.[ReportHeaderId],
    	bun.[TestResultType],
    	bun.[DisplayTitle],
    	bun.[ComponentLabTests],
    	bun.[WorkflowStage],
    	bun.[TATRank],
    	bun.[LastUpdated],
    	lab.[PostOrderEntryWorkflowStage],
    	lab.[PostResultEntryWorkflowStage],
    	lab.[PostResultVerificationWorkflowStage],
    	lab.[PostResultFinalizationWorkflowStage],
    	lab.[PostReportCollationWorkflowStage],
    	bun.[FinalizingConsultantName],
    	bun.[ResultNotes]
	FROM
  		[TestResults].[ResultBundles] bun
  		INNER JOIN [PROE].[PatientLabOrders] ord ON (bun.InvoiceId = ord.InvoiceId)
  		INNER JOIN [Catalog].[Labs] lab ON (bun.LabId = lab.Id)
	WHERE  
  		bun.[IsActive] = 1 AND
  		lab.[IsActive] = 1 AND
  		lab.[IsAuxProcedure] = 0 AND
  		ord.[IsCancelled] = 0 AND
  		ord.[InvoiceId] = @invoiceId
go
IF OBJECT_ID('TestResults.SP_GetActiveResultBundlesForInvoice') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE TestResults.SP_GetActiveResultBundlesForInvoice >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE TestResults.SP_GetActiveResultBundlesForInvoice >>>'
go


/* 
 * PROCEDURE: [TestResults].[SP_GetActiveResultBundlesInInvoiceForUser] 
 */

CREATE PROCEDURE [TestResults].[SP_GetActiveResultBundlesInInvoiceForUser]
	@invoiceId bigint, 
	@userId smallint
AS
	SET NOCOUNT ON
	
	/* Check if the user is restricted to certain lab groups */
	DECLARE @sandbox_count int
	
	SELECT 	
  		@sandbox_count = COUNT(*)
	FROM
  		[Staff].[ConsultantSandboxedGroupsLink] uglink
  		INNER JOIN [Catalog].[SandboxedLabGroups] [group]
    		ON uglink.[GroupId] = [group].[Id]
  		INNER JOIN [Catalog].[SandboxedGroupLabsLink] lglink
    		ON lglink.[GroupId] = [group].[Id]
  		INNER JOIN [Catalog].[Labs] lab
    		ON lglink.[LabId] = lab.[Id]
	WHERE
  		uglink.[UserId] = @userId AND
  		lab.[IsAuxProcedure] = 0 AND
  		lab.[IsActive] = 1
  	
	IF @sandbox_count = 0
	BEGIN
		SELECT 
    		bun.[Id],
    		lab.[Id] AS LabId,
    		bun.[ReportHeaderId],
    		bun.[TestResultType],
    		bun.[DisplayTitle],
    		bun.[ComponentLabTests],
    		bun.[WorkflowStage],
    		bun.[TATRank],
    		bun.[LastUpdated],
    		lab.[PostOrderEntryWorkflowStage],
    		lab.[PostResultEntryWorkflowStage],
    		lab.[PostResultVerificationWorkflowStage],
    		lab.[PostResultFinalizationWorkflowStage],
    		lab.[PostReportCollationWorkflowStage],
    		bun.[FinalizingConsultantName],
    		bun.[ResultNotes]
		FROM
  			[TestResults].[ResultBundles] bun
  			INNER JOIN [PROE].[PatientLabOrders] ord 
				ON (bun.InvoiceId = ord.InvoiceId)
  			INNER JOIN [Catalog].[Labs] lab 
				ON (bun.LabId = lab.Id)
		WHERE  
  			bun.[IsActive] = 1 AND
  			lab.[IsActive] = 1 AND
  			lab.[IsAuxProcedure] = 0 AND
  			ord.[IsCancelled] = 0 AND
  			ord.[InvoiceId] = @invoiceId
	END
	ELSE
	BEGIN
		SELECT 
    		bun.[Id],
    		lab.[Id] AS LabId,
    		bun.[ReportHeaderId],
    		bun.[TestResultType],
    		bun.[DisplayTitle],
    		bun.[ComponentLabTests],
    		bun.[WorkflowStage],
    		bun.[TATRank],
    		bun.[LastUpdated],
    		lab.[PostOrderEntryWorkflowStage],
    		lab.[PostResultEntryWorkflowStage],
    		lab.[PostResultVerificationWorkflowStage],
    		lab.[PostResultFinalizationWorkflowStage],
    		lab.[PostReportCollationWorkflowStage],
    		bun.[FinalizingConsultantName],
    		bun.[ResultNotes]
		FROM
	  		[Catalog].[SandboxedGroupLabsLink] lg_link
	  		INNER JOIN [Catalog].[SandboxedLabGroups] grp 
				ON (lg_link.[GroupId] = grp.[Id])
	  		INNER JOIN [Staff].[ConsultantSandboxedGroupsLink] cg_link 
				ON (grp.[Id] = cg_link.[GroupId])
	  		INNER JOIN [Catalog].[Labs] lab 
				ON (lg_link.[LabId] = lab.[Id])
	  		INNER JOIN [TestResults].[ResultBundles] bun 
				ON (bun.[LabId] = lab.[Id])
	  		INNER JOIN [PROE].[PatientLabOrders] ord 
				ON (bun.[InvoiceId] = ord.[InvoiceId])
		WHERE
	  		bun.[IsActive] = 1 AND 
	  		lab.[IsActive] = 1 AND 
	  		lab.[IsAuxProcedure] = 0 AND
	  		ord.[IsCancelled] = 0 AND 
	  		ord.[InvoiceId] = @invoiceId AND 
	  		cg_link.[UserId] = @userId
	END

go
IF OBJECT_ID('TestResults.SP_GetActiveResultBundlesInInvoiceForUser') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE TestResults.SP_GetActiveResultBundlesInInvoiceForUser >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE TestResults.SP_GetActiveResultBundlesInInvoiceForUser >>>'
go


/* 
 * PROCEDURE: [TestResults].[SP_GetActiveResultBundleStatusForInvoice] 
 */

CREATE PROCEDURE [TestResults].[SP_GetActiveResultBundleStatusForInvoice]
	@invoiceId bigint
AS
    SET NOCOUNT ON	

	SELECT 
    	bun.[Id],
    	lab.[Id] AS LabId,
    	bun.[TestResultType],
    	bun.[WorkflowStage]
	FROM
  		[TestResults].[ResultBundles] bun
  		INNER JOIN [PROE].[PatientLabOrders] ord ON (bun.InvoiceId = ord.InvoiceId)
  		INNER JOIN [Catalog].[Labs] lab ON (bun.LabId = lab.Id)
	WHERE  
  		bun.[IsActive] = 1 AND
  		lab.[IsActive] = 1 AND
  		lab.[IsAuxProcedure] = 0 AND
  		ord.[IsCancelled] = 0 AND
  		ord.[InvoiceId] = @invoiceId
go
IF OBJECT_ID('TestResults.SP_GetActiveResultBundleStatusForInvoice') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE TestResults.SP_GetActiveResultBundleStatusForInvoice >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE TestResults.SP_GetActiveResultBundleStatusForInvoice >>>'
go


/* 
 * PROCEDURE: [TestResults].[SP_GetActiveResultBundleWithInvoiceDetails] 
 */

CREATE PROCEDURE [TestResults].[SP_GetActiveResultBundleWithInvoiceDetails]
  @bundleId BIGINT
  WITH
  EXEC AS CALLER
AS
  SET  NOCOUNT ON

  SELECT
    TOP 1
    rb.Id AS BundleId,
    rb.DisplayTitle AS BundleTitle,
    rb.WorkflowStage AS BundleWorkflowStage,
    rb.TestResultType AS BundleResultType,
    ord.InvoiceId,
    ord.OrderId,
    ord.OrderDateTime,
    ord.FullName AS PatientName,
    inv.DueAmount
  FROM
    TestResults.ResultBundles rb
    INNER JOIN PROE.PatientLabOrders ord ON rb.InvoiceId = ord.InvoiceId
    INNER JOIN Finances.InvoiceMaster inv ON inv.InvoiceId = ord.InvoiceId
  WHERE
    rb.Id = @bundleId AND
    rb.IsActive = 1 AND
    ord.IsCancelled = 0
go
IF OBJECT_ID('TestResults.SP_GetActiveResultBundleWithInvoiceDetails') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE TestResults.SP_GetActiveResultBundleWithInvoiceDetails >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE TestResults.SP_GetActiveResultBundleWithInvoiceDetails >>>'
go


/* 
 * PROCEDURE: [RBAC].[SP_GetActiveRolesWithPermissionCode] 
 */

CREATE PROCEDURE [RBAC].[SP_GetActiveRolesWithPermissionCode]
  @permCode VARCHAR(32)
AS
  SET  NOCOUNT ON

  SELECT
    role.Id,
    role.Name,
    role.IsAdmin
  FROM
    RBAC.RolePermissions rplnk
    INNER JOIN RBAC.permissions prm ON rplnk.PermissionId = prm.Id
    INNER JOIN RBAC.Roles role ON rplnk.RoleId = role.Id
  WHERE
    prm.PermCode = @permCode AND
    role.IsActive = 1
go
IF OBJECT_ID('RBAC.SP_GetActiveRolesWithPermissionCode') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE RBAC.SP_GetActiveRolesWithPermissionCode >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE RBAC.SP_GetActiveRolesWithPermissionCode >>>'
go


/* 
 * PROCEDURE: [TestResults].[SP_GetActiveTestsInResultBundle] 
 */

CREATE PROCEDURE [TestResults].[SP_GetActiveTestsInResultBundle]
	@bundleId bigint
AS
	SET NOCOUNT ON 
	
	SELECT 
  		ot.[Id] AS OrderedTestId,
  		ot.[LabTestId],
  		test.[ShortName] AS TestName,
  		ot.[WorkflowStage],
  		ot.[ResultsETA],
  		test.[ReportSortPriority]
	FROM
  		[PROE].[OrderedTests] ot
  		INNER JOIN [TestResults].[ResultBundles] bun ON (ot.[ResultBundleId] = bun.[Id])
  		INNER JOIN [Catalog].[LabTests] test ON (ot.[LabTestId] = test.[Id])
  		INNER JOIN [Catalog].[Labs] lab ON (test.[ResultingLabId] = lab.[Id])
	WHERE
  		bun.[IsActive] = 1 AND 
  		ot.[IsCancelled] = 0 AND 
  		test.[IsActive] = 1 AND 
  		lab.[IsActive] = 1 AND
  		lab.[IsAuxProcedure] = 0 AND
  		bun.[Id] = @bundleId
go
IF OBJECT_ID('TestResults.SP_GetActiveTestsInResultBundle') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE TestResults.SP_GetActiveTestsInResultBundle >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE TestResults.SP_GetActiveTestsInResultBundle >>>'
go


/* 
 * PROCEDURE: [Marketing].[SP_GetAllActiveMarketingExecs] 
 */

CREATE PROCEDURE [Marketing].[SP_GetAllActiveMarketingExecs]
AS
	SET NOCOUNT ON 	
	
	SELECT
		mex.[Id],
		usr.[FirstName],
		usr.[LastName],
		usr.[DisplayName],
		usr.[IsActive]
	FROM
		[Marketing].[MarketingExecs] mex
		INNER JOIN [Staff].[Users] usr
			ON mex.[Id] = usr.[Id]
	WHERE
		usr.[IsActive] = 1
	ORDER BY
		usr.[DisplayName]
go
IF OBJECT_ID('Marketing.SP_GetAllActiveMarketingExecs') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Marketing.SP_GetAllActiveMarketingExecs >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Marketing.SP_GetAllActiveMarketingExecs >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_GetAllActiveReferrers] 
 */

CREATE PROCEDURE [Catalog].[SP_GetAllActiveReferrers]
AS
	SET NOCOUNT ON
	
	SELECT phy.Id
     	, phy.IsActive
     	, phy.Prefix
     	, phy.Name
     	, phy.Suffix
     	, phy.IdentifyingTag
     	, phy.MobilePhone
     	, phy.FullName
     	, NULL as CategoryId 
     	, NULL as CategoryName
     	, phy.ReferralClassId
     	, class.Name AS ReferralClassName
     	, class.ReferralEligible
	FROM
  	Catalog.Referrers phy
  	INNER JOIN Marketing.ReferralClasses class
    	ON phy.ReferralClassId = class.Id
	WHERE
			phy.[IsActive] = 1
go
IF OBJECT_ID('Catalog.SP_GetAllActiveReferrers') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_GetAllActiveReferrers >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_GetAllActiveReferrers >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_GetAllActiveTemplateGroups] 
 */

CREATE PROCEDURE [Catalog].[SP_GetAllActiveTemplateGroups]
AS
	SET NOCOUNT ON
	
	SELECT [Id]
     	, [IsActive]
     	, [Name]
     	, [LabId]
	FROM
		[Catalog].[TemplateGroups]
	WHERE
		[IsActive] = 1

go
IF OBJECT_ID('Catalog.SP_GetAllActiveTemplateGroups') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_GetAllActiveTemplateGroups >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_GetAllActiveTemplateGroups >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_GetAllActiveTemplateGroupsForLab] 
 */

CREATE PROCEDURE [Catalog].[SP_GetAllActiveTemplateGroupsForLab]
	@labId smallint
AS
	SET NOCOUNT ON
	
	SELECT [Id]
		, [IsActive]
		, [Name]
		, [LabId]
	FROM
		[Catalog].[TemplateGroups]
	WHERE
		[LabId] = @labId
		AND [IsActive] = 1
go
IF OBJECT_ID('Catalog.SP_GetAllActiveTemplateGroupsForLab') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_GetAllActiveTemplateGroupsForLab >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_GetAllActiveTemplateGroupsForLab >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_GetAllActiveTemplateReportsInGroup] 
 */

CREATE PROCEDURE [Catalog].[SP_GetAllActiveTemplateReportsInGroup]
	@groupId smallint
AS
	SET NOCOUNT ON
	
	SELECT 
  		TemplateReports.[Id]
 		,TemplateReports.[IsActive]
 		,TemplateReports.[Name]
 		,TemplateReports.[SortPriority]
 		,TemplateReports.[Tags]
	FROM 
		[Catalog].TemplateGroupLinks
		INNER JOIN [Catalog].TemplateGroups
			ON TemplateGroupLinks.GroupId = TemplateGroups.Id
		INNER JOIN [Catalog].TemplateReports
			ON TemplateGroupLinks.TemplateId = TemplateReports.Id
	WHERE 
		TemplateGroups.Id = @groupId AND 
		TemplateReports.IsActive = 1
go
IF OBJECT_ID('Catalog.SP_GetAllActiveTemplateReportsInGroup') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_GetAllActiveTemplateReportsInGroup >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_GetAllActiveTemplateReportsInGroup >>>'
go


/* 
 * PROCEDURE: [Staff].[SP_GetAllActiveUsersWithRole] 
 */

CREATE PROCEDURE [Staff].[SP_GetAllActiveUsersWithRole]
AS
	SET NOCOUNT ON
	
	SELECT 
		u.[Id] AS UserId,
		u.[UserName],
		u.[PassHash],
		r.[Name],
		r.[IsAdmin], 
		r.[RoleCode]
	FROM
		[Staff].[Users] u
		INNER JOIN [RBAC].[Roles] r 
			ON (u.[RoleId] = r.[Id])
	WHERE
		u.[IsActive] = 1 AND
		r.[IsActive] = 1

go
IF OBJECT_ID('Staff.SP_GetAllActiveUsersWithRole') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Staff.SP_GetAllActiveUsersWithRole >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Staff.SP_GetAllActiveUsersWithRole >>>'
go


/* 
 * PROCEDURE: [APP_SYS].[SP_GetAllAuditRecordsForInvoice] 
 */

CREATE PROCEDURE [APP_SYS].[SP_GetAllAuditRecordsForInvoice]
	@invoiceId bigint
AS
  	SET NOCOUNT ON
	
	SELECT 
       	t.Id
       	, t.PerformingUserId
     	, u.DisplayName AS PerformingUserName
     	, t.PatientLabOrderId
     	, t.ResultBundleId
     	, t.EventTime
     	, t.EventCategory
     	, t.EventType
     	, t.WorkflowStage
     	, t.UserIpAddress
     	, t.Note     
	FROM
  		APP_SYS.AuditTrails t
  		LEFT OUTER JOIN Staff.Users u
    		ON t.PerformingUserId = u.Id
	WHERE
      	t.PatientLabOrderId = @invoiceId
	ORDER BY
  		t.Id DESC
go
IF OBJECT_ID('APP_SYS.SP_GetAllAuditRecordsForInvoice') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE APP_SYS.SP_GetAllAuditRecordsForInvoice >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE APP_SYS.SP_GetAllAuditRecordsForInvoice >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_GetAllDiscreteReportLineItemsForTest] 
 */

CREATE PROCEDURE [Catalog].[SP_GetAllDiscreteReportLineItemsForTest]
	@testId smallint
AS
	SET NOCOUNT ON
	
	SELECT [Id]
		, [SortOrder]
		, [IsResultableItem]
		, [IndentLevel]
		, [Parameter]
		, [DefaultResult]
		, [Units]
		, [ReferenceRange]
	FROM
		[Catalog].[DiscreteReportLineItems]
	WHERE
		[LabTestId] = @testId
go
IF OBJECT_ID('Catalog.SP_GetAllDiscreteReportLineItemsForTest') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_GetAllDiscreteReportLineItemsForTest >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_GetAllDiscreteReportLineItemsForTest >>>'
go


/* 
 * PROCEDURE: [Marketing].[SP_GetAllEligibleReferrers] 
 */

CREATE PROCEDURE [Marketing].[SP_GetAllEligibleReferrers]
AS
	SET NOCOUNT ON
  	
	SELECT ref.Id
     	, ref.FullName
     	, ref.SuppressNetReferral
	FROM
  		Catalog.Referrers ref
  		INNER JOIN Marketing.ReferralClasses class
    		ON ref.ReferralClassId = class.Id
	WHERE
  		ref.IsActive = 1
  		AND class.ReferralEligible = 1
go
IF OBJECT_ID('Marketing.SP_GetAllEligibleReferrers') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Marketing.SP_GetAllEligibleReferrers >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Marketing.SP_GetAllEligibleReferrers >>>'
go


/* 
 * PROCEDURE: [Marketing].[SP_GetAllLabOrderTestsWithReferralByDateRange] 
 */

CREATE PROCEDURE [Marketing].[SP_GetAllLabOrderTestsWithReferralByDateRange]
  @dtFrom  SMALLDATETIME,
  @dtTo    SMALLDATETIME
AS
  SET  NOCOUNT ON

  SELECT
    ord.InvoiceId,
    ord.OrderId,
    ord.OrderDateTime,
    ord.IsCancelled AS OrderIsCancelled,
    ord.FullName AS PatientName,
    ord.Sex,
    ord.Age,
    ord.ReferrerId,
    ord.DisallowReferral,
    ord.IsExternalSubOrder,
    ord.IsReferrerUnknown,
    ord.ReferrerCustomName,
    ord.OrderNotes,
    inv.GrossPayable,
    inv.DiscountAmount,
    inv.TaxAmount,
    inv.SurchargeAmount,
    inv.NetPayable,
    inv.PaidAmount,
    inv.DueAmount,
    inv.RefundAmount,
    lt.ShortName AS TestName,
    ot.IsCancelled AS TestIsCancelled,
    ot.UnitPrice AS TestPrice,
    lab.Name AS LabName,
    lab.IsAuxProcedure,
    lab.ReferralGroupDisplayName,
    rg.Name AS ReferralGroupName,
    rg.IsActive AS ReferralGroupIsActive,
    rg.ReferralMode,
    rg.ReferralPercent,
    rg.ReferralAmount,
    phy.FullName AS ReferrerName,
    phy.IsActive AS ReferrerIsActive,
    cls.Name AS ReferralClassName,
    cls.ReferralEligible,
    phycat.Name AS ReferrerCategoryName,
    phycat.IsActive AS ReferrerCategoryIsActive
  FROM
    PROE.OrderedTests ot
    INNER JOIN PROE.PatientLabOrders ord ON ot.InvoiceId = ord.InvoiceId
    INNER JOIN Catalog.LabTests lt ON ot.LabTestId = lt.Id
    INNER JOIN Catalog.Labs lab ON lt.PerformingLabId = lab.Id
    INNER JOIN Marketing.ReferralGroups rg ON lab.ReferralGroupId = rg.Id
    INNER JOIN Finances.InvoiceMaster inv ON inv.InvoiceId = ord.InvoiceId
    LEFT OUTER JOIN Catalog.Referrers phy ON ord.ReferrerId = phy.Id
    INNER JOIN Marketing.ReferralClasses cls ON phy.ReferralClassId = cls.Id
    LEFT OUTER JOIN Catalog.ReferrerCategoryLink phy_cat_lnk
      ON phy_cat_lnk.ReferrerId = phy.Id
    LEFT OUTER JOIN Catalog.ReferrerCategories phycat
      ON phy_cat_lnk.CatergoryId = phycat.Id
  WHERE
    ord.OrderDateTime BETWEEN @dtFrom AND @dtTo
go
IF OBJECT_ID('Marketing.SP_GetAllLabOrderTestsWithReferralByDateRange') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Marketing.SP_GetAllLabOrderTestsWithReferralByDateRange >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Marketing.SP_GetAllLabOrderTestsWithReferralByDateRange >>>'
go


/* 
 * PROCEDURE: [Marketing].[SP_GetAllLabOrderTestsWithReferralByDateRangeReferrer] 
 */

CREATE PROCEDURE [Marketing].[SP_GetAllLabOrderTestsWithReferralByDateRangeReferrer]
  @dtFrom  SMALLDATETIME,
  @dtTo    SMALLDATETIME,
  @refId   INT
  WITH
  EXEC AS CALLER
AS
  SET  NOCOUNT ON

  SELECT
    ord.InvoiceId,
    ord.OrderId,
    ord.OrderDateTime,
    ord.IsCancelled AS OrderIsCancelled,
    ord.FullName AS PatientName,
    ord.Sex,
    ord.Age,
    ord.ReferrerId,
    ord.DisallowReferral,
    ord.IsExternalSubOrder,
    ord.IsReferrerUnknown,
    ord.ReferrerCustomName,
    ord.OrderNotes,
    inv.GrossPayable,
    inv.DiscountAmount,
    inv.TaxAmount,
    inv.SurchargeAmount,
    inv.NetPayable,
    inv.PaidAmount,
    inv.DueAmount,
    inv.RefundAmount,
    lt.ShortName AS TestName,
    ot.IsCancelled AS TestIsCancelled,
    ot.UnitPrice AS TestPrice,
    lab.Name AS LabName,
    lab.IsAuxProcedure,
    lab.ReferralGroupDisplayName,
    rg.Name AS ReferralGroupName,
    rg.IsActive AS ReferralGroupIsActive,
    rg.ReferralMode,
    rg.ReferralPercent,
    rg.ReferralAmount,
    phy.FullName AS ReferrerName,
    phy.IsActive AS ReferrerIsActive,
    cls.Name AS ReferralClassName,
    cls.ReferralEligible,
    phycat.Name AS ReferrerCategoryName,
    phycat.IsActive AS ReferrerCategoryIsActive
  FROM
    PROE.OrderedTests ot
    INNER JOIN PROE.PatientLabOrders ord ON ot.InvoiceId = ord.InvoiceId
    INNER JOIN Catalog.LabTests lt ON ot.LabTestId = lt.Id
    INNER JOIN Catalog.Labs lab ON lt.PerformingLabId = lab.Id
    INNER JOIN Marketing.ReferralGroups rg ON lab.ReferralGroupId = rg.Id
    INNER JOIN Finances.InvoiceMaster inv ON inv.InvoiceId = ord.InvoiceId
    LEFT OUTER JOIN Catalog.Referrers phy ON ord.ReferrerId = phy.Id
    INNER JOIN Marketing.ReferralClasses cls ON phy.ReferralClassId = cls.Id
    LEFT OUTER JOIN Catalog.ReferrerCategoryLink phy_cat_lnk
      ON phy_cat_lnk.ReferrerId = phy.Id
    LEFT OUTER JOIN Catalog.ReferrerCategories phycat
      ON phy_cat_lnk.CatergoryId = phycat.Id
  WHERE
    ord.OrderDateTime BETWEEN @dtFrom AND @dtTo AND
    phy.Id = @refId
go
IF OBJECT_ID('Marketing.SP_GetAllLabOrderTestsWithReferralByDateRangeReferrer') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Marketing.SP_GetAllLabOrderTestsWithReferralByDateRangeReferrer >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Marketing.SP_GetAllLabOrderTestsWithReferralByDateRangeReferrer >>>'
go


/* 
 * PROCEDURE: [Marketing].[SP_GetAllLabOrderTestsWithReferralByDateRangeReferrerCategory] 
 */

CREATE PROCEDURE [Marketing].[SP_GetAllLabOrderTestsWithReferralByDateRangeReferrerCategory]
  @dtFrom  SMALLDATETIME,
  @dtTo    SMALLDATETIME,
  @catId   SMALLINT
  WITH
  EXEC AS CALLER
AS
  SET  NOCOUNT ON

  SELECT
    ord.InvoiceId,
    ord.OrderId,
    ord.OrderDateTime,
    ord.IsCancelled AS OrderIsCancelled,
    ord.FullName AS PatientName,
    ord.Sex,
    ord.Age,
    ord.ReferrerId,
    ord.DisallowReferral,
    ord.IsExternalSubOrder,
    ord.IsReferrerUnknown,
    ord.ReferrerCustomName,
    ord.OrderNotes,
    inv.GrossPayable,
    inv.DiscountAmount,
    inv.TaxAmount,
    inv.SurchargeAmount,
    inv.NetPayable,
    inv.PaidAmount,
    inv.DueAmount,
    inv.RefundAmount,
    lt.ShortName AS TestName,
    ot.IsCancelled AS TestIsCancelled,
    ot.UnitPrice AS TestPrice,
    lab.Name AS LabName,
    lab.IsAuxProcedure,
    lab.ReferralGroupDisplayName,
    rg.Name AS ReferralGroupName,
    rg.IsActive AS ReferralGroupIsActive,
    rg.ReferralMode,
    rg.ReferralPercent,
    rg.ReferralAmount,
    phy.FullName AS ReferrerName,
    phy.IsActive AS ReferrerIsActive,
    cls.Name AS ReferralClassName,
    cls.ReferralEligible,
    phycat.Name AS ReferrerCategoryName,
    phycat.IsActive AS ReferrerCategoryIsActive
  FROM
    PROE.OrderedTests ot
    INNER JOIN PROE.PatientLabOrders ord ON ot.InvoiceId = ord.InvoiceId
    INNER JOIN Catalog.LabTests lt ON ot.LabTestId = lt.Id
    INNER JOIN Catalog.Labs lab ON lt.PerformingLabId = lab.Id
    INNER JOIN Marketing.ReferralGroups rg ON lab.ReferralGroupId = rg.Id
    INNER JOIN Finances.InvoiceMaster inv ON inv.InvoiceId = ord.InvoiceId
    LEFT OUTER JOIN Catalog.Referrers phy ON ord.ReferrerId = phy.Id
    INNER JOIN Marketing.ReferralClasses cls ON phy.ReferralClassId = cls.Id
    LEFT OUTER JOIN Catalog.ReferrerCategoryLink phy_cat_lnk
      ON phy_cat_lnk.ReferrerId = phy.Id
    LEFT OUTER JOIN Catalog.ReferrerCategories phycat
      ON phy_cat_lnk.CatergoryId = phycat.Id
  WHERE
    ord.OrderDateTime BETWEEN @dtFrom AND @dtTo AND
    phycat.Id = @catId
go
IF OBJECT_ID('Marketing.SP_GetAllLabOrderTestsWithReferralByDateRangeReferrerCategory') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Marketing.SP_GetAllLabOrderTestsWithReferralByDateRangeReferrerCategory >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Marketing.SP_GetAllLabOrderTestsWithReferralByDateRangeReferrerCategory >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_GetAllLabReportHeaders] 
 */

CREATE PROCEDURE [Catalog].[SP_GetAllLabReportHeaders]
AS
	SET NOCOUNT ON
	
	SELECT [Id]
		, [LabId]
		, [IsActive]
		, [Name]
		, [SortPriority]
		, [ReportHeader]
	FROM
		[Catalog].[LabReportHeaders]
go
IF OBJECT_ID('Catalog.SP_GetAllLabReportHeaders') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_GetAllLabReportHeaders >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_GetAllLabReportHeaders >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_GetAllLinkedBillableItemsForTest] 
 */

CREATE PROCEDURE [Catalog].[SP_GetAllLinkedBillableItemsForTest]
	@testId smallint
AS
	SET NOCOUNT ON
	
	SELECT lnk.Id
		, lnk.Quantity
		, lnk.OptimizationLevel
		, lnk.BillableItemId
	FROM
		Catalog.TestBILink lnk
		INNER JOIN Catalog.LabTests test
			ON lnk.TestId = test.Id
	WHERE
		test.Id = @testId
go
IF OBJECT_ID('Catalog.SP_GetAllLinkedBillableItemsForTest') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_GetAllLinkedBillableItemsForTest >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_GetAllLinkedBillableItemsForTest >>>'
go


/* 
 * PROCEDURE: [Finances].[SP_GetAllNonMatchingTransactionsInDateRange] 
 */

CREATE PROCEDURE [Finances].[SP_GetAllNonMatchingTransactionsInDateRange]
  @txType  TINYINT,
  @dtFrom  SMALLDATETIME,
  @dtTo    SMALLDATETIME
AS
  SET  NOCOUNT ON

  SELECT
    tx.InvoiceId,
    ord.OrderId,
    ord.OrderDateTime,
    ref.FullName AS ReferredBy,
    ord.ReferrerId,
    usr.DisplayName AS PerformedBy,
    tx.PerformingUserId,
    tx.WorkShiftId,
    tx.TxTime,
    tx.TxType,
    tx.TxFlag,
    tx.TxAmount,
    tx.UserIpAddress,
    tx.UserRemarks AS TxRemarks,
    ord.IsCancelled AS InvoiceIsCancelled,
    inv.GrossPayable AS InvoiceGross,
    inv.DiscountAmount AS InvoiceDiscount,
    inv.NetPayable AS InvoicePayable,
    inv.PaidAmount AS InvoicePaid,
    inv.DueAmount AS InvoiceDue,
    inv.RefundAmount AS InvoiceRefund
  FROM
    Finances.InvoiceTransactions tx
    INNER JOIN Finances.InvoiceMaster inv ON tx.InvoiceId = inv.InvoiceId
    INNER JOIN Staff.Users usr ON tx.PerformingUserId = usr.Id
    INNER JOIN PROE.PatientLabOrders ord ON inv.InvoiceId = ord.InvoiceId
    LEFT OUTER JOIN Catalog.Referrers ref ON ord.ReferrerId = ref.Id
  WHERE
    tx.TxType <> @txType AND
    tx.TxTime BETWEEN @dtFrom AND @dtTo
  ORDER BY
    tx.InvoiceId
go
IF OBJECT_ID('Finances.SP_GetAllNonMatchingTransactionsInDateRange') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Finances.SP_GetAllNonMatchingTransactionsInDateRange >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Finances.SP_GetAllNonMatchingTransactionsInDateRange >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_GetAllOrderableBillableItems] 
 */

CREATE PROCEDURE [Catalog].[SP_GetAllOrderableBillableItems]
AS
	SET NOCOUNT ON

	SELECT 
		bi.Id, bi.Name, bi.UnitPrice
	FROM 
		[Catalog].[BillableItems] bi
	WHERE 
		bi.[IsActive] = 1
go
IF OBJECT_ID('Catalog.SP_GetAllOrderableBillableItems') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_GetAllOrderableBillableItems >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_GetAllOrderableBillableItems >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_GetAllOrderableTests] 
 */

CREATE PROCEDURE [Catalog].[SP_GetAllOrderableTests]
AS
	SET NOCOUNT ON
	
	SELECT test.Id TestId
		, test.TestSKU
		, test.ShortName
		, test.CanonicalName
		, test.ListPrice
		, test.SubOrderPrice
		, test.ReqSlipPrintOption
		, test.PerformingLabId
		, plab.Name PerformingLabName
		, plab.ReqPrintName PerformingLabDisplayName
		, plab.ReqPrintCanonicalTestName
		, disc.DiscountMode
		, disc.DiscountAmount
		, disc.DiscountPercent
		, tatg.TATRank
		, tatg.DaysRequired
		, tatg.HoursRequired
		, tatg.MorningSlabBegin
		, tatg.MorningSlabEnd
		, tatg.MorningDeliveryHour
		, tatg.NoonSlabBegin
		, tatg.NoonSlabEnd
		, tatg.NoonDeliveryHour
		, tatg.EveningSlabBegin
		, tatg.EveningSlabEnd
		, tatg.EveningDeliveryHour
		, test.Mnemonics
		, plab.IsAuxProcedure
		, plab.PostOrderEntryWorkflowStage
		, plab.PostResultEntryWorkflowStage
		, rlab.IsAuxProcedure IsAux
		, rlab.PostOrderEntryWorkflowStage OEWorkflow
		, rlab.PostResultEntryWorkflowStage REWorkflow
		, plab.PostResultVerificationWorkflowStage
		, plab.PostResultFinalizationWorkflowStage
		, plab.PostReportCollationWorkflowStage
		, rlab.PostResultVerificationWorkflowStage PostRV
		, rlab.PostResultFinalizationWorkflowStage PostRF
		, rlab.PostReportCollationWorkflowStage PostRC
	FROM
		[Catalog].[LabTests] test
		INNER JOIN [Catalog].[Labs] plab
			ON (test.PerformingLabId = plab.Id)
		INNER JOIN [Catalog].[Labs] rlab
			ON (test.ResultingLabId = rlab.Id)
		LEFT OUTER JOIN [Catalog].[DiscountLevels] disc
			ON (plab.DiscountLevelId = disc.Id)
		LEFT OUTER JOIN [Catalog].[TATGroups] tatg
			ON (test.TATGroupId = tatg.Id)
	WHERE
		test.IsActive = 1
		AND plab.IsActive = 1
		AND rlab.IsActive = 1
go
IF OBJECT_ID('Catalog.SP_GetAllOrderableTests') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_GetAllOrderableTests >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_GetAllOrderableTests >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_GetAllOrderableTestsWithBillableItems] 
 */

CREATE PROCEDURE [Catalog].[SP_GetAllOrderableTestsWithBillableItems]
AS
	SET NOCOUNT ON
	
	SELECT test.[Id] AS TestId
		, bi.[Id] AS ItemId
		, bi.[Name] AS ItemName
		, lnk.[Quantity]
		, bi.[UnitPrice]
		, lnk.[OptimizationLevel]
		, test.Mnemonics
	FROM
		[Catalog].[TestBILink] lnk
		INNER JOIN [Catalog].[LabTests] test
			ON (lnk.[TestId] = test.[Id])
		INNER JOIN [Catalog].[BillableItems] bi
			ON (lnk.[BillableItemId] = bi.[Id])
	WHERE
		bi.[IsActive] = 1
go
IF OBJECT_ID('Catalog.SP_GetAllOrderableTestsWithBillableItems') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_GetAllOrderableTestsWithBillableItems >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_GetAllOrderableTestsWithBillableItems >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_GetAllOrderedBillableItemsInInvoice] 
 */

CREATE PROCEDURE [PROE].[SP_GetAllOrderedBillableItemsInInvoice]
	@invoiceId bigint
AS
	SET NOCOUNT ON
	
	SELECT obi.Id AS OrderedItemId
		, obi.BillableItemId
		, bi.Name
		, obi.UnitPrice
		, obi.Quantity
		, bi.IsActive
		, obi.DateCreated
		, obi.IsCancelled
	FROM
		PROE.OrderedBillableItems obi
		INNER JOIN Catalog.BillableItems bi
			ON obi.BillableItemId = bi.Id
	WHERE
		obi.InvoiceId = @invoiceId
go
IF OBJECT_ID('PROE.SP_GetAllOrderedBillableItemsInInvoice') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_GetAllOrderedBillableItemsInInvoice >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_GetAllOrderedBillableItemsInInvoice >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_GetAllOrderedTestDetailsInInvoice] 
 */

CREATE PROCEDURE [PROE].[SP_GetAllOrderedTestDetailsInInvoice]
	@invoiceId bigint
AS
   SET  NOCOUNT ON

   SELECT ot.Id,
          ot.LabTestId,
          lt.CanonicalName AS TestName,
          lab.Name AS LabName,
          ot.UnitPrice,
          ot.IsCancelled,
          ot.DateCreated,
          ot.LastModified,
          ot.ResultsETA,
          ot.WorkflowStage AS TestWorkflowStage,
          ot.ResultBundleId AS BundleId,
          rb.IsActive AS BundleIsActive,
          rb.TestResultType AS BundleResultType,
          rb.LastUpdated AS BundleLastModified,
          rb.DateCreated AS BundleDateCreated,
          rb.WorkflowStage AS BundleWorkflowStage
     FROM PROE.OrderedTests ot
          INNER JOIN Catalog.LabTests lt ON ot.LabTestId = lt.Id
          INNER JOIN Catalog.Labs lab ON lt.PerformingLabId = lab.Id
          LEFT OUTER JOIN TestResults.ResultBundles rb
             ON ot.ResultBundleId = rb.Id
    WHERE 
    	ot.InvoiceId = @invoiceId
go
IF OBJECT_ID('PROE.SP_GetAllOrderedTestDetailsInInvoice') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_GetAllOrderedTestDetailsInInvoice >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_GetAllOrderedTestDetailsInInvoice >>>'
go


/* 
 * PROCEDURE: [TestResults].[SP_GetAllOrderedTestDetailsInResultBundle] 
 */

CREATE PROCEDURE [TestResults].[SP_GetAllOrderedTestDetailsInResultBundle]
  @bundleId bigint
AS
	SET NOCOUNT ON 
    	
	SELECT ot.Id
     	, ot.LabTestId
     	, lt.CanonicalName AS TestName
     	, lab.Name AS LabName
     	, ot.UnitPrice
     	, ot.IsCancelled
     	, ot.DateCreated
     	, ot.LastModified
     	, ot.ResultsETA
     	, ot.WorkflowStage AS TestWorkflowStage
     	, ot.ResultBundleId AS BundleId
     	, rb.IsActive AS BundleIsActive
     	, rb.TestResultType AS BundleResultType
     	, rb.LastUpdated AS BundleLastModified
     	, rb.DateCreated AS BundleDateCreated
     	, rb.WorkflowStage AS BundleWorkflowStage
	FROM
  		PROE.OrderedTests ot
  		INNER JOIN Catalog.LabTests lt
    		ON ot.LabTestId = lt.Id
  		INNER JOIN Catalog.Labs lab
    		ON lt.PerformingLabId = lab.Id
  		LEFT OUTER JOIN TestResults.ResultBundles rb
    		ON ot.ResultBundleId = rb.Id
	WHERE
  		ot.ResultBundleId = @bundleId
go
IF OBJECT_ID('TestResults.SP_GetAllOrderedTestDetailsInResultBundle') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE TestResults.SP_GetAllOrderedTestDetailsInResultBundle >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE TestResults.SP_GetAllOrderedTestDetailsInResultBundle >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_GetAllOrderedTestsInInvoice] 
 */

CREATE PROCEDURE [PROE].[SP_GetAllOrderedTestsInInvoice]
	@invoiceId bigint
AS
	SET NOCOUNT ON
	
	SELECT
		ot.[Id] as OrderedTestId,
		test.[Id] as TestId,
		test.[ShortName],
		test.[CanonicalName],
		ot.[IsCancelled],
		ot.[WorkflowStage],
		tatg.TATRank,
		ot.[ResultsETA],
		test.[ReqSlipPrintOption],
		test.[ReportSortPriority],
		plab.[Id] AS LabId,
		plab.[ReqPrintName] AS LabName,
		plab.[ReqPrintCanonicalTestName],
		rlab.[TestResultType],
		rlab.[IsAuxProcedure],
		ot.[UnitPrice]
	FROM
		[PROE].[OrderedTests] ot
		INNER JOIN [PROE].[PatientLabOrders] ord
			ON ot.[InvoiceId] = ord.[InvoiceId]
		INNER JOIN [Catalog].[LabTests] test
			ON ot.[LabTestId] = test.[Id]
		INNER JOIN [Catalog].[Labs] plab
			ON test.[PerformingLabId] = plab.[Id]
		INNER JOIN [Catalog].[Labs] rlab
			ON test.[ResultingLabId] = rlab.[Id]
		INNER JOIN [Catalog].[TATGroups] tatg
			ON test.[TATGroupId] = tatg.[Id]
	WHERE
		ord.[InvoiceId] = @invoiceId
go
IF OBJECT_ID('PROE.SP_GetAllOrderedTestsInInvoice') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_GetAllOrderedTestsInInvoice >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_GetAllOrderedTestsInInvoice >>>'
go


/* 
 * PROCEDURE: [RBAC].[SP_GetAllPermCodesForUser] 
 */

CREATE PROCEDURE [RBAC].[SP_GetAllPermCodesForUser]
	@userId smallint
AS
	SET NOCOUNT ON
	
	SELECT p.PermCode
	FROM
		Staff.Users u
		INNER JOIN RBAC.Roles r
			ON u.RoleId = r.Id
		INNER JOIN RBAC.RolePermissions rp
			ON rp.RoleId = r.Id
		INNER JOIN RBAC.Permissions p
			ON rp.PermissionId = p.Id
	WHERE
		u.Id = @userId
go
IF OBJECT_ID('RBAC.SP_GetAllPermCodesForUser') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE RBAC.SP_GetAllPermCodesForUser >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE RBAC.SP_GetAllPermCodesForUser >>>'
go


/* 
 * PROCEDURE: [Marketing].[SP_GetAllReferrersInCategory] 
 */

CREATE PROCEDURE [Marketing].[SP_GetAllReferrersInCategory]
	@catId smallint
AS
	SET NOCOUNT ON
	
	SELECT phy.Id
		, phy.IsActive
		, phy.Prefix
		, phy.Name
		, phy.Suffix
		, phy.IdentifyingTag
		, phy.MobilePhone
		, phy.FullName
		, cat.Id AS CategoryId
		, cat.Name AS CategoryName
		, phy.ReferralClassId
		, class.Name AS ReferralClassName
		, class.ReferralEligible
	FROM
		Catalog.ReferrerCategoryLink lnk
    	RIGHT JOIN Catalog.Referrers phy
      		ON lnk.ReferrerId = phy.Id
    	LEFT JOIN Catalog.ReferrerCategories cat
      		ON lnk.CatergoryId = cat.Id
    	LEFT JOIN Marketing.ReferralClasses class
      		ON phy.ReferralClassId = class.Id
  	WHERE
		cat.Id = @catId
go
IF OBJECT_ID('Marketing.SP_GetAllReferrersInCategory') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Marketing.SP_GetAllReferrersInCategory >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Marketing.SP_GetAllReferrersInCategory >>>'
go


/* 
 * PROCEDURE: [Marketing].[SP_GetAllReferrersInReferralClass] 
 */

CREATE PROCEDURE [Marketing].[SP_GetAllReferrersInReferralClass]
	@classId smallint
AS
	SET NOCOUNT ON
	
	SELECT phy.Id
     	, phy.IsActive
     	, phy.Prefix
     	, phy.Name
     	, phy.Suffix
     	, phy.IdentifyingTag
     	, phy.MobilePhone
     	, phy.FullName
     	, NULL as CategoryId 
     	, NULL as CategoryName
     	, phy.ReferralClassId
     	, class.Name AS ReferralClassName
     	, class.ReferralEligible
	FROM
  		Catalog.Referrers phy
  		INNER JOIN Marketing.ReferralClasses class
    		ON phy.ReferralClassId = class.Id
	WHERE
		class.Id = @classId
go
IF OBJECT_ID('Marketing.SP_GetAllReferrersInReferralClass') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Marketing.SP_GetAllReferrersInReferralClass >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Marketing.SP_GetAllReferrersInReferralClass >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_GetAllReqParametersForTest] 
 */

CREATE PROCEDURE [Catalog].[SP_GetAllReqParametersForTest]
	@testId smallint
AS
	SET NOCOUNT ON
	
	SELECT Id
		, SortOrder
		, Parameter
		, IndentLevel
	FROM
		Catalog.ReqParameters
	WHERE
		LabTestId = @testId
go
IF OBJECT_ID('Catalog.SP_GetAllReqParametersForTest') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_GetAllReqParametersForTest >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_GetAllReqParametersForTest >>>'
go


/* 
 * PROCEDURE: [TestResults].[SP_GetAllResultBundlesForInvoice] 
 */

CREATE PROCEDURE [TestResults].[SP_GetAllResultBundlesForInvoice]
	@invoiceId bigint
AS
	SET NOCOUNT ON
	
	SELECT 
  		bun.[Id],
  		bun.[IsActive],
  		bun.[TestResultType],
  		bun.[DisplayTitle],
  		bun.[WorkflowStage],
  		bun.[TATRank],
  		bun.[ResultNotes]
	FROM
  		[TestResults].[ResultBundles] bun
  		INNER JOIN [PROE].[PatientLabOrders] ord ON (bun.[InvoiceId] = ord.[InvoiceId])
  		INNER JOIN [Catalog].[Labs] lab ON (bun.[LabId] = lab.[Id])
	WHERE  
  		lab.[IsActive] = 1 AND
  		lab.[IsAuxProcedure] = 0 AND
  		ord.[IsCancelled] = 0 AND
  		ord.[InvoiceId] = @invoiceId
go
IF OBJECT_ID('TestResults.SP_GetAllResultBundlesForInvoice') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE TestResults.SP_GetAllResultBundlesForInvoice >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE TestResults.SP_GetAllResultBundlesForInvoice >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_GetAllTATGroups] 
 */

CREATE PROCEDURE [Catalog].[SP_GetAllTATGroups]
AS
	SET NOCOUNT ON
	
	SELECT Id
     	, Name
     	, TATRank
     	, DaysRequired
     	, HoursRequired
     	, MorningSlabBegin
     	, MorningSlabEnd
     	, MorningDeliveryHour
     	, NoonSlabBegin
     	, NoonSlabEnd
     	, NoonDeliveryHour
     	, EveningSlabBegin
     	, EveningSlabEnd
     	, EveningDeliveryHour
	FROM
  		Catalog.TATGroups
go
IF OBJECT_ID('Catalog.SP_GetAllTATGroups') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_GetAllTATGroups >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_GetAllTATGroups >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_GetAllTemplateGroups] 
 */

CREATE PROCEDURE [Catalog].[SP_GetAllTemplateGroups]
AS
	SET NOCOUNT ON
	
	SELECT [Id]
     	, [IsActive]
     	, [Name]
     	, [LabId]
	FROM
		[Catalog].[TemplateGroups]

go
IF OBJECT_ID('Catalog.SP_GetAllTemplateGroups') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_GetAllTemplateGroups >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_GetAllTemplateGroups >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_GetAllTemplateReports] 
 */

CREATE PROCEDURE [Catalog].[SP_GetAllTemplateReports]
AS
	SET NOCOUNT ON
	
	SELECT [Id]
		, [IsActive]
		, [Name]
		, [SortPriority]
		, [Tags]
	FROM
		[Catalog].[TemplateReports]
	ORDER BY
		[Id]

go
IF OBJECT_ID('Catalog.SP_GetAllTemplateReports') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_GetAllTemplateReports >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_GetAllTemplateReports >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_GetAllTemplateReportsInGroup] 
 */

CREATE PROCEDURE [Catalog].[SP_GetAllTemplateReportsInGroup]
	@groupId smallint
AS
	SET NOCOUNT ON
	
	SELECT 
  		TemplateReports.[Id]
 		,TemplateReports.[IsActive]
 		,TemplateReports.[Name]
 		,TemplateReports.[SortPriority]
 		,TemplateReports.[Tags]
	FROM 
		[Catalog].TemplateGroupLinks
		INNER JOIN [Catalog].TemplateGroups
			ON TemplateGroupLinks.GroupId = TemplateGroups.Id
		INNER JOIN [Catalog].TemplateReports
			ON TemplateGroupLinks.TemplateId = TemplateReports.Id
	WHERE 
		TemplateGroups.Id = @groupId
go
IF OBJECT_ID('Catalog.SP_GetAllTemplateReportsInGroup') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_GetAllTemplateReportsInGroup >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_GetAllTemplateReportsInGroup >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_GetAllTestsForPerformingLab] 
 */

CREATE PROCEDURE [Catalog].[SP_GetAllTestsForPerformingLab]
	@labId smallint
AS
	SET NOCOUNT ON
	
	SELECT *
	FROM
		[Catalog].[LabTests]
	WHERE
		[PerformingLabId] = @labId
go
IF OBJECT_ID('Catalog.SP_GetAllTestsForPerformingLab') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_GetAllTestsForPerformingLab >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_GetAllTestsForPerformingLab >>>'
go


/* 
 * PROCEDURE: [TestResults].[SP_GetAllTestsInResultBundle] 
 */

CREATE PROCEDURE [TestResults].[SP_GetAllTestsInResultBundle]
	@bundleId bigint
AS
	SET NOCOUNT ON 
	
	SELECT 
  		ot.[Id] AS OrderedTestId,
  		ot.[LabTestId],
  		ot.[ResultsETA],
  		ot.[IsCancelled]
  	FROM
  		[PROE].[OrderedTests] ot
  		INNER JOIN [TestResults].[ResultBundles] bun ON (ot.[ResultBundleId] = bun.[Id])
  		INNER JOIN [Catalog].[LabTests] test ON (ot.[LabTestId] = test.[Id])
  		INNER JOIN [Catalog].[Labs] lab ON (test.[ResultingLabId] = lab.[Id])
	WHERE
  		bun.[IsActive] = 1 AND 
  		test.[IsActive] = 1 AND 
  		lab.[IsActive] = 1 AND
  		lab.[IsAuxProcedure] = 0 AND
  		bun.[Id] = @bundleId
go
IF OBJECT_ID('TestResults.SP_GetAllTestsInResultBundle') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE TestResults.SP_GetAllTestsInResultBundle >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE TestResults.SP_GetAllTestsInResultBundle >>>'
go


/* 
 * PROCEDURE: [Finances].[SP_GetAllTransactionsForDateRange] 
 */

CREATE PROCEDURE [Finances].[SP_GetAllTransactionsForDateRange]
	@startDate smalldatetime,
	@endDate smalldatetime
AS
	SET NOCOUNT ON
	
	SELECT tx.PerformingUserId
     	, usr.DisplayName AS UserName
     	, rol.Name AS RoleName
     	, tx.InvoiceId
     	, ord.OrderId
     	, ord.OrderDateTime
     	, ord.IsCancelled AS OrderCancelled
     	, tx.TxType
     	, tx.TxFlag
     	, tx.TxTime
     	, tx.TxAmount
     	, tx.UserRemarks AS TxRemarks
     	, inv.GrossPayable
     	, inv.NetPayable
     	, inv.PaidAmount
     	, inv.DueAmount
     	, COALESCE(ref.[FullName], ord.[ReferrerCustomName], '') AS [ReferrerName]
	FROM
  		PROE.PatientLabOrders ord
  		INNER JOIN Finances.InvoiceTransactions tx
    		ON ord.InvoiceId = tx.InvoiceId
  		INNER JOIN Staff.Users usr
    		ON tx.PerformingUserId = usr.Id
  		INNER JOIN RBAC.Roles rol
    		ON usr.RoleId = rol.Id
  		INNER JOIN Finances.InvoiceMaster inv
    		ON tx.InvoiceId = inv.InvoiceId
  		LEFT OUTER JOIN Catalog.Referrers ref
    		ON ord.ReferrerId = ref.Id     	
	WHERE
  		tx.TxTime BETWEEN @startDate AND @endDate
go
IF OBJECT_ID('Finances.SP_GetAllTransactionsForDateRange') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Finances.SP_GetAllTransactionsForDateRange >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Finances.SP_GetAllTransactionsForDateRange >>>'
go


/* 
 * PROCEDURE: [Finances].[SP_GetAllTransactionsForRoleDateRange] 
 */

CREATE PROCEDURE [Finances].[SP_GetAllTransactionsForRoleDateRange]
	@startDate smalldatetime
	, @endDate smalldatetime
	, @roleId smallint
AS
	SET NOCOUNT ON
		
	SELECT tx.PerformingUserId
     		, usr.DisplayName AS UserName
     		, rol.Name AS RoleName
     		, tx.InvoiceId
     		, ord.OrderId
     		, ord.OrderDateTime
     		, ord.IsCancelled AS OrderCancelled
     		, tx.TxType
     		, tx.TxFlag
     		, tx.TxTime
     		, tx.TxAmount
     		, tx.UserRemarks AS TxRemarks
     		, inv.GrossPayable
     		, inv.NetPayable
     		, inv.PaidAmount
     		, inv.DueAmount
     		, coalesce(ref.FullName, ord.ReferrerCustomName, '') AS ReferrerName
	FROM
  		PROE.PatientLabOrders ord
  		INNER JOIN Finances.InvoiceTransactions tx
    		ON ord.InvoiceId = tx.InvoiceId
  		INNER JOIN Staff.Users usr
    		ON tx.PerformingUserId = usr.Id
  		INNER JOIN RBAC.Roles rol
    		ON usr.RoleId = rol.Id
  		INNER JOIN Finances.InvoiceMaster inv
    		ON tx.InvoiceId = inv.InvoiceId
  		LEFT OUTER JOIN Catalog.Referrers ref
    		ON ord.ReferrerId = ref.Id
	WHERE
  		tx.TxTime BETWEEN @startDate AND @endDate
  		AND rol.Id = @roleId

go
IF OBJECT_ID('Finances.SP_GetAllTransactionsForRoleDateRange') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Finances.SP_GetAllTransactionsForRoleDateRange >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Finances.SP_GetAllTransactionsForRoleDateRange >>>'
go


/* 
 * PROCEDURE: [Finances].[SP_GetAllTransactionsForUserDateRange] 
 */

CREATE PROCEDURE [Finances].[SP_GetAllTransactionsForUserDateRange]
	@userId smallint, 
	@startDate smalldatetime, 
	@endDate smalldatetime
AS
	SET NOCOUNT ON
	
	SELECT tx.PerformingUserId
     	, usr.DisplayName AS UserName
     	, rol.Name AS RoleName
     	, tx.InvoiceId
     	, ord.OrderId
     	, ord.OrderDateTime
     	, ord.IsCancelled AS OrderCancelled
     	, tx.TxType
     	, tx.TxFlag
     	, tx.TxTime
     	, tx.TxAmount
     	, tx.UserRemarks AS TxRemarks
     	, inv.GrossPayable
     	, inv.NetPayable
     	, inv.PaidAmount
     	, inv.DueAmount
     	, COALESCE(ref.[FullName], ord.[ReferrerCustomName], '') AS [ReferrerName]
	FROM
  		PROE.PatientLabOrders ord
  		INNER JOIN Finances.InvoiceTransactions tx
    		ON ord.InvoiceId = tx.InvoiceId
  		INNER JOIN Staff.Users usr
    		ON tx.PerformingUserId = usr.Id
  		INNER JOIN RBAC.Roles rol
    		ON usr.RoleId = rol.Id
  		INNER JOIN Finances.InvoiceMaster inv
    		ON tx.InvoiceId = inv.InvoiceId
  		LEFT OUTER JOIN Catalog.Referrers ref
    		ON ord.ReferrerId = ref.Id     	
	WHERE
  		tx.TxTime BETWEEN @startDate AND @endDate
  		AND tx.PerformingUserId = @userId
go
IF OBJECT_ID('Finances.SP_GetAllTransactionsForUserDateRange') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Finances.SP_GetAllTransactionsForUserDateRange >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Finances.SP_GetAllTransactionsForUserDateRange >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_GetAppTemplateReportContent] 
 */

CREATE PROCEDURE [Catalog].[SP_GetAppTemplateReportContent]
	@reportCode varchar(80),
	@reportContent varbinary(MAX) OUTPUT
AS
	SET NOCOUNT ON
	
	SELECT TOP 1
		@reportContent = [ReportContent]
	FROM
		Catalog.AppReportTemplates
	WHERE
		ReportCode = @reportCode
go
IF OBJECT_ID('Catalog.SP_GetAppTemplateReportContent') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_GetAppTemplateReportContent >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_GetAppTemplateReportContent >>>'
go


/* 
 * PROCEDURE: [APP_SYS].[SP_GetAuditRecordForInvoiceByType] 
 */

CREATE PROCEDURE [APP_SYS].[SP_GetAuditRecordForInvoiceByType]
  @invoiceId  BIGINT,
  @evType     TINYINT
AS
  SET  NOCOUNT ON

  SELECT
    t.Id,
    t.PerformingUserId,
    u.DisplayName AS PerformingUserName,
    t.PatientLabOrderId,
    t.ResultBundleId,
    t.EventTime,
    t.EventCategory,
    t.EventType,
    t.WorkflowStage,
    t.UserIpAddress,
    t.Note
  FROM
    APP_SYS.AuditTrails t
    LEFT OUTER JOIN Staff.Users u ON t.PerformingUserId = u.Id
  WHERE
    t.PatientLabOrderId = @invoiceId AND
    t.EventType = @evType
  ORDER BY
    t.Id DESC

go
IF OBJECT_ID('APP_SYS.SP_GetAuditRecordForInvoiceByType') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE APP_SYS.SP_GetAuditRecordForInvoiceByType >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE APP_SYS.SP_GetAuditRecordForInvoiceByType >>>'
go


/* 
 * PROCEDURE: [APP_SYS].[SP_GetAuditRecordForOrderedTestByType] 
 */

CREATE PROCEDURE [APP_SYS].[SP_GetAuditRecordForOrderedTestByType]
  @ordTestId  BIGINT,
  @evType     SMALLINT
  WITH
  EXEC AS CALLER
AS
  SET  NOCOUNT ON

  SELECT
    t.Id,
    t.PerformingUserId,
    u.DisplayName AS PerformingUserName,
    t.PatientLabOrderId,
    t.ResultBundleId,
    t.EventTime,
    t.EventCategory,
    t.EventType,
    t.WorkflowStage,
    t.UserIpAddress,
    t.Note
  FROM
    APP_SYS.AuditTrails t
    LEFT OUTER JOIN Staff.Users u ON t.PerformingUserId = u.Id
  WHERE
    t.OrderedTestId = @ordTestId AND
    t.EventType = @evType
  ORDER BY
    t.Id DESC
go
IF OBJECT_ID('APP_SYS.SP_GetAuditRecordForOrderedTestByType') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE APP_SYS.SP_GetAuditRecordForOrderedTestByType >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE APP_SYS.SP_GetAuditRecordForOrderedTestByType >>>'
go


/* 
 * PROCEDURE: [APP_SYS].[SP_GetAuditRecordsForInvoice] 
 */

CREATE PROCEDURE [APP_SYS].[SP_GetAuditRecordsForInvoice]
	@invoiceId bigint, 
	@evCategory tinyint
AS
	SET NOCOUNT ON
	
	SELECT 
       	t.Id
       	, t.PerformingUserId
     	, u.DisplayName AS PerformingUserName
     	, t.PatientLabOrderId
     	, t.ResultBundleId
     	, t.EventTime
     	, t.EventCategory
     	, t.EventType
     	, t.WorkflowStage
     	, t.UserIpAddress
     	, t.Note     
	FROM
  		APP_SYS.AuditTrails t
  		LEFT OUTER JOIN Staff.Users u
    		ON t.PerformingUserId = u.Id
	WHERE
  		t.EventCategory = @evCategory
  		AND t.PatientLabOrderId = @invoiceId
	ORDER BY
  		t.Id DESC
go
IF OBJECT_ID('APP_SYS.SP_GetAuditRecordsForInvoice') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE APP_SYS.SP_GetAuditRecordsForInvoice >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE APP_SYS.SP_GetAuditRecordsForInvoice >>>'
go


/* 
 * PROCEDURE: [APP_SYS].[SP_GetAuditRecordsForResultBundle] 
 */

CREATE PROCEDURE [APP_SYS].[SP_GetAuditRecordsForResultBundle]
	@bundleId bigint, 
	@evCategory tinyint
AS
	SET NOCOUNT ON
	
	SELECT 
       	t.Id
       	, t.PerformingUserId
     	, u.DisplayName AS PerformingUserName
     	, t.PatientLabOrderId
     	, t.ResultBundleId
     	, t.EventTime
     	, t.EventCategory
     	, t.EventType
     	, t.WorkflowStage
     	, t.UserIpAddress
     	, t.Note     
	FROM
  		APP_SYS.AuditTrails t
  		LEFT OUTER JOIN Staff.Users u
    		ON t.PerformingUserId = u.Id
	WHERE
  		t.EventCategory = @evCategory
  		AND t.ResultBundleId = @bundleId
	ORDER BY
  		t.Id DESC
go
IF OBJECT_ID('APP_SYS.SP_GetAuditRecordsForResultBundle') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE APP_SYS.SP_GetAuditRecordsForResultBundle >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE APP_SYS.SP_GetAuditRecordsForResultBundle >>>'
go


/* 
 * PROCEDURE: [APP_SYS].[SP_GetAuditRecordsInCategoryByDateRange] 
 */

CREATE PROCEDURE [APP_SYS].[SP_GetAuditRecordsInCategoryByDateRange]
  @evCat   TINYINT,
  @dtFrom  SMALLDATETIME,
  @dtTill  SMALLDATETIME
AS
  SET  NOCOUNT ON

  SELECT
    trl.PerformingUserId AS StaffId,
    usr.DisplayName AS StaffName,
    trl.PatientLabOrderId AS InvoiceId,
    ord.OrderId,
    ord.OrderDateTime,
    trl.EventTime,
    trl.EventType,
    trl.WorkflowStage,
    rb.TestResultType,
    trl.ResultBundleId,
    rb.DisplayTitle AS ResultBundleTitle,
    trl.UserIpAddress
  FROM
    APP_SYS.AuditTrails trl
    INNER JOIN Staff.Users usr ON trl.PerformingUserId = usr.Id
    INNER JOIN TestResults.ResultBundles rb ON trl.ResultBundleId = rb.Id
    INNER JOIN PROE.PatientLabOrders ord ON rb.InvoiceId = ord.InvoiceId
  WHERE
    rb.TestResultType <> 0 AND
    ord.IsCancelled = 0 AND
    trl.EventCategory = @evCat AND
    trl.EventTime BETWEEN @dtFrom AND @dtTill
go
IF OBJECT_ID('APP_SYS.SP_GetAuditRecordsInCategoryByDateRange') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE APP_SYS.SP_GetAuditRecordsInCategoryByDateRange >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE APP_SYS.SP_GetAuditRecordsInCategoryByDateRange >>>'
go


/* 
 * PROCEDURE: [SubContract].[SP_GetAuthenticatedUserInRequestingLab] 
 */

CREATE PROCEDURE [SubContract].[SP_GetAuthenticatedUserInRequestingLab]
  @labId  INT,
  @usr    VARCHAR(20),
  @hash   CHAR(32)
AS
  SET  NOCOUNT ON

  SELECT
    TOP 1
    usr.Id,
    usr.FullName,
    usr.LastLogin
  FROM
    SubContract.RequestingLabUsers usr
    INNER JOIN SubContract.RequestingLabs lab ON usr.RequestingLabId = lab.Id
  WHERE
    lab.IsActive = 1 AND
    usr.IsActive = 1 AND
    usr.UserName = @usr AND
    usr.PassHash = @hash AND
    lab.Id = @labId

go
IF OBJECT_ID('SubContract.SP_GetAuthenticatedUserInRequestingLab') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE SubContract.SP_GetAuthenticatedUserInRequestingLab >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE SubContract.SP_GetAuthenticatedUserInRequestingLab >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_GetBatteryComponentsDetailed] 
 */

CREATE PROCEDURE [Catalog].[SP_GetBatteryComponentsDetailed]
  @batteryId INT
AS
  SET  NOCOUNT ON

  SELECT
    comp.LabTestId,
    lt.ShortName,
    lt.ListPrice
  FROM
    Catalog.BatteryComponents comp
    INNER JOIN Catalog.LabTests lt ON comp.LabTestId = lt.Id
  WHERE
    lt.IsActive = 1 AND
    comp.BatteryMasterId = @batteryId

go
IF OBJECT_ID('Catalog.SP_GetBatteryComponentsDetailed') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_GetBatteryComponentsDetailed >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_GetBatteryComponentsDetailed >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_GetCancelledOrdersInDateRange] 
 */

CREATE PROCEDURE [PROE].[SP_GetCancelledOrdersInDateRange]
  @dtFrom  SMALLDATETIME,
  @dtTo    SMALLDATETIME
AS
  SET  NOCOUNT ON

  SELECT
    ord.InvoiceId,
    ord.OrderId,
    ord.OrderDateTime,
    ord.IsCancelled,
    ord.WorkflowStage,
    inv.PaymentStatus,
    inv.GrossPayable,
    inv.TaxAmount,
    inv.DiscountAmount,
    inv.NetPayable,
    inv.PaidAmount,
    inv.DueAmount,
    ord.PhoneNumber,
    ord.Title,
    ord.FirstName,
    ord.LastName,
    ord.DoB,
    ord.Age,
    ord.Sex,
    ord.DisallowReferral,
    ord.IsReferrerUnknown,
    ord.ReferrerId,
    COALESCE(phy.FullName, ord.ReferrerCustomName, '') AS ReferrerName,
    ord.OrderingUserId,
    usr.DisplayName AS OrderingUserName,
    ord.OrderNotes
  FROM
    Finances.InvoiceMaster inv
    INNER JOIN PROE.PatientLabOrders ord ON inv.InvoiceId = ord.InvoiceId
    LEFT OUTER JOIN Catalog.Referrers phy ON ord.ReferrerId = phy.Id
    INNER JOIN Staff.Users usr ON ord.OrderingUserId = usr.Id
  WHERE
    ord.IsCancelled = 1 AND
    ord.OrderDateTime BETWEEN @dtFrom AND @dtTo
  ORDER BY
    ord.InvoiceId
go
IF OBJECT_ID('PROE.SP_GetCancelledOrdersInDateRange') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_GetCancelledOrdersInDateRange >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_GetCancelledOrdersInDateRange >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_GetDefaultTemplateContentForLabTest] 
 */

CREATE PROCEDURE [Catalog].[SP_GetDefaultTemplateContentForLabTest]
	@testId smallint
	, @content varbinary(MAX) OUTPUT
AS
	SET NOCOUNT ON
	
	SELECT TOP 1 @content = tpl.Content
	FROM
		Catalog.LabTests test
		INNER JOIN Catalog.TemplateReports tpl
		ON test.DefaultTemplateId = tpl.Id
	WHERE
		test.Id = @testId
go
IF OBJECT_ID('Catalog.SP_GetDefaultTemplateContentForLabTest') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_GetDefaultTemplateContentForLabTest >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_GetDefaultTemplateContentForLabTest >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_GetDiscountLevels] 
 */

CREATE PROCEDURE [Catalog].[SP_GetDiscountLevels]
AS
	SET NOCOUNT ON
	
	SELECT 
		[Id], 
		[Name], 
		[DiscountMode], 
		[DiscountPercent], 
		[DiscountAmount]
	FROM
		[Catalog].[DiscountLevels]
go
IF OBJECT_ID('Catalog.SP_GetDiscountLevels') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_GetDiscountLevels >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_GetDiscountLevels >>>'
go


/* 
 * PROCEDURE: [TestResults].[SP_GetDiscreteOrderedTestsInResultBundle] 
 */

CREATE PROCEDURE [TestResults].[SP_GetDiscreteOrderedTestsInResultBundle]
  @bundleId BIGINT
AS
  SET  XACT_ABORT ON

  SET  NOCOUNT ON

  SELECT
    ot.[Id],
    lt.[CanonicalName],
    lt.[ReportSortPriority],
    lt.[ReportLineGroupingTag]
  FROM
    [PROE].[OrderedTests] ot
    INNER JOIN [TestResults].[ResultBundles] rb
      ON ot.[ResultBundleId] = rb.[Id]
    INNER JOIN [Catalog].[LabTests] lt ON ot.[LabTestId] = lt.[Id]
  WHERE
    rb.[Id] = @bundleId AND
    rb.[IsActive] = 1 AND
    ot.IsCancelled = 0
  ORDER BY
    lt.[ReportSortPriority] DESC,
    lt.[ReportLineGroupingTag] ASC
go
IF OBJECT_ID('TestResults.SP_GetDiscreteOrderedTestsInResultBundle') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE TestResults.SP_GetDiscreteOrderedTestsInResultBundle >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE TestResults.SP_GetDiscreteOrderedTestsInResultBundle >>>'
go


/* 
 * PROCEDURE: [TestResults].[SP_GetDiscreteReportLineItemsForOrderedTest] 
 */

CREATE PROCEDURE [TestResults].[SP_GetDiscreteReportLineItemsForOrderedTest]
	@orderedTestId bigint
AS
	SET NOCOUNT ON 	
	
	SELECT
  		test.[CanonicalName] AS TestName,
  		item.[SortOrder],
  		item.[IsResultableItem],
  		item.[IndentLevel],
  		item.[Parameter],
  		item.[DefaultResult],
  		item.[Units],
  		item.[ReferenceRange],
  		ot.[ResultsETA],
  		ot.[WorkflowStage]
	FROM
  		[Catalog].[DiscreteReportLineItems] item
  		INNER JOIN [Catalog].[LabTests] test
    		ON item.[LabTestId] = test.[Id]
  		INNER JOIN [PROE].[OrderedTests] ot
    		ON ot.[LabTestId] = test.[Id]
	WHERE
  		ot.[Id] = @orderedTestId AND  
  		test.[IsActive] = 1 AND
  		ot.[IsCancelled] = 0
	ORDER BY
  		item.[SortOrder]
go
IF OBJECT_ID('TestResults.SP_GetDiscreteReportLineItemsForOrderedTest') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE TestResults.SP_GetDiscreteReportLineItemsForOrderedTest >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE TestResults.SP_GetDiscreteReportLineItemsForOrderedTest >>>'
go


/* 
 * PROCEDURE: [TestResults].[SP_GetDiscreteResultLineItemsForOrderedTest] 
 */

CREATE PROCEDURE [TestResults].[SP_GetDiscreteResultLineItemsForOrderedTest]
	@ordTestId bigint, 
	@resBundleId bigint
AS
	SET NOCOUNT ON 		

	SELECT
	  [Id] AS LineItemId,
	  [OrderedTestId],
	  [Parameter],
	  [Result],
	  [Units],
	  [ReferenceRange],
	  [Flag],
	  [SortOrder],
	  [IndentLevel],
	  [IsResultableItem]
	FROM
	  [TestResults].[DiscreteResultLineItems]
	WHERE
      [OrderedTestId] = @ordTestId AND
	  [ResultBundleId] = @resBundleId
  	ORDER BY
    	[SortOrder]	  
go
IF OBJECT_ID('TestResults.SP_GetDiscreteResultLineItemsForOrderedTest') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE TestResults.SP_GetDiscreteResultLineItemsForOrderedTest >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE TestResults.SP_GetDiscreteResultLineItemsForOrderedTest >>>'
go


/* 
 * PROCEDURE: [TestResults].[SP_GetDiscreteResultLineItemsForReportGeneration] 
 */

CREATE PROCEDURE [TestResults].[SP_GetDiscreteResultLineItemsForReportGeneration]
	@bundleId bigint
AS
	SET NOCOUNT ON
  	
  	-- get the list of ordered tests in the result bundle, 
  	-- sorted by priority
  	
	DECLARE @testId bigint  
	DECLARE @testName varchar(160)
	
	DECLARE cur_testid CURSOR FOR
		SELECT 
			ot.[Id], 
			lt.[CanonicalName]
		FROM
	  	[PROE].[OrderedTests] ot
	  	INNER JOIN [TestResults].[ResultBundles] rb
			ON ot.[ResultBundleId] = rb.[Id]
	  	INNER JOIN [Catalog].[LabTests] lt
			ON ot.[LabTestId] = lt.[Id]
		WHERE
	  	rb.[Id] = @bundleId
	  	AND rb.[IsActive] = 1
		ORDER BY
	  	lt.[ReportSortPriority]
	  	, lt.[ReportLineGroupingTag] ASC

	OPEN cur_testid
	
	FETCH NEXT FROM cur_testid 
		INTO @testId, @testName
	
	DECLARE @tmpDiscreteItems TABLE (  
  		Parameter varchar(max),
  		Results varchar(max),
  		Units varchar(max),
  		ReferenceRange varchar(max),
  		Flag varchar(max),
  		IndentLevel tinyint,
  		IsRootNode bit
	)

	WHILE @@FETCH_STATUS=0
	BEGIN
		INSERT INTO @tmpDiscreteItems
		VALUES
	  		(@testName, NULL, NULL, NULL, NULL, 0, 1)
	
		-- now fetch the sorted list of discrete line items
		INSERT INTO @tmpDiscreteItems
			SELECT 
				drli.[Parameter], 
				drli.[Result], 
				drli.[Units], 
				drli.[ReferenceRange], 
				drli.[Flag], 
				drli.[IndentLevel] + 1,
				0
			FROM
  				[PROE].[OrderedTests] ot
  				INNER JOIN [TestResults].[ResultBundles] rb
    				ON ot.[ResultBundleId] = rb.[Id]
  				INNER JOIN [TestResults].[DiscreteResultLineItems] drli
    				ON drli.[ResultBundleId] = rb.[Id] AND drli.[OrderedTestId] = ot.[Id]
			WHERE
  				rb.[Id] = @bundleId AND 
  				ot.[Id] = @testId
			ORDER BY
  				drli.[SortOrder]
		
		FETCH NEXT FROM cur_testid 
			INTO @testId, @testName
	END
	
	CLOSE cur_testid
	
	-- now return the curated line items
	SELECT 
		*
	FROM
  		@tmpDiscreteItems
go
IF OBJECT_ID('TestResults.SP_GetDiscreteResultLineItemsForReportGeneration') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE TestResults.SP_GetDiscreteResultLineItemsForReportGeneration >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE TestResults.SP_GetDiscreteResultLineItemsForReportGeneration >>>'
go


/* 
 * PROCEDURE: [APP_SYS].[SP_GetDistinctAuditedInvoicesInDateRange] 
 */

CREATE PROCEDURE [APP_SYS].[SP_GetDistinctAuditedInvoicesInDateRange]
  @evCat   TINYINT,
  @dtFrom  SMALLDATETIME,
  @dtTill  SMALLDATETIME
AS
  SET  NOCOUNT ON

  SELECT
    DISTINCT rb.InvoiceId
  FROM
    APP_SYS.AuditTrails trl
    INNER JOIN TestResults.ResultBundles rb ON trl.ResultBundleId = rb.Id
  WHERE
    trl.EventCategory = @evCat AND
    trl.EventTime BETWEEN @dtFrom AND @dtTill AND
    rb.TestResultType <> 0 AND
    trl.ResultBundleId IS NOT NULL AND
    rb.InvoiceId IS NOT NULL 
go
IF OBJECT_ID('APP_SYS.SP_GetDistinctAuditedInvoicesInDateRange') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE APP_SYS.SP_GetDistinctAuditedInvoicesInDateRange >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE APP_SYS.SP_GetDistinctAuditedInvoicesInDateRange >>>'
go


/* 
 * PROCEDURE: [APP_SYS].[SP_GetDistinctAuditedResultBundlesInDateRange] 
 */

CREATE PROCEDURE [APP_SYS].[SP_GetDistinctAuditedResultBundlesInDateRange]
  @evCat   TINYINT,
  @dtFrom  SMALLDATETIME,
  @dtTill  SMALLDATETIME
AS
  SET  NOCOUNT ON

  SELECT
    DISTINCT trl.ResultBundleId
  FROM
    APP_SYS.AuditTrails trl
    INNER JOIN TestResults.ResultBundles rb ON trl.ResultBundleId = rb.Id
  WHERE
    trl.EventCategory = @evCat AND
    trl.EventTime BETWEEN @dtFrom AND @dtTill AND
    rb.TestResultType <> 0 AND
    trl.ResultBundleId IS NOT NULL AND
    rb.InvoiceId IS NOT NULL
go
IF OBJECT_ID('APP_SYS.SP_GetDistinctAuditedResultBundlesInDateRange') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE APP_SYS.SP_GetDistinctAuditedResultBundlesInDateRange >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE APP_SYS.SP_GetDistinctAuditedResultBundlesInDateRange >>>'
go


/* 
 * PROCEDURE: [Marketing].[SP_GetEligibleReferrersInCategory] 
 */

CREATE PROCEDURE [Marketing].[SP_GetEligibleReferrersInCategory]
	@catId smallint
AS
	SET NOCOUNT ON
	
	SELECT ref.Id
     	, ref.FullName
     	, ref.SuppressNetReferral
	FROM
  		Catalog.ReferrerCategoryLink catlnk
  		INNER JOIN Catalog.Referrers ref
    		ON catlnk.ReferrerId = ref.Id
  		INNER JOIN Catalog.ReferrerCategories cat
    		ON catlnk.CatergoryId = cat.Id
  		INNER JOIN Marketing.ReferralClasses class
    		ON ref.ReferralClassId = class.Id
	WHERE
  		cat.IsActive = 1
  		AND ref.IsActive = 1
  		AND class.ReferralEligible = 1
  		AND cat.Id = @catId
go
IF OBJECT_ID('Marketing.SP_GetEligibleReferrersInCategory') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Marketing.SP_GetEligibleReferrersInCategory >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Marketing.SP_GetEligibleReferrersInCategory >>>'
go


/* 
 * PROCEDURE: [Finances].[SP_GetFirstInvoiceTransactionByTypeFlag] 
 */

CREATE PROCEDURE [Finances].[SP_GetFirstInvoiceTransactionByTypeFlag]
	@invoiceId bigint
	, @txType tinyint
	, @txFlag tinyint
AS
	SET NOCOUNT ON
	
	SELECT TOP 1 *
	FROM
		Finances.InvoiceTransactions
	WHERE
		InvoiceId = @invoiceId
		AND TxType = @txType
		AND TxFlag = @txFlag
go
IF OBJECT_ID('Finances.SP_GetFirstInvoiceTransactionByTypeFlag') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Finances.SP_GetFirstInvoiceTransactionByTypeFlag >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Finances.SP_GetFirstInvoiceTransactionByTypeFlag >>>'
go


/* 
 * PROCEDURE: [Finances].[SP_GetFirstOpenWorkShiftForUser] 
 */

CREATE PROCEDURE [Finances].[SP_GetFirstOpenWorkShiftForUser]
	@userId smallint
AS
	SET NOCOUNT ON
	
	SELECT TOP 1 WorkShifts.Id
	FROM
		Finances.WorkShifts
	WHERE
		WorkShifts.UserId = @userId
		AND WorkShifts.IsClosed = 0
go
IF OBJECT_ID('Finances.SP_GetFirstOpenWorkShiftForUser') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Finances.SP_GetFirstOpenWorkShiftForUser >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Finances.SP_GetFirstOpenWorkShiftForUser >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_GetGroupMembershipsForTemplateReport] 
 */

CREATE PROCEDURE [Catalog].[SP_GetGroupMembershipsForTemplateReport]
	@reportId smallint
AS
	SET NOCOUNT ON
	
	SELECT lnk.[Id] AS LinkId
		, lnk.[GroupId]
		, g.[Name] AS GroupName
		, g.[IsActive] AS GroupIsActive
	FROM
		[Catalog].[TemplateGroupLinks] lnk
		INNER JOIN [Catalog].[TemplateReports] r
			ON lnk.[TemplateId] = r.[Id]
		INNER JOIN [Catalog].[TemplateGroups] g
			ON lnk.[GroupId] = g.[Id]
	WHERE
		r.[Id] = @reportId
go
IF OBJECT_ID('Catalog.SP_GetGroupMembershipsForTemplateReport') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_GetGroupMembershipsForTemplateReport >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_GetGroupMembershipsForTemplateReport >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_GetHeldOrdersInShift] 
 */

CREATE PROCEDURE [PROE].[SP_GetHeldOrdersInShift]
	@ShiftId int
AS
	SET NOCOUNT ON 		
	SELECT
		[HeldLabOrders].[Id],
		[HeldLabOrders].[DateCreated],
		[HeldLabOrders].[FullName] AS [PatientName],
		COALESCE([Referrers].[FullName], [HeldLabOrders].[ReferrerCustomName], '') AS [ReferrerName],
		[HeldLabOrders].[Sex] AS [PatientSex],
		[HeldLabOrders].[Age] AS [PatientAge]
	FROM
		[PROE].[HeldLabOrders]
		LEFT OUTER JOIN [Catalog].[Referrers]
			ON [HeldLabOrders].[ReferrerId] = [Referrers].[Id]
	WHERE
		[HeldLabOrders].[WorkShiftId] = @ShiftId

go
IF OBJECT_ID('PROE.SP_GetHeldOrdersInShift') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_GetHeldOrdersInShift >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_GetHeldOrdersInShift >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_GetInvoiceContactDetails] 
 */

CREATE PROCEDURE [PROE].[SP_GetInvoiceContactDetails]
	@invoiceId bigint
AS
	SET NOCOUNT ON
	SELECT TOP 1 lo.InvoiceId
		, lo.OrderId
		, lo.OrderDateTime
		, lo.FullName AS PatientName
		, COALESCE(phy.FullName, lo.ReferrerCustomName, '') AS ReferrerName
		, lo.Age
		, lo.Sex
		, inv.GrossPayable
		, inv.NetPayable
		, inv.PaidAmount
		, inv.DueAmount
		, inv.DiscountAmount
		, inv.RefundAmount
		, lo.IsCancelled
		, lo.PhoneNumber
		, lo.EmailAddress
		, lo.EmailTestResults
		, lo.WebAccessToken
		, lo.IsExternalSubOrder
		, lo.SubOrderTrackingId
		, lo.WorkflowStage
	FROM
		PROE.PatientLabOrders lo
		INNER JOIN Finances.InvoiceMaster inv
		ON inv.InvoiceId = lo.InvoiceId
		LEFT OUTER JOIN Catalog.Referrers phy
		ON lo.ReferrerId = phy.Id
	WHERE
		inv.InvoiceId = @invoiceId
go
IF OBJECT_ID('PROE.SP_GetInvoiceContactDetails') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_GetInvoiceContactDetails >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_GetInvoiceContactDetails >>>'
go


/* 
 * PROCEDURE: [Finances].[SP_GetInvoiceFinancialDetails] 
 */

CREATE PROCEDURE [Finances].[SP_GetInvoiceFinancialDetails]
	@invoiceId bigint
AS
	SET NOCOUNT ON
	
	SELECT TOP 1 PaymentStatus
		, GrossPayable
		, DiscountAmount
		, TaxAmount
		, SurchargeAmount
		, NetPayable
		, PaidAmount
		, DueAmount
		, RefundAmount
	FROM
		Finances.InvoiceMaster
	WHERE
		InvoiceId = @invoiceId

go
IF OBJECT_ID('Finances.SP_GetInvoiceFinancialDetails') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Finances.SP_GetInvoiceFinancialDetails >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Finances.SP_GetInvoiceFinancialDetails >>>'
go


/* 
 * PROCEDURE: [Finances].[SP_GetInvoicesInShift] 
 */

CREATE PROCEDURE [Finances].[SP_GetInvoicesInShift]
	@ShiftId int
AS

	SET NOCOUNT ON
	
	SELECT
		lo.[InvoiceId],
		lo.[OrderId],
		lo.[OrderDateTime],
		lo.[FullName] AS [PatientName],
		COALESCE(phy.[FullName], lo.[ReferrerCustomName], '')  AS [ReferrerName],
		lo.[Age],
		lo.[Sex],
		inv.[GrossPayable],
		inv.[NetPayable],
		inv.[PaidAmount],
		inv.[DueAmount],
		inv.[DiscountAmount],
		inv.[RefundAmount]
	FROM
		PROE.PatientLabOrders lo
		INNER JOIN Finances.WorkShifts shft
			ON lo.WorkShiftId = shft.Id
		INNER JOIN Finances.InvoiceMaster inv
			ON inv.InvoiceId = lo.InvoiceId
		LEFT OUTER JOIN Catalog.Referrers phy
			ON lo.ReferrerId = phy.[Id]
	WHERE
		shft.[Id] = @ShiftId
	ORDER BY
		lo.[OrderDateTime]
		
go
IF OBJECT_ID('Finances.SP_GetInvoicesInShift') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Finances.SP_GetInvoicesInShift >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Finances.SP_GetInvoicesInShift >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_GetInvoiceSummary] 
 */

CREATE PROCEDURE [PROE].[SP_GetInvoiceSummary]
	@invoiceId bigint
AS
	SET NOCOUNT ON
	SELECT TOP 1 lo.InvoiceId
		, lo.OrderId
		, lo.OrderDateTime
		, lo.FullName AS PatientName
		, COALESCE(phy.FullName, lo.ReferrerCustomName, '') AS ReferrerName
		, lo.Age
		, lo.Sex
		, inv.GrossPayable
		, inv.NetPayable
		, inv.PaidAmount
		, inv.DueAmount
		, inv.DiscountAmount
		, inv.RefundAmount
		, lo.IsCancelled
	FROM
		PROE.PatientLabOrders lo
		INNER JOIN Finances.InvoiceMaster inv
			ON inv.InvoiceId = lo.InvoiceId
		LEFT OUTER JOIN Catalog.Referrers phy
			ON lo.ReferrerId = phy.Id
	WHERE
		inv.InvoiceId = @invoiceId

go
IF OBJECT_ID('PROE.SP_GetInvoiceSummary') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_GetInvoiceSummary >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_GetInvoiceSummary >>>'
go


/* 
 * PROCEDURE: [Finances].[SP_GetInvoiceTransactionsInDateRangeType] 
 */

CREATE PROCEDURE [Finances].[SP_GetInvoiceTransactionsInDateRangeType]
	@txFlag tinyint
	, @txType tinyint
	, @startDate smalldatetime
	, @endDate smalldatetime
AS
  	SET NOCOUNT ON

	SELECT tx.InvoiceId
		, ord.OrderId
     	, tx.TxAmount
     	, ord.OrderDateTime
     	, inv.GrossPayable
     	, inv.DiscountAmount
     	, inv.NetPayable
     	, inv.PaidAmount
     	, inv.DueAmount
	FROM
  		PROE.PatientLabOrders ord
  		INNER JOIN Finances.InvoiceTransactions tx
    		ON ord.InvoiceId = tx.InvoiceId
      	INNER JOIN Finances.InvoiceMaster inv
        	ON tx.InvoiceId = inv.InvoiceId
	WHERE
  		tx.TxFlag <> @txFlag
  		AND tx.TxTime BETWEEN @startDate AND @endDate
  		AND tx.TxType = @txType
  		AND ord.IsCancelled = 0
go
IF OBJECT_ID('Finances.SP_GetInvoiceTransactionsInDateRangeType') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Finances.SP_GetInvoiceTransactionsInDateRangeType >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Finances.SP_GetInvoiceTransactionsInDateRangeType >>>'
go


/* 
 * PROCEDURE: [Finances].[SP_GetInvoiceTransactionsInDateRangeTypeExcludeReferrerCategory] 
 */

CREATE PROCEDURE [Finances].[SP_GetInvoiceTransactionsInDateRangeTypeExcludeReferrerCategory]
	@txFlag tinyint
	, @txType tinyint
	, @startDate smalldatetime
	, @endDate smalldatetime
	, @catId smallint
AS
	SET NOCOUNT ON

	SELECT tx.InvoiceId
		, ord.OrderId
     	, tx.TxAmount
     	, ord.OrderDateTime
     	, inv.GrossPayable
     	, inv.DiscountAmount
     	, inv.NetPayable
     	, inv.PaidAmount
     	, inv.DueAmount
	FROM
  		PROE.PatientLabOrders ord
  		INNER JOIN Finances.InvoiceTransactions tx
    		ON ord.InvoiceId = tx.InvoiceId
  		LEFT OUTER JOIN Catalog.Referrers ref
    		ON ord.ReferrerId = ref.Id
  		INNER JOIN Catalog.ReferrerCategoryLink rclnk
    		ON rclnk.ReferrerId = ref.Id
  		INNER JOIN Catalog.ReferrerCategories cat
    		ON rclnk.CatergoryId = cat.Id
    	INNER JOIN Finances.InvoiceMaster inv
      	ON tx.InvoiceId = inv.InvoiceId      
  WHERE
  		tx.TxFlag <> @txFlag
  		AND tx.TxTime BETWEEN @startDate AND @endDate
  		AND tx.TxType = @txType
  		AND ord.IsCancelled = 0
  		AND ord.IsReferrerUnknown = 0
  		AND ord.ReferrerId IS NOT NULL
  		AND cat.Id <> @catId
go
IF OBJECT_ID('Finances.SP_GetInvoiceTransactionsInDateRangeTypeExcludeReferrerCategory') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Finances.SP_GetInvoiceTransactionsInDateRangeTypeExcludeReferrerCategory >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Finances.SP_GetInvoiceTransactionsInDateRangeTypeExcludeReferrerCategory >>>'
go


/* 
 * PROCEDURE: [Finances].[SP_GetInvoiceTransactionsInDateRangeTypeReferrerCategory] 
 */

CREATE PROCEDURE [Finances].[SP_GetInvoiceTransactionsInDateRangeTypeReferrerCategory]
	@txFlag tinyint
	, @txType tinyint
	, @startDate smalldatetime
	, @endDate smalldatetime
	, @catId smallint
AS
	SET NOCOUNT ON

	SELECT tx.InvoiceId
		, ord.OrderId
     	, tx.TxAmount
     	, ord.OrderDateTime
     	, inv.GrossPayable
     	, inv.DiscountAmount
     	, inv.NetPayable
     	, inv.PaidAmount
     	, inv.DueAmount      
	FROM
  		PROE.PatientLabOrders ord
  		INNER JOIN Finances.InvoiceTransactions tx
    		ON ord.InvoiceId = tx.InvoiceId
  		LEFT OUTER JOIN Catalog.Referrers ref
    		ON ord.ReferrerId = ref.Id
  		INNER JOIN Catalog.ReferrerCategoryLink rclnk
    		ON rclnk.ReferrerId = ref.Id
  		INNER JOIN Catalog.ReferrerCategories cat
    		ON rclnk.CatergoryId = cat.Id
    	INNER JOIN Finances.InvoiceMaster inv
      	ON tx.InvoiceId = inv.InvoiceId
  WHERE
  		tx.TxFlag <> @txFlag
  		AND tx.TxTime BETWEEN @startDate AND @endDate
  		AND tx.TxType = @txType
  		AND ord.IsCancelled = 0
  		AND ord.IsReferrerUnknown = 0
  		AND ord.ReferrerId IS NOT NULL
  		AND cat.Id = @catId
go
IF OBJECT_ID('Finances.SP_GetInvoiceTransactionsInDateRangeTypeReferrerCategory') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Finances.SP_GetInvoiceTransactionsInDateRangeTypeReferrerCategory >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Finances.SP_GetInvoiceTransactionsInDateRangeTypeReferrerCategory >>>'
go


/* 
 * PROCEDURE: [Finances].[SP_GetInvoiceTransactionsInWorkshiftDateRange] 
 */

CREATE PROCEDURE [Finances].[SP_GetInvoiceTransactionsInWorkshiftDateRange]
	@shiftId int
	, @invoiceId bigint
	, @beginDate datetime
	, @endDate datetime
AS
	SET NOCOUNT ON
	
	SELECT *
	FROM
  		Finances.InvoiceTransactions
	WHERE
  		WorkShiftId = @shiftId
  		AND InvoiceId = @invoiceId
  		AND TxTime BETWEEN CAST(@beginDate AS DATE) AND CAST(@endDate AS DATE)
	ORDER BY
  		Id
go
IF OBJECT_ID('Finances.SP_GetInvoiceTransactionsInWorkshiftDateRange') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Finances.SP_GetInvoiceTransactionsInWorkshiftDateRange >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Finances.SP_GetInvoiceTransactionsInWorkshiftDateRange >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_GetLabName] 
 */

CREATE PROCEDURE [Catalog].[SP_GetLabName]
	@labId smallint
AS
	SET NOCOUNT ON
	
	SELECT TOP 1 [Labs].[Name]
	FROM
  		[Catalog].[Labs]
	WHERE
  		[Labs].[Id] = @labId

go
IF OBJECT_ID('Catalog.SP_GetLabName') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_GetLabName >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_GetLabName >>>'
go


/* 
 * PROCEDURE: [Marketing].[SP_GetLabOrdersEligibleForReferral] 
 */

CREATE PROCEDURE [Marketing].[SP_GetLabOrdersEligibleForReferral]
	@dtStart datetime
	, @dtEnd datetime
AS
	SET NOCOUNT ON
	
	SELECT ord.InvoiceId
     	, ord.OrderId
     	, ord.OrderDateTime
     	, ord.FullName
     	, ord.ReferrerId
     	, inv.GrossPayable
     	, inv.DiscountAmount
     	, inv.NetPayable
     	, inv.PaidAmount
     	, inv.DueAmount
	FROM
  		Finances.InvoiceMaster inv
  		INNER JOIN PROE.PatientLabOrders ord
    		ON inv.InvoiceId = ord.InvoiceId
	WHERE
  		ord.IsCancelled = 0
  		AND ord.IsReferrerUnknown = 0
  		AND ord.ReferrerId <> NULL
  		AND ord.DisallowReferral = 0
  		AND ord.OrderDateTime BETWEEN CAST(@dtStart AS DATE) AND DATEADD(SECOND, -1, (DATEADD(DAY, 1, @dtEnd)))
go
IF OBJECT_ID('Marketing.SP_GetLabOrdersEligibleForReferral') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Marketing.SP_GetLabOrdersEligibleForReferral >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Marketing.SP_GetLabOrdersEligibleForReferral >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_GetLabReportHeaderContent] 
 */

CREATE PROCEDURE [Catalog].[SP_GetLabReportHeaderContent]
	@id int, 
	@headerContent varbinary(max) OUT
AS
	SET NOCOUNT ON
	
	SELECT TOP 1 
		@headerContent = [ReportHeader]
	FROM
		[Catalog].[LabReportHeaders]
	WHERE
		[Id] = @id

go
IF OBJECT_ID('Catalog.SP_GetLabReportHeaderContent') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_GetLabReportHeaderContent >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_GetLabReportHeaderContent >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_GetLabs] 
 */

CREATE PROCEDURE [Catalog].[SP_GetLabs]
AS
	SET NOCOUNT ON
	
	SELECT [Id] 
		, [IsActive] 
		, [Name] 
		, [LabCode] 
		, [ReqPrintName] 
		, [ReqPrintCanonicalTestName] 
		, [TestResultType] 
		, [IsAuxProcedure] 
		, [PostOrderEntryWorkflowStage] 
		, [PostResultEntryWorkflowStage] 
		, [PostResultVerificationWorkflowStage] 
		, [PostResultFinalizationWorkflowStage] 
		, [PostReportCollationWorkflowStage] 
		, [DiscountLevelId] 
		, [ReferralGroupId]
		, [ReferralGroupDisplayName]
		, [AccountingGroupDisplayName]
		, [DefaultReportHeaderId]
	FROM
		[Catalog].[Labs]
go
IF OBJECT_ID('Catalog.SP_GetLabs') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_GetLabs >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_GetLabs >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_GetLabTestCodeCount] 
 */

CREATE PROCEDURE [Catalog].[SP_GetLabTestCodeCount]
	@testCode varchar(16)
AS
	SELECT COUNT(*) AS [Count]
	FROM
		[Catalog].[LabTests]
	WHERE
		[TestSKU] = @testCode
go
IF OBJECT_ID('Catalog.SP_GetLabTestCodeCount') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_GetLabTestCodeCount >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_GetLabTestCodeCount >>>'
go


/* 
 * PROCEDURE: [Finances].[SP_GetOpenWorkShiftsForUser] 
 */

CREATE PROCEDURE [Finances].[SP_GetOpenWorkShiftsForUser]
	@userId smallint
AS
	SET NOCOUNT ON
	
	SELECT WorkShifts.Id
	FROM
		Finances.WorkShifts
	WHERE
		WorkShifts.UserId = @userId
		AND WorkShifts.IsClosed = 0
go
IF OBJECT_ID('Finances.SP_GetOpenWorkShiftsForUser') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Finances.SP_GetOpenWorkShiftsForUser >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Finances.SP_GetOpenWorkShiftsForUser >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_GetOrderableTestDetails] 
 */

CREATE PROCEDURE [Catalog].[SP_GetOrderableTestDetails]
	@testId smallint
AS
  	SET NOCOUNT ON
	
	SELECT TOP 1 
      	test.Id TestId
		, test.TestSKU
		, test.ShortName
		, test.CanonicalName
		, test.ListPrice
		, test.SubOrderPrice
		, test.ReqSlipPrintOption
		, test.PerformingLabId
		, plab.Name PerformingLabName
		, plab.ReqPrintName PerformingLabDisplayName
		, plab.ReqPrintCanonicalTestName
		, disc.DiscountMode
		, disc.DiscountAmount
		, disc.DiscountPercent
		, tatg.TATRank
		, tatg.DaysRequired
		, tatg.HoursRequired
		, tatg.MorningSlabBegin
		, tatg.MorningSlabEnd
		, tatg.MorningDeliveryHour
		, tatg.NoonSlabBegin
		, tatg.NoonSlabEnd
		, tatg.NoonDeliveryHour
		, tatg.EveningSlabBegin
		, tatg.EveningSlabEnd
		, tatg.EveningDeliveryHour
		, test.Mnemonics
		, plab.IsAuxProcedure
		, plab.PostOrderEntryWorkflowStage
		, plab.PostResultEntryWorkflowStage
		, rlab.IsAuxProcedure IsAux
		, rlab.PostOrderEntryWorkflowStage OEWorkflow
		, rlab.PostResultEntryWorkflowStage REWorkflow
		, plab.PostResultVerificationWorkflowStage
		, plab.PostResultFinalizationWorkflowStage
		, plab.PostReportCollationWorkflowStage
		, rlab.PostResultVerificationWorkflowStage PostRV
		, rlab.PostResultFinalizationWorkflowStage PostRF
		, rlab.PostReportCollationWorkflowStage PostRC
	FROM
		[Catalog].[LabTests] test
		INNER JOIN [Catalog].[Labs] plab
			ON (test.PerformingLabId = plab.Id)
		INNER JOIN [Catalog].[Labs] rlab
			ON (test.ResultingLabId = rlab.Id)
		LEFT OUTER JOIN [Catalog].[DiscountLevels] disc
			ON (plab.DiscountLevelId = disc.Id)
		LEFT OUTER JOIN [Catalog].[TATGroups] tatg
			ON (test.TATGroupId = tatg.Id)
	WHERE
		test.Id = @testId
go
IF OBJECT_ID('Catalog.SP_GetOrderableTestDetails') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_GetOrderableTestDetails >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_GetOrderableTestDetails >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_GetOrderedTestNamesByPerformingLab] 
 */

CREATE PROCEDURE [PROE].[SP_GetOrderedTestNamesByPerformingLab]
	@invoiceId bigint
	, @labId smallint
AS
	SET NOCOUNT ON
	
	SELECT
  		lt.ShortName
	FROM
  		PROE.OrderedTests ot
  		INNER JOIN [Catalog].LabTests lt ON (ot.LabTestId = lt.Id)
		WHERE
  			(ot.InvoiceId = @invoiceId) AND
  			(lt.PerformingLabId = @labId)
go
IF OBJECT_ID('PROE.SP_GetOrderedTestNamesByPerformingLab') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_GetOrderedTestNamesByPerformingLab >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_GetOrderedTestNamesByPerformingLab >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_GetPrimalInvoicesByDateRange] 
 */

CREATE PROCEDURE [PROE].[SP_GetPrimalInvoicesByDateRange]
  @beginDate  DATE,
  @endDate    DATE
AS
  SET  NOCOUNT ON

  SELECT
    ord.InvoiceId,
    ord.OrderId,
    ord.OrderDateTime,
    ord.IsCancelled,
    ord.FullName AS PatientName,
    inv.GrossPayable,
    inv.DiscountAmount,
    inv.TaxAmount,
    inv.SurchargeAmount,
    inv.NetPayable,
    inv.PaidAmount,
    inv.DueAmount,
    inv.RefundAmount,
    ord.WorkShiftId,
    ord.OrderingUserId AS StaffId,
    usr.DisplayName AS StaffName,
    ord.ReferrerId,
    ord.DisallowReferral,
    ord.IsReferrerUnknown,
    COALESCE(phy.FullName, ord.ReferrerCustomName, '') AS ReferrerName
  FROM
    Finances.InvoicePrimal inv
    INNER JOIN PROE.PatientLabOrders ord ON inv.InvoiceId = ord.InvoiceId
    INNER JOIN Staff.Users usr ON ord.OrderingUserId = usr.Id
    LEFT OUTER JOIN Catalog.Referrers phy ON ord.ReferrerId = phy.Id
  WHERE
    CONVERT(DATE, ord.OrderDateTime) BETWEEN @beginDate AND @endDate
go
IF OBJECT_ID('PROE.SP_GetPrimalInvoicesByDateRange') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_GetPrimalInvoicesByDateRange >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_GetPrimalInvoicesByDateRange >>>'
go


/* 
 * PROCEDURE: [TestResults].[SP_GetRecentlyUpdatedResultBundleDetails] 
 */

CREATE PROCEDURE [TestResults].[SP_GetRecentlyUpdatedResultBundleDetails]
	@wfStageMin tinyint,
	@wfStageMax tinyint
AS
	SET NOCOUNT ON 
	
	SELECT 
  		ord.[InvoiceId],
  		ord.[OrderId],
  		ord.[FullName] AS [PatientFullName],
  		ord.[Sex] AS [PatientSex],
  		upd.[LastUpdated],
  		upd.[WorkflowStage],
  		ord.[OrderDateTime],
  		lab.[Name] AS [LabName],
  		upd.[ResultBundleId]
	FROM
  		[TestResults].[RecentlyUpdatedResultBundles] upd
  		INNER JOIN [TestResults].[ResultBundles] rb 
  			ON (upd.[ResultBundleId] = rb.[Id])
  		INNER JOIN [PROE].[PatientLabOrders] ord 
  			ON (upd.[InvoiceId] = ord.[InvoiceId])
  		INNER JOIN [Catalog].[Labs] lab 
  			ON (rb.[LabId] = lab.[Id])
	WHERE
  		rb.[IsActive] = 1 AND 
  		ord.[IsCancelled] = 0 AND 
  		lab.[IsActive] = 1 AND
  		lab.[IsAuxProcedure] = 0 AND
  		upd.[WorkflowStage] >= @wfStageMin AND
  		upd.[WorkflowStage] < @wfStageMax
	ORDER BY
		upd.[SortPriority] DESC  		
go
IF OBJECT_ID('TestResults.SP_GetRecentlyUpdatedResultBundleDetails') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE TestResults.SP_GetRecentlyUpdatedResultBundleDetails >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE TestResults.SP_GetRecentlyUpdatedResultBundleDetails >>>'
go


/* 
 * PROCEDURE: [Marketing].[SP_GetReferralEligibleLabOrdersByDateRange] 
 */

CREATE PROCEDURE [Marketing].[SP_GetReferralEligibleLabOrdersByDateRange]
  @dtFrom  SMALLDATETIME,
  @dtTo    SMALLDATETIME
AS
  SET  NOCOUNT ON

  SELECT
    ord.InvoiceId,
    ord.OrderId,
    ord.OrderDateTime,
    ord.IsCancelled,
    ord.FullName AS PatientName,
    ord.ReferrerId,
    ref.FullName AS ReferrerName,
    inv.GrossPayable,
    inv.DiscountAmount,
    inv.NetPayable,
    inv.PaidAmount,
    inv.DueAmount
  FROM
    PROE.PatientLabOrders ord
    INNER JOIN Catalog.Referrers ref ON ord.ReferrerId = ref.Id
    INNER JOIN Finances.InvoiceMaster inv ON inv.InvoiceId = ord.InvoiceId
    INNER JOIN Marketing.ReferralClasses cls ON ref.ReferralClassId = cls.Id
  WHERE
    ref.IsActive = 1 AND
    cls.ReferralEligible = 1 AND
    ord.OrderDateTime BETWEEN @dtFrom AND @dtTo
go
IF OBJECT_ID('Marketing.SP_GetReferralEligibleLabOrdersByDateRange') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Marketing.SP_GetReferralEligibleLabOrdersByDateRange >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Marketing.SP_GetReferralEligibleLabOrdersByDateRange >>>'
go


/* 
 * PROCEDURE: [Marketing].[SP_GetReferralEligibleLabOrdersByDateRangeAndCategory] 
 */

CREATE PROCEDURE [Marketing].[SP_GetReferralEligibleLabOrdersByDateRangeAndCategory]
  @dtFrom  SMALLDATETIME,
  @dtTo    SMALLDATETIME,
  @catId   SMALLINT
AS
  SET  NOCOUNT ON

  SELECT
    ord.InvoiceId,
    ord.OrderId,
    ord.OrderDateTime,
    ord.IsCancelled,
    ord.FullName AS PatientName,
    ord.ReferrerId,
    ref.FullName AS ReferrerName,
    inv.GrossPayable,
    inv.DiscountAmount,
    inv.NetPayable,
    inv.PaidAmount,
    inv.DueAmount
  FROM
    PROE.PatientLabOrders ord
    INNER JOIN Catalog.Referrers ref ON ord.ReferrerId = ref.Id
    INNER JOIN Finances.InvoiceMaster inv ON inv.InvoiceId = ord.InvoiceId
    INNER JOIN Marketing.ReferralClasses cls ON ref.ReferralClassId = cls.Id
    INNER JOIN Catalog.ReferrerCategoryLink rclnk ON rclnk.ReferrerId = ref.Id
    INNER JOIN Catalog.ReferrerCategories refcat
      ON rclnk.CatergoryId = refcat.Id
  WHERE
    ref.IsActive = 1 AND
    cls.ReferralEligible = 1 AND
    ord.OrderDateTime BETWEEN @dtFrom AND @dtTo AND
    refcat.Id = @catId
go
IF OBJECT_ID('Marketing.SP_GetReferralEligibleLabOrdersByDateRangeAndCategory') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Marketing.SP_GetReferralEligibleLabOrdersByDateRangeAndCategory >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Marketing.SP_GetReferralEligibleLabOrdersByDateRangeAndCategory >>>'
go


/* 
 * PROCEDURE: [Marketing].[SP_GetReferralEligibleLabOrdersByDateRangeForReferrer] 
 */

CREATE PROCEDURE [Marketing].[SP_GetReferralEligibleLabOrdersByDateRangeForReferrer]
  @dtFrom  SMALLDATETIME,
  @dtTo    SMALLDATETIME,
  @refId   INT
AS
  SET  NOCOUNT ON

  SELECT
    ord.InvoiceId,
    ord.OrderId,
    ord.OrderDateTime,
    ord.IsCancelled,
    ord.FullName AS PatientName,
    ord.ReferrerId,
    ref.FullName AS ReferrerName,
    inv.GrossPayable,
    inv.DiscountAmount,
    inv.NetPayable,
    inv.PaidAmount,
    inv.DueAmount
  FROM
    PROE.PatientLabOrders ord
    INNER JOIN Catalog.Referrers ref ON ord.ReferrerId = ref.Id
    INNER JOIN Finances.InvoiceMaster inv ON inv.InvoiceId = ord.InvoiceId
    INNER JOIN Marketing.ReferralClasses cls ON ref.ReferralClassId = cls.Id
  WHERE
    ref.IsActive = 1 AND
    cls.ReferralEligible = 1 AND
    ord.OrderDateTime BETWEEN @dtFrom AND @dtTo AND
    ref.Id = @refId
go
IF OBJECT_ID('Marketing.SP_GetReferralEligibleLabOrdersByDateRangeForReferrer') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Marketing.SP_GetReferralEligibleLabOrdersByDateRangeForReferrer >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Marketing.SP_GetReferralEligibleLabOrdersByDateRangeForReferrer >>>'
go


/* 
 * PROCEDURE: [Marketing].[SP_GetReferralEligibleLabTestsCatalog] 
 */

CREATE PROCEDURE [Marketing].[SP_GetReferralEligibleLabTestsCatalog]
AS
	SET NOCOUNT ON
	
	SELECT test.Id AS TestId
     	, test.CanonicalName AS TestName
     	, lab.Id AS LabId
     	, lab.Name AS LabName
     	, lab.LabCode
     	, lab.ReferralGroupDisplayName
     	, rgrp.ReferralMode
     	, rgrp.ReferralPercent
     	, rgrp.ReferralAmount
     	, test.ListPrice
     	, test.CostBasis
	FROM
  		Catalog.LabTests test
  		INNER JOIN Catalog.Labs lab
    		ON test.PerformingLabId = lab.Id
  		INNER JOIN Marketing.ReferralGroups rgrp
    		ON lab.ReferralGroupId = rgrp.Id
	WHERE
  		lab.IsActive = 1
  		AND test.IsActive = 1
  		AND rgrp.IsActive = 1
  		AND lab.IsAuxProcedure = 0
go
IF OBJECT_ID('Marketing.SP_GetReferralEligibleLabTestsCatalog') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Marketing.SP_GetReferralEligibleLabTestsCatalog >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Marketing.SP_GetReferralEligibleLabTestsCatalog >>>'
go


/* 
 * PROCEDURE: [Marketing].[SP_GetReferralEligibleOrderedTests] 
 */

CREATE PROCEDURE [Marketing].[SP_GetReferralEligibleOrderedTests]
	@invoiceId bigint
AS
	SET NOCOUNT ON
	
	SELECT ot.LabTestId
	FROM
  		PROE.OrderedTests ot
  		INNER JOIN Catalog.LabTests lt
    		ON ot.LabTestId = lt.Id
  		INNER JOIN Catalog.Labs lab
    		ON lt.PerformingLabId = lab.Id
	WHERE
  		ot.InvoiceId = @invoiceId
  		AND ot.IsCancelled = 0
  		AND lab.IsActive = 1
  		AND lt.IsActive = 1
  		AND lab.IsAuxProcedure = 0
go
IF OBJECT_ID('Marketing.SP_GetReferralEligibleOrderedTests') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Marketing.SP_GetReferralEligibleOrderedTests >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Marketing.SP_GetReferralEligibleOrderedTests >>>'
go


/* 
 * PROCEDURE: [Marketing].[SP_GetReferralGroups] 
 */

CREATE PROCEDURE [Marketing].[SP_GetReferralGroups]
AS
	SET NOCOUNT ON
	
	SELECT 
		[Id],
		[IsActive],
		[Name],
		[ReferralMode],
		[ReferralPercent],
		[ReferralAmount]
	FROM
		[Marketing].[ReferralGroups]
go
IF OBJECT_ID('Marketing.SP_GetReferralGroups') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Marketing.SP_GetReferralGroups >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Marketing.SP_GetReferralGroups >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_GetReferrerCategories] 
 */

CREATE PROCEDURE [Catalog].[SP_GetReferrerCategories]
AS
	SET NOCOUNT ON
	
	SELECT 
		Id, 
		IsActive, 
		Name
	FROM
		Catalog.ReferrerCategories
go
IF OBJECT_ID('Catalog.SP_GetReferrerCategories') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_GetReferrerCategories >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_GetReferrerCategories >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_GetReferrerCategoriesForReferrer] 
 */

CREATE PROCEDURE [Catalog].[SP_GetReferrerCategoriesForReferrer]
	@physicianId int
AS
	SET NOCOUNT ON
	
	SELECT 
		cat.[Id], 
		cat.[IsActive], 
		cat.[Name]
	FROM
		Catalog.ReferrerCategoryLink phycatlnk
		INNER JOIN Catalog.ReferrerCategories cat
			ON phycatlnk.CatergoryId = cat.Id
		INNER JOIN Catalog.Referrers phy
			ON phycatlnk.ReferrerId = phy.Id
	WHERE
		phy.[Id] = @physicianId
go
IF OBJECT_ID('Catalog.SP_GetReferrerCategoriesForReferrer') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_GetReferrerCategoriesForReferrer >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_GetReferrerCategoriesForReferrer >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_GetReferrersInCategory] 
 */

CREATE PROCEDURE [Catalog].[SP_GetReferrersInCategory]
	@categoryId smallint
AS
	SET NOCOUNT ON
	
	SELECT
		phy.*
	FROM
		Catalog.ReferrerCategoryLink pc_lnk
		INNER JOIN Catalog.Referrers phy
			ON pc_lnk.ReferrerId = phy.Id
		INNER JOIN Catalog.ReferrerCategories cat
			ON pc_lnk.CatergoryId = cat.Id
	WHERE
		cat.[IsActive] = 1 AND
		phy.[IsActive] = 1 AND
		cat.[Id] = @categoryId
go
IF OBJECT_ID('Catalog.SP_GetReferrersInCategory') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_GetReferrersInCategory >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_GetReferrersInCategory >>>'
go


/* 
 * PROCEDURE: [TestResults].[SP_GetResultBundleDetails] 
 */

CREATE PROCEDURE [TestResults].[SP_GetResultBundleDetails]
	@bundleId bigint
AS
SET  NOCOUNT ON

SELECT TOP 1
       rb.Id,
       rb.InvoiceId,
       rb.IsActive,
       rb.DisplayTitle,
       rb.LastUpdated,
       rb.FinalizingConsultantId,
       rb.FinalizingConsultantName,
       rb.WorkflowStage,
       rb.ComponentLabTests,
       rb.DateCreated,
       rb.LabId,
       lab.[Name] AS LabName,
       rb.TestResultType,
       rb.ReportHeaderId
  FROM TestResults.ResultBundles rb
       INNER JOIN [Catalog].Labs lab ON (rb.LabId = lab.Id)
 WHERE rb.Id = @bundleId
go
IF OBJECT_ID('TestResults.SP_GetResultBundleDetails') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE TestResults.SP_GetResultBundleDetails >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE TestResults.SP_GetResultBundleDetails >>>'
go


/* 
 * PROCEDURE: [TestResults].[SP_GetResultBundleDetailsForReportGeneration] 
 */

CREATE PROCEDURE [TestResults].[SP_GetResultBundleDetailsForReportGeneration]
	@bundleId bigint
AS
	SET NOCOUNT ON 		
	
	SELECT 
		lab.[Name] AS LabName, 
		lab.[ReqPrintName] AS LabPrintName, 
		rb.[FinalizingConsultantName] AS ConsultantName, 
		cons.[SignatureImage] AS ConsultantSignatureImage,
		cons.[SignatureText] AS ConsultantSignatureText,
		hdr.[ReportHeader], 
		rb.[ResultNotes]
	FROM
		[TestResults].[ResultBundles] rb
		INNER JOIN [Catalog].[Labs] lab
			ON rb.[LabId] = lab.[Id]
		LEFT JOIN [Catalog].[LabReportHeaders] hdr
			ON hdr.[Id] = rb.[ReportHeaderId]
		INNER JOIN [Staff].[Users] usr
			ON rb.[FinalizingConsultantId] = usr.[Id]
		LEFT JOIN [Staff].[Consultants] cons
			ON cons.[UserId] = usr.[Id]
	WHERE
		rb.[IsActive] = 1 AND 
		rb.[Id] = @bundleId
go
IF OBJECT_ID('TestResults.SP_GetResultBundleDetailsForReportGeneration') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE TestResults.SP_GetResultBundleDetailsForReportGeneration >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE TestResults.SP_GetResultBundleDetailsForReportGeneration >>>'
go


/* 
 * PROCEDURE: [TestResults].[SP_GetResultBundleWorkflowUserName] 
 */

CREATE PROCEDURE [TestResults].[SP_GetResultBundleWorkflowUserName]
	@bundleId bigint
	, @wfStage tinyint
AS
	SET NOCOUNT ON
	
	SELECT TOP 1 Users.DisplayName AS UserName
	FROM
		Staff.Users
		INNER JOIN APP_SYS.AuditTrails
			ON Users.Id = AuditTrails.PerformingUserId
	WHERE
		AuditTrails.ResultBundleId = @bundleId
		AND AuditTrails.WorkflowStage = @wfStage
go
IF OBJECT_ID('TestResults.SP_GetResultBundleWorkflowUserName') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE TestResults.SP_GetResultBundleWorkflowUserName >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE TestResults.SP_GetResultBundleWorkflowUserName >>>'
go


/* 
 * PROCEDURE: [RBAC].[SP_GetRolePermissionCount] 
 */

CREATE PROCEDURE [RBAC].[SP_GetRolePermissionCount]
	@RoleId int, 
	@PermCode varchar(20)
AS
	SELECT
		COUNT(*)
	FROM
		[RBAC].[RolePermissions] role_perm
		INNER JOIN [RBAC].[Roles] role
			ON role_perm.[RoleId] = role.[Id]
			INNER JOIN [RBAC].[Permissions] perms
				ON role_perm.[PermissionId] = perms.[Id]
	WHERE
		perms.[IsActive] = 1 AND
		role.[IsActive] = 1 AND
		perms.[PermCode] = @PermCode AND
		role.[Id] = @RoleId
go
IF OBJECT_ID('RBAC.SP_GetRolePermissionCount') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE RBAC.SP_GetRolePermissionCount >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE RBAC.SP_GetRolePermissionCount >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_GetSandboxedLabsForConsultant] 
 */

CREATE PROCEDURE [Catalog].[SP_GetSandboxedLabsForConsultant]
	@userId smallint
AS
	SET NOCOUNT ON 		
	
	SELECT
		lglink.[LabId]
	FROM
		[Staff].[ConsultantSandboxedGroupsLink] uglink
		INNER JOIN [Catalog].[SandboxedLabGroups] grp
			ON uglink.[GroupId] = grp.[Id]
		INNER JOIN [Catalog].[SandboxedGroupLabsLink] lglink
			ON lglink.[GroupId] = grp.[Id]
		INNER JOIN [Catalog].[Labs] lab
			ON lglink.[LabId] = lab.[Id]
	WHERE
		uglink.[UserId] = @userId AND
		lab.[IsActive] = 1 AND
		lab.[IsAuxProcedure] = 0
go
IF OBJECT_ID('Catalog.SP_GetSandboxedLabsForConsultant') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_GetSandboxedLabsForConsultant >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_GetSandboxedLabsForConsultant >>>'
go


/* 
 * PROCEDURE: [APP_SYS].[SP_GetServerTime] 
 */

CREATE PROCEDURE [APP_SYS].[SP_GetServerTime]
AS
  SET NOCOUNT ON

  SELECT CURRENT_TIMESTAMP AS ServerTime
go
IF OBJECT_ID('APP_SYS.SP_GetServerTime') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE APP_SYS.SP_GetServerTime >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE APP_SYS.SP_GetServerTime >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_GetTemplateReportContent] 
 */

CREATE PROCEDURE [Catalog].[SP_GetTemplateReportContent]
	@reportId smallint, 
	@reportContent varbinary(max) OUT
AS
	SET NOCOUNT ON

	SELECT TOP 1
  		@reportContent = [Content]
	FROM
  		[Catalog].[TemplateReports]
	WHERE
  		[Id] = @reportId
go
IF OBJECT_ID('Catalog.SP_GetTemplateReportContent') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_GetTemplateReportContent >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_GetTemplateReportContent >>>'
go


/* 
 * PROCEDURE: [TestResults].[SP_GetTemplateResultForOrderedTest] 
 */

CREATE PROCEDURE [TestResults].[SP_GetTemplateResultForOrderedTest]
	@ordTestId bigint, 
	@resBundleId bigint
AS
	SET NOCOUNT ON 		
	
	SELECT TOP 1 
		[Id] AS ItemId,
		[Content]
	FROM
		[TestResults].[TemplateResults]
	WHERE
		[OrderedTestId] = @ordTestId AND
		[ResultBundleId] = @resBundleId
go
IF OBJECT_ID('TestResults.SP_GetTemplateResultForOrderedTest') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE TestResults.SP_GetTemplateResultForOrderedTest >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE TestResults.SP_GetTemplateResultForOrderedTest >>>'
go


/* 
 * PROCEDURE: [TestResults].[SP_GetTemplateResultForReportGeneration] 
 */

CREATE PROCEDURE [TestResults].[SP_GetTemplateResultForReportGeneration]
	@bundleId bigint
AS
	SET NOCOUNT ON
	
	SELECT TOP 1 
		lt.[CanonicalName] AS [TestName], 
		tr.[Content]
	FROM
		[TestResults].[TemplateResults] tr
		INNER JOIN [TestResults].[ResultBundles] rb
			ON tr.[ResultBundleId] = rb.[Id]
		INNER JOIN [PROE].[OrderedTests] ot
			ON tr.[OrderedTestId] = ot.[Id]
		INNER JOIN [Catalog].[LabTests] lt
			ON ot.[LabTestId] = lt.[Id]
	WHERE
		rb.[Id] = @bundleId
go
IF OBJECT_ID('TestResults.SP_GetTemplateResultForReportGeneration') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE TestResults.SP_GetTemplateResultForReportGeneration >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE TestResults.SP_GetTemplateResultForReportGeneration >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_GetTemplatesListForLabTest] 
 */

CREATE PROCEDURE [Catalog].[SP_GetTemplatesListForLabTest]
	@testId smallint
AS
	SET NOCOUNT ON
	DECLARE @rowCount int

	-- First check if the test is restricted to some specific template groups
	SELECT 
		@rowCount = COUNT(*)
	FROM
		[Catalog].[LabTests] lt
		INNER JOIN [Catalog].[TemplateGroups] grp
			ON lt.[TemplateGroupId] = grp.[Id]
		INNER JOIN [Catalog].[TemplateGroupLinks] grpreplnk
			ON grpreplnk.[GroupId] = grp.[Id]
		INNER JOIN [Catalog].[TemplateReports] rep
			ON grpreplnk.[TemplateId] = rep.[Id]
	WHERE
		grp.[IsActive] = 1 AND
		lt.[IsActive] = 1 AND
		rep.[IsActive] = 1 AND
		lt.[Id] = @testId
		
	IF @rowCount = 0		
	BEGIN
		-- Select all templates
		SELECT
				rep.[Id],
				rep.[Name],
				rep.[Tags],
				CAST (CASE WHEN lt.DefaultTemplateId = rep.Id THEN 1 ELSE 0 END AS BIT) AS [IsDefault],
				rep.[SortPriority]
		FROM
		  [Catalog].[TemplateReports] rep, [Catalog].[LabTests] lt
		WHERE
		  lt.[IsActive] = 1 AND
		  rep.[IsActive] = 1 AND
		  lt.[Id] = @testId
		ORDER BY
			rep.[SortPriority]
	END
	ELSE
	BEGIN
		-- load all active templates from the appropriate template group
		SELECT
			rep.[Id],
			rep.[Name],
			rep.[Tags],
			CAST (CASE WHEN lt.DefaultTemplateId = rep.Id THEN 1 ELSE 0 END AS BIT) AS [IsDefault],
			rep.[SortPriority]
		FROM
			[Catalog].[LabTests] lt
			INNER JOIN [Catalog].[TemplateGroups] grp
				ON lt.[TemplateGroupId] = grp.[Id]
			INNER JOIN [Catalog].[TemplateGroupLinks] grpreplnk
				ON grpreplnk.[GroupId] = grp.[Id]
			INNER JOIN [Catalog].[TemplateReports] rep
				ON grpreplnk.[TemplateId] = rep.[Id]
		WHERE
			grp.[IsActive] = 1 AND
			lt.[IsActive] = 1 AND
			rep.[IsActive] = 1 AND
			lt.[Id] = @testId
		ORDER BY
			rep.[SortPriority]
	END
go
IF OBJECT_ID('Catalog.SP_GetTemplatesListForLabTest') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_GetTemplatesListForLabTest >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_GetTemplatesListForLabTest >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_GetTestDetailsForOrderedTest] 
 */

CREATE PROCEDURE [PROE].[SP_GetTestDetailsForOrderedTest]
	@ordTestId bigint
AS
	SET NOCOUNT ON
	
	SELECT TOP 1
		test.Id AS TestId
     	, test.TestSKU
     	, test.ShortName
     	, test.CanonicalName
     	, test.ListPrice
     	, test.SubOrderPrice
     	, test.ReqSlipPrintOption
     	, test.PerformingLabId
     	, plab.Name AS PerformingLabName
     	, plab.ReqPrintName AS PerformingLabDisplayName
     	, plab.ReqPrintCanonicalTestName
     	, disc.DiscountMode
     	, disc.DiscountAmount
     	, disc.DiscountPercent
     	, tatg.TATRank
     	, tatg.DaysRequired
     	, tatg.HoursRequired
     	, tatg.MorningSlabBegin
     	, tatg.MorningSlabEnd
     	, tatg.MorningDeliveryHour
     	, tatg.NoonSlabBegin
     	, tatg.NoonSlabEnd
     	, tatg.NoonDeliveryHour
     	, tatg.EveningSlabBegin
     	, tatg.EveningSlabEnd
     	, tatg.EveningDeliveryHour
     	, test.Mnemonics
     	, plab.IsAuxProcedure
     	, plab.PostOrderEntryWorkflowStage
     	, plab.PostResultEntryWorkflowStage
     	, rlab.IsAuxProcedure AS IsAux
     	, rlab.PostOrderEntryWorkflowStage AS OEWorkflow
     	, rlab.PostResultEntryWorkflowStage AS REWorkflow
     	, plab.PostResultVerificationWorkflowStage
     	, plab.PostResultFinalizationWorkflowStage
     	, plab.PostReportCollationWorkflowStage
     	, rlab.PostResultVerificationWorkflowStage AS PostRV
     	, rlab.PostResultFinalizationWorkflowStage AS PostRF
     	, rlab.PostReportCollationWorkflowStage AS PostRC
	FROM
  		Catalog.LabTests test
  		INNER JOIN Catalog.Labs plab
    		ON test.PerformingLabId = plab.Id
  		INNER JOIN Catalog.Labs rlab
    		ON test.ResultingLabId = rlab.Id
  		LEFT OUTER JOIN Catalog.DiscountLevels disc
    		ON plab.DiscountLevelId = disc.Id
  		LEFT OUTER JOIN Catalog.TATGroups tatg
    		ON test.TATGroupId = tatg.Id
  		INNER JOIN PROE.OrderedTests ot
    		ON ot.LabTestId = test.Id
	WHERE
  		ot.Id = @ordTestId
go
IF OBJECT_ID('PROE.SP_GetTestDetailsForOrderedTest') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_GetTestDetailsForOrderedTest >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_GetTestDetailsForOrderedTest >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_GetTestResultTypeForOrderedTest] 
 */

CREATE PROCEDURE [PROE].[SP_GetTestResultTypeForOrderedTest]
  @ordTestId bigint
AS
  SET NOCOUNT ON 	
  
  SELECT TOP 1 
    Labs.TestResultType
  FROM
    PROE.OrderedTests
    INNER JOIN Catalog.LabTests
      ON OrderedTests.LabTestId = LabTests.Id
    INNER JOIN Catalog.Labs
      ON LabTests.PerformingLabId = Labs.Id
  WHERE
    OrderedTests.Id = @ordTestId
go
IF OBJECT_ID('PROE.SP_GetTestResultTypeForOrderedTest') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_GetTestResultTypeForOrderedTest >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_GetTestResultTypeForOrderedTest >>>'
go


/* 
 * PROCEDURE: [Finances].[SP_GetTransactionsInInvoice] 
 */

CREATE PROCEDURE [Finances].[SP_GetTransactionsInInvoice]
	@InvoiceId bigint
AS
	SET NOCOUNT ON 		
	SELECT inv.[InvoiceId]
		,tx.[TxTime]
		,tx.[TxType]
		,tx.[TxFlag]
		,tx.[TxAmount]
	FROM
		[Finances].[InvoiceTransactions] tx
			INNER JOIN [Finances].[InvoiceMaster] inv
				ON tx.[InvoiceId] = inv.[InvoiceId]
	WHERE
		inv.[InvoiceId] = @InvoiceId
	ORDER BY
		tx.[Id] DESC
go
IF OBJECT_ID('Finances.SP_GetTransactionsInInvoice') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Finances.SP_GetTransactionsInInvoice >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Finances.SP_GetTransactionsInInvoice >>>'
go


/* 
 * PROCEDURE: [Finances].[SP_GetTransactionsInInvoiceDetailed] 
 */

CREATE PROCEDURE [Finances].[SP_GetTransactionsInInvoiceDetailed]
	@InvoiceId bigint
AS
	SET NOCOUNT ON 		
	
	SELECT pu.[DisplayName] AS [StaffName]
 	    , au.[DisplayName] AS [AuthorizerName]
		, tx.[TxTime]
		, tx.[TxType]
		, tx.[TxFlag]
		, tx.[TxAmount]
		, tx.[UserRemarks]
	FROM
		[Finances].[InvoiceTransactions] tx
		INNER JOIN [Finances].[InvoiceMaster] inv
			ON tx.[InvoiceId] = inv.[InvoiceId]
		LEFT OUTER JOIN [Staff].[Users] pu
			ON tx.[PerformingUserId] = pu.[Id]
		LEFT OUTER JOIN [Staff].[Users] au
			ON tx.[AuthorizingUserId] = au.[Id]

	WHERE
		inv.[InvoiceId] = @InvoiceId
	ORDER BY
		tx.[Id] DESC
go
IF OBJECT_ID('Finances.SP_GetTransactionsInInvoiceDetailed') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Finances.SP_GetTransactionsInInvoiceDetailed >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Finances.SP_GetTransactionsInInvoiceDetailed >>>'
go


/* 
 * PROCEDURE: [Finances].[SP_GetTransactionsInShift] 
 */

CREATE PROCEDURE [Finances].[SP_GetTransactionsInShift]
	@ShiftId int
AS
	SET NOCOUNT ON 		
	SELECT inv.[InvoiceId]
		, tx.[TxTime]
		, tx.[TxType]
		, tx.[TxFlag]
		, tx.[TxAmount]
	FROM
		[Finances].[InvoiceTransactions] tx
		INNER JOIN 
			[Finances].[WorkShifts] shft ON (tx.[WorkShiftId] = shft.[Id])
			INNER JOIN 
				[Finances].[InvoiceMaster] inv ON (tx.[InvoiceId] = inv.[InvoiceId])
				INNER JOIN 
					[PROE].[PatientLabOrders] ord ON (inv.[InvoiceId] = ord.[InvoiceId])
	WHERE 
		shft.[Id] = @ShiftId
	ORDER BY
		tx.[Id]		
go
IF OBJECT_ID('Finances.SP_GetTransactionsInShift') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Finances.SP_GetTransactionsInShift >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Finances.SP_GetTransactionsInShift >>>'
go


/* 
 * PROCEDURE: [Finances].[SP_GetTransactionsInShiftDetailed] 
 */

CREATE PROCEDURE [Finances].[SP_GetTransactionsInShiftDetailed]
	@shiftId int
AS
	SET NOCOUNT ON
	SELECT ord.InvoiceId
     	, ord.OrderId
     	, tx.TxTime
     	, tx.TxType
     	, tx.TxFlag
     	, tx.TxAmount
     	, pu.DisplayName AS PerformingUser
     	, au.DisplayName AS AuthorizingUser
     	, tx.UserRemarks
	FROM
  		Finances.InvoiceTransactions tx
  		INNER JOIN Staff.Users pu
    		ON tx.PerformingUserId = pu.Id
  		LEFT OUTER JOIN Staff.Users au
    		ON tx.AuthorizingUserId = au.Id
  		INNER JOIN Finances.InvoiceMaster inv
    		ON tx.InvoiceId = inv.InvoiceId
  		INNER JOIN PROE.PatientLabOrders ord
    		ON inv.InvoiceId = ord.InvoiceId
	WHERE
  		tx.WorkShiftId = @shiftId
	ORDER BY
		tx.Id		

go
IF OBJECT_ID('Finances.SP_GetTransactionsInShiftDetailed') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Finances.SP_GetTransactionsInShiftDetailed >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Finances.SP_GetTransactionsInShiftDetailed >>>'
go


/* 
 * PROCEDURE: [Finances].[SP_GetTransactionsInWorkshiftDateRange] 
 */

CREATE PROCEDURE [Finances].[SP_GetTransactionsInWorkshiftDateRange]
	@shiftId int
	, @beginDate datetime
	, @endDate datetime

AS
	SET NOCOUNT ON

	SELECT *
	FROM
  		Finances.InvoiceTransactions
	WHERE
  		WorkShiftId = @shiftId
  		AND TxTime BETWEEN CAST(@beginDate AS DATE) AND CAST(@endDate AS DATE)
	ORDER BY
  		Id
go
IF OBJECT_ID('Finances.SP_GetTransactionsInWorkshiftDateRange') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Finances.SP_GetTransactionsInWorkshiftDateRange >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Finances.SP_GetTransactionsInWorkshiftDateRange >>>'
go


/* 
 * PROCEDURE: [Finances].[SP_GetTransactionsOfTypeInDateRangeWithPrimalInvoice] 
 */

CREATE PROCEDURE [Finances].[SP_GetTransactionsOfTypeInDateRangeWithPrimalInvoice]
  @dtFrom  SMALLDATETIME,
  @dtTo    SMALLDATETIME,
  @txType  TINYINT
AS
  SET  NOCOUNT ON

  SELECT
    ord.OrderId,
    tx.InvoiceId,
    ord.FullName AS PatientName,
    tx.PerformingUserId AS StaffId,
    usr.DisplayName AS StaffName,
    tx.WorkShiftId,
    tx.TxTime,
    tx.TxType,
    tx.TxFlag,
    tx.TxAmount,
    tx.UserRemarks,
    tx.UserIpAddress,
    inv.RefundAmount,
    inv.DueAmount,
    inv.PaidAmount,
    inv.NetPayable,
    inv.SurchargeAmount,
    inv.TaxAmount,
    inv.DiscountAmount,
    inv.GrossPayable,
    ord.OrderDateTime,
    ord.IsCancelled,
    ord.ReferrerId,
    coalesce(
      phy.FullName,
      ord.ReferrerCustomName,
      '')
      AS ReferrerName
  FROM
    Finances.InvoicePrimal inv
    INNER JOIN PROE.PatientLabOrders ord ON inv.InvoiceId = ord.InvoiceId
    INNER JOIN Finances.InvoiceTransactions tx ON ord.InvoiceId = tx.InvoiceId
    INNER JOIN Staff.Users usr ON tx.PerformingUserId = usr.Id
    LEFT OUTER JOIN Catalog.Referrers phy ON ord.ReferrerId = phy.Id
  WHERE
    tx.TxTime BETWEEN @dtFrom AND @dtTo AND
    tx.TxType = @txType
go
IF OBJECT_ID('Finances.SP_GetTransactionsOfTypeInDateRangeWithPrimalInvoice') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Finances.SP_GetTransactionsOfTypeInDateRangeWithPrimalInvoice >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Finances.SP_GetTransactionsOfTypeInDateRangeWithPrimalInvoice >>>'
go


/* 
 * PROCEDURE: [Finances].[SP_GetTransactionsOfTypeInDateRangeWithUpdatedInvoice] 
 */

CREATE PROCEDURE [Finances].[SP_GetTransactionsOfTypeInDateRangeWithUpdatedInvoice]
  @dtFrom  SMALLDATETIME,
  @dtTo    SMALLDATETIME,
  @txType  TINYINT
AS
  SET  NOCOUNT ON

  SELECT
    ord.OrderId,
    tx.InvoiceId,
    ord.FullName AS PatientName,
    tx.PerformingUserId AS StaffId,
    usr.DisplayName AS StaffName,
    tx.WorkShiftId,
    tx.TxTime,
    tx.TxType,
    tx.TxFlag,
    tx.TxAmount,
    tx.UserRemarks,
    tx.UserIpAddress,
    inv.RefundAmount,
    inv.DueAmount,
    inv.PaidAmount,
    inv.NetPayable,
    inv.SurchargeAmount,
    inv.TaxAmount,
    inv.DiscountAmount,
    inv.GrossPayable,
    ord.OrderDateTime,
    ord.IsCancelled,
    ord.ReferrerId,
    coalesce(
      phy.FullName,
      ord.ReferrerCustomName,
      '')
      AS ReferrerName
  FROM
    Finances.InvoiceMaster inv
    INNER JOIN PROE.PatientLabOrders ord ON inv.InvoiceId = ord.InvoiceId
    INNER JOIN Finances.InvoiceTransactions tx ON ord.InvoiceId = tx.InvoiceId
    INNER JOIN Staff.Users usr ON tx.PerformingUserId = usr.Id
    LEFT OUTER JOIN Catalog.Referrers phy ON ord.ReferrerId = phy.Id
  WHERE
    tx.TxTime BETWEEN @dtFrom AND @dtTo AND
    tx.TxType = @txType

go
IF OBJECT_ID('Finances.SP_GetTransactionsOfTypeInDateRangeWithUpdatedInvoice') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Finances.SP_GetTransactionsOfTypeInDateRangeWithUpdatedInvoice >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Finances.SP_GetTransactionsOfTypeInDateRangeWithUpdatedInvoice >>>'
go


/* 
 * PROCEDURE: [Finances].[SP_GetTransactionsSumByTypeFlag] 
 */

CREATE PROCEDURE [Finances].[SP_GetTransactionsSumByTypeFlag]
	@invoiceId bigint
	, @txType tinyint
	, @txFlag tinyint
AS
  SET NOCOUNT ON
	
SELECT SUM(TxAmount)
FROM
	Finances.InvoiceTransactions
WHERE
	InvoiceId = @invoiceId
	AND TxType = @txType
	AND TxFlag = @txFlag
go
IF OBJECT_ID('Finances.SP_GetTransactionsSumByTypeFlag') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Finances.SP_GetTransactionsSumByTypeFlag >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Finances.SP_GetTransactionsSumByTypeFlag >>>'
go


/* 
 * PROCEDURE: [Finances].[SP_GetTransactionsSumByTypeFlagEndDate] 
 */

CREATE PROCEDURE [Finances].[SP_GetTransactionsSumByTypeFlagEndDate]
	@invoiceId bigint
	, @endDate smalldatetime
	, @txType tinyint
	, @txFlag tinyint
AS
	SET NOCOUNT ON
	
	SELECT SUM(TxAmount)
	FROM
		Finances.InvoiceTransactions
	WHERE
		InvoiceId = @invoiceId
		AND TxType = @txType
		AND TxFlag = @txFlag
		AND TxTime <= @endDate
go
IF OBJECT_ID('Finances.SP_GetTransactionsSumByTypeFlagEndDate') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Finances.SP_GetTransactionsSumByTypeFlagEndDate >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Finances.SP_GetTransactionsSumByTypeFlagEndDate >>>'
go


/* 
 * PROCEDURE: [Staff].[SP_GetUserDisplayName] 
 */

CREATE PROCEDURE [Staff].[SP_GetUserDisplayName]
	@uid smallint
AS
	SET NOCOUNT ON 
	
	SELECT TOP 1 [Users].[DisplayName]
	FROM
  		[Staff].[Users]
	WHERE
  		[Users].[Id] = @uid
go
IF OBJECT_ID('Staff.SP_GetUserDisplayName') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Staff.SP_GetUserDisplayName >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Staff.SP_GetUserDisplayName >>>'
go


/* 
 * PROCEDURE: [Staff].[SP_GetUserIdForServiceBusAgent] 
 */

CREATE PROCEDURE [Staff].[SP_GetUserIdForServiceBusAgent]
AS
	SET NOCOUNT ON
	
	SELECT TOP 1 [Id]
	FROM
  		[Staff].[Users]
	WHERE
  		[IsActive] = 1
  		AND [UserName] = '$_SVC_BUS_AGENT_$'
go
IF OBJECT_ID('Staff.SP_GetUserIdForServiceBusAgent') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Staff.SP_GetUserIdForServiceBusAgent >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Staff.SP_GetUserIdForServiceBusAgent >>>'
go


/* 
 * PROCEDURE: [Staff].[SP_GetUserIdForWebServiceAgent] 
 */

CREATE PROCEDURE [Staff].[SP_GetUserIdForWebServiceAgent]
AS
	SET NOCOUNT ON
	
	SELECT TOP 1 [Id]
	FROM
  		[Staff].[Users]
	WHERE
  		[IsActive] = 1
  		AND [UserName] = '$_WEB_SVC_AGENT_$'

go
IF OBJECT_ID('Staff.SP_GetUserIdForWebServiceAgent') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Staff.SP_GetUserIdForWebServiceAgent >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Staff.SP_GetUserIdForWebServiceAgent >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_GetUserTemplatesListForLabTest] 
 */

CREATE PROCEDURE [Catalog].[SP_GetUserTemplatesListForLabTest]
	@testId smallint, 
	@userId smallint
AS
	SET NOCOUNT ON
	
	SELECT
		rep.[Id],
		rep.[Name],
		rep.[Tags],
		CAST (CASE WHEN lt.DefaultTemplateId = rep.Id THEN 1 ELSE 0 END AS BIT) AS [IsDefault],
		rep.[SortPriority]
	FROM
		[Catalog].[LabTests] lt
		INNER JOIN [Catalog].[TemplateGroups] grp
			ON lt.[TemplateGroupId] = grp.[Id]
		INNER JOIN [Catalog].[UserTemplateGroupLinks] grptmplnk
			ON grptmplnk.[TemplateGroupId] = grp.[Id]
		INNER JOIN [Staff].[Users] usr
			ON grptmplnk.[UserId] = usr.[Id]
		INNER JOIN [Catalog].[UserTemplateReports] rep
			ON rep.[TemplateGroupLinkId] = grptmplnk.[Id]
	WHERE
		grp.[IsActive] = 1 AND
		lt.[Id] = @testId AND
		usr.[Id] = @userId
	ORDER BY
		rep.[SortPriority]
go
IF OBJECT_ID('Catalog.SP_GetUserTemplatesListForLabTest') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_GetUserTemplatesListForLabTest >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_GetUserTemplatesListForLabTest >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_GetWebLoginIdCount] 
 */

CREATE PROCEDURE [Catalog].[SP_GetWebLoginIdCount]
	@loginId varchar(40)
AS
	SELECT COUNT(*) AS Count
	FROM 
		Catalog.Referrers
	WHERE 
		WebLoginId = @loginId

go
IF OBJECT_ID('Catalog.SP_GetWebLoginIdCount') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_GetWebLoginIdCount >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_GetWebLoginIdCount >>>'
go


/* 
 * PROCEDURE: [APP_SYS].[SP_InsertAppFaultData] 
 */

CREATE PROCEDURE [APP_SYS].[SP_InsertAppFaultData]
	@guid uniqueidentifier,
	@evTime datetime,
	@clrVer varchar(MAX),
	@hostApp varchar(MAX),
	@hostAppVer varchar(160),
	@idThr varchar(MAX),
	@idWin varchar(MAX),
	@machine varchar(80),
	@ip varchar(MAX),
	@exType varchar(MAX),
	@exMsg varchar(MAX),
	@exSrc varchar(MAX),
	@exDet varchar(MAX),
	@exData varchar(MAX),
	@stackFrames varchar(MAX)
AS
	SET XACT_ABORT ON
	
	BEGIN TRANSACTION
	
	INSERT INTO APP_SYS.AppFaults (
		CorrelationId,
		EventTime,
		CLRVersion,
		HostApplication,
		HostApplicationVersion,
		ThreadIdentity,
		WindowsIdentity,
		MachineName,
		IPAddresses,
		ExceptionType,
		ExceptionMessage,
		ExceptionSource,
		ExceptionDetail,
		ExceptionFormattedData,
        StackFrames)
	VALUES (
		@guid,
		@evTime,
		@clrVer,
		@hostApp,
		@hostAppVer,
		@idThr,
		@idWin,
		@machine,
		@ip,
		@exType,
		@exMsg,
		@exSrc,
		@exDet,
		@exData,
        @stackFrames)
	
	COMMIT TRANSACTION
go
IF OBJECT_ID('APP_SYS.SP_InsertAppFaultData') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE APP_SYS.SP_InsertAppFaultData >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE APP_SYS.SP_InsertAppFaultData >>>'
go


/* 
 * PROCEDURE: [APP_SYS].[SP_InsertAuditRecord] 
 */

CREATE PROCEDURE [APP_SYS].[SP_InsertAuditRecord]
	@eventCategory tinyint, 
	@eventType smallint, 
	@workflowStage tinyint, 
	@userId smallint = NULL, 
	@workShiftId int = NULL, 
	@labOrderId bigint = NULL, 
	@orderedTestId bigint = NULL, 
	@resultBundleId bigint = NULL, 
	@userIpAddress int = 0, 
	@notes varchar(max) = NULL
AS
	SET XACT_ABORT ON

	BEGIN TRANSACTION

	INSERT INTO [APP_SYS].[AuditTrails]
		([PerformingUserId], [WorkShiftId], [PatientLabOrderId],
		 [OrderedTestId], [ResultBundleId], [EventTime],
		 [EventCategory], [EventType], [WorkflowStage],
		 [UserIpAddress], [Note])
	VALUES
		(@userId, @workShiftId, @labOrderId,
		 @orderedTestId, @resultBundleId, GETDATE(),
		 @eventCategory, @eventType, @workflowStage,
		 @userIpAddress, @notes)
	 
	COMMIT TRANSACTION
go
IF OBJECT_ID('APP_SYS.SP_InsertAuditRecord') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE APP_SYS.SP_InsertAuditRecord >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE APP_SYS.SP_InsertAuditRecord >>>'
go


/* 
 * PROCEDURE: [TestResults].[SP_InsertDiscreteResultLineItem] 
 */

CREATE PROCEDURE [TestResults].[SP_InsertDiscreteResultLineItem]
	@ordTestId bigint, 
	@resBundleId bigint, 
	@param varchar(max), 
	@result varchar(max), 
	@units varchar(max), 
	@refRange varchar(max), 
	@sortOrd tinyint, 
	@indent tinyint, 
	@isResultable bit
AS
	SET XACT_ABORT ON
	
	SET NOCOUNT ON 		
	
	BEGIN TRANSACTION
	
		INSERT INTO [TestResults].[DiscreteResultLineItems]
			(
				[OrderedTestId], [ResultBundleId], [Parameter], [Units], [ReferenceRange],
				[SortOrder], [IndentLevel], [IsResultableItem], [Result]
			)
		VALUES
			(
				@ordTestId, @resBundleId, @param, @units, @refRange,
				@sortOrd, @indent, @isResultable, @result
			)    	 	
	
	COMMIT TRANSACTION

go
IF OBJECT_ID('TestResults.SP_InsertDiscreteResultLineItem') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE TestResults.SP_InsertDiscreteResultLineItem >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE TestResults.SP_InsertDiscreteResultLineItem >>>'
go


/* 
 * PROCEDURE: [APP_SYS].[SP_InsertInvoiceNotification] 
 */

CREATE PROCEDURE [APP_SYS].[SP_InsertInvoiceNotification]
	@invoice BIGINT,
	@type TINYINT,
	@code TINYINT
AS
  SET XACT_ABORT ON

  SET NOCOUNT ON 		

  BEGIN TRANSACTION

	INSERT INTO APP_SYS.InvoiceNotifications(
      	InvoiceId,
      	NotificationType,
      	NotificationCode,
      	CreatedOn)
  	VALUES (
      	@invoice,
      	@type,
      	@code,
      	GETDATE())

  COMMIT TRANSACTION
go
IF OBJECT_ID('APP_SYS.SP_InsertInvoiceNotification') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE APP_SYS.SP_InsertInvoiceNotification >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE APP_SYS.SP_InsertInvoiceNotification >>>'
go


/* 
 * PROCEDURE: [TestResults].[SP_InsertResultBundle] 
 */

CREATE PROCEDURE [TestResults].[SP_InsertResultBundle]
  @invoiceId bigint, 
  @labId smallint, 
  @tatRank tinyint, 
  @resultType tinyint, 
  @workflowStage tinyint, 
  @userId smallint, 
  @title varchar(max), 
  @componentTests varchar(max),
  @notes varbinary(max), 
  @newBundleId bigint OUT 
AS
	SET XACT_ABORT ON
	
	SET NOCOUNT ON 		
	
	BEGIN TRANSACTION
	
	INSERT INTO [TestResults].[ResultBundles]
		(
			InvoiceId, IsActive, TestResultType, DisplayTitle, LabId, DateCreated, 
        	LastUpdated, TATRank, WorkflowStage, CreatingUserId, ResultNotes, ComponentLabTests
		)
	VALUES
		(
			@invoiceId, 1, @resultType, @title, @labId, GETDATE(), GETDATE(),
			@tatRank, @workflowStage, @userId, @notes, @componentTests
		);
	
	SELECT @newBundleId = SCOPE_IDENTITY()
			
	COMMIT TRANSACTION
go
IF OBJECT_ID('TestResults.SP_InsertResultBundle') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE TestResults.SP_InsertResultBundle >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE TestResults.SP_InsertResultBundle >>>'
go


/* 
 * PROCEDURE: [TestResults].[SP_InsertTemplateResult] 
 */

CREATE PROCEDURE [TestResults].[SP_InsertTemplateResult]
	@ordTestId bigint, 
	@resBundleId bigint, 
	@lobContent varbinary(max)
AS
	SET XACT_ABORT ON
	
	SET NOCOUNT ON 		
	
	BEGIN TRANSACTION
	
		INSERT INTO [TestResults].[TemplateResults]
			([OrderedTestId], [ResultBundleId], [Content])
		VALUES
			(@ordTestId, @resBundleId, @lobContent)
	
	COMMIT TRANSACTION

go
IF OBJECT_ID('TestResults.SP_InsertTemplateResult') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE TestResults.SP_InsertTemplateResult >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE TestResults.SP_InsertTemplateResult >>>'
go


/* 
 * PROCEDURE: [APP_SYS].[SP_InvoiceHasNotification] 
 */

CREATE PROCEDURE [APP_SYS].[SP_InvoiceHasNotification]
	@invoice BIGINT,
	@type TINYINT,
	@code TINYINT
AS
  SET  NOCOUNT ON

  SELECT CASE WHEN EXISTS
           (SELECT * FROM APP_SYS.InvoiceNotifications
            WHERE
              InvoiceId = @invoice AND
              NotificationType = @type AND
              NotificationCode = @code)
    THEN
      CAST(1 AS BIT)
    ELSE
      CAST(0 AS BIT)
  END
go
IF OBJECT_ID('APP_SYS.SP_InvoiceHasNotification') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE APP_SYS.SP_InvoiceHasNotification >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE APP_SYS.SP_InvoiceHasNotification >>>'
go


/* 
 * PROCEDURE: [TestResults].[SP_OrderedTestHasResultItems] 
 */

CREATE PROCEDURE [TestResults].[SP_OrderedTestHasResultItems]
	@ordTestId bigint
AS
	DECLARE @found BIT
	SET @found = 0
	
	If (SELECT COUNT(*) FROM [TestResults].[DiscreteResultLineItems] WHERE [OrderedTestId] = @ordTestId) > 0
		SET @found = 1
	ELSE If (SELECT COUNT(*) FROM [TestResults].[TemplateResults] WHERE [OrderedTestId] = @ordTestId) > 0
		SET @found = 1
	
	SELECT @found AS RESULT
go
IF OBJECT_ID('TestResults.SP_OrderedTestHasResultItems') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE TestResults.SP_OrderedTestHasResultItems >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE TestResults.SP_OrderedTestHasResultItems >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_RemoveAllBillableItemsInInvoice] 
 */

CREATE PROCEDURE [PROE].[SP_RemoveAllBillableItemsInInvoice]
	@invoiceId bigint
AS
	SET XACT_ABORT ON
	
	SET NOCOUNT ON
	
	BEGIN TRANSACTION
	
		DELETE
		FROM
			[PROE].[OrderedBillableItems]
		WHERE
			[InvoiceId] = @invoiceId
	
	COMMIT TRANSACTION
go
IF OBJECT_ID('PROE.SP_RemoveAllBillableItemsInInvoice') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_RemoveAllBillableItemsInInvoice >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_RemoveAllBillableItemsInInvoice >>>'
go


/* 
 * PROCEDURE: [TestResults].[SP_RemoveAllResultBundlesForInvoice] 
 */

CREATE PROCEDURE [TestResults].[SP_RemoveAllResultBundlesForInvoice]
  @invoiceId BIGINT
AS
  SET  XACT_ABORT ON

  SET  NOCOUNT ON

  BEGIN TRANSACTION

  	DELETE FROM TestResults.DiscreteResultLineItems
  	WHERE       Id IN
                	(SELECT item.Id
                 	FROM   TestResults.DiscreteResultLineItems item
                        	INNER JOIN TestResults.ResultBundles bundle ON item.ResultBundleId = bundle.Id
                 	WHERE  bundle.InvoiceId = @invoiceId)
	
  	DELETE FROM TestResults.TemplateResults
  	WHERE       Id IN
                	(SELECT result.Id
                 	FROM   TestResults.TemplateResults result
                        	INNER JOIN TestResults.ResultBundles bundle ON result.ResultBundleId = bundle.Id
                 	WHERE  bundle.InvoiceId = @invoiceId)
	
  	DELETE FROM APP_SYS.AuditTrails
  	WHERE       PatientLabOrderId = @invoiceId AND ResultBundleId IN (SELECT Id
                                                                    	FROM   TestResults.ResultBundles
                                                                    	WHERE  InvoiceId = @invoiceId)
	
  	DELETE FROM TestResults.RecentlyUpdatedResultBundles
  	WHERE       InvoiceId = @invoiceId
	
  	DELETE FROM TestResults.ResultBundles
  	WHERE       InvoiceId = @invoiceId
  
  COMMIT TRANSACTION
go
IF OBJECT_ID('TestResults.SP_RemoveAllResultBundlesForInvoice') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE TestResults.SP_RemoveAllResultBundlesForInvoice >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE TestResults.SP_RemoveAllResultBundlesForInvoice >>>'
go


/* 
 * PROCEDURE: [TestResults].[SP_RemoveDiscreteItemsAndUpdateResultBundleForOrderedTest] 
 */

CREATE PROCEDURE [TestResults].[SP_RemoveDiscreteItemsAndUpdateResultBundleForOrderedTest]
  @ordTestId bigint, 
  @wfStageNew tinyint
AS
  SET XACT_ABORT ON

  SET NOCOUNT ON 		

  BEGIN TRANSACTION	
    DECLARE @bundleId BIGINT
    SET @bundleId = -1
    
    SELECT 
      @bundleId = ResultBundleId
    FROM
      PROE.OrderedTests
    WHERE
      Id = @ordTestId
    
    DELETE
    FROM
      TestResults.DiscreteResultLineItems
    WHERE
      DiscreteResultLineItems.OrderedTestId = @ordTestId

    DECLARE @itemsCount INT
    
    SELECT 
      @itemsCount = COUNT(*)
    FROM
      TestResults.DiscreteResultLineItems
      INNER JOIN PROE.OrderedTests
        ON DiscreteResultLineItems.OrderedTestId = OrderedTests.Id
    WHERE
      OrderedTests.Id = @ordTestId
      
    IF @itemsCount > 0
    BEGIN
      UPDATE 
        TestResults.ResultBundles
      SET 
        IsActive = 0,
        LastUpdated = GETDATE(),
        WorkflowStage = @wfStageNew
      WHERE
        Id = @bundleId
    END
  COMMIT TRANSACTION
go
IF OBJECT_ID('TestResults.SP_RemoveDiscreteItemsAndUpdateResultBundleForOrderedTest') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE TestResults.SP_RemoveDiscreteItemsAndUpdateResultBundleForOrderedTest >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE TestResults.SP_RemoveDiscreteItemsAndUpdateResultBundleForOrderedTest >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_RemoveHeldOrder] 
 */

CREATE PROCEDURE [PROE].[SP_RemoveHeldOrder]
  @OrderId bigint
AS
	SET XACT_ABORT ON
	
	SET NOCOUNT ON 		
	
	BEGIN TRANSACTION
	
	DELETE 
		FROM [PROE].[HeldLabOrders]
	WHERE
  		[HeldLabOrders].[Id] = @OrderId
  		
	COMMIT TRANSACTION
go
IF OBJECT_ID('PROE.SP_RemoveHeldOrder') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_RemoveHeldOrder >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_RemoveHeldOrder >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_RemoveHeldOrdersInShift] 
 */

CREATE PROCEDURE [PROE].[SP_RemoveHeldOrdersInShift]
	@ShiftId int
AS
	SET XACT_ABORT ON
	
 	SET NOCOUNT ON 		
 	
	BEGIN TRANSACTION
	
	DELETE 
		FROM [PROE].[HeldLabOrders]
	WHERE
  		[HeldLabOrders].[WorkShiftId] = @ShiftId
  		
  	COMMIT TRANSACTION
go
IF OBJECT_ID('PROE.SP_RemoveHeldOrdersInShift') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_RemoveHeldOrdersInShift >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_RemoveHeldOrdersInShift >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_RemoveReferrerCategoryLink] 
 */

CREATE PROCEDURE [Catalog].[SP_RemoveReferrerCategoryLink]
	@linkId int
	, @refId int
AS
  SET XACT_ABORT ON
	
	SET NOCOUNT ON 		
	
	BEGIN TRANSACTION	
	
		DELETE
		FROM
			Catalog.ReferrerCategoryLink
		WHERE
			Id = @linkId AND
			ReferrerId = @refId
		
	COMMIT TRANSACTION
go
IF OBJECT_ID('Catalog.SP_RemoveReferrerCategoryLink') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_RemoveReferrerCategoryLink >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_RemoveReferrerCategoryLink >>>'
go


/* 
 * PROCEDURE: [TestResults].[SP_RemoveResultBundle] 
 */

CREATE PROCEDURE [TestResults].[SP_RemoveResultBundle]
	@bundleId bigint
AS
	SET XACT_ABORT ON
	
	SET NOCOUNT ON 		
	
	BEGIN TRANSACTION	
	
		DELETE FROM 
			[TestResults].[DiscreteResultLineItems]
		WHERE 
			[ResultBundleId] = @bundleId
            
		DELETE FROM 
			[TestResults].[TemplateResults]
		WHERE 
			[ResultBundleId] = @bundleId 
            
		DELETE FROM 
			[TestResults].[RecentlyUpdatedResultBundles]
		WHERE 
			[ResultBundleId] = @bundleId                        
	
		DELETE FROM 
			[TestResults].[ResultBundles]
		WHERE 
			[Id] = @bundleId
	
    COMMIT TRANSACTION
go
IF OBJECT_ID('TestResults.SP_RemoveResultBundle') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE TestResults.SP_RemoveResultBundle >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE TestResults.SP_RemoveResultBundle >>>'
go


/* 
 * PROCEDURE: [TestResults].[SP_RemoveResultBundleFromRecentUpdatesList] 
 */

CREATE PROCEDURE [TestResults].[SP_RemoveResultBundleFromRecentUpdatesList]
	@bundleId bigint
AS
	SET XACT_ABORT ON
	
	SET NOCOUNT ON 		
	
	BEGIN TRANSACTION
	
		DELETE FROM 
			[TestResults].[RecentlyUpdatedResultBundles]
		WHERE 
			[ResultBundleId] = @bundleId
	
	COMMIT TRANSACTION
go
IF OBJECT_ID('TestResults.SP_RemoveResultBundleFromRecentUpdatesList') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE TestResults.SP_RemoveResultBundleFromRecentUpdatesList >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE TestResults.SP_RemoveResultBundleFromRecentUpdatesList >>>'
go


/* 
 * PROCEDURE: [TestResults].[SP_RemoveTemplateResultAndUpdateResultBundleForOrderedTest] 
 */


CREATE PROCEDURE [TestResults].[SP_RemoveTemplateResultAndUpdateResultBundleForOrderedTest]
  @ordTestId bigint, 
  @wfStageNew tinyint
AS
  SET XACT_ABORT ON

  SET NOCOUNT ON 		

  BEGIN TRANSACTION	

    DECLARE @bundleId BIGINT
    SET @bundleId = -1
    
    SELECT 
      @bundleId = ResultBundleId
    FROM
      PROE.OrderedTests
    WHERE
      Id = @ordTestId
    
    DELETE
    FROM
      TestResults.TemplateResults
    WHERE
      TemplateResults.OrderedTestId = @ordTestId

    UPDATE 
      TestResults.ResultBundles
    SET 
      IsActive = 0,
      LastUpdated = GETDATE(),
      WorkflowStage = @wfStageNew
    WHERE
      Id = @bundleId

  COMMIT TRANSACTION

go
IF OBJECT_ID('TestResults.SP_RemoveTemplateResultAndUpdateResultBundleForOrderedTest') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE TestResults.SP_RemoveTemplateResultAndUpdateResultBundleForOrderedTest >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE TestResults.SP_RemoveTemplateResultAndUpdateResultBundleForOrderedTest >>>'
go


/* 
 * PROCEDURE: [APP_SYS].[SP_ResetSequencesAtDayStart] 
 */

CREATE PROCEDURE [APP_SYS].[SP_ResetSequencesAtDayStart]
AS
	SET XACT_ABORT ON
	
	BEGIN TRANSACTION		
	
		DECLARE @rowCount int
		DECLARE @beginDate SMALLDATETIME
		DECLARE @endDate SMALLDATETIME

		SET @beginDate = CAST(GETDATE() AS DATE)		
		SET @endDate   = CAST(DATEADD(dd, 1, GETDATE()) AS DATE)
		SET @endDate   = DATEADD(n, -1, @endDate)

		SELECT 
			   @rowCount = COUNT(*)
		FROM  
			  [PROE].[PatientLabOrders]
		WHERE 
			  [OrderDateTime] BETWEEN @beginDate AND @endDate

		IF @rowCount = 0
		BEGIN
			 ALTER SEQUENCE [PROE].[SEQ_LabOrders] RESTART WITH 0
			 ALTER SEQUENCE [PROE].[SEQ_HL7AccessionNum] RESTART WITH 1
		END
		
	COMMIT TRANSACTION
go
IF OBJECT_ID('APP_SYS.SP_ResetSequencesAtDayStart') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE APP_SYS.SP_ResetSequencesAtDayStart >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE APP_SYS.SP_ResetSequencesAtDayStart >>>'
go


/* 
 * PROCEDURE: [RBAC].[SP_RoleRemovePermission] 
 */

CREATE PROCEDURE [RBAC].[SP_RoleRemovePermission]
	@roleId smallint, 
	@permId smallint
AS
	SET XACT_ABORT ON
	
	SET NOCOUNT ON 		
	
	BEGIN TRANSACTION	
	
    	DELETE
    	FROM
      		RBAC.RolePermissions
    	WHERE
      		RoleId = @roleId AND
      		PermissionId = @permId
  
	COMMIT TRANSACTION
go
IF OBJECT_ID('RBAC.SP_RoleRemovePermission') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE RBAC.SP_RoleRemovePermission >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE RBAC.SP_RoleRemovePermission >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_ScanLabOrderDetailsByDateRange] 
 */

CREATE PROCEDURE [PROE].[SP_ScanLabOrderDetailsByDateRange]
  @beginDate DATETIME, @endDate DATETIME
WITH EXEC AS CALLER
AS
  SET NOCOUNT ON

  SELECT
    ord.InvoiceId,
    ord.OrderId,
    ord.OrderDateTime,
    ord.IsCancelled AS InvoiceCancelled,
    ord.IsExternalSubOrder,
    ord.FullName AS PatientName,
    COALESCE(ref.FullName, ord.ReferrerCustomName, '') AS ReferrerName,
    inv.GrossPayable,
    inv.DiscountAmount,
    inv.TaxAmount,
    inv.SurchargeAmount,
    inv.NetPayable,
    inv.PaidAmount,
    inv.DueAmount,
    inv.RefundAmount,
    ord.IsReferrerUnknown,
    ord.DisallowReferral,
    ot.LabTestId AS TestId,
    lt.ShortName AS TestName,
    ot.IsCancelled AS TestCancelled,
    lab.TestResultType,
    ot.UnitPrice,
    lab.IsAuxProcedure,
    lt.PerformingLabId AS LabId,
    lab.LabCode,
    lab.Name AS LabName
  FROM PROE.OrderedTests ot
  	INNER JOIN PROE.PatientLabOrders ord
    	ON ot.InvoiceId = ord.InvoiceId
  	INNER JOIN Finances.InvoiceMaster inv
    	ON inv.InvoiceId = ord.InvoiceId
  	INNER JOIN Catalog.LabTests lt
    	ON ot.LabTestId = lt.Id
  	INNER JOIN Catalog.Labs lab
    	ON lt.PerformingLabId = lab.Id
  	LEFT OUTER JOIN Catalog.Referrers ref
    	ON ord.ReferrerId = ref.Id
  WHERE 
  	ord.OrderDateTime BETWEEN @beginDate AND @endDate
  ORDER 
  	BY inv.InvoiceId
GO
go
IF OBJECT_ID('PROE.SP_ScanLabOrderDetailsByDateRange') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_ScanLabOrderDetailsByDateRange >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_ScanLabOrderDetailsByDateRange >>>'
go


/* 
 * PROCEDURE: [TestResults].[SP_ScavengeAllOrphanedResultBundlesInInvoice] 
 */

CREATE PROCEDURE [TestResults].[SP_ScavengeAllOrphanedResultBundlesInInvoice]
	@invoiceId bigint, 
	@wfCancelStage tinyint,
	@ignoreResultType tinyint
AS
	DECLARE @bundleId BIGINT
	
	SET ROWCOUNT 0
	
	SELECT 
		[Id] INTO #tmpTable
	FROM 
		[TestResults].[ResultBundles]
	WHERE
		[InvoiceId] = @invoiceId
		AND [IsActive] = 1
	
	SET ROWCOUNT 1
	
	SELECT @bundleId = Id FROM #tmpTable
	
	WHILE @@ROWCOUNT <> 0
	BEGIN
		SET ROWCOUNT 0
		
		EXEC [TestResults].[SP_ScavengeOrphanedResultBundle] @bundleId, @wfCancelStage, @ignoreResultType
		
		DELETE #tmpTable WHERE Id = @bundleId
	
		SET ROWCOUNT 1
		
		SELECT @bundleId = Id FROM #tmpTable
	END
	
	SET ROWCOUNT 0
go
IF OBJECT_ID('TestResults.SP_ScavengeAllOrphanedResultBundlesInInvoice') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE TestResults.SP_ScavengeAllOrphanedResultBundlesInInvoice >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE TestResults.SP_ScavengeAllOrphanedResultBundlesInInvoice >>>'
go


/* 
 * PROCEDURE: [TestResults].[SP_ScavengeOrphanedResultBundle] 
 */

CREATE PROCEDURE [TestResults].[SP_ScavengeOrphanedResultBundle]
	@bundleId bigint, 
	@wfCancelStage tinyint,
	@ignoreResultType tinyint
AS
  	SET XACT_ABORT ON
	
	SET NOCOUNT ON 		
		
	IF (SELECT COUNT(*) FROM [TestResults].[TemplateResults] WHERE [ResultBundleId] = @bundleId) > 0
	BEGIN
		RETURN
	END
	
	IF (SELECT COUNT(*) FROM [TestResults].[DiscreteResultLineItems] WHERE [ResultBundleId] = @bundleId) > 0
	BEGIN
		RETURN
	END

  	BEGIN TRANSACTION
		
		UPDATE [TestResults].[ResultBundles]
		SET
			[IsActive] = 0
			, [LastUpdated] = GETDATE()
			, [WorkflowStage] = @wfCancelStage
		WHERE
			[Id] = @bundleId
      		AND [TestResultType] <> @ignoreResultType
	
	COMMIT TRANSACTION
go
IF OBJECT_ID('TestResults.SP_ScavengeOrphanedResultBundle') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE TestResults.SP_ScavengeOrphanedResultBundle >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE TestResults.SP_ScavengeOrphanedResultBundle >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_SearchActiveLabOrderByInvoiceId] 
 */

CREATE PROCEDURE [PROE].[SP_SearchActiveLabOrderByInvoiceId]
	@idInvoice bigint
AS
	SET NOCOUNT ON
	
	SELECT TOP 1 
	   	ord.InvoiceId
     	, ord.OrderId
     	, ord.OrderDateTime
     	, ord.WorkflowStage
     	, ord.IsCancelled
     	, ord.IsExternalSubOrder
     	, ord.DisallowReferral
     	, ord.IsReferrerUnknown
     	, ord.FullName AS PatientName
     	, ord.Sex
     	, ord.Age
     	, ord.DoB     	
     	, COALESCE(phy.FullName, ord.ReferrerCustomName, '') AS ReferrerName
     	, inv.GrossPayable
     	, inv.DiscountAmount
     	, inv.NetPayable
     	, inv.PaidAmount
     	, inv.DueAmount
	FROM
  		PROE.PatientLabOrders ord
  		LEFT OUTER JOIN Catalog.Referrers phy
    		ON ord.ReferrerId = phy.Id
  		INNER JOIN Finances.InvoiceMaster inv
    		ON inv.InvoiceId = ord.InvoiceId
	WHERE
		ord.IsCancelled = 0 AND
		ord.InvoiceId = @idInvoice
go
IF OBJECT_ID('PROE.SP_SearchActiveLabOrderByInvoiceId') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_SearchActiveLabOrderByInvoiceId >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_SearchActiveLabOrderByInvoiceId >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_SearchActiveLabOrdersByDateRange] 
 */

CREATE PROCEDURE [PROE].[SP_SearchActiveLabOrdersByDateRange]
	@dtFrom datetime
	, @dtTo datetime
AS
	SET NOCOUNT ON
	
	SELECT ord.InvoiceId
		, ord.OrderId
		, ord.OrderDateTime
		, ord.WorkflowStage
		, ord.IsCancelled
		, ord.IsExternalSubOrder
     	, ord.DisallowReferral
     	, ord.IsReferrerUnknown
		, ord.FullName AS PatientName
		, ord.Sex
		, ord.Age
		, ord.DoB
		, COALESCE(phy.FullName, ord.ReferrerCustomName, '') AS ReferrerName
		, inv.GrossPayable
		, inv.DiscountAmount
		, inv.NetPayable
		, inv.PaidAmount
		, inv.DueAmount
	FROM
		PROE.PatientLabOrders ord
		LEFT OUTER JOIN Catalog.Referrers phy
			ON ord.ReferrerId = phy.Id
		INNER JOIN Finances.InvoiceMaster inv
			ON inv.InvoiceId = ord.InvoiceId
	WHERE
		ord.IsCancelled = 0
		AND ord.OrderDateTime BETWEEN @dtFrom AND @dtTo
	ORDER BY   
		ord.InvoiceId
go
IF OBJECT_ID('PROE.SP_SearchActiveLabOrdersByDateRange') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_SearchActiveLabOrdersByDateRange >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_SearchActiveLabOrdersByDateRange >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_SearchActiveLabOrdersByDateRangeAndResultBundleWorkflow] 
 */

CREATE PROCEDURE [PROE].[SP_SearchActiveLabOrdersByDateRangeAndResultBundleWorkflow]
  @wfStage  TINYINT,
  @dtFrom   DATETIME,
  @dtTo     DATETIME
AS
  SET  NOCOUNT ON

  SELECT DISTINCT
    ord.InvoiceId,
    ord.OrderId,
    ord.OrderDateTime,
    ord.WorkflowStage,
    ord.IsCancelled,
    ord.IsExternalSubOrder,
    ord.DisallowReferral,
    ord.IsReferrerUnknown,
    ord.FullName AS PatientName,
    ord.Sex,
    ord.Age,
    ord.DoB,
    COALESCE(phy.FullName, ord.ReferrerCustomName, '') AS ReferrerName,
    inv.GrossPayable,
    inv.DiscountAmount,
    inv.NetPayable,
    inv.PaidAmount,
    inv.DueAmount  
  FROM
    PROE.PatientLabOrders ord
    LEFT OUTER JOIN Catalog.Referrers phy ON ord.ReferrerId = phy.Id
    INNER JOIN Finances.InvoiceMaster inv ON inv.InvoiceId = ord.InvoiceId
    INNER JOIN TestResults.ResultBundles bun ON bun.InvoiceId = ord.InvoiceId
  WHERE
    bun.IsActive = 1 AND
    ord.IsCancelled = 0 AND
    ord.OrderDateTime BETWEEN @dtFrom AND @dtTo AND
    bun.WorkflowStage = @wfStage
  ORDER BY
    ord.InvoiceId
go
IF OBJECT_ID('PROE.SP_SearchActiveLabOrdersByDateRangeAndResultBundleWorkflow') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_SearchActiveLabOrdersByDateRangeAndResultBundleWorkflow >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_SearchActiveLabOrdersByDateRangeAndResultBundleWorkflow >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_SearchActiveLabOrdersByDateRangeAndResultBundleWorkflowBetween] 
 */

CREATE PROCEDURE [PROE].[SP_SearchActiveLabOrdersByDateRangeAndResultBundleWorkflowBetween]
  @wfFrom  TINYINT,
  @wfTo    TINYINT,
  @dtFrom  DATETIME,
  @dtTo    DATETIME
AS
  SET  NOCOUNT ON

  SELECT DISTINCT
    ord.InvoiceId,
    ord.OrderId,
    ord.OrderDateTime,
    ord.WorkflowStage,
    ord.IsCancelled,
    ord.IsExternalSubOrder,
    ord.DisallowReferral,
    ord.IsReferrerUnknown,
    ord.FullName AS PatientName,
    ord.Sex,
    ord.Age,
    ord.DoB,
    COALESCE(phy.FullName, ord.ReferrerCustomName, '') AS ReferrerName,
    inv.GrossPayable,
    inv.DiscountAmount,
    inv.NetPayable,
    inv.PaidAmount,
    inv.DueAmount
  FROM
    PROE.PatientLabOrders ord
    LEFT OUTER JOIN Catalog.Referrers phy ON ord.ReferrerId = phy.Id
    INNER JOIN Finances.InvoiceMaster inv ON inv.InvoiceId = ord.InvoiceId
    INNER JOIN TestResults.ResultBundles bun ON bun.InvoiceId = ord.InvoiceId
  WHERE
    bun.IsActive = 1 AND
    ord.IsCancelled = 0 AND
    ord.OrderDateTime BETWEEN @dtFrom AND @dtTo AND
    bun.WorkflowStage BETWEEN @wfFrom AND @wfTo
  ORDER BY
    ord.InvoiceId
go
IF OBJECT_ID('PROE.SP_SearchActiveLabOrdersByDateRangeAndResultBundleWorkflowBetween') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_SearchActiveLabOrdersByDateRangeAndResultBundleWorkflowBetween >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_SearchActiveLabOrdersByDateRangeAndResultBundleWorkflowBetween >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_SearchActiveLabOrdersByPatientIdAndDateRange] 
 */

CREATE PROCEDURE [PROE].[SP_SearchActiveLabOrdersByPatientIdAndDateRange]
	@idPatient varchar(8),
	@dtFrom datetime,
	@dtTo datetime
AS
	SET NOCOUNT ON
	
	SELECT ord.InvoiceId
     	, ord.OrderId
     	, ord.OrderDateTime
     	, ord.WorkflowStage
     	, ord.IsCancelled
     	, ord.IsExternalSubOrder
     	, ord.DisallowReferral
     	, ord.IsReferrerUnknown
     	, ord.FullName AS PatientName
     	, ord.Sex
     	, ord.Age
     	, ord.DoB     	
     	, COALESCE(phy.FullName, ord.ReferrerCustomName, '') AS ReferrerName
     	, inv.GrossPayable
     	, inv.DiscountAmount
     	, inv.NetPayable
     	, inv.PaidAmount
     	, inv.DueAmount
	FROM
  		PROE.PatientLabOrders ord
  		LEFT OUTER JOIN Catalog.Referrers phy
    		ON ord.ReferrerId = phy.Id
  		INNER JOIN Finances.InvoiceMaster inv
    		ON inv.InvoiceId = ord.InvoiceId
	WHERE
		ord.IsCancelled = 0
		AND ord.OrderId = @idPatient
		AND ord.OrderDateTime BETWEEN @dtFrom AND @dtTo
	ORDER BY   
		ord.InvoiceId
go
IF OBJECT_ID('PROE.SP_SearchActiveLabOrdersByPatientIdAndDateRange') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_SearchActiveLabOrdersByPatientIdAndDateRange >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_SearchActiveLabOrdersByPatientIdAndDateRange >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_SearchActiveLabOrdersByWorkflowAndDateRange] 
 */

CREATE PROCEDURE [PROE].[SP_SearchActiveLabOrdersByWorkflowAndDateRange]
	@wfStage tinyint, 
	@dtFrom datetime,
	@dtTo datetime
AS
	SET NOCOUNT ON
	
	SELECT ord.InvoiceId
     	, ord.OrderId
     	, ord.OrderDateTime
     	, ord.WorkflowStage
     	, ord.IsCancelled
     	, ord.IsExternalSubOrder
     	, ord.DisallowReferral
     	, ord.IsReferrerUnknown
     	, ord.FullName AS PatientName
     	, ord.Sex
     	, ord.Age
     	, ord.DoB     	
     	, COALESCE(phy.FullName, ord.ReferrerCustomName, '') AS ReferrerName
     	, inv.GrossPayable
     	, inv.DiscountAmount
     	, inv.NetPayable
     	, inv.PaidAmount
     	, inv.DueAmount
	FROM
  		PROE.PatientLabOrders ord
  		LEFT OUTER JOIN Catalog.Referrers phy
    		ON ord.ReferrerId = phy.Id
  		INNER JOIN Finances.InvoiceMaster inv
    		ON inv.InvoiceId = ord.InvoiceId
	WHERE
		ord.IsCancelled = 0
		AND ord.WorkflowStage = @wfStage
		AND ord.OrderDateTime BETWEEN @dtFrom AND @dtTo
	ORDER BY   
		ord.InvoiceId
go
IF OBJECT_ID('PROE.SP_SearchActiveLabOrdersByWorkflowAndDateRange') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_SearchActiveLabOrdersByWorkflowAndDateRange >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_SearchActiveLabOrdersByWorkflowAndDateRange >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_SearchLabOrderByInvoiceId] 
 */

CREATE PROCEDURE [PROE].[SP_SearchLabOrderByInvoiceId]
	@invoiceId bigint
AS
	SET NOCOUNT ON
	
	SELECT TOP 1
		ord.InvoiceId
		, ord.OrderId
		, ord.OrderDateTime
		, ord.WorkflowStage
		, ord.IsCancelled
		, ord.IsExternalSubOrder
     	, ord.DisallowReferral
     	, ord.IsReferrerUnknown
		, ord.FullName AS PatientName
     	, ord.Sex
     	, ord.Age
     	, ord.DoB		
		, COALESCE(phy.FullName, ord.ReferrerCustomName, '') AS ReferrerName
		, inv.GrossPayable
		, inv.DiscountAmount
		, inv.NetPayable
		, inv.PaidAmount
		, inv.DueAmount
	FROM
		PROE.PatientLabOrders ord
		LEFT OUTER JOIN Catalog.Referrers phy
			ON ord.ReferrerId = phy.Id
		INNER JOIN Finances.InvoiceMaster inv
			ON inv.InvoiceId = ord.InvoiceId
	WHERE
		ord.InvoiceId = @invoiceId
go
IF OBJECT_ID('PROE.SP_SearchLabOrderByInvoiceId') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_SearchLabOrderByInvoiceId >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_SearchLabOrderByInvoiceId >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_SearchLabOrdersByDateRange] 
 */

CREATE PROCEDURE [PROE].[SP_SearchLabOrdersByDateRange]
	@dtFrom datetime
	, @dtTo datetime
AS
	SET NOCOUNT ON
		
	SELECT ord.InvoiceId
     	, ord.OrderId
     	, ord.OrderDateTime
     	, ord.WorkflowStage
     	, ord.IsCancelled
     	, ord.IsExternalSubOrder
     	, ord.DisallowReferral
     	, ord.IsReferrerUnknown
     	, ord.FullName AS PatientName
     	, ord.Sex
     	, ord.Age
     	, ord.DoB
     	, COALESCE(phy.FullName, ord.ReferrerCustomName, '') AS ReferrerName
     	, inv.GrossPayable
     	, inv.DiscountAmount
     	, inv.NetPayable
     	, inv.PaidAmount
     	, inv.DueAmount
	FROM
  		PROE.PatientLabOrders ord
  		LEFT OUTER JOIN Catalog.Referrers phy
    		ON ord.ReferrerId = phy.Id
  		INNER JOIN Finances.InvoiceMaster inv
    		ON inv.InvoiceId = ord.InvoiceId
	WHERE
		ord.OrderDateTime BETWEEN @dtFrom AND @dtTo
	ORDER BY   
		ord.InvoiceId
go
IF OBJECT_ID('PROE.SP_SearchLabOrdersByDateRange') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_SearchLabOrdersByDateRange >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_SearchLabOrdersByDateRange >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_SearchLabOrdersByPatientIdAndDateRange] 
 */

CREATE PROCEDURE [PROE].[SP_SearchLabOrdersByPatientIdAndDateRange]
	@idPatient varchar(8)
	, @dtFrom datetime
	, @dtTo datetime
AS
	SET NOCOUNT ON
	
	SELECT ord.InvoiceId
		, ord.OrderId
		, ord.OrderDateTime
		, ord.WorkflowStage
		, ord.IsCancelled
		, ord.IsExternalSubOrder
     	, ord.DisallowReferral
     	, ord.IsReferrerUnknown
		, ord.FullName AS PatientName
     	, ord.Sex
     	, ord.Age
     	, ord.DoB		
		, COALESCE(phy.FullName, ord.ReferrerCustomName, '') AS ReferrerName
		, inv.GrossPayable
		, inv.DiscountAmount
		, inv.NetPayable
		, inv.PaidAmount
		, inv.DueAmount
	FROM
		PROE.PatientLabOrders ord
		LEFT OUTER JOIN Catalog.Referrers phy
			ON ord.ReferrerId = phy.Id
		INNER JOIN Finances.InvoiceMaster inv
			ON inv.InvoiceId = ord.InvoiceId
	WHERE
		ord.OrderId = @idPatient
		AND ord.OrderDateTime BETWEEN @dtFrom AND @dtTo
	ORDER BY
		ord.InvoiceId
go
IF OBJECT_ID('PROE.SP_SearchLabOrdersByPatientIdAndDateRange') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_SearchLabOrdersByPatientIdAndDateRange >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_SearchLabOrdersByPatientIdAndDateRange >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_SearchLabOrdersByPatientNameAndDateRange] 
 */

CREATE PROCEDURE [PROE].[SP_SearchLabOrdersByPatientNameAndDateRange]
	@searchString varchar(160)
	, @dtFrom datetime
	, @dtTo datetime
AS
	SET NOCOUNT ON
	
	SELECT ord.InvoiceId
		, ord.OrderId
		, ord.OrderDateTime
		, ord.WorkflowStage
		, ord.IsCancelled
		, ord.IsExternalSubOrder
		, ord.DisallowReferral
		, ord.IsReferrerUnknown
		, ord.FullName AS PatientName
		, ord.Sex
		, ord.Age
		, ord.DoB
		, COALESCE(phy.FullName, ord.ReferrerCustomName, '') AS ReferrerName
		, inv.GrossPayable
		, inv.DiscountAmount
		, inv.NetPayable
		, inv.PaidAmount
		, inv.DueAmount
	FROM
		PROE.PatientLabOrders ord
		LEFT OUTER JOIN Catalog.Referrers phy
			ON ord.ReferrerId = phy.Id
		INNER JOIN Finances.InvoiceMaster inv
			ON inv.InvoiceId = ord.InvoiceId
	WHERE
		FREETEXT(ord.FullName, @searchString)
		AND ord.OrderDateTime BETWEEN @dtFrom AND @dtTo
	ORDER BY
		ord.InvoiceId
go
IF OBJECT_ID('PROE.SP_SearchLabOrdersByPatientNameAndDateRange') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_SearchLabOrdersByPatientNameAndDateRange >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_SearchLabOrdersByPatientNameAndDateRange >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_SearchLabOrdersByReferrerIdAndDateRange] 
 */

CREATE PROCEDURE [PROE].[SP_SearchLabOrdersByReferrerIdAndDateRange]
	@referrerId int
	, @dtFrom datetime
	, @dtTo datetime

AS
	SET NOCOUNT ON
		
	SELECT ord.InvoiceId
     	, ord.OrderId
     	, ord.OrderDateTime
     	, ord.WorkflowStage
     	, ord.IsCancelled
     	, ord.IsExternalSubOrder
     	, ord.DisallowReferral
     	, ord.IsReferrerUnknown
     	, ord.FullName AS PatientName
     	, ord.Sex
     	, ord.Age
     	, ord.DoB
     	, COALESCE(phy.FullName, ord.ReferrerCustomName, '') AS ReferrerName
     	, inv.GrossPayable
     	, inv.DiscountAmount
     	, inv.NetPayable
     	, inv.PaidAmount
     	, inv.DueAmount
	FROM
  		PROE.PatientLabOrders ord
  		LEFT OUTER JOIN Catalog.Referrers phy
    		ON ord.ReferrerId = phy.Id
  		INNER JOIN Finances.InvoiceMaster inv
    		ON inv.InvoiceId = ord.InvoiceId
	WHERE
  		ord.ReferrerId = @referrerId
  		AND ord.OrderDateTime BETWEEN @dtFrom AND @dtTo
	ORDER BY
  		ord.InvoiceId
go
IF OBJECT_ID('PROE.SP_SearchLabOrdersByReferrerIdAndDateRange') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_SearchLabOrdersByReferrerIdAndDateRange >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_SearchLabOrdersByReferrerIdAndDateRange >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_SearchLabOrdersByWorkflowAndDateRange] 
 */

CREATE PROCEDURE [PROE].[SP_SearchLabOrdersByWorkflowAndDateRange]
	@wfStage tinyint
	, @dtFrom datetime
	, @dtTo datetime
AS
	SET NOCOUNT ON
	
	SELECT ord.InvoiceId
		, ord.OrderId
		, ord.OrderDateTime
		, ord.WorkflowStage
		, ord.IsCancelled
		, ord.IsExternalSubOrder
     	, ord.DisallowReferral
     	, ord.IsReferrerUnknown
		, ord.FullName AS PatientName
     	, ord.Sex
     	, ord.Age
     	, ord.DoB		
		, COALESCE(phy.FullName, ord.ReferrerCustomName, '') AS ReferrerName
		, inv.GrossPayable
		, inv.DiscountAmount
		, inv.NetPayable
		, inv.PaidAmount
		, inv.DueAmount
	FROM
		PROE.PatientLabOrders ord
		LEFT OUTER JOIN Catalog.Referrers phy
			ON ord.ReferrerId = phy.Id
		INNER JOIN Finances.InvoiceMaster inv
			ON inv.InvoiceId = ord.InvoiceId
	WHERE
		ord.WorkflowStage = @wfStage
		AND ord.OrderDateTime BETWEEN @dtFrom AND @dtTo
	ORDER BY   
		ord.InvoiceId
go
IF OBJECT_ID('PROE.SP_SearchLabOrdersByWorkflowAndDateRange') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_SearchLabOrdersByWorkflowAndDateRange >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_SearchLabOrdersByWorkflowAndDateRange >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_SearchLabOrdersForRequestingLabByDateRange] 
 */

CREATE PROCEDURE [PROE].[SP_SearchLabOrdersForRequestingLabByDateRange]
  @labId   INT,
  @dtFrom  DATETIME,
  @dtTo    DATETIME
AS
  SET  NOCOUNT ON

  SELECT
    ord.InvoiceId,
    ord.OrderId,
    ord.OrderDateTime,
    ord.WorkflowStage,
    ord.IsCancelled,
    ord.IsExternalSubOrder,
    ord.DisallowReferral,
    ord.IsReferrerUnknown,
    ord.FullName AS PatientName,
    ord.Sex,
    ord.Age,
    ord.DoB,
    COALESCE(
      phy.FullName,
      ord.ReferrerCustomName,
      '')
      AS ReferrerName,
    inv.GrossPayable,
    inv.DiscountAmount,
    inv.NetPayable,
    inv.PaidAmount,
    inv.DueAmount,
    inv.RefundAmount,
    ord.ReferrerId
  FROM
    PROE.PatientLabOrders ord
    LEFT OUTER JOIN Catalog.Referrers phy ON ord.ReferrerId = phy.Id
    INNER JOIN Finances.InvoiceMaster inv ON inv.InvoiceId = ord.InvoiceId
    INNER JOIN SubContract.RequestingLabs reqLab
      ON ord.RequestingLabId = reqLab.Id
  WHERE
    reqLab.Id = @labId AND
    ord.OrderDateTime BETWEEN @dtFrom AND @dtTo
  ORDER BY
    ord.InvoiceId
go
IF OBJECT_ID('PROE.SP_SearchLabOrdersForRequestingLabByDateRange') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_SearchLabOrdersForRequestingLabByDateRange >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_SearchLabOrdersForRequestingLabByDateRange >>>'
go


/* 
 * PROCEDURE: [SubContract].[SP_SearchLabOrdersForRequestingLabByTrackingId] 
 */

CREATE PROCEDURE [SubContract].[SP_SearchLabOrdersForRequestingLabByTrackingId]
  @labId   INT,
  @dtFrom  DATETIME,
  @dtTo    DATETIME,
  @trkId   VARCHAR(40)
AS
  SET  NOCOUNT ON

  SELECT
    ord.InvoiceId,
    ord.OrderId,
    ord.OrderDateTime,
    ord.WorkflowStage,
    ord.IsCancelled,
    ord.IsExternalSubOrder,
    ord.DisallowReferral,
    ord.IsReferrerUnknown,
    ord.FullName AS PatientName,
    ord.Sex,
    ord.Age,
    ord.DoB,
    COALESCE(phy.FullName, ord.ReferrerCustomName, '') AS ReferrerName,
    inv.GrossPayable,
    inv.DiscountAmount,
    inv.NetPayable,
    inv.PaidAmount,
    inv.DueAmount,
    inv.RefundAmount,
    ord.ReferrerId
  FROM
    PROE.PatientLabOrders ord
    LEFT OUTER JOIN Catalog.Referrers phy ON ord.ReferrerId = phy.Id
    INNER JOIN Finances.InvoiceMaster inv ON inv.InvoiceId = ord.InvoiceId
    INNER JOIN SubContract.RequestingLabs reqLab
      ON ord.RequestingLabId = reqLab.Id
  WHERE
    reqLab.Id = @labId AND
    ord.SubOrderTrackingId = @trkId AND
    ord.OrderDateTime BETWEEN @dtFrom AND @dtTo
  ORDER BY
    ord.InvoiceId

go
IF OBJECT_ID('SubContract.SP_SearchLabOrdersForRequestingLabByTrackingId') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE SubContract.SP_SearchLabOrdersForRequestingLabByTrackingId >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE SubContract.SP_SearchLabOrdersForRequestingLabByTrackingId >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_SearchOrderedBillableItemsInDateRange] 
 */

CREATE PROCEDURE [PROE].[SP_SearchOrderedBillableItemsInDateRange]
  @dtStart  SMALLDATETIME,
  @dtEnd    SMALLDATETIME
AS
  SET  NOCOUNT ON

  SELECT
    bi.Id,
    obi.IsCancelled,
    bi.Name,
    obi.UnitPrice,
    obi.Quantity
  FROM
    PROE.OrderedBillableItems obi
    INNER JOIN Catalog.BillableItems bi ON obi.BillableItemId = bi.Id
  WHERE
    bi.IsActive = 1 AND
    obi.DateCreated BETWEEN @dtStart AND @dtEnd
go
IF OBJECT_ID('PROE.SP_SearchOrderedBillableItemsInDateRange') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_SearchOrderedBillableItemsInDateRange >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_SearchOrderedBillableItemsInDateRange >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_SearchOrderedTestsForLabInDateRange] 
 */

CREATE PROCEDURE [PROE].[SP_SearchOrderedTestsForLabInDateRange]
  @labId    SMALLINT,
  @dtStart  SMALLDATETIME,
  @dtEnd    SMALLDATETIME
AS
  SET  NOCOUNT ON

  SELECT
    ot.LabTestId,
    lab.Id AS LabId,
    lt.ShortName,
    lt.CanonicalName,
    ot.UnitPrice,
    lt.SubOrderPrice,
    lt.CostBasis,
    ot.IsCancelled
  FROM
    Catalog.LabTests lt
    INNER JOIN Catalog.Labs lab ON lt.PerformingLabId = lab.Id
    INNER JOIN PROE.OrderedTests ot ON ot.LabTestId = lt.Id
  WHERE
    ot.DateCreated BETWEEN @dtStart AND @dtEnd AND
    lab.Id = @labId
go
IF OBJECT_ID('PROE.SP_SearchOrderedTestsForLabInDateRange') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_SearchOrderedTestsForLabInDateRange >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_SearchOrderedTestsForLabInDateRange >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_SearchOrderedTestsInDateRange] 
 */

CREATE PROCEDURE [PROE].[SP_SearchOrderedTestsInDateRange]
  @dtStart  SMALLDATETIME,
  @dtEnd    SMALLDATETIME
AS
  SET  NOCOUNT ON

  SELECT
    ot.LabTestId,
    lab.Id AS LabId,
    lt.ShortName,
    lt.CanonicalName,
    ot.UnitPrice,
    lt.SubOrderPrice,
    lt.CostBasis,
    ot.IsCancelled
  FROM
    Catalog.LabTests lt
    INNER JOIN Catalog.Labs lab ON lt.PerformingLabId = lab.Id
    INNER JOIN PROE.OrderedTests ot ON ot.LabTestId = lt.Id
  WHERE
    ot.DateCreated BETWEEN @dtStart AND @dtEnd
go
IF OBJECT_ID('PROE.SP_SearchOrderedTestsInDateRange') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_SearchOrderedTestsInDateRange >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_SearchOrderedTestsInDateRange >>>'
go


/* 
 * PROCEDURE: [APP_SYS].[SP_UpdateAppFaultScreenCap] 
 */

CREATE PROCEDURE [APP_SYS].[SP_UpdateAppFaultScreenCap]
	@uuid uniqueidentifier,
	@data varbinary(max)
AS
	SET XACT_ABORT ON
	
	SET NOCOUNT ON 		
	
	BEGIN TRANSACTION
	
		UPDATE 
			APP_SYS.AppFaults 
		SET 
			ScreenCap = @data 
		WHERE 
			CorrelationId = @uuid
		
	COMMIT TRANSACTION
go
IF OBJECT_ID('APP_SYS.SP_UpdateAppFaultScreenCap') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE APP_SYS.SP_UpdateAppFaultScreenCap >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE APP_SYS.SP_UpdateAppFaultScreenCap >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_UpdateAppTemplateReportContent] 
 */

CREATE PROCEDURE [Catalog].[SP_UpdateAppTemplateReportContent]
	@reportCode varchar(80),
	@lobContent varbinary(MAX)
AS
	SET XACT_ABORT ON
	
	SET NOCOUNT ON 		
	
	BEGIN TRANSACTION
	
		UPDATE Catalog.AppReportTemplates
		SET
			ReportContent = @lobContent,
      		LastModified = GETDATE()
		WHERE
			ReportCode = @reportCode
	
	COMMIT TRANSACTION

go
IF OBJECT_ID('Catalog.SP_UpdateAppTemplateReportContent') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_UpdateAppTemplateReportContent >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_UpdateAppTemplateReportContent >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_UpdateBillableItem] 
 */

CREATE PROCEDURE [Catalog].[SP_UpdateBillableItem]
	@itemId smallint,
	@name varchar(80),
	@isActive bit,
	@unitPrice smallmoney
AS
	SET XACT_ABORT ON
	
	SET NOCOUNT ON 		
	
	BEGIN TRANSACTION
	
		UPDATE 
			[Catalog].[BillableItems]
		SET 
			[Name] = @name,
			[UnitPrice] = @unitPrice,
			[IsActive] = @isActive
		WHERE
			[Id] = @itemId
	
	COMMIT TRANSACTION
go
IF OBJECT_ID('Catalog.SP_UpdateBillableItem') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_UpdateBillableItem >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_UpdateBillableItem >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_UpdateDiscountLevel] 
 */

CREATE PROCEDURE [Catalog].[SP_UpdateDiscountLevel]
	@levelId SMALLINT,
	@name VARCHAR(40),
	@discMode TINYINT,
	@discPercent FLOAT,
	@discAmount SMALLMONEY
AS
	SET XACT_ABORT ON
	
	SET NOCOUNT ON 		
	
	BEGIN TRANSACTION
	
		UPDATE 
			[Catalog].[DiscountLevels]
		SET 
			[Name] = @name,
			[DiscountMode] = @discMode,
			[DiscountPercent] = @discPercent,
			[DiscountAmount] = @discAmount
		WHERE
			[Id] = @levelId
	
	COMMIT TRANSACTION
go
IF OBJECT_ID('Catalog.SP_UpdateDiscountLevel') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_UpdateDiscountLevel >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_UpdateDiscountLevel >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_UpdateDiscreteReportLineItemSortOrder] 
 */

CREATE PROCEDURE [Catalog].[SP_UpdateDiscreteReportLineItemSortOrder]
	@id int, 
	@sortOrder tinyint
AS
	SET XACT_ABORT ON
	
	SET NOCOUNT ON 		
	
	BEGIN TRANSACTION
	
		UPDATE [Catalog].[DiscreteReportLineItems]
		SET
			SortOrder = @sortOrder
		WHERE
			Id = @id
	
	COMMIT TRANSACTION
go
IF OBJECT_ID('Catalog.SP_UpdateDiscreteReportLineItemSortOrder') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_UpdateDiscreteReportLineItemSortOrder >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_UpdateDiscreteReportLineItemSortOrder >>>'
go


/* 
 * PROCEDURE: [TestResults].[SP_UpdateDiscreteResultLineItem] 
 */

CREATE PROCEDURE [TestResults].[SP_UpdateDiscreteResultLineItem]
	@itemId bigint, 
	@param varchar(max), 
	@units varchar(max),
	@result varchar(max),      
	@refRange varchar(max), 
	@flag varchar(40)
AS
	SET XACT_ABORT ON
	
	SET NOCOUNT ON 		
	
	BEGIN TRANSACTION
		
		UPDATE
			[TestResults].[DiscreteResultLineItems]
		SET
			[Parameter] = @param,
			[Result] = @result,
			[Units] = @units,
			[ReferenceRange] = @refRange,
			[Flag] = @flag
		WHERE
			[Id] = @itemId
		
	COMMIT TRANSACTION
go
IF OBJECT_ID('TestResults.SP_UpdateDiscreteResultLineItem') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE TestResults.SP_UpdateDiscreteResultLineItem >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE TestResults.SP_UpdateDiscreteResultLineItem >>>'
go


/* 
 * PROCEDURE: [Finances].[SP_UpdateInvoiceMaster] 
 */

CREATE PROCEDURE [Finances].[SP_UpdateInvoiceMaster]
	@invoiceId    BIGINT,
	@status       TINYINT,
	@gross        MONEY,
	@discount     MONEY,
	@tax          MONEY,
	@surch        MONEY,
	@net          MONEY,
	@paid         MONEY,
	@due          MONEY,
	@refund       MONEY
AS
   SET  XACT_ABORT ON
   SET  NOCOUNT ON

   BEGIN TRANSACTION

   		UPDATE Finances.InvoiceMaster
      		SET PaymentStatus = @status,
          		GrossPayable = @gross,
          		DiscountAmount = @discount,
          		TaxAmount = @tax,
          		SurchargeAmount = @surch,
          		NetPayable = @net,
          		PaidAmount = @paid,
          		DueAmount = @due,
          		RefundAmount = @refund
    		WHERE InvoiceId = @invoiceId

   COMMIT TRANSACTION
go
IF OBJECT_ID('Finances.SP_UpdateInvoiceMaster') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Finances.SP_UpdateInvoiceMaster >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Finances.SP_UpdateInvoiceMaster >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_UpdateLab] 
 */

CREATE PROCEDURE [Catalog].[SP_UpdateLab]
	@labId smallint, 
	@isActive bit, 
	@code varchar(16), 
	@name varchar(40), 
	@printName varchar(40), 
	@printCanon bit, 
	@resType tinyint, 
	@isAux bit, 
	@postOE tinyint, 
	@postRE tinyint, 
	@postRV tinyint, 
	@postRF tinyint, 
	@postRC tinyint, 
	@refId smallint, 
	@refGrpName varchar(40), 
	@accGrpName varchar(40),
	@lvlId smallint, 
	@hdrId int
AS
	SET XACT_ABORT ON
	
	SET NOCOUNT ON 		
	
	BEGIN TRANSACTION
		
		UPDATE
			[Catalog].[Labs]
		SET
			[Name] = @name,
			[IsActive] = @isActive,
			[LabCode] = @code,
			[ReqPrintName] = @printName,
			[ReqPrintCanonicalTestName] = @printCanon,
			[TestResultType] = @resType,
			[IsAuxProcedure] = @isAux,
			[PostOrderEntryWorkflowStage] = @postOE,
			[PostResultEntryWorkflowStage] = @postRE,
			[PostResultVerificationWorkflowStage] = @postRV,
			[PostResultFinalizationWorkflowStage] = @postRF,
			[PostReportCollationWorkflowStage] = @postRC,
			[ReferralGroupId] = @refId,
			[ReferralGroupDisplayName] = @refGrpName,
			[AccountingGroupDisplayName] = @accGrpName,
			[DiscountLevelId] = @lvlId,
			[DefaultReportHeaderId] = @hdrId
		WHERE
			[Id] = @labId
	
	COMMIT TRANSACTION
go
IF OBJECT_ID('Catalog.SP_UpdateLab') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_UpdateLab >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_UpdateLab >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_UpdateLabOrderDemographic] 
 */

CREATE PROCEDURE [PROE].[SP_UpdateLabOrderDemographic]
   @invoiceId       BIGINT,
   @title           VARCHAR (20),
   @fname           VARCHAR (120),
   @lname           VARCHAR (120),
   @sex             TINYINT,
   @age             VARCHAR (20),
   @dob             DATE,
   @phone           VARCHAR (20),
   @email           VARCHAR (40),
   @emailResults    BIT,
   @refId           INT,
   @refDisallow     BIT,
   @refUnk          BIT,
   @refCustName     VARCHAR (160),
   @extSubOrd       BIT,
   @extTrack        VARCHAR (16),
   @mirror          TINYINT,
   @note            VARCHAR (160)
AS
   SET  XACT_ABORT ON

   SET  NOCOUNT ON

   BEGIN TRANSACTION

   	UPDATE PROE.PatientLabOrders
      	SET ReferrerId = @refId,
      		LastModified = GETDATE(),
          	DisallowReferral = @refDisallow,
          	Title = @title,
          	FirstName = @fname,
          	LastName = @lname,
          	Sex = @sex,
          	Age = @age,
          	DoB = @dob,
          	PhoneNumber = @phone,
          	EmailAddress = @email,
          	EmailTestResults = @emailResults,
          	IsReferrerUnknown = @refUnk,
          	ReferrerCustomName = @refCustName,
          	OrderNotes = @note,
          	IsExternalSubOrder = @extSubOrd,
          	SubOrderTrackingId = @extTrack,
          	MirrorFlag = @mirror
    	WHERE 
    		InvoiceId = @invoiceId

   COMMIT TRANSACTION
go
IF OBJECT_ID('PROE.SP_UpdateLabOrderDemographic') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_UpdateLabOrderDemographic >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_UpdateLabOrderDemographic >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_UpdateLabOrderMirrorFlag] 
 */

CREATE PROCEDURE [PROE].[SP_UpdateLabOrderMirrorFlag]
	@invoiceId bigint, 
	@mirrorFlag tinyint
AS
	SET XACT_ABORT ON
	
	SET NOCOUNT ON 		
	
	BEGIN TRANSACTION
	
		UPDATE 
			[PROE].[PatientLabOrders]
		SET 
			[MirrorFlag] = @mirrorFlag,
			[LastModified] = GETDATE()
		WHERE 
			[InvoiceId] = @invoiceId
	
	COMMIT TRANSACTION
go
IF OBJECT_ID('PROE.SP_UpdateLabOrderMirrorFlag') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_UpdateLabOrderMirrorFlag >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_UpdateLabOrderMirrorFlag >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_UpdateLabOrderWorkflowStage] 
 */

CREATE PROCEDURE [PROE].[SP_UpdateLabOrderWorkflowStage]
	@invoiceId bigint, 
	@wfStage tinyint
AS
	SET XACT_ABORT ON
	
	SET NOCOUNT ON 		
	
	BEGIN TRANSACTION
	
	UPDATE 
		[PROE].[PatientLabOrders]
	SET 
		[WorkflowStage] = @wfStage,
		[LastModified] = GETDATE()
	WHERE 
		[InvoiceId] = @invoiceId
	
	COMMIT TRANSACTION

go
IF OBJECT_ID('PROE.SP_UpdateLabOrderWorkflowStage') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_UpdateLabOrderWorkflowStage >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_UpdateLabOrderWorkflowStage >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_UpdateLabReportHeader] 
 */

CREATE PROCEDURE [Catalog].[SP_UpdateLabReportHeader]
	@id int,
	@name varchar(80), 
	@labId smallint, 
	@isActive bit, 
	@sortPriority tinyint, 
	@headerContent varbinary(max)
AS

	SET XACT_ABORT ON
	
	SET NOCOUNT ON 		
	
	BEGIN TRANSACTION
	
		UPDATE [Catalog].[LabReportHeaders]
		SET
			LabId = @labId
			, IsActive = @isActive
			, Name = @name
			, SortPriority = @sortPriority
			, ReportHeader = @headerContent
		WHERE 
			Id = @id
	
	COMMIT TRANSACTION
go
IF OBJECT_ID('Catalog.SP_UpdateLabReportHeader') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_UpdateLabReportHeader >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_UpdateLabReportHeader >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_UpdateLabTest] 
 */

CREATE PROCEDURE [Catalog].[SP_UpdateLabTest]
   @id            SMALLINT,
   @active        BIT,
   @sku           VARCHAR (24),
   @shortName     VARCHAR (160),
   @canonName     VARCHAR (160),
   @price         SMALLMONEY,
   @subPrice      SMALLMONEY,
   @costBasis     SMALLMONEY,
   @slipOpt       TINYINT,
   @hl7           TINYINT,
   @prio          TINYINT,
   @mnenonics     VARCHAR (160),
   @plab          SMALLINT,
   @rlab          SMALLINT,
   @tplId         SMALLINT,
   @tat           SMALLINT,
   @tplGrp        SMALLINT,
   @rptLineGrp    VARCHAR (16)
AS
   SET  XACT_ABORT ON

   SET  NOCOUNT ON

   BEGIN TRANSACTION

   UPDATE Catalog.LabTests
      SET IsActive = @active,
          TestSKU = @sku,
          ShortName = @shortName,
          CanonicalName = @canonName,
          ListPrice = @price,
          SubOrderPrice = @subPrice,
          CostBasis = @costBasis,
          ReqSlipPrintOption = @slipOpt,
          HL7AutomationOption = @hl7,
          ReportSortPriority = @prio,
          LastModified = GETDATE (),
          Mnemonics = @mnenonics,
          PerformingLabId = @plab,
          ResultingLabId = @rlab,
          DefaultTemplateId = @tplId,
          TATGroupId = @tat,
          TemplateGroupId = @tplGrp,
          ReportLineGroupingTag = @rptLineGrp
    WHERE Id = @id

   COMMIT TRANSACTION
go
IF OBJECT_ID('Catalog.SP_UpdateLabTest') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_UpdateLabTest >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_UpdateLabTest >>>'
go


/* 
 * PROCEDURE: [Marketing].[SP_UpdateReferralGroup] 
 */

CREATE PROCEDURE [Marketing].[SP_UpdateReferralGroup]
	@refId SMALLINT,
	@isActive BIT,
	@name VARCHAR(40),
	@refMode TINYINT,
	@refPercent FLOAT,
	@refAmount SMALLMONEY
AS
	SET XACT_ABORT ON
	
	SET NOCOUNT ON 		
	
	BEGIN TRANSACTION
	
		UPDATE 
			[Marketing].[ReferralGroups]
		SET 
			[Name] = @name,
			[IsActive] = @isActive,
			[ReferralMode] = @refMode,
			[ReferralPercent] = @refPercent,
			[ReferralAmount] = @refAmount
		WHERE
			[Id] = @refId
	
	COMMIT TRANSACTION
go
IF OBJECT_ID('Marketing.SP_UpdateReferralGroup') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Marketing.SP_UpdateReferralGroup >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Marketing.SP_UpdateReferralGroup >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_UpdateReferrer] 
 */

CREATE PROCEDURE [Catalog].[SP_UpdateReferrer]
  @id         INT,
  @active     BIT,
  @prefix     VARCHAR(40),
  @name       VARCHAR(160),
  @suffix     VARCHAR(160),
  @tag        VARCHAR(40),
  @mobile     VARCHAR(20),
  @email      VARCHAR(40),
  @webActive  BIT,
  @webid      VARCHAR(40),
  @webPass    VARCHAR(40),
  @updUid     SMALLINT,
  @execId     SMALLINT,
  @refClass   SMALLINT,
  @suppress   BIT
AS
  SET  XACT_ABORT ON

  SET  NOCOUNT ON

  BEGIN TRANSACTION

    UPDATE
      Catalog.Referrers
    SET
      IsActive            = @active,
      Prefix              = @prefix,
      Name                = @name,
      Suffix              = @suffix,
      IdentifyingTag      = @tag,
      MobilePhone         = @mobile,
      Email               = @email,
      LastUpdated         = GETDATE(),
      WebLoginEnabled     = @webActive,
      WebLoginId          = @webid,
      WebPassKey          = @webPass,
      LastUpdatedByUserId = @updUid,
      ReferralClassId     = @refClass,
      MarketingExecId     = @execId,
      SuppressNetReferral = @suppress
    WHERE
      Id = @id

  COMMIT TRANSACTION
go
IF OBJECT_ID('Catalog.SP_UpdateReferrer') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_UpdateReferrer >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_UpdateReferrer >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_UpdateReferrerCategory] 
 */

CREATE PROCEDURE [Catalog].[SP_UpdateReferrerCategory]
	@id smallint,
	@name varchar(40),
	@isActive bit
AS
	SET XACT_ABORT ON
	
	SET NOCOUNT ON 		
	
	BEGIN TRANSACTION	
	
		UPDATE 
			Catalog.ReferrerCategories
		SET 
			[Name] = @name,
			[IsActive] = @isActive
		WHERE
			[Id] = @id
	
	COMMIT TRANSACTION
go
IF OBJECT_ID('Catalog.SP_UpdateReferrerCategory') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_UpdateReferrerCategory >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_UpdateReferrerCategory >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_UpdateReqParameterSortOrder] 
 */

CREATE PROCEDURE [Catalog].[SP_UpdateReqParameterSortOrder]
	@id int, 
	@sortOrder tinyint
AS
	SET XACT_ABORT ON
	
	SET NOCOUNT ON 		
	
	BEGIN TRANSACTION
	
		UPDATE [Catalog].[ReqParameters]
		SET
			SortOrder = @sortOrder
		WHERE
			Id = @id
	
	COMMIT TRANSACTION
go
IF OBJECT_ID('Catalog.SP_UpdateReqParameterSortOrder') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_UpdateReqParameterSortOrder >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_UpdateReqParameterSortOrder >>>'
go


/* 
 * PROCEDURE: [SubContract].[SP_UpdateRequestingLabUserLoginTime] 
 */

CREATE PROCEDURE [SubContract].[SP_UpdateRequestingLabUserLoginTime]
  @userId   INT
AS
	SET XACT_ABORT ON	
	SET NOCOUNT ON	
	
	BEGIN TRANSACTION
    	UPDATE [SubContract].[RequestingLabUsers] 
    	SET LastLogin = GETDATE() 
    	WHERE Id = @userId
  	COMMIT TRANSACTION
go
IF OBJECT_ID('SubContract.SP_UpdateRequestingLabUserLoginTime') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE SubContract.SP_UpdateRequestingLabUserLoginTime >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE SubContract.SP_UpdateRequestingLabUserLoginTime >>>'
go


/* 
 * PROCEDURE: [TestResults].[SP_UpdateResultBundleFinalizingConsultant] 
 */

CREATE PROCEDURE [TestResults].[SP_UpdateResultBundleFinalizingConsultant]
	@bundleId bigint, 
	@consultantId smallint
AS
	SET XACT_ABORT ON
	
	SET NOCOUNT ON 		
	
	BEGIN TRANSACTION	
		DECLARE @dispName varchar(160)
		DECLARE @query nvarchar(200)
		DECLARE @param nvarchar(200)
		
		-- fetch the users full name
		SELECT @query = N'SELECT TOP 1 @dispNameOut = LTRIM(RTRIM(COALESCE([Title], '' '') + '' '' + COALESCE([FirstName], '' '') + '' '' + COALESCE([LastName], '' '') + '' '' + COALESCE([Suffix], '' ''))) FROM [Staff].[Users] WHERE [Id]=' + CAST(@consultantId AS VARCHAR(16));
		SET @param = N'@dispNameOut varchar(160) OUTPUT';
		EXEC sp_executesql @query, @param, @dispNameOut=@dispName OUTPUT;

		-- now update the record
		UPDATE 
			[TestResults].[ResultBundles]
		SET 
			[LastUpdated] = GETDATE(),
			[FinalizingConsultantId] = @consultantId,
			[FinalizingConsultantName] = @dispName
		WHERE 
			[Id] = @bundleId
	
	COMMIT TRANSACTION
go
IF OBJECT_ID('TestResults.SP_UpdateResultBundleFinalizingConsultant') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE TestResults.SP_UpdateResultBundleFinalizingConsultant >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE TestResults.SP_UpdateResultBundleFinalizingConsultant >>>'
go


/* 
 * PROCEDURE: [TestResults].[SP_UpdateResultBundleLabReportHeader] 
 */

CREATE PROCEDURE [TestResults].[SP_UpdateResultBundleLabReportHeader]
	@bundleId bigint, 
	@headerId int
AS
	SET XACT_ABORT ON
	
	SET NOCOUNT ON 		
	
	BEGIN TRANSACTION
	
		UPDATE 
			[TestResults].[ResultBundles]
		SET 
			[LastUpdated] = GETDATE(),
			[ReportHeaderId] = @headerId
		WHERE 
			[Id] = @bundleId
	
	COMMIT TRANSACTION
go
IF OBJECT_ID('TestResults.SP_UpdateResultBundleLabReportHeader') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE TestResults.SP_UpdateResultBundleLabReportHeader >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE TestResults.SP_UpdateResultBundleLabReportHeader >>>'
go


/* 
 * PROCEDURE: [TestResults].[SP_UpdateResultBundleLastUpdatedTime] 
 */

CREATE PROCEDURE [TestResults].[SP_UpdateResultBundleLastUpdatedTime]
	@bundleId bigint
AS
	SET XACT_ABORT ON
	
	SET NOCOUNT ON 		
	
	BEGIN TRANSACTION
	
		UPDATE 
			[TestResults].[ResultBundles]
		SET 
			[LastUpdated] = GETDATE()
		WHERE 
			[Id] = @bundleId
	
	COMMIT TRANSACTION
GO
go
IF OBJECT_ID('TestResults.SP_UpdateResultBundleLastUpdatedTime') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE TestResults.SP_UpdateResultBundleLastUpdatedTime >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE TestResults.SP_UpdateResultBundleLastUpdatedTime >>>'
go


/* 
 * PROCEDURE: [TestResults].[SP_UpdateResultBundleResultNotes] 
 */

CREATE PROCEDURE [TestResults].[SP_UpdateResultBundleResultNotes]
	@bundleId bigint, 
	@notes varbinary(max)
AS
	SET XACT_ABORT ON
	
 	SET NOCOUNT ON 		
 	
	BEGIN TRANSACTION

		UPDATE 
			[TestResults].[ResultBundles]
		SET 
			[LastUpdated] = GETDATE(),
			[ResultNotes] = @notes
		WHERE 
			[Id] = @bundleId
		
	COMMIT TRANSACTION
go
IF OBJECT_ID('TestResults.SP_UpdateResultBundleResultNotes') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE TestResults.SP_UpdateResultBundleResultNotes >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE TestResults.SP_UpdateResultBundleResultNotes >>>'
go


/* 
 * PROCEDURE: [TestResults].[SP_UpdateResultBundleWorkflowStage] 
 */

CREATE PROCEDURE [TestResults].[SP_UpdateResultBundleWorkflowStage]
	@bundleId bigint, 
	@wfStage tinyint
AS
	SET XACT_ABORT ON
	
 	SET NOCOUNT ON 		
 	
	BEGIN TRANSACTION

		UPDATE 
			[TestResults].[ResultBundles]
		SET 
			[LastUpdated] = GETDATE(),
			[WorkflowStage] = @wfStage
		WHERE 
			[Id] = @bundleId
		
    	UPDATE 
      		[PROE].[OrderedTests]
    	SET
      		[LastModified] = GETDATE(),
      		[WorkflowStage] = @wfStage
    	WHERE
      		[ResultBundleId] = @bundleId AND
      		[IsCancelled] = 0      
		
	COMMIT TRANSACTION
GO
go
IF OBJECT_ID('TestResults.SP_UpdateResultBundleWorkflowStage') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE TestResults.SP_UpdateResultBundleWorkflowStage >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE TestResults.SP_UpdateResultBundleWorkflowStage >>>'
go


/* 
 * PROCEDURE: [Finances].[SP_UpdateShiftAdditionalBalance] 
 */

CREATE PROCEDURE [Finances].[SP_UpdateShiftAdditionalBalance]
	@shiftId int
	, @amount money
AS
	SET  XACT_ABORT ON
	SET  NOCOUNT ON
	
	BEGIN TRANSACTION
	
		UPDATE Finances.WorkShifts 
		SET LastUpdated = GETDATE()
			, AdditionalBalance = @amount
		WHERE Id = @shiftId
	
	COMMIT TRANSACTION
go
IF OBJECT_ID('Finances.SP_UpdateShiftAdditionalBalance') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Finances.SP_UpdateShiftAdditionalBalance >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Finances.SP_UpdateShiftAdditionalBalance >>>'
go


/* 
 * PROCEDURE: [Finances].[SP_UpdateShiftFinances] 
 */

CREATE PROCEDURE [Finances].[SP_UpdateShiftFinances]
	@shiftId int
	, @numOrders smallint
	, @payment money
	, @discount money
	, @discRebate money
	, @refund money
	, @addlBal money
	, @finalBal money
AS
	SET  XACT_ABORT ON
	SET  NOCOUNT ON
	
	BEGIN TRANSACTION
		
		UPDATE Finances.WorkShifts 
		SET
			LastUpdated = GETDATE()
			,NumOrders = @numOrders
			,AdditionalBalance = @addlBal
			,ReceiveAmount = @payment
			,DiscountAmount = @discount
			,DiscountRebateAmount = @discRebate
			,RefundAmount = @refund
			,FinalBalance = @finalBal
		WHERE
			Id = @shiftId

  	COMMIT TRANSACTION
go
IF OBJECT_ID('Finances.SP_UpdateShiftFinances') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Finances.SP_UpdateShiftFinances >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Finances.SP_UpdateShiftFinances >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_UpdateTemplateReport] 
 */

CREATE PROCEDURE [Catalog].[SP_UpdateTemplateReport]
	@id smallint, 
	@active bit, 
	@name varchar(160), 
	@sort tinyint, 
	@tags varchar(160)
AS
	SET XACT_ABORT ON
	
	SET NOCOUNT ON 		
	
	BEGIN TRANSACTION
	
		UPDATE [Catalog].[TemplateReports]
		SET
			[IsActive] = @active
			, [Name] = @name
			, [SortPriority] = @sort
			, [Tags] = @tags
		WHERE
			[Id] = @id
	
	COMMIT TRANSACTION
go
IF OBJECT_ID('Catalog.SP_UpdateTemplateReport') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_UpdateTemplateReport >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_UpdateTemplateReport >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_UpdateTemplateReportContent] 
 */

CREATE PROCEDURE [Catalog].[SP_UpdateTemplateReportContent]
	@itemId smallint, 
	@lobContent varbinary(max)
AS
	SET XACT_ABORT ON
	
	SET NOCOUNT ON 		
	
	BEGIN TRANSACTION

		UPDATE 
            [Catalog].[TemplateReports]
		SET 
            [Content] = @lobContent
		WHERE
			[Id] = @itemId
	
	COMMIT TRANSACTION
go
IF OBJECT_ID('Catalog.SP_UpdateTemplateReportContent') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_UpdateTemplateReportContent >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_UpdateTemplateReportContent >>>'
go


/* 
 * PROCEDURE: [TestResults].[SP_UpdateTemplateResult] 
 */

CREATE PROCEDURE [TestResults].[SP_UpdateTemplateResult]
	@itemId bigint, 
	@lobContent varbinary(max)
AS
	SET XACT_ABORT ON
	
	SET NOCOUNT ON 		
	
	BEGIN TRANSACTION
	
		UPDATE 
			[TestResults].[TemplateResults]
		SET 
			[Content] = @lobContent
		WHERE
			[Id] = @itemId
	
	COMMIT TRANSACTION
go
IF OBJECT_ID('TestResults.SP_UpdateTemplateResult') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE TestResults.SP_UpdateTemplateResult >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE TestResults.SP_UpdateTemplateResult >>>'
go


/* 
 * PROCEDURE: [Catalog].[SP_UpdateTestBILink] 
 */

CREATE PROCEDURE [Catalog].[SP_UpdateTestBILink]
	@id int,
	@testId smallint, 
	@itemId smallint, 
	@optLevel tinyint, 
	@qty smallint
AS
	SET XACT_ABORT ON
	
	SET NOCOUNT ON 		
	
	BEGIN TRANSACTION	
	
		UPDATE 
			Catalog.TestBILink
		SET
			TestId = @testId, 
			BillableItemId = @itemId, 
			OptimizationLevel = @optLevel, 
			Quantity = @qty
		WHERE
			Id = @id
		
	COMMIT TRANSACTION
go
IF OBJECT_ID('Catalog.SP_UpdateTestBILink') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Catalog.SP_UpdateTestBILink >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Catalog.SP_UpdateTestBILink >>>'
go


/* 
 * PROCEDURE: [TestResults].[SP_UpdateTestWithResultBundle] 
 */

CREATE PROCEDURE [TestResults].[SP_UpdateTestWithResultBundle]
	@testId bigint,
	@bundleId bigint
AS
	SET XACT_ABORT ON
	
 	SET NOCOUNT ON 		
 	
	BEGIN TRANSACTION

		UPDATE 
			[PROE].[OrderedTests]
		SET 
			[ResultBundleId] = @bundleId,
			[LastModified] = GETDATE()
		WHERE 
			[Id] = @testId
		
	COMMIT TRANSACTION
go
IF OBJECT_ID('TestResults.SP_UpdateTestWithResultBundle') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE TestResults.SP_UpdateTestWithResultBundle >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE TestResults.SP_UpdateTestWithResultBundle >>>'
go


/* 
 * PROCEDURE: [PROE].[SP_UpdateTestWorkflowStage] 
 */

CREATE PROCEDURE [PROE].[SP_UpdateTestWorkflowStage]
	@testId bigint,
	@wfStage tinyint
AS
	SET XACT_ABORT ON
	
 	SET NOCOUNT ON 		
 	
	BEGIN TRANSACTION

		UPDATE 
			[PROE].[OrderedTests]
		SET 
			[WorkflowStage] = @wfStage,
			[LastModified] = GETDATE()
		WHERE 
			[Id] = @testId
		
	COMMIT TRANSACTION
go
IF OBJECT_ID('PROE.SP_UpdateTestWorkflowStage') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE PROE.SP_UpdateTestWorkflowStage >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE PROE.SP_UpdateTestWorkflowStage >>>'
go


/* 
 * PROCEDURE: [Staff].[SP_UpdateUserLastLogin] 
 */

CREATE PROCEDURE [Staff].[SP_UpdateUserLastLogin]
	@UserId smallint
AS
	SET XACT_ABORT ON
	
	SET NOCOUNT ON 		
	
	BEGIN TRANSACTION

	UPDATE 
		[Staff].[Users]
	SET 
		[LastLogin] = CURRENT_TIMESTAMP
	WHERE 
		[Id] = @UserId
	
	COMMIT TRANSACTION
go
IF OBJECT_ID('Staff.SP_UpdateUserLastLogin') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Staff.SP_UpdateUserLastLogin >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Staff.SP_UpdateUserLastLogin >>>'
go


/* 
 * PROCEDURE: [Staff].[SP_UpdateUserPassHash] 
 */

CREATE PROCEDURE [Staff].[SP_UpdateUserPassHash]
	@userId smallint
	, @passHash varchar(32)
AS
	SET XACT_ABORT ON

	BEGIN TRANSACTION
		UPDATE Staff.Users
		SET
			PassHash = @passHash
			,LastModified = GETDATE()
		WHERE Id = @userId
	COMMIT TRANSACTION
go
IF OBJECT_ID('Staff.SP_UpdateUserPassHash') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Staff.SP_UpdateUserPassHash >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Staff.SP_UpdateUserPassHash >>>'
go


/* 
 * PROCEDURE: [Staff].[SP_UpsertConsultantSignature] 
 */

CREATE PROCEDURE [Staff].[SP_UpsertConsultantSignature]
	@userId smallint, 
	@sigImg varbinary(MAX), 
	@sigText varchar(MAX)
AS
	SET XACT_ABORT ON
	
	SET NOCOUNT ON 		
	
	BEGIN TRANSACTION
	
		DECLARE @rowId smallint
    
		-- First check if this settings record already exists
		SELECT 
			@rowId = UserId
		FROM
			Staff.Consultants
		WHERE
			UserId = @userId
      
		IF @rowId IS NULL
		BEGIN
      		INSERT INTO Staff.Consultants(UserId
                                  		, SignatureImage
                                  		, SignatureText
                                  		, LastModified)
      		VALUES
        		(@userId, @sigImg, @sigText, GETDATE())
		END
		ELSE
		BEGIN
      		UPDATE Staff.Consultants
      		SET
        		SignatureImage = @sigImg
        		, SignatureText = @sigText
        		, LastModified = GETDATE()
      		WHERE
        		UserId = @userId
		END
    
	COMMIT TRANSACTION
go
IF OBJECT_ID('Staff.SP_UpsertConsultantSignature') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE Staff.SP_UpsertConsultantSignature >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE Staff.SP_UpsertConsultantSignature >>>'
go


/* 
 * PROCEDURE: [APP_SYS].[SP_UpsertGlobalSettings] 
 */

CREATE PROCEDURE [APP_SYS].[SP_UpsertGlobalSettings]
	@key varchar(160), 
	@intVal int = -1, 
	@strVal varchar(max) = NULL
AS
	SET XACT_ABORT ON
	
	SET NOCOUNT ON 		
	
	BEGIN TRANSACTION
	
		DECLARE @rowId smallint
	
		-- First check if this settings record already exists
		SELECT 
			@rowId = [Id]
		FROM
			[APP_SYS].[GlobalSettings]
		WHERE
			[SettingsKey] = @key
			
		IF @rowId IS NULL
		BEGIN
			-- Insert
			INSERT INTO	
				[APP_SYS].[GlobalSettings](
					[SettingsKey], 
					[IntValue], 
					[StrValue]
					)
				VALUES (
					@key, 
					@intVal, 
					@strVal
					)
		END
		ELSE
		BEGIN
			-- Update		
			UPDATE
				[APP_SYS].[GlobalSettings]
			SET
				[IntValue] = @intVal,
				[StrValue] = @strVal
			WHERE
				[Id] = @rowId
		END
	
	COMMIT TRANSACTION
go
IF OBJECT_ID('APP_SYS.SP_UpsertGlobalSettings') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE APP_SYS.SP_UpsertGlobalSettings >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE APP_SYS.SP_UpsertGlobalSettings >>>'
go


/* 
 * PROCEDURE: [TestResults].[SP_UpsertResultBundleToRecentUpdatesList] 
 */

CREATE PROCEDURE [TestResults].[SP_UpsertResultBundleToRecentUpdatesList]
	@bundleId bigint, 
	@invoiceId bigint, 
	@wfStage tinyint,
	@sortPriority tinyint
AS
	SET XACT_ABORT ON
	
	SET NOCOUNT ON 		
	
	BEGIN TRANSACTION
	
		DECLARE @rowId bigint
	
		-- First check if this result bundle already exists
		SELECT 
			@rowId = [Id]
		FROM
			[TestResults].[RecentlyUpdatedResultBundles]
		WHERE
			[ResultBundleId] = @bundleId
			
		IF @rowId IS NULL
		BEGIN
			-- Insert
			INSERT INTO	
				[TestResults].[RecentlyUpdatedResultBundles](
					[ResultBundleId], 
					[InvoiceId], 
					[LastUpdated], 
					[WorkflowStage],
					[SortPriority]
					)
				VALUES (
					@bundleId, 
					@invoiceId, 
					GETDATE(), 
					@wfStage,
					@sortPriority
					)
		END
		ELSE
		BEGIN
			-- Update		
			UPDATE
				[TestResults].[RecentlyUpdatedResultBundles]
			SET
				[LastUpdated] = GETDATE(),
				[WorkflowStage] = @wfStage
			WHERE
				[Id] = @rowId
		END
	
	COMMIT TRANSACTION
go
IF OBJECT_ID('TestResults.SP_UpsertResultBundleToRecentUpdatesList') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE TestResults.SP_UpsertResultBundleToRecentUpdatesList >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE TestResults.SP_UpsertResultBundleToRecentUpdatesList >>>'
go


/* 
 * PROCEDURE: [APP_SYS].[SP_UpsertWorkstationRegistry] 
 */

CREATE PROCEDURE [APP_SYS].[SP_UpsertWorkstationRegistry]
  @ipAddr   INT,
  @macAddr  VARCHAR(24),
  @appVer   VARCHAR(24),
  @user     VARCHAR(24),
  @osVer    VARCHAR(MAX),
  @remarks  TEXT
AS
  SET  XACT_ABORT ON

  SET  NOCOUNT ON

  BEGIN TRANSACTION

  DECLARE @rowId   BIGINT

  -- First check if this record already exists
  SELECT
  @rowId = Id
  FROM
  [APP_SYS].[WorkstationsRegistry]
  WHERE
  IPAddress = @ipAddr AND
  MACAddress = @macAddr

  IF @rowId IS NULL
    BEGIN
      INSERT INTO
        APP_SYS.WorkstationsRegistry(
          MACAddress,
          IPAddress,
          AppVersion,
          CurrentUser,
          OSVersion,
          Remarks,
          LastSeen)
      VALUES
        (
          @macAddr,
          @ipAddr,
          @appVer,
          @user,
          @osVer,
          @remarks,
          GETDATE())
    END
  ELSE
    BEGIN
      UPDATE
        APP_SYS.WorkstationsRegistry
      SET
        AppVersion  = @appVer,
        CurrentUser = @user,
        OSVersion   = @osVer,
        Remarks     = @remarks,
        LastSeen    = GETDATE()
      WHERE
        Id = @rowId
    END

  COMMIT TRANSACTION
go
IF OBJECT_ID('APP_SYS.SP_UpsertWorkstationRegistry') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE APP_SYS.SP_UpsertWorkstationRegistry >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE APP_SYS.SP_UpsertWorkstationRegistry >>>'
go


/* 
 * PROCEDURE: [RBAC].[SP_UserHasPermCode] 
 */

CREATE PROCEDURE [RBAC].[SP_UserHasPermCode]
	@userId smallint
	, @permCode varchar(32)
AS
	SET NOCOUNT ON
	
	SELECT COUNT(*)
	FROM
		Staff.Users u
		INNER JOIN RBAC.Roles r
			ON u.RoleId = r.Id
		INNER JOIN RBAC.RolePermissions rp
			ON rp.RoleId = r.Id
		INNER JOIN RBAC.permissions p
			ON rp.PermissionId = p.Id
	WHERE
		u.Id = @userId
		AND p.PermCode = @permCode
go
IF OBJECT_ID('RBAC.SP_UserHasPermCode') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE RBAC.SP_UserHasPermCode >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE RBAC.SP_UserHasPermCode >>>'
go


/* 
 * PROCEDURE: [TestResults].[SP_VerifyResultBundleWorkflowStageMatch] 
 */

CREATE PROCEDURE [TestResults].[SP_VerifyResultBundleWorkflowStageMatch]
	@bundleId bigint, 
	@wfStage tinyint
AS
	SET NOCOUNT ON
	
	DECLARE @result BIT
	DECLARE @wfCurrent TINYINT
	
	SET @result = 0
	
	SELECT TOP 1 
		@wfCurrent = [WorkflowStage]
	FROM
		[TestResults].[ResultBundles]
	WHERE
		[Id] = @bundleId
	
	IF @wfStage = @wfCurrent
		SET @result = 1
	
	SELECT @result
go
IF OBJECT_ID('TestResults.SP_VerifyResultBundleWorkflowStageMatch') IS NOT NULL
    PRINT '<<< CREATED PROCEDURE TestResults.SP_VerifyResultBundleWorkflowStageMatch >>>'
ELSE
    PRINT '<<< FAILED CREATING PROCEDURE TestResults.SP_VerifyResultBundleWorkflowStageMatch >>>'
go


/* 
 * TRIGGER: [TRG_AU_AppReportTemplates] 
 */

CREATE TRIGGER [TRG_AU_AppReportTemplates]
	ON [Catalog].[AppReportTemplates]
	FOR UPDATE 
AS 
	BEGIN
   		UPDATE 
   			[Catalog].[AppReportTemplates]
   		SET 
   			[LastModified] = GETDATE()
   		FROM 
   			INSERTED ins
   		WHERE 
   			ins.[Id] = [Catalog].[AppReportTemplates].[Id]
	END
go
IF OBJECT_ID('TRG_AU_AppReportTemplates') IS NOT NULL
    PRINT '<<< CREATED TRIGGER TRG_AU_AppReportTemplates >>>'
ELSE
    PRINT '<<< FAILED CREATING TRIGGER TRG_AU_AppReportTemplates >>>'
go


/* 
 * TRIGGER: [TRG_AU_Consultants] 
 */

CREATE TRIGGER [TRG_AU_Consultants]
	ON [Staff].[Consultants]
	FOR UPDATE 
AS 
	BEGIN
   		UPDATE 
   			[Staff].[Consultants]
   		SET 
   			[LastModified] = GETDATE()
   		FROM 
   			INSERTED ins
   		WHERE 
   			ins.[UserId] = [Staff].[Consultants].[UserId]
	END
go
IF OBJECT_ID('TRG_AU_Consultants') IS NOT NULL
    PRINT '<<< CREATED TRIGGER TRG_AU_Consultants >>>'
ELSE
    PRINT '<<< FAILED CREATING TRIGGER TRG_AU_Consultants >>>'
go


/* 
 * TRIGGER: [TRG_AU_LabTests] 
 */

CREATE TRIGGER [TRG_AU_LabTests]
	ON [Catalog].[LabTests]
	FOR UPDATE 
AS 
	BEGIN
   		UPDATE 
   			[Catalog].[LabTests]
   		SET 
   			[LastModified] = GETDATE()
   		FROM 
   			INSERTED ins
   		WHERE 
   			ins.[Id] = [Catalog].[LabTests].[Id]
	END
go
IF OBJECT_ID('TRG_AU_LabTests') IS NOT NULL
    PRINT '<<< CREATED TRIGGER TRG_AU_LabTests >>>'
ELSE
    PRINT '<<< FAILED CREATING TRIGGER TRG_AU_LabTests >>>'
go


/* 
 * TRIGGER: [TRG_AU_OrderedTests] 
 */

CREATE TRIGGER [TRG_AU_OrderedTests]
	ON [PROE].[OrderedTests]
	FOR UPDATE 
AS 
	BEGIN
   		UPDATE 
   			[PROE].[OrderedTests]
   		SET 
   			[LastModified] = GETDATE()
   		FROM 
   			INSERTED ins
   		WHERE 
   			ins.[Id] = [PROE].[OrderedTests].[Id]
	END
go
IF OBJECT_ID('TRG_AU_OrderedTests') IS NOT NULL
    PRINT '<<< CREATED TRIGGER TRG_AU_OrderedTests >>>'
ELSE
    PRINT '<<< FAILED CREATING TRIGGER TRG_AU_OrderedTests >>>'
go


/* 
 * TRIGGER: [TRG_AU_PatientLabOrders] 
 */

CREATE TRIGGER [TRG_AU_PatientLabOrders]
	ON [PROE].[PatientLabOrders]
	FOR UPDATE 
AS 
	BEGIN
   		UPDATE 
   			[PROE].[PatientLabOrders]
   		SET 
   			[LastModified] = GETDATE()
   		FROM 
   			INSERTED ins
   		WHERE 
   			ins.[InvoiceId] = [PROE].[PatientLabOrders].[InvoiceId]
	END
	
	
go
IF OBJECT_ID('TRG_AU_PatientLabOrders') IS NOT NULL
    PRINT '<<< CREATED TRIGGER TRG_AU_PatientLabOrders >>>'
ELSE
    PRINT '<<< FAILED CREATING TRIGGER TRG_AU_PatientLabOrders >>>'
go


/* 
 * TRIGGER: [TRG_AU_ResultBundles] 
 */

CREATE TRIGGER [TRG_AU_ResultBundles]
	ON [TestResults].[ResultBundles]
	FOR UPDATE 
AS 
	BEGIN
   		UPDATE 
   			[TestResults].[ResultBundles]
   		SET 
   			[LastUpdated] = GETDATE()
   		FROM 
   			INSERTED ins
   		WHERE 
   			ins.[Id] = [TestResults].[ResultBundles].[Id]
	END
go
IF OBJECT_ID('TRG_AU_ResultBundles') IS NOT NULL
    PRINT '<<< CREATED TRIGGER TRG_AU_ResultBundles >>>'
ELSE
    PRINT '<<< FAILED CREATING TRIGGER TRG_AU_ResultBundles >>>'
go


/* 
 * TRIGGER: [TRG_AU_SurchargeLevels] 
 */

CREATE TRIGGER [TRG_AU_SurchargeLevels]
	ON [Catalog].[SurchargeLevels]
	FOR UPDATE 
AS 
	BEGIN
   		UPDATE 
   			[Catalog].[SurchargeLevels]
   		SET 
   			[LastModified] = GETDATE()
   		FROM 
   			INSERTED ins
   		WHERE 
   			ins.[Id] = [Catalog].[SurchargeLevels].[Id]
	END
go
IF OBJECT_ID('TRG_AU_SurchargeLevels') IS NOT NULL
    PRINT '<<< CREATED TRIGGER TRG_AU_SurchargeLevels >>>'
ELSE
    PRINT '<<< FAILED CREATING TRIGGER TRG_AU_SurchargeLevels >>>'
go


/* 
 * TRIGGER: [TRG_AU_TaxLevels] 
 */

CREATE TRIGGER [TRG_AU_TaxLevels]
	ON [Catalog].[TaxLevels]
	FOR UPDATE 
AS 
	BEGIN
   		UPDATE 
   			[Catalog].[TaxLevels]
   		SET 
   			[LastModified] = GETDATE()
   		FROM 
   			INSERTED ins
   		WHERE 
   			ins.[Id] = [Catalog].[TaxLevels].[Id]
	END
go
IF OBJECT_ID('TRG_AU_TaxLevels') IS NOT NULL
    PRINT '<<< CREATED TRIGGER TRG_AU_TaxLevels >>>'
ELSE
    PRINT '<<< FAILED CREATING TRIGGER TRG_AU_TaxLevels >>>'
go


/* 
 * TRIGGER: [TRG_AU_Users] 
 */

CREATE TRIGGER [TRG_AU_Users]
	ON [Staff].[Users]
	FOR UPDATE 
AS 
	BEGIN
   		UPDATE 
   			[Staff].[Users]
   		SET 
   			[LastModified] = GETDATE()
   		FROM 
   			INSERTED ins
   		WHERE 
   			ins.[Id] = [Staff].[Users].[Id]
	END
	
	
go
IF OBJECT_ID('TRG_AU_Users') IS NOT NULL
    PRINT '<<< CREATED TRIGGER TRG_AU_Users >>>'
ELSE
    PRINT '<<< FAILED CREATING TRIGGER TRG_AU_Users >>>'
go


/* 
 * TRIGGER: [TRG_AU_WorkShifts] 
 */

CREATE TRIGGER [TRG_AU_WorkShifts]
	ON [Finances].[WorkShifts]
	FOR UPDATE 
AS 
	BEGIN
   		UPDATE 
   			[Finances].[WorkShifts]
   		SET 
   			[LastUpdated] = GETDATE()
   		FROM 
   			INSERTED ins
   		WHERE 
   			ins.[Id] = [Finances].[WorkShifts].[Id]
	END
	
	
go
IF OBJECT_ID('TRG_AU_WorkShifts') IS NOT NULL
    PRINT '<<< CREATED TRIGGER TRG_AU_WorkShifts >>>'
ELSE
    PRINT '<<< FAILED CREATING TRIGGER TRG_AU_WorkShifts >>>'
go


