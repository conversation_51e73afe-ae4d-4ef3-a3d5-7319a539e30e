﻿/*
Created: 2021-09-21
Modified: 2024-10-17
Model: LabMaestro Enhancements
Database: MS SQL Server 2019
*/


-- Create schemas section -------------------------------------------------

CREATE SCHEMA [ConsultancyBilling]
go

CREATE SCHEMA [OuterOMS]
go

CREATE SCHEMA [CRM]
go

CREATE SCHEMA [ServiceDesk]
go

CREATE SCHEMA [Subscriptions]
go

CREATE SCHEMA [EMR]
go

-- Create tables section -------------------------------------------------

-- Table OuterOMS.AssociateOrganizations

CREATE TABLE [OuterOMS].[AssociateOrganizations]
(
 [Id] Smallint IDENTITY(101,1) NOT NULL,
 [IsActive] Bit DEFAULT 0 NOT NULL,
 [Name] Varchar(120) NOT NULL,
 [Address] Text NULL,
 [NumOrders] Bigint NULL,
 [CLV] Money DEFAULT 0.00 NOT NULL,
 [ContactName] Varchar(64) NULL,
 [Phone] Varchar(20) NULL,
 [Email] Varchar(32) NULL,
 [Remarks] Text NULL,
 [MarketingTeamId] Smallint NULL,
 [CreatedOn] Smalldatetime DEFAULT GETDATE() NOT NULL,
 [LastUpdated] Smalldatetime NULL,
 [RowGuid] Uniqueidentifier DEFAULT NEWSEQUENTIALID() ROWGUIDCOL NOT NULL
)
ON [FG_Static]
go

-- Create indexes for table OuterOMS.AssociateOrganizations

CREATE INDEX [IX_AssociateOrganizations] ON [OuterOMS].[AssociateOrganizations] ([Name],[IsActive])
 ON [FG_Static]
go

-- Add keys for table OuterOMS.AssociateOrganizations

ALTER TABLE [OuterOMS].[AssociateOrganizations] ADD CONSTRAINT [PK_AssociateOrganizations] PRIMARY KEY ([Id])
 ON [FG_Static]
go

ALTER TABLE [OuterOMS].[AssociateOrganizations] ADD CONSTRAINT [AK_AssociateOrganizations_RowGuid] UNIQUE ([RowGuid])
 ON [FG_Static]
go

-- Table OuterOMS.AssociateLabUsers

CREATE TABLE [OuterOMS].[AssociateLabUsers]
(
 [Id] Smallint IDENTITY(1,1) NOT NULL,
 [OrganizationId] Smallint NOT NULL,
 [Name] Varchar(64) NOT NULL,
 [IsActive] Bit DEFAULT 0 NOT NULL,
 [Login] Varchar(20) NOT NULL,
 [PassHash] Varchar(64) NULL,
 [Phone] Varchar(20) NULL,
 [Email] Varchar(32) NULL,
 [Department] Varchar(32) NULL,
 [Designation] Varchar(32) NULL,
 [LastSeenAt] Smalldatetime NULL,
 [CreatedOn] Smalldatetime DEFAULT GETDATE() NOT NULL,
 [LastUpdated] Smalldatetime NULL,
 [RowGuid] Uniqueidentifier DEFAULT NEWSEQUENTIALID() ROWGUIDCOL NOT NULL
)
ON [FG_Static]
go

-- Create indexes for table OuterOMS.AssociateLabUsers

CREATE INDEX [IX_AssociateLabUsers_Organization] ON [OuterOMS].[AssociateLabUsers] ([OrganizationId])
 ON [FG_Static]
go

CREATE INDEX [IX_AssociateLabUsers_Web] ON [OuterOMS].[AssociateLabUsers] ([Login],[IsActive])
 ON [FG_Static]
go

-- Add keys for table OuterOMS.AssociateLabUsers

ALTER TABLE [OuterOMS].[AssociateLabUsers] ADD CONSTRAINT [PK_AssociateLabUsers] PRIMARY KEY ([Id])
 ON [FG_Static]
go

ALTER TABLE [OuterOMS].[AssociateLabUsers] ADD CONSTRAINT [AK_AssociateLabUsers] UNIQUE ([Login])
 ON [FG_Static]
go

ALTER TABLE [OuterOMS].[AssociateLabUsers] ADD CONSTRAINT [AK_AssociateLabUsers_RowGuid] UNIQUE ([RowGuid])
 ON [FG_Static]
go

-- Table OuterOMS.AssociateAuditTrails

CREATE TABLE [OuterOMS].[AssociateAuditTrails]
(
 [Id] Bigint IDENTITY(1,1) NOT NULL,
 [EventTime] Smalldatetime DEFAULT GETDATE() NOT NULL,
 [EventType] Smallint NOT NULL,
 [OrganizationId] Smallint NULL,
 [OrganizationUserId] Smallint NULL,
 [InvoiceId] Bigint NULL,
 [ResultBundleId] Bigint NULL,
 [LocalUserId] Smallint NULL,
 [IpAddress] Int DEFAULT 0 NOT NULL,
 [Remarks] Text NULL
)
ON [PS_OLTP_Date_13_33]([EventTime])
go

-- Create indexes for table OuterOMS.AssociateAuditTrails

CREATE INDEX [IX_AssociateAuditTrails_Organization] ON [OuterOMS].[AssociateAuditTrails] ([OrganizationId],[EventTime])
go

CREATE INDEX [IX_AssociateAuditTrails_Order] ON [OuterOMS].[AssociateAuditTrails] ([InvoiceId],[EventTime])
go

CREATE INDEX [IX_AssociateAuditTrails_Organization_User] ON [OuterOMS].[AssociateAuditTrails] ([OrganizationUserId],[EventTime])
go

CREATE INDEX [IX_AssociateAuditTrails] ON [OuterOMS].[AssociateAuditTrails] ([EventTime])
go

-- Add keys for table OuterOMS.AssociateAuditTrails

ALTER TABLE [OuterOMS].[AssociateAuditTrails] ADD CONSTRAINT [PK_AssociateAuditTrails] PRIMARY KEY ([Id])
go

-- Table Marketing.Affiliates

CREATE TABLE [Marketing].[Affiliates]
(
 [Id] Smallint IDENTITY(1001,1) NOT NULL,
 [IsActive] Bit DEFAULT 0 NOT NULL,
 [UPIN] Varchar(40) NOT NULL,
 [Name] Varchar(120) NOT NULL,
 [IdentifyingTag] Varchar(120) NULL,
 [MobilePhone] Varchar(32) NULL,
 [Email] Varchar(40) NULL,
 [WebAccess] Bit DEFAULT 0 NOT NULL,
 [Login] Varchar(20) NOT NULL,
 [PassHash] Varchar(64) NULL,
 [CLV] Money DEFAULT 0.00 NOT NULL,
 [NumOrders] Int DEFAULT 0 NOT NULL,
 [MarketingExecId] Smallint NULL,
 [CreatedOn] Smalldatetime DEFAULT GETDATE() NOT NULL,
 [LastUpdated] Smalldatetime DEFAULT GETDATE() NULL,
 [RowGuid] Uniqueidentifier DEFAULT NEWSEQUENTIALID() ROWGUIDCOL NOT NULL
)
ON [FG_Static]
go

-- Create indexes for table Marketing.Affiliates

CREATE UNIQUE INDEX [AK_Affiliates_UPIN] ON [Marketing].[Affiliates] ([UPIN])
 ON [FG_Static]
go

CREATE UNIQUE INDEX [AK_Affiliates_Web] ON [Marketing].[Affiliates] ([Login])
 ON [FG_Static]
go

CREATE INDEX [IX_Affiliates] ON [Marketing].[Affiliates] ([IsActive],[Name])
 ON [FG_Static]
go

CREATE INDEX [IX_Affiliates_Web] ON [Marketing].[Affiliates] ([IsActive],[Login])
 ON [FG_Static]
go

CREATE INDEX [IX_Affiliates_MarketingExec] ON [Marketing].[Affiliates] ([MarketingExecId])
 ON [FG_Static]
go

-- Add keys for table Marketing.Affiliates

ALTER TABLE [Marketing].[Affiliates] ADD CONSTRAINT [PK_Affiliates] PRIMARY KEY ([Id])
 ON [FG_Static]
go

ALTER TABLE [Marketing].[Affiliates] ADD CONSTRAINT [AK_Affiliates_RowGuid] UNIQUE ([RowGuid])
 ON [FG_Static]
go

-- Table Marketing.CorporateClientGroups

CREATE TABLE [Marketing].[CorporateClientGroups]
(
 [Id] Smallint IDENTITY(101,1) NOT NULL,
 [IsActive] Bit DEFAULT 0 NOT NULL,
 [Name] Varchar(120) NOT NULL,
 [Remarks] Text NULL,
 [CreatedOn] Smalldatetime DEFAULT GETDATE() NOT NULL,
 [LastUpdated] Smalldatetime NULL,
 [RowGuid] Uniqueidentifier DEFAULT NEWSEQUENTIALID() ROWGUIDCOL NOT NULL
)
ON [FG_Static]
go

-- Add keys for table Marketing.CorporateClientGroups

ALTER TABLE [Marketing].[CorporateClientGroups] ADD CONSTRAINT [PK_CorporateClientGroups] PRIMARY KEY ([Id])
 ON [FG_Static]
go

ALTER TABLE [Marketing].[CorporateClientGroups] ADD CONSTRAINT [AK_CorporateClientGroups] UNIQUE ([Name])
 ON [FG_Static]
go

ALTER TABLE [Marketing].[CorporateClientGroups] ADD CONSTRAINT [AK_CorporateClientGroups_RowGuid] UNIQUE ([RowGuid])
 ON [FG_Static]
go

-- Table Marketing.CorporateClients

CREATE TABLE [Marketing].[CorporateClients]
(
 [Id] Smallint IDENTITY(1001,1) NOT NULL,
 [IsActive] Bit DEFAULT 0 NOT NULL,
 [UID] Varchar(40) NOT NULL,
 [Name] Varchar(120) NOT NULL,
 [Address] Text NULL,
 [VatNumber] Varchar(40) NULL,
 [ContactPerson] Varchar(64) NULL,
 [Phone] Varchar(20) NULL,
 [Email] Varchar(32) NULL,
 [WebAccess] Bit DEFAULT 0 NOT NULL,
 [Login] Varchar(20) NOT NULL,
 [PassHash] Varchar(64) NULL,
 [ClientGroupId] Smallint NULL,
 [MarketingExecId] Smallint NULL,
 [DiscountProfileId] Smallint NULL,
 [CLV] Money DEFAULT 0.00 NOT NULL,
 [NumOrders] Int DEFAULT 0 NOT NULL,
 [AgreementStartDate] Date NULL,
 [AgreementExpiryDate] Date NULL,
 [CreatedOn] Smalldatetime DEFAULT GETDATE() NOT NULL,
 [LastUpdated] Smalldatetime DEFAULT GETDATE() NULL,
 [RowGuid] Uniqueidentifier DEFAULT NEWSEQUENTIALID() ROWGUIDCOL NOT NULL
)
ON [FG_Static]
go

-- Create indexes for table Marketing.CorporateClients

CREATE UNIQUE INDEX [AK_CorporateClients_UID] ON [Marketing].[CorporateClients] ([UID])
 ON [FG_Static]
go

CREATE UNIQUE INDEX [AK_CorporateClients_Web] ON [Marketing].[CorporateClients] ([Login])
 ON [FG_Static]
go

CREATE INDEX [IX_CorporateClients_MarketingExec] ON [Marketing].[CorporateClients] ([MarketingExecId])
 ON [FG_Static]
go

CREATE UNIQUE INDEX [AK_CorporateClients_Name] ON [Marketing].[CorporateClients] ([Name])
 ON [FG_Static]
go

CREATE INDEX [IX_CorporateClients_Group] ON [Marketing].[CorporateClients] ([ClientGroupId])
 ON [FG_Static]
go

CREATE INDEX [IX_CorporateClients_Web] ON [Marketing].[CorporateClients] ([Login],[IsActive],[WebAccess])
 ON [FG_Static]
go

CREATE INDEX [IX_CorporateClients_Discount] ON [Marketing].[CorporateClients] ([DiscountProfileId])
 ON [FG_Static]
go

-- Add keys for table Marketing.CorporateClients

ALTER TABLE [Marketing].[CorporateClients] ADD CONSTRAINT [PK_CorporateClients] PRIMARY KEY ([Id])
 ON [FG_Static]
go

ALTER TABLE [Marketing].[CorporateClients] ADD CONSTRAINT [AK_CorporateClients_RowGuid] UNIQUE ([RowGuid])
 ON [FG_Static]
go

-- Table Finances.CorporateBillSettlements

CREATE TABLE [Finances].[CorporateBillSettlements]
(
 [Id] Int IDENTITY(1001,1) NOT NULL,
 [CorporateClientId] Smallint NOT NULL,
 [AuthorizingUserId] Smallint NULL,
 [BillingPeriodStart] Date NOT NULL,
 [BillingPeriodEnd] Date NOT NULL,
 [PaymentMethod] Tinyint DEFAULT 0 NOT NULL,
 [GrossPayable] Money DEFAULT 0.00 NOT NULL,
 [Deductions] Smallmoney DEFAULT 0.00 NOT NULL,
 [DiscountAmount] Smallmoney DEFAULT 0.00 NOT NULL,
 [NetPayable] Money DEFAULT 0.00 NOT NULL,
 [PaidAmount] Money DEFAULT 0.00 NOT NULL,
 [DueAmount] Money DEFAULT 0.00 NOT NULL,
 [OriginatorName] Varchar(120) NULL,
 [OriginatorReference] Varchar(240) NULL,
 [OriginatorBank] Varchar(120) NULL,
 [PaymentDate] Date NULL,
 [PayeeBank] Varchar(120) NULL,
 [PaymentReference] Varchar(120) NULL,
 [SettlementStatus] Tinyint DEFAULT 0 NOT NULL,
 [SettledOn] Smalldatetime NULL,
 [Remarks] Text NULL,
 [CreatedOn] Smalldatetime DEFAULT GETDATE() NOT NULL,
 [LastUpdated] Smalldatetime NULL,
 [RowGuid] Uniqueidentifier DEFAULT NEWSEQUENTIALID() ROWGUIDCOL NOT NULL
)
go

-- Create indexes for table Finances.CorporateBillSettlements

CREATE UNIQUE INDEX [AK_CorporateBillSettlements_Corp_Period] ON [Finances].[CorporateBillSettlements] ([CorporateClientId],[BillingPeriodEnd],[BillingPeriodStart],[PaymentMethod])
go

CREATE INDEX [IX_CorporateBillSettlements] ON [Finances].[CorporateBillSettlements] ([CorporateClientId])
go

CREATE INDEX [IX_CorporateBillSettlements_Receiver] ON [Finances].[CorporateBillSettlements] ([AuthorizingUserId])
go

-- Add keys for table Finances.CorporateBillSettlements

ALTER TABLE [Finances].[CorporateBillSettlements] ADD CONSTRAINT [PK_CorporateBillSettlements] PRIMARY KEY ([Id])
go

ALTER TABLE [Finances].[CorporateBillSettlements] ADD CONSTRAINT [AK_CorporateBillSettlements_RowGuid] UNIQUE ([RowGuid])
go

-- Table Marketing.CorporateDiscountProfiles

CREATE TABLE [Marketing].[CorporateDiscountProfiles]
(
 [Id] Smallint IDENTITY(1,1) NOT NULL,
 [IsActive] Bit DEFAULT 0 NOT NULL,
 [Name] Varchar(64) NOT NULL,
 [Remarks] Text NULL,
 [CreatedOn] Smalldatetime DEFAULT GETDATE() NOT NULL,
 [LastUpdated] Smalldatetime NULL,
 [RowGuid] Uniqueidentifier ROWGUIDCOL NOT NULL
)
ON [FG_Static]
go

-- Create indexes for table Marketing.CorporateDiscountProfiles

CREATE UNIQUE INDEX [AK_CorporateDiscountProfiles] ON [Marketing].[CorporateDiscountProfiles] ([Name])
 ON [FG_Static]
go

CREATE INDEX [IX_CorporateDiscountProfiles] ON [Marketing].[CorporateDiscountProfiles] ([IsActive],[Name])
 ON [FG_Static]
go

-- Add keys for table Marketing.CorporateDiscountProfiles

ALTER TABLE [Marketing].[CorporateDiscountProfiles] ADD CONSTRAINT [PK_CorporateDiscountProfiles] PRIMARY KEY ([Id])
 ON [FG_Static]
go

ALTER TABLE [Marketing].[CorporateDiscountProfiles] ADD CONSTRAINT [AK_CorporateDiscountProfiles_RowGuid] UNIQUE ([RowGuid])
go

-- Table Marketing.AssociateDiscountProfiles

CREATE TABLE [Marketing].[AssociateDiscountProfiles]
(
 [Id] Smallint IDENTITY(1,1) NOT NULL,
 [IsActive] Bit DEFAULT 0 NOT NULL,
 [Name] Varchar(64) NOT NULL,
 [Remarks] Text NULL,
 [CreatedOn] Smalldatetime DEFAULT GETDATE() NOT NULL,
 [LastUpdated] Smalldatetime NULL,
 [RowGuid] Uniqueidentifier ROWGUIDCOL NOT NULL
)
ON [FG_Static]
go

-- Create indexes for table Marketing.AssociateDiscountProfiles

CREATE UNIQUE INDEX [AK_AssociateDiscountProfiles] ON [Marketing].[AssociateDiscountProfiles] ([Name])
 ON [FG_Static]
go

CREATE INDEX [IX_AssociateDiscountProfiles] ON [Marketing].[AssociateDiscountProfiles] ([IsActive],[Name])
 ON [FG_Static]
go

-- Add keys for table Marketing.AssociateDiscountProfiles

ALTER TABLE [Marketing].[AssociateDiscountProfiles] ADD CONSTRAINT [PK_AssociateDiscountProfiles] PRIMARY KEY ([Id])
 ON [FG_Static]
go

ALTER TABLE [Marketing].[AssociateDiscountProfiles] ADD CONSTRAINT [AK_AssociateDiscountProfiles_RowGuid] UNIQUE ([RowGuid])
go

-- Table Marketing.CorporateDiscountDetails

CREATE TABLE [Marketing].[CorporateDiscountDetails]
(
 [Id] Bigint IDENTITY(1,1) NOT NULL,
 [ProfileId] Smallint NOT NULL,
 [LabId] Smallint NULL,
 [LabTestId] Smallint NULL,
 [Mode] Tinyint DEFAULT 0 NOT NULL,
 [FlatAmount] Smallmoney DEFAULT 0.00 NOT NULL,
 [Percentage] Float DEFAULT 0 NOT NULL,
 [MaxAmount] Smallmoney DEFAULT 0.00 NOT NULL,
 [CreatedOn] Smalldatetime DEFAULT GETDATE() NOT NULL,
 [LastUpdated] Smalldatetime NULL,
 [RowGuid] Uniqueidentifier DEFAULT NEWSEQUENTIALID() ROWGUIDCOL NOT NULL
)
ON [FG_Static]
go

-- Create indexes for table Marketing.CorporateDiscountDetails

CREATE INDEX [IX_CorporateDiscountDetails_Lab] ON [Marketing].[CorporateDiscountDetails] ([LabId])
 ON [FG_Static]
go

CREATE INDEX [IX_CorporateDiscountDetails_Profile] ON [Marketing].[CorporateDiscountDetails] ([ProfileId])
 ON [FG_Static]
go

CREATE INDEX [IX_CorporateDiscountDetails_LabTest] ON [Marketing].[CorporateDiscountDetails] ([LabTestId])
 ON [FG_Static]
go

CREATE INDEX [IX_CorporateDiscountDetails_Lab_Profile] ON [Marketing].[CorporateDiscountDetails] ([ProfileId],[LabId])
 ON [FG_Static]
go

CREATE INDEX [IX_CorporateDiscountDetails_LabTest_Profile] ON [Marketing].[CorporateDiscountDetails] ([LabTestId],[ProfileId])
 ON [FG_Static]
go

-- Add keys for table Marketing.CorporateDiscountDetails

ALTER TABLE [Marketing].[CorporateDiscountDetails] ADD CONSTRAINT [PK_CorporateDiscountDetails] PRIMARY KEY ([Id])
 ON [FG_Static]
go

ALTER TABLE [Marketing].[CorporateDiscountDetails] ADD CONSTRAINT [AK_CorporateDiscountDetails_RowGuid] UNIQUE ([RowGuid])
 ON [FG_Static]
go

-- Table Marketing.AssociateDiscountDetails

CREATE TABLE [Marketing].[AssociateDiscountDetails]
(
 [Id] Bigint IDENTITY(1,1) NOT NULL,
 [ProfileId] Smallint NOT NULL,
 [LabId] Smallint NULL,
 [LabTestId] Smallint NULL,
 [Mode] Tinyint DEFAULT 0 NOT NULL,
 [FlatAmount] Smallmoney DEFAULT 0.00 NOT NULL,
 [Percentage] Float DEFAULT 0 NOT NULL,
 [MaxAmount] Smallmoney DEFAULT 0.00 NOT NULL,
 [CreatedOn] Smalldatetime DEFAULT GETDATE() NOT NULL,
 [LastUpdated] Smalldatetime NULL,
 [RowGuid] Uniqueidentifier DEFAULT NEWSEQUENTIALID() ROWGUIDCOL NOT NULL
)
ON [FG_Static]
go

-- Create indexes for table Marketing.AssociateDiscountDetails

CREATE INDEX [IX_AssociateDiscountDetails_Lab] ON [Marketing].[AssociateDiscountDetails] ([LabId])
 ON [FG_Static]
go

CREATE INDEX [IX_AssociateDiscountDetails_Profile] ON [Marketing].[AssociateDiscountDetails] ([ProfileId])
 ON [FG_Static]
go

CREATE INDEX [IX_AssociateDiscountDetails_LabTest] ON [Marketing].[AssociateDiscountDetails] ([LabTestId])
 ON [FG_Static]
go

CREATE INDEX [IX_AssociateDiscountDetails_Lab_Profile] ON [Marketing].[AssociateDiscountDetails] ([ProfileId],[LabId])
 ON [FG_Static]
go

CREATE INDEX [IX_AssociateDiscountDetails_LabTest_Profile] ON [Marketing].[AssociateDiscountDetails] ([LabTestId],[ProfileId])
 ON [FG_Static]
go

-- Add keys for table Marketing.AssociateDiscountDetails

ALTER TABLE [Marketing].[AssociateDiscountDetails] ADD CONSTRAINT [PK_AssociateDiscountDetails] PRIMARY KEY ([Id])
 ON [FG_Static]
go

ALTER TABLE [Marketing].[AssociateDiscountDetails] ADD CONSTRAINT [AK_AssociateDiscountDetails_RowGuid] UNIQUE ([RowGuid])
 ON [FG_Static]
go

-- Table CRM.Customers

CREATE TABLE [CRM].[Customers]
(
 [Id] Bigint IDENTITY(1001,1) NOT NULL,
 [IsActive] Bit DEFAULT 0 NOT NULL,
 [UPIN] Varchar(32) NOT NULL,
 [Title] Varchar(20) NULL,
 [FirstName] Varchar(120) NOT NULL,
 [LastName] Varchar(120) NULL,
 [CompanyName] Varchar(120) NULL,
 [Sex] Tinyint DEFAULT 0 NOT NULL,
 [Age] Varchar(8) NULL,
 [DoB] Date NULL,
 [Phone] Varchar(20) NULL,
 [Phone2] Varchar(20) NULL,
 [Email] Varchar(32) NULL,
 [Occupation] Varchar(64) NULL,
 [ContactMethod] Tinyint DEFAULT 0 NOT NULL,
 [WebAccess] Bit DEFAULT 0 NOT NULL,
 [Login] Varchar(32) NOT NULL,
 [PassHash] Varchar(64) NULL,
 [MaritalStatus] Tinyint DEFAULT 0 NOT NULL,
 [BloodGroup] Varchar(8) NULL,
 [Height] Varchar(8) NULL,
 [Weight] Varchar(8) NULL,
 [FathersName] Varchar(120) NULL,
 [MothersName] Varchar(120) NULL,
 [SpouseName] Varchar(120) NULL,
 [SpouseDoB] Date NULL,
 [NumChildren] Tinyint DEFAULT 0 NOT NULL,
 [PassportNumber] Varchar(32) NULL,
 [PassportExpiryDate] Date NULL,
 [DriversLicense] Varchar(40) NULL,
 [NationalIdNumber] Varchar(64) NULL,
 [Nationality] Varchar(8) NULL,
 [PresentAddress] Text NULL,
 [Contact1Name] Varchar(64) NULL,
 [Contact1Phone] Varchar(20) NULL,
 [Contact2Name] Varchar(64) NULL,
 [Contact2Phone] Varchar(20) NULL,
 [CLV] Money DEFAULT 0.00 NOT NULL,
 [NumOrders] Int DEFAULT 0 NOT NULL,
 [CorporateClientId] Smallint NULL,
 [EnrolledOn] Date DEFAULT GETDATE() NOT NULL,
 [EnrollingUserId] Smallint NULL,
 [LastSeenOn] Smalldatetime NULL,
 [Notes] Text NULL,
 [CreatedOn] Smalldatetime DEFAULT GETDATE() NOT NULL,
 [LastUpdated] Smalldatetime DEFAULT GETDATE() NULL,
 [RowGuid] Uniqueidentifier DEFAULT NEWSEQUENTIALID() ROWGUIDCOL NOT NULL
)
go

-- Create indexes for table CRM.Customers

CREATE UNIQUE INDEX [AK_Customers_Web] ON [CRM].[Customers] ([Login])
go

CREATE INDEX [IX_Customers_Phones] ON [CRM].[Customers] ([Phone],[Phone2])
go

CREATE INDEX [IX_Customers_Email] ON [CRM].[Customers] ([Email])
go

CREATE INDEX [IX_Customers_Passport_NID] ON [CRM].[Customers] ([PassportNumber],[NationalIdNumber])
go

CREATE INDEX [IX_Customers_Web] ON [CRM].[Customers] ([Login],[IsActive],[WebAccess])
go

CREATE INDEX [IX_Customers_User] ON [CRM].[Customers] ([EnrollingUserId])
go

CREATE INDEX [IX_Customers_Corporate] ON [CRM].[Customers] ([CorporateClientId])
go

-- Add keys for table CRM.Customers

ALTER TABLE [CRM].[Customers] ADD CONSTRAINT [PK_Customers] PRIMARY KEY ([Id])
go

ALTER TABLE [CRM].[Customers] ADD CONSTRAINT [AK_Customers] UNIQUE ([UPIN])
go

ALTER TABLE [CRM].[Customers] ADD CONSTRAINT [AK_Customers_RowGuid] UNIQUE ([RowGuid])
go

-- Table CRM.CustomerAddresses

CREATE TABLE [CRM].[CustomerAddresses]
(
 [Id] Bigint IDENTITY(1,1) NOT NULL,
 [CustomerId] Bigint NOT NULL,
 [IsPreferred] Bit DEFAULT 0 NOT NULL,
 [AddressType] Tinyint DEFAULT 0 NOT NULL,
 [Line1] Varchar(240) NULL,
 [Line2] Varchar(240) NULL,
 [Line3] Varchar(240) NULL,
 [CityVillage] Varchar(40) NULL,
 [District] Varchar(40) NULL,
 [PostCode] Varchar(12) NULL,
 [CountryCode] Varchar(8) NULL,
 [CreatedOn] Smalldatetime DEFAULT GETDATE() NOT NULL,
 [LastUpdated] Smalldatetime NULL,
 [RowGuid] Uniqueidentifier DEFAULT NEWSEQUENTIALID() ROWGUIDCOL NOT NULL
)
go

-- Create indexes for table CRM.CustomerAddresses

CREATE INDEX [IX_CustomerAddresses_Patient] ON [CRM].[CustomerAddresses] ([CustomerId])
go

-- Add keys for table CRM.CustomerAddresses

ALTER TABLE [CRM].[CustomerAddresses] ADD CONSTRAINT [PK_CustomerAddresses] PRIMARY KEY ([Id])
go

ALTER TABLE [CRM].[CustomerAddresses] ADD CONSTRAINT [AK_CustomerAddresses_RowGuid] UNIQUE ([RowGuid])
go

-- Table Catalog.HealthPackages

CREATE TABLE [Catalog].[HealthPackages]
(
 [Id] Smallint IDENTITY(1001,1) NOT NULL,
 [Code] Varchar(40) NOT NULL,
 [Name] Varchar(160) NOT NULL,
 [IsActive] Bit DEFAULT 0 NOT NULL,
 [HideComponentPrices] Bit DEFAULT 0 NOT NULL,
 [SexRestricted] Tinyint DEFAULT 0 NOT NULL,
 [AgeRestricted] Tinyint DEFAULT 0 NOT NULL,
 [Availability] Tinyint DEFAULT 0 NOT NULL,
 [PackageValue] Smallmoney DEFAULT 0.00 NOT NULL,
 [NetPrice] Smallmoney DEFAULT 0.00 NOT NULL,
 [StartDate] Date NULL,
 [ExpiryDate] Date NULL,
 [Description] Text NULL,
 [CreatedOn] Smalldatetime DEFAULT GETDATE() NOT NULL,
 [LastUpdated] Smalldatetime NULL,
 [RowGuid] Uniqueidentifier DEFAULT NEWSEQUENTIALID() ROWGUIDCOL NOT NULL
)
ON [FG_Static]
go

-- Create indexes for table Catalog.HealthPackages

CREATE UNIQUE INDEX [AK_HealthPackages_Name] ON [Catalog].[HealthPackages] ([Name])
 ON [FG_Static]
go

CREATE INDEX [IX_HealthPackages] ON [Catalog].[HealthPackages] ([IsActive],[Name])
 ON [FG_Static]
go

CREATE UNIQUE INDEX [AK_HealthPackages_Code] ON [Catalog].[HealthPackages] ([Code])
 ON [FG_Static]
go

CREATE INDEX [IX_HealthPackages_Valid] ON [Catalog].[HealthPackages] ([IsActive],[ExpiryDate],[StartDate],[Name])
 ON [FG_Static]
go

-- Add keys for table Catalog.HealthPackages

ALTER TABLE [Catalog].[HealthPackages] ADD CONSTRAINT [PK_HealthPackages] PRIMARY KEY ([Id])
go

ALTER TABLE [Catalog].[HealthPackages] ADD CONSTRAINT [AK_HealthPackages_RowGuid] UNIQUE ([RowGuid])
 ON [FG_Static]
go

-- Table Catalog.HealthPackageComponents

CREATE TABLE [Catalog].[HealthPackageComponents]
(
 [Id] Int IDENTITY(1,1) NOT NULL,
 [HealthPackageId] Smallint NOT NULL,
 [LabTestId] Smallint NOT NULL,
 [HealthPakcageCompomentGroupId] Smallint NULL
)
go

-- Create indexes for table Catalog.HealthPackageComponents

CREATE INDEX [IX_HealthPackageComponents_Package] ON [Catalog].[HealthPackageComponents] ([HealthPackageId])
go

CREATE INDEX [IX_HealthPackageComponents_Test] ON [Catalog].[HealthPackageComponents] ([LabTestId])
go

CREATE UNIQUE INDEX [AK_HealthPackageComponents] ON [Catalog].[HealthPackageComponents] ([LabTestId],[HealthPackageId])
go

CREATE INDEX [IX_HealthPackageComponents_Group] ON [Catalog].[HealthPackageComponents] ([HealthPakcageCompomentGroupId])
go

-- Add keys for table Catalog.HealthPackageComponents

ALTER TABLE [Catalog].[HealthPackageComponents] ADD CONSTRAINT [PK_HealthPackageComponents] PRIMARY KEY ([Id])
go

-- Table Catalog.HealthPakcageCompomentGroups

CREATE TABLE [Catalog].[HealthPakcageCompomentGroups]
(
 [Id] Smallint NOT NULL,
 [Name] Varchar(64) NOT NULL,
 [Priority] Tinyint DEFAULT 0 NOT NULL
)
go

-- Add keys for table Catalog.HealthPakcageCompomentGroups

ALTER TABLE [Catalog].[HealthPakcageCompomentGroups] ADD CONSTRAINT [PK_HealthPakcageCompomentGroups] PRIMARY KEY ([Id])
go

ALTER TABLE [Catalog].[HealthPakcageCompomentGroups] ADD CONSTRAINT [AK_HealthPakcageCompomentGroups] UNIQUE ([Name])
go

-- Table Finances.ReferreredDailySalesSummary

CREATE TABLE [Finances].[ReferreredDailySalesSummary]
(
 [Id] Bigint IDENTITY(1,1) NOT NULL,
 [ReferrerId] Int NULL,
 [AffiliateId] Smallint NULL,
 [CorporateClientId] Smallint NULL,
 [Date] Date NOT NULL,
 [NumOrders] Smallint DEFAULT 0 NOT NULL,
 [GrossPayable] Smallmoney DEFAULT 0 NOT NULL,
 [DiscountAmount] Smallmoney DEFAULT 0 NOT NULL,
 [NetPayable] Smallmoney DEFAULT 0 NOT NULL,
 [PaidAmount] Smallmoney DEFAULT 0 NOT NULL,
 [RefundAmount] Smallmoney DEFAULT 0 NOT NULL,
 [DueAmount] Smallmoney DEFAULT 0 NOT NULL,
 [EstimatedReferralAmount] Smallmoney DEFAULT 0 NOT NULL,
 [LastUpdated] Smalldatetime DEFAULT GETDATE() NOT NULL
)
go

-- Create indexes for table Finances.ReferreredDailySalesSummary

CREATE INDEX [IX_ReferreredDailySalesSummary_Physician] ON [Finances].[ReferreredDailySalesSummary] ([ReferrerId])
go

CREATE UNIQUE INDEX [AK_ReferreredDailySalesSummary_Physician] ON [Finances].[ReferreredDailySalesSummary] ([Date],[ReferrerId])
go

CREATE INDEX [IX_ReferreredDailySalesSummary_Affiliate] ON [Finances].[ReferreredDailySalesSummary] ([AffiliateId])
go

CREATE UNIQUE INDEX [AK_ReferreredDailySalesSummary_Affiliate] ON [Finances].[ReferreredDailySalesSummary] ([Date],[AffiliateId])
go

CREATE INDEX [IX_ReferreredDailySalesSummary_Corporate] ON [Finances].[ReferreredDailySalesSummary] ([CorporateClientId])
go

CREATE UNIQUE INDEX [AK_ReferreredDailySalesSummary_Corporaate] ON [Finances].[ReferreredDailySalesSummary] ([Date],[CorporateClientId])
go

-- Add keys for table Finances.ReferreredDailySalesSummary

ALTER TABLE [Finances].[ReferreredDailySalesSummary] ADD CONSTRAINT [PK_ReferreredDailySalesSummary] PRIMARY KEY ([Id])
go

-- Table Marketing.CorporateHealthPackageLinks

CREATE TABLE [Marketing].[CorporateHealthPackageLinks]
(
 [Id] Int IDENTITY(1,1) NOT NULL,
 [CorporateClientId] Smallint NOT NULL,
 [HealthPackageId] Smallint NOT NULL
)
go

-- Create indexes for table Marketing.CorporateHealthPackageLinks

CREATE UNIQUE INDEX [AK_CorporateHealthPackageLinks] ON [Marketing].[CorporateHealthPackageLinks] ([HealthPackageId],[CorporateClientId])
go

-- Add keys for table Marketing.CorporateHealthPackageLinks

ALTER TABLE [Marketing].[CorporateHealthPackageLinks] ADD CONSTRAINT [PK_CorporateHealthPackageLinks] PRIMARY KEY ([Id])
go

-- Table Catalog.Branches

CREATE TABLE [Catalog].[Branches]
(
 [Id] Smallint NOT NULL,
 [Name] Varchar(64) NOT NULL,
 [Address] Text NULL,
 [BranchType] Tinyint DEFAULT 0 NOT NULL,
 [Phone] Varchar(32) NULL,
 [MapLocation] Text NULL
)
ON [FG_Static]
go

-- Add keys for table Catalog.Branches

ALTER TABLE [Catalog].[Branches] ADD CONSTRAINT [PK_Branches] PRIMARY KEY ([Id])
go

-- Table ConsultancyBilling.BillingProfiles

CREATE TABLE [ConsultancyBilling].[BillingProfiles]
(
 [Id] Smallint IDENTITY(101,1) NOT NULL,
 [Name] Varchar(120) NOT NULL,
 [IsActive] Bit DEFAULT 0 NOT NULL,
 [Remarks] Text NULL,
 [CreatedOn] Smalldatetime DEFAULT GETDATE() NOT NULL,
 [LastUpdated] Smalldatetime NULL,
 [RowGuid] Uniqueidentifier DEFAULT NEWSEQUENTIALID() ROWGUIDCOL NOT NULL
)
ON [FG_Static]
go

-- Create indexes for table ConsultancyBilling.BillingProfiles

CREATE INDEX [IX_BillingProfiles] ON [ConsultancyBilling].[BillingProfiles] ([IsActive],[Name])
go

-- Add keys for table ConsultancyBilling.BillingProfiles

ALTER TABLE [ConsultancyBilling].[BillingProfiles] ADD CONSTRAINT [PK_BillingProfiles] PRIMARY KEY ([Id])
 ON [FG_Static]
go

ALTER TABLE [ConsultancyBilling].[BillingProfiles] ADD CONSTRAINT [AK_BillingProfiles_Name] UNIQUE ([Name])
 ON [FG_Static]
go

ALTER TABLE [ConsultancyBilling].[BillingProfiles] ADD CONSTRAINT [AK_BillingProfiles_RowGuid] UNIQUE ([RowGuid])
go

-- Table ConsultancyBilling.BillingProfileDetails

CREATE TABLE [ConsultancyBilling].[BillingProfileDetails]
(
 [Id] Int IDENTITY(101,1) NOT NULL,
 [ProfileId] Smallint NOT NULL,
 [BillingMode] Tinyint DEFAULT 0 NOT NULL,
 [Percentage] Float DEFAULT 0.00 NOT NULL,
 [FlatAmount] Smallmoney DEFAULT 0.00 NOT NULL,
 [LabId] Smallint NULL,
 [LabTestId] Smallint NULL,
 [CreatedOn] Smalldatetime DEFAULT GETDATE() NOT NULL,
 [LastUpdated] Smalldatetime NULL,
 [RowGuid] Uniqueidentifier DEFAULT NEWSEQUENTIALID() ROWGUIDCOL NOT NULL
)
ON [FG_Static]
go

-- Create indexes for table ConsultancyBilling.BillingProfileDetails

CREATE INDEX [IX_BillingProfileDetails_Profile] ON [ConsultancyBilling].[BillingProfileDetails] ([ProfileId])
go

-- Add keys for table ConsultancyBilling.BillingProfileDetails

ALTER TABLE [ConsultancyBilling].[BillingProfileDetails] ADD CONSTRAINT [PK_BillingProfileDetails] PRIMARY KEY ([Id])
go

ALTER TABLE [ConsultancyBilling].[BillingProfileDetails] ADD CONSTRAINT [AK_BillingProfileDetails_RowGuid] UNIQUE ([RowGuid])
go

-- Table ConsultancyBilling.BillableServices

CREATE TABLE [ConsultancyBilling].[BillableServices]
(
 [Id] Bigint IDENTITY(1,1) NOT NULL,
 [ServiceDate] Date DEFAULT GETDATE() NOT NULL,
 [ConsultantId] Smallint NOT NULL,
 [InvoiceId] Bigint NOT NULL,
 [ResultBundleId] Bigint NOT NULL,
 [BillingProfileId] Smallint NOT NULL,
 [OrderedTestId] Bigint NOT NULL,
 [ServiceName] Varchar(120) NULL,
 [ServiceListPrice] Smallmoney NOT NULL,
 [AmountPayable] Smallmoney NOT NULL,
 [BillingMode] Tinyint DEFAULT 0 NOT NULL,
 [IncentiveAmount] Float DEFAULT 0.00 NOT NULL,
 [EditedByUserId] Smallint NULL,
 [CreatedOn] Smalldatetime DEFAULT GETDATE() NOT NULL,
 [LastUpdated] Smalldatetime NULL,
 [Remarks] Text NULL
)
ON [PS_OLTP_Date_13_33]([CreatedOn])
go

-- Create indexes for table ConsultancyBilling.BillableServices

CREATE INDEX [IX_BillableServices] ON [ConsultancyBilling].[BillableServices] ([ServiceDate],[ConsultantId])
go

CREATE INDEX [IX_BillableServices_Invoice] ON [ConsultancyBilling].[BillableServices] ([InvoiceId])
go

CREATE INDEX [IX_BillableServices_Consultant] ON [ConsultancyBilling].[BillableServices] ([ConsultantId])
go

CREATE INDEX [IX_BillableServices_Date] ON [ConsultancyBilling].[BillableServices] ([ServiceDate])
go

CREATE UNIQUE INDEX [AK_BillableServices_OrderedTest] ON [ConsultancyBilling].[BillableServices] ([OrderedTestId])
go

-- Add keys for table ConsultancyBilling.BillableServices

ALTER TABLE [ConsultancyBilling].[BillableServices] ADD CONSTRAINT [PK_BillableServices] PRIMARY KEY ([Id])
go

-- Table Marketing.TestReferralDetails

CREATE TABLE [Marketing].[TestReferralDetails]
(
 [Id] Bigint IDENTITY(1,1) NOT NULL,
 [ReferrerId] Int NULL,
 [AffiliateId] Smallint NULL,
 [LabTestId] Smallint NOT NULL,
 [Mode] Tinyint DEFAULT 0 NOT NULL,
 [FlatAmount] Smallmoney DEFAULT 0.00 NOT NULL,
 [Percentage] Float DEFAULT 0.00 NOT NULL,
 [MaxAmount] Smallmoney DEFAULT 0.00 NOT NULL,
 [CreatedOn] Smalldatetime DEFAULT GETDATE() NOT NULL,
 [LastUpdated] Smalldatetime NULL,
 [RowGuid] Uniqueidentifier DEFAULT NEWSEQUENTIALID() ROWGUIDCOL NOT NULL
)
ON [FG_Static]
go

-- Create indexes for table Marketing.TestReferralDetails

CREATE INDEX [IX_TestReferralDetails_Referrer] ON [Marketing].[TestReferralDetails] ([ReferrerId])
 ON [FG_Static]
go

CREATE INDEX [IX_TestReferralDetails_Affiliate] ON [Marketing].[TestReferralDetails] ([AffiliateId])
 ON [FG_Static]
go

CREATE INDEX [IX_TestReferralDetails_LabTest] ON [Marketing].[TestReferralDetails] ([LabTestId])
 ON [FG_Static]
go

-- Add keys for table Marketing.TestReferralDetails

ALTER TABLE [Marketing].[TestReferralDetails] ADD CONSTRAINT [PK_TestReferralDetails] PRIMARY KEY ([Id])
 ON [FG_Static]
go

ALTER TABLE [Marketing].[TestReferralDetails] ADD CONSTRAINT [AK_TestReferralDetails_RowGuid] UNIQUE ([RowGuid])
go

-- Table Subscriptions.Plans

CREATE TABLE [Subscriptions].[Plans]
(
 [Id] Smallint IDENTITY(1001,1) NOT NULL,
 [FeatureSetCode] Varchar(64) NOT NULL,
 [Name] Varchar(64) NOT NULL,
 [IsActive] Bit DEFAULT 0 NOT NULL,
 [DurationMonths] Smallint DEFAULT 0 NOT NULL,
 [GraceDays] Smallint DEFAULT 0 NOT NULL,
 [ListPrice] Smallmoney DEFAULT 0.00 NOT NULL,
 [Description] Text NULL,
 [CreatedOn] Smalldatetime DEFAULT GETDATE() NOT NULL,
 [LastUpdated] Smalldatetime NULL,
 [RowGuid] Uniqueidentifier DEFAULT NEWSEQUENTIALID() ROWGUIDCOL NOT NULL
)
go

-- Create indexes for table Subscriptions.Plans

CREATE INDEX [IX_Plan_FeatureSet] ON [Subscriptions].[Plans] ([FeatureSetCode])
go

-- Add keys for table Subscriptions.Plans

ALTER TABLE [Subscriptions].[Plans] ADD CONSTRAINT [PK_Plans] PRIMARY KEY ([Id])
go

ALTER TABLE [Subscriptions].[Plans] ADD CONSTRAINT [AK_Plans_Name] UNIQUE ([Name])
go

ALTER TABLE [Subscriptions].[Plans] ADD CONSTRAINT [AK_Plans_RowGuid] UNIQUE ([RowGuid])
go

-- Table Subscriptions.Subscriptions

CREATE TABLE [Subscriptions].[Subscriptions]
(
 [Id] Bigint IDENTITY(1001,1) NOT NULL,
 [CustomerId] Bigint NOT NULL,
 [PlanId] Smallint NOT NULL,
 [PerformingUserId] Smallint NOT NULL,
 [PlanName] Varchar(120) NOT NULL,
 [IsActive] Bit DEFAULT 0 NOT NULL,
 [StartedAt] Date DEFAULT GETDATE() NOT NULL,
 [ExpiresAt] Date NULL,
 [CanceledAt] Date NULL,
 [GraceDaysEndedAt] Date NULL,
 [WasSwitched] Bit DEFAULT 0 NULL,
 [AmountPaid] Smallmoney DEFAULT 0.00 NOT NULL,
 [Notes] Text NULL,
 [CreatedOn] Smalldatetime DEFAULT GETDATE() NOT NULL,
 [LastUpdated] Smalldatetime NULL,
 [RowGuid] Uniqueidentifier DEFAULT NEWSEQUENTIALID() ROWGUIDCOL NOT NULL
)
go

-- Create indexes for table Subscriptions.Subscriptions

CREATE INDEX [IX_Subscriptions_Customer] ON [Subscriptions].[Subscriptions] ([CustomerId])
go

CREATE INDEX [IX_Subscriptions_Plan] ON [Subscriptions].[Subscriptions] ([PlanId])
go

CREATE INDEX [IX_Subscriptions_Expiry] ON [Subscriptions].[Subscriptions] ([ExpiresAt],[IsActive])
go

CREATE INDEX [IX_Subscriptions_User] ON [Subscriptions].[Subscriptions] ([PerformingUserId],[CreatedOn])
go

-- Add keys for table Subscriptions.Subscriptions

ALTER TABLE [Subscriptions].[Subscriptions] ADD CONSTRAINT [PK_Subscriptions] PRIMARY KEY ([Id])
go

ALTER TABLE [Subscriptions].[Subscriptions] ADD CONSTRAINT [AK_Subscriptions_RowGuid] UNIQUE ([RowGuid])
go

-- Table ServiceDesk.Ticket

CREATE TABLE [ServiceDesk].[Ticket]
(
 [Id] Bigint IDENTITY(1001,1) NOT NULL,
 [CustomerId] Bigint NOT NULL,
 [InvoiceId] Bigint NULL,
 [Subject] Varchar(240) NOT NULL,
 [Status] Tinyint DEFAULT 0 NOT NULL,
 [Priority] Tinyint DEFAULT 0 NOT NULL,
 [AssignedAgentId] Smallint NULL,
 [LastReply] Smalldatetime DEFAULT GETDATE() NOT NULL,
 [CreatedOn] Smalldatetime DEFAULT GETDATE() NOT NULL,
 [LastUpdated] Smalldatetime NULL
)
go

-- Create indexes for table ServiceDesk.Ticket

CREATE INDEX [IX_Ticket_Customer] ON [ServiceDesk].[Ticket] ([CustomerId])
go

CREATE INDEX [IX_Ticket] ON [ServiceDesk].[Ticket] ([CustomerId],[CreatedOn])
go

CREATE INDEX [IX_Ticket_Agent] ON [ServiceDesk].[Ticket] ([AssignedAgentId],[CreatedOn])
go

CREATE INDEX [IX_Ticket_Status_Priority] ON [ServiceDesk].[Ticket] ([Status],[Priority])
go

-- Add keys for table ServiceDesk.Ticket

ALTER TABLE [ServiceDesk].[Ticket] ADD CONSTRAINT [PK_Ticket] PRIMARY KEY ([Id])
go

-- Table ServiceDesk.Messages

CREATE TABLE [ServiceDesk].[Messages]
(
 [Id] Bigint IDENTITY(1001,1) NOT NULL,
 [TicketId] Bigint NOT NULL,
 [AgentId] Smallint NULL,
 [IsVisible] Bit DEFAULT 0 NOT NULL,
 [Content] Text NOT NULL,
 [UserIpAddress] Varchar(64) NULL,
 [CreatedOn] Smalldatetime DEFAULT GETDATE() NOT NULL,
 [LastUpdated] Smalldatetime NULL
)
go

-- Create indexes for table ServiceDesk.Messages

CREATE INDEX [IX_Messages_Ticket] ON [ServiceDesk].[Messages] ([TicketId])
go

CREATE INDEX [IX_Messages_Agent] ON [ServiceDesk].[Messages] ([AgentId])
go

CREATE INDEX [IX_Messages] ON [ServiceDesk].[Messages] ([TicketId],[IsVisible],[Id])
go

-- Add keys for table ServiceDesk.Messages

ALTER TABLE [ServiceDesk].[Messages] ADD CONSTRAINT [PK_Messages] PRIMARY KEY ([Id])
go

-- Table CRM.Notifications

CREATE TABLE [CRM].[Notifications]
(
 [Id] Bigint IDENTITY(1,1) NOT NULL,
 [CustomerId] Bigint NOT NULL,
 [Channel] Tinyint DEFAULT 0 NOT NULL,
 [IsSeen] Bit DEFAULT 0 NOT NULL,
 [Recipient] Varchar(120) NULL,
 [Message] Text NOT NULL,
 [CreatedOn] Smalldatetime DEFAULT GETDATE() NOT NULL,
 [SeenOn] Smalldatetime NULL
)
go

-- Create indexes for table CRM.Notifications

CREATE INDEX [IX_Notifications_Customer] ON [CRM].[Notifications] ([CustomerId])
go

CREATE INDEX [IX_Notifications] ON [CRM].[Notifications] ([CustomerId],[IsSeen],[CreatedOn])
go

-- Add keys for table CRM.Notifications

ALTER TABLE [CRM].[Notifications] ADD CONSTRAINT [PK_Notifications] PRIMARY KEY ([Id])
go

-- Table CRM.TrackedLabOrders

CREATE TABLE [CRM].[TrackedLabOrders]
(
 [Id] Bigint IDENTITY(1,1) NOT NULL,
 [CustomerId] Bigint NOT NULL,
 [PatientLabOrderId] Bigint NOT NULL,
 [ApprovingUserId] Smallint NULL,
 [Relationship] Tinyint DEFAULT 0 NOT NULL,
 [Remarks] Text NULL,
 [CreatedOn] Smalldatetime DEFAULT GETDATE() NOT NULL,
 [LastUpdated] Smalldatetime NULL
)
go

-- Create indexes for table CRM.TrackedLabOrders

CREATE INDEX [IX_TrackedLabOrders_Customer] ON [CRM].[TrackedLabOrders] ([CustomerId])
go

CREATE INDEX [IX_TrackedLabOrders_Order] ON [CRM].[TrackedLabOrders] ([PatientLabOrderId])
go

CREATE UNIQUE INDEX [AK_TrackedLabOrders] ON [CRM].[TrackedLabOrders] ([CustomerId],[PatientLabOrderId])
go

-- Add keys for table CRM.TrackedLabOrders

ALTER TABLE [CRM].[TrackedLabOrders] ADD CONSTRAINT [PK_TrackedLabOrders] PRIMARY KEY ([Id])
go

-- Table ServiceDesk.LabOrderTrackingRequests

CREATE TABLE [ServiceDesk].[LabOrderTrackingRequests]
(
 [Id] Bigint IDENTITY(1,1) NOT NULL,
 [CustomerId] Bigint NOT NULL,
 [InvoiceNum] Varchar(20) NOT NULL,
 [InvoiceDate] Date NOT NULL,
 [OrderId] Varchar(8) NULL,
 [PatientName] Varchar(120) NULL,
 [PatientAge] Varchar(8) NULL,
 [PatientPhone] Varchar(20) NULL,
 [PatientEmail] Varchar(20) NULL,
 [Referrer] Varchar(120) NULL,
 [Relationship] Tinyint DEFAULT 0 NOT NULL,
 [CustomerRemarks] Text NOT NULL,
 [AssigningUserId] Smallint NULL,
 [Status] Tinyint DEFAULT 0 NOT NULL,
 [OperatorReply] Text NULL,
 [CreatedOn] Smalldatetime DEFAULT GETDATE() NOT NULL,
 [LastUpdated] Smalldatetime NULL
)
go

-- Create indexes for table ServiceDesk.LabOrderTrackingRequests

CREATE INDEX [IX_LabOrderTrackingRequests_Customer] ON [ServiceDesk].[LabOrderTrackingRequests] ([CustomerId])
go

CREATE INDEX [IX_LabOrderTrackingRequests] ON [ServiceDesk].[LabOrderTrackingRequests] ([Status],[CreatedOn])
go

-- Add keys for table ServiceDesk.LabOrderTrackingRequests

ALTER TABLE [ServiceDesk].[LabOrderTrackingRequests] ADD CONSTRAINT [PK_LabOrderTrackingRequests] PRIMARY KEY ([Id])
go

-- Table Subscriptions.Features

CREATE TABLE [Subscriptions].[Features]
(
 [Id] Smallint IDENTITY(1001,1) NOT NULL,
 [Code] Varchar(120) NOT NULL,
 [FeatureSetCode] Varchar(64) NOT NULL,
 [Name] Varchar(240) NULL,
 [CreatedOn] Smalldatetime DEFAULT GETDATE() NOT NULL,
 [RowGuid] Uniqueidentifier DEFAULT NEWSEQUENTIALID() ROWGUIDCOL NOT NULL
)
go

-- Create indexes for table Subscriptions.Features

CREATE INDEX [IX_Features_FeatureSet] ON [Subscriptions].[Features] ([FeatureSetCode])
go

CREATE UNIQUE INDEX [AK_Features] ON [Subscriptions].[Features] ([Code],[FeatureSetCode])
go

-- Add keys for table Subscriptions.Features

ALTER TABLE [Subscriptions].[Features] ADD CONSTRAINT [PK_Features] PRIMARY KEY ([Id])
go

ALTER TABLE [Subscriptions].[Features] ADD CONSTRAINT [AK_Features_RowGuid] UNIQUE ([RowGuid])
go

-- Table CRM.AuditLogs

CREATE TABLE [CRM].[AuditLogs]
(
 [Id] Bigint IDENTITY(1,1) NOT NULL,
 [CustomerId] Bigint NOT NULL,
 [OriginalCustomerId] Bigint NULL,
 [EventTime] Smalldatetime DEFAULT GETDATE() NOT NULL,
 [EventType] Smallint DEFAULT 0 NOT NULL,
 [PerformingUserId] Smallint NULL,
 [PatientLabOrderId] Bigint NULL,
 [ResultBundleId] Bigint NULL,
 [IpAddress] Int NULL,
 [Remarks] Text NULL
)
go

-- Create indexes for table CRM.AuditLogs

CREATE INDEX [IX_AuditLogs] ON [CRM].[AuditLogs] ([CustomerId],[EventTime])
go

CREATE INDEX [IX_AuditLogs_Invoice] ON [CRM].[AuditLogs] ([PatientLabOrderId],[EventTime])
go

CREATE INDEX [IX_AuditLogs_OriginalCustomer] ON [CRM].[AuditLogs] ([OriginalCustomerId],[EventTime])
go

-- Add keys for table CRM.AuditLogs

ALTER TABLE [CRM].[AuditLogs] ADD CONSTRAINT [PK_AuditLogs] PRIMARY KEY ([Id])
go

-- Table Subscriptions.Invoices

CREATE TABLE [Subscriptions].[Invoices]
(
 [Id] Bigint IDENTITY(1001,1) NOT NULL,
 [CustomerId] Bigint NOT NULL,
 [SubscriptionId] Bigint NOT NULL,
 [PerformingStaffId] Smallint NULL,
 [InvoiceDate] Smalldatetime DEFAULT GETDATE() NOT NULL,
 [PlanListPrice] Smallmoney DEFAULT 0.00 NOT NULL,
 [SurchargeAmount] Smallmoney DEFAULT 0.00 NOT NULL,
 [TaxAmount] Smallmoney DEFAULT 0.00 NOT NULL,
 [InvoiceAmount] Smallmoney DEFAULT 0.00 NOT NULL,
 [PaidAmount] Smallmoney DEFAULT 0.00 NOT NULL,
 [PaymentMethod] Tinyint DEFAULT 0 NOT NULL,
 [PaymentGateway] Tinyint DEFAULT 0 NOT NULL,
 [TxReferenceId] Varchar(120) NULL,
 [TxParams] Text NULL,
 [Notes] Text NULL,
 [LastUpdated] Smalldatetime NULL,
 [RowGuid] Uniqueidentifier DEFAULT NEWSEQUENTIALID() ROWGUIDCOL NOT NULL
)
go

-- Create indexes for table Subscriptions.Invoices

CREATE INDEX [IX_Invoices_Subscription] ON [Subscriptions].[Invoices] ([SubscriptionId])
go

CREATE INDEX [IX_Invoices_Customer] ON [Subscriptions].[Invoices] ([CustomerId])
go

CREATE INDEX [IX_Invoices_Staff] ON [Subscriptions].[Invoices] ([PerformingStaffId],[InvoiceDate])
go

CREATE INDEX [IX_Invoices] ON [Subscriptions].[Invoices] ([CustomerId],[InvoiceDate])
go

CREATE INDEX [IX_Invoices_TxRef] ON [Subscriptions].[Invoices] ([TxReferenceId])
go

-- Add keys for table Subscriptions.Invoices

ALTER TABLE [Subscriptions].[Invoices] ADD CONSTRAINT [PK_Invoices] PRIMARY KEY ([Id])
go

-- Table CRM.VerificationCodes

CREATE TABLE [CRM].[VerificationCodes]
(
 [Id] Int IDENTITY(1,1) NOT NULL,
 [CustomerId] Bigint NOT NULL,
 [OtpCode] Varchar(8) NOT NULL,
 [ExpiresAt] Smalldatetime NULL,
 [CreatedOn] Smalldatetime DEFAULT GETDATE() NOT NULL
)
go

-- Create indexes for table CRM.VerificationCodes

CREATE INDEX [IX_VerificationCodes_Customer] ON [CRM].[VerificationCodes] ([CustomerId])
go

-- Add keys for table CRM.VerificationCodes

ALTER TABLE [CRM].[VerificationCodes] ADD CONSTRAINT [PK_VerificationCodes] PRIMARY KEY ([Id])
go

ALTER TABLE [CRM].[VerificationCodes] ADD CONSTRAINT [AK_VerificationCodes_Code] UNIQUE ([OtpCode])
go

-- Table Subscriptions.FeatureSets

CREATE TABLE [Subscriptions].[FeatureSets]
(
 [Id] Smallint IDENTITY(1,1) NOT NULL,
 [Code] Varchar(64) NOT NULL,
 [CreatedOn] Smalldatetime DEFAULT GETDATE() NOT NULL,
 [RowGuid] Uniqueidentifier DEFAULT NEWSEQUENTIALID() ROWGUIDCOL NOT NULL
)
go

-- Add keys for table Subscriptions.FeatureSets

ALTER TABLE [Subscriptions].[FeatureSets] ADD CONSTRAINT [PK_FeatureSets] PRIMARY KEY ([Id])
go

ALTER TABLE [Subscriptions].[FeatureSets] ADD CONSTRAINT [AK_FeatureSets_Code] UNIQUE ([Code])
go

ALTER TABLE [Subscriptions].[FeatureSets] ADD CONSTRAINT [AK_FeatureSets_RowGuid] UNIQUE ([RowGuid])
go

-- Table CRM.TrackedPatients

CREATE TABLE [CRM].[TrackedPatients]
(
 [Id] Bigint IDENTITY(1,1) NOT NULL,
 [FollowerId] Bigint NOT NULL,
 [FolloweeId] Bigint NOT NULL,
 [Relationship] Tinyint DEFAULT 0 NOT NULL,
 [ApprovingUserId] Smallint NULL,
 [Remarks] Text NULL,
 [CreatedOn] Smalldatetime DEFAULT GETDATE() NOT NULL,
 [LastUpdated] Smalldatetime NULL
)
go

-- Create indexes for table CRM.TrackedPatients

CREATE INDEX [IX_TrackedPatients_Followee] ON [CRM].[TrackedPatients] ([FolloweeId])
go

CREATE INDEX [IX_TrackedPatients_Follower] ON [CRM].[TrackedPatients] ([FollowerId])
go

CREATE UNIQUE INDEX [AK_TrackedPatients] ON [CRM].[TrackedPatients] ([FolloweeId],[FollowerId])
go

-- Add keys for table CRM.TrackedPatients

ALTER TABLE [CRM].[TrackedPatients] ADD CONSTRAINT [PK_TrackedPatients] PRIMARY KEY ([Id])
go

-- Table ServiceDesk.CustomerTrackingRequests

CREATE TABLE [ServiceDesk].[CustomerTrackingRequests]
(
 [Id] Bigint IDENTITY(1,1) NOT NULL,
 [RequesterId] Bigint NOT NULL,
 [RequesteeId] Bigint NULL,
 [RequesteeUPIN] Varchar(32) NULL,
 [RequesteeName] Varchar(120) NULL,
 [RequesteePhone] Varchar(20) NULL,
 [Reason] Text NULL,
 [AssigningUserId] Smallint NULL,
 [OperatorReply] Text NULL,
 [Status] Tinyint DEFAULT 0 NOT NULL,
 [CreatedOn] Smalldatetime DEFAULT GETDATE() NOT NULL,
 [LastUpdated] Smalldatetime NULL
)
go

-- Create indexes for table ServiceDesk.CustomerTrackingRequests

CREATE INDEX [IX_CustomerTrackingRequests_Requester] ON [ServiceDesk].[CustomerTrackingRequests] ([RequesterId])
go

-- Add keys for table ServiceDesk.CustomerTrackingRequests

ALTER TABLE [ServiceDesk].[CustomerTrackingRequests] ADD CONSTRAINT [PK_CustomerTrackingRequests] PRIMARY KEY ([Id])
go

-- Table CRM.Contacts

CREATE TABLE [CRM].[Contacts]
(
 [Id] Bigint IDENTITY(1001,1) NOT NULL,
 [CustomerId] Bigint NOT NULL,
 [IsActive] Bit DEFAULT 0 NOT NULL,
 [Name] Varchar(240) NOT NULL,
 [Email] Varchar(64) NULL,
 [Phone] Varchar(20) NULL,
 [ContactType] Tinyint DEFAULT 0 NOT NULL,
 [IsFavorite] Bit DEFAULT 0 NOT NULL,
 [Notes] Text NULL,
 [CreatedOn] Smalldatetime DEFAULT GETDATE() NOT NULL,
 [LastUpdated] Smalldatetime NULL,
 [DeletedOn] Smalldatetime NULL
)
go

-- Create indexes for table CRM.Contacts

CREATE INDEX [IX_Contacts] ON [CRM].[Contacts] ([CustomerId],[IsActive])
go

-- Add keys for table CRM.Contacts

ALTER TABLE [CRM].[Contacts] ADD CONSTRAINT [PK_Contacts] PRIMARY KEY ([Id])
go

-- Table EMR.MedicalRecords

CREATE TABLE [EMR].[MedicalRecords]
(
 [Id] Bigint IDENTITY(1,1) NOT NULL,
 [CustomerId] Bigint NOT NULL,
 [AppointmentDate] Date NULL,
 [DoctorId] Bigint NULL,
 [DoctorName] Varchar(240) NULL,
 [DoctorPhone] Varchar(20) NULL,
 [DoctorEmail] Varchar(20) NULL,
 [PrescriptionFilename] Varchar(120) NULL,
 [Notes] Text NULL,
 [CreatedOn] Smalldatetime DEFAULT GETDATE() NOT NULL,
 [LastUpdated] Smalldatetime NULL
)
go

-- Create indexes for table EMR.MedicalRecords

CREATE INDEX [IX_MedicalRecords] ON [EMR].[MedicalRecords] ([CustomerId],[AppointmentDate])
go

-- Add keys for table EMR.MedicalRecords

ALTER TABLE [EMR].[MedicalRecords] ADD CONSTRAINT [PK_MedicalRecords] PRIMARY KEY ([Id])
go

-- Table EMR.Physicians

CREATE TABLE [EMR].[Physicians]
(
 [Id] Bigint IDENTITY(1,1) NOT NULL,
 [CustomerId] Bigint NOT NULL,
 [SpecialtyId] Smallint NULL,
 [Name] Varchar(240) NOT NULL,
 [Sex] Tinyint DEFAULT 0 NOT NULL,
 [Qualifications] Varchar(120) NULL,
 [InstitutionName] Varchar(120) NULL,
 [WorkAddress] Text NULL,
 [Phone] Varchar(40) NULL,
 [AppointmentContact] Varchar(120) NULL,
 [Email] Varchar(40) NULL,
 [RegistrationNo] Varchar(40) NULL,
 [CreatedOn] Smalldatetime DEFAULT GETDATE() NOT NULL,
 [UpdatedOn] Smalldatetime NULL
)
go

-- Create indexes for table EMR.Physicians

CREATE INDEX [IX_Physicians] ON [EMR].[Physicians] ([CustomerId])
go

-- Add keys for table EMR.Physicians

ALTER TABLE [EMR].[Physicians] ADD CONSTRAINT [PK_Physicians] PRIMARY KEY ([Id])
go

-- Table EMR.MedicalSpecialties

CREATE TABLE [EMR].[MedicalSpecialties]
(
 [Id] Smallint IDENTITY(101,1) NOT NULL,
 [Name] Varchar(240) NOT NULL,
 [CreatedOn] Smalldatetime DEFAULT GETDATE() NOT NULL
)
go

-- Add keys for table EMR.MedicalSpecialties

ALTER TABLE [EMR].[MedicalSpecialties] ADD CONSTRAINT [PK_MedicalSpecialties] PRIMARY KEY ([Id])
go

ALTER TABLE [EMR].[MedicalSpecialties] ADD CONSTRAINT [AK MedicalSpecialties] UNIQUE ([Name])
go

-- Table EMR.Medications

CREATE TABLE [EMR].[Medications]
(
 [Id] Bigint IDENTITY(1,1) NOT NULL,
 [CustomerId] Bigint NOT NULL,
 [MedicalRecordId] Bigint NULL,
 [IsActive] Bit DEFAULT 0 NOT NULL,
 [DrugName] Varchar(240) NOT NULL,
 [GenericName] Varchar(120) NULL,
 [Dosage] Varchar(64) NULL,
 [Route] Varchar(20) NULL,
 [Frequency] Varchar(120) NULL,
 [Duration] Varchar(120) NULL,
 [StartDate] Date NULL,
 [EndDate] Date NULL,
 [Notes] Text NULL,
 [CreatedOn] Smalldatetime DEFAULT GETDATE() NOT NULL,
 [LastUpdated] Smalldatetime NULL
)
go

-- Create indexes for table EMR.Medications

CREATE INDEX [IX_Medications_Customer] ON [EMR].[Medications] ([CustomerId])
go

CREATE INDEX [IX_Medications_Records] ON [EMR].[Medications] ([MedicalRecordId])
go

-- Add keys for table EMR.Medications

ALTER TABLE [EMR].[Medications] ADD CONSTRAINT [PK_Medications] PRIMARY KEY ([Id])
go

-- Table ServiceDesk.PatientTrackingRequests

CREATE TABLE [ServiceDesk].[PatientTrackingRequests]
(
 [Id] Bigint IDENTITY(1,1) NOT NULL,
 [CustomerId] Bigint NOT NULL,
 [PatientName] Varchar(120) NOT NULL,
 [PatientAge] Varchar(8) NULL,
 [PatientDoB] Date NULL,
 [PatientUPIN] Varchar(20) NULL,
 [PatientPhone] Varchar(20) NULL,
 [PatientEmail] Varchar(20) NULL,
 [Relationship] Tinyint DEFAULT 0 NOT NULL,
 [InvoiceNum] Varchar(20) NULL,
 [InvoiceDate] Date NULL,
 [AssigningUserId] Smallint NULL,
 [Status] Tinyint DEFAULT 0 NOT NULL,
 [CustomerRemarks] Text NULL,
 [OperatorReply] Text NULL,
 [CreatedOn] Smalldatetime DEFAULT GETDATE() NOT NULL,
 [LastUpdated] Smalldatetime NULL
)
go

-- Create indexes for table ServiceDesk.PatientTrackingRequests

CREATE INDEX [IX_PatientTrackingRequests_Customer] ON [ServiceDesk].[PatientTrackingRequests] ([CustomerId])
go

CREATE INDEX [IX_PatientTrackingRequests_Status] ON [ServiceDesk].[PatientTrackingRequests] ([Status])
go

CREATE INDEX [IX_PatientTrackingRequests_Staff] ON [ServiceDesk].[PatientTrackingRequests] ([AssigningUserId])
go

-- Add keys for table ServiceDesk.PatientTrackingRequests

ALTER TABLE [ServiceDesk].[PatientTrackingRequests] ADD CONSTRAINT [PK_PatientTrackingRequests] PRIMARY KEY ([Id])
go

-- Table PROE.Outcalls

CREATE TABLE [PROE].[Outcalls]
(
 [Id] Bigint IDENTITY(1,1) NOT NULL,
 [PatientLabOrderId] Bigint NOT NULL,
 [ScheduledCollectionTime] Smalldatetime NULL,
 [Address] Text NULL,
 [ContactPhone] Varchar(20) NULL,
 [CreatedOn] Smalldatetime DEFAULT GETDATE() NOT NULL,
 [LastUpdated] Smalldatetime NULL
)
go

-- Create indexes for table PROE.Outcalls

CREATE INDEX [IX_Outcalls_Invoice] ON [PROE].[Outcalls] ([PatientLabOrderId])
go

CREATE INDEX [IX_Outcalls_Scheduled] ON [PROE].[Outcalls] ([ScheduledCollectionTime])
go

CREATE INDEX [IX_Outcalls_Created] ON [PROE].[Outcalls] ([CreatedOn])
go

-- Add keys for table PROE.Outcalls

ALTER TABLE [PROE].[Outcalls] ADD CONSTRAINT [PK_Outcalls] PRIMARY KEY ([Id])
go

-- Table Marketing.MarketingTeams

CREATE TABLE [Marketing].[MarketingTeams]
(
 [Id] Smallint IDENTITY(1001,1) NOT NULL,
 [Name] Varchar(64) NOT NULL,
 [Manager] Varchar(64) NULL,
 [CreatedOn] Smalldatetime DEFAULT GETDATE() NOT NULL,
 [LastUpdated] Smalldatetime NULL,
 [RowGuid] Uniqueidentifier DEFAULT NEWSEQUENTIALID() NOT NULL
)
ON [FG_Static]
go

-- Add keys for table Marketing.MarketingTeams

ALTER TABLE [Marketing].[MarketingTeams] ADD CONSTRAINT [PK_MarketingTeams] PRIMARY KEY ([Id])
go

ALTER TABLE [Marketing].[MarketingTeams] ADD CONSTRAINT [AK MarketingTeams_Name] UNIQUE ([Name])
go

-- Create foreign keys (relationships) section ------------------------------------------------- 


ALTER TABLE [Marketing].[Affiliates] ADD CONSTRAINT [FK_MarketingExec_Affiliate] FOREIGN KEY ([MarketingExecId]) REFERENCES [Marketing].[MarketingExecs] ([Id]) ON UPDATE NO ACTION ON DELETE SET NULL
go



ALTER TABLE [Marketing].[CorporateDiscountDetails] ADD CONSTRAINT [FK_Lab_DiscountDetails] FOREIGN KEY ([LabId]) REFERENCES [Catalog].[Labs] ([Id]) ON UPDATE NO ACTION ON DELETE CASCADE
go



ALTER TABLE [Marketing].[CorporateDiscountDetails] ADD CONSTRAINT [FK_DiscountGroup_Details] FOREIGN KEY ([ProfileId]) REFERENCES [Marketing].[CorporateDiscountProfiles] ([Id]) ON UPDATE NO ACTION ON DELETE CASCADE
go



ALTER TABLE [Marketing].[CorporateDiscountDetails] ADD CONSTRAINT [FK_LabTest_DiscountDetails] FOREIGN KEY ([LabTestId]) REFERENCES [Catalog].[LabTests] ([Id]) ON UPDATE NO ACTION ON DELETE CASCADE
go



ALTER TABLE [Marketing].[TestReferralDetails] ADD CONSTRAINT [FK_Referrer_Referral] FOREIGN KEY ([ReferrerId]) REFERENCES [Catalog].[Referrers] ([Id]) ON UPDATE NO ACTION ON DELETE NO ACTION
go



ALTER TABLE [Marketing].[TestReferralDetails] ADD CONSTRAINT [FK_Affiliate_Referral] FOREIGN KEY ([AffiliateId]) REFERENCES [Marketing].[Affiliates] ([Id]) ON UPDATE NO ACTION ON DELETE SET NULL
go



ALTER TABLE [Marketing].[TestReferralDetails] ADD CONSTRAINT [FK_LabTest_ReferralDetails] FOREIGN KEY ([LabTestId]) REFERENCES [Catalog].[LabTests] ([Id]) ON UPDATE NO ACTION ON DELETE CASCADE
go



ALTER TABLE [Marketing].[CorporateClients] ADD CONSTRAINT [FK_MarketingExec_Corporate] FOREIGN KEY ([MarketingExecId]) REFERENCES [Marketing].[MarketingExecs] ([Id]) ON UPDATE NO ACTION ON DELETE SET NULL
go



ALTER TABLE [Finances].[ReferreredDailySalesSummary] ADD CONSTRAINT [FK_Referrer_SalesSummary] FOREIGN KEY ([ReferrerId]) REFERENCES [Catalog].[Referrers] ([Id]) ON UPDATE NO ACTION ON DELETE CASCADE
go



ALTER TABLE [Finances].[ReferreredDailySalesSummary] ADD CONSTRAINT [FK_Affiliate_SalesSummary] FOREIGN KEY ([AffiliateId]) REFERENCES [Marketing].[Affiliates] ([Id]) ON UPDATE NO ACTION ON DELETE CASCADE
go



ALTER TABLE [Finances].[ReferreredDailySalesSummary] ADD CONSTRAINT [FK_Corporate_SalesSummary] FOREIGN KEY ([CorporateClientId]) REFERENCES [Marketing].[CorporateClients] ([Id]) ON UPDATE NO ACTION ON DELETE CASCADE
go



ALTER TABLE [Catalog].[HealthPackageComponents] ADD CONSTRAINT [FK_HealthPackage_Test] FOREIGN KEY ([HealthPackageId]) REFERENCES [Catalog].[HealthPackages] ([Id]) ON UPDATE NO ACTION ON DELETE CASCADE
go



ALTER TABLE [Catalog].[HealthPackageComponents] ADD CONSTRAINT [FK_LabTest_HealthPackage] FOREIGN KEY ([LabTestId]) REFERENCES [Catalog].[LabTests] ([Id]) ON UPDATE NO ACTION ON DELETE CASCADE
go



ALTER TABLE [Finances].[CorporateBillSettlements] ADD CONSTRAINT [FK_CorporateClient_BillSettlements] FOREIGN KEY ([CorporateClientId]) REFERENCES [Marketing].[CorporateClients] ([Id]) ON UPDATE NO ACTION ON DELETE CASCADE
go



ALTER TABLE [Marketing].[CorporateHealthPackageLinks] ADD CONSTRAINT [FK_Corporate_Packages] FOREIGN KEY ([CorporateClientId]) REFERENCES [Marketing].[CorporateClients] ([Id]) ON UPDATE NO ACTION ON DELETE CASCADE
go



ALTER TABLE [Marketing].[CorporateHealthPackageLinks] ADD CONSTRAINT [FK_HealthPackage_Corporate] FOREIGN KEY ([HealthPackageId]) REFERENCES [Catalog].[HealthPackages] ([Id]) ON UPDATE NO ACTION ON DELETE CASCADE
go



ALTER TABLE [Catalog].[HealthPackageComponents] ADD CONSTRAINT [FK_HealthPackageComponents_Group] FOREIGN KEY ([HealthPakcageCompomentGroupId]) REFERENCES [Catalog].[HealthPakcageCompomentGroups] ([Id]) ON UPDATE NO ACTION ON DELETE NO ACTION
go



ALTER TABLE [Marketing].[CorporateClients] ADD CONSTRAINT [FK_CorporateClientGroup_Companies] FOREIGN KEY ([ClientGroupId]) REFERENCES [Marketing].[CorporateClientGroups] ([Id]) ON UPDATE NO ACTION ON DELETE SET NULL
go



ALTER TABLE [ConsultancyBilling].[BillableServices] ADD CONSTRAINT [FK_LabOrder_BillableServices] FOREIGN KEY ([InvoiceId]) REFERENCES [PROE].[PatientLabOrders] ([InvoiceId]) ON UPDATE NO ACTION ON DELETE CASCADE
go



ALTER TABLE [Finances].[CorporateBillSettlements] ADD CONSTRAINT [FK_User_CorporateSettlement] FOREIGN KEY ([AuthorizingUserId]) REFERENCES [Staff].[Users] ([Id]) ON UPDATE NO ACTION ON DELETE NO ACTION
go



ALTER TABLE [OuterOMS].[AssociateLabUsers] ADD CONSTRAINT [FK_AssociateLab_Users] FOREIGN KEY ([OrganizationId]) REFERENCES [OuterOMS].[AssociateOrganizations] ([Id]) ON UPDATE NO ACTION ON DELETE CASCADE
go



ALTER TABLE [CRM].[CustomerAddresses] ADD CONSTRAINT [FK_Customer_Addresses] FOREIGN KEY ([CustomerId]) REFERENCES [CRM].[Customers] ([Id]) ON UPDATE NO ACTION ON DELETE CASCADE
go



ALTER TABLE [CRM].[Customers] ADD CONSTRAINT [FK_Staff_EnrolledPatients] FOREIGN KEY ([EnrollingUserId]) REFERENCES [Staff].[Users] ([Id]) ON UPDATE NO ACTION ON DELETE SET NULL
go



ALTER TABLE [ConsultancyBilling].[BillableServices] ADD CONSTRAINT [FK_BillingProfile_Services] FOREIGN KEY ([BillingProfileId]) REFERENCES [ConsultancyBilling].[BillingProfiles] ([Id]) ON UPDATE NO ACTION ON DELETE CASCADE
go



ALTER TABLE [ConsultancyBilling].[BillableServices] ADD CONSTRAINT [FK_ResuleBundle_BillableService] FOREIGN KEY ([ResultBundleId]) REFERENCES [TestResults].[ResultBundles] ([Id]) ON UPDATE NO ACTION ON DELETE CASCADE
go



ALTER TABLE [Marketing].[CorporateClients] ADD CONSTRAINT [FK_CorporateDiscountSchedule_Clients] FOREIGN KEY ([DiscountProfileId]) REFERENCES [Marketing].[CorporateDiscountProfiles] ([Id]) ON UPDATE NO ACTION ON DELETE SET NULL
go



ALTER TABLE [Marketing].[AssociateDiscountDetails] ADD CONSTRAINT [FK_AssociateDiscount_Details] FOREIGN KEY ([ProfileId]) REFERENCES [Marketing].[AssociateDiscountProfiles] ([Id]) ON UPDATE NO ACTION ON DELETE CASCADE
go



ALTER TABLE [ConsultancyBilling].[BillableServices] ADD CONSTRAINT [FK_User_ConsultancyServices] FOREIGN KEY ([ConsultantId]) REFERENCES [Staff].[Users] ([Id]) ON UPDATE NO ACTION ON DELETE CASCADE
go



ALTER TABLE [ConsultancyBilling].[BillableServices] ADD CONSTRAINT [FK_OrderedTest_Billing] FOREIGN KEY ([OrderedTestId]) REFERENCES [PROE].[OrderedTests] ([Id]) ON UPDATE NO ACTION ON DELETE CASCADE
go



ALTER TABLE [ConsultancyBilling].[BillingProfileDetails] ADD CONSTRAINT [FK_BillingProfile_Details] FOREIGN KEY ([ProfileId]) REFERENCES [ConsultancyBilling].[BillingProfiles] ([Id]) ON UPDATE NO ACTION ON DELETE CASCADE
go



ALTER TABLE [ConsultancyBilling].[BillingProfileDetails] ADD CONSTRAINT [FK_Lab_BillingDetails] FOREIGN KEY ([LabId]) REFERENCES [Catalog].[Labs] ([Id]) ON UPDATE NO ACTION ON DELETE SET NULL
go



ALTER TABLE [ConsultancyBilling].[BillingProfileDetails] ADD CONSTRAINT [FK_LabTest_BillingDetails] FOREIGN KEY ([LabTestId]) REFERENCES [Catalog].[LabTests] ([Id]) ON UPDATE NO ACTION ON DELETE SET NULL
go



ALTER TABLE [Subscriptions].[Subscriptions] ADD CONSTRAINT [FK_Customer_Subscriptions] FOREIGN KEY ([CustomerId]) REFERENCES [CRM].[Customers] ([Id]) ON UPDATE NO ACTION ON DELETE CASCADE
go



ALTER TABLE [Subscriptions].[Subscriptions] ADD CONSTRAINT [FK_Plan_Subscriptions] FOREIGN KEY ([PlanId]) REFERENCES [Subscriptions].[Plans] ([Id]) ON UPDATE NO ACTION ON DELETE CASCADE
go



ALTER TABLE [ServiceDesk].[Ticket] ADD CONSTRAINT [FK_Customer_Tickets] FOREIGN KEY ([CustomerId]) REFERENCES [CRM].[Customers] ([Id]) ON UPDATE NO ACTION ON DELETE CASCADE
go



ALTER TABLE [ServiceDesk].[Messages] ADD CONSTRAINT [FK_Ticket_Messages] FOREIGN KEY ([TicketId]) REFERENCES [ServiceDesk].[Ticket] ([Id]) ON UPDATE NO ACTION ON DELETE CASCADE
go



ALTER TABLE [ServiceDesk].[Messages] ADD CONSTRAINT [FK_User_Ticket_Messages] FOREIGN KEY ([AgentId]) REFERENCES [Staff].[Users] ([Id]) ON UPDATE NO ACTION ON DELETE SET NULL
go



ALTER TABLE [CRM].[Notifications] ADD CONSTRAINT [FK_Customer_Notifications] FOREIGN KEY ([CustomerId]) REFERENCES [CRM].[Customers] ([Id]) ON UPDATE NO ACTION ON DELETE CASCADE
go



ALTER TABLE [CRM].[TrackedLabOrders] ADD CONSTRAINT [FK_Customer_TrackedOrders] FOREIGN KEY ([CustomerId]) REFERENCES [CRM].[Customers] ([Id]) ON UPDATE NO ACTION ON DELETE CASCADE
go



ALTER TABLE [CRM].[TrackedLabOrders] ADD CONSTRAINT [FK_LabOrder_Linked_Customer] FOREIGN KEY ([PatientLabOrderId]) REFERENCES [PROE].[PatientLabOrders] ([InvoiceId]) ON UPDATE NO ACTION ON DELETE CASCADE
go



ALTER TABLE [ServiceDesk].[LabOrderTrackingRequests] ADD CONSTRAINT [FK_Customer_TrackingRequest] FOREIGN KEY ([CustomerId]) REFERENCES [CRM].[Customers] ([Id]) ON UPDATE NO ACTION ON DELETE CASCADE
go



ALTER TABLE [Subscriptions].[Invoices] ADD CONSTRAINT [FK_Subscription_Invoices] FOREIGN KEY ([SubscriptionId]) REFERENCES [Subscriptions].[Subscriptions] ([Id]) ON UPDATE NO ACTION ON DELETE CASCADE
go



ALTER TABLE [Subscriptions].[Invoices] ADD CONSTRAINT [FK_Customer_Invoices] FOREIGN KEY ([CustomerId]) REFERENCES [CRM].[Customers] ([Id]) ON UPDATE NO ACTION ON DELETE NO ACTION
go



ALTER TABLE [CRM].[VerificationCodes] ADD CONSTRAINT [FK_Customer_OtpCodes] FOREIGN KEY ([CustomerId]) REFERENCES [CRM].[Customers] ([Id]) ON UPDATE NO ACTION ON DELETE CASCADE
go



ALTER TABLE [Subscriptions].[Features] ADD CONSTRAINT [FK_FeatureSet_Features] FOREIGN KEY ([FeatureSetCode]) REFERENCES [Subscriptions].[FeatureSets] ([Code]) ON UPDATE NO ACTION ON DELETE CASCADE
go



ALTER TABLE [Subscriptions].[Plans] ADD CONSTRAINT [FK_FeatureSet_Plans] FOREIGN KEY ([FeatureSetCode]) REFERENCES [Subscriptions].[FeatureSets] ([Code]) ON UPDATE NO ACTION ON DELETE CASCADE
go



ALTER TABLE [CRM].[TrackedPatients] ADD CONSTRAINT [FK_Customer_Followees] FOREIGN KEY ([FolloweeId]) REFERENCES [CRM].[Customers] ([Id]) ON UPDATE CASCADE ON DELETE NO ACTION
go



ALTER TABLE [CRM].[TrackedPatients] ADD CONSTRAINT [FK_Customer_Followers] FOREIGN KEY ([FollowerId]) REFERENCES [CRM].[Customers] ([Id]) ON UPDATE NO ACTION ON DELETE CASCADE
go



ALTER TABLE [ServiceDesk].[CustomerTrackingRequests] ADD CONSTRAINT [FK_Customer_Requester] FOREIGN KEY ([RequesterId]) REFERENCES [CRM].[Customers] ([Id]) ON UPDATE NO ACTION ON DELETE CASCADE
go




