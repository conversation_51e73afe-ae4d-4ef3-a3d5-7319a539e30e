-- schemas

CREATE SCHEMA [ConsultancyBilling]
go

CREATE SCHEMA [OuterOMS]
go


-- ConsultancyBillingProfileId

-- ALTER TABLE [Catalog].[Labs] ADD [ConsultancyBillingProfileId] smallint NULL;
-- GO

-- ALTER TABLE [Catalog].[LabTests] ADD [ConsultancyBillingProfileId] smallint NULL;
-- GO

ALTER TABLE [Staff].[Users] ADD [ConsultancyBillingProfileId] smallint NULL;
GO

ALTER TABLE [Catalog].[Labs] ADD [ResultingGroupName] VARCHAR(40) NULL;
GO

UPDATE [Catalog].[Labs] SET [ResultingGroupName] = [ReqPrintName] WHERE [IsAuxProcedure] = 0 AND [IsActive] = 1
GO

ALTER TABLE [Staff].[Users] ADD [UserGroupName] varchar(40) DEFAULT NULL
GO

-- PatientLabOrders

EXECUTE sys.sp_rename @objname = N'[PROE].[PatientLabOrders].[RegisteredMemberId]', @newname = N'temp_1', @objtype = 'COLUMN'
GO
EXECUTE sys.sp_rename @objname = N'[PROE].[PatientLabOrders].[SubOrderTrackingId]', @newname = N'temp_2', @objtype = 'COLUMN'
GO
EXECUTE sys.sp_rename @objname = N'[PROE].[PatientLabOrders].[temp_1]', @newname = N'CustomerId', @objtype = 'COLUMN'
GO
EXECUTE sys.sp_rename @objname = N'[PROE].[PatientLabOrders].[temp_2]', @newname = N'AssociateLabAccessionId', @objtype = 'COLUMN'
GO

-- ?? drop requestinglabid

ALTER TABLE [PROE].[PatientLabOrders] DROP CONSTRAINT [FK_REQLABS_ORDERS]
GO

-- add columns

ALTER TABLE [PROE].[PatientLabOrders] ADD [RowGuid] UNIQUEIDENTIFIER NOT NULL DEFAULT NEWSEQUENTIALID() ROWGUIDCOL
GO

ALTER TABLE [PROE].[PatientLabOrders] ADD [IsHidden] BIT NOT NULL DEFAULT 0
GO

ALTER TABLE [PROE].[PatientLabOrders] ADD [IsNonFungibleOrder] BIT NOT NULL DEFAULT 0
GO

ALTER TABLE [PROE].[PatientLabOrders] ADD [HealthPackageId] SMALLINT NULL
GO

ALTER TABLE [PROE].[PatientLabOrders] ADD [CorporateClientId] SMALLINT NULL
GO

ALTER TABLE [PROE].[PatientLabOrders] ADD[CorporateBillSettlementId] INT NULL
GO

ALTER TABLE [PROE].[PatientLabOrders] ADD [AffiliateId] SMALLINT NULL
GO

ALTER TABLE [PROE].[PatientLabOrders] ADD [AssociateLabId] SMALLINT NULL
GO

-- indices

CREATE INDEX [IX_PatientLabOrders_Corporate] ON [PROE].[PatientLabOrders] ([CorporateClientId])
go

CREATE INDEX [IX_PatientLabOrders_Settlement] ON [PROE].[PatientLabOrders] ([CorporateBillSettlementId])
go

CREATE INDEX [IX_PatientLabOrders_Customer] ON [PROE].[PatientLabOrders] ([CustomerId])
go

CREATE INDEX [IX_PatientLabOrders_Affiliate] ON [PROE].[PatientLabOrders] ([AffiliateId])
go

CREATE INDEX [IX_PatientLabOrders_Associate] ON [PROE].[PatientLabOrders] ([AssociateLabId])
go

CREATE INDEX [IX_PatientLabOrders_Associate_Accession] ON [PROE].[PatientLabOrders] ([AssociateLabId],[AssociateLabAccessionId])
go

ALTER TABLE [PROE].[PatientLabOrders] ADD CONSTRAINT [AK_PatientLabOrders_RowGuid] UNIQUE ([RowGuid])
go



-- insert repartition schemes/functions


-- billing

-- Table ConsultancyBilling.BillingProfiles

CREATE TABLE [ConsultancyBilling].[BillingProfiles]
(
 [Id] Smallint IDENTITY(101,1) NOT NULL,
 [Name] Varchar(120) NOT NULL,
 [IsActive] Bit DEFAULT 0 NOT NULL,
 [Remarks] Text NULL,
 [CreatedOn] Smalldatetime DEFAULT GETDATE() NOT NULL,
 [LastUpdated] Smalldatetime NULL,
 [RowGuid] Uniqueidentifier DEFAULT NEWSEQUENTIALID() ROWGUIDCOL NOT NULL
)
ON [FG_Static]
go

-- Create indexes for table ConsultancyBilling.BillingProfiles

CREATE INDEX [IX_BillingProfiles] ON [ConsultancyBilling].[BillingProfiles] ([IsActive],[Name])
go

-- Add keys for table ConsultancyBilling.BillingProfiles

ALTER TABLE [ConsultancyBilling].[BillingProfiles] ADD CONSTRAINT [PK_BillingProfiles] PRIMARY KEY ([Id])
 ON [FG_Static]
go

ALTER TABLE [ConsultancyBilling].[BillingProfiles] ADD CONSTRAINT [AK_BillingProfiles_Name] UNIQUE ([Name])
 ON [FG_Static]
go

ALTER TABLE [ConsultancyBilling].[BillingProfiles] ADD CONSTRAINT [AK_BillingProfiles_RowGuid] UNIQUE ([RowGuid])
go

-- Table ConsultancyBilling.BillingProfileDetails

CREATE TABLE [ConsultancyBilling].[BillingProfileDetails]
(
 [Id] Int IDENTITY(101,1) NOT NULL,
 [ProfileId] Smallint NOT NULL,
 [BillingMode] Tinyint DEFAULT 0 NOT NULL,
 [Percentage] Float DEFAULT 0.00 NOT NULL,
 [FlatAmount] Smallmoney DEFAULT 0.00 NOT NULL,
 [LabId] Smallint NULL,
 [LabTestId] Smallint NULL,
 [CreatedOn] Smalldatetime DEFAULT GETDATE() NOT NULL,
 [LastUpdated] Smalldatetime NULL,
 [RowGuid] Uniqueidentifier DEFAULT NEWSEQUENTIALID() ROWGUIDCOL NOT NULL
)
ON [FG_Static]
go

-- Create indexes for table ConsultancyBilling.BillingProfileDetails

CREATE INDEX [IX_BillingProfileDetails_Profile] ON [ConsultancyBilling].[BillingProfileDetails] ([ProfileId])
go

-- Add keys for table ConsultancyBilling.BillingProfileDetails

ALTER TABLE [ConsultancyBilling].[BillingProfileDetails] ADD CONSTRAINT [PK_BillingProfileDetails] PRIMARY KEY ([Id])
go

ALTER TABLE [ConsultancyBilling].[BillingProfileDetails] ADD CONSTRAINT [AK_BillingProfileDetails_RowGuid] UNIQUE ([RowGuid])
go

-- Table ConsultancyBilling.BillableServices

CREATE TABLE [ConsultancyBilling].[BillableServices]
(
 [Id] Bigint IDENTITY(1,1) NOT NULL,
 [ServiceDate] Date DEFAULT GETDATE() NOT NULL,
 [ConsultantId] Smallint NOT NULL,
 [InvoiceId] Bigint NOT NULL,
 [ResultBundleId] Bigint NOT NULL,
 [BillingProfileId] Smallint NOT NULL,
 [OrderedTestId] Bigint NOT NULL,
 [ServiceName] Varchar(120) NULL,
 [ServiceListPrice] Smallmoney NOT NULL,
 [AmountPayable] Smallmoney NOT NULL,
 [BillingMode] Tinyint DEFAULT 0 NOT NULL,
 [IncentiveAmount] Float DEFAULT 0.00 NOT NULL,
 [CreatedOn] Smalldatetime DEFAULT GETDATE() NOT NULL
)
--ON [PS_OLTP_Date_13_33]([CreatedOn])
go

-- Create indexes for table ConsultancyBilling.BillableServices

CREATE INDEX [IX_BillableServices] ON [ConsultancyBilling].[BillableServices] ([ServiceDate],[ConsultantId])
go

CREATE INDEX [IX_BillableServices_Invoice] ON [ConsultancyBilling].[BillableServices] ([InvoiceId])
go

CREATE INDEX [IX_BillableServices_Consultant] ON [ConsultancyBilling].[BillableServices] ([ConsultantId])
go

CREATE INDEX [IX_BillableServices_Date] ON [ConsultancyBilling].[BillableServices] ([ServiceDate])
go

CREATE UNIQUE INDEX [AK_BillableServices_OrderedTest] ON [ConsultancyBilling].[BillableServices] ([OrderedTestId])
go

-- Add keys for table ConsultancyBilling.BillableServices

ALTER TABLE [ConsultancyBilling].[BillableServices] ADD CONSTRAINT [PK_BillableServices] PRIMARY KEY ([Id])
go
