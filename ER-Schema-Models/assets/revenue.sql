-- 6 months
SELECT
	CONVERT ( VARCHAR, CAST ( COUNT ( InvoiceMaster.InvoiceId ) / 6 AS INT ), 1 ) NumPatientsAvg,
	FORMAT( CAST ( SUM ( InvoiceMaster.GrossPayable ) AS MONEY ), 'N0', 'en-in') GrossTotal,
	FORMAT( CAST ( SUM ( InvoiceMaster.NetPayable ) AS MONEY ), 'N0', 'en-in') AS NetTotal,
	FORMAT( CAST ( SUM ( InvoiceMaster.GrossPayable ) / 6 AS MONEY ), 'N0', 'en-in')  AS GrossMonth,
	FORMAT( CAST ( SUM ( InvoiceMaster.NetPayable ) / 6 AS MONEY ), 'N0', 'en-in') AS NetMonth
FROM
	Finances.InvoiceMaster
WHERE
	InvoiceMaster.DateCreated >= DATEADD( MONTH, - 6, GETDATE() )


-- 1 year
SELECT
	CONVERT ( VARCHAR, CAST ( COUNT ( InvoiceMaster.InvoiceId ) / 12 AS INT ), 1 ) NumPatientsAvg,
	FORMAT( CAST ( SUM ( InvoiceMaster.GrossPayable ) AS MONEY ), 'N0', 'en-in') GrossTotal,
	FORMAT( CAST ( SUM ( InvoiceMaster.NetPayable ) AS MONEY ), 'N0', 'en-in') AS NetTotal,
	FORMAT( CAST ( SUM ( InvoiceMaster.GrossPayable ) / 12 AS MONEY ), 'N0', 'en-in')  AS GrossMonth,
	FORMAT( CAST ( SUM ( InvoiceMaster.NetPayable ) / 12 AS MONEY ), 'N0', 'en-in') AS NetMonth
FROM
	Finances.InvoiceMaster
WHERE
	InvoiceMaster.DateCreated >= DATEADD( MONTH, - 12, GETDATE() )


-- 2 years
SELECT
	CONVERT ( VARCHAR, CAST ( COUNT ( InvoiceMaster.InvoiceId ) / 24 AS INT ), 1 ) NumPatientsAvg,
	FORMAT( CAST ( SUM ( InvoiceMaster.GrossPayable ) AS MONEY ), 'N0', 'en-in') GrossTotal,
	FORMAT( CAST ( SUM ( InvoiceMaster.NetPayable ) AS MONEY ), 'N0', 'en-in') AS NetTotal,
	FORMAT( CAST ( SUM ( InvoiceMaster.GrossPayable ) / 24 AS MONEY ), 'N0', 'en-in')  AS GrossMonth,
	FORMAT( CAST ( SUM ( InvoiceMaster.NetPayable ) / 24 AS MONEY ), 'N0', 'en-in') AS NetMonth
FROM
	Finances.InvoiceMaster
WHERE
	InvoiceMaster.DateCreated >= DATEADD( MONTH, - 24, GETDATE() )