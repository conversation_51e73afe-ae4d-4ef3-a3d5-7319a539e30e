USE [LabMaestro];
GO
SET  ANSI_NULLS ON;
GO
SET  QUOTED_IDENTIFIER ON;
GO

CREATE PROCEDURE [PROE].[SP_CreateNewLabOrderFullEx] @wfStage                       TINYINT
                                                    ,@refId                         INT = NULL
                                                    ,@refDisallow                   BIT
                                                    ,@uid                           SMALLINT
                                                    ,@shiftId                       INT
                                                    ,@reqLabId                      INT = NULL
                                                    ,@title                         VARCHAR(20) = NULL
                                                    ,@fname                         VARCHAR(120)
                                                    ,@lname                         VARCHAR(120) = NULL
                                                    ,@sex                           TINYINT
                                                    ,@age                           VARCHAR(20) = NULL
                                                    ,@dob                           DATE = NULL
                                                    ,@phone                         VARCHAR(20) = NULL
                                                    ,@email                         VARCHAR(40) = NULL
                                                    ,@emailResult                   BIT
                                                    ,@refUnk                        BIT
                                                    ,@refCustName                   VARCHAR(160) = NULL
                                                    ,@notes                         VARCHAR(160) = NULL
                                                    ,@webToken                      VARCHAR(8) = NULL
                                                    ,@regMemberId                   BIGINT = NULL
                                                    ,@subTrackingId                 VARCHAR(40) = NULL
                                                    ,@subIsExternal                 BIT
                                                    ,@isHidden                      BIT
                                                    ,@isNonFungible                 BIT
                                                    ,@healthPackageId               SMALLINT = NULL
                                                    ,@corporateClientId             SMALLINT = NULL
                                                    ,@corporateBillSettlementId     INT = NULL
                                                    ,@affiliateId                   SMALLINT = NULL
                                                    ,@associateLabId                SMALLINT = NULL
                                                    ,@releaseFlag                   TINYINT = 0
                                                    ,@newOrderId                    BIGINT OUTPUT
  WITH
  EXEC AS CALLER
AS
BEGIN
  SET  XACT_ABORT ON
  SET  NOCOUNT ON

  BEGIN TRANSACTION
  DECLARE @seqCounter   INT
  SET @seqCounter = NEXT VALUE FOR [SEQ_LabOrders]

  DECLARE @cutoff   INT
  SET @cutoff     = 99

  DECLARE @prefix   CHAR(1)
  DECLARE @letter   CHAR(1)
  DECLARE @num   INT

  SET @prefix     = ''
  SET @letter     = CHAR(ASCII('A') + ((@seqCounter / @cutoff) % 26))
  SET @num        = (@seqCounter % @cutoff) + 1

  DECLARE @orderNum   VARCHAR(8)
  SET @orderNum   = LTRIM(RTRIM(@prefix + @letter + CAST(@num AS CHAR(4))))

  INSERT INTO PROE.PatientLabOrders
	 (
     OrderId
    ,OrderDateTime
    ,WorkflowStage
    ,LastModified
    ,IsCancelled
    ,ReferrerId
    ,DisallowReferral
    ,OrderingUserId
    ,WorkShiftId
    ,RequestingLabId
    ,Title
    ,FirstName
    ,LastName
    ,Sex
    ,Age
    ,DoB
    ,PhoneNumber
    ,EmailAddress
    ,EmailTestResults
    ,IsReferrerUnknown
    ,ReferrerCustomName
    ,OrderNotes
    ,WebAccessToken
    ,CustomerId
    ,AssociateLabAccessionId
    ,IsExternalSubOrder
    ,MirrorFlag
    ,IsHidden
    ,IsNonFungibleOrder
    ,HealthPackageId
    ,CorporateClientId
    ,CorporateBillSettlementId
    ,AffiliateId
    ,AssociateLabId
    ,RegisteredMemberId
    ,SubOrderTrackingId
    ,ResultsReleaseFlag
	)
  VALUES
  (
     @orderNum  -- OrderId - varchar(8)
    ,GETDATE() -- OrderDateTime - smalldatetime
    ,@wfStage   -- WorkflowStage - tinyint
    ,GETDATE() -- LastModified - smalldatetime
    ,0  -- IsCancelled - bit
    ,@refId -- ReferrerId - int
    ,@refDisallow  -- DisallowReferral - bit
    ,@uid -- OrderingUserId - smallint
    ,@shiftId -- WorkShiftId - int
    ,@reqLabId -- RequestingLabId - int
    ,@title -- Title - varchar(20)
    ,@fname  -- FirstName - varchar(120)
    ,@lname -- LastName - varchar(120)
    ,@sex   -- Sex - tinyint
    ,@age -- Age - varchar(20)
    ,@dob -- DoB - date
    ,@phone -- PhoneNumber - varchar(20)
    ,@email -- EmailAddress - varchar(40)
    ,@emailResult  -- EmailTestResults - bit
    ,@refUnk  -- IsReferrerUnknown - bit
    ,@refCustName -- ReferrerCustomName - varchar(160)
    ,@notes -- OrderNotes - varchar(160)
    ,@webToken -- WebAccessToken - varchar(8)
    ,@regMemberId -- CustomerId - bigint
    ,@subTrackingId -- AssociateLabAccessionId - varchar(40)
    ,@subIsExternal  -- IsExternalSubOrder - bit
    ,0 -- MirrorFlag - tinyint
    ,@isHidden -- IsHidden - bit
    ,@isNonFungible -- IsNonFungibleOrder - bit
    ,@healthPackageId -- HealthPackageId - smallint
    ,@corporateClientId -- CorporateClientId - smallint
    ,@corporateBillSettlementId -- CorporateBillSettlementId - int
    ,@affiliateId -- AffiliateId - smallint
    ,@associateLabId -- AssociateLabId - smallint
    ,@regMemberId -- RegisteredMemberId - bigint
    ,@subTrackingId -- SubOrderTrackingId - varchar(40)
    ,@releaseFlag -- ResultsReleaseFlag - tinyint
  )

  SELECT @newOrderId = SCOPE_IDENTITY()

  COMMIT TRANSACTION
END;






ALTER TABLE [OuterOMS].[AssociateOrganizations] ADD [OverrideDuesCheck] bit DEFAULT 1 NOT NULL;
ALTER TABLE [OuterOMS].[AssociateOrganizations] ADD [CanViewFinancials] bit DEFAULT 0 NOT NULL;
ALTER TABLE [OuterOMS].[AssociateOrganizations] ADD [DiscountLevel] float DEFAULT 0.0 NOT NULL;



CREATE PROCEDURE Marketing.SP_AssignCorporateClientToInvoices
	@invoiceIdList NVARCHAR ( MAX ),-- Comma-separated list of InvoiceId values
	@corpClientId SMALLINT -- New CorporateClientId value
AS BEGIN

	SET NOCOUNT ON;
	DECLARE @query NVARCHAR ( MAX );

	SET @query = '
	UPDATE PatientLabOrders
	SET CorporateClientId = @corpClientId
	WHERE InvoiceId IN (' + @invoiceIdList + ')
	';

	EXEC sp_executesql @query, N'@corpClientId SMALLINT', @corpClientId;
END;


CREATE PROCEDURE PROE.SP_CustomerIdToInvoices
	@invoiceIdList NVARCHAR ( MAX ),-- Comma-separated list of InvoiceId values
	@corpClientId SMALLINT -- New CorporateClientId value
AS BEGIN

	SET NOCOUNT ON;
	DECLARE @query NVARCHAR ( MAX );

	SET @query = '
	UPDATE PatientLabOrders
	SET CorporateClientId = @corpClientId
	WHERE InvoiceId IN (' + @invoiceIdList + ')
	';

	EXEC sp_executesql @query, N'@corpClientId SMALLINT', @corpClientId;
END;


CREATE PROCEDURE PROE.SP_AssignCustomerIdToInvoices
	@invoiceIdList NVARCHAR ( MAX ),-- Comma-separated list of InvoiceId values
	@custId BIGINT -- New CorporateClientId value
AS BEGIN

	SET NOCOUNT ON;
	DECLARE @query NVARCHAR ( MAX );

	SET @query = '
	UPDATE PatientLabOrders
	SET CustomerId = @custId
	WHERE InvoiceId IN (' + @invoiceIdList + ')
	';

	EXEC sp_executesql @query, N'@custId BIGINT', @custId;
END;





CREATE PROCEDURE [Marketing].[SP_GetActiveCorporateClients]
AS
BEGIN

	SET NOCOUNT ON

	SELECT
		 c.Id
		,c.IsActive
		,c.UID
		,c.[Name]
		,c.Address
		,c.VatNumber
		,c.ContactPerson
		,c.Phone
		,c.Email
		,c.WebAccess
		,c.[Login]
		,c.PassHash
		,c.ClientGroupId
		,g.[Name] AS ClientGroupName
		,c.CLV
		,c.NumOrders
		,c.MouStartDate
		,c.MouExpiryDate
		,c.CreatedOn
		,c.LastUpdated
	FROM
		Marketing.[CorporateClients] c
		INNER JOIN Marketing.[CorporateClientGroups] g ON c.ClientGroupId = g.Id
	WHERE
		c.IsActive = 1
		AND g.IsActive = 1

END

CREATE PROCEDURE [Marketing].[SP_GetAllCorporateClients]
AS
BEGIN

	SET NOCOUNT ON

	SELECT
		 c.Id
		,c.IsActive
		,c.UID
		,c.[Name]
		,c.Address
		,c.VatNumber
		,c.ContactPerson
		,c.Phone
		,c.Email
		,c.WebAccess
		,c.[Login]
		,c.PassHash
		,c.ClientGroupId
		,g.[Name] AS ClientGroupName
		,c.CLV
		,c.NumOrders
		,c.MouStartDate
		,c.MouExpiryDate
		,c.CreatedOn
		,c.LastUpdated
	FROM
		Marketing.[CorporateClients] c
		INNER JOIN Marketing.[CorporateClientGroups] g ON c.ClientGroupId = g.Id

END








CREATE PROCEDURE [Marketing].[SP_GetActiveAffiliates]
AS
BEGIN

	SET NOCOUNT ON

	SELECT Id, IsActive, UPIN, [Name], IdentifyingTag, MobilePhone, Email, WebAccess, [Login], PassHash, CLV, NumOrders, MarketingExecId, CreatedOn, LastUpdated, RowGuid
	FROM Marketing.[Affiliates]
	WHERE IsActive = 1

END


CREATE PROCEDURE [Marketing].[SP_GetAllAffiliates]
AS
BEGIN

	SET NOCOUNT ON

	SELECT Id, IsActive, UPIN, [Name], IdentifyingTag, MobilePhone, Email, WebAccess, [Login], PassHash, CLV, NumOrders, MarketingExecId, CreatedOn, LastUpdated, RowGuid
	FROM Marketing.[Affiliates]

END


CREATE PROCEDURE [OuterOMS].[SP_GetActiveAssociateOrganizations]
AS
BEGIN

  SET NOCOUNT ON

  SELECT
    Id,
    IsActive,
    Name,
    Address,
    NumOrders,
    CLV,
    ContactName,
    Phone,
    Email,
    Remarks,
    CreatedOn,
    LastUpdated,
    CanViewFinancials,
    OverrideDuesCheck,
    DiscountLevel 
  FROM
    OuterOMS.AssociateOrganizations 
  WHERE
    IsActive = 1

END


CREATE PROCEDURE [OuterOMS].[SP_GetAllAssociateOrganizations]
AS
BEGIN

  SET NOCOUNT ON

  SELECT
    Id,
    IsActive,
    Name,
    Address,
    NumOrders,
    CLV,
    ContactName,
    Phone,
    Email,
    Remarks,
    CreatedOn,
    LastUpdated,
    CanViewFinancials,
    OverrideDuesCheck,
    DiscountLevel 
  FROM
    OuterOMS.AssociateOrganizations

END




ALTER TABLE [Finances].[InvoiceTransactions] ADD [NonCashAmount] money DEFAULT 0.0 NOT NULL
GO

ALTER TABLE [Finances].[InvoiceTransactions] ADD [PaymentMethod] tinyint DEFAULT 0 NOT NULL
GO

ALTER TABLE [Finances].[InvoiceTransactions] ADD [PaymentSource] text NULL
GO

ALTER TABLE [Finances].[InvoiceTransactions] ADD [PaymentReference] text NULL
GO


CREATE PROCEDURE [Finances].[SP_GetTransactionsInInvoiceDetailedEx]
	@InvoiceId bigint
AS
	SET NOCOUNT ON

	SELECT
		pu.DisplayName AS StaffName,
		au.DisplayName AS AuthorizerName,
		tx.TxTime,
		tx.TxType,
		tx.TxFlag,
		tx.TxAmount,
		tx.UserRemarks,
		tx.NonCashAmount,
		tx.PaymentMethod,
		tx.PaymentSource,
		tx.PaymentReference
	FROM
		Finances.InvoiceTransactions AS tx
		INNER JOIN Finances.InvoiceMaster AS inv ON tx.InvoiceId = inv.InvoiceId
		LEFT JOIN Staff.Users AS pu ON tx.PerformingUserId = pu.Id
		LEFT JOIN Staff.Users AS au ON tx.AuthorizingUserId = au.Id
	WHERE
		inv.InvoiceId = @InvoiceId
	ORDER BY
		tx.Id DESC



CREATE PROCEDURE [Finances].[SP_GetTransactionsInInvoiceDetailedEx]
	@InvoiceId bigint
AS
	SET NOCOUNT ON

	SELECT
		pu.DisplayName AS StaffName,
		au.DisplayName AS AuthorizerName,
		tx.TxTime,
		tx.TxType,
		tx.TxFlag,
		tx.TxAmount,
		tx.UserRemarks,
		tx.NonCashAmount,
		tx.PaymentMethod,
		tx.PaymentSource,
		tx.PaymentReference
	FROM
		Finances.InvoiceTransactions AS tx
		INNER JOIN Finances.InvoiceMaster AS inv ON tx.InvoiceId = inv.InvoiceId
		LEFT JOIN Staff.Users AS pu ON tx.PerformingUserId = pu.Id
		LEFT JOIN Staff.Users AS au ON tx.AuthorizingUserId = au.Id
	WHERE
		inv.InvoiceId = @InvoiceId
	ORDER BY
		tx.Id DESC




CREATE PROCEDURE [Finances].[SP_CreateNewTransactionEx]
	@invoiceId          BIGINT,
	@userId             SMALLINT = NULL,
	@authorizerId       SMALLINT = NULL,
	@shiftId            INT = NULL,
	@txType             TINYINT,
	@txFlag             TINYINT,
	@txAmount           MONEY,
	@ipAddr             INT = NULL,
	@remarks            VARCHAR (160) = NULL,
	@nonCashAmount      MONEY,
	@pmtMethod			TINYINT,
	@pmtSrc             TEXT = NULL,
	@pmtRef             TEXT = NULL,
	@txId               BIGINT OUTPUT
AS
	SET XACT_ABORT ON

	SET NOCOUNT ON

	BEGIN TRANSACTION

		INSERT INTO [Finances].[InvoiceTransactions](
			InvoiceId,
			PerformingUserId,
			WorkShiftId,
			AuthorizingUserId,
			TxTime,
			TxType,
			TxFlag,
			TxAmount,
			UserIpAddress,
			UserRemarks,
			NonCashAmount,
			PaymentMethod,
			PaymentSource,
			PaymentReference
		)
		VALUES
		(
			@invoiceId, @userId, @authorizerId, @shiftId, CURRENT_TIMESTAMP, @txType, @txFlag, @txAmount, @ipAddr, @remarks, @nonCashAmount, @pmtMethod, @pmtSrc, @pmtRef
		)

		SELECT @txId = SCOPE_IDENTITY()

	COMMIT TRANSACTION



ALTER TABLE [Finances].[WorkShifts] ADD [NonCashAmount] money DEFAULT 0.0 NOT NULL
GO

ALTER TABLE [PROE].[HeldLabOrders] ADD [CustomerId] bigint NULL
GO






CREATE PROCEDURE [Finances].[SP_GetTransactionsInShiftEx]
	@ShiftId int
AS
	SET NOCOUNT ON

	SELECT
		inv.InvoiceId,
		tx.TxTime,
		tx.TxType,
		tx.TxFlag,
		tx.TxAmount,
		tx.NonCashAmount,
		tx.PaymentMethod
	FROM
		Finances.InvoiceTransactions AS tx
		INNER JOIN Finances.WorkShifts AS shft ON ( tx.WorkShiftId = shft.Id )
		INNER JOIN Finances.InvoiceMaster AS inv ON ( tx.InvoiceId = inv.InvoiceId )
		INNER JOIN PROE.PatientLabOrders AS ord ON ( inv.InvoiceId = ord.InvoiceId )
	WHERE
		shft.Id = @ShiftId
	ORDER BY
		tx.Id ASC








CREATE PROCEDURE [TestResults].[SP_GetDiscreteResultLineItemsInBundle]
	@bundleId BIGINT
WITH
EXEC AS CALLER
AS
	SET  XACT_ABORT ON

	SET  NOCOUNT ON

	SELECT
		drl.Id AS LineItemId,
		drl.OrderedTestId,
		ot.LabTestId,
		tst.TestSKU,
		drl.Parameter,
		drl.Result,
		drl.Units,
		drl.ReferenceRange,
		drl.Flag,
		drl.SortOrder,
		drl.IndentLevel,
		drl.IsResultableItem
	FROM
		TestResults.DiscreteResultLineItems AS drl
		INNER JOIN PROE.OrderedTests AS ot ON drl.OrderedTestId = ot.Id
		INNER JOIN [Catalog].LabTests AS tst ON ot.LabTestId = tst.Id
	WHERE
		drl.ResultBundleId = @bundleId
	ORDER BY
		drl.SortOrder ASC







ALTER TABLE [PROE].[PatientLabOrders] ADD [ResultsReleaseFlag] tinyint DEFAULT 0 NOT NULL













CREATE PROCEDURE [CRM].[SP_FindCustomersByNames]
  @firstName AS varchar (120),
  @lastName AS varchar (120)
WITH EXEC AS CALLER
AS
BEGIN

	SET  NOCOUNT ON

	SELECT
		cust.Id,
		cust.IsActive,
		cust.UPIN,
		cust.Title,
		cust.FirstName,
		cust.LastName,
		cust.CompanyName,
		cust.Sex,
		cust.Age,
		cust.DoB,
		cust.Phone,
		cust.Phone2,
		cust.Email,
		cust.WebAccess,
		cust.Login,
		cust.PassHash,
		cust.PassportNumber,
		cust.DriversLicense,
		cust.NationalIdNumber,
		cust.CLV,
		cust.NumOrders,
		cust.CorporateClientId,
		corp.Name AS CorporateClientName,
		cust.CreatedOn,
		cust.LastSeenOn,
		cust.EnrolledOn
	FROM
		CRM.Customers AS cust
		LEFT JOIN Marketing.CorporateClients AS corp ON cust.CorporateClientId = corp.Id
	WHERE
		cust.FirstName LIKE @firstName
		OR cust.LastName LIKE @lastName

END


CREATE PROCEDURE [CRM].[SP_FindCustomersByUPIN]
  @upin AS varchar(32)
WITH EXEC AS CALLER
AS
BEGIN
	SET  NOCOUNT ON

	SELECT
		cust.Id,
		cust.IsActive,
		cust.UPIN,
		cust.Title,
		cust.FirstName,
		cust.LastName,
		cust.CompanyName,
		cust.Sex,
		cust.Age,
		cust.DoB,
		cust.Phone,
		cust.Phone2,
		cust.Email,
		cust.WebAccess,
		cust.Login,
		cust.PassHash,
		cust.PassportNumber,
		cust.DriversLicense,
		cust.NationalIdNumber,
		cust.CLV,
		cust.NumOrders,
		cust.CorporateClientId,
		corp.Name AS CorporateClientName,
		cust.CreatedOn,
		cust.LastSeenOn,
		cust.EnrolledOn
	FROM
		CRM.Customers AS cust
		LEFT JOIN Marketing.CorporateClients AS corp ON cust.CorporateClientId = corp.Id
	WHERE
		cust.UPIN LIKE @upin

END



CREATE PROCEDURE [CRM].[SP_FindCustomersByEmail]
  @email AS varchar
WITH EXEC AS CALLER
AS
BEGIN

	SET  NOCOUNT ON

	SELECT
		cust.Id,
		cust.IsActive,
		cust.UPIN,
		cust.Title,
		cust.FirstName,
		cust.LastName,
		cust.CompanyName,
		cust.Sex,
		cust.Age,
		cust.DoB,
		cust.Phone,
		cust.Phone2,
		cust.Email,
		cust.WebAccess,
		cust.Login,
		cust.PassHash,
		cust.PassportNumber,
		cust.DriversLicense,
		cust.NationalIdNumber,
		cust.CLV,
		cust.NumOrders,
		cust.CorporateClientId,
		corp.Name AS CorporateClientName,
		cust.CreatedOn,
		cust.LastSeenOn,
		cust.EnrolledOn
	FROM
		CRM.Customers AS cust
		LEFT JOIN Marketing.CorporateClients AS corp ON cust.CorporateClientId = corp.Id
	WHERE
		cust.Email LIKE @email

END




CREATE PROCEDURE [CRM].[SP_FindCustomerByUPINExact]
  @upin AS varchar(32)
WITH EXEC AS CALLER
AS
BEGIN
	SET  NOCOUNT ON

	SELECT TOP 1
		cust.Id,
		cust.IsActive,
		cust.UPIN,
		cust.Title,
		cust.FirstName,
		cust.LastName,
		cust.CompanyName,
		cust.Sex,
		cust.Age,
		cust.DoB,
		cust.Phone,
		cust.Phone2,
		cust.Email,
		cust.WebAccess,
		cust.Login,
		cust.PassHash,
		cust.PassportNumber,
		cust.DriversLicense,
		cust.NationalIdNumber,
		cust.CLV,
		cust.NumOrders,
		cust.CorporateClientId,
		corp.Name AS CorporateClientName,
		cust.CreatedOn,
		cust.LastSeenOn,
		cust.EnrolledOn
	FROM
		CRM.Customers AS cust
		LEFT JOIN Marketing.CorporateClients AS corp ON cust.CorporateClientId = corp.Id
	WHERE
		cust.UPIN = @upin

END





CREATE NONCLUSTERED INDEX [IX_RESULT_BUNDLE_LASTUPDATE]
ON [TestResults].[ResultBundles] (
  [IsActive],
  [LastUpdated]
)



CREATE PROCEDURE [TestResults].[SP_GetCustomerLatestUpdatedResultBundles]
  @cutoffMinutes int
WITH EXEC AS CALLER
AS
BEGIN
	SET NOCOUNT ON

	SELECT
		rb.Id AS ResultBundleId,
		rb.LabId,
		rb.TestResultType AS ResultBundleResultType,
		rb.DisplayTitle AS ResultBundleTitle,
		rb.DateCreated AS ResultBundleDateCreated,
		rb.LastUpdated AS ResultBundleLastUpdated,
		rb.WorkflowStage AS ResultBundleWorkflowStage,
		rb.InvoiceId,
		ord.OrderId,
		ord.OrderDateTime,
		ord.IsCancelled AS OrderIsCancelled,
		ord.PhoneNumber AS OrderPhone,
		ord.EmailAddress AS OrderEmail,
		ord.WorkflowStage AS OrderWorkflowStage,
		ord.CustomerId,
		cust.IsActive AS CustomerIsActive,
		cust.UPIN AS CustomerUPIN,
		cust.FirstName AS CustomerFirstName,
		cust.LastName AS CustomerLastName,
		cust.Phone AS CustomerPhone,
		cust.Email AS CustomerEmail
	FROM
		TestResults.ResultBundles AS rb
		INNER JOIN PROE.PatientLabOrders AS ord ON rb.InvoiceId = ord.InvoiceId
		LEFT JOIN CRM.Customers AS cust ON ord.CustomerId = cust.Id
	WHERE
		rb.LastUpdated >= DATEADD( MINUTE, -@cutoffMinutes, GETDATE() )
		AND rb.IsActive = 1
END


CREATE PROCEDURE [CRM].[SP_GetFollowersForCustomer]
  @followeeId bigint
WITH EXEC AS CALLER
AS
BEGIN
	SET NOCOUNT ON

	SELECT
		follower.Id,
		follower.UPIN,
		follower.FirstName,
		follower.LastName,
		follower.Sex,
		follower.Phone,
		follower.Email,
		follower.Login,
		follower.LastSeenOn
	FROM
		CRM.Customers AS follower
		INNER JOIN CRM.TrackedCustomers AS trk ON follower.Id = trk.FollowerId
	WHERE
		trk.FolloweeId = @followeeId
		AND follower.IsActive = 1
END







CREATE PROCEDURE [CRM].[SP_GetCustomerTrackedLabOrders]
  @invoices AS VARCHAR(MAX)
WITH EXEC AS CALLER
AS
BEGIN
	SET NOCOUNT ON

	IF OBJECT_ID( 'tempdb..#tmp_invoices' ) IS NOT NULL
	BEGIN
		DROP TABLE #tmp_invoices
	END

	SELECT DISTINCT value INTO #tmp_invoices FROM STRING_SPLIT( @invoices, ',' )

	SELECT
		tlo.PatientLabOrderId,
		tlo.CustomerId,
		cust.UPIN,
		cust.FirstName,
		cust.LastName,
		cust.Sex,
		cust.Phone,
		cust.Email,
		cust.Login,
		cust.LastSeenOn
	FROM
		CRM.TrackedLabOrders AS tlo
		INNER JOIN CRM.Customers AS cust ON tlo.CustomerId = cust.Id
		INNER JOIN PROE.PatientLabOrders AS plo ON tlo.PatientLabOrderId = plo.InvoiceId
		INNER JOIN #tmp_invoices on #tmp_invoices.value = tlo.PatientLabOrderId
	WHERE
		cust.IsActive = 1
		AND plo.IsCancelled = 0
END









ALTER TABLE [CRM].[AuditLogs] ADD [OriginalCustomerId] bigint NULL

CREATE NONCLUSTERED INDEX [IX_AuditLogs_OriginalCustomer]
ON [CRM].[AuditLogs] (
  [OriginalCustomerId]
)














CREATE PROCEDURE [PROE].[SP_GetLabOrderFullDetails]
	@invoiceId AS bigint
WITH EXEC AS CALLER
AS
BEGIN
	SET NOCOUNT ON

	SELECT TOP(1)
		ord.InvoiceId,
		ord.OrderId,
		ord.OrderDateTime,
		ord.WorkflowStage,
		ord.LastModified,
		ord.IsCancelled,
		ord.Title,
		ord.FirstName,
		ord.LastName,
		ord.Sex,
		ord.Age,
		ord.DoB,
		ord.PhoneNumber,
		ord.EmailAddress,
		ord.EmailTestResults,
		ord.IsReferrerUnknown,
		ord.DisallowReferral,
		ord.ReferrerId,
		COALESCE ( ref.FullName, ord.ReferrerCustomName, '' ) AS ReferrerName,
		ord.OrderNotes,
		ord.WebAccessToken,
		ord.MirrorFlag,
		ord.FullName,
		ord.IsHidden,
		ord.IsNonFungibleOrder,
		ord.HealthPackageId,
		ord.ResultsReleaseFlag,
		inv.PaymentStatus,
		inv.GrossPayable,
		inv.DiscountAmount,
		inv.TaxAmount,
		inv.SurchargeAmount,
		inv.NetPayable,
		inv.PaidAmount,
		inv.DueAmount,
		inv.RefundAmount,
		ord.OrderingUserId,
		usr.DisplayName AS OrderingUserName,
		ord.WorkShiftId,
		ord.CustomerId,
		cust.UPIN AS CustomerUPIN,
		cust.Phone AS CustomerPhone,
		cust.Email AS CustomerEmail,
		ord.IsExternalSubOrder,
		--ord.RequestingLabId,
		ord.AssociateLabId,
		ord.AssociateLabAccessionId,
		assoc.Name AS AssociateLabName,
		assoc.Phone AS AssociateLabPhone,
		assoc.Email AS AssociateLabEmail,
		ord.CorporateClientId,
		corp.UID AS CorporateClientUID,
		corp.Name AS CorporateClientName,
		corp.Phone AS CorporateClientPhone,
		corp.Email AS CorporateClientEmail,
		ord.AffiliateId,
		aff.UPIN AS AffiliateUPIN,
		aff.Name AS AffiliateName
	FROM
		PROE.PatientLabOrders AS ord
		INNER JOIN Finances.InvoiceMaster AS inv ON ord.InvoiceId = inv.InvoiceId
		INNER JOIN Staff.Users usr ON ord.OrderingUserId = usr.Id
		LEFT OUTER JOIN OuterOMS.AssociateOrganizations AS assoc ON ord.AssociateLabId = assoc.Id
		LEFT OUTER JOIN Marketing.Affiliates AS aff ON ord.AffiliateId = aff.Id
		LEFT OUTER JOIN Marketing.CorporateClients AS corp ON ord.CorporateClientId = corp.Id
		LEFT OUTER JOIN CRM.Customers AS cust ON ord.CustomerId = cust.Id
		LEFT OUTER JOIN [Catalog].Referrers AS ref ON ord.ReferrerId = ref.Id
	WHERE
		ord.InvoiceId = @invoiceId

END




CREATE VIEW [ConsultancyBilling].[vw_ActiveBillableServices]
AS
SELECT
	svc.Id AS ServiceId,
	svc.ServiceDate,
	svc.CreatedOn AS ServiceCreatedOn,
	svc.LastUpdated AS ServiceLastUpdated,
	svc.ServiceName,
	svc.ServiceListPrice,
	svc.ConsultantId,
	doc.DisplayName AS ConsultantName,
	doc.DepartmentId,
	dept.Name AS DepartmentName,
	svc.InvoiceId,
	ord.OrderId,
	ord.OrderDateTime,
	ord.FullName AS PatientName,
	ord.Sex,
	ord.PhoneNumber,
	svc.ResultBundleId,
	svc.OrderedTestId,
	svc.AmountPayable,
	svc.BillingProfileId,
	bill.Name AS BillingProfileName,
	svc.BillingMode,
	svc.IncentiveAmount,
	ord.CustomerId,
	cust.IsActive AS CustomerIsActive,
	cust.UPIN AS CustomerUPIN,
	cust.Phone AS CustomerPhone,
	cust.Email AS CustomerEmail,
	cust.Login AS CustomerLogin,
	ord.CorporateClientId,
	corp.IsActive AS CorporateClientIsActive,
	corp.UID AS CorporateClientUID,
	corp.Name AS CorporateClientName,
	ord.AffiliateId,
	aff.IsActive AS AffiliateIsActive,
	aff.UPIN AS AffiliateUPIN,
	aff.Name AS AffiliateName,
	ord.AssociateLabId,
	assoc.IsActive AS AssociateLabIsActive,
	assoc.Name AS AssociateLabName,
	svc.EditedByUserId AS ServiceEditorUserId,
	editor.DisplayName AS ServiceEditorDisplayName,
	editor.UserName AS ServiceEditorLoginName,
	svc.Remarks
FROM
	ConsultancyBilling.BillableServices AS svc
	INNER JOIN Staff.Users AS doc ON svc.ConsultantId = doc.Id
	INNER JOIN PROE.PatientLabOrders AS ord ON svc.InvoiceId = ord.InvoiceId
	INNER JOIN TestResults.ResultBundles AS bun ON svc.ResultBundleId = bun.Id
	INNER JOIN PROE.OrderedTests AS test ON svc.OrderedTestId = test.Id
	INNER JOIN ConsultancyBilling.BillingProfiles AS bill ON svc.BillingProfileId = bill.Id
	INNER JOIN Staff.Departments AS dept ON doc.DepartmentId = dept.Id
	LEFT JOIN CRM.Customers AS cust ON ord.CustomerId = cust.Id
	LEFT JOIN Marketing.CorporateClients AS corp ON ord.CorporateClientId = corp.Id
	LEFT JOIN Marketing.Affiliates AS aff ON ord.AffiliateId = aff.Id
	LEFT JOIN OuterOMS.AssociateOrganizations AS assoc ON ord.AssociateLabId = assoc.Id
	LEFT JOIN Staff.Users AS editor ON svc.EditedByUserId = editor.Id
WHERE
	ord.IsCancelled = 0
	AND test.IsCancelled = 0
	AND bun.IsActive = 1
	AND doc.IsActive = 1
	AND bill.IsActive = 1
GO





-------------------------- noncash



CREATE PROCEDURE [Finances].[SP_GetAllTransactionsForUserDateRangeEx]
  @userId SMALLINT, @startDate smalldatetime, @endDate smalldatetime
WITH EXEC AS CALLER
AS
  SET NOCOUNT ON

  SELECT
    tx.PerformingUserId,
    usr.DisplayName AS UserName,
    rol.Name AS RoleName,
    tx.InvoiceId,
    ord.OrderId,
    ord.OrderDateTime,
    ord.IsCancelled AS OrderCancelled,
    tx.TxType,
    tx.TxFlag,
    tx.TxTime,
    tx.TxAmount,
    tx.NonCashAmount,
    tx.PaymentMethod,
    tx.PaymentSource,
    tx.PaymentReference,
    tx.UserRemarks AS TxRemarks,
    inv.GrossPayable,
    inv.NetPayable,
    inv.PaidAmount,
    inv.DueAmount,
    COALESCE ( ref.[FullName], ord.[ReferrerCustomName], '' ) AS ReferrerName
  FROM
    PROE.PatientLabOrders ord
    INNER JOIN Finances.InvoiceTransactions tx ON ord.InvoiceId = tx.InvoiceId
    INNER JOIN Staff.Users usr ON tx.PerformingUserId = usr.Id
    INNER JOIN RBAC.Roles rol ON usr.RoleId = rol.Id
    INNER JOIN Finances.InvoiceMaster inv ON tx.InvoiceId = inv.InvoiceId
    LEFT OUTER JOIN CATALOG.Referrers ref ON ord.ReferrerId = ref.Id
  WHERE
    tx.TxTime BETWEEN @startDate AND @endDate
    AND tx.PerformingUserId = @userId
GO


CREATE PROCEDURE [Finances].[SP_GetAllNonMatchingTransactionsInDateRangeEx]
  @txType tinyint, @dtFrom smalldatetime, @dtTo smalldatetime
WITH EXEC AS CALLER
AS
  SET  NOCOUNT ON

  SELECT
    tx.InvoiceId,
    ord.OrderId,
    ord.OrderDateTime,
    ref.FullName AS ReferredBy,
    ord.ReferrerId,
    usr.DisplayName AS PerformedBy,
    tx.PerformingUserId,
    tx.WorkShiftId,
    tx.TxTime,
    tx.TxType,
    tx.TxFlag,
    tx.TxAmount,
    tx.UserIpAddress,
    tx.NonCashAmount,
    tx.PaymentMethod,
    tx.PaymentSource,
    tx.PaymentReference,
    tx.UserRemarks AS TxRemarks,
    ord.IsCancelled AS InvoiceIsCancelled,
    inv.GrossPayable AS InvoiceGross,
    inv.DiscountAmount AS InvoiceDiscount,
    inv.NetPayable AS InvoicePayable,
    inv.PaidAmount AS InvoicePaid,
    inv.DueAmount AS InvoiceDue,
    inv.RefundAmount AS InvoiceRefund
  FROM
    Finances.InvoiceTransactions tx
    INNER JOIN Finances.InvoiceMaster inv ON tx.InvoiceId = inv.InvoiceId
    INNER JOIN Staff.Users usr ON tx.PerformingUserId = usr.Id
    INNER JOIN PROE.PatientLabOrders ord ON inv.InvoiceId = ord.InvoiceId
    LEFT OUTER JOIN Catalog.Referrers ref ON ord.ReferrerId = ref.Id
  WHERE
    tx.TxType <> @txType AND
    tx.TxTime BETWEEN @dtFrom AND @dtTo
  ORDER BY
    tx.InvoiceId
GO


CREATE PROCEDURE [Finances].[SP_GetAllTransactionsForDateRangeEx]
  @startDate smalldatetime, @endDate smalldatetime
WITH EXEC AS CALLER
AS
  SET NOCOUNT ON

  SELECT
    tx.PerformingUserId,
    usr.DisplayName AS UserName,
    rol.Name AS RoleName,
    tx.InvoiceId,
    ord.OrderId,
    ord.OrderDateTime,
    ord.IsCancelled AS OrderCancelled,
    tx.TxType,
    tx.TxFlag,
    tx.TxTime,
    tx.TxAmount,
    tx.NonCashAmount,
    tx.PaymentMethod,
    tx.PaymentSource,
    tx.PaymentReference,
    tx.UserRemarks AS TxRemarks,
    inv.GrossPayable,
    inv.NetPayable,
    inv.PaidAmount,
    inv.DueAmount,
    COALESCE ( ref.[FullName], ord.[ReferrerCustomName], '' ) AS ReferrerName
  FROM
    PROE.PatientLabOrders AS ord
    INNER JOIN Finances.InvoiceTransactions AS tx ON ord.InvoiceId = tx.InvoiceId
    INNER JOIN Staff.Users AS usr ON tx.PerformingUserId = usr.Id
    INNER JOIN RBAC.Roles AS rol ON usr.RoleId = rol.Id
    INNER JOIN Finances.InvoiceMaster AS inv ON tx.InvoiceId = inv.InvoiceId
    LEFT JOIN [Catalog].Referrers AS ref ON ord.ReferrerId = ref.Id
  WHERE
    tx.TxTime BETWEEN @startDate AND @endDate
GO





CREATE PROCEDURE [Finances].[SP_GetAllTransactionsForRoleDateRangeEx]
  @startDate smalldatetime, @endDate smalldatetime, @roleId smallint
WITH EXEC AS CALLER
AS
  SET NOCOUNT ON

  SELECT
    tx.PerformingUserId,
    usr.DisplayName AS UserName,
    rol.Name AS RoleName,
    tx.InvoiceId,
    ord.OrderId,
    ord.OrderDateTime,
    ord.IsCancelled AS OrderCancelled,
    tx.TxType,
    tx.TxFlag,
    tx.TxTime,
    tx.TxAmount,
    tx.NonCashAmount,
    tx.PaymentMethod,
    tx.PaymentSource,
    tx.PaymentReference,
    tx.UserRemarks AS TxRemarks,
    inv.GrossPayable,
    inv.NetPayable,
    inv.PaidAmount,
    inv.DueAmount,
    COALESCE ( ref.FullName, ord.ReferrerCustomName, '' ) AS ReferrerName
  FROM
    PROE.PatientLabOrders AS ord
    INNER JOIN Finances.InvoiceTransactions AS tx ON ord.InvoiceId = tx.InvoiceId
    INNER JOIN Staff.Users AS usr ON tx.PerformingUserId = usr.Id
    INNER JOIN RBAC.Roles AS rol ON usr.RoleId = rol.Id
    INNER JOIN Finances.InvoiceMaster AS inv ON tx.InvoiceId = inv.InvoiceId
    LEFT JOIN [Catalog].Referrers AS ref ON ord.ReferrerId = ref.Id
  WHERE
    tx.TxTime BETWEEN @startDate AND @endDate
    AND rol.Id = @roleId

GO



CREATE PROCEDURE [Finances].[SP_FindWorkshiftByDateRangeRolesEx]
	@dtStart smalldatetime,
	@dtEnd smalldatetime,
	@roleIds [PROE].[SmallIntListTableType] READONLY
AS
	SET NOCOUNT ON

	SELECT
		s.Id,
		s.UserId,
		u.DisplayName AS UserName,
		r.Name AS RoleName,
		s.IsClosed,
		s.StartTime,
		s.EndTime,
		s.NumOrders,
		s.AdditionalBalance,
		s.ReceiveAmount,
		s.NonCashAmount,
		s.DiscountAmount,
		s.DiscountRebateAmount,
		s.RefundAmount,
		s.FinalBalance,
		s.UserNotes
	FROM
		Finances.WorkShifts AS s
		INNER JOIN Staff.Users AS u ON s.UserId = u.Id
		INNER JOIN RBAC.Roles AS r ON u.RoleId = r.Id
	WHERE
		s.StartTime BETWEEN @dtStart AND @dtEnd
		AND r.Id IN ( ( SELECT n FROM @roleIds ) )

GO




CREATE PROCEDURE [Finances].[SP_FindWorkshiftByDateRangeEx]
	@dtStart smalldatetime,
	@dtEnd smalldatetime
AS
	SET NOCOUNT ON

	SELECT
		s.Id,
		s.UserId,
		u.DisplayName AS UserName,
		r.Name AS RoleName,
		s.IsClosed,
		s.StartTime,
		s.EndTime,
		s.NumOrders,
		s.AdditionalBalance,
		s.ReceiveAmount,
		s.NonCashAmount,
		s.DiscountAmount,
		s.DiscountRebateAmount,
		s.RefundAmount,
		s.FinalBalance,
		s.UserNotes
	FROM
		Finances.WorkShifts AS s
		INNER JOIN Staff.Users AS u ON s.UserId = u.Id
		INNER JOIN RBAC.Roles AS r ON u.RoleId = r.Id
	WHERE
		s.StartTime BETWEEN @dtStart AND @dtEnd
GO



ALTER TABLE [ConsultancyBilling].[BillableServices] ADD [EditedByUserId] smallint NULL
GO

ALTER TABLE [ConsultancyBilling].[BillableServices] ADD [LastUpdated] smalldatetime NULL
GO

ALTER TABLE [ConsultancyBilling].[BillableServices] ADD [Remarks] text NULL
GO






CREATE PROCEDURE [Finances].[SP_GetAllTransactionsForRoleDateRangeEx]
@startDate smalldatetime, @endDate smalldatetime, @roleId smallint
WITH EXEC AS CALLER
AS
	SET NOCOUNT ON

	SELECT
		tx.PerformingUserId,
		usr.DisplayName AS UserName,
		rol.Name AS RoleName,
		tx.InvoiceId,
		ord.OrderId,
		ord.OrderDateTime,
		ord.IsCancelled AS OrderCancelled,
		tx.TxType,
		tx.TxFlag,
		tx.TxTime,
		tx.TxAmount,
		tx.NonCashAmount,
		tx.PaymentMethod,
		tx.PaymentSource,
		tx.PaymentReference,
		tx.UserRemarks AS TxRemarks,
		inv.GrossPayable,
		inv.NetPayable,
		inv.PaidAmount,
		inv.DueAmount,
		COALESCE ( ref.FullName, ord.ReferrerCustomName, '' ) AS ReferrerName
	FROM
		PROE.PatientLabOrders AS ord
		INNER JOIN Finances.InvoiceTransactions AS tx ON ord.InvoiceId = tx.InvoiceId
		INNER JOIN Staff.Users AS usr ON tx.PerformingUserId = usr.Id
		INNER JOIN RBAC.Roles AS rol ON usr.RoleId = rol.Id
		INNER JOIN Finances.InvoiceMaster AS inv ON tx.InvoiceId = inv.InvoiceId
		LEFT JOIN [Catalog].Referrers AS ref ON ord.ReferrerId = ref.Id
	WHERE
  		tx.TxTime BETWEEN @startDate AND @endDate
  		AND rol.Id = @roleId
GO




CREATE PROCEDURE [Finances].[SP_GetAllTransactionsForUserDateRangeEx]
@userId smallint, @startDate smalldatetime, @endDate smalldatetime
WITH EXEC AS CALLER
AS
	SET NOCOUNT ON

	SELECT
		tx.PerformingUserId,
		usr.DisplayName AS UserName,
		rol.Name AS RoleName,
		tx.InvoiceId,
		ord.OrderId,
		ord.OrderDateTime,
		ord.IsCancelled AS OrderCancelled,
		tx.TxType,
		tx.TxFlag,
		tx.TxTime,
		tx.TxAmount,
		tx.NonCashAmount,
		tx.PaymentMethod,
		tx.PaymentSource,
		tx.PaymentReference,
		tx.UserRemarks AS TxRemarks,
		inv.GrossPayable,
		inv.NetPayable,
		inv.PaidAmount,
		inv.DueAmount,
		COALESCE ( ref.FullName, ord.ReferrerCustomName, '' ) AS ReferrerName
	FROM
		PROE.PatientLabOrders AS ord
		INNER JOIN Finances.InvoiceTransactions AS tx ON ord.InvoiceId = tx.InvoiceId
		INNER JOIN Staff.Users AS usr ON tx.PerformingUserId = usr.Id
		INNER JOIN RBAC.Roles AS rol ON usr.RoleId = rol.Id
		INNER JOIN Finances.InvoiceMaster AS inv ON tx.InvoiceId = inv.InvoiceId
		LEFT JOIN [Catalog].Referrers AS ref ON ord.ReferrerId = ref.Id
	WHERE
  		tx.TxTime BETWEEN @startDate AND @endDate
  		AND tx.PerformingUserId = @userId
GO






CREATE PROCEDURE [Finances].[SP_GetAllTransactionsForDateRangeEx]
@startDate smalldatetime, @endDate smalldatetime
WITH EXEC AS CALLER
AS
	SET NOCOUNT ON

	SELECT
		tx.PerformingUserId,
		usr.DisplayName AS UserName,
		rol.Name AS RoleName,
		tx.InvoiceId,
		ord.OrderId,
		ord.OrderDateTime,
		ord.IsCancelled AS OrderCancelled,
		tx.TxType,
		tx.TxFlag,
		tx.TxTime,
		tx.TxAmount,
		tx.NonCashAmount,
		tx.PaymentMethod,
		tx.PaymentSource,
		tx.PaymentReference,
		tx.UserRemarks AS TxRemarks,
		inv.GrossPayable,
		inv.NetPayable,
		inv.PaidAmount,
		inv.DueAmount,
		COALESCE ( ref.FullName, ord.ReferrerCustomName, '' ) AS ReferrerName
	FROM
		PROE.PatientLabOrders AS ord
		INNER JOIN Finances.InvoiceTransactions AS tx ON ord.InvoiceId = tx.InvoiceId
		INNER JOIN Staff.Users AS usr ON tx.PerformingUserId = usr.Id
		INNER JOIN RBAC.Roles AS rol ON usr.RoleId = rol.Id
		INNER JOIN Finances.InvoiceMaster AS inv ON tx.InvoiceId = inv.InvoiceId
		LEFT JOIN [Catalog].Referrers AS ref ON ord.ReferrerId = ref.Id
	WHERE
  		tx.TxTime BETWEEN @startDate AND @endDate
GO



CREATE PROCEDURE [Finances].[SP_GetAllNonMatchingTransactionsInDateRangeEx]
@txType tinyint, @dtFrom smalldatetime, @dtTo smalldatetime
WITH EXEC AS CALLER
AS
	SET  NOCOUNT ON

  SELECT
    tx.InvoiceId,
    ord.OrderId,
    ord.OrderDateTime,
    ref.FullName AS ReferredBy,
    ord.ReferrerId,
    usr.DisplayName AS PerformedBy,
    tx.PerformingUserId,
    tx.WorkShiftId,
    tx.TxTime,
    tx.TxType,
    tx.TxFlag,
    tx.TxAmount,
	tx.NonCashAmount,
	tx.PaymentMethod,
	tx.PaymentSource,
	tx.PaymentReference,
    tx.UserIpAddress,
    tx.UserRemarks AS TxRemarks,
    ord.IsCancelled AS InvoiceIsCancelled,
    inv.GrossPayable AS InvoiceGross,
    inv.DiscountAmount AS InvoiceDiscount,
    inv.NetPayable AS InvoicePayable,
    inv.PaidAmount AS InvoicePaid,
    inv.DueAmount AS InvoiceDue,
    inv.RefundAmount AS InvoiceRefund
  FROM
    Finances.InvoiceTransactions tx
    INNER JOIN Finances.InvoiceMaster inv ON tx.InvoiceId = inv.InvoiceId
    INNER JOIN Staff.Users usr ON tx.PerformingUserId = usr.Id
    INNER JOIN PROE.PatientLabOrders ord ON inv.InvoiceId = ord.InvoiceId
    LEFT OUTER JOIN Catalog.Referrers ref ON ord.ReferrerId = ref.Id
  WHERE
    tx.TxType <> @txType AND
    tx.TxTime BETWEEN @dtFrom AND @dtTo
  ORDER BY
    tx.InvoiceId
GO




CREATE PROCEDURE [Finances].[SP_CreateNewTransactionEx]
	@invoiceId          BIGINT,
	@userId             SMALLINT = NULL,
	@authorizerId       SMALLINT = NULL,
	@shiftId            INT = NULL,
	@txType             TINYINT,
	@txFlag             TINYINT,
	@txAmount           MONEY,
	@ipAddr             INT = NULL,
	@remarks            VARCHAR (160) = NULL,
	@nonCashAmount      MONEY,
	@pmtMethod					TINYINT,
	@pmtSrc             TEXT = NULL,
	@pmtRef             TEXT = NULL,
	@txId               BIGINT OUTPUT
AS
	SET XACT_ABORT ON

	SET NOCOUNT ON

	BEGIN TRANSACTION

		INSERT INTO [Finances].[InvoiceTransactions] (
			InvoiceId,
			PerformingUserId,
			AuthorizingUserId,
			WorkShiftId,
			TxTime,
			TxType,
			TxFlag,
			TxAmount,
			UserIpAddress,
			UserRemarks,
			NonCashAmount,
			PaymentMethod,
			PaymentSource,
			PaymentReference
		)
		VALUES (
			@invoiceId,
			@userId,
			@authorizerId,
			@shiftId,
			CURRENT_TIMESTAMP,
			@txType,
			@txFlag,
			@txAmount,
			@ipAddr,
			@remarks,
			@nonCashAmount,
			@pmtMethod,
			@pmtSrc,
			@pmtRef
		)

		SELECT @txId = SCOPE_IDENTITY()

	COMMIT TRANSACTION
GO





CREATE PROCEDURE [Finances].[SP_FindWorkshiftByDateRangeEx]
	@dtStart smalldatetime, @dtEnd smalldatetime
AS
	SET NOCOUNT ON

	SELECT
		s.Id,
		s.UserId,
		u.DisplayName AS UserName,
		r.Name AS RoleName,
		s.IsClosed,
		s.StartTime,
		s.EndTime,
		s.NumOrders,
		s.AdditionalBalance,
		s.ReceiveAmount,
		s.NonCashAmount,
		s.DiscountAmount,
		s.DiscountRebateAmount,
		s.RefundAmount,
		s.FinalBalance,
		s.UserNotes
	FROM
		Finances.WorkShifts AS s
		INNER JOIN Staff.Users AS u ON s.UserId = u.Id
		INNER JOIN RBAC.Roles AS r ON u.RoleId = r.Id
	WHERE
		s.StartTime BETWEEN @dtStart AND @dtEnd
GO



CREATE PROCEDURE [Finances].[SP_FindWorkshiftByDateRangeRolesEx]
	@dtStart smalldatetime,
	@dtEnd smalldatetime,
	@roleIds [PROE].[SmallIntListTableType] READONLY
AS
	SET NOCOUNT ON

	SELECT
		s.Id,
		s.UserId,
		u.DisplayName AS UserName,
		r.Name AS RoleName,
		s.IsClosed,
		s.StartTime,
		s.EndTime,
		s.NumOrders,
		s.AdditionalBalance,
		s.ReceiveAmount,
		s.NonCashAmount,
		s.DiscountAmount,
		s.DiscountRebateAmount,
		s.RefundAmount,
		s.FinalBalance,
		s.UserNotes
	FROM
		Finances.WorkShifts AS s
		INNER JOIN Staff.Users AS u ON s.UserId = u.Id
		INNER JOIN RBAC.Roles AS r ON u.RoleId = r.Id
	WHERE
		s.StartTime BETWEEN @dtStart AND @dtEnd
		AND r.Id IN (SELECT n FROM @roleIds)
GO




CREATE PROCEDURE [CRM].[SP_GetRegisteredLoginOrPhoneCount]
	@login VARCHAR(MAX),
	@phone VARCHAR(MAX)
AS
	SET NOCOUNT ON

	SELECT
		COUNT(*) AS NumRows
	FROM
		CRM.Customers
	WHERE
		@login IN ( Phone, Login )
		OR @phone IN ( Phone, Login )
GO



CREATE PROCEDURE Subscriptions.SP_GetCustomerInvoiceDetails
  @invoiceId AS bigint
AS
  SET NOCOUNT ON

  SELECT TOP(1)
      sub.CustomerId,
      cust.IsActive AS CustomerIsActive,
      TRIM(ISNULL(cust.Title, '') + SPACE(1) + cust.FirstName + SPACE(1) + ISNULL(cust.LastName, '')) AS CustomerName,
      cust.UPIN,
      cust.Sex,
      cust.Phone,
      cust.Email,
      cust.Login,
      sub.PlanId,
      sub.PlanName,
      inv.PlanListPrice,
      inv.SubscriptionId,
      sub.IsActive AS SubscriptionIsActive,
      sub.StartedAt AS SubscriptionStartedAt,
      sub.ExpiresAt AS SubscriptionExpiresAt,
      sub.CanceledAt AS SubscriptionCanceledAt,
      inv.Id AS InvoiceId,
      inv.InvoiceDate,
      inv.SurchargeAmount,
      inv.TaxAmount,
      inv.InvoiceAmount,
      inv.PaidAmount,
      inv.PaymentMethod,
      inv.TxReferenceId,
      inv.Notes,
      inv.PerformingStaffId,
      staff.DisplayName AS PerformingStaffName
  FROM
      CRM.Customers AS cust
      INNER JOIN Subscriptions.Subscriptions AS sub ON cust.Id = sub.CustomerId
      INNER JOIN Subscriptions.Invoices AS inv ON sub.Id = inv.SubscriptionId
      LEFT JOIN Staff.Users AS staff ON inv.PerformingStaffId = staff.Id
  WHERE
      inv.Id = @invoiceId
GO



CREATE PROCEDURE [PROE].[SP_FetchLabOrderDetailsEx]
	@invoiceId bigint
AS
	SET NOCOUNT ON

	SELECT TOP(1)
	  ord.InvoiceId,
	  ord.OrderId,
	  ord.OrderDateTime,
	  ord.IsCancelled,
	  ord.WorkflowStage,
	  inv.PaymentStatus,
	  inv.GrossPayable,
	  inv.TaxAmount,
	  inv.DiscountAmount,
	  inv.NetPayable,
	  inv.PaidAmount,
	  inv.DueAmount,
	  ord.PhoneNumber,
	  ord.Title,
	  ord.FirstName,
	  ord.LastName,
	  ord.DoB,
	  ord.Age,
	  ord.Sex,
	  ord.DisallowReferral,
	  ord.IsReferrerUnknown,
	  ord.ReferrerId,
	  COALESCE ( phy.[FullName], ord.[ReferrerCustomName], '' ) AS ReferrerName,
	  ord.OrderingUserId,
	  usr.DisplayName AS OrderingUserName,
	  ord.OrderNotes,
	  ord.CustomerId,
	  cust.UPIN AS CustomerUPIN,
	  TRIM ( CONCAT ( cust.FirstName, ' ', cust.LastName ) ) AS CustomerName,
	  cust.Phone AS CustomerPhone,
	  cust.Email AS CustomerEmail,
	  cust.Login AS CustomerLogin,
	  ord.ResultsReleaseFlag,
	  ord.CorporateClientId,
	  corp.UID AS CorporateClientUID,
	  corp.Name AS CorporateClientName,
	  ord.IsExternalSubOrder,
	  ord.AssociateLabId,
	  assoc_lab.Name AS AssociateLabName,
	  ord.AssociateLabAccessionId
	FROM
	  Finances.InvoiceMaster AS inv
	  INNER JOIN PROE.PatientLabOrders AS ord ON inv.InvoiceId = ord.InvoiceId
	  LEFT JOIN [Catalog].Referrers AS phy ON ord.ReferrerId = phy.Id
	  INNER JOIN Staff.Users AS usr ON ord.OrderingUserId = usr.Id
	  LEFT JOIN CRM.Customers AS cust ON ord.CustomerId = cust.Id
	  LEFT JOIN Marketing.CorporateClients AS corp ON ord.CorporateClientId = corp.Id
	  LEFT JOIN OuterOMS.AssociateOrganizations AS assoc_lab ON ord.AssociateLabId = assoc_lab.Id
	WHERE
	  ord.InvoiceId = @invoiceId
GO





ALTER PROCEDURE [TestResults].[SP_GetResultBundleDetailsForReportGeneration]
	@bundleId bigint
AS
	SET NOCOUNT ON

	SELECT
		rb.[WorkflowStage],
		lab.[Name] AS LabName,
		lab.[ReqPrintName] AS LabPrintName,
		rb.[FinalizingConsultantName] AS ConsultantName,
		cons.[SignatureImage] AS ConsultantSignatureImage,
		cons.[SignatureText] AS ConsultantSignatureText,
		hdr.[ReportHeader],
		rb.[ResultNotes]
	FROM
		[TestResults].[ResultBundles] rb
		INNER JOIN [Catalog].[Labs] lab
			ON rb.[LabId] = lab.[Id]
		LEFT JOIN [Catalog].[LabReportHeaders] hdr
			ON hdr.[Id] = rb.[ReportHeaderId]
		LEFT JOIN [Staff].[Users] usr
			ON rb.[FinalizingConsultantId] = usr.[Id]
		LEFT JOIN [Staff].[Consultants] cons
			ON cons.[UserId] = usr.[Id]
	WHERE
		rb.[IsActive] = 1 AND
		rb.[Id] = @bundleId
GO










CREATE PROCEDURE [PROE].[SP_GetInvoiceContactDetailsFull]
  @invoiceId BIGINT
  WITH EXEC AS CALLER
AS
  SET  NOCOUNT ON

  SELECT TOP(1)
    lo.InvoiceId,
    lo.OrderId,
    lo.OrderDateTime,
    lo.FullName AS PatientName,
    COALESCE ( phy.FullName, lo.ReferrerCustomName, '' ) AS ReferrerName,
    lo.Age,
    lo.Sex,
    inv.GrossPayable,
    inv.NetPayable,
    inv.PaidAmount,
    inv.DueAmount,
    inv.DiscountAmount,
    inv.RefundAmount,
    lo.IsCancelled,
    lo.PhoneNumber,
    lo.EmailAddress,
    lo.EmailTestResults,
    lo.IsHidden,
    lo.WebAccessToken,
    lo.WorkflowStage,
    lo.ResultsReleaseFlag,
    lo.HealthPackageId,
    lo.CustomerId,
    cust.UPIN AS CustomerUPIN,
    TRIM ( cust.FirstName + SPACE ( 1 ) + ISNULL( cust.LastName, '' ) ) AS CustomerName,
    cust.IsActive AS CustomerIsActive,
    cust.Phone AS CustomerPhone,
    cust.Email AS CustomerEmail,
    cust.Login AS CustomerLogin,
    lo.CorporateClientId,
    corp.IsActive AS CorporateIsActive,
    corp.UID AS CorporateUID,
    corp.Name AS CorporateName,
    corp.Phone AS CorporatePhone,
    corp.Email AS CorporateEmail,
    corp.Login AS CorporateLogin,
    lo.IsExternalSubOrder,
    lo.AssociateLabId,
    lo.AssociateLabAccessionId,
    assoc.IsActive AS AssociateLabIsActive,
    assoc.Name AS AssociateLabName,
    assoc.Phone AS AssociateLabPhone,
    assoc.Email AS AssociateLabEmail,
    lo.AffiliateId,
    aff.IsActive AS AffiliateIsActive,
    aff.UPIN AS AffiliateUPIN,
    aff.Name AS AffiliateName,
    aff.MobilePhone AS AffiliatePhone,
    aff.Email AS AffiliateEmail,
    aff.Login AS AffiliateLogin
  FROM
    PROE.PatientLabOrders AS lo
    INNER JOIN Finances.InvoiceMaster AS inv ON inv.InvoiceId = lo.InvoiceId
    LEFT JOIN [CATALOG].Referrers AS phy ON lo.ReferrerId = phy.Id
    LEFT JOIN CRM.Customers AS cust ON lo.CustomerId = cust.Id
    LEFT JOIN Marketing.CorporateClients AS corp ON lo.CorporateClientId = corp.Id
    LEFT JOIN OuterOMS.AssociateOrganizations AS assoc ON lo.AssociateLabId = assoc.Id
    LEFT JOIN Marketing.Affiliates AS aff ON lo.AffiliateId = aff.Id
  WHERE
    inv.InvoiceId = @invoiceId
GO







CREATE PROCEDURE Subscriptions.SP_GetCustomerSubscriptionDetails
  @subId AS bigint
AS
  SET NOCOUNT ON

  SELECT TOP(1)
      sub.CustomerId,
      cust.IsActive AS CustomerIsActive,
      TRIM(ISNULL(cust.Title, '') + SPACE(1) + cust.FirstName + SPACE(1) + ISNULL(cust.LastName, '')) AS CustomerName,
      cust.UPIN,
      cust.Sex,
      cust.Phone,
      cust.Email,
      cust.Login,
      sub.PlanId,
      sub.PlanName,
      inv.PlanListPrice,
      inv.SubscriptionId,
      sub.IsActive AS SubscriptionIsActive,
      sub.StartedAt AS SubscriptionStartedAt,
      sub.ExpiresAt AS SubscriptionExpiresAt,
      sub.CanceledAt AS SubscriptionCanceledAt,
      inv.Id AS InvoiceId,
      inv.InvoiceDate,
      inv.SurchargeAmount,
      inv.TaxAmount,
      inv.InvoiceAmount,
      inv.PaidAmount,
      inv.PaymentMethod,
      inv.TxReferenceId,
      inv.Notes,
      inv.PerformingStaffId,
      staff.DisplayName AS PerformingStaffName
  FROM
    Subscriptions.Subscriptions AS sub
    INNER JOIN CRM.Customers AS cust ON cust.Id = sub.CustomerId
    INNER JOIN Subscriptions.Invoices AS inv ON sub.Id = inv.SubscriptionId
    LEFT JOIN Staff.Users AS staff ON inv.PerformingStaffId = staff.Id
  WHERE
      sub.Id = @subId
GO


GO





















CREATE PROCEDURE [PROE].[SP_SearchLabOrderByAffiliateIdAndDateRange]
  @id SMALLINT, @dtFrom DATETIME, @dtTo DATETIME
WITH EXEC AS CALLER
AS
  SET  NOCOUNT ON

  SELECT
    ord.InvoiceId,
    ord.OrderId,
    ord.OrderDateTime,
    ord.WorkflowStage,
    ord.IsCancelled,
    ord.IsExternalSubOrder,
    ord.DisallowReferral,
    ord.IsReferrerUnknown,
    ord.FullName AS PatientName,
    ord.Sex,
    ord.Age,
    ord.DoB,
    ord.ReferrerId,
    COALESCE ( phy.FullName, ord.ReferrerCustomName, '' ) AS ReferrerName,
    ord.CustomerId,
    cust.UPIN AS CustomerUPIN,
    ord.AffiliateId,
    aff.Name AS AffiliateName,
    aff.UPIN AS AffiliateUPIN,
    ord.AssociateLabId,
    assoc.Name AS AssociateLabName,
    ord.AssociateLabAccessionId,
    ord.CorporateClientId,
    corp.Name AS CorporateClientName,
    inv.GrossPayable,
    inv.DiscountAmount,
    inv.NetPayable,
    inv.PaidAmount,
    inv.DueAmount,
    inv.RefundAmount
  FROM
    PROE.PatientLabOrders AS ord
    LEFT JOIN [Catalog].Referrers AS phy ON ord.ReferrerId = phy.Id
    INNER JOIN Finances.InvoiceMaster AS inv ON inv.InvoiceId = ord.InvoiceId
    LEFT JOIN CRM.Customers AS cust ON ord.CustomerId = cust.Id
    LEFT JOIN OuterOMS.AssociateOrganizations AS assoc ON ord.AssociateLabId = assoc.Id
    LEFT JOIN Marketing.CorporateClients AS corp ON ord.CorporateClientId = corp.Id
    LEFT JOIN Marketing.Affiliates AS aff ON ord.AffiliateId = aff.Id
  WHERE
    ord.AffiliateId = @id
    AND ord.OrderDateTime BETWEEN @dtFrom AND @dtTo
  ORDER BY
    ord.InvoiceId ASC



GO




CREATE PROCEDURE [PROE].[SP_SearchLabOrderByCorporateClientIdAndDateRange]
  @id SMALLINT, @dtFrom DATETIME, @dtTo DATETIME
WITH EXEC AS CALLER
AS
  SET  NOCOUNT ON

  SELECT
    ord.InvoiceId,
    ord.OrderId,
    ord.OrderDateTime,
    ord.WorkflowStage,
    ord.IsCancelled,
    ord.IsExternalSubOrder,
    ord.DisallowReferral,
    ord.IsReferrerUnknown,
    ord.FullName AS PatientName,
    ord.Sex,
    ord.Age,
    ord.DoB,
    ord.ReferrerId,
    COALESCE ( phy.FullName, ord.ReferrerCustomName, '' ) AS ReferrerName,
    ord.CustomerId,
    cust.UPIN AS CustomerUPIN,
    ord.AffiliateId,
    aff.Name AS AffiliateName,
    aff.UPIN AS AffiliateUPIN,
    ord.AssociateLabId,
    assoc.Name AS AssociateLabName,
    ord.AssociateLabAccessionId,
    ord.CorporateClientId,
    corp.Name AS CorporateClientName,
    inv.GrossPayable,
    inv.DiscountAmount,
    inv.NetPayable,
    inv.PaidAmount,
    inv.DueAmount,
    inv.RefundAmount
  FROM
    PROE.PatientLabOrders AS ord
    LEFT JOIN [Catalog].Referrers AS phy ON ord.ReferrerId = phy.Id
    INNER JOIN Finances.InvoiceMaster AS inv ON inv.InvoiceId = ord.InvoiceId
    LEFT JOIN CRM.Customers AS cust ON ord.CustomerId = cust.Id
    LEFT JOIN OuterOMS.AssociateOrganizations AS assoc ON ord.AssociateLabId = assoc.Id
    LEFT JOIN Marketing.CorporateClients AS corp ON ord.CorporateClientId = corp.Id
    LEFT JOIN Marketing.Affiliates AS aff ON ord.AffiliateId = aff.Id
  WHERE
    ord.CorporateClientId = @id
    AND ord.OrderDateTime BETWEEN @dtFrom AND @dtTo
  ORDER BY
    ord.InvoiceId ASC


GO


CREATE PROCEDURE [PROE].[SP_SearchLabOrderByAssociateLabIdAndDateRange]
  @id SMALLINT, @dtFrom DATETIME, @dtTo DATETIME
WITH EXEC AS CALLER
AS
  SET  NOCOUNT ON

  SELECT
    ord.InvoiceId,
    ord.OrderId,
    ord.OrderDateTime,
    ord.WorkflowStage,
    ord.IsCancelled,
    ord.IsExternalSubOrder,
    ord.DisallowReferral,
    ord.IsReferrerUnknown,
    ord.FullName AS PatientName,
    ord.Sex,
    ord.Age,
    ord.DoB,
    ord.ReferrerId,
    COALESCE ( phy.FullName, ord.ReferrerCustomName, '' ) AS ReferrerName,
    ord.CustomerId,
    cust.UPIN AS CustomerUPIN,
    ord.AffiliateId,
    aff.Name AS AffiliateName,
    aff.UPIN AS AffiliateUPIN,
    ord.AssociateLabId,
    assoc.Name AS AssociateLabName,
    ord.AssociateLabAccessionId,
    ord.CorporateClientId,
    corp.Name AS CorporateClientName,
    inv.GrossPayable,
    inv.DiscountAmount,
    inv.NetPayable,
    inv.PaidAmount,
    inv.DueAmount,
    inv.RefundAmount
  FROM
    PROE.PatientLabOrders AS ord
    LEFT JOIN [Catalog].Referrers AS phy ON ord.ReferrerId = phy.Id
    INNER JOIN Finances.InvoiceMaster AS inv ON inv.InvoiceId = ord.InvoiceId
    LEFT JOIN CRM.Customers AS cust ON ord.CustomerId = cust.Id
    LEFT JOIN OuterOMS.AssociateOrganizations AS assoc ON ord.AssociateLabId = assoc.Id
    LEFT JOIN Marketing.CorporateClients AS corp ON ord.CorporateClientId = corp.Id
    LEFT JOIN Marketing.Affiliates AS aff ON ord.AffiliateId = aff.Id
  WHERE
    ord.AssociateLabId = @id
    AND ord.OrderDateTime BETWEEN @dtFrom AND @dtTo
  ORDER BY
    ord.InvoiceId ASC
    
    
GO









CREATE PROCEDURE [PROE].[SP_SearchLabOrderByCustomerIdAndDateRange]
  @id INT, @dtFrom DATETIME, @dtTo DATETIME
WITH EXEC AS CALLER
AS
  SET  NOCOUNT ON

  SELECT
    ord.InvoiceId,
    ord.OrderId,
    ord.OrderDateTime,
    ord.WorkflowStage,
    ord.IsCancelled,
    ord.IsExternalSubOrder,
    ord.DisallowReferral,
    ord.IsReferrerUnknown,
    ord.FullName AS PatientName,
    ord.Sex,
    ord.Age,
    ord.DoB,
    ord.ReferrerId,
    COALESCE ( phy.FullName, ord.ReferrerCustomName, '' ) AS ReferrerName,
    ord.CustomerId,
    cust.UPIN AS CustomerUPIN,
    ord.AffiliateId,
    aff.Name AS AffiliateName,
    aff.UPIN AS AffiliateUPIN,
    ord.AssociateLabId,
    assoc.Name AS AssociateLabName,
    ord.AssociateLabAccessionId,
    ord.CorporateClientId,
    corp.Name AS CorporateClientName,
    inv.GrossPayable,
    inv.DiscountAmount,
    inv.NetPayable,
    inv.PaidAmount,
    inv.DueAmount,
    inv.RefundAmount
  FROM
    PROE.PatientLabOrders AS ord
    LEFT JOIN [Catalog].Referrers AS phy ON ord.ReferrerId = phy.Id
    INNER JOIN Finances.InvoiceMaster AS inv ON inv.InvoiceId = ord.InvoiceId
    LEFT JOIN CRM.Customers AS cust ON ord.CustomerId = cust.Id
    LEFT JOIN OuterOMS.AssociateOrganizations AS assoc ON ord.AssociateLabId = assoc.Id
    LEFT JOIN Marketing.CorporateClients AS corp ON ord.CorporateClientId = corp.Id
    LEFT JOIN Marketing.Affiliates AS aff ON ord.AffiliateId = aff.Id
  WHERE
    ord.CustomerId = @id
    AND ord.OrderDateTime BETWEEN @dtFrom AND @dtTo
  ORDER BY
    ord.InvoiceId ASC
    
    
GO








CREATE PROCEDURE [PROE].[SP_SearchLabOrderByCustomerUPINAndDateRange]
  @upin VARCHAR(8), @dtFrom DATETIME, @dtTo DATETIME
WITH EXEC AS CALLER
AS
  SET  NOCOUNT ON

  SELECT
    ord.InvoiceId,
    ord.OrderId,
    ord.OrderDateTime,
    ord.WorkflowStage,
    ord.IsCancelled,
    ord.IsExternalSubOrder,
    ord.DisallowReferral,
    ord.IsReferrerUnknown,
    ord.FullName AS PatientName,
    ord.Sex,
    ord.Age,
    ord.DoB,
    ord.ReferrerId,
    COALESCE ( phy.FullName, ord.ReferrerCustomName, '' ) AS ReferrerName,
    ord.CustomerId,
    cust.UPIN AS CustomerUPIN,
    ord.AffiliateId,
    aff.Name AS AffiliateName,
    aff.UPIN AS AffiliateUPIN,
    ord.AssociateLabId,
    assoc.Name AS AssociateLabName,
    ord.AssociateLabAccessionId,
    ord.CorporateClientId,
    corp.Name AS CorporateClientName,
    inv.GrossPayable,
    inv.DiscountAmount,
    inv.NetPayable,
    inv.PaidAmount,
    inv.DueAmount,
    inv.RefundAmount
  FROM
    PROE.PatientLabOrders AS ord
    LEFT JOIN [Catalog].Referrers AS phy ON ord.ReferrerId = phy.Id
    INNER JOIN Finances.InvoiceMaster AS inv ON inv.InvoiceId = ord.InvoiceId
    LEFT JOIN CRM.Customers AS cust ON ord.CustomerId = cust.Id
    LEFT JOIN OuterOMS.AssociateOrganizations AS assoc ON ord.AssociateLabId = assoc.Id
    LEFT JOIN Marketing.CorporateClients AS corp ON ord.CorporateClientId = corp.Id
    LEFT JOIN Marketing.Affiliates AS aff ON ord.AffiliateId = aff.Id
  WHERE
    cust.UPIN = @upin
    AND ord.OrderDateTime BETWEEN @dtFrom AND @dtTo
  ORDER BY
    ord.InvoiceId ASC
    
    
GO




CREATE VIEW PROE.vw_PatientLabOrders AS
SELECT
  ord.InvoiceId,
  ord.OrderId,
  ord.OrderDateTime,
  ord.WorkflowStage,
  ord.IsCancelled,
  ord.ReferrerId,
  COALESCE ( ord.ReferrerCustomName, '' ) AS ReferrerName,
  ord.FullName AS PatientName,
	CASE    
    WHEN ord.Sex = 10 THEN 'M' 
    WHEN ord.Sex = 20 THEN 'F' 
    ELSE 'U'
  END AS Sex, 
  ord.Sex AS SexType,
  ord.Age,
  ord.DoB,
  ord.LastModified,
  ord.PhoneNumber,
  ord.EmailAddress,
  inv.PaymentStatus,
  inv.GrossPayable,
  inv.DiscountAmount,
  inv.TaxAmount,
  inv.SurchargeAmount,
  inv.NetPayable,
  inv.PaidAmount,
  inv.DueAmount,
  inv.RefundAmount,
  ord.ResultsReleaseFlag,
  ord.IsExternalSubOrder,
  ord.CustomerId,
  ord.WebAccessToken,
  ord.IsReferrerUnknown,
  ord.MirrorFlag,
  ord.IsHidden,
  ord.IsNonFungibleOrder,
  ord.HealthPackageId,
  NULL AS HealthPackageName,
  ord.CorporateClientId,
  corp.Name AS CorporateClientName,
  ord.CorporateBillSettlementId,
  ord.AffiliateId,
  aff.Name AS AffiliateName,
  ord.AssociateLabId,
  org.Name AS AssociateLabName,
  ord.AssociateLabAccessionId,
  ord.RowGuid 
FROM
  PROE.PatientLabOrders AS ord
  INNER JOIN Finances.InvoiceMaster AS inv ON ord.InvoiceId = inv.InvoiceId
  LEFT JOIN OuterOMS.AssociateOrganizations AS org ON ord.AssociateLabId = org.Id
  LEFT JOIN Marketing.CorporateClients AS corp ON ord.CorporateClientId = corp.Id
  LEFT JOIN Marketing.Affiliates AS aff ON ord.AffiliateId = aff.Id

GO









-- ----------------------------
-- Table structure for OnlinePayments
-- ----------------------------

CREATE TABLE [Finances].[OnlinePayments] (
  [Id] int IDENTITY(1, 1) NOT NULL,
  [InvoiceId] bigint  NOT NULL,
  [CustomerId] bigint  NULL,
  [TransactionId] varchar(80) COLLATE SQL_Latin1_General_CP1_CI_AS  NOT NULL,
  [Amount] smallmoney  NULL,
  [Details] text COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
  [CreatedAt] smalldatetime DEFAULT getdate() NOT NULL
)
GO

ALTER TABLE [Finances].[OnlinePayments] SET (LOCK_ESCALATION = TABLE)
GO

CREATE NONCLUSTERED INDEX [IX_ONLINE_TRANSACTION_INVOICE]
ON [Finances].[OnlinePayments] (
  [InvoiceId] ASC
)
GO

CREATE NONCLUSTERED INDEX [IX_ONLINE_TRANSACTION_TXID]
ON [Finances].[OnlinePayments] (
  [TransactionId] ASC
)
GO

CREATE NONCLUSTERED INDEX [IX_ONLINE_TRANSACTION_CUSTOMER]
ON [Finances].[OnlinePayments] (
  [CustomerId] ASC
)
GO
