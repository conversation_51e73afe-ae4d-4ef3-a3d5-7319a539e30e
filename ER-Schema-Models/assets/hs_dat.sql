--
-- PostgreSQL database dump
--

-- Dumped from database version 15.4
-- Dumped by pg_dump version 15.4

-- Started on 2023-10-03 16:15:16

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- TOC entry 3697 (class 0 OID 639176)
-- Dependencies: 250
-- Data for Name: activities; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.activities (id, name) FROM stdin;
1	Log in
2	Create lab order
3	Payment
4	View lab order
5	Edit profile
6	Account creation
7	View lab report
\.


--
-- TOC entry 3662 (class 0 OID 638861)
-- Dependencies: 215
-- Data for Name: branches; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.branches (id, is_active, priority, uin, name, phone, email, address, api_url, api_secret, username, password, geo_location, map_url, img_url, created_at, updated_at) FROM stdin;
4	t	0	CGNAV	Chevron BNS Issa Khan	***********	\N	Navy Hospital Gate, M. A Aziz Road, Chattogram	\N	\N	hs_web	chevron123	\N	7QJM+HRP, Chattogram	\N	2023-10-03 15:13:32.775444	2023-10-03 15:13:32.775444
5	t	0	ANWR	Chevron Anwara	***********	<EMAIL>	Chevron Bhaban Chatori Chowmuhani, Anwara 4376	\N	\N	hs_web	chevron123	\N	6VMC+4M Anwara	\N	2023-10-03 15:14:46.098174	2023-10-03 15:14:46.098174
6	t	0	HTHZ	Chevron Hathazari	***********	\N	2nd Floor, Hathazari N Johur Shopping Complex, Kachari Road, Hathazari, Chattogram 4330	\N	\N	hs_web	chevron123	\N	GR35+3M Hathazari	\N	2023-10-03 15:15:01.274921	2023-10-03 15:15:01.274921
7	t	0	CGBND	Chevron Bandartila	01833362220	\N	\N	\N	\N	hs_web	chevron123	\N	9Q4M+P7V, Bandartila, 4204	\N	2023-10-03 15:15:56.049496	2023-10-03 15:15:56.049496
8	t	0	PTKDR	Chevron Patiya	01829738070	\N	Nabi Super Market, Patiya	\N	\N	hs_web	chevron123	\N	7XX9+MW7	\N	2023-10-03 15:16:08.218374	2023-10-03 15:16:08.218374
9	t	0	FENI	Chevron Feni	01820090185	\N	\N	\N	\N	hs_web	chevron123	\N	2C52+FF Feni	\N	2023-10-03 15:16:18.***********-10-03 15:16:18.384459
10	t	0	CXB	Chevron Cox's Bazar	01862375555	\N	698 Hospital Rd, Cox's Bazar	\N	\N	hs_web	chevron123	\N	\N	\N	2023-10-03 15:16:41.***********-10-03 15:16:41.875596
11	t	0	RNGM	Chevron Rangamati	\N	\N	\N	\N	\N	hs_web	chevron123	\N	M53G+HPP, Rangamati	\N	2023-10-03 15:17:11.***********-10-03 15:17:11.961545
12	t	0	CHKR	Chevron Chakaria	01879651616	\N	Health Complex Road, Chakaria	\N	\N	hs_web	chevron123	\N	Q36G+V7X, Chakaria	\N	2023-10-03 15:26:15.***********-10-03 15:26:15.142211
13	t	0	PTSHA	Chevron Shantir Hat	\N	\N	\N	\N	\N	hs_web	chevron123	\N	7VXR+XJ Kusumpura	\N	2023-10-03 15:26:38.***********-10-03 15:26:38.054388
3	t	0	CGSBG	Chevron Shantibagh	01701229090	\N	2 Agrabad Access Road, Chattogram	\N	\N	hs_web	chevron123	\N	\N	\N	2023-10-03 15:28:26.***********-10-03 15:28:26.559022
1	t	200	CGPHQ	Chevron Panchlaish	01755666969	\N	12/12, O R Nizam Rd, Chattogram 4203	\N	\N	hs_web	chevron123	\N	9R7J+82 Chattogram	\N	2023-10-03 15:12:24.***********-10-03 15:12:24.386834
2	t	120	CGHAL	Chevron Halishahar	01680223993	\N	EPZ Branch M.A Aziz Road, Airport Road, Chattogram	\N	\N	hs_web	chevron123	\N	\N	\N	2023-10-03 15:12:50.***********-10-03 15:12:50.004782
\.


--
-- TOC entry 3705 (class 0 OID 639236)
-- Dependencies: 258
-- Data for Name: corporate_clients; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.corporate_clients (id, branch_id, source_guid, is_active, web_enabled, name, username, password, address, phone, email, contact_person, ltv, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 3672 (class 0 OID 638953)
-- Dependencies: 225
-- Data for Name: fulfillment_centers; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.fulfillment_centers (id, branch_id, uin, priority, is_active, name, address, phone, email, geo_location, img_url, map_url, created_at, updated_at) FROM stdin;
2	2	FCGHAL	120	t	Chevron Halishahar	\N	\N	\N	\N	\N	\N	2023-10-03 16:00:02.187617	2023-10-03 16:00:02.187617
1	1	FCGPHQ	200	t	Chevron Clinical Lab Panchlaish	\N	\N	\N	\N	\N	\N	2023-10-03 15:59:31.177808	2023-10-03 15:59:31.177808
\.


--
-- TOC entry 3715 (class 0 OID 639330)
-- Dependencies: 268
-- Data for Name: geo_regions; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.geo_regions (id, is_active, priority, name, region) FROM stdin;
55	t	0	Panchlaish	Chittagong
1	t	0	Anowara	Anowara
2	t	0	Bairag	Anowara
3	t	0	Barakhain	Anowara
4	t	0	Barasat	Anowara
5	t	0	Battali	Anowara
6	t	0	Burumchhara	Anowara
7	t	0	Chatari	Anowara
8	t	0	Haildhar	Anowara
9	t	0	Juidandi	Anowara
10	t	0	Paraikora	Anowara
11	t	0	Roypur	Anowara
12	t	0	Baharchhara	Banshkhali
13	t	0	Bailchhari	Banshkhali
14	t	0	Banshkhali	Banshkhali
15	t	0	Chambal	Banshkhali
16	t	0	Chhanua	Banshkhali
17	t	0	Gandamara	Banshkhali
18	t	0	Kalipur	Banshkhali
19	t	0	Katharia	Banshkhali
20	t	0	Khankhanabad	Banshkhali
21	t	0	Puichhari	Banshkhali
22	t	0	Pukuria	Banshkhali
23	t	0	Sadhanpur	Banshkhali
24	t	0	Saral	Banshkhali
25	t	0	Sekherkhil	Banshkhali
26	t	0	Silkup	Banshkhali
27	t	0	Ahla Karaldanga	Boalkhali
28	t	0	Amuchia	Boalkhali
29	t	0	Charandwip	Boalkhali
30	t	0	Kandhurkhil	Boalkhali
31	t	0	Paschim Gomdandi	Boalkhali
32	t	0	Popadia	Boalkhali
33	t	0	Purba Gomdandi	Boalkhali
34	t	0	Sakpura	Boalkhali
35	t	0	Saroatali	Boalkhali
36	t	0	Sreepur Kharandwip	Boalkhali
37	t	0	Bailtali	Chandanaish
38	t	0	Barama	Chandanaish
39	t	0	Barkal	Chandanaish
40	t	0	Chandanaish	Chandanaish
41	t	0	Dhopachhari	Chandanaish
42	t	0	Dohazari	Chandanaish
43	t	0	Hashimpur	Chandanaish
44	t	0	Joara	Chandanaish
45	t	0	Kanchanabad	Chandanaish
46	t	0	Satbaria	Chandanaish
47	t	0	Bakalia	Chittagong
48	t	0	Bayejid Bostami	Chittagong
49	t	0	Chandgaon	Chittagong
50	t	0	Chittagong Port	Chittagong
51	t	0	Double Mooring	Chittagong
52	t	0	Halishahar	Chittagong
53	t	0	Khulshi	Chittagong
54	t	0	Kotwali	Chittagong
56	t	0	Patenga	Chittagong
57	t	0	Abdullapur	Fatikchhari
58	t	0	Bagan Bazar	Fatikchhari
59	t	0	Baktapur	Fatikchhari
60	t	0	Bhujpur	Fatikchhari
61	t	0	Dantmara	Fatikchhari
62	t	0	Daulatpur	Fatikchhari
63	t	0	Dharmapur	Fatikchhari
64	t	0	Dhurung	Fatikchhari
65	t	0	Harwalchhari	Fatikchhari
66	t	0	Jafarnagar	Fatikchhari
67	t	0	Kanchan Nagar	Fatikchhari
68	t	0	Khiram	Fatikchhari
69	t	0	Lelang	Fatikchhari
70	t	0	Nanupur	Fatikchhari
71	t	0	Narayanhat	Fatikchhari
72	t	0	Paindanga	Fatikchhari
73	t	0	Rangamatia	Fatikchhari
74	t	0	Roushangiri	Fatikchhari
75	t	0	Samitirhat	Fatikchhari
76	t	0	Suabil	Fatikchhari
77	t	0	Sundarpur	Fatikchhari
78	t	0	Burish Char	Hathazari
79	t	0	Chhibatali	Hathazari
80	t	0	Chikandandi	Hathazari
81	t	0	Chittagang Cnt.	Hathazari
82	t	0	Dakshin Madarsha	Hathazari
83	t	0	Dhalai	Hathazari
84	t	0	Fatehpur	Hathazari
85	t	0	Forhadabad	Hathazari
86	t	0	Garduara	Hathazari
87	t	0	Guman Mardan	Hathazari
88	t	0	Hathazari	Hathazari
89	t	0	Mekhal	Hathazari
90	t	0	Mirzapur	Hathazari
91	t	0	Nangalmora	Hathazari
92	t	0	Shikarpur	Hathazari
93	t	0	Uttar Madarsa	Hathazari
94	t	0	Adhunagar	Lohagara
95	t	0	Amirabad	Lohagara
96	t	0	Barahatia	Lohagara
97	t	0	Charamba	Lohagara
98	t	0	Chunati	Lohagara
99	t	0	Kalauzan	Lohagara
100	t	0	Lohagara	Lohagara
101	t	0	Padua	Lohagara
102	t	0	Putibila	Lohagara
103	t	0	Dhum	Mirsharai
104	t	0	Durgapur	Mirsharai
105	t	0	Haitkandi	Mirsharai
106	t	0	Hinguli	Mirsharai
107	t	0	Ichhakhali	Mirsharai
108	t	0	Karerhat	Mirsharai
109	t	0	Katachhara	Mirsharai
110	t	0	Khaiyachhara	Mirsharai
111	t	0	Maghadia	Mirsharai
112	t	0	Mayani	Mirsharai
113	t	0	Mirsharai	Mirsharai
114	t	0	Mithanala	Mirsharai
115	t	0	Osmanpur	Mirsharai
116	t	0	Saherkhali	Mirsharai
117	t	0	Wahedpur	Mirsharai
118	t	0	Zorwarganj	Mirsharai
119	t	0	Pahartali	Pahartali
120	t	0	Asia	Patiya
121	t	0	Bara Uthan	Patiya
122	t	0	Baralia	Patiya
123	t	0	Bhatikhain	Patiya
124	t	0	Chanhara	Patiya
125	t	0	Char Lakshya	Patiya
126	t	0	Char Patharghata	Patiya
127	t	0	Dakhin D.Bhurshi	Patiya
128	t	0	Dhalghat	Patiya
129	t	0	Habilas Dwip	Patiya
130	t	0	Haidgaon	Patiya
131	t	0	Janglukhain	Patiya
132	t	0	Jiri	Patiya
133	t	0	Juldha	Patiya
134	t	0	Kachuai	Patiya
135	t	0	Kasiais	Patiya
136	t	0	Kelishahar	Patiya
137	t	0	Kharana	Patiya
138	t	0	Kolagaon	Patiya
139	t	0	Kusumpura	Patiya
140	t	0	Patiya	Patiya
141	t	0	Sikalbaha	Patiya
142	t	0	Sobhandandi	Patiya
143	t	0	Betagi	Rangunia
144	t	0	Chandraghona Kadamtali	Rangunia
145	t	0	Dakshin Rajanagar	Rangunia
146	t	0	Hosnabad	Rangunia
147	t	0	Islampur	Rangunia
148	t	0	Kodala	Rangunia
149	t	0	Lalanagar	Rangunia
150	t	0	Mariamnagar	Rangunia
151	t	0	Padua	Rangunia
152	t	0	Parua	Rangunia
153	t	0	Pomara	Rangunia
154	t	0	Rajanagar	Rangunia
155	t	0	Rangunia	Rangunia
156	t	0	Sarapbhata	Rangunia
157	t	0	Silok	Rangunia
158	t	0	Bagoan	Raozan
159	t	0	Binajuri	Raozan
160	t	0	Chikdair	Raozan
161	t	0	Dabua	Raozan
162	t	0	Gahira	Raozan
163	t	0	Haladia	Raozan
164	t	0	Kadalpur	Raozan
165	t	0	Noa Para	Raozan
166	t	0	Noajispur	Raozan
167	t	0	Pahartali	Raozan
168	t	0	Paschim Guzara	Raozan
169	t	0	Purba Guzara	Raozan
170	t	0	Raozan	Raozan
171	t	0	Urkirchar	Raozan
172	t	0	Amanullah	Sandwip
173	t	0	Azimpur	Sandwip
174	t	0	Bauria	Sandwip
175	t	0	Digghapar	Sandwip
176	t	0	Gachhua	Sandwip
177	t	0	Haramia	Sandwip
178	t	0	Harispur	Sandwip
179	t	0	Kalapania	Sandwip
180	t	0	Magdhara	Sandwip
181	t	0	Maitbhanga	Sandwip
182	t	0	Musapur	Sandwip
183	t	0	Rahmatpur	Sandwip
184	t	0	Sandwip	Sandwip
185	t	0	Santoshpur	Sandwip
186	t	0	Sarikait	Sandwip
187	t	0	Urirchar	Sandwip
188	t	0	Amilais	Satkania
189	t	0	Bazalia	Satkania
190	t	0	Charati	Satkania
191	t	0	Dharmapur	Satkania
192	t	0	Dhemsa	Satkania
193	t	0	Eochia	Satkania
194	t	0	Kaliais	Satkania
195	t	0	Kanchana	Satkania
196	t	0	Keochia	Satkania
197	t	0	Khagaria	Satkania
198	t	0	Madarsa	Satkania
199	t	0	Nalua	Satkania
200	t	0	Paschim Dhemsa	Satkania
201	t	0	Puranagar	Satkania
202	t	0	Sadaha	Satkania
203	t	0	Satkania	Satkania
204	t	0	Sonakania	Satkania
205	t	0	Banshbaria	Sitakunda
206	t	0	Barabkunda	Sitakunda
207	t	0	Bariadyala	Sitakunda
208	t	0	Bhatiari	Sitakunda
209	t	0	Kumira	Sitakunda
210	t	0	Muradpur	Sitakunda
211	t	0	Saidpur	Sitakunda
212	t	0	Salimpur	Sitakunda
213	t	0	Sitakunda	Sitakunda
214	t	0	Sonaichhari	Sitakunda
\.


--
-- TOC entry 3674 (class 0 OID 638970)
-- Dependencies: 227
-- Data for Name: patients; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.patients (id, corporate_id, upin, is_active, web_access, title, first_name, last_name, username, password, sex, age, dob, email, phone, phone2, father_name, mother_name, occupation, company, contact_name1, contact_phone1, contact_name2, contact_phone2, passport_num, nid_num, region_id, num_orders, ltv, enrolled_at, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 3668 (class 0 OID 638918)
-- Dependencies: 221
-- Data for Name: referrers; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.referrers (id, branch_id, is_active, name, source_guid, source_id, username, password, ltv, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 3711 (class 0 OID 639299)
-- Dependencies: 264
-- Data for Name: roles; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.roles (id, is_active, name, code, god_mode) FROM stdin;
2	t	User	user	f
1	t	Admin	admin	t
3	t	Branch Admin	branch.admin	f
\.


--
-- TOC entry 3703 (class 0 OID 639218)
-- Dependencies: 256
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.users (id, branch_id, center_id, role_id, is_active, name, department, username, password, phone, email, last_seen_at, created_at, updated_at) FROM stdin;
1	\N	\N	1	t	Dr. Masroor Ehsan	\N	masroor	masroor	\N	\N	2023-10-03 16:01:10.***********-10-03 16:01:10.***********-10-03 16:01:10.180247
2	1	\N	3	t	Pulak Parial	\N	pulak	pulak	\N	\N	2023-10-03 16:02:53.***********-10-03 16:02:53.***********-10-03 16:02:53.646144
3	1	\N	2	t	Sumit Majumdar	\N	sumit	sumit	\N	\N	2023-10-03 16:03:38.***********-10-03 16:03:38.***********-10-03 16:03:38.123875
\.


--
-- TOC entry 3693 (class 0 OID 639147)
-- Dependencies: 246
-- Data for Name: activity_logs; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.activity_logs (id, event_time, patient_id, referrer_id, staff_id, order_id, device_id, activity_id, ip_addr, remarks) FROM stdin;
\.


--
-- TOC entry 3678 (class 0 OID 639028)
-- Dependencies: 231
-- Data for Name: affiliates; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.affiliates (id, branch_id, is_active, name, source_guid, source_id, username, password, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 3707 (class 0 OID 639256)
-- Dependencies: 260
-- Data for Name: associate_labs; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.associate_labs (id, branch_id, source_guid, name, is_active, web_access, username, password, created_at, updated_at) FROM stdin;
2	1	123	Chevron Rangamati Branch	t	t	rangamati1	rangamati1	2023-10-03 16:05:18.***********-10-03 16:05:18.276038
\.


--
-- TOC entry 3713 (class 0 OID 639314)
-- Dependencies: 266
-- Data for Name: associate_lab_users; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.associate_lab_users (id, associate_lab_id, is_active, name, username, password, phone, email, last_seen_at, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 3686 (class 0 OID 639091)
-- Dependencies: 239
-- Data for Name: discount_levels; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.discount_levels (id, branch_id, is_active, is_universal, mode, percentage, amount, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 3666 (class 0 OID 638899)
-- Dependencies: 219
-- Data for Name: procedure_categories; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.procedure_categories (id, is_active, is_universal, priority, center_id, branch_id, name, img_url, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 3664 (class 0 OID 638877)
-- Dependencies: 217
-- Data for Name: procedures; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.procedures (id, branch_id, center_id, category_id, discount_level_id, source_guid, source_id, name, is_active, is_universal, list_price, img_url, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 3725 (class 0 OID 639397)
-- Dependencies: 278
-- Data for Name: branch_procedure_sales; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.branch_procedure_sales (id, sale_date, branch_id, procedure_id, category_id, count, value, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 3701 (class 0 OID 639197)
-- Dependencies: 254
-- Data for Name: branch_sales; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.branch_sales (id, branch_id, sale_date, num_orders, num_cancels, num_referred, gross_payable, discount_amount, net_payable, refund_amount, due_amount, paid_amount, referral_amount, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 3709 (class 0 OID 639278)
-- Dependencies: 262
-- Data for Name: corporate_settlements; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.corporate_settlements (id, corporate_id, branch_id, dealing_user_id, source_id, source_guid, settled_on, billing_code, billing_period_start, billing_period_end, payment_method, gross_payable, discount_amount, deductions, net_payable, paid_amount, due_amount, settlement_status, originator_name, originator_ref, originator_bank, payee_bank, payment_ref, remarks, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 3682 (class 0 OID 639059)
-- Dependencies: 235
-- Data for Name: coupons; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.coupons (id, branch_id, is_active, code, start_date, expiry_date, remarks, created_at, updated_at, discount_amount, discount_type, minimum_order, maximum_discount, limit_per_patient) FROM stdin;
\.


--
-- TOC entry 3695 (class 0 OID 639164)
-- Dependencies: 248
-- Data for Name: devices; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.devices (id, name, created_at) FROM stdin;
\.


--
-- TOC entry 3670 (class 0 OID 638936)
-- Dependencies: 223
-- Data for Name: health_packages; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.health_packages (id, branch_id, is_active, is_universal, priority, name, gross_value, net_price, num_tests, img_url, description, remarks, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 3691 (class 0 OID 639131)
-- Dependencies: 244
-- Data for Name: taxes; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.taxes (id, name, code, tax_rate, tax_type, is_active, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 3676 (class 0 OID 638995)
-- Dependencies: 229
-- Data for Name: orders; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.orders (id, branch_id, center_id, patient_id, corporate_id, associate_lab_id, associate_lab_tracking_id, is_outer_order, source_invoice_id, source_order_id, source_order_datetime, is_canceled, is_immutable_order, disallow_referral, payment_status, gross_payable, discount_amount, tax_amount, surcharge_amount, net_payable, paid_amount, due_amount, referrer_custom_name, referrer_id, affiliate_id, package_id, coupon_id, tax_id, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 3689 (class 0 OID 639113)
-- Dependencies: 242
-- Data for Name: messages; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.messages (id, branch_id, patient_id, referrer_id, order_id, sent_at, seen_at, content, updated_at) FROM stdin;
\.


--
-- TOC entry 3680 (class 0 OID 639045)
-- Dependencies: 233
-- Data for Name: order_procedures; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.order_procedures (id, order_id, procedure_id, source_id, workflow_stage, is_canceled, unit_price, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 3684 (class 0 OID 639076)
-- Dependencies: 237
-- Data for Name: order_transactions; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.order_transactions (id, order_id, tx_type, tx_time, tx_amount, tx_flag, source_id, performing_user, payment_provider, payment_ref, ip_addr, remarks, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 3699 (class 0 OID 639187)
-- Dependencies: 252
-- Data for Name: package_components; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.package_components (id, package_id, procedure_id) FROM stdin;
\.


--
-- TOC entry 3687 (class 0 OID 639103)
-- Dependencies: 240
-- Data for Name: patient_addresses; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.patient_addresses (id, patient_id, is_default, label, address, lat, lng) FROM stdin;
\.


--
-- TOC entry 3717 (class 0 OID 639342)
-- Dependencies: 270
-- Data for Name: permissions; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.permissions (id, is_active, name, code) FROM stdin;
\.


--
-- TOC entry 3723 (class 0 OID 639383)
-- Dependencies: 276
-- Data for Name: referrer_procedure_sales; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.referrer_procedure_sales (id, sale_date, referrer_id, procedure_id, count, value, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 3721 (class 0 OID 639365)
-- Dependencies: 274
-- Data for Name: referrer_sales; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.referrer_sales (id, sale_date, referrer_id, num_orders, gross_payable, discount_amount, net_payable, paid_amount, due_amount, referral_amount, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 3719 (class 0 OID 639355)
-- Dependencies: 272
-- Data for Name: role_permission_links; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.role_permission_links (id, role_id, permission_id) FROM stdin;
\.


--
-- TOC entry 3727 (class 0 OID 639414)
-- Dependencies: 280
-- Data for Name: settings; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.settings (id, key, val_str, val_int, val_float, branch_id, user_id) FROM stdin;
\.


--
-- TOC entry 3733 (class 0 OID 0)
-- Dependencies: 249
-- Name: activities_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.activities_id_seq', 7, true);


--
-- TOC entry 3734 (class 0 OID 0)
-- Dependencies: 245
-- Name: activity_logs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.activity_logs_id_seq', 1, false);


--
-- TOC entry 3735 (class 0 OID 0)
-- Dependencies: 230
-- Name: affiliates_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.affiliates_id_seq', 1, false);


--
-- TOC entry 3736 (class 0 OID 0)
-- Dependencies: 265
-- Name: associate_lab_users_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.associate_lab_users_id_seq', 1, false);


--
-- TOC entry 3737 (class 0 OID 0)
-- Dependencies: 259
-- Name: associate_labs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.associate_labs_id_seq', 2, true);


--
-- TOC entry 3738 (class 0 OID 0)
-- Dependencies: 277
-- Name: branch_procedure_sales_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.branch_procedure_sales_id_seq', 1, false);


--
-- TOC entry 3739 (class 0 OID 0)
-- Dependencies: 253
-- Name: branch_sales_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.branch_sales_id_seq', 1, false);


--
-- TOC entry 3740 (class 0 OID 0)
-- Dependencies: 214
-- Name: branches_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.branches_id_seq', 14, true);


--
-- TOC entry 3741 (class 0 OID 0)
-- Dependencies: 257
-- Name: corporate_clients_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.corporate_clients_id_seq', 1, false);


--
-- TOC entry 3742 (class 0 OID 0)
-- Dependencies: 261
-- Name: corporate_settlements_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.corporate_settlements_id_seq', 1, false);


--
-- TOC entry 3743 (class 0 OID 0)
-- Dependencies: 234
-- Name: coupons_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.coupons_id_seq', 1, false);


--
-- TOC entry 3744 (class 0 OID 0)
-- Dependencies: 247
-- Name: devices_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.devices_id_seq', 1, false);


--
-- TOC entry 3745 (class 0 OID 0)
-- Dependencies: 238
-- Name: discount_levels_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.discount_levels_id_seq', 1, false);


--
-- TOC entry 3746 (class 0 OID 0)
-- Dependencies: 224
-- Name: fulfillment_centers_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.fulfillment_centers_id_seq', 2, true);


--
-- TOC entry 3747 (class 0 OID 0)
-- Dependencies: 267
-- Name: geo_regions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.geo_regions_id_seq', 214, true);


--
-- TOC entry 3748 (class 0 OID 0)
-- Dependencies: 222
-- Name: health_packages_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.health_packages_id_seq', 1, false);


--
-- TOC entry 3749 (class 0 OID 0)
-- Dependencies: 241
-- Name: messages_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.messages_id_seq', 1, false);


--
-- TOC entry 3750 (class 0 OID 0)
-- Dependencies: 232
-- Name: order_procedures_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.order_procedures_id_seq', 1, false);


--
-- TOC entry 3751 (class 0 OID 0)
-- Dependencies: 236
-- Name: order_transactions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.order_transactions_id_seq', 1, false);


--
-- TOC entry 3752 (class 0 OID 0)
-- Dependencies: 228
-- Name: orders_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.orders_id_seq', 1, false);


--
-- TOC entry 3753 (class 0 OID 0)
-- Dependencies: 251
-- Name: package_components_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.package_components_id_seq', 1, false);


--
-- TOC entry 3754 (class 0 OID 0)
-- Dependencies: 226
-- Name: patients_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.patients_id_seq', 1, false);


--
-- TOC entry 3755 (class 0 OID 0)
-- Dependencies: 269
-- Name: permissions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.permissions_id_seq', 1, false);


--
-- TOC entry 3756 (class 0 OID 0)
-- Dependencies: 218
-- Name: procedure_categories_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.procedure_categories_id_seq', 1, false);


--
-- TOC entry 3757 (class 0 OID 0)
-- Dependencies: 216
-- Name: procedures_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.procedures_id_seq', 1, false);


--
-- TOC entry 3758 (class 0 OID 0)
-- Dependencies: 275
-- Name: referrer_procedure_sales_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.referrer_procedure_sales_id_seq', 1, false);


--
-- TOC entry 3759 (class 0 OID 0)
-- Dependencies: 273
-- Name: referrer_sales_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.referrer_sales_id_seq', 1, false);


--
-- TOC entry 3760 (class 0 OID 0)
-- Dependencies: 220
-- Name: referrers_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.referrers_id_seq', 1, false);


--
-- TOC entry 3761 (class 0 OID 0)
-- Dependencies: 271
-- Name: role_permission_links_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.role_permission_links_id_seq', 1, false);


--
-- TOC entry 3762 (class 0 OID 0)
-- Dependencies: 263
-- Name: roles_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.roles_id_seq', 3, true);


--
-- TOC entry 3763 (class 0 OID 0)
-- Dependencies: 279
-- Name: settings_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.settings_id_seq', 1, false);


--
-- TOC entry 3764 (class 0 OID 0)
-- Dependencies: 243
-- Name: taxes_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.taxes_id_seq', 1, false);


--
-- TOC entry 3765 (class 0 OID 0)
-- Dependencies: 255
-- Name: users_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.users_id_seq', 3, true);


-- Completed on 2023-10-03 16:15:16

--
-- PostgreSQL database dump complete
--

