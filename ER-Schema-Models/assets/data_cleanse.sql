SELECT TOP 1 Id FROM XXX WHERE EventTime < DATEADD(MONTH, -3, GETDATE())



DELETE FROM APP_SYS.AppFaults WHERE EventTime < DATEADD(MONTH, -3, GETDATE())
DELETE FROM APP_SYS.AuditTrails WHERE EventTime < DATEADD(MONTH, -3, GETDATE())
DELETE FROM TestResults.TemplateResults WHERE DateCreated < DATEADD(MONTH, -3, GETDATE())
DELETE FROM Finances.InvoicePrimal WHERE DateCreated < DATEADD(MONTH, -3, GETDATE())
DELETE FROM TestResults.MachineData WHERE EntryTime < DATEADD(MONTH, -3, GETDATE())
DELETE FROM TestResults.DiscreteResultLineItems WHERE DateCreated < DATEADD(MONTH, -3, GETDATE())
DELETE FROM TestResults.ResultBundles WHERE DateCreated < DATEADD(MONTH, -3, GETDATE())
DELETE FROM PROE.OrderedTests WHERE DateCreated < DATEADD(MONTH, -3, GETDATE())
DELETE FROM PROE.OrderedBillableItems WHERE DateCreated < DATEADD(MONTH, -3, GETDATE())

TRUNCATE TABLE PROE.HeldLabOrderTests
TRUNCATE TABLE PROE.HeldLabOrders

DELETE FROM Finances.WorkShifts WHERE StartTime < DATEADD(MONTH, -3, GETDATE())

DELETE FROM Finances.InvoiceTransactions WHERE TxTime < DATEADD(MONTH, -3, GETDATE())

DELETE FROM Finances.InvoiceMaster WHERE DateCreated < DATEADD(MONTH, -3, GETDATE())

DELETE FROM PROE.PatientLabOrders WHERE OrderDateTime < DATEADD(MONTH, -3, GETDATE())

SELECT TOP 1 InvoiceId FROM PROE.PatientLabOrders WHERE OrderDateTime >= DATEADD(MONTH, -3, GETDATE())

DELETE FROM Finances.InvoiceTransactions WHERE InvoiceId < 3185357
DELETE FROM Finances.InvoiceMaster WHERE InvoiceId < 3185357


DELETE FROM PROE.OrderedTests WHERE InvoiceId < 3185357
DELETE FROM PROE.OrderedBillableItems WHERE InvoiceId < 3185357
DELETE FROM PROE.PatientLabOrders  WHERE InvoiceId < 3185357

-- IDEAS

WHILE 1 = 1 
BEGIN
	--DELETE TOP (10000) FROM PROE.PatientLabOrders  WHERE InvoiceId < 4439802;
	--DELETE TOP (10000) FROM TestResults.ResultBundles WHERE InvoiceId < 4439802;
	--DELETE TOP (10000) FROM TestResults.ResultBundles WHERE InvoiceId IS NULL;
	--DELETE TOP 10000 FROM APP_SYS.AuditTrails WHERE EventTime < DATEADD(MONTH, -3, GETDATE());
	DELETE FROM APP_SYS.AuditTrails WHERE PatientLabOrderId < 4439802;

	IF @@ROWCOUNT = 0 BREAK;
END;