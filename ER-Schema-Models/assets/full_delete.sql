TRUNCATE TABLE TestResults.RecentlyUpdatedResultBundles;
go
TRUNCATE TABLE APP_SYS.InvoiceNotifications;
go
TRUNCATE TABLE APP_SYS.AppFaults;
go
TRUNCATE TABLE APP_SYS.ErrorLog;
go
TRUNCATE TABLE APP_SYS.AuditTrails;
go
TRUNCATE TABLE APP_SYS.WorkstationsRegistry;
go
TRUNCATE TABLE TestResults.TemplateResults;
go
TRUNCATE TABLE Finances.InvoicePrimal;
go
TRUNCATE TABLE TestResults.DiscreteResultLineItems;
go
DELETE FROM TestResults.ResultBundles;
go
DELETE FROM PROE.OrderedTests;
go
TRUNCATE TABLE PROE.OrderedBillableItems;
go
TRUNCATE TABLE PROE.HeldLabOrderTests;
go
DELETE FROM PROE.HeldLabOrders;
go
DELETE FROM Finances.WorkShifts;
go
TRUNCATE TABLE Finances.InvoiceTransactions;
go
DELETE FROM Finances.InvoiceMaster;
go
DELETE FROM PROE.PatientLabOrders;
go
DBCC CHECKIDENT ('[PROE].[HeldLabOrders]', RESEED, 10001);
go
DBCC CHECKIDENT ('[PROE].[PatientLabOrders]', RESEED, 10001);
go
DBCC CHECKIDENT ('[TestResults].[ResultBundles]', RESEED, 10001);
go
DBCC CHECKIDENT ('[PROE].[OrderedTests]', RESEED, 10001);
go
DBCC SHRINKFILE (LabMaestro_Log, 0);
GO