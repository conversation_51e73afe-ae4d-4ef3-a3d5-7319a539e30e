-- regenerate RowGuid

UPDATE Catalog.BillableItems SET RowGuid = NEWID();
UPDATE Catalog.BillOfMaterials SET RowGuid = NEWID();
UPDATE Catalog.DiscountLevels SET RowGuid = NEWID();
UPDATE Catalog.DiscreteReportLineItems SET RowGuid = NEWID();
UPDATE Catalog.LabReportHeaders SET RowGuid = NEWID();
UPDATE Catalog.Labs SET RowGuid = NEWID();
UPDATE Catalog.LabTests SET RowGuid = NEWID();
UPDATE Catalog.ReferrerAddresses SET RowGuid = NEWID();
UPDATE Catalog.ReferrerCategories SET RowGuid = NEWID();
UPDATE Catalog.ReferrerCategoryLink SET RowGuid = NEWID();
UPDATE Catalog.ReferrerPhoneNumbers SET RowGuid = NEWID();
UPDATE Catalog.Referrers SET RowGuid = NEWID();
UPDATE Catalog.ReqParameters SET RowGuid = NEWID();
UPDATE Catalog.SandboxedGroupLabsLink SET RowGuid = NEWID();
UPDATE Catalog.SandboxedLabGroups SET RowGuid = NEWID();
UPDATE Catalog.SurchargeLevels SET RowGuid = NEWID();
UPDATE Catalog.TATGroups SET RowGuid = NEWID();
UPDATE Catalog.TaxLevels SET RowGuid = NEWID();
UPDATE Catalog.TemplateGroupLinks SET RowGuid = NEWID();
UPDATE Catalog.TemplateGroups SET RowGuid = NEWID();
UPDATE Catalog.TemplateReports SET RowGuid = NEWID();
UPDATE Catalog.TestBILink SET RowGuid = NEWID();
UPDATE Catalog.TestBoMLink SET RowGuid = NEWID();
UPDATE Catalog.UserTemplateGroupLinks SET RowGuid = NEWID();
UPDATE Catalog.UserTemplateReports SET RowGuid = NEWID();
UPDATE Staff.Users SET RowGuid = NEWID();




-- rename tables
UPDATE SubContract.AssociateLabs SET RowGuid = NEWID();
UPDATE SubContract.AssociateLabUsers SET RowGuid = NEWID();



-- add RowGuid column
UPDATE PROE.PatientLabOrders SET RowGuid = NEWID();

GO