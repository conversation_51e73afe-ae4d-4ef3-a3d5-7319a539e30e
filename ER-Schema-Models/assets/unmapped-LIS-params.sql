SELECT
	Labs.Name AS Lab,
	LabTests.CanonicalName AS TestName,
	DiscreteReportLineItems.Parameter AS ParameterLine,
	DiscreteReportLineItems.MachineParam,
	DiscreteReportLineItems.LabTestId,
	DiscreteReportLineItems.Id AS ItemId 
FROM
	[Catalog].LabTests
	INNER JOIN [Catalog].DiscreteReportLineItems ON LabTests.Id = DiscreteReportLineItems.LabTestId
	INNER JOIN [Catalog].Labs ON LabTests.PerformingLabId = Labs.Id 
WHERE
	LabTests.IsActive = 1 
	AND DiscreteReportLineItems.IsResultableItem = 1 
ORDER BY
	Labs.Name ASC,
	LabTests.CanonicalName ASC