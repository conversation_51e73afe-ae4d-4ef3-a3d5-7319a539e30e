ALTER TABLE TestResults.TemplateResults DROP CONSTRAINT PK_TEMPLATE_RESULTS
GO

ALTER TABLE TestResults.TemplateResults ADD CONSTRAINT PK_TEMPLATE_RESULTS PRIMARY KEY NONCLUSTERED (Id ASC)
   WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, 
         ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [FG_Indices]
GO

CREATE CLUSTERED INDEX PIX_TEMPLATE_RESULTS ON TestResults.TemplateResults (DateCreated)
  WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, 
        ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) 
  ON PS_DateRange(DateCreated)
GO


SELECT o.name objectname,i.name indexname, partition_id, partition_number, [rows]
FROM sys.partitions p
INNER JOIN sys.objects o ON o.object_id=p.object_id
INNER JOIN sys.indexes i ON i.object_id=p.object_id and p.index_id=i.index_id
WHERE o.name LIKE '%TemplateResults%'