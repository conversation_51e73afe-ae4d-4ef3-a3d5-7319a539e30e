-- add partitions for years 2024-2033

-- filegroups

ALTER DATABASE LabMaestro ADD FILEGROUP FG_Data_24H1
GO

ALTER DATABASE LabMaestro ADD FILEGROUP FG_Data_24H2
GO

ALTER DATABASE LabMaestro ADD FILEGROUP FG_Data_25H1
GO

ALTER DATABASE LabMaestro ADD FILEGROUP FG_Data_25H2
GO

ALTER DATABASE LabMaestro ADD FILEGROUP FG_Data_26H1
GO

ALTER DATABASE LabMaestro ADD FILEGROUP FG_Data_26H2
GO

ALTER DATABASE LabMaestro ADD FILEGROUP FG_Data_27H1
GO

ALTER DATABASE LabMaestro ADD FILEGROUP FG_Data_27H2
GO

ALTER DATABASE LabMaestro ADD FILEGROUP FG_Data_28H1
GO

ALTER DATABASE LabMaestro ADD FILEGROUP FG_Data_28H2
GO

ALTER DATABASE LabMaestro ADD FILEGROUP FG_Data_29H1
GO

ALTER DATABASE LabMaestro ADD FILEGROUP FG_Data_29H2
GO

ALTER DATABASE LabMaestro ADD FILEGROUP FG_Data_30H1
GO

ALTER DATABASE LabMaestro ADD FILEGROUP FG_Data_30H2
GO

ALTER DATABASE LabMaestro ADD FILEGROUP FG_Data_31H1
GO

ALTER DATABASE LabMaestro ADD FILEGROUP FG_Data_31H2
GO

ALTER DATABASE LabMaestro ADD FILEGROUP FG_Data_32H1
GO

ALTER DATABASE LabMaestro ADD FILEGROUP FG_Data_32H2
GO

ALTER DATABASE LabMaestro ADD FILEGROUP FG_Data_33H1
GO

ALTER DATABASE LabMaestro ADD FILEGROUP FG_Data_33H2
GO


-- files

ALTER DATABASE LabMaestro ADD FILE (
    NAME='LabMaestro_data_24H1',
    FILENAME='F:\LABMAESTRO_OLTP\lm_oltp_data_24H1.ndf',
    SIZE=4MB,
    MAXSIZE=UNLIMITED,
    FILEGROWTH=32MB
) TO FILEGROUP FG_Data_24H1
GO

ALTER DATABASE LabMaestro ADD FILE (
    NAME='LabMaestro_data_24H2',
    FILENAME='F:\LABMAESTRO_OLTP\lm_oltp_data_24H2.ndf',
    SIZE=4MB,
    MAXSIZE=UNLIMITED,
    FILEGROWTH=32MB
) TO FILEGROUP FG_Data_24H2
GO

ALTER DATABASE LabMaestro ADD FILE (
    NAME='LabMaestro_data_25H1',
    FILENAME='F:\LABMAESTRO_OLTP\lm_oltp_data_25H1.ndf',
    SIZE=4MB,
    MAXSIZE=UNLIMITED,
    FILEGROWTH=32MB
) TO FILEGROUP FG_Data_25H1
GO

ALTER DATABASE LabMaestro ADD FILE (
    NAME='LabMaestro_data_25H2',
    FILENAME='F:\LABMAESTRO_OLTP\lm_oltp_data_25H2.ndf',
    SIZE=4MB,
    MAXSIZE=UNLIMITED,
    FILEGROWTH=32MB
) TO FILEGROUP FG_Data_25H2
GO

ALTER DATABASE LabMaestro ADD FILE (
    NAME='LabMaestro_data_26H1',
    FILENAME='F:\LABMAESTRO_OLTP\lm_oltp_data_26H1.ndf',
    SIZE=4MB,
    MAXSIZE=UNLIMITED,
    FILEGROWTH=32MB
) TO FILEGROUP FG_Data_26H1
GO

ALTER DATABASE LabMaestro ADD FILE (
    NAME='LabMaestro_data_26H2',
    FILENAME='F:\LABMAESTRO_OLTP\lm_oltp_data_26H2.ndf',
    SIZE=4MB,
    MAXSIZE=UNLIMITED,
    FILEGROWTH=32MB
) TO FILEGROUP FG_Data_26H2
GO

ALTER DATABASE LabMaestro ADD FILE (
    NAME='LabMaestro_data_27H1',
    FILENAME='F:\LABMAESTRO_OLTP\lm_oltp_data_27H1.ndf',
    SIZE=4MB,
    MAXSIZE=UNLIMITED,
    FILEGROWTH=32MB
) TO FILEGROUP FG_Data_27H1
GO

ALTER DATABASE LabMaestro ADD FILE (
    NAME='LabMaestro_data_27H2',
    FILENAME='F:\LABMAESTRO_OLTP\lm_oltp_data_27H2.ndf',
    SIZE=4MB,
    MAXSIZE=UNLIMITED,
    FILEGROWTH=32MB
) TO FILEGROUP FG_Data_27H2
GO

ALTER DATABASE LabMaestro ADD FILE (
    NAME='LabMaestro_data_28H1',
    FILENAME='F:\LABMAESTRO_OLTP\lm_oltp_data_28H1.ndf',
    SIZE=4MB,
    MAXSIZE=UNLIMITED,
    FILEGROWTH=32MB
) TO FILEGROUP FG_Data_28H1
GO

ALTER DATABASE LabMaestro ADD FILE (
    NAME='LabMaestro_data_28H2',
    FILENAME='F:\LABMAESTRO_OLTP\lm_oltp_data_28H2.ndf',
    SIZE=4MB,
    MAXSIZE=UNLIMITED,
    FILEGROWTH=32MB
) TO FILEGROUP FG_Data_28H2
GO

ALTER DATABASE LabMaestro ADD FILE (
    NAME='LabMaestro_data_29H1',
    FILENAME='F:\LABMAESTRO_OLTP\lm_oltp_data_29H1.ndf',
    SIZE=4MB,
    MAXSIZE=UNLIMITED,
    FILEGROWTH=32MB
) TO FILEGROUP FG_Data_29H1
GO

ALTER DATABASE LabMaestro ADD FILE (
    NAME='LabMaestro_data_29H2',
    FILENAME='F:\LABMAESTRO_OLTP\lm_oltp_data_29H2.ndf',
    SIZE=4MB,
    MAXSIZE=UNLIMITED,
    FILEGROWTH=32MB
) TO FILEGROUP FG_Data_29H2
GO

ALTER DATABASE LabMaestro ADD FILE (
    NAME='LabMaestro_data_30H1',
    FILENAME='F:\LABMAESTRO_OLTP\lm_oltp_data_30H1.ndf',
    SIZE=4MB,
    MAXSIZE=UNLIMITED,
    FILEGROWTH=32MB
) TO FILEGROUP FG_Data_30H1
GO

ALTER DATABASE LabMaestro ADD FILE (
    NAME='LabMaestro_data_30H2',
    FILENAME='F:\LABMAESTRO_OLTP\lm_oltp_data_30H2.ndf',
    SIZE=4MB,
    MAXSIZE=UNLIMITED,
    FILEGROWTH=32MB
) TO FILEGROUP FG_Data_30H2
GO

ALTER DATABASE LabMaestro ADD FILE (
    NAME='LabMaestro_data_31H1',
    FILENAME='F:\LABMAESTRO_OLTP\lm_oltp_data_31H1.ndf',
    SIZE=4MB,
    MAXSIZE=UNLIMITED,
    FILEGROWTH=32MB
) TO FILEGROUP FG_Data_31H1
GO

ALTER DATABASE LabMaestro ADD FILE (
    NAME='LabMaestro_data_31H2',
    FILENAME='F:\LABMAESTRO_OLTP\lm_oltp_data_31H2.ndf',
    SIZE=4MB,
    MAXSIZE=UNLIMITED,
    FILEGROWTH=32MB
) TO FILEGROUP FG_Data_31H2
GO

ALTER DATABASE LabMaestro ADD FILE (
    NAME='LabMaestro_data_32H1',
    FILENAME='F:\LABMAESTRO_OLTP\lm_oltp_data_32H1.ndf',
    SIZE=4MB,
    MAXSIZE=UNLIMITED,
    FILEGROWTH=32MB
) TO FILEGROUP FG_Data_32H1
GO

ALTER DATABASE LabMaestro ADD FILE (
    NAME='LabMaestro_data_32H2',
    FILENAME='F:\LABMAESTRO_OLTP\lm_oltp_data_32H2.ndf',
    SIZE=4MB,
    MAXSIZE=UNLIMITED,
    FILEGROWTH=32MB
) TO FILEGROUP FG_Data_32H2
GO

ALTER DATABASE LabMaestro ADD FILE (
    NAME='LabMaestro_data_33H1',
    FILENAME='F:\LABMAESTRO_OLTP\lm_oltp_data_33H1.ndf',
    SIZE=4MB,
    MAXSIZE=UNLIMITED,
    FILEGROWTH=32MB
) TO FILEGROUP FG_Data_33H1
GO

ALTER DATABASE LabMaestro ADD FILE (
    NAME='LabMaestro_data_33H2',
    FILENAME='F:\LABMAESTRO_OLTP\lm_oltp_data_33H2.ndf',
    SIZE=4MB,
    MAXSIZE=UNLIMITED,
    FILEGROWTH=32MB
) TO FILEGROUP FG_Data_33H2
GO





-- function

CREATE PARTITION FUNCTION [PF_OLTP_Date_13_33](smalldatetime) AS RANGE RIGHT FOR VALUES (
 '2013-01-01 00:00:00',
 '2013-07-01 00:00:00',
 '2014-01-01 00:00:00',
 '2014-07-01 00:00:00',
 '2015-01-01 00:00:00',
 '2015-07-01 00:00:00',
 '2016-01-01 00:00:00',
 '2016-07-01 00:00:00',
 '2017-01-01 00:00:00',
 '2017-07-01 00:00:00',
 '2018-01-01 00:00:00',
 '2018-07-01 00:00:00',
 '2019-01-01 00:00:00',
 '2019-07-01 00:00:00',
 '2020-01-01 00:00:00',
 '2020-07-01 00:00:00',
 '2021-01-01 00:00:00',
 '2021-07-01 00:00:00',
 '2022-01-01 00:00:00',
 '2022-07-01 00:00:00',
 '2023-01-01 00:00:00',
 '2023-07-01 00:00:00',
 '2024-01-01 00:00:00',
 '2024-07-01 00:00:00',
 '2025-01-01 00:00:00',
 '2025-07-01 00:00:00',
 '2026-01-01 00:00:00',
 '2026-07-01 00:00:00',
 '2027-01-01 00:00:00',
 '2027-07-01 00:00:00',
 '2028-01-01 00:00:00',
 '2028-07-01 00:00:00',
 '2029-01-01 00:00:00',
 '2029-07-01 00:00:00',
 '2030-01-01 00:00:00',
 '2030-07-01 00:00:00',
 '2031-01-01 00:00:00',
 '2031-07-01 00:00:00',
 '2032-01-01 00:00:00',
 '2032-07-01 00:00:00',
 '2033-01-01 00:00:00',
 '2033-07-01 00:00:00'
);
GO


-- scheme

CREATE PARTITION SCHEME [PS_OLTP_Date_13_33]
AS PARTITION [PF_OLTP_Date_13_33] TO (
  FG_Data_13H1,
  FG_Data_13H1,
  FG_Data_13H2,
  FG_Data_14H1,
  FG_Data_14H2,
  FG_Data_15H1,
  FG_Data_15H2,
  FG_Data_16H1,
  FG_Data_16H2,
  FG_Data_17H1,
  FG_Data_17H2,
  FG_Data_18H1,
  FG_Data_18H2,
  FG_Data_19H1,
  FG_Data_19H2,
  FG_Data_20H1,
  FG_Data_20H2,
  FG_Data_21H1,
  FG_Data_21H2,
  FG_Data_22H1,
  FG_Data_22H2,
  FG_Data_23H1,
  FG_Data_23H2,
  FG_Data_24H1,
  FG_Data_24H2,
  FG_Data_25H1,
  FG_Data_25H2,
  FG_Data_26H1,
  FG_Data_26H2,
  FG_Data_27H1,
  FG_Data_27H2,
  FG_Data_28H1,
  FG_Data_28H2,
  FG_Data_29H1,
  FG_Data_29H2,
  FG_Data_30H1,
  FG_Data_30H2,
  FG_Data_31H1,
  FG_Data_31H2,
  FG_Data_32H1,
  FG_Data_32H2,
  FG_Data_33H1,
  FG_Data_33H2,
  [PRIMARY],
  [PRIMARY] );
GO






-- ResultBundles -----------------------------------------------------------------------------

ALTER TABLE [TestResults].[DiscreteResultLineItems] DROP CONSTRAINT [FK_RESULTBUNDLE_DISCRETE];
GO

ALTER TABLE [TestResults].[TemplateResults] DROP CONSTRAINT [FK_RESULTBUNDLE_TEMPLATE];
GO

ALTER TABLE [TestResults].[RecentlyUpdatedResultBundles] DROP CONSTRAINT [FK_RESULTBUNDLE_UPDATED];
GO

ALTER TABLE [PROE].[OrderedTests] DROP CONSTRAINT [FK_RESULTBUNDLE_TESTS];
GO

--DROP INDEX [PK_RESULT_BUNDLES] ON [TestResults].[ResultBundles];
ALTER TABLE [TestResults].[ResultBundles] DROP CONSTRAINT [PK_RESULT_BUNDLES];
GO


CREATE NONCLUSTERED INDEX [PK_RESULT_BUNDLES]
  ON [TestResults].[ResultBundles]([Id])
  WITH (PAD_INDEX = OFF , FILLFACTOR = 100 , IGNORE_DUP_KEY = OFF , STATISTICS_NORECOMPUTE = OFF , ALLOW_ROW_LOCKS = ON , ALLOW_PAGE_LOCKS = ON , OPTIMIZE_FOR_SEQUENTIAL_KEY=OFF , DATA_COMPRESSION = NONE)
  ON [FG_Indices];
GO

-- DROP INDEX [PIX_RESULT_BUNDLES] ON [TestResults].[ResultBundles];

CREATE CLUSTERED INDEX [PIX_RESULT_BUNDLES]
  ON [TestResults].[ResultBundles]([DateCreated])
  WITH(STATISTICS_NORECOMPUTE = OFF , IGNORE_DUP_KEY = OFF , ALLOW_ROW_LOCKS = ON , ALLOW_PAGE_LOCKS = ON)
  ON [PS_OLTP_Date_13_33] ( [DateCreated] );
GO

ALTER TABLE [TestResults].[DiscreteResultLineItems] ADD CONSTRAINT [FK_RESULTBUNDLE_DISCRETE] FOREIGN KEY ([ResultBundleId]) REFERENCES [TestResults].[ResultBundles]([Id]);
GO

ALTER TABLE [TestResults].[TemplateResults] ADD CONSTRAINT [FK_RESULTBUNDLE_TEMPLATE]  FOREIGN KEY ([ResultBundleId]) REFERENCES [TestResults].[ResultBundles]([Id]) ON DELETE CASCADE;
GO

ALTER TABLE [TestResults].[RecentlyUpdatedResultBundles] ADD CONSTRAINT [FK_RESULTBUNDLE_UPDATED]  FOREIGN KEY ([ResultBundleId]) REFERENCES [TestResults].[ResultBundles]([Id]) ON DELETE CASCADE;
GO

ALTER TABLE [PROE].[OrderedTests] ADD CONSTRAINT [FK_RESULTBUNDLE_TESTS] FOREIGN KEY([ResultBundleId]) REFERENCES [TestResults].[ResultBundles] ([Id]) ON DELETE SET NULL;
GO



-- TemplateResults -----------------------------------------------------

ALTER TABLE [TestResults].[TemplateResults] DROP CONSTRAINT [PK_TEMPLATE_RESULTS];
GO


ALTER TABLE [TestResults].[TemplateResults] ADD CONSTRAINT [PK_TEMPLATE_RESULTS] PRIMARY KEY NONCLUSTERED ([Id] ASC)
   WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) 
   ON [FG_Indices]
GO


-- DROP INDEX [PIX_TEMPLATE_RESULTS] ON [TestResults].[TemplateResults];

CREATE CLUSTERED INDEX [PIX_TEMPLATE_RESULTS]
  ON [TestResults].[TemplateResults]([DateCreated])
  WITH(STATISTICS_NORECOMPUTE = OFF , IGNORE_DUP_KEY = OFF , ALLOW_ROW_LOCKS = ON , ALLOW_PAGE_LOCKS = ON)
  ON [PS_OLTP_Date_13_33] ( [DateCreated] );
GO


-- DiscreteResultLineItems

ALTER TABLE [TestResults].[DiscreteResultLineItems] DROP CONSTRAINT [PK_DISCRETE_RESULTS];
GO

ALTER TABLE [TestResults].[DiscreteResultLineItems] ADD CONSTRAINT [PK_DISCRETE_RESULTS] PRIMARY KEY NONCLUSTERED ([Id] ASC)
   WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) 
   ON [FG_Indices]
GO


-- DROP INDEX [PIX_DISCRETE_RESULTS] ON [TestResults].[DiscreteResultLineItems];

CREATE CLUSTERED INDEX [PIX_DISCRETE_RESULTS]
  ON [TestResults].[DiscreteResultLineItems]([DateCreated])
  WITH(STATISTICS_NORECOMPUTE = OFF , IGNORE_DUP_KEY = OFF , ALLOW_ROW_LOCKS = ON , ALLOW_PAGE_LOCKS = ON)
  ON [PS_OLTP_Date_13_33] ( [DateCreated] );
GO




-- AppFaults

ALTER TABLE [APP_SYS].[AppFaults] DROP CONSTRAINT [PK_APP_FAULTS];
GO

-- ********* convert [SubmissionTime] to smalldatetime


ALTER TABLE [APP_SYS].[AppFaults] ADD CONSTRAINT [PK_APP_FAULTS] PRIMARY KEY NONCLUSTERED ([Id] ASC)
   WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) 
   ON [FG_Indices]
GO


CREATE CLUSTERED INDEX [PIX_APP_FAULTS]
  ON [APP_SYS].[AppFaults]([EventTime])
  WITH(STATISTICS_NORECOMPUTE = OFF , IGNORE_DUP_KEY = OFF , ALLOW_ROW_LOCKS = ON , ALLOW_PAGE_LOCKS = ON)
  ON [PS_OLTP_Date_13_33]([SubmissionTime]);
GO




-- AuditTrails

ALTER TABLE [APP_SYS].[AuditTrails] DROP CONSTRAINT [PK_AUDIT_TRAIL];
GO


ALTER TABLE [APP_SYS].[AuditTrails] ADD CONSTRAINT [PK_AUDIT_TRAIL] PRIMARY KEY NONCLUSTERED ([Id] ASC)
   WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) 
   ON [FG_Indices]
GO



CREATE CLUSTERED INDEX [PIX_AUDIT_TRAIL]
  ON [APP_SYS].[AuditTrails]([EventTime])
  WITH(STATISTICS_NORECOMPUTE = OFF , IGNORE_DUP_KEY = OFF , ALLOW_ROW_LOCKS = ON , ALLOW_PAGE_LOCKS = ON)
  ON [PS_OLTP_Date_13_33]([EventTime]);
GO


--- bundles


ALTER TABLE [TestResults].[TemplateResults] DROP CONSTRAINT [FK_ORDERED_TEST_TEMPLATE];
GO

ALTER TABLE [TestResults].[DiscreteResultLineItems] DROP CONSTRAINT [FK_RESULTMASTER_DISCRETE];
GO

-- OrderedTests

ALTER TABLE [PROE].[OrderedTests] DROP CONSTRAINT [PK_ORDERED_TESTS];
GO

CREATE NONCLUSTERED INDEX [PK_ORDERED_TESTS]
  ON [PROE].[OrderedTests]([Id])
  WITH (PAD_INDEX = OFF , FILLFACTOR = 100 , IGNORE_DUP_KEY = OFF , STATISTICS_NORECOMPUTE = OFF , ALLOW_ROW_LOCKS = ON , ALLOW_PAGE_LOCKS = ON , OPTIMIZE_FOR_SEQUENTIAL_KEY=OFF , DATA_COMPRESSION = NONE)
  ON [FG_Indices];
GO


CREATE CLUSTERED INDEX [PIX_ORDERED_TESTS]
  ON [PROE].[OrderedTests]([DateCreated])
  WITH(STATISTICS_NORECOMPUTE = OFF , IGNORE_DUP_KEY = OFF , ALLOW_ROW_LOCKS = ON , ALLOW_PAGE_LOCKS = ON)
  ON [PS_OLTP_Date_13_33]([DateCreated]);
GO



ALTER TABLE [TestResults].[TemplateResults] ADD CONSTRAINT [FK_ORDERED_TEST_TEMPLATE] FOREIGN KEY ([OrderedTestId]) REFERENCES [PROE].[OrderedTests]([Id]) ON DELETE CASCADE;
GO

ALTER TABLE [TestResults].[DiscreteResultLineItems] ADD CONSTRAINT [FK_RESULTMASTER_DISCRETE]  FOREIGN KEY ([OrderedTestId]) REFERENCES [PROE].[OrderedTests]([Id]) ON DELETE CASCADE;
GO







