-- get current referral and percentage referral for comparison

SELECT
  t.Id,
  t.<PERSON>,
  t.<PERSON>,
  t.<PERSON>,
  l.Name AS Lab,
  r.Name AS PlanName,
  r.<PERSON>,
  r.ReferralPercent AS PercentageRate,
  r.ReferralAmount AS FlatRate,
  CASE
    r.ReferralMode 
    WHEN 10 THEN
    t.ListPrice * ( r.ReferralPercent/ 100 ) ELSE r.ReferralAmount 
  END AS ProjectedReferral 
FROM
  [Catalog].Labs AS l
  INNER JOIN [Catalog].LabTests AS t ON l.Id = t.PerformingLabId
  INNER JOIN Marketing.ReferralGroups AS r ON l.ReferralGroupId = r.Id 
WHERE
  t.IsActive= 1 
  AND l.IsActive= 1 
ORDER BY
  l.Name,
  t.ShortName