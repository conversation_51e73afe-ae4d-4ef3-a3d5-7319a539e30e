year_start = 13
year_end = 33

fg_data_tpl = "FG_Data_{0}H{1},"

fg_tpl = """ALTER DATABASE LabMaestro ADD FILEGROUP FG_Data_{0}H{1}
GO
"""

fg_file_tpl = """ALTER DATABASE LabMaestro ADD FILE (
    NAME='LabMaestro_data_{0}H{1}',
    FILENAME='F:\LABMAESTRO_OLTP\lm_oltp_data_{0}H{1}.ndf',
    SIZE=4MB,
    MAXSIZE=UNLIMITED,
    FILEGROWTH=32MB
) TO FILEGROUP FG_Data_{0}H{1}
GO
"""

part_fn_tpl = """
CREATE PARTITION FUNCTION [PF_OLTP_Date_13_33](smalldatetime) AS RANGE RIGHT FOR VALUES (
{0}
);
GO
"""

alter_part_tpl = """ALTER PARTITION FUNCTION [PF_OLTP_Date_13_33]() SPLIT RANGE ({0});
GO
"""

part_years = []

for i in range(year_start, year_end + 1):
    part_years.append(f" '20{i}-01-01 00:00:00'")
    part_years.append(f" '20{i}-07-01 00:00:00'")

for dt in part_years:
    print(alter_part_tpl.format(dt.strip()))

print(part_fn_tpl.format(",\n".join(part_years)))

for i in range(year_start, year_end + 1):
    for j in [1, 2]:
        print(fg_data_tpl.format(i, j))

for i in range(year_start, year_end + 1):
    for j in [1, 2]:
        print(fg_tpl.format(i, j))
        # print(fg_file_tpl.format(i, j))

for i in range(year_start, year_end + 1):
    for j in [1, 2]:
        # print(fg_tpl.format(i, j))
        print(fg_file_tpl.format(i, j))
