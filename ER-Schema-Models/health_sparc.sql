﻿/*
Created: 2023-09-28
Modified: 2023-10-11
Model: healthsparc
Database: PostgreSQL 12
*/

-- Create tables section -------------------------------------------------

-- Table branches

CREATE TABLE "branches"
(
  "id" Smallserial NOT NULL,
  "is_active" Boolean DEFAULT false NOT NULL,
  "priority" Smallint DEFAULT 0 NOT NULL,
  "uin" Character varying NOT NULL,
  "name" Character varying NOT NULL,
  "phone" Character varying,
  "email" Character varying,
  "address" Text,
  "api_url" Character varying,
  "api_secret" Character varying,
  "login" Character varying,
  "passhash" Character varying,
  "geo_location" Point,
  "map_url" Text,
  "img_url" Character varying,
  "created_at" Timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
  "updated_at" Timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL
)
WITH (
  autovacuum_enabled=true)
;

CREATE INDEX "ix_branches" ON "branches" ("is_active","priority","name")
;

ALTER TABLE "branches" ADD CONSTRAINT "pk_branches" PRIMARY KEY ("id")
;

ALTER TABLE "branches" ADD CONSTRAINT "ak_branches" UNIQUE ("uin")
;

-- Table procedures

CREATE TABLE "procedures"
(
  "id" Serial NOT NULL,
  "branch_id" Smallint,
  "center_id" Smallint,
  "category_id" Smallint,
  "discount_level_id" Smallint,
  "source_guid" Character varying NOT NULL,
  "source_id" Bigint NOT NULL,
  "name" Character varying NOT NULL,
  "is_active" Boolean DEFAULT false NOT NULL,
  "is_universal" Boolean DEFAULT false NOT NULL,
  "list_price" Numeric(12,2) DEFAULT 0.00 NOT NULL,
  "img_url" Character varying,
  "created_at" Timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
  "updated_at" Timestamp
)
WITH (
  autovacuum_enabled=true)
;

CREATE INDEX "ix_procedures_branch" ON "procedures" ("branch_id")
;

CREATE INDEX "ix_procedures" ON "procedures" ("is_active","name")
;

CREATE INDEX "ix_procedures_source" ON "procedures" ("branch_id","source_id")
;

CREATE INDEX "ix_procedures_center" ON "procedures" ("center_id")
;

CREATE INDEX "ix_procedures_category" ON "procedures" ("category_id")
;

CREATE INDEX "ix_procedures_discount" ON "procedures" ("discount_level_id")
;

CREATE INDEX "ix_procedures_universal" ON "procedures" ("is_universal","is_active")
;

ALTER TABLE "procedures" ADD CONSTRAINT "pk_procedures" PRIMARY KEY ("id")
;

ALTER TABLE "procedures" ADD CONSTRAINT "ak_procedures_source" UNIQUE ("source_guid")
;

-- Table procedure_categories

CREATE TABLE "procedure_categories"
(
  "id" Smallserial NOT NULL,
  "is_active" Boolean DEFAULT false NOT NULL,
  "is_universal" Boolean DEFAULT false NOT NULL,
  "priority" Smallint DEFAULT 0 NOT NULL,
  "center_id" Smallint,
  "branch_id" Smallint,
  "name" Character varying NOT NULL,
  "img_url" Character varying,
  "created_at" Timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
  "updated_at" Timestamp
)
WITH (
  autovacuum_enabled=true)
;

CREATE INDEX "ix_procedure_categories_universal" ON "procedure_categories" ("is_active","is_universal","name")
;

CREATE INDEX "ix_procedure_categories_center" ON "procedure_categories" ("center_id")
;

CREATE INDEX "ix_procedure_categories_branch" ON "procedure_categories" ("branch_id")
;

CREATE INDEX "ix_procedure_categories" ON "procedure_categories" ("is_active","is_universal","priority","branch_id","center_id","name")
;

ALTER TABLE "procedure_categories" ADD CONSTRAINT "pk_procedure_categories" PRIMARY KEY ("id")
;

ALTER TABLE "procedure_categories" ADD CONSTRAINT "ak_procedure_categories" UNIQUE ("name")
;

-- Table referrers

CREATE TABLE "referrers"
(
  "id" Serial NOT NULL,
  "branch_id" Smallint,
  "is_active" Boolean DEFAULT false NOT NULL,
  "name" Character varying NOT NULL,
  "upin" Character varying NOT NULL,
  "source_id" Bigint NOT NULL,
  "source_guid" Character varying NOT NULL,
  "login" Character varying NOT NULL,
  "passhash" Character varying NOT NULL,
  "clv" Numeric(12,2) DEFAULT 0.00 NOT NULL,
  "created_at" Timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
  "updated_at" Timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL
)
WITH (
  autovacuum_enabled=true)
;

CREATE INDEX "ix_referrers_branch" ON "referrers" ("branch_id")
;

CREATE INDEX "ix_referrers_source" ON "referrers" ("source_id","branch_id")
;

CREATE INDEX "ix_referrers" ON "referrers" ("is_active","branch_id","name")
;

ALTER TABLE "referrers" ADD CONSTRAINT "pk_referrers" PRIMARY KEY ("id")
;

ALTER TABLE "referrers" ADD CONSTRAINT "ak_referrers_login" UNIQUE ("login")
;

ALTER TABLE "referrers" ADD CONSTRAINT "ak_referrers_upin" UNIQUE ("upin")
;

-- Table health_packages

CREATE TABLE "health_packages"
(
  "id" Smallserial NOT NULL,
  "branch_id" Smallint,
  "is_active" Boolean DEFAULT false NOT NULL,
  "is_universal" Boolean DEFAULT false NOT NULL,
  "priority" Smallint DEFAULT 0 NOT NULL,
  "name" Character varying NOT NULL,
  "gross_value" Integer NOT NULL,
  "net_price" Integer NOT NULL,
  "num_tests" Smallint DEFAULT 0 NOT NULL,
  "img_url" Character varying,
  "description" Text,
  "remarks" Text,
  "created_at" Timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
  "updated_at" Timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL
)
WITH (
  autovacuum_enabled=true)
;

CREATE INDEX "ix_health_packages_branch" ON "health_packages" ("branch_id")
;

CREATE INDEX "ix_health_packages" ON "health_packages" ("is_universal","is_active","priority","branch_id","name")
;

ALTER TABLE "health_packages" ADD CONSTRAINT "pk_health_packages" PRIMARY KEY ("id")
;

-- Table fulfillment_centers

CREATE TABLE "fulfillment_centers"
(
  "id" Smallserial NOT NULL,
  "branch_id" Smallint NOT NULL,
  "uin" Character varying NOT NULL,
  "priority" Smallint DEFAULT 0 NOT NULL,
  "is_active" Boolean DEFAULT false NOT NULL,
  "name" Character varying NOT NULL,
  "address" Text,
  "phone" Character varying,
  "email" Character varying,
  "geo_location" Point,
  "img_url" Character varying,
  "map_url" Text,
  "created_at" Timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
  "updated_at" Timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL
)
WITH (
  autovacuum_enabled=true)
;

CREATE INDEX "ix_fulfillment_centers_branch" ON "fulfillment_centers" ("branch_id")
;

CREATE INDEX "ix_fulfillment_centers" ON "fulfillment_centers" ("is_active","priority","name")
;

ALTER TABLE "fulfillment_centers" ADD CONSTRAINT "pk_fulfillment_centers" PRIMARY KEY ("id")
;

ALTER TABLE "fulfillment_centers" ADD CONSTRAINT "ak_fulfillment_centers" UNIQUE ("uin")
;

-- Table patients

CREATE TABLE "patients"
(
  "id" BigSerial NOT NULL,
  "corporate_id" Smallint,
  "upin" Character varying NOT NULL,
  "is_active" Boolean DEFAULT false NOT NULL,
  "web_access" Boolean DEFAULT false NOT NULL,
  "title" Character varying,
  "first_name" Character varying NOT NULL,
  "last_name" Character varying,
  "login" Character varying,
  "passhash" Character varying,
  "sex" Smallint DEFAULT 0 NOT NULL,
  "age" Smallint,
  "dob" Date,
  "email" Character varying,
  "phone" Character varying,
  "phone2" Character varying,
  "fathers_name" Character varying,
  "mothers_name" Character varying,
  "marital_status" Smallint,
  "occupation" Character varying,
  "company" Character varying,
  "contact_name1" Character varying,
  "contact_phone1" Character varying,
  "contact_name2" Character varying,
  "contact_phone2" Character varying,
  "nid_num" Character varying,
  "passport_num" Character varying,
  "passport_expiry_date" Date,
  "nationality_id" Smallint,
  "region_id" Smallint,
  "num_orders" Integer DEFAULT 0 NOT NULL,
  "clv" Numeric(12,2) DEFAULT 0.00 NOT NULL,
  "enrolled_at" Timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
  "created_at" Timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
  "updated_at" Timestamp
)
WITH (
  autovacuum_enabled=true)
;

CREATE INDEX "ix_patients_corporate" ON "patients" ("corporate_id")
;

CREATE INDEX "ix_patients_web" ON "patients" ("is_active","web_access","login")
;

CREATE INDEX "ix_patients_location" ON "patients" ("region_id")
;

CREATE INDEX "ix_patients_nationality" ON "patients" ("nationality_id")
;

CREATE INDEX "ix_patients_phone" ON "patients" ("phone")
;

CREATE INDEX "ix_patients_email" ON "patients" ("email")
;

ALTER TABLE "patients" ADD CONSTRAINT "pk_patients" PRIMARY KEY ("id")
;

ALTER TABLE "patients" ADD CONSTRAINT "ak_patients_upin" UNIQUE ("upin")
;

-- Table orders

CREATE TABLE "orders"
(
  "id" BigSerial NOT NULL,
  "branch_id" Smallint NOT NULL,
  "center_id" Smallint NOT NULL,
  "patient_id" Bigint NOT NULL,
  "corporate_id" Smallint,
  "associate_lab_id" Smallint,
  "associate_lab_tracking_id" Character varying,
  "is_outer_order" Boolean DEFAULT false NOT NULL,
  "source_invoice_id" Bigint NOT NULL,
  "source_order_id" Character varying NOT NULL,
  "source_order_datetime" Timestamp NOT NULL,
  "is_canceled" Boolean DEFAULT false NOT NULL,
  "is_immutable_order" Boolean DEFAULT false NOT NULL,
  "disallow_referral" Boolean DEFAULT false NOT NULL,
  "payment_status" Smallint NOT NULL,
  "gross_payable" Numeric(12,2) DEFAULT 0.00 NOT NULL,
  "discount_amount" Numeric(12,2) DEFAULT 0.00 NOT NULL,
  "tax_amount" Numeric(12,2) DEFAULT 0.00 NOT NULL,
  "surcharge_amount" Numeric(12,2) DEFAULT 0.00 NOT NULL,
  "net_payable" Numeric(12,2) DEFAULT 0.00 NOT NULL,
  "paid_amount" Numeric(12,2) DEFAULT 0.00 NOT NULL,
  "due_amount" Numeric(12,2) DEFAULT 0.00 NOT NULL,
  "referrer_custom_name" Character varying,
  "referrer_id" Integer,
  "affiliate_id" Integer,
  "package_id" Smallint,
  "coupon_id" Smallint,
  "tax_id" Smallint,
  "created_at" Timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
  "updated_at" Timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL
)
WITH (
  autovacuum_enabled=true)
;

CREATE INDEX "ix_orders_branch" ON "orders" ("branch_id")
;

CREATE INDEX "ix_orders_center" ON "orders" ("center_id")
;

CREATE INDEX "ix_orders_patient" ON "orders" ("patient_id")
;

CREATE INDEX "ix_orders_coupon" ON "orders" ("coupon_id")
;

CREATE INDEX "ix_orders_referrer" ON "orders" ("referrer_id")
;

CREATE INDEX "ix_orders_tax" ON "orders" ("tax_id")
;

CREATE INDEX "ix_orders_affiliate" ON "orders" ("affiliate_id")
;

CREATE INDEX "ix_orders_corporate" ON "orders" ("corporate_id")
;

CREATE INDEX "ix_orders_referring_lab" ON "orders" ("associate_lab_id")
;

CREATE INDEX "ix_orders_referring_lab_order" ON "orders" ("associate_lab_tracking_id","associate_lab_id","source_order_datetime")
;

CREATE INDEX "ix_orders_package" ON "orders" ("package_id")
;

ALTER TABLE "orders" ADD CONSTRAINT "pk_orders" PRIMARY KEY ("id")
;

-- Table affiliates

CREATE TABLE "affiliates"
(
  "id" Serial NOT NULL,
  "branch_id" Smallint,
  "is_active" Boolean DEFAULT false NOT NULL,
  "name" Character varying NOT NULL,
  "source_guid" Character varying NOT NULL,
  "source_id" Bigint NOT NULL,
  "login" Character varying NOT NULL,
  "passhash" Character varying NOT NULL,
  "created_at" Timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
  "updated_at" Timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL
)
WITH (
  autovacuum_enabled=true)
;

CREATE INDEX "ix_affiliates_source" ON "affiliates" ("source_id","branch_id")
;

CREATE INDEX "ix_affiliates" ON "affiliates" ("is_active","branch_id","name")
;

CREATE INDEX "ix_affiliates_branch" ON "affiliates" ("branch_id")
;

ALTER TABLE "affiliates" ADD CONSTRAINT "pk_affiliates" PRIMARY KEY ("id")
;

ALTER TABLE "affiliates" ADD CONSTRAINT "ak_affiliates" UNIQUE ("login")
;

-- Table order_procedures

CREATE TABLE "order_procedures"
(
  "id" BigSerial NOT NULL,
  "order_id" Bigint NOT NULL,
  "procedure_id" Integer NOT NULL,
  "source_id" Bigint,
  "workflow_stage" Smallint DEFAULT 0 NOT NULL,
  "is_canceled" Boolean DEFAULT false NOT NULL,
  "unit_price" Numeric(12,2) DEFAULT 0.00 NOT NULL,
  "created_at" Timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
  "updated_at" Timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL
)
WITH (
  autovacuum_enabled=true)
;

CREATE INDEX "ix_order_procedures_order" ON "order_procedures" ("order_id")
;

CREATE INDEX "ix_order_procedures_procedure" ON "order_procedures" ("procedure_id")
;

ALTER TABLE "order_procedures" ADD CONSTRAINT "pk_order_procedures" PRIMARY KEY ("id")
;

-- Table coupons

CREATE TABLE "coupons"
(
  "id" Smallserial NOT NULL,
  "branch_id" Smallint,
  "is_active" Boolean DEFAULT false NOT NULL,
  "code" Character varying NOT NULL,
  "start_date" Date,
  "expiry_date" Date,
  "remarks" Text,
  "created_at" Timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
  "updated_at" Timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
  "discount_amount" Numeric(12,2) DEFAULT 0.00 NOT NULL,
  "discount_type" Smallint NOT NULL,
  "minimum_order" Numeric(12,2),
  "maximum_discount" Numeric(12,2),
  "limit_per_patient" Smallint
)
WITH (
  autovacuum_enabled=true)
;

CREATE INDEX "ix_coupons" ON "coupons" ("is_active","code")
;

CREATE INDEX "ix_coupons_branch" ON "coupons" ("branch_id")
;

ALTER TABLE "coupons" ADD CONSTRAINT "pk_coupons" PRIMARY KEY ("id")
;

ALTER TABLE "coupons" ADD CONSTRAINT "ak_coupons" UNIQUE ("code")
;

-- Table order_transactions

CREATE TABLE "order_transactions"
(
  "id" BigSerial NOT NULL,
  "order_id" Bigint NOT NULL,
  "tx_type" Smallint NOT NULL,
  "tx_time" Timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
  "tx_amount" Numeric(12,2) DEFAULT 0.00 NOT NULL,
  "tx_flag" Smallint NOT NULL,
  "source_id" Bigint,
  "performing_user" Character varying,
  "payment_provider" Character varying,
  "payment_ref" Character varying,
  "ip_addr" Character varying,
  "remarks" Text,
  "created_at" Timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
  "updated_at" Timestamp
)
WITH (
  autovacuum_enabled=true)
;

CREATE INDEX "ix_order_transactions_order" ON "order_transactions" ("order_id")
;

CREATE INDEX "ix_order_transactions_branch_id" ON "order_transactions" ("source_id")
;

CREATE INDEX "ix_order_transactions" ON "order_transactions" ("order_id","tx_time")
;

ALTER TABLE "order_transactions" ADD CONSTRAINT "pk_order_transactions" PRIMARY KEY ("id")
;

-- Table discount_levels

CREATE TABLE "discount_levels"
(
  "id" Smallserial NOT NULL,
  "branch_id" Smallint,
  "is_active" Boolean NOT NULL,
  "is_universal" Boolean NOT NULL,
  "mode" Smallint NOT NULL,
  "percentage" Numeric(2,2) DEFAULT 0.0 NOT NULL,
  "amount" Numeric(12,2) DEFAULT 0.00 NOT NULL,
  "created_at" Timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
  "updated_at" Timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL
)
WITH (
  autovacuum_enabled=true)
;

CREATE INDEX "ix_discount_levels_branch" ON "discount_levels" ("branch_id")
;

CREATE INDEX "ix_discount_levels" ON "discount_levels" ("is_active","is_universal","branch_id")
;

ALTER TABLE "discount_levels" ADD CONSTRAINT "pk_discount_levels" PRIMARY KEY ("id")
;

-- Table patient_addresses

CREATE TABLE "patient_addresses"
(
  "id" Bigint NOT NULL,
  "patient_id" Bigint NOT NULL,
  "is_default" Boolean DEFAULT false NOT NULL,
  "label" Character varying NOT NULL,
  "address" Text NOT NULL,
  "lat" Numeric(10,8),
  "lng" Numeric(11,8)
)
WITH (
  autovacuum_enabled=true)
;

CREATE INDEX "ix_patient_addresses_patient" ON "patient_addresses" ("patient_id")
;

ALTER TABLE "patient_addresses" ADD CONSTRAINT "pk_patient_addresses" PRIMARY KEY ("id")
;

-- Table messages

CREATE TABLE "messages"
(
  "id" BigSerial NOT NULL,
  "branch_id" Smallint,
  "patient_id" Bigint,
  "referrer_id" Integer,
  "order_id" Bigint,
  "sent_at" Timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
  "seen_at" Timestamp,
  "content" Text NOT NULL,
  "updated_at" Timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL
)
WITH (
  autovacuum_enabled=true)
;

CREATE INDEX "ix_messages_patient" ON "messages" ("patient_id")
;

CREATE INDEX "ix_messages_branch" ON "messages" ("branch_id")
;

CREATE INDEX "ix_messages_order" ON "messages" ("order_id")
;

CREATE INDEX "ix_messages_referrer" ON "messages" ("referrer_id")
;

CREATE INDEX "ix_messages_patient_view" ON "messages" ("patient_id","sent_at")
;

CREATE INDEX "ix_messages_referrer_view" ON "messages" ("referrer_id","sent_at")
;

CREATE INDEX "ix_messages_order_view" ON "messages" ("sent_at","order_id")
;

ALTER TABLE "messages" ADD CONSTRAINT "pk_messages" PRIMARY KEY ("id")
;

-- Table taxes

CREATE TABLE "taxes"
(
  "id" Smallserial NOT NULL,
  "name" Character varying NOT NULL,
  "code" Character varying NOT NULL,
  "tax_rate" Numeric(5,2) DEFAULT 0.00 NOT NULL,
  "tax_type" Smallint NOT NULL,
  "is_active" Boolean DEFAULT false NOT NULL,
  "created_at" Timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
  "updated_at" Timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL
)
WITH (
  autovacuum_enabled=true)
;

CREATE INDEX "ix_taxes" ON "taxes" ("is_active","name")
;

ALTER TABLE "taxes" ADD CONSTRAINT "pk_taxes" PRIMARY KEY ("id")
;

ALTER TABLE "taxes" ADD CONSTRAINT "ak_taxes" UNIQUE ("code")
;

-- Table activity_logs

CREATE TABLE "activity_logs"
(
  "id" BigSerial NOT NULL,
  "event_time" Timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
  "patient_id" Bigint,
  "referrer_id" Integer,
  "staff_id" Smallint,
  "order_id" Bigint,
  "device_id" Integer,
  "activity_id" Smallint NOT NULL,
  "ip_addr" Inet,
  "remarks" Text
)
WITH (
  autovacuum_enabled=true)
;

CREATE INDEX "ix_activity_logs_patient" ON "activity_logs" ("patient_id")
;

CREATE INDEX "ix_activity_logs_patient_activities" ON "activity_logs" ("patient_id","event_time")
;

CREATE INDEX "ix_activity_logs_referrer" ON "activity_logs" ("referrer_id")
;

CREATE INDEX "ix_activity_logs_referrer_activities" ON "activity_logs" ("referrer_id","event_time")
;

CREATE INDEX "ix_activity_logs_staff" ON "activity_logs" ("staff_id")
;

CREATE INDEX "ix_activity_logs_staff_activities" ON "activity_logs" ("staff_id","event_time")
;

CREATE INDEX "ix_activity_logs_ip" ON "activity_logs" ("ip_addr")
;

ALTER TABLE "activity_logs" ADD CONSTRAINT "pk_activity_logs" PRIMARY KEY ("id")
;

-- Table devices

CREATE TABLE "devices"
(
  "id" Serial NOT NULL,
  "name" Character varying NOT NULL,
  "created_at" Timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL
)
WITH (
  autovacuum_enabled=true)
;

ALTER TABLE "devices" ADD CONSTRAINT "PK_devices" PRIMARY KEY ("id")
;

ALTER TABLE "devices" ADD CONSTRAINT "name" UNIQUE ("name")
;

-- Table activities

CREATE TABLE "activities"
(
  "id" Smallserial NOT NULL,
  "name" Character varying NOT NULL
)
WITH (
  autovacuum_enabled=true)
;

ALTER TABLE "activities" ADD CONSTRAINT "pk_activities" PRIMARY KEY ("id")
;

ALTER TABLE "activities" ADD CONSTRAINT "ak_activities" UNIQUE ("name")
;

-- Table package_components

CREATE TABLE "package_components"
(
  "id" Serial NOT NULL,
  "package_id" Smallint NOT NULL,
  "procedure_id" Integer NOT NULL
)
WITH (
  autovacuum_enabled=true)
;

CREATE INDEX "ix_package_components_package" ON "package_components" ("package_id")
;

CREATE INDEX "ix_package_components_procedure" ON "package_components" ("procedure_id")
;

CREATE UNIQUE INDEX "ak_package_components" ON "package_components" ("package_id","procedure_id")
;

ALTER TABLE "package_components" ADD CONSTRAINT "pk_package_components" PRIMARY KEY ("id")
;

-- Table branch_sales

CREATE TABLE "branch_sales"
(
  "id" BigSerial NOT NULL,
  "branch_id" Smallint NOT NULL,
  "sale_date" Date DEFAULT CURRENT_DATE NOT NULL,
  "num_orders" Smallint DEFAULT 0 NOT NULL,
  "num_cancels" Smallint DEFAULT 0 NOT NULL,
  "num_referred" Smallint DEFAULT 0 NOT NULL,
  "gross_payable" Numeric(12,2) DEFAULT 0.00 NOT NULL,
  "discount_amount" Numeric(12,2) DEFAULT 0.00 NOT NULL,
  "net_payable" Numeric(12,2) DEFAULT 0.00 NOT NULL,
  "refund_amount" Numeric(12,2) DEFAULT 0.00 NOT NULL,
  "due_amount" Numeric(12,2) DEFAULT 0.00 NOT NULL,
  "paid_amount" Numeric(12,2) DEFAULT 0.00 NOT NULL,
  "referral_amount" Numeric(12,2) DEFAULT 0.00 NOT NULL,
  "created_at" Timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
  "updated_at" Timestamp
)
WITH (
  autovacuum_enabled=true)
;

CREATE INDEX "ix_branch_sales_branch" ON "branch_sales" ("branch_id")
;

CREATE UNIQUE INDEX "ak_branch_sales" ON "branch_sales" ("branch_id","sale_date")
;

ALTER TABLE "branch_sales" ADD CONSTRAINT "pk_branch_sales" PRIMARY KEY ("id")
;

-- Table users

CREATE TABLE "users"
(
  "id" Smallserial NOT NULL,
  "branch_id" Smallint,
  "center_id" Smallint,
  "role_id" Smallint NOT NULL,
  "is_active" Boolean DEFAULT false NOT NULL,
  "name" Character varying NOT NULL,
  "department" Character varying,
  "login" Character varying NOT NULL,
  "passhash" Character varying,
  "phone" Character varying,
  "email" Character varying,
  "last_seen_at" Timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
  "created_at" Timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
  "updated_at" Timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL
)
WITH (
  autovacuum_enabled=true)
;

CREATE INDEX "ix_users_branch" ON "users" ("branch_id")
;

CREATE INDEX "ix_users_center" ON "users" ("center_id")
;

CREATE INDEX "IX_Relationship38" ON "users" ("role_id")
;

ALTER TABLE "users" ADD CONSTRAINT "pk_users" PRIMARY KEY ("id")
;

ALTER TABLE "users" ADD CONSTRAINT "ak_users" UNIQUE ("login")
;

-- Table corporate_clients

CREATE TABLE "corporate_clients"
(
  "id" Smallserial NOT NULL,
  "branch_id" Smallint,
  "source_guid" Character varying NOT NULL,
  "is_active" Boolean DEFAULT false NOT NULL,
  "web_access" Boolean DEFAULT false NOT NULL,
  "name" Character varying NOT NULL,
  "login" Character varying NOT NULL,
  "passhash" Character varying,
  "address" Text,
  "phone" Character varying,
  "email" Character varying,
  "contact_person" Character varying,
  "clv" Numeric(12,2) NOT NULL,
  "wallet_balance" Numeric(12,2) NOT NULL,
  "created_at" Timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
  "updated_at" Timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL
)
WITH (
  autovacuum_enabled=true)
;

CREATE INDEX "ix_corporate_clients_branch" ON "corporate_clients" ("branch_id")
;

CREATE INDEX "ix_corporate_clients_web" ON "corporate_clients" ("is_active","web_access","login")
;

CREATE INDEX "ix_corporate_clients" ON "corporate_clients" ("is_active","name")
;

ALTER TABLE "corporate_clients" ADD CONSTRAINT "pk_corporate_clients" PRIMARY KEY ("id")
;

ALTER TABLE "corporate_clients" ADD CONSTRAINT "ak_corporate_clients_userrname" UNIQUE ("login")
;

ALTER TABLE "corporate_clients" ADD CONSTRAINT "ak_corporate_clients_source" UNIQUE ("source_guid")
;

-- Table associate_labs

CREATE TABLE "associate_labs"
(
  "id" Smallserial NOT NULL,
  "branch_id" Smallint NOT NULL,
  "source_guid" Character varying NOT NULL,
  "name" Character varying NOT NULL,
  "is_active" Boolean DEFAULT false NOT NULL,
  "web_access" Boolean DEFAULT false NOT NULL,
  "login" Character varying NOT NULL,
  "passhash" Character varying,
  "created_at" Timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
  "updated_at" Timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL
)
WITH (
  autovacuum_enabled=true)
;

CREATE INDEX "ix_associate_labs_branch" ON "associate_labs" ("branch_id")
;

CREATE INDEX "ix_associate_labs_web" ON "associate_labs" ("login","is_active","web_access")
;

CREATE INDEX "ix_associate_labs" ON "associate_labs" ("is_active","name")
;

ALTER TABLE "associate_labs" ADD CONSTRAINT "pk_associate_labs" PRIMARY KEY ("id")
;

ALTER TABLE "associate_labs" ADD CONSTRAINT "ak_associate_labs_name" UNIQUE ("name")
;

ALTER TABLE "associate_labs" ADD CONSTRAINT "ak_associate_labs_login" UNIQUE ("login")
;

ALTER TABLE "associate_labs" ADD CONSTRAINT "ak_associate_labs_source" UNIQUE ("source_guid")
;

-- Table bulk_payments

CREATE TABLE "bulk_payments"
(
  "id" Serial NOT NULL,
  "corporate_id" Smallint NOT NULL,
  "branch_id" Smallint NOT NULL,
  "user_id" Smallint,
  "source_id" Bigint,
  "source_guid" Character varying,
  "settled_on" Date DEFAULT CURRENT_DATE NOT NULL,
  "billing_code" Character varying,
  "billing_period_start" Date,
  "billing_period_end" Date,
  "payment_method" Smallint NOT NULL,
  "gross_payable" Numeric(12,2) DEFAULT 0.00 NOT NULL,
  "discount_amount" Numeric(12,2) DEFAULT 0.00 NOT NULL,
  "deductions" Numeric(12,2) DEFAULT 0.00 NOT NULL,
  "net_payable" Numeric(12,2) DEFAULT 0.00 NOT NULL,
  "paid_amount" Numeric(12,2) DEFAULT 0.00 NOT NULL,
  "due_amount" Numeric(12,2) DEFAULT 0.00 NOT NULL,
  "settlement_status" Smallint NOT NULL,
  "originator_name" Character varying,
  "originator_ref" Character varying,
  "originator_bank" Character varying,
  "payee_bank" Character varying,
  "payment_ref" Character varying,
  "remarks" Character varying,
  "created_at" Date DEFAULT CURRENT_DATE NOT NULL,
  "updated_at" Timestamp
)
WITH (
  autovacuum_enabled=true)
;

CREATE INDEX "ix_bulk_payments_corporate" ON "bulk_payments" ("corporate_id")
;

CREATE INDEX "ix_bulk_payments_branch" ON "bulk_payments" ("branch_id")
;

CREATE INDEX "ix_bulk_payments_staff" ON "bulk_payments" ("user_id")
;

CREATE INDEX "ix_bulk_payments_source" ON "bulk_payments" ("source_id")
;

ALTER TABLE "bulk_payments" ADD CONSTRAINT "pk_bulk_payments" PRIMARY KEY ("id")
;

-- Table roles

CREATE TABLE "roles"
(
  "id" Smallserial NOT NULL,
  "is_active" Boolean DEFAULT false NOT NULL,
  "name" Character varying NOT NULL,
  "code" Character varying NOT NULL,
  "god_mode" Boolean DEFAULT false NOT NULL
)
WITH (
  autovacuum_enabled=true)
;

CREATE INDEX "ix_roles_code" ON "roles" ("code","is_active")
;

CREATE INDEX "ix_roles_name" ON "roles" ("name","is_active")
;

ALTER TABLE "roles" ADD CONSTRAINT "pk_roles" PRIMARY KEY ("id")
;

ALTER TABLE "roles" ADD CONSTRAINT "ak_roles" UNIQUE ("code")
;

-- Table associate_lab_users

CREATE TABLE "associate_lab_users"
(
  "id" Serial NOT NULL,
  "associate_lab_id" Smallint NOT NULL,
  "is_active" Boolean DEFAULT false NOT NULL,
  "name" Character varying NOT NULL,
  "login" Character varying NOT NULL,
  "passhash" Character varying,
  "phone" Character varying,
  "email" Character varying,
  "last_seen_at" Timestamp,
  "created_at" Timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
  "updated_at" Timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL
)
WITH (
  autovacuum_enabled=true)
;

CREATE INDEX "ix_associate_lab_users_lab" ON "associate_lab_users" ("associate_lab_id")
;

CREATE INDEX "ix_associate_lab_users" ON "associate_lab_users" ("is_active","login")
;

ALTER TABLE "associate_lab_users" ADD CONSTRAINT "pk_associate_lab_users" PRIMARY KEY ("id")
;

ALTER TABLE "associate_lab_users" ADD CONSTRAINT "ak_associate_lab_users" UNIQUE ("login")
;

-- Table geo_regions

CREATE TABLE "geo_regions"
(
  "id" Smallserial NOT NULL,
  "is_active" Boolean DEFAULT false NOT NULL,
  "priority" Smallint DEFAULT 0 NOT NULL,
  "name" Character varying NOT NULL,
  "region" Character varying
)
WITH (
  autovacuum_enabled=true)
;

CREATE INDEX "ix_geo_regions" ON "geo_regions" ("is_active","priority","name")
;

ALTER TABLE "geo_regions" ADD CONSTRAINT "pk_geo_regions" PRIMARY KEY ("id")
;

-- Table permissions

CREATE TABLE "permissions"
(
  "id" Smallserial NOT NULL,
  "is_active" Boolean DEFAULT false NOT NULL,
  "name" Character varying NOT NULL,
  "code" Character varying NOT NULL
)
WITH (
  autovacuum_enabled=true)
;

CREATE INDEX "ix_permissions" ON "permissions" ("is_active","code")
;

ALTER TABLE "permissions" ADD CONSTRAINT "pk_permissions" PRIMARY KEY ("id")
;

ALTER TABLE "permissions" ADD CONSTRAINT "ak_permissions" UNIQUE ("code")
;

-- Table role_permission_links

CREATE TABLE "role_permission_links"
(
  "id" Serial NOT NULL,
  "role_id" Smallint NOT NULL,
  "permission_id" Smallint NOT NULL
)
WITH (
  autovacuum_enabled=true)
;

CREATE INDEX "ix_role_permission_links_role" ON "role_permission_links" ("role_id")
;

CREATE INDEX "ix_role_permission_links_permission" ON "role_permission_links" ("permission_id")
;

CREATE UNIQUE INDEX "ak_role_permission_links" ON "role_permission_links" ("permission_id","role_id")
;

ALTER TABLE "role_permission_links" ADD CONSTRAINT "pk_role_permission_links" PRIMARY KEY ("id")
;

-- Table referrer_sales

CREATE TABLE "referrer_sales"
(
  "id" BigSerial NOT NULL,
  "sale_date" Date DEFAULT CURRENT_DATE NOT NULL,
  "referrer_id" Integer,
  "num_orders" Smallint DEFAULT 0 NOT NULL,
  "gross_payable" Numeric(12,2) DEFAULT 0.00 NOT NULL,
  "discount_amount" Numeric(12,2) DEFAULT 0.00 NOT NULL,
  "net_payable" Numeric(12,2) DEFAULT 0.00 NOT NULL,
  "paid_amount" Numeric(12,2) DEFAULT 0.00 NOT NULL,
  "due_amount" Numeric(12,2) DEFAULT 0.00 NOT NULL,
  "referral_amount" Numeric(12,2) DEFAULT 0.00 NOT NULL,
  "created_at" Timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
  "updated_at" Timestamp
)
WITH (
  autovacuum_enabled=true)
;

CREATE INDEX "ix_referrer_sales_referrer" ON "referrer_sales" ("referrer_id")
;

CREATE UNIQUE INDEX "ak_referrer_sales" ON "referrer_sales" ("referrer_id","sale_date")
;

ALTER TABLE "referrer_sales" ADD CONSTRAINT "pk_referrer_sales" PRIMARY KEY ("id")
;

-- Table referrer_procedure_sales

CREATE TABLE "referrer_procedure_sales"
(
  "id" BigSerial NOT NULL,
  "sale_date" Date DEFAULT CURRENT_DATE NOT NULL,
  "referrer_id" Integer NOT NULL,
  "procedure_id" Integer NOT NULL,
  "count" Smallint DEFAULT 0 NOT NULL,
  "value" Numeric(12,2) DEFAULT 0.00 NOT NULL,
  "created_at" Timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
  "updated_at" Timestamp
)
WITH (
  autovacuum_enabled=true)
;

CREATE INDEX "ix_referrer_procedure_sales_referrer" ON "referrer_procedure_sales" ("referrer_id")
;

CREATE INDEX "ix_referrer_procedure_sales_procedure" ON "referrer_procedure_sales" ("procedure_id")
;

CREATE UNIQUE INDEX "ak_referrer_procedure_sales" ON "referrer_procedure_sales" ("sale_date","referrer_id","procedure_id")
;

ALTER TABLE "referrer_procedure_sales" ADD CONSTRAINT "pk_referrer_procedure_sales" PRIMARY KEY ("id")
;

-- Table branch_procedure_sales

CREATE TABLE "branch_procedure_sales"
(
  "id" BigSerial NOT NULL,
  "sale_date" Date DEFAULT CURRENT_DATE NOT NULL,
  "branch_id" Smallint NOT NULL,
  "procedure_id" Integer NOT NULL,
  "category_id" Smallint,
  "count" Smallint DEFAULT 0 NOT NULL,
  "value" Numeric(12,2) DEFAULT 0.00 NOT NULL,
  "created_at" Timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
  "updated_at" Timestamp
)
WITH (
  autovacuum_enabled=true)
;

CREATE INDEX "ix_branch_procedure_sales_procedure" ON "branch_procedure_sales" ("procedure_id")
;

CREATE INDEX "ix_branch_procedure_sales_branch" ON "branch_procedure_sales" ("branch_id")
;

CREATE UNIQUE INDEX "ak_branch_procedure_sales" ON "branch_procedure_sales" ("branch_id","sale_date","procedure_id")
;

CREATE INDEX "ix_branch_procedure_sales_procedure_sales" ON "branch_procedure_sales" ("sale_date","procedure_id")
;

CREATE INDEX "ix_branch_procedure_sales_branch_sales" ON "branch_procedure_sales" ("branch_id","sale_date")
;

CREATE INDEX "ix_branch_procedure_sales_category" ON "branch_procedure_sales" ("category_id")
;

ALTER TABLE "branch_procedure_sales" ADD CONSTRAINT "pk_branch_procedure_sales" PRIMARY KEY ("id")
;

-- Table settings

CREATE TABLE "settings"
(
  "id" Smallserial NOT NULL,
  "key" Character varying NOT NULL,
  "val_str" Text,
  "val_int" Bigint,
  "val_float" Numeric(12,3),
  "branch_id" Smallint,
  "user_id" Smallint
)
WITH (
  autovacuum_enabled=true)
;

CREATE INDEX "ix_settings_branch" ON "settings" ("branch_id")
;

CREATE INDEX "ix_settings" ON "settings" ("key","branch_id")
;

CREATE INDEX "ix_settings_user" ON "settings" ("user_id")
;

ALTER TABLE "settings" ADD CONSTRAINT "pk_settings" PRIMARY KEY ("id")
;

-- Table nationalities

CREATE TABLE "nationalities"
(
  "id" Smallserial NOT NULL,
  "name" Character varying NOT NULL
)
WITH (
  autovacuum_enabled=true)
;

ALTER TABLE "nationalities" ADD CONSTRAINT "pk_nationalities" PRIMARY KEY ("id")
;

ALTER TABLE "nationalities" ADD CONSTRAINT "ak_nationalities" UNIQUE ("name")
;

-- Create foreign keys (relationships) section -------------------------------------------------

ALTER TABLE "procedures"
  ADD CONSTRAINT "fk_branch_procedures"
    FOREIGN KEY ("branch_id")
    REFERENCES "branches" ("id")
      ON DELETE CASCADE
      ON UPDATE NO ACTION
;

ALTER TABLE "referrers"
  ADD CONSTRAINT "fk_branch_referrers"
    FOREIGN KEY ("branch_id")
    REFERENCES "branches" ("id")
      ON DELETE SET NULL
      ON UPDATE NO ACTION
;

ALTER TABLE "health_packages"
  ADD CONSTRAINT "fk_branch_packages"
    FOREIGN KEY ("branch_id")
    REFERENCES "branches" ("id")
      ON DELETE SET NULL
      ON UPDATE NO ACTION
;

ALTER TABLE "fulfillment_centers"
  ADD CONSTRAINT "fk_branch_collection_centers"
    FOREIGN KEY ("branch_id")
    REFERENCES "branches" ("id")
      ON DELETE CASCADE
      ON UPDATE CASCADE
;

ALTER TABLE "procedures"
  ADD CONSTRAINT "fk_center_procedures"
    FOREIGN KEY ("center_id")
    REFERENCES "fulfillment_centers" ("id")
      ON DELETE CASCADE
      ON UPDATE NO ACTION
;

ALTER TABLE "procedure_categories"
  ADD CONSTRAINT "fk_center_procedure_categories"
    FOREIGN KEY ("center_id")
    REFERENCES "fulfillment_centers" ("id")
      ON DELETE SET NULL
      ON UPDATE NO ACTION
;

ALTER TABLE "procedure_categories"
  ADD CONSTRAINT "fk_branch_procedure_categories"
    FOREIGN KEY ("branch_id")
    REFERENCES "branches" ("id")
      ON DELETE SET NULL
      ON UPDATE NO ACTION
;

ALTER TABLE "orders"
  ADD CONSTRAINT "fk_branch_orders"
    FOREIGN KEY ("branch_id")
    REFERENCES "branches" ("id")
      ON DELETE CASCADE
      ON UPDATE NO ACTION
;

ALTER TABLE "orders"
  ADD CONSTRAINT "fk_center_orders"
    FOREIGN KEY ("center_id")
    REFERENCES "fulfillment_centers" ("id")
      ON DELETE CASCADE
      ON UPDATE NO ACTION
;

ALTER TABLE "orders"
  ADD CONSTRAINT "fk_patient_orders"
    FOREIGN KEY ("patient_id")
    REFERENCES "patients" ("id")
      ON DELETE CASCADE
      ON UPDATE NO ACTION
;

ALTER TABLE "affiliates"
  ADD CONSTRAINT "fk_branch_affiliates"
    FOREIGN KEY ("branch_id")
    REFERENCES "branches" ("id")
      ON DELETE SET NULL
      ON UPDATE NO ACTION
;

ALTER TABLE "order_procedures"
  ADD CONSTRAINT "fk_order_services"
    FOREIGN KEY ("order_id")
    REFERENCES "orders" ("id")
      ON DELETE CASCADE
      ON UPDATE NO ACTION
;

ALTER TABLE "order_procedures"
  ADD CONSTRAINT "fk_service_orders"
    FOREIGN KEY ("procedure_id")
    REFERENCES "procedures" ("id")
      ON DELETE CASCADE
      ON UPDATE NO ACTION
;

ALTER TABLE "procedures"
  ADD CONSTRAINT "fk_procedure_groups"
    FOREIGN KEY ("category_id")
    REFERENCES "procedure_categories" ("id")
      ON DELETE SET NULL
      ON UPDATE NO ACTION
;

ALTER TABLE "orders"
  ADD CONSTRAINT "fk_coupon_orders"
    FOREIGN KEY ("coupon_id")
    REFERENCES "coupons" ("id")
      ON DELETE SET NULL
      ON UPDATE NO ACTION
;

ALTER TABLE "order_transactions"
  ADD CONSTRAINT "fk_order_payments"
    FOREIGN KEY ("order_id")
    REFERENCES "orders" ("id")
      ON DELETE CASCADE
      ON UPDATE NO ACTION
;

ALTER TABLE "orders"
  ADD CONSTRAINT "fk_referrer_orders"
    FOREIGN KEY ("referrer_id")
    REFERENCES "referrers" ("id")
      ON DELETE SET NULL
      ON UPDATE NO ACTION
;

ALTER TABLE "discount_levels"
  ADD CONSTRAINT "fk_branch_discounts"
    FOREIGN KEY ("branch_id")
    REFERENCES "branches" ("id")
      ON DELETE SET NULL
      ON UPDATE NO ACTION
;

ALTER TABLE "procedures"
  ADD CONSTRAINT "fk_discount_services"
    FOREIGN KEY ("discount_level_id")
    REFERENCES "discount_levels" ("id")
      ON DELETE SET NULL
      ON UPDATE NO ACTION
;

ALTER TABLE "patient_addresses"
  ADD CONSTRAINT "fk_patient_addresses"
    FOREIGN KEY ("patient_id")
    REFERENCES "patients" ("id")
      ON DELETE CASCADE
      ON UPDATE NO ACTION
;

ALTER TABLE "messages"
  ADD CONSTRAINT "fk_patient_messages"
    FOREIGN KEY ("patient_id")
    REFERENCES "patients" ("id")
      ON DELETE SET NULL
      ON UPDATE NO ACTION
;

ALTER TABLE "messages"
  ADD CONSTRAINT "fk_branch_patient_messages"
    FOREIGN KEY ("branch_id")
    REFERENCES "branches" ("id")
      ON DELETE SET NULL
      ON UPDATE NO ACTION
;

ALTER TABLE "messages"
  ADD CONSTRAINT "fk_order_patient_messages"
    FOREIGN KEY ("order_id")
    REFERENCES "orders" ("id")
      ON DELETE SET NULL
      ON UPDATE NO ACTION
;

ALTER TABLE "orders"
  ADD CONSTRAINT "fk_tax_orders"
    FOREIGN KEY ("tax_id")
    REFERENCES "taxes" ("id")
      ON DELETE SET NULL
      ON UPDATE NO ACTION
;

ALTER TABLE "activity_logs"
  ADD CONSTRAINT "fk_patient_activities"
    FOREIGN KEY ("patient_id")
    REFERENCES "patients" ("id")
      ON DELETE SET NULL
      ON UPDATE NO ACTION
;

ALTER TABLE "activity_logs"
  ADD CONSTRAINT "fk_referrer_activities"
    FOREIGN KEY ("referrer_id")
    REFERENCES "referrers" ("id")
      ON DELETE SET NULL
      ON UPDATE NO ACTION
;

ALTER TABLE "package_components"
  ADD CONSTRAINT "fk_package_components"
    FOREIGN KEY ("package_id")
    REFERENCES "health_packages" ("id")
      ON DELETE CASCADE
      ON UPDATE NO ACTION
;

ALTER TABLE "package_components"
  ADD CONSTRAINT "fk_procedure_components"
    FOREIGN KEY ("procedure_id")
    REFERENCES "procedures" ("id")
      ON DELETE CASCADE
      ON UPDATE NO ACTION
;

ALTER TABLE "coupons"
  ADD CONSTRAINT "fk_branch_coupons"
    FOREIGN KEY ("branch_id")
    REFERENCES "branches" ("id")
      ON DELETE SET NULL
      ON UPDATE NO ACTION
;

ALTER TABLE "messages"
  ADD CONSTRAINT "fk_referrer_messages"
    FOREIGN KEY ("referrer_id")
    REFERENCES "referrers" ("id")
      ON DELETE SET NULL
      ON UPDATE NO ACTION
;

ALTER TABLE "branch_sales"
  ADD CONSTRAINT "fk_branch_sales_summaries"
    FOREIGN KEY ("branch_id")
    REFERENCES "branches" ("id")
      ON DELETE CASCADE
      ON UPDATE NO ACTION
;

ALTER TABLE "orders"
  ADD CONSTRAINT "fk_affiliate_orders"
    FOREIGN KEY ("affiliate_id")
    REFERENCES "affiliates" ("id")
      ON DELETE SET NULL
      ON UPDATE NO ACTION
;

ALTER TABLE "users"
  ADD CONSTRAINT "fk_center_staff"
    FOREIGN KEY ("branch_id")
    REFERENCES "branches" ("id")
      ON DELETE NO ACTION
      ON UPDATE NO ACTION
;

ALTER TABLE "users"
  ADD CONSTRAINT "fk_branch_staff"
    FOREIGN KEY ("center_id")
    REFERENCES "fulfillment_centers" ("id")
      ON DELETE SET NULL
      ON UPDATE NO ACTION
;

ALTER TABLE "activity_logs"
  ADD CONSTRAINT "fk_staff_activities"
    FOREIGN KEY ("staff_id")
    REFERENCES "users" ("id")
      ON DELETE SET NULL
      ON UPDATE NO ACTION
;

ALTER TABLE "corporate_clients"
  ADD CONSTRAINT "fk_branch_corporate_clients"
    FOREIGN KEY ("branch_id")
    REFERENCES "branches" ("id")
      ON DELETE SET NULL
      ON UPDATE NO ACTION
;

ALTER TABLE "orders"
  ADD CONSTRAINT "fk_corporte_orders"
    FOREIGN KEY ("corporate_id")
    REFERENCES "corporate_clients" ("id")
      ON DELETE SET NULL
      ON UPDATE NO ACTION
;

ALTER TABLE "patients"
  ADD CONSTRAINT "fk_corporate_patients"
    FOREIGN KEY ("corporate_id")
    REFERENCES "corporate_clients" ("id")
      ON DELETE NO ACTION
      ON UPDATE NO ACTION
;

ALTER TABLE "associate_labs"
  ADD CONSTRAINT "fk_branch_referring_labs"
    FOREIGN KEY ("branch_id")
    REFERENCES "branches" ("id")
      ON DELETE CASCADE
      ON UPDATE NO ACTION
;

ALTER TABLE "orders"
  ADD CONSTRAINT "fk_associate_lab_orders"
    FOREIGN KEY ("associate_lab_id")
    REFERENCES "associate_labs" ("id")
      ON DELETE SET NULL
      ON UPDATE NO ACTION
;

ALTER TABLE "orders"
  ADD CONSTRAINT "fk_package_orders"
    FOREIGN KEY ("package_id")
    REFERENCES "health_packages" ("id")
      ON DELETE SET NULL
      ON UPDATE NO ACTION
;

ALTER TABLE "bulk_payments"
  ADD CONSTRAINT "fk_corporate_bulk_payments"
    FOREIGN KEY ("corporate_id")
    REFERENCES "corporate_clients" ("id")
      ON DELETE CASCADE
      ON UPDATE NO ACTION
;

ALTER TABLE "bulk_payments"
  ADD CONSTRAINT "fk_branch_settlements"
    FOREIGN KEY ("branch_id")
    REFERENCES "branches" ("id")
      ON DELETE CASCADE
      ON UPDATE NO ACTION
;

ALTER TABLE "bulk_payments"
  ADD CONSTRAINT "fk_user_bulk_payments"
    FOREIGN KEY ("user_id")
    REFERENCES "users" ("id")
      ON DELETE SET NULL
      ON UPDATE NO ACTION
;

ALTER TABLE "patients"
  ADD CONSTRAINT "fk_geo_region_patients"
    FOREIGN KEY ("region_id")
    REFERENCES "geo_regions" ("id")
      ON DELETE SET NULL
      ON UPDATE NO ACTION
;

ALTER TABLE "associate_lab_users"
  ADD CONSTRAINT "fk_associate_lab_users"
    FOREIGN KEY ("associate_lab_id")
    REFERENCES "associate_labs" ("id")
      ON DELETE CASCADE
      ON UPDATE NO ACTION
;

ALTER TABLE "users"
  ADD CONSTRAINT "fk_role_users"
    FOREIGN KEY ("role_id")
    REFERENCES "roles" ("id")
      ON DELETE CASCADE
      ON UPDATE NO ACTION
;

ALTER TABLE "role_permission_links"
  ADD CONSTRAINT "fk_role_permissions"
    FOREIGN KEY ("role_id")
    REFERENCES "roles" ("id")
      ON DELETE CASCADE
      ON UPDATE NO ACTION
;

ALTER TABLE "role_permission_links"
  ADD CONSTRAINT "fk_permission_roles"
    FOREIGN KEY ("permission_id")
    REFERENCES "permissions" ("id")
      ON DELETE CASCADE
      ON UPDATE NO ACTION
;

ALTER TABLE "referrer_sales"
  ADD CONSTRAINT "Relationship41"
    FOREIGN KEY ("referrer_id")
    REFERENCES "referrers" ("id")
      ON DELETE NO ACTION
      ON UPDATE NO ACTION
;

ALTER TABLE "referrer_procedure_sales"
  ADD CONSTRAINT "fk_referrer_procedure_sales"
    FOREIGN KEY ("referrer_id")
    REFERENCES "referrers" ("id")
      ON DELETE CASCADE
      ON UPDATE NO ACTION
;

ALTER TABLE "referrer_procedure_sales"
  ADD CONSTRAINT "fk_procedure_referrer_sales"
    FOREIGN KEY ("procedure_id")
    REFERENCES "procedures" ("id")
      ON DELETE CASCADE
      ON UPDATE NO ACTION
;

ALTER TABLE "branch_procedure_sales"
  ADD CONSTRAINT "fkprocedure_branch__sales"
    FOREIGN KEY ("procedure_id")
    REFERENCES "procedures" ("id")
      ON DELETE CASCADE
      ON UPDATE NO ACTION
;

ALTER TABLE "branch_procedure_sales"
  ADD CONSTRAINT "fk_branch_procedure_sales"
    FOREIGN KEY ("branch_id")
    REFERENCES "branches" ("id")
      ON DELETE CASCADE
      ON UPDATE NO ACTION
;

ALTER TABLE "branch_procedure_sales"
  ADD CONSTRAINT "Relationship46"
    FOREIGN KEY ("category_id")
    REFERENCES "procedure_categories" ("id")
      ON DELETE NO ACTION
      ON UPDATE NO ACTION
;

ALTER TABLE "settings"
  ADD CONSTRAINT "fk_branch_settings"
    FOREIGN KEY ("branch_id")
    REFERENCES "branches" ("id")
      ON DELETE SET NULL
      ON UPDATE NO ACTION
;

ALTER TABLE "settings"
  ADD CONSTRAINT "fk_user_settings"
    FOREIGN KEY ("user_id")
    REFERENCES "users" ("id")
      ON DELETE SET NULL
      ON UPDATE NO ACTION
;

ALTER TABLE "patients"
  ADD CONSTRAINT "fk_nationality_patients"
    FOREIGN KEY ("nationality_id")
    REFERENCES "nationalities" ("id")
      ON DELETE SET NULL
      ON UPDATE NO ACTION
;

