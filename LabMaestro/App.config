﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>

  <configSections>
    <section name="nlog" type="NLog.Config.ConfigSectionHandler, NLog" />
    <section name="ideablade.configuration" type="IdeaBlade.Core.Configuration.IdeaBladeSection, IdeaBlade.Core" />
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=5.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
  </configSections>

  <nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <targets>
      <target name="file" xsi:type="File" layout="${date:format=HH\:mm\:ss} ${machinename} ${threadid} ${level:uppercase=true} ${logger} ${message} ${exception:format=message,type,method,stacktrace}" fileName="${basedir}/logs/labmaestro.log" archiveFileName="${basedir}/logs/archive_${shortdate}.log" archiveNumbering="Rolling" archiveEvery="Day" maxArchiveFiles="30" concurrentWrites="false" keepFileOpen="false" encoding="iso-8859-2" />
    </targets>

    <rules>
      <logger name="*" minlevel="Debug" writeTo="file" />
    </rules>
  </nlog>

  <ideablade.configuration version="6.00" xmlns="http://schemas.ideablade.com/2010/IdeaBladeConfig">
    <!-- <objectServer remoteBaseURL="http://localhost." serverPort="9009" serviceName="EntityService.svc"> <clientSettings isDistributed="true" /> </objectServer> -->
    <!-- Additional configuration can be added to override defaults. 
         See the sample config files in the Learning Resources for more information. -->
    <!-- objectServer remoteBaseURL="http://localhost" serverPort="80" serviceName="MBoss/EntityService.svc" -->
    <objectServer serviceName="EntityService">
      <clientSettings isDistributed="true" />
    </objectServer>

    <logging logFile="logs\devforce.xml" archiveLogs="true" />
  </ideablade.configuration>

  <entityFramework>
    <defaultConnectionFactory type="System.Data.Entity.Infrastructure.SqlConnectionFactory, EntityFramework" />
  </entityFramework>

  <system.serviceModel>

    <client>
      <endpoint name="EntityService" address="http://localhost:9009/EntityService.svc" binding="customBinding" bindingConfiguration="compressedBinaryBinding" contract="IdeaBlade.EntityModel.IEntityServiceContract" />

      <endpoint name="EntityServer" address="http://localhost:9009/EntityServer.svc" binding="customBinding" bindingConfiguration="compressedBinaryBinding" contract="IdeaBlade.EntityModel.IEntityServerContract" />
    </client>

    <extensions>
      <bindingElementExtensions>
        <add name="gzipMessageEncoding" type="IdeaBlade.Core.Wcf.Extensions.GZipMessageEncodingElement, IdeaBlade.Core" />
      </bindingElementExtensions>
    </extensions>

    <bindings>
      <customBinding>
        <binding name="compressedBinaryBinding">
          <gzipMessageEncoding>
            <readerQuotas maxDepth="2147483647" maxStringContentLength="2147483647" maxArrayLength="2147483647" />
          </gzipMessageEncoding>
          <httpTransport maxReceivedMessageSize="2147483647" />
        </binding>
      </customBinding>
    </bindings>

  </system.serviceModel>

  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.7.2" />
  </startup>

  <appSettings>
    <add key="entityServerAddress" value="localhost" />    
    <add key="bgJobsEndpoint" value="http://localhost:13455/" />
  </appSettings>

  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="MassTransit" publicKeyToken="b8e0e9f2f1e657fa" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Magnum" publicKeyToken="b800c4cfcdeea87b" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Common.Logging" publicKeyToken="af08829b84f0328e" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="AutoMapper" publicKeyToken="be96cd2c38ef1005" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.2.1.0" newVersion="2.2.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Common.Logging.Core" publicKeyToken="af08829b84f0328e" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.2.0.0" newVersion="3.2.0.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>

</configuration>
