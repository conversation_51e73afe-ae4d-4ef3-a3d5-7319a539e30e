<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{75FA7804-4C02-4E9A-AD87-324A90993DC5}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>LabMaestro.Client</RootNamespace>
    <AssemblyName>LabMaestro</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SolutionDir Condition="$(SolutionDir) == '' Or $(SolutionDir) == '*Undefined*'">..\LabMaestro\</SolutionDir>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <TargetFrameworkProfile />
    <LangVersion>latestmajor</LangVersion>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>false</MapFileExtensions>
    <OpenBrowserOnPublish>false</OpenBrowserOnPublish>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.0</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <PublishWizardCompleted>true</PublishWizardCompleted>
    <BootstrapperEnabled>false</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>true</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>
    </DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <SignAssembly>false</SignAssembly>
  </PropertyGroup>
  <PropertyGroup>
    <AssemblyOriginatorKeyFile>labmaestro.snk</AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>LabMaestro.ico</ApplicationIcon>
  </PropertyGroup>
  <PropertyGroup>
    <GenerateManifests>true</GenerateManifests>
  </PropertyGroup>
  <PropertyGroup>
    <TargetZone>LocalIntranet</TargetZone>
  </PropertyGroup>
  <PropertyGroup />
  <PropertyGroup>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
  </PropertyGroup>
  <PropertyGroup>
    <NoWin32Manifest>true</NoWin32Manifest>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AutomaticUpdater">
      <HintPath>..\libs\AutomaticUpdater.dll</HintPath>
    </Reference>
    <Reference Include="C1.LiveLinq.4">
      <HintPath>..\libs\C1.LiveLinq.4.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.BonusSkins.v24.2, Version=24.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Drawing.v24.2, Version=24.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="DevExpress.Data.v24.2, Version=24.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="DevExpress.Office.v24.2.Core, Version=24.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="DevExpress.Pdf.v24.2.Core, Version=24.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="DevExpress.RichEdit.v24.2.Export, Version=24.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="DevExpress.Printing.v24.2.Core, Version=24.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="DevExpress.Docs.v24.2, Version=24.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="DevExpress.RichEdit.v24.2.Core, Version=24.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="DevExpress.Sparkline.v24.2.Core, Version=24.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="DevExpress.Data.Desktop.v24.2, Version=24.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Utils.v24.2, Version=24.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraEditors.v24.2, Version=24.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="Stimulsoft.Base">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\libs\Stimulsoft.Base.dll</HintPath>
    </Reference>
    <Reference Include="Stimulsoft.Controls">
      <HintPath>..\libs\Stimulsoft.Controls.dll</HintPath>
    </Reference>
    <Reference Include="Stimulsoft.Controls.Win">
      <HintPath>..\libs\Stimulsoft.Controls.Win.dll</HintPath>
    </Reference>
    <Reference Include="Stimulsoft.Report">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\libs\Stimulsoft.Report.dll</HintPath>
    </Reference>
    <Reference Include="Stimulsoft.Report.Win">
      <HintPath>..\libs\Stimulsoft.Report.Win.dll</HintPath>
    </Reference>
    <Reference Include="Syncfusion.Grid.Base, Version=26.2462.4, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89, processorArchitecture=MSIL">
      <Private>True</Private>
    </Reference>
    <Reference Include="Syncfusion.Grid.Windows, Version=26.2462.4, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89, processorArchitecture=MSIL">
      <Private>True</Private>
    </Reference>
    <Reference Include="Syncfusion.GridConverter.Windows, Version=26.2462.4, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89, processorArchitecture=MSIL">
      <Private>True</Private>
    </Reference>
    <Reference Include="Syncfusion.GridHelperClasses.Windows, Version=26.2462.4, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89, processorArchitecture=MSIL">
      <Private>True</Private>
    </Reference>
    <Reference Include="Syncfusion.Shared.Base, Version=26.2462.4, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89, processorArchitecture=MSIL">
      <Private>True</Private>
    </Reference>
    <Reference Include="Syncfusion.Shared.Windows, Version=26.2462.4, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89, processorArchitecture=MSIL">
      <Private>True</Private>
    </Reference>
    <Reference Include="Syncfusion.Tools.Base, Version=26.2462.4, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89, processorArchitecture=MSIL">
      <Private>True</Private>
    </Reference>
    <Reference Include="Syncfusion.Tools.Windows, Version=26.2462.4, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89, processorArchitecture=MSIL">
      <Private>True</Private>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="Xceed.Editors.v2.6, Version=2.6.20076.12370, Culture=neutral, PublicKeyToken=ba83ff368b7563c6, processorArchitecture=MSIL" />
    <Reference Include="Xceed.Grid.v4.3, Version=4.3.20076.12370, Culture=neutral, PublicKeyToken=ba83ff368b7563c6, processorArchitecture=MSIL" />
    <Reference Include="Xceed.UI.v1.4, Version=1.4.20076.12370, Culture=neutral, PublicKeyToken=ba83ff368b7563c6, processorArchitecture=MSIL" />
    <Reference Include="Xceed.Validation.v1.3, Version=1.3.20076.12370, Culture=neutral, PublicKeyToken=ba83ff368b7563c6, processorArchitecture=MSIL" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <None Include="labmaestro.snk" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config">
      <SubType>Designer</SubType>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\LabMaestro.BusinessLogic\LabMaestro.BusinessLogic.csproj">
      <Project>{a13d1c5d-3bbf-4e7d-88fc-47ba5dcccd83}</Project>
      <Name>LabMaestro.BusinessLogic</Name>
    </ProjectReference>
    <ProjectReference Include="..\LabMaestro.Controls.Win\LabMaestro.Controls.Win.csproj">
      <Project>{ded48497-1198-41c9-9640-e7cc64a61a80}</Project>
      <Name>LabMaestro.Controls.Win</Name>
    </ProjectReference>
    <ProjectReference Include="..\LabMaestro.Domain\LabMaestro.Domain.csproj">
      <Project>{72642b75-e640-41a5-a394-fb8e95b17599}</Project>
      <Name>LabMaestro.Domain</Name>
    </ProjectReference>
    <ProjectReference Include="..\LabMaestro.Infrastructure.Client\LabMaestro.Infrastructure.Client.csproj">
      <Project>{0f4fd7b4-6342-44fa-b190-ff9f482e8798}</Project>
      <Name>LabMaestro.Infrastructure.Client</Name>
    </ProjectReference>
    <ProjectReference Include="..\LabMaestro.Infrastructure.Search\LabMaestro.Infrastructure.Search.csproj">
      <Project>{abb2c9e0-bf9c-4dcf-951f-d8ba032a2e5c}</Project>
      <Name>LabMaestro.Infrastructure.Search</Name>
    </ProjectReference>
    <ProjectReference Include="..\LabMaestro.Printing\LabMaestro.Printing.csproj">
      <Project>{32d5bffa-5141-461e-985f-58a219660599}</Project>
      <Name>LabMaestro.Printing</Name>
    </ProjectReference>
    <ProjectReference Include="..\LabMaestro.Shared\LabMaestro.Shared.csproj">
      <Project>{b15584a4-1ea1-48aa-a823-29307bb8d16a}</Project>
      <Name>LabMaestro.Shared</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="LabMaestro.ico" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.5">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4.5 %28x86 and x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1 Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <PublishFile Include="DevExpress.BonusSkins.v24.2">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.Charts.v24.2.Core">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.Data.v24.2">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.DataAccess.v24.2">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.Docs.v24.2">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.Office.v24.2.Core">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.Pdf.v24.2.Core">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.Printing.v24.2.Core">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.RichEdit.v24.2.Core">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.Snap.v24.2.Core">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.Sparkline.v24.2.Core">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.Spreadsheet.v24.2.Core">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.Utils.v24.2">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.Xpo.v24.2">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.XtraBars.v24.2">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.XtraCharts.v24.2">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.XtraEditors.v24.2">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.XtraGrid.v24.2">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.XtraLayout.v24.2">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.XtraPrinting.v24.2">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.XtraRichEdit.v24.2">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.XtraRichEdit.v24.2.Extensions">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.XtraTreeList.v24.2">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Syncfusion.Core">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Syncfusion.Grid.Base">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Syncfusion.Grid.Windows">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Syncfusion.Shared.Base">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Syncfusion.Shared.Windows">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Syncfusion.Tools.Base">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Syncfusion.Tools.Windows">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="IdeaBlade.DevForce.Core">
      <Version>7.5.5</Version>
    </PackageReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>