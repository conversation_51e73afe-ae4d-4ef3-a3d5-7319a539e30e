﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: Program.cs 1288 2014-05-22 10:34:41Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Configuration;
using System.Diagnostics;
using System.Linq;
using System.Runtime.InteropServices;
using System.Threading;
using System.Windows.Forms;
using DevExpress.LookAndFeel;
using DevExpress.Skins;
using DevExpress.UserSkins;
using DevExpress.XtraSplashScreen;
using LabMaestro.Controls.Win;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Printing;
using LabMaestro.Shared;
using LabMaestro.Shared.AppFaults;
using GlobalSettingsHelper = LabMaestro.BusinessLogic.GlobalSettingsHelper;

namespace LabMaestro
{
    internal static class Program
    {
        private const int SPLASH_TIMEOUT = 5 * 1000;
        private const string MUTEX_ID = @"79F48456-E40F-4D4B-8241-F54B7C82F653";

        private static void sleepTillTimeout(DateTime startedOn)
        {
            var span = DateTime.Now - startedOn;
            var remaining = (int)(SPLASH_TIMEOUT - span.TotalMilliseconds);
            if (remaining > 100) {
                Thread.Sleep(remaining);
            }
        }

        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool SetForegroundWindow(IntPtr hWnd);

        private static bool allowMultipleInstances(string[] args)
        {
            return args != null && args.Any(arg => String.Compare("/m", arg, StringComparison.OrdinalIgnoreCase) == 0);
        }

        /// <summary>
        ///     The main entry point for the application.
        /// </summary>
        [STAThread]
        private static void Main(string[] args)
        {
            //JobClient.Initialize(ConfigurationManager.AppSettings[@"jobDb"]);
            AppFaultLogger.ServiceEndpoint = ConfigurationManager.AppSettings[@"faultLoggerWebEndpoint"];

            FaultHandler.Activate();
            AppLogger.Initialize();
#if !DEBUG
            Licensing.InitializeLicenseFiles(false);
#endif
            Stimulsoft.Base.StiLicense.Key = Licensing.GetLicenseKey();

            // prevent duplicate instances
            using (new Mutex(true, MUTEX_ID, out var createdNew)) {
                if (!createdNew && !allowMultipleInstances(args)) {
                    var currProc = Process.GetCurrentProcess();
                    foreach (var proc in Process.GetProcessesByName(currProc.ProcessName).Where(p => p.Id != currProc.Id)) {
                        SetForegroundWindow(proc.MainWindowHandle);
                        return;
                    }
                }

                Application.SetCompatibleTextRenderingDefault(false);
#if DEBUG
                // skip the splash screen
#else
                SplashScreenManager.ShowForm(typeof(StartupSplashScreen));
#endif
                var startedOn = DateTime.Now;
                var pingSuccess = true;
                try {
                    BonusSkins.Register();
                    SkinManager.EnableFormSkins();
                    LookAndFeelHelper.ForceDefaultLookAndFeelChanged();
                    WinUtils.UnlockControls();

                    /*
                    try
                    {
                        var entityServer = ConfigurationManager.AppSettings[@"entityServerAddress"];
                        AppLogger.Info("Pinging entity server " + entityServer + "...");
                        pingSuccess = ICMPHelper.PingHost(entityServer);
                        AppLogger.Info("Ping successful");
                    }
                    catch (Exception e)
                    {
                        AppLogger.Exception("Error pinging entity server", e);
                        pingSuccess = false;
                    }
                    */

                    if (pingSuccess) {
                        CurrentUserContext.Kickstart();
                        try {
                            AppLogger.Info("Connecting to BOSS server...");
                            DomainManager.ConnectServer();
                        }
                        catch (Exception e) {
                            AppLogger.Exception("Error connecting to BOSS server", e);
                        }
                    }
                    else {
                        AppLogger.Error("Ping failed!");
                    }

                    Application.EnableVisualStyles();

#if DEBUG
                    // skip the splash screen
#else
                    sleepTillTimeout(startedOn);
#endif
                }
                finally {
#if DEBUG
                    // skip the splash screen
#else
                    SplashScreenManager.CloseForm();
                    // Allow breathing space for the splash form to wind down
                    Thread.Sleep(750);
#endif
                }

                if (!pingSuccess) {
                    var msg = "Ping failure!";
                    msg += "\n\nThe LabMaestro BOSS server could not be reached.";
                    msg += "\nPlease ensure your workstation is connected to the network.";
                    msg += "\nContact your administrator if this problem persists.";
                    MessageBox.Show(msg);
                }

                if (!DomainManager.IsConnected) {
                    var msg = "Failed to connect to the LabMaestro BOSS server!";
                    msg += "\n\nPlease ensure your workstation is connected to the network.";
                    msg += "\nContact your administrator if this problem persists.";
                    msg += "\nThis application cannot function without server connectivity.";
                    MessageBox.Show(msg);
                    AppLogger.Error("Could not connect to the entity server. Shutting down...");
                    Application.Exit();
                    return;
                }

                if (LoginDialog.ExecuteUserLogin()) {
                    if (string.IsNullOrEmpty(GlobalSettingsHelper.System.IPSubnetPrefix)) {
                        GlobalSettingsHelper.System.IPSubnetPrefix = "10.";
                    }

                    var ipPrefix = GlobalSettingsRepository.GetStringValue(GlobalSettingKeys.System.IPSubnetPrefix);
                    var ipAddr = SharedUtilities.GetLocalIpAddress(ipPrefix);
                    AuditTrailRepository.InitializeCurrentContext(
                        CurrentUserContext.UserId,
                        CurrentUserContext.WorkShiftId,
                        ipAddr);

                    var macAddr = SharedUtilities.FetchMACAddress();
                    AppSysRepository.UpsertWorkstationRegistry(
                        ipAddr,
                        macAddr,
                        CurrentUserContext.UserDisplayName,
                        SysInfo.GetOSVersion(),
                        string.Empty);

                    Application.Run(new LaunchPadForm());
                }
                else {
                    Application.Exit();
                }
            }
        }
    }
}