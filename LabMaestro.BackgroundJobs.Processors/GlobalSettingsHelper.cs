﻿using System.IO;
using LabMaestro.ServiceDAL;
using LabMaestro.Services.ReportExport;
using LabMaestro.Shared;

namespace LabMaestro.BackgroundJobs;

public static class GlobalSettingsHelper
{
    private const string CACHE_KEY = @"$GLOBAL#RecentlyUpdatedBundlesMinimumThreshold$";
    public static string BulkEmailPickupFolder { get; set; }
    public static string BulkSmsPickupFolder { get; set; }
    public static int BulkSmsDelayMinutes { get; set; }
    public static int BulkSmsExpiryHours { get; set; }

    public static WorkflowStageType RecentlyUpdatedBundlesMinimumThreshold(DataAccessHelper db)
    {
        int wfStage;
        if (AppCache.Contains(CACHE_KEY))
            wfStage = AppCache.GetInt(CACHE_KEY);
        else
        {
            wfStage = db.GlobalSettingsGetInt("results.recently_updated_result_bundles.min_threshold");
            AppCache.Store(CACHE_KEY, wfStage, 5);
        }

        return (WorkflowStageType)wfStage;
    }

    public static string? GetConfigContent(string filename)
    {
        if (string.IsNullOrEmpty(filename))
            return null;
        filename = Path.ChangeExtension(filename.ToLowerInvariant(), ".yaml");
        var path = SharedUtilities.GetLocalPath(["config", filename]);
        return File.Exists(path) ? File.ReadAllText(path) : null;
    }
}