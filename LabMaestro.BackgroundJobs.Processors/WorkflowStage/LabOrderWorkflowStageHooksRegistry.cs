﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: LabOrderWorkflowStageHooksRegistry.cs 1380 2014-06-20 16:27:28Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using NLog;

namespace LabMaestro.BackgroundJobs;

/// <summary>
///     Singleton collection of Workflow hooks that operate on the entire lab order
/// </summary>
public sealed class LabOrderWorkflowStageHooksRegistry : WorkflowStageHooksRegistryBase
{
    private static readonly Lazy<LabOrderWorkflowStageHooksRegistry> _instance = new(() => new());

    private static readonly Logger _logger = LogManager.GetCurrentClassLogger();

    public static LabOrderWorkflowStageHooksRegistry Instance => _instance.Value;

    protected override void InstallDefaultHooks()
    {
        var hook = new LabOrderSmsSenderHook();
        RegisterHook(hook);
        _logger.Info("Installed " + hook.HookId);
    }
}