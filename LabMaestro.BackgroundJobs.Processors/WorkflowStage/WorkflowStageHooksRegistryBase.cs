// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: WorkflowStageHooksRegistryBase.cs 1380 2014-06-20 16:27:28Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System.Collections.Generic;
using System.Linq;
using LabMaestro.ServiceDAL;
using LabMaestro.ServiceDAL.Model;
using LabMaestro.Shared;
using NLog;

namespace LabMaestro.BackgroundJobs;

public abstract class WorkflowStageHooksRegistryBase
{
    private readonly Logger _logger = LogManager.GetCurrentClassLogger();
    protected Dictionary<string, IWorkflowStageHook> _registry = new();

    protected WorkflowStageHooksRegistryBase() => InstallDefaultHooks();

    public List<IWorkflowStageHook> Hooks => _registry.Values.ToList();

    public void RegisterHook(IWorkflowStageHook hook) => _registry[hook.HookId] = hook;

    public void RemoveHook(IWorkflowStageHook hook)
    {
        if (_registry.ContainsKey(hook.HookId)) _registry.Remove(hook.HookId);
    }

    public void Execute(long invoiceId, long bundleId)
    {
        InvoiceContactDetailsFullDto? invoice = null;
        ResultBundleDetailsDto? bundle = null;
        var wfStage = WorkflowStageType.Unknown;

        using var db = DataAccessFactory.GetClient();
        if (invoiceId > 0)
        {
            invoice = db.GetInvoiceContactDetailsFull(invoiceId);
            if (invoice != null)
                wfStage = (WorkflowStageType)invoice.WorkflowStage;
        }

        if (bundleId > 0)
        {
            bundle = db.GetResultBundleDetails(bundleId);
            if (bundle != null)
                wfStage = (WorkflowStageType)bundle.WorkflowStage;
        }

        if (invoice != null)
            foreach (var hook in _registry.Values.Where(h => h.DesiredWorkflowStage == wfStage))
            {
                _logger.Info("Executing " + hook.HookId);
                hook.Execute(invoice, bundle);
            }
    }

    protected abstract void InstallDefaultHooks();
}