﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: LabOrderWorkflowStageEstimator.cs 1476 2014-10-16 07:15:14Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using LabMaestro.ServiceDAL;
using LabMaestro.Shared;

namespace LabMaestro.BackgroundJobs;

public sealed class LabOrderWorkflowStageEstimator
{
    private readonly DataAccessHelper _db;
    private readonly long _invoiceId;
    private readonly short _userId;

    public static void Execute(long invoiceId, DataAccessHelper? db = null)
    {
        var wfEstimator = new LabOrderWorkflowStageEstimator(invoiceId, db);
        wfEstimator.EstimateWorkflowStage();
    }

    public LabOrderWorkflowStageEstimator(long invoiceId, DataAccessHelper? db)
    {
        _invoiceId = invoiceId;
        _db = db ?? DataAccessFactory.GetClient();
        _userId = _db.GetUserIdForServiceBusAgent();
    }

    public WorkflowStageType EstimateWorkflowStage()
    {
        var invoice = _db.GetInvoiceSummary(_invoiceId);

        if (invoice == null) return WorkflowStageType.Unknown;

        if (invoice.IsCancelled)
        {
            updateAndLogOrderWorkflow(WorkflowStageType.Canceled);
            return WorkflowStageType.Canceled;
        }

        var bundles = _db.GetActiveResultBundleStatusForInvoice(_invoiceId);
        var wfOrder = WorkflowStageType.OrderFulfillment;

        //PatientLabOrdersRepository.AutoUpdateLabOrderWorkflowStageFromOrderedTests(_invoiceId, WorkflowStageType.OrderFulfillment);

        foreach (var bundle in bundles)
        {
            /*
            switch ((TestResultType) bundle.TestResultType)
            {
                case TestResultType.Unarchived:
                    // TODO: explore this special case.. do we even need to
                    // filter out this type of bundle? what if the order
                    // consists only of unarchived result bundles?
                    break;
                default:
                    if (wfStageBundles > bundle.WorkflowStage)
                        wfStageBundles = bundle.WorkflowStage;
                    break;
            }
            */
            if (wfOrder > bundle.BundleWorkflowStage)
                wfOrder = bundle.BundleWorkflowStage;
        }

        /* mark the order as "fulfilled" if all the following criterias have been met:
         * 1) all reports have been dispatched / remotely dispatched
         * 2) invoice is paid in full
         */
        if (wfOrder >= WorkflowStageType.ReportDispatch &&
            wfOrder != WorkflowStageType.Canceled &&
            invoice.DueAmount == 0m)
            wfOrder = WorkflowStageType.OrderFulfillment;

        // TODO: what if the workflow is demoted?
        updateAndLogOrderWorkflow(wfOrder);

        // perform order workflow hook request
        handleLabOrderWorkflowHook();

        return wfOrder;
    }

    private void handleLabOrderWorkflowHook() => LabOrderWorkflowStageHooksRegistry.Instance.Execute(_invoiceId, -1);

    private void updateAndLogOrderWorkflow(WorkflowStageType wfOrder)
    {
        _db.UpdateLabOrderWorkflowStage(_invoiceId, wfOrder);
        _db.InsertAuditRecord(AuditEventCategory.System,
            AuditEventType.wfAutoEstimated,
            wfOrder,
            _userId,
            null,
            _invoiceId);
    }
}