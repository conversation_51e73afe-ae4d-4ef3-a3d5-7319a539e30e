<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{5B650925-F69E-4F59-B03A-BC2DBF680E01}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>LabMaestro.BackgroundJobs.Processors</RootNamespace>
    <AssemblyName>LabMaestro.BackgroundJobs.Processors</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <LangVersion>latestmajor</LangVersion>
    <Nullable>enable</Nullable>
    <SignAssembly>false</SignAssembly>
    <AssemblyOriginatorKeyFile>..\Keys\labmaestro.snk</AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data" />
    <Reference Include="System.IO.Compression" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="GlobalSettingsHelper.cs" />
    <Compile Include="Lifecycle\EventHandlerFactory.cs" />
    <Compile Include="Lifecycle\Handlers\FinancePaymentMade.cs" />
    <Compile Include="Lifecycle\Handlers\FinanceStatusUpdated.cs" />
    <Compile Include="Lifecycle\Handlers\OrderCreated.cs" />
    <Compile Include="Lifecycle\Handlers\OrderEventHandlerBase.cs" />
    <Compile Include="Lifecycle\Handlers\ReportCollated.cs" />
    <Compile Include="Lifecycle\Handlers\ResultEntered.cs" />
    <Compile Include="Lifecycle\Handlers\ResultFinalized.cs" />
    <Compile Include="Lifecycle\Handlers\ResultVerified.cs" />
    <Compile Include="Lifecycle\ILifecycleEventHandler.cs" />
    <Compile Include="Messaging\MailerBase.cs" />
    <Compile Include="Messaging\PdfInvoiceMailer.cs" />
    <Compile Include="Messaging\SmsSender.cs" />
    <Compile Include="Messaging\TemplateEngine.cs" />
    <Compile Include="Messaging\PdfReportMailer.cs" />
    <Compile Include="ProcessLogger.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="ResultBundleCompiler\IInvoiceOrderedTestsDataProvider.cs" />
    <Compile Include="ResultBundleCompiler\InvoiceOrderedTestsDataProvider.cs" />
    <Compile Include="ResultBundleCompiler\ResultBundleCompiler.cs" />
    <Compile Include="ResultBundleCompiler\ResultBundleDbPersister.cs" />
    <Compile Include="ResultBundleCompiler\ResultBundleSlice.cs" />
    <Compile Include="WorkflowHooks\LabOrderSmsSenderHook.cs" />
    <Compile Include="WorkflowHooks\ResultBundleEmailSenderHook.cs" />
    <Compile Include="WorkflowStage\IWorkflowStageHook.cs" />
    <Compile Include="WorkflowStage\LabOrderWorkflowStageEstimator.cs" />
    <Compile Include="WorkflowStage\LabOrderWorkflowStageHooksRegistry.cs" />
    <Compile Include="WorkflowStage\ResultBundleWorkflowStageHooksRegistry.cs" />
    <Compile Include="WorkflowStage\WorkflowStageHooksRegistryBase.cs" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="CuttingEdge.Conditions">
      <Version>1.2.0</Version>
    </PackageReference>
    <PackageReference Include="Dapper">
      <Version>2.1.66</Version>
    </PackageReference>
    <PackageReference Include="Handlebars.Net">
      <Version>2.1.6</Version>
    </PackageReference>
    <PackageReference Include="NRules">
      <Version>1.0.2</Version>
    </PackageReference>
    <PackageReference Include="Serilog">
      <Version>4.3.0</Version>
    </PackageReference>
    <PackageReference Include="YamlDotNet">
      <Version>16.3.0</Version>
    </PackageReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="..\Keys\labmaestro.snk">
      <Link>labmaestro.snk</Link>
    </None>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\LabMaestro.BulkMessageTransport.Core\LabMaestro.BulkMessageTransport.Core.csproj">
      <Project>{a0450d82-4dbe-4fcd-b87b-12dacd96c3ad}</Project>
      <Name>LabMaestro.BulkMessageTransport.Core</Name>
    </ProjectReference>
    <ProjectReference Include="..\LabMaestro.BulkMessageTransport.Email\LabMaestro.BulkMessageTransport.Email.csproj">
      <Project>{6a94f78f-8314-4e00-965f-d42a0ce02f65}</Project>
      <Name>LabMaestro.BulkMessageTransport.Email</Name>
    </ProjectReference>
    <ProjectReference Include="..\LabMaestro.BulkMessageTransport.Sms\LabMaestro.BulkMessageTransport.Sms.csproj">
      <Project>{21dbad7b-8e1b-46e1-bbca-a27d3e7cdbd7}</Project>
      <Name>LabMaestro.BulkMessageTransport.Sms</Name>
    </ProjectReference>
    <ProjectReference Include="..\LabMaestro.Messaging.Core\LabMaestro.Messaging.Core.csproj">
      <Project>{e0426849-eafc-4e1d-898e-564bdc18c6e6}</Project>
      <Name>LabMaestro.Messaging.Core</Name>
    </ProjectReference>
    <ProjectReference Include="..\LabMaestro.Messaging.Transport\LabMaestro.Messaging.Transport.csproj">
      <Project>{ea45fb03-a09f-489d-b646-935a8d3eea09}</Project>
      <Name>LabMaestro.Messaging.Transport</Name>
    </ProjectReference>
    <ProjectReference Include="..\LabMaestro.ServiceDAL.Model\LabMaestro.ServiceDAL.Model.csproj">
      <Project>{d6c2bf28-5823-4b3a-be17-84ecc60a7d3c}</Project>
      <Name>LabMaestro.ServiceDAL.Model</Name>
    </ProjectReference>
    <ProjectReference Include="..\LabMaestro.ServiceDAL\LabMaestro.ServiceDAL.csproj">
      <Project>{ad31717e-94c6-4963-ad95-398d8ab5308d}</Project>
      <Name>LabMaestro.ServiceDAL</Name>
    </ProjectReference>
    <ProjectReference Include="..\LabMaestro.Services.ReportExport\LabMaestro.Services.ReportExport.csproj">
      <Project>{b60b239d-ed1d-418e-9272-191e7a3f7a67}</Project>
      <Name>LabMaestro.Services.ReportExport</Name>
    </ProjectReference>
    <ProjectReference Include="..\LabMaestro.Shared\LabMaestro.Shared.csproj">
      <Project>{b15584a4-1ea1-48aa-a823-29307bb8d16a}</Project>
      <Name>LabMaestro.Shared</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="config\chevronlab.pfx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="config\mail.yaml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="config\organization.yaml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="config\sms.yaml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="templates\email_invoice_report.mustache">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="templates\email_results_report.mustache">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Rules\" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
         Other similar extension points exist, see Microsoft.Common.targets.
    <Target Name="BeforeBuild">
    </Target>
    <Target Name="AfterBuild">
    </Target>
    -->
</Project>