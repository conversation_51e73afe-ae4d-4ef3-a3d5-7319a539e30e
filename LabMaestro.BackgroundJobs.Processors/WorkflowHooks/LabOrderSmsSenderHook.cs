﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: LabOrderSmsSenderHook.cs 1478 2014-10-17 16:13:13Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using LabMaestro.BulkMessageTransport.Sms;
using LabMaestro.ServiceDAL;
using LabMaestro.ServiceDAL.Model;
using LabMaestro.Shared;

namespace LabMaestro.BackgroundJobs;

public sealed class LabOrderSmsSenderHook : IWorkflowStageHook
{
    public WorkflowStageType DesiredWorkflowStage => WorkflowStageType.ReportCollation;

    public string HookId => "order.collation.sms";

    public void Execute(InvoiceContactDetailsFullDto order, ResultBundleDetailsDto bundle)
    {
        ProcessLogger.Log.Information("Processing invoice {Order.InvoiceId}", order.InvoiceId);
        if (order.IsCancelled
            || string.IsNullOrEmpty(order.PhoneNumber)
            || order.WorkflowStage != (byte)DesiredWorkflowStage)
        {
            if (order.WorkflowStage != (byte)DesiredWorkflowStage)
                ProcessLogger.Log.Information("{Order.InvoiceId} Skipped. Order workflow stage={Order.WorkflowStage}",
                    order.InvoiceId, order.WorkflowStage);
            else if (string.IsNullOrEmpty(order.PhoneNumber))
                ProcessLogger.Log.Information("{Order.InvoiceId} Skipped. No phone number given", order.InvoiceId);
            else
                ProcessLogger.Log.Information("{Order.InvoiceId} Skipped. Order cancelled", order.InvoiceId);

            return;
        }

        //var task = Task.Factory.StartNew(() => sendEmail(order, bundle));
        //task.Wait();

        using var dal = DataAccessFactory.GetClient();
        if (dal.InvoiceHasNotification(order.InvoiceId,
                (byte)InvoiceNotificationCode.InvoiceCollatedMessage,
                (byte)InvoiceNotificationType.Sms))
        {
            ProcessLogger.Log.Information("{Order.InvoiceId} Skipped. Notification already sent.", order.InvoiceId);
            return;
        }

        queueSms(order, bundle, dal);
    }

    private void queueSms(InvoiceContactDetailsFullDto order, ResultBundleDetailsDto bundle, DataAccessHelper dal)
    {
        ProcessLogger.Log.Information("Queueing SMS to {Order.PhoneNumber}", order.PhoneNumber);
        var transport = new BulkSmsTransportAgent
        {
            DelayMinutes = GlobalSettingsHelper.BulkSmsDelayMinutes,
            ExpiryHours = GlobalSettingsHelper.BulkSmsExpiryHours
        };
        if (!string.IsNullOrEmpty(GlobalSettingsHelper.BulkSmsPickupFolder))
            transport.PickupFolder = GlobalSettingsHelper.BulkSmsPickupFolder;
        transport.DeliverMessage(order, bundle);

        try
        {
            // add the notification to database to prevent multiple sending
            dal.InsertInvoiceNotification(order.InvoiceId,
                (byte)InvoiceNotificationCode.InvoiceCollatedMessage,
                (byte)InvoiceNotificationType.Sms);
            //_logger.Debug("Added invoice notification to database");
        }
        catch (Exception exc)
        {
            ProcessLogger.Log.Error(exc, "Error inserting notification data");
        }
    }
}