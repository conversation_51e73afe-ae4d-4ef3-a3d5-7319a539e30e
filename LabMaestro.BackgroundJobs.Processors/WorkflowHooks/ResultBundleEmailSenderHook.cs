// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ResultBundleEmailSenderHook.cs 1399 2014-09-03 09:57:13Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using LabMaestro.BulkMessageTransport.Email;
using LabMaestro.ServiceDAL.Model;
using LabMaestro.Shared;

namespace LabMaestro.BackgroundJobs;

public class ResultBundleEmailSenderHook : IWorkflowStageHook
{
    public WorkflowStageType DesiredWorkflowStage => WorkflowStageType.ReportCollation;

    public string HookId => "bundle.collation.emailer";

    public void Execute(InvoiceContactDetailsFullDto order, ResultBundleDetailsDto bundle)
    {
#if DEBUG
        // debugging
        //order.EmailTestResults = true;
        //if (string.IsNullOrEmpty(order.EmailAddress))
        //{
        //    order.EmailAddress = "<EMAIL>";
        //}
#endif
        if (order == null
            || order.IsCancelled
            || order.DueAmount > 0m
            || !order.EmailTestResults
            || string.IsNullOrEmpty(order.EmailAddress))
            return;

        if (bundle is not { IsActive: true }
            || bundle.WorkflowStage != (byte)DesiredWorkflowStage)
            return;

        ProcessLogger.Log.Information("Processing invoice: {Order.InvoiceId} bundle: {Bundle.Id)}", order.InvoiceId,
            bundle.Id);
        //var task = Task.Factory.StartNew(() => sendEmail(order, bundle));
        //task.Wait();
        sendEmail(order, bundle);
    }

    private void sendEmail(InvoiceContactDetailsFullDto invoice, ResultBundleDetailsDto bundle)
    {
        ProcessLogger.Log.Information($"Transmitting e-report to <{invoice.EmailAddress}>");
        var transport = new BulkEmailTransportAgent(new BulkEmailContentComposer());
        if (!string.IsNullOrEmpty(GlobalSettingsHelper.BulkEmailPickupFolder))
            transport.PickupFolder = GlobalSettingsHelper.BulkEmailPickupFolder;
        transport.DeliverMessage(invoice, bundle);
    }
}