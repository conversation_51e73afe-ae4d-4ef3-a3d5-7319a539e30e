﻿using System;
using LabMaestro.Messaging.Core;
using LabMaestro.Services.ReportExport;
using LabMaestro.Shared;

namespace LabMaestro.BackgroundJobs.Messaging;

public sealed class PdfReportMailer(PdfReportEmail request) : MailerBase
{
    protected override GenericEmail? ComposeMessage()
    {
        request.Sender = Organization.GetEmailSender();
        ReportExporterFactory.Initialize(true, false);
        var service = new PdfReportExportService(request.InvoiceId, request.ResultBundleId)
        {
            AllowedWorkflowStage = request.AllowedWorkflowStage,
            SkipValidityCheck = request.ReleaseReport,
        };
        service.InitializeService();

        if (!service.HasValidData)
            return null;

        var email = request.ToGenericEmail();
        email.Subject =
            $"Your {service.ResultBundlePrintDto.LabName ?? "laboratory"} test results from Chevron [{request.InvoiceId}:{request.ResultBundleId}]";
        var body = renderMessageBody(service, email);
        email.BodyPlainText = body ?? "Please find attached your laboratory test results.";

        var basename = $"CHEVRON_RESULTS_{request.InvoiceId}_{request.ResultBundleId}_{DateTime.Today:yyMMdd}";
        var tempPath = EphemeralFilePath.MakeTempPath(basename: basename, extension: "pdf");
        service.ExportToFile(tempPath.WrappedFilePath);
        Attachments.Add(tempPath);
        email.Attachments.Add(tempPath.WrappedFilePath);

        return email;
    }

    private string? renderMessageBody(PdfReportExportService service, GenericEmail email)
    {
        var engine = new TemplateEngine();
        engine.Context.Add("organization_name", Organization.Name);
        engine.Context.Add("organization_address", Organization.Address);
        engine.Context.Add("organization_contact", Organization.Contact);
        engine.Context.Add("organization_sender_name", Organization.SenderName);
        engine.Context.Add("organization_sender_email", Organization.SenderEmail);
        engine.Context.Add("organization_app_portal", Organization.AppPortal);

        engine.Context.Add("invoice_id", service.InvoicePrintDto.InvoiceId);
        engine.Context.Add("order_id", service.InvoicePrintDto.OrderId);
        engine.Context.Add("bundle_id", service.ResultBundlePrintDto.ResultBundleId);
        engine.Context.Add("patient_name", service.InvoicePrintDto.PatientName ?? "Sir/Madam");
        engine.Context.Add("invoice_date", service.InvoicePrintDto.BookingDateOnly);
        engine.Context.Add("lab_name", service.ResultBundlePrintDto.LabName ?? "laboratory");

        engine.Context.Add("customer_show",
            !string.IsNullOrEmpty(service.InvoicePrintDto.CustomerId) &&
            email.Recipients.Contains(service.InvoicePrintDto.CustomerEmail));
        engine.Context.Add("customer_upin", service.InvoicePrintDto.CustomerUPIN);
        engine.Context.Add("customer_login", service.InvoicePrintDto.CustomerLogin);
        engine.Context.Add("customer_name", service.InvoicePrintDto.CustomerName);

        engine.Context.Add("corporate_show", !string.IsNullOrEmpty(service.InvoicePrintDto.CorporateClientId));
        engine.Context.Add("corporate_name", service.InvoicePrintDto.CorporateClientName);
        engine.Context.Add("corporate_uid", service.InvoicePrintDto.CorporateClientUID);

        return engine.RenderTemplate("email_results_report");
    }
}