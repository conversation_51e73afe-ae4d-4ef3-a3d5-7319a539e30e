﻿using System;
using System.Collections.Generic;
using LabMaestro.Messaging.Core;
using LabMaestro.Messaging.Transport.Email;
using LabMaestro.Services.ReportExport;
using LabMaestro.Shared;
using YamlDotNet.Serialization;
using YamlDotNet.Serialization.NamingConventions;

namespace LabMaestro.BackgroundJobs.Messaging;

public abstract class MailerBase : IDisposable
{
    protected readonly List<EphemeralFilePath> Attachments = new();

    public MailerBase()
    {
        loadConfig();
        initializeMailer();
        StimulsoftLicensing.InstallLicense(false);
    }

    protected abstract GenericEmail? ComposeMessage();

    public OrganizationInfo Organization { get; private set; }
    public MailerSettings MailerConfig { get; private set; }

    public SimpleEmailSender Mailer { get; private set; }

    public void Cleanup()
    {
        foreach (var attachment in Attachments)
            attachment.Dispose();

        Attachments.Clear();
    }

    public bool Send()
    {
        var email = ComposeMessage();
        return email != null && Mailer.Send(email);
    }

    private void loadConfig()
    {
        var org_cfg = GlobalSettingsHelper.GetConfigContent("organization");

        if (string.IsNullOrEmpty(org_cfg)) {
            Organization = new OrganizationInfo { Name = "Chevron Clinical Laboratory (Pte) Ltd" };
        }
        else {
            var deserializer = new DeserializerBuilder()
                               .WithNamingConvention(UnderscoredNamingConvention.Instance)
                               .Build();
            Organization = deserializer.Deserialize<OrganizationInfo>(org_cfg);
        }

        var mail_cfg = GlobalSettingsHelper.GetConfigContent("mail");
        if (string.IsNullOrEmpty(mail_cfg)) {
            MailerConfig = MailerSettings.Make();
        }
        else {
            var deserializer = new DeserializerBuilder()
                               .WithNamingConvention(UnderscoredNamingConvention.Instance)
                               .Build();
            MailerConfig = deserializer.Deserialize<MailerSettings>(mail_cfg);
        }
    }

    private void initializeMailer()
    {
        Mailer = new SimpleEmailSender
        {
            SmtpHost = MailerConfig.Host,
            SmtpPort = MailerConfig.Port,
            Username = MailerConfig.Login,
            Password = MailerConfig.Password,
            SenderEmail = MailerConfig.Sender,
            StartTls = MailerConfig.Tls,
            MailbeeLicense = MailerConfig.MbLicense,
            RemoveXSender = MailerConfig.ResetHeaders,
        };
    }

    public void Dispose() => Cleanup();
}