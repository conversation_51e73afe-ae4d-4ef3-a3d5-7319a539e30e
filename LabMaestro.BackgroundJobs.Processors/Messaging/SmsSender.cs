﻿using System.Collections.Generic;
using System.Linq;
using LabMaestro.Messaging.Core;
using LabMaestro.Messaging.Transport.Sms;
using Serilog;
using YamlDotNet.Serialization;
using YamlDotNet.Serialization.NamingConventions;
using static System.String;

namespace LabMaestro.BackgroundJobs.Messaging;

file sealed class SmsProviderSettings
{
    public string Name { get; set; }
    public string ApiEndpoint { get; set; }
    public string ApiKey { get; set; }
    public string ApiSecret { get; set; }
}

file sealed class SmsSettings
{
    public string Gateway { get; set; }
    public List<SmsProviderSettings> Providers { get; set; }

    public SmsProviderSettings? GetDefaultProviderSettings() =>
        Providers.FirstOrDefault(p => Compare(p.Name, Gateway, true) == 0);
}

public static class SmsSender
{
    private static bool _initialized = false;

    private static void Initialize()
    {
        if (_initialized)
            return;

        var sms_config = GlobalSettingsHelper.GetConfigContent("sms");
        if (IsNullOrEmpty(sms_config)) return;

        var deserializer = new DeserializerBuilder()
            .WithNamingConvention(UnderscoredNamingConvention.Instance)
            .Build();
        var settings = deserializer.Deserialize<SmsSettings>(sms_config);
        var provider = settings.GetDefaultProviderSettings();
        SmsGatewayFactory.Gateway = settings.Gateway.Trim().ToLowerInvariant();
        SmsGatewayFactory.ApiEndpoint = provider.ApiEndpoint;
        SmsGatewayFactory.ApiKey = provider.ApiKey;
        SmsGatewayFactory.ApiSecret = provider.ApiSecret;

        _initialized = true;
    }

    public static bool SendMessage(SmsMessage payload)
    {
        Initialize();
        Log.Information("SMS via {Gateway}: {@Payload}", SmsGatewayFactory.Gateway, payload);
        return SmsGatewayFactory.SendMessage(payload);
    }
}