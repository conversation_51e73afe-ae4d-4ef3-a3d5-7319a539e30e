﻿using System.Collections.Generic;
using System.IO;
using HandlebarsDotNet;
using LabMaestro.Shared;

namespace LabMaestro.BackgroundJobs.Messaging;

public sealed class TemplateEngine
{
    public const string EXTENSION = "mustache";
    public Dictionary<string, object> Context { get; } = new();

    public string? Render(string? source)
    {
        if (string.IsNullOrEmpty(source) || (Context.Count == 0))
            return source;

        var template = Handlebars.Compile(source);
        var result = template(Context);
        return result;
    }

    public string? LoadTemplate(string templateName)
    {
        if (string.IsNullOrEmpty(templateName))
            return null;

        templateName = Path.ChangeExtension(templateName.ToLowerInvariant(), EXTENSION);
        var path = Path.Combine(SharedUtilities.AssemblyDirectory, "templates", templateName);
        return File.Exists(path) ? File.ReadAllText(path) : null;
    }

    public string? RenderTemplate(string templateName)
    {
        var template = LoadTemplate(templateName);
        return Render(template);
    }
}