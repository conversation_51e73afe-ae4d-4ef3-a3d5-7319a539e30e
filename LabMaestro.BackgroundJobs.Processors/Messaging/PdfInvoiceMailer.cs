﻿using System;
using LabMaestro.Messaging.Core;
using LabMaestro.Services.ReportExport;
using LabMaestro.Shared;

namespace LabMaestro.BackgroundJobs.Messaging;

public class PdfInvoiceMailer(PdfInvoiceEmail request) : MailerBase
{
    protected override GenericEmail? ComposeMessage()
    {
        request.Sender = Organization.GetEmailSender();
        ReportExporterFactory.Initialize(true, false);
        var service = new PdfInvoiceExportService(request.InvoiceId);
        service.InitializeService("<JobsDaemon/>");

        if (!service.HasValidData)
            return null;

        var email = request.ToGenericEmail();
        email.Subject = $"Your invoice from Chevron [{request.InvoiceId}]";
        var body = renderMessageBody(service, email);
        email.BodyPlainText = body ?? "Please find attached your invoice.";

        var basename =
            $"CHEVRON_INVOICE_{service.InvoiceDto.InvoiceId}_{service.InvoiceDto.OrderId}_{DateTime.Today:yyMMdd}";
        var tempPath = EphemeralFilePath.MakeTempPath(basename: basename, extension: "pdf");
        service.ExportToFile(tempPath.WrappedFilePath);
        Attachments.Add(tempPath);
        email.Attachments.Add(tempPath.WrappedFilePath);

        return email;
    }

    private string? renderMessageBody(PdfInvoiceExportService service, GenericEmail email)
    {
        var engine = new TemplateEngine();
        engine.Context.Add("organization_name", Organization.Name);
        engine.Context.Add("organization_address", Organization.Address);
        engine.Context.Add("organization_contact", Organization.Contact);
        engine.Context.Add("organization_sender_name", Organization.SenderName);
        engine.Context.Add("organization_sender_email", Organization.SenderEmail);
        engine.Context.Add("organization_app_portal", Organization.AppPortal);

        engine.Context.Add("invoice_id", service.InvoiceDto.InvoiceId);
        engine.Context.Add("order_id", service.InvoiceDto.OrderId);
        engine.Context.Add("patient_name", service.InvoiceDto.PatientName ?? "Sir/Madam");
        engine.Context.Add("invoice_date", service.InvoiceDto.BookingDateOnly);

        engine.Context.Add("customer_show",
            !string.IsNullOrEmpty(service.InvoiceDto.CustomerId) &&
            email.Recipients.Contains(service.InvoiceDto.CustomerEmail));
        engine.Context.Add("customer_upin", service.InvoiceDto.CustomerUPIN);

        engine.Context.Add("corporate_show", !string.IsNullOrEmpty(service.InvoiceDto.CorporateClientId));
        engine.Context.Add("corporate_name", service.InvoiceDto.CorporateClientName);
        engine.Context.Add("corporate_uid", service.InvoiceDto.CorporateClientUID);

        return engine.RenderTemplate("email_invoice_report");
    }
}