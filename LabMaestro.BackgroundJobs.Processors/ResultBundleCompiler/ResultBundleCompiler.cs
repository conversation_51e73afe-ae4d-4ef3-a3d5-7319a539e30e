﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ResultBundleCompiler.cs 1321 2014-05-26 17:49:05Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using LabMaestro.ServiceDAL.Model;

namespace LabMaestro.BackgroundJobs.ResultBundleCompiler;

public sealed class ResultBundleCompiler(long invoiceId, IInvoiceOrderedTestsDataProvider dataProvider)
{
    private List<OrderedTestDto> _orderedTests = [];
    private List<short> _allLabIds = [];
    private readonly Random _rand = new();
    private readonly Dictionary<string, ResultBundleSlice> _resultBundleSlices = new();
    internal List<ResultBundleSlice> ResultBundleSlices => _resultBundleSlices.Values.ToList();
    public long InvoiceId { get; } = invoiceId;

    private void fetchOrderedTests() => _orderedTests = dataProvider.FetchOrderedTestSlices(InvoiceId);

    public void CompileBundles()
    {
        fetchOrderedTests();
        _resultBundleSlices.Clear();
        _allLabIds = _orderedTests.Select(x => x.LabId).Distinct().ToList();

        // BUG FIX #95: Ordered test sorting issue
        foreach (var slice in _orderedTests.OrderByDescending(x => x.ReportSortPriority))
        {
            // skip if the test is an auxiliary procedure/service
            if (slice.IsAuxProcedure)
                continue;

            ensureBundleForTest(slice)?.AddTest(slice);
        }
    }

    private ResultBundleSlice? ensureBundleForTest(OrderedTestDto dto)
    {
        if (dto.IsCancelled)
            return null;

        ResultBundleSlice bundle;
        if (dto.RequiresExclusiveBundle())
        {
            bundle = new()
            {
                LabId = dto.LabId,
                Name = dto.LabName,
                TatRank = dto.TatRanking,
                ResultType = dto.ResultBundleType
            };
            _resultBundleSlices.Add(generateUniqueKey(), bundle);
            return bundle;
        }

        var bundleKey = generateBundleKey(dto);
        if (_resultBundleSlices.TryGetValue(bundleKey, out bundle))
            return bundle;

        bundle = new ResultBundleSlice
        {
            LabId = dto.LabId,
            Name = dto.LabName,
            TatRank = dto.TatRanking,
            ResultType = dto.ResultBundleType
        };
        _resultBundleSlices.Add(bundleKey, bundle);
        return bundle;
    }

    private string generateBundleKey(OrderedTestDto dto) => $@"__lab__{dto.LabId}_{dto.TatRank}";

    private string generateUniqueKey()
    {
        while (true)
        {
            var num = (short)_rand.Next(1001, short.MaxValue - 1);
            if (_allLabIds.All(x => x != num))
                return $@"__uniq__{num}";
        }
    }
}