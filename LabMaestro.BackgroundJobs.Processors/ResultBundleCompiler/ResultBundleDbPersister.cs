﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ResultBundleDbPersister.cs 1321 2014-05-26 17:49:05Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using LabMaestro.ServiceDAL;
using LabMaestro.Shared;

namespace LabMaestro.BackgroundJobs.ResultBundleCompiler;

public static class ResultBundleDbPersister
{
    public static void InsertNewResultBundles(ResultBundleCompiler compiler, DataAccessHelper db)
    {
        var recencyThreshold = GlobalSettingsHelper.RecentlyUpdatedBundlesMinimumThreshold(db);
        var userId = db.GetUserIdForServiceBusAgent();
        var numSlices = compiler.ResultBundleSlices.Count;
        ProcessLogger.Log.Information(
            "ResultBundleDbPersister.InsertNewResultBundles recency:{RecencyThreshold} uid:{UserId} slices:{NumSlices}",
            recencyThreshold, userId, numSlices);
        foreach (var bundleSlice in compiler.ResultBundleSlices)
        {
            var bundleId = db.InsertResultBundle(userId,
                bundleSlice.LabId,
                compiler.InvoiceId,
                bundleSlice.TatRank,
                bundleSlice.WorkflowStage,
                bundleSlice.ResultType,
                bundleSlice.Name,
                bundleSlice.ComponentLabTests,
                null);

            db.InsertAuditRecord(AuditEventCategory.System,
                AuditEventType.wfResultBundleCreated,
                WorkflowStageType.Unknown,
                userId,
                null,
                compiler.InvoiceId,
                null,
                bundleId);

            updateOrderedTestsInBundle(bundleId, bundleSlice, db);

            // in case of an auto-finalized (usually un-archived) bundle, put it in
            // the recently updated history with a low priority
            if (bundleSlice.WorkflowStage >= recencyThreshold)
                db.UpsertResultBundleToRecentUpdatesList(bundleId,
                    compiler.InvoiceId,
                    bundleSlice.WorkflowStage,
                    SortPriorityType.Low);
        }
    }

    private static void updateOrderedTestsInBundle(long bundleId, ResultBundleSlice bundle, DataAccessHelper db)
    {
        foreach (var slice in bundle.OrderedTests)
        {
            db.UpdateTestWithResultBundle(slice.OrderedTestId, bundleId);
            insertDiscreteResultLineItems(slice.OrderedTestId, bundleId, db);
        }
    }

    private static void insertDiscreteResultLineItems(long ordTestId, long resBundleId, DataAccessHelper db)
    {
        var lineItems = db.GetDiscreteReportLineItemsForOrderedTest(ordTestId);
        foreach (var lineItem in lineItems)
            db.InsertDiscreteResultLineItem(ordTestId,
                resBundleId,
                lineItem.Parameter,
                lineItem.DefaultResult,
                lineItem.Units,
                lineItem.ReferenceRange,
                lineItem.SortOrder,
                lineItem.IndentLevel,
                lineItem.IsResultableItem);
    }
}