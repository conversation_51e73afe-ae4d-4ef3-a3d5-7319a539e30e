﻿using System.Collections.Generic;
using LabMaestro.Shared;

namespace LabMaestro.BackgroundJobs.Lifecycle;

public static class EventHandlerFactory
{
    private static bool _initialized = false;
    private static readonly Dictionary<OrderLifecycleEventType, ILifecycleEventHandler> _handlers = new();
    public static void Register(OrderLifecycleEventType et, ILifecycleEventHandler proc) => _handlers[et] = proc;
    public static void Register(ILifecycleEventHandler proc) => Register(proc.EventType, proc);
    public static ILifecycleEventHandler GetHandler(OrderLifecycleEventType et) => _handlers[et];

    public static void InitializeHandlers()
    {
        if (_initialized) return;

        Register(new OrderCreated());
        Register(new FinancePaymentMade());
        Register(new FinanceStatusUpdated());
        Register(new ResultEntered());
        Register(new ResultVerified());
        Register(new ResultFinalized());
        Register(new ReportCollated());

        _initialized = true;
    }

    public static void Handle(OrderLifecycleEvent ev)
    {
        if (_handlers.TryGetValue(ev.EventType, out var handler)) handler.Handle(ev);
    }
}