﻿using LabMaestro.Shared;

namespace LabMaestro.BackgroundJobs.Lifecycle;

public sealed class FinanceStatusUpdated : OrderEventHandlerBase
{
    public override OrderLifecycleEventType EventType => OrderLifecycleEventType.FinanceStatusUpdate;

    public override bool Handle(OrderLifecycleEvent data)
    {
        // todo: process event
        ProcessLogger.Log.Information("[lifecycle handler] ev:{EventType} data:{@Data}", EventType, data);
        return true;
    }
}