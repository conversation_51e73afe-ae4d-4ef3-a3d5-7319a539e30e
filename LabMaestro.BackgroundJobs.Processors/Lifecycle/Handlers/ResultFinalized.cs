﻿using LabMaestro.Shared;

namespace LabMaestro.BackgroundJobs.Lifecycle;

public class ResultFinalized : OrderEventHandlerBase
{
    public override OrderLifecycleEventType EventType => OrderLifecycleEventType.ResultFinalize;

    public override bool Handle(OrderLifecycleEvent data)
    {
        // todo: process event
        ProcessLogger.Log.Information("[lifecycle handler] ev:{EventType} data:{@Data}", EventType, data);
        return true;
    }
}