﻿using System.Collections.Generic;
using LabMaestro.ServiceDAL;
using LabMaestro.Shared;

namespace LabMaestro.BackgroundJobs.Lifecycle;

public sealed class OrderCreated : OrderEventHandlerBase
{
    public override OrderLifecycleEventType EventType => OrderLifecycleEventType.OrderCreate;
    private readonly List<string> _emails = [];

    private bool emailSent(string email) => _emails.Contains(email.Trim().ToLowerInvariant());

    private void emailAdd(string email) => _emails.Add(email.Trim().ToLowerInvariant());

    public override bool Handle(OrderLifecycleEvent data)
    {
        if (!data.InvoiceId.HasValue) return false;

        using var db = DataAccessFactory.GetClient();
        // check invoice: IsValid, HasEmail
        var invoice = db.GetInvoiceContactDetailsFull((long)data.InvoiceId);

        if (invoice.IsCancelled)
            return true;


        if (invoice.EmailTestResults && !string.IsNullOrEmpty(invoice.EmailAddress))
        {
            emailAdd(invoice.EmailAddress);
            // send email to order level email
        }

        if (data.CustomerId.HasValue)
        {
            if (invoice is { CustomerIsActive: true, CustomerId: not null, CustomerEmail: not "" or null } &&
                invoice.CustomerId.Value == data.CustomerId.Value &&
                !emailSent(invoice.CustomerEmail))
            {
                // send

                emailAdd(invoice.CustomerEmail);
            }
        }

        if (data.CorporateClientId.HasValue)
        {
            if (invoice is { CorporateIsActive: true, CorporateClientId: not null, CorporateEmail: not "" or null } &&
                invoice.CorporateClientId.Value == data.CorporateClientId.Value &&
                !emailSent(invoice.CorporateEmail))
            {
                // send

                emailAdd(invoice.CorporateEmail);
            }
        }

        if (data.AssociateLabId.HasValue)
        {
            if (invoice is
                {
                    AssociateLabIsActive: true, AssociateLabId: not null, AssociateLabEmail: not "" or null
                } &&
                invoice.AssociateLabId.Value == data.AssociateLabId.Value &&
                !emailSent(invoice.AssociateLabEmail))
            {
                // send

                emailAdd(invoice.AssociateLabEmail);
            }
        }

        return true;
    }
}