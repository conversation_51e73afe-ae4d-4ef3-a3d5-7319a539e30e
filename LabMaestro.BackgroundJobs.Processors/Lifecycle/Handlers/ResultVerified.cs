﻿using LabMaestro.Shared;

namespace LabMaestro.BackgroundJobs.Lifecycle;

public class ResultVerified : OrderEventHandlerBase
{
    public override OrderLifecycleEventType EventType => OrderLifecycleEventType.ResultVerify;

    public override bool Handle(OrderLifecycleEvent data)
    {
        // todo: process event
        ProcessLogger.Log.Information("[lifecycle handler] ev:{EventType} data:{@Data}", EventType, data);
        return true;
    }
}