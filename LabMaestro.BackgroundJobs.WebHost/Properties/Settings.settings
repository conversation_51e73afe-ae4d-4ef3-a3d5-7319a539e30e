﻿<?xml version='1.0' encoding='utf-8'?>
<SettingsFile xmlns="http://schemas.microsoft.com/VisualStudio/2004/01/settings" CurrentProfile="(Default)" GeneratedClassNamespace="LabMaestro.BackgroundJobs.WebHost.Properties" GeneratedClassName="Settings">
  <Profiles />
  <Settings>
    <Setting Name="jobsDb" Type="System.String" Scope="Application">
      <Value Profile="(Default)">Data Source=(local);Initial Catalog=Hangfire;Integrated Security=True;TrustServerCertificate=True;Encrypt=false;</Value>
    </Setting>
    <Setting Name="serviceDb" Type="System.String" Scope="Application">
      <Value Profile="(Default)">Data Source=(local);Initial Catalog=LabMaestro;Integrated Security=True;TrustServerCertificate=True;Encrypt=false;</Value>
    </Setting>
    <Setting Name="bulkEmailPickupFolder" Type="System.String" Scope="Application">
      <Value Profile="(Default)">C:\LabMaestro.MailQueue\pickup</Value>
    </Setting>
    <Setting Name="bulkSmsPickupFolder" Type="System.String" Scope="Application">
      <Value Profile="(Default)">C:\LabMaestro.Sms\pickup</Value>
    </Setting>
    <Setting Name="bulkSmsDelay" Type="System.Int32" Scope="Application">
      <Value Profile="(Default)">15</Value>
    </Setting>
    <Setting Name="bulkSmsExpiry" Type="System.Int32" Scope="Application">
      <Value Profile="(Default)">6</Value>
    </Setting>
  </Settings>
</SettingsFile>