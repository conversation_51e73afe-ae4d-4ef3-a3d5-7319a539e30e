﻿using System.Net;
using System.Web.Mvc;
using Hangfire;
using LabMaestro.ServiceDAL;
using LabMaestro.Shared.AppFaults;

namespace LabMaestro.BackgroundJobs.WebHost.Controllers
{
    public class JobController : Controller
    {
        public ActionResult CompileBundle(long invoice)
        {
            BackgroundJob.Enqueue(() => ResultBundleCompilationProcessor.Perform(invoice));
            return new HttpStatusCodeResult(HttpStatusCode.NoContent);
        }

        public ActionResult OrderWorkflowHook(long invoice)
        {
            BackgroundJob.Enqueue(() => LabOrderWorkflowHookProcessor.Perform(invoice));
            return new HttpStatusCodeResult(HttpStatusCode.NoContent);
        }

        public ActionResult EstimateOrderWorkflow(long invoice)
        {
            BackgroundJob.Enqueue(() => LabOrderWorkflowStageEstimateProcessor.Perform(invoice));
            return new HttpStatusCodeResult(HttpStatusCode.NoContent);
        }

        public ActionResult ResultBundleWorkflowHook(long invoice, long bundle)
        {
            BackgroundJob.Enqueue(() => ResultBundleWorkflowHookProcessor.Perform(invoice, bundle));
            return new HttpStatusCodeResult(HttpStatusCode.NoContent);
        }

        [HttpPost]
        public ActionResult AppFault(AppFaultData data)
        {
            try
            {
                using var db = DataAccessFactory.GetClient();
                db.InsertAppFaultData(data);
            }
            catch
            {
                // yawn
            }

            return new HttpStatusCodeResult(HttpStatusCode.NoContent);
        }
    }
}