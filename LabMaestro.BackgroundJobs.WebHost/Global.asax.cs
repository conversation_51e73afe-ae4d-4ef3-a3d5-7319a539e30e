﻿using System;
using System.Collections.Generic;
using System.Web.Routing;
using Hangfire;
using Hangfire.SqlServer;
using LabMaestro.BackgroundJobs.WebHost.Properties;
using LabMaestro.ServiceDAL;

namespace LabMaestro.BackgroundJobs.WebHost
{
    public class WebApiApplication : System.Web.HttpApplication
    {
        private IEnumerable<IDisposable> GetHangfireServers()
        {
            var options = new SqlServerStorageOptions
            {
                SlidingInvisibilityTimeout = TimeSpan.FromMinutes(5),
                QueuePollInterval = TimeSpan.Zero
            };
            
            GlobalSettingsHelper.BulkEmailPickupFolder = Settings.Default.bulkEmailPickupFolder;
            GlobalSettingsHelper.BulkSmsPickupFolder = Settings.Default.bulkSmsPickupFolder;
            GlobalSettingsHelper.BulkSmsDelayMinutes = Settings.Default.bulkSmsDelay;
            GlobalSettingsHelper.BulkSmsExpiryHours = Settings.Default.bulkSmsExpiry;
            DataAccessFactory.SetConnectionString(Settings.Default.serviceDb);

            GlobalConfiguration.Configuration
                .SetDataCompatibilityLevel(CompatibilityLevel.Version_180)
                .UseSimpleAssemblyNameTypeSerializer()
                .UseRecommendedSerializerSettings()
                .UseSqlServerStorage(Settings.Default.jobsDb, options)
                .UseNLogLogProvider();
            
            yield return new BackgroundJobServer();
        }

        protected void Application_Start()
        {
            RouteConfig.RegisterRoutes(RouteTable.Routes);
            HangfireAspNet.Use(GetHangfireServers);
        }
    }
}