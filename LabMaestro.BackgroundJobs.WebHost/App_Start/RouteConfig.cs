﻿using System.Web.Mvc;
using System.Web.Routing;

namespace LabMaestro.BackgroundJobs.WebHost
{
    public class RouteConfig
    {
        public static void RegisterRoutes(RouteCollection routes)
        {
            routes.IgnoreRoute("{resource}.axd/{*pathInfo}");

            routes.MapRoute(
                name: "Default",
                url: "{controller}/{action}/{invoice}",
                defaults: new { controller = "Job" },
                new { invoice = @"\d+" }
            );

            routes.MapRoute(
                name: "Default_Bundle",
                url: "{controller}/{action}/{invoice}/{bundle}",
                defaults: new { controller = "Job" },
                new { invoice = @"\d+", bundle = @"\d+" }
            );

            routes.MapRoute(
                name: "Fault",
                url: "{controller}/{action}",
                defaults: new { controller = "Job" }
            );
        }
    }
}