﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Antlr" version="3.5.0.2" targetFramework="net472" />
  <package id="Hangfire" version="1.8.12" targetFramework="net472" />
  <package id="Hangfire.AspNet" version="0.3.0" targetFramework="net472" />
  <package id="Hangfire.Core" version="1.8.12" targetFramework="net472" />
  <package id="Hangfire.NetCore" version="1.8.12" targetFramework="net472" />
  <package id="Hangfire.SqlServer" version="1.8.12" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Antiforgery" version="2.0.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Cryptography.Internal" version="2.0.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.DataProtection" version="2.0.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.DataProtection.Abstractions" version="2.0.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Hosting.Abstractions" version="2.0.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Hosting.Server.Abstractions" version="2.0.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Http.Abstractions" version="2.0.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Http.Extensions" version="2.0.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Http.Features" version="2.0.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.WebUtilities" version="2.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration.Abstractions" version="2.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="2.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.FileProviders.Abstractions" version="2.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Hosting.Abstractions" version="2.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Logging.Abstractions" version="2.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.ObjectPool" version="2.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Options" version="2.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Primitives" version="2.0.0" targetFramework="net472" />
  <package id="Microsoft.Net.Http.Headers" version="2.0.0" targetFramework="net472" />
  <package id="Microsoft.Owin" version="3.0.1" targetFramework="net472" />
  <package id="Microsoft.Owin.Host.SystemWeb" version="3.0.1" targetFramework="net472" />
  <package id="Microsoft.Win32.Registry" version="4.4.0" targetFramework="net472" />
  <package id="NLog" version="5.3.2" targetFramework="net472" />
  <package id="NLog.Web" version="5.3.11" targetFramework="net472" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi" version="5.3.0" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.Client" version="6.0.0" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.Core" version="5.3.0" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.HelpPage" version="5.3.0" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.WebHost" version="5.3.0" targetFramework="net472" />
  <package id="System.Memory" version="4.5.5" targetFramework="net472" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="4.5.3" targetFramework="net472" />
  <package id="Newtonsoft.Json.Bson" version="1.0.2" targetFramework="net472" />
  <package id="Owin" version="1.0" targetFramework="net472" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net472" />
  <package id="System.Security.AccessControl" version="4.4.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.Xml" version="4.4.0" targetFramework="net472" />
  <package id="System.Security.Principal.Windows" version="4.4.0" targetFramework="net472" />
  <package id="System.Text.Encodings.Web" version="4.4.0" targetFramework="net472" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net472" />
  <package id="Microsoft.AspNet.Mvc" version="5.3.0" targetFramework="net472" />
  <package id="Microsoft.AspNet.Razor" version="3.3.0" targetFramework="net472" />
  <package id="Microsoft.AspNet.Web.Optimization" version="1.1.3" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebPages" version="3.3.0" targetFramework="net472" />
  <package id="Microsoft.CodeDom.Providers.DotNetCompilerPlatform" version="2.0.1" targetFramework="net472" />
  <package id="Microsoft.Web.Infrastructure" version="2.0.0" targetFramework="net472" />
  <package id="Modernizr" version="2.8.3" targetFramework="net472" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net472" />
  <package id="WebGrease" version="1.6.0" targetFramework="net472" />
  <package id="routedebugger" version="2.1.5" targetFramework="net472" />
</packages>