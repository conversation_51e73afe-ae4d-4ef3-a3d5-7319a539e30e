﻿<?xml version="1.0" encoding="utf-8"?>

<!--
  For more information on how to configure your ASP.NET application, please visit
  https://go.microsoft.com/fwlink/?LinkId=301879
  -->
<configuration>
    <configSections>
        <sectionGroup name="applicationSettings"
                      type="System.Configuration.ApplicationSettingsGroup, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
            <section name="LabMaestro.BackgroundJobs.WebHost.Properties.Settings"
                     type="System.Configuration.ClientSettingsSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"
                     requirePermission="false" />
        </sectionGroup>
    </configSections>

    <appSettings>
        <add key="webpages:Version" value="*******" />
        <add key="webpages:Enabled" value="false" />
        <add key="ClientValidationEnabled" value="false" />
        <add key="UnobtrusiveJavaScriptEnabled" value="false" />
        <add key="owin:AutomaticAppStartup" value="false" />
        <add key="RouteDebugger:Enabled" value="true" />
    </appSettings>

    <system.web>
        <compilation debug="true" targetFramework="4.7.2" />
        <httpRuntime targetFramework="4.7.2" />
    </system.web>
    <runtime>
        <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
            <dependentAssembly>
                <assemblyIdentity name="Antlr3.Runtime" publicKeyToken="eb42632606e9261f" />
                <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="Newtonsoft.Json" culture="neutral" publicKeyToken="30ad4fe6b2a6aeed" />
                <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.Web.Optimization" publicKeyToken="31bf3856ad364e35" />
                <bindingRedirect oldVersion="1.0.0.0-1.1.0.0" newVersion="1.1.0.0" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="Microsoft.Web.Infrastructure" publicKeyToken="31bf3856ad364e35" />
                <bindingRedirect oldVersion="0.0.0.0-2.0.0.0" newVersion="2.0.0.0" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35" />
                <bindingRedirect oldVersion="0.0.0.0-1.6.5135.21930" newVersion="1.6.5135.21930" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="Hangfire.Core" publicKeyToken="null" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-1.8.12.0" newVersion="1.8.12.0" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35" />
                <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35" />
                <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35" />
                <bindingRedirect oldVersion="1.0.0.0-5.3.0.0" newVersion="5.3.0.0" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="mscorlib" publicKeyToken="b77a5c561934e089" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System" publicKeyToken="b77a5c561934e089" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.Collections.Immutable" publicKeyToken="b03f5f7f11d50a3a"
                                  culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-8.0.0.0" newVersion="8.0.0.0" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.IO.Compression" publicKeyToken="b77a5c561934e089" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-4.2.0.0" newVersion="4.2.0.0" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-4.0.1.2" newVersion="4.0.1.2" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.Reflection.Metadata" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-8.0.0.0" newVersion="8.0.0.0" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a"
                                  culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-4.0.4.1" newVersion="4.0.4.1" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.Text.Encodings.Web" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-4.0.2.0" newVersion="4.0.2.0" />
            </dependentAssembly>
        </assemblyBinding>
    </runtime>
    <system.codedom>
        <compilers>
            <compiler language="c#;cs;csharp" extension=".cs"
                      type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.CSharpCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35"
                      warningLevel="4" compilerOptions="/langversion:default /nowarn:1659;1699;1701" />
            <compiler language="vb;vbs;visualbasic;vbscript" extension=".vb"
                      type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.VBCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35"
                      warningLevel="4"
                      compilerOptions="/langversion:default /nowarn:41008 /define:_MYTYPE=\&quot;Web\&quot; /optionInfer+" />
        </compilers>
    </system.codedom>
    <applicationSettings>
        <LabMaestro.BackgroundJobs.WebHost.Properties.Settings>
            <setting name="jobsDb" serializeAs="String">
                <value>Data Source=(local);Initial Catalog=Hangfire;Integrated Security=True;TrustServerCertificate=True;Encrypt=false;</value>
            </setting>
            <setting name="serviceDb" serializeAs="String">
                <value>Data Source=(local);Initial Catalog=LabMaestro;Integrated Security=True;TrustServerCertificate=True;Encrypt=false;</value>
            </setting>
            <setting name="bulkEmailPickupFolder" serializeAs="String">
                <value>C:\LabMaestro.MailQueue\pickup</value>
            </setting>
            <setting name="bulkSmsPickupFolder" serializeAs="String">
                <value>C:\LabMaestro.Sms\pickup</value>
            </setting>
            <setting name="bulkSmsDelay" serializeAs="String">
                <value>15</value>
            </setting>
            <setting name="bulkSmsExpiry" serializeAs="String">
                <value>6</value>
            </setting>
        </LabMaestro.BackgroundJobs.WebHost.Properties.Settings>
    </applicationSettings>
    
    <system.webServer>
        <handlers>
            <remove name="ExtensionlessUrlHandler-Integrated-4.0" />
            <remove name="OPTIONSVerbHandler" />
            <remove name="TRACEVerbHandler" />
            <add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="*"
                 type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" />
        </handlers>
    </system.webServer>
</configuration>