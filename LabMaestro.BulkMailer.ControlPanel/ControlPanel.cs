// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ControlPanel.cs 1429 2014-10-01 06:45:24Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.ComponentModel;
using System.Diagnostics;
using System.IO;
using System.Windows.Forms;
using LabMaestro.BulkMailer.Core;
using MailBee;
using MailBee.Security;
using MailBee.SmtpMail;

public class ControlPanel : Form
{
    private const string LicenseKeyInRegistry = "{Valid license key found in the registry}";
    private const int AuthComboIndexAnonym = 0;
    private const int AuthComboIndexAuto = 1;
    private const int AuthComboIndexLogin = 2;
    private const int AuthComboIndexPlain = 3;
    private const int AuthComboIndexNtlm = 4;
    private const int AuthComboIndexGssApi = 5;
    private const int AuthComboIndexIwaNtlm = 6;
    private const int AuthComboIndexIwaGssApi = 7;
    private bool _changed;
    private QueueRunner _qr;
    private bool _queueRootChanged;
    private int badFileCount;
    private Button btnBrowse;
    private Button btnClearLog;
    private Button btnInstall;
    private Button btnRetryBads;
    private Button btnSaveSettings;
    private Button btnStart;
    private Button btnStop;
    private Button btnTest;
    private Button btnUninstall;
    private Button btnViewLog;
    private CheckBox cbDisableChunking;
    private CheckBox cbEnableLogging;
    private ComboBox comboAuth;
    private ComboBox comboSsl;
    private IContainer components;
    private FolderBrowserDialog folderSelect;
    private FileSystemWatcher fswBad;
    private FileSystemWatcher fswPickup;
    private Label lBadFiles;
    private Label lPickupFiles;
    private Label lServiceStatus;
    private Label label1;
    private Label label10;
    private Label label11;
    private Label label12;
    private Label label13;
    private Label label14;
    private Label label15;
    private Label label16;
    private Label label17;
    private Label label18;
    private Label label19;
    private Label label2;
    private Label label20;
    private Label label21;
    private Label label22;
    private Label label23;
    private Label label24;
    private Label label3;
    private Label label4;
    private Label label5;
    private Label label6;
    private Label label7;
    private Label label8;
    private Label label9;
    private NumericUpDown nMaxThreads;

    private int pickupFileCount;
    private TextBox tbActualLogFilePath;
    private TextBox tbBadFolder;
    private TextBox tbLicenseKey;
    private TextBox tbLogFilename;
    private TextBox tbMaxSendsPerSession;
    private TextBox tbMaxSimultConnections;
    private TextBox tbPassword;
    private TextBox tbPause;
    private TextBox tbPickupFolder;
    private TextBox tbPort;
    private TextBox tbQueueRoot;
    private TextBox tbServerName;
    private TextBox tbUserName;
    private TabControl tcControlPanel;
    private Timer timer;
    private int timerTickCount;
    private TabPage tpLogging;
    private TabPage tpMain;
    private TabPage tpSmtp;

    public ControlPanel()
    {
        //
        // Required for Windows Form Designer support
        //
        InitializeComponent();

        //
        // TODO: Add any constructor code after InitializeComponent call
        //
    }

    /// <summary>
    ///     Clean up any resources being used.
    /// </summary>
    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            if (components != null)
            {
                components.Dispose();
            }
        }
        base.Dispose(disposing);
    }

    /// <summary>
    ///     The main entry point for the application.
    /// </summary>
    [STAThread]
    private static void Main()
    {
        Application.Run(new ControlPanel());
    }

    private int GetSslComboIndexByEnumValue(SslStartupMode sslMode)
    {
        switch (sslMode)
        {
            case SslStartupMode.Manual:
                return 0;
            case SslStartupMode.OnConnect:
                return 1;
            case SslStartupMode.UseStartTlsIfSupported:
                return 2;
            case SslStartupMode.UseStartTls:
                return 3;
            default:
                return 0;
        }
    }

    private SslStartupMode GetSslEnumValueByComboIndex(int comboIndex)
    {
        switch (comboIndex)
        {
            case 0:
                return SslStartupMode.Manual;
            case 1:
                return SslStartupMode.OnConnect;
            case 2:
                return SslStartupMode.UseStartTlsIfSupported;
            case 3:
                return SslStartupMode.UseStartTls;
            default:
                return SslStartupMode.Manual;
        }
    }

    private int GetAuthComboIndexByEnumValue(AuthenticationMethods authMethods, string userName)
    {
        switch (authMethods)
        {
            case AuthenticationMethods.None:
                return AuthComboIndexAnonym;
            case AuthenticationMethods.Auto:
                return AuthComboIndexAuto;
            case AuthenticationMethods.SaslLogin:
                return AuthComboIndexLogin;
            case AuthenticationMethods.SaslPlain:
                return AuthComboIndexPlain;
            case AuthenticationMethods.SaslNtlm:
                if (string.IsNullOrEmpty(userName))
                {
                    return AuthComboIndexIwaNtlm;
                }
                return AuthComboIndexNtlm;
            case AuthenticationMethods.SaslGssApi:
                if (string.IsNullOrEmpty(userName))
                {
                    return AuthComboIndexIwaGssApi;
                }
                return AuthComboIndexGssApi;
            default:
                return AuthComboIndexAnonym;
        }
    }

    private AuthenticationMethods GetAuthEnumValueByComboIndex(int comboIndex)
    {
        switch (comboIndex)
        {
            case AuthComboIndexAnonym:
                return AuthenticationMethods.None;
            case AuthComboIndexAuto:
                return AuthenticationMethods.Auto;
            case AuthComboIndexLogin:
                return AuthenticationMethods.SaslLogin;
            case AuthComboIndexPlain:
                return AuthenticationMethods.SaslPlain;
            case AuthComboIndexNtlm:
            case AuthComboIndexIwaNtlm:
                return AuthenticationMethods.SaslNtlm;
            case AuthComboIndexGssApi:
            case AuthComboIndexIwaGssApi:
                return AuthenticationMethods.SaslGssApi;
            default:
                return AuthenticationMethods.None;
        }
    }

    private bool IsAuthUsernameRequiredByComboIndex(int comboIndex)
    {
        return
            !(comboIndex == AuthComboIndexAnonym || comboIndex == AuthComboIndexIwaNtlm ||
              comboIndex == AuthComboIndexIwaGssApi);
    }

    private void EnableUserNameAndPasswordControlsByCombo(int selectedIndex)
    {
        bool enable = (selectedIndex != AuthComboIndexAnonym &&
                       selectedIndex != AuthComboIndexIwaNtlm &&
                       selectedIndex != AuthComboIndexIwaGssApi);
        tbUserName.Enabled = enable;
        tbPassword.Enabled = enable;
    }

    private void EnableLogFilenameByCheckbox(bool isChecked)
    {
        tbLogFilename.Enabled = isChecked;
    }

    private void SetActualLogFilePath()
    {
        tbActualLogFilePath.Text = LoggingConfig.GetActualLogFilePath(tbLogFilename.Text,
            _qr.ServiceConfig.Queue.RootFolder);
    }

    private void TrimTextBox(params TextBox[] items)
    {
        foreach (TextBox item in items)
        {
            item.Text = item.Text.Trim();
        }
    }

    private void SetPickupAndBadFoldersByQueueRoot()
    {
        tbPickupFolder.Text = QueueConfig.GetPickupFolderPath(tbQueueRoot.Text);
        tbBadFolder.Text = QueueConfig.GetBadFolderPath(tbQueueRoot.Text);
    }

    private bool CreateDirectoryStructureIfNeeded()
    {
        _qr.SetRootFolderIfNotSet();
        if (!_qr.RootDirectoryExists())
        {
            string rootFolder = _qr.ServiceConfig.Queue.RootFolder;
            if (rootFolder == string.Empty)
            {
                rootFolder = _qr.GetDefaultRootFolder();
            }
            if (MessageBox.Show(
                @"In order to function properly, MailBee.NET Queue needs a set of folders on your computer:
- the pickup folder where your applications will drop .EML files to be sent
- the bad e-mails folders where MailBee.NET Queue will move .EML files which could not be sent
- log files folder
All these folders will be sub-folders of so called Queue Root folder.

MailBee.NET Queue can now create """ + rootFolder + @""" Queue Root folder and its sub-folders Pickup, Bad, Logs.
Click Yes to create these folders. Click No to use other name for the Queue Root folder (you'll need
to create all the folders manually and then change the Queue Root in MailBee.NET Queue Control Panel.",
                "Set working folders", MessageBoxButtons.YesNo) == DialogResult.Yes)
            {
                _qr.CreateDirectoryStructure();
                return true;
            }

            return false;
        }

        return true;
    }

    private void InitFileSystemWatch(bool showErrors)
    {
        try
        {
            pickupFileCount = _qr.GetPickupCount();
            fswPickup.Path = _qr.ServiceConfig.Queue.GetPickupFolderPath();
        }
        catch (IOException ex)
        {
            if (showErrors) ShowError(ex.Message);
        }
        try
        {
            badFileCount = _qr.GetBadCount();
            fswBad.Path = _qr.ServiceConfig.Queue.GetBadFolderPath();
        }
        catch (IOException ex)
        {
            if (showErrors) ShowError(ex.Message);
        }

        if (Directory.Exists(_qr.ServiceConfig.Queue.GetPickupFolderPath()))
        {
            lPickupFiles.Text = _qr.GetPickupCount().ToString();
        }
        else
        {
            lPickupFiles.Text = "?";
        }
        if (Directory.Exists(_qr.ServiceConfig.Queue.GetBadFolderPath()))
        {
            lBadFiles.Text = _qr.GetBadCount().ToString();
        }
        else
        {
            lBadFiles.Text = "?";
        }
    }

    private void LoadDataFromConfig()
    {
        _qr = new QueueRunner(false);
        BulkMailerConfig conf = _qr.ServiceConfig;

        if (string.IsNullOrEmpty(conf.LicenseKey))
        {
            try
            {
                var mailerr = new Smtp();
                tbLicenseKey.Text = LicenseKeyInRegistry;
            }
            catch (MailBeeLicenseException)
            {
                tbLicenseKey.Text = string.Empty;
            }
        }
        else
        {
            tbLicenseKey.Text = conf.LicenseKey;
        }

        bool foldersOK = CreateDirectoryStructureIfNeeded();

        tbQueueRoot.Text = conf.Queue.RootFolder;
        SetPickupAndBadFoldersByQueueRoot();
        InitFileSystemWatch(foldersOK);
        nMaxThreads.Value = conf.MaxThreadCount;

        if (conf.Queue.SmtpServers.Count > 0)
        {
            SmtpServer smtp = conf.Queue.SmtpServers[0];
            tbServerName.Text = smtp.Name;
            tbPort.Text = smtp.Port.ToString();
            comboSsl.SelectedIndex = GetSslComboIndexByEnumValue(smtp.SslMode);
            comboAuth.SelectedIndex = GetAuthComboIndexByEnumValue(smtp.AuthMethods, smtp.AccountName);
            tbUserName.Text = smtp.AccountName;
            tbPassword.Text = smtp.Password;
            EnableUserNameAndPasswordControlsByCombo(comboAuth.SelectedIndex);
            tbMaxSimultConnections.Text = smtp.MaxConnectionCount.ToString();
            tbMaxSendsPerSession.Text = smtp.MaxSendPerSessionCount.ToString();
            tbPause.Text = smtp.PauseInterval.ToString();
            cbDisableChunking.Checked = ((smtp.SmtpOptions & ExtendedSmtpOptions.NoChunking) > 0);
        }

        cbEnableLogging.Checked = conf.Logging.Enabled;
        EnableLogFilenameByCheckbox(cbEnableLogging.Checked);
        tbLogFilename.Text = conf.Logging.Filename;
        SetActualLogFilePath();
        _changed = false;
        _queueRootChanged = false;
        btnRetryBads.Enabled = true;
    }

    private bool ValidateData()
    {
        TrimTextBox(tbLicenseKey, tbServerName, tbQueueRoot, tbUserName, tbPassword, tbLogFilename);

        try
        {
            if (tbLicenseKey.Text != LicenseKeyInRegistry)
            {
                Global.LicenseKey = tbLicenseKey.Text;
            }
            else
            {
                var mailer = new Smtp();
            }
        }
        catch (MailBeeLicenseException)
        {
            ShowError("The specified license key is incorrect");
            if (tbLicenseKey.Text == LicenseKeyInRegistry)
            {
                tbLicenseKey.Text = string.Empty;
            }
            return false;
        }
        if (tbLicenseKey.Text == string.Empty)
        {
            tbLicenseKey.Text = LicenseKeyInRegistry;
        }
        if (cbEnableLogging.Checked)
        {
            if (tbLogFilename.Text.Length == 0)
            {
                ShowError("Empty log file name not allowed if logging is enabled");
                return false;
            }
            if (!Directory.Exists(Path.GetDirectoryName(tbActualLogFilePath.Text)))
            {
                ShowError("The specified logs folder does not exist");
                return false;
            }
        }
        if (!Directory.Exists(tbQueueRoot.Text))
        {
            ShowError("The specified queue root folder does not exist");
            return false;
        }
        if (!Directory.Exists(QueueConfig.GetPickupFolderPath(tbQueueRoot.Text)))
        {
            ShowError("The pickup folder \"" + QueueConfig.GetPickupFolderPath(tbQueueRoot.Text) + "\" does not exist");
            return false;
        }
        if (!Directory.Exists(QueueConfig.GetBadFolderPath(tbQueueRoot.Text)))
        {
            ShowError("The bad files folder \"" + QueueConfig.GetBadFolderPath(tbQueueRoot.Text) + "\" does not exist");
            return false;
        }
        if (nMaxThreads.Value == 0)
        {
            ShowError("Specify -1 to allow any number of threads or 1 to allow only one thread");
            return false;
        }

        // If SMTP relay is set, check if it's set correctly.
        if (_qr.ServiceConfig.Queue.SmtpServers.Count > 0 || tbServerName.Text.Length > 0)
        {
            if (comboAuth.SelectedIndex < 0)
            {
                comboAuth.SelectedIndex = 0;
            }
            if (comboSsl.SelectedIndex < 0)
            {
                comboSsl.SelectedIndex = 0;
            }

            if (IsAuthUsernameRequiredByComboIndex(comboAuth.SelectedIndex) && tbUserName.Text.Length == 0)
            {
                ShowError("Empty user name not allowed for the given SMTP authentication method");
                return false;
            }
            int dummy;
            if (!int.TryParse(tbPort.Text, out dummy) || dummy < 1)
            {
                ShowError("Port number is incorrect");
                return false;
            }
            if (!int.TryParse(tbMaxSimultConnections.Text, out dummy) || dummy == 0)
            {
                ShowError("Max simultaneous connections number is incorrect");
                return false;
            }
            if (!int.TryParse(tbMaxSendsPerSession.Text, out dummy) || dummy == 0)
            {
                ShowError("Max number of e-mails to be sent over an SMTP session is incorrect");
                return false;
            }
            if (!int.TryParse(tbPause.Text, out dummy) || dummy < 0)
            {
                ShowError("The value of pause interval between two SMTP sessions is incorrect");
                return false;
            }
        }

        return true;
    }

    private bool SaveDataToConfig()
    {
        if (!ValidateData())
        {
            return false;
        }

        BulkMailerConfig conf = _qr.ServiceConfig;
        if (tbLicenseKey.Text != LicenseKeyInRegistry)
        {
            conf.LicenseKey = tbLicenseKey.Text;
        }
        conf.Queue.RootFolder = tbQueueRoot.Text;
        SetPickupAndBadFoldersByQueueRoot();
        InitFileSystemWatch(true);
        conf.MaxThreadCount = (int) nMaxThreads.Value;

        SmtpServer smtp = null;
        if (conf.Queue.SmtpServers.Count == 0)
        {
            if (tbServerName.Text.Length > 0)
            {
                smtp = new SmtpServer();
                conf.Queue.SmtpServers.Add(smtp);
            }

            // Otherwise, assume we do not use SMTP relay (direct send, maybe).
        }
        else
        {
            smtp = conf.Queue.SmtpServers[0];
        }

        if (smtp != null)
        {
            smtp.Name = tbServerName.Text;
            smtp.Port = int.Parse(tbPort.Text);
            smtp.SslMode = GetSslEnumValueByComboIndex(comboSsl.SelectedIndex);
            smtp.AuthMethods = GetAuthEnumValueByComboIndex(comboAuth.SelectedIndex);
            smtp.AccountName = tbUserName.Text;
            smtp.Password = tbPassword.Text;
            smtp.MaxConnectionCount = int.Parse(tbMaxSimultConnections.Text);
            smtp.MaxSendPerSessionCount = int.Parse(tbMaxSendsPerSession.Text);
            smtp.PauseInterval = int.Parse(tbPause.Text);
            if (cbDisableChunking.Checked)
            {
                smtp.SmtpOptions |= ExtendedSmtpOptions.NoChunking;
            }
            else
            {
                smtp.SmtpOptions &= ~ExtendedSmtpOptions.NoChunking;
            }
        }

        conf.Logging.Filename = tbLogFilename.Text;
        conf.Logging.Enabled = cbEnableLogging.Checked;

        conf.Save("app.config");
        _changed = false;
        _queueRootChanged = false;
        btnRetryBads.Enabled = true;
        return true;
    }

    private void ControlPanel_Load(object sender, EventArgs e)
    {
        LoadDataFromConfig();
        ShowServiceStatus();
    }

    private void comboAuth_SelectedIndexChanged(object sender, EventArgs e)
    {
        EnableUserNameAndPasswordControlsByCombo(comboAuth.SelectedIndex);
        if (!_changed) _changed = true;
    }

    private void cbEnableLogging_CheckedChanged(object sender, EventArgs e)
    {
        EnableLogFilenameByCheckbox(cbEnableLogging.Checked);
        if (!_changed) _changed = true;
    }

    private void tbLogFilename_TextChanged(object sender, EventArgs e)
    {
        SetActualLogFilePath();
        if (!_changed) _changed = true;
    }

    private void ShowError(string text)
    {
        MessageBox.Show(text, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
    }

    private Process StartServiceApp(string argument)
    {
        try
        {
            return Process.Start(Path.Combine(Application.StartupPath, "MailBeeNetQueue.exe"), argument);
        }
        catch (Win32Exception ex)
        {
            ShowError("MailBeeNetQueue.exe probably not found in the current folder. More details:\r\n" + ex.Message);
            return null;
        }
    }

    private ServiceStatus GetServiceStatus()
    {
        return ServiceStatusChecker.GetServiceStatus(ServiceStatusChecker.ServiceName);
    }

    private void ShowServiceStatus()
    {
        ServiceStatus s = GetServiceStatus();
        switch (s)
        {
            case ServiceStatus.Error:
                lServiceStatus.Text = "Error";
                break;
            case ServiceStatus.NotInstalled:
            case ServiceStatus.Stopped:
                lServiceStatus.Text = "Stopped";
                break;
            case ServiceStatus.Running:
                lServiceStatus.Text = "Started";
                break;
        }
    }

    private string getServiceLocation()
    {
        return Path.Combine(Application.StartupPath, "LabMaestro.BulkMailer.Service.exe");
    }

    private void btnStart_Click(object sender, EventArgs e)
    {
        if (_changed)
        {
            DialogResult r = MessageBox.Show("Save configuration changes?", "Question", MessageBoxButtons.OKCancel);
            if (r == DialogResult.OK)
            {
                if (!SaveDataToConfig())
                {
                    return;
                }
            }
            else
            {
                return;
            }
        }
        else if (!ValidateData())
        {
            return;
        }
        Process proc = null;

        try
        {
            proc = Process.Start(getServiceLocation(), "start");
        }
        catch (Win32Exception ex)
        {
            ShowError("LabMaestro.BulkMailer.Service.exe probably not found in the current folder. More details:\r\n" +
                      ex.Message);
            lServiceStatus.Text = "Error";
        }


        if (proc != null)
        {
            proc.WaitForExit();
            switch (proc.ExitCode)
            {
                case (int) ServiceError.OK:
                    lServiceStatus.Text = "Started";
                    break;
                case (int) ServiceError.AlreadyInstalled:
                    ShowError("The service already started/installed");
                    lServiceStatus.Text = "Started";
                    break;
                case (int) ServiceError.AlreadyRunning:
                    ShowError("The service already running");
                    lServiceStatus.Text = "Started";
                    break;
                case (int) ServiceError.StartFailed:
                    ShowError("The service failed to start. See Event Viewer / Application for more details.");
                    lServiceStatus.Text = "Started";
                    break;
                default:
                    ShowError("The unknown error occurred");
                    lServiceStatus.Text = "Error";
                    break;
            }
        }
    }

    private void btnStop_Click(object sender, EventArgs e)
    {
        Process p = null;
        try
        {
            p = Process.Start(getServiceLocation(), "stop");
        }
        catch (Win32Exception ex)
        {
            ShowError("LabMaestro.BulkMailer.Service.exe probably not found in the current folder. More details:\r\n" +
                      ex.Message);
            lServiceStatus.Text = "Error";
        }

        if (p != null)
        {
            p.WaitForExit();
            switch (p.ExitCode)
            {
                case (int) ServiceError.OK:
                    lServiceStatus.Text = "Stopped";
                    break;
                case (int) ServiceError.NotInstalled:
                    ShowError("The service already stopped or even not installed");
                    lServiceStatus.Text = "Stopped";
                    break;
                default:
                    ShowError("The unknown error occurred");
                    lServiceStatus.Text = "Error";
                    break;
            }
        }
    }

    private void btnSaveSettings_Click(object sender, EventArgs e)
    {
        SaveDataToConfig();
    }

    private void btnTest_Click(object sender, EventArgs e)
    {
        TrimTextBox(tbLicenseKey);
        try
        {
            if (tbLicenseKey.Text != LicenseKeyInRegistry)
            {
                Global.LicenseKey = tbLicenseKey.Text;
            }
            else
            {
                var mailer = new Smtp();
            }

            MessageBox.Show("License key is OK");
            if (tbLicenseKey.Text == string.Empty)
            {
                tbLicenseKey.Text = LicenseKeyInRegistry;
            }
        }
        catch (MailBeeLicenseException ex)
        {
            ShowError(ex.Message);
            if (tbLicenseKey.Text == LicenseKeyInRegistry)
            {
                tbLicenseKey.Text = string.Empty;
            }
        }
    }

    private void btnBrowse_Click(object sender, EventArgs e)
    {
        if (folderSelect.ShowDialog() == DialogResult.OK)
        {
            tbQueueRoot.Text = folderSelect.SelectedPath;
        }
    }

    private void btnRetryBads_Click(object sender, EventArgs e)
    {
        try
        {
            _qr.RetryBads();
        }
        catch (IOException ex)
        {
            ShowError(ex.Message);
        }
    }

    private void timer_Tick(object sender, EventArgs e)
    {
        ShowServiceStatus();
        timerTickCount++;
        if (timerTickCount >= 5)
        {
            timerTickCount = 0;
            InitFileSystemWatch(false);
        }
    }

    private void btnViewLog_Click(object sender, EventArgs e)
    {
        if (File.Exists(tbActualLogFilePath.Text))
        {
            Process.Start(tbActualLogFilePath.Text);
        }
        else
        {
            ShowError("The log file with the given name does not exist yet");
        }
    }

    private void btnClearLog_Click(object sender, EventArgs e)
    {
        if (File.Exists(tbActualLogFilePath.Text))
        {
            using (File.Create(tbActualLogFilePath.Text))
            {
            }
        }
        else
        {
            ShowError("The log file with the given name does not exist yet");
        }
    }

    private void tbLicenseKey_TextChanged(object sender, EventArgs e)
    {
        if (!_changed) _changed = true;
    }

    private void tbQueueRoot_TextChanged(object sender, EventArgs e)
    {
        if (!_changed) _changed = true;
        if (!_queueRootChanged)
        {
            _queueRootChanged = true;
            btnRetryBads.Enabled = false;
        }
        tbPickupFolder.Text = "<Click Save Settings to apply changes>";
        tbBadFolder.Text = "<Click Save Settings to apply changes>";
        lPickupFiles.Text = "N/A";
        lBadFiles.Text = "N/A";
    }

    private void nMaxThreads_ValueChanged(object sender, EventArgs e)
    {
        if (!_changed) _changed = true;
    }

    private void tbServerName_TextChanged(object sender, EventArgs e)
    {
        if (!_changed) _changed = true;
    }

    private void tbPort_TextChanged(object sender, EventArgs e)
    {
        if (!_changed) _changed = true;
    }

    private void comboSsl_SelectedIndexChanged(object sender, EventArgs e)
    {
        if (!_changed) _changed = true;
    }

    private void tbUserName_TextChanged(object sender, EventArgs e)
    {
        if (!_changed) _changed = true;
    }

    private void tbPassword_TextChanged(object sender, EventArgs e)
    {
        if (!_changed) _changed = true;
    }

    private void fswPickup_Created(object sender, FileSystemEventArgs e)
    {
        if (_queueRootChanged) return;
        pickupFileCount++;
        lPickupFiles.Text = pickupFileCount.ToString();
    }

    private void fswPickup_Deleted(object sender, FileSystemEventArgs e)
    {
        if (_queueRootChanged) return;
        if (pickupFileCount > 0)
        {
            pickupFileCount--;
        }
        else
        {
            pickupFileCount = _qr.GetPickupCount();
        }
        lPickupFiles.Text = pickupFileCount.ToString();
    }

    private void fswBad_Created(object sender, FileSystemEventArgs e)
    {
        if (_queueRootChanged) return;
        badFileCount++;
        lBadFiles.Text = badFileCount.ToString();
    }

    private void fswBad_Deleted(object sender, FileSystemEventArgs e)
    {
        if (_queueRootChanged) return;
        if (badFileCount > 0)
        {
            badFileCount--;
        }
        else
        {
            badFileCount = _qr.GetBadCount();
        }
        lBadFiles.Text = badFileCount.ToString();
    }

    private void tbMaxSimultConnections_TextChanged(object sender, EventArgs e)
    {
        if (!_changed) _changed = true;
    }

    private void tbMaxSendsPerSession_TextChanged(object sender, EventArgs e)
    {
        if (!_changed) _changed = true;
    }

    private void tbPause_TextChanged(object sender, EventArgs e)
    {
        if (!_changed) _changed = true;
    }

    private void cbDisableChunking_CheckedChanged(object sender, EventArgs e)
    {
        if (!_changed) _changed = true;
    }

    private void btnInstall_Click(object sender, EventArgs e)
    {
        Process proc = null;
        try
        {
            proc = Process.Start(getServiceLocation(), "install");
        }
        catch (Win32Exception ex)
        {
            ShowError("LabMaestro.BulkMailer.Service.exe probably not found in the current folder. More details:\r\n" +
                      ex.Message);
            lServiceStatus.Text = "Error";
        }
    }

    private void btnUninstall_Click(object sender, EventArgs e)
    {
        Process p = null;
        try
        {
            p = Process.Start(getServiceLocation(), "uninstall");
        }
        catch (Win32Exception ex)
        {
            ShowError("LabMaestro.BulkMailer.Service.exe probably not found in the current folder. More details:\r\n" +
                      ex.Message);
            lServiceStatus.Text = "Error";
        }
    }

    #region Windows Form Designer generated code

    /// <summary>
    ///     Required method for Designer support - do not modify
    ///     the contents of this method with the code editor.
    /// </summary>
    private void InitializeComponent()
    {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ControlPanel));
            this.tcControlPanel = new System.Windows.Forms.TabControl();
            this.tpMain = new System.Windows.Forms.TabPage();
            this.btnUninstall = new System.Windows.Forms.Button();
            this.btnInstall = new System.Windows.Forms.Button();
            this.label24 = new System.Windows.Forms.Label();
            this.btnRetryBads = new System.Windows.Forms.Button();
            this.btnTest = new System.Windows.Forms.Button();
            this.nMaxThreads = new System.Windows.Forms.NumericUpDown();
            this.label9 = new System.Windows.Forms.Label();
            this.btnStop = new System.Windows.Forms.Button();
            this.btnStart = new System.Windows.Forms.Button();
            this.lServiceStatus = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.lBadFiles = new System.Windows.Forms.Label();
            this.tbBadFolder = new System.Windows.Forms.TextBox();
            this.label6 = new System.Windows.Forms.Label();
            this.lPickupFiles = new System.Windows.Forms.Label();
            this.tbPickupFolder = new System.Windows.Forms.TextBox();
            this.label3 = new System.Windows.Forms.Label();
            this.btnBrowse = new System.Windows.Forms.Button();
            this.tbQueueRoot = new System.Windows.Forms.TextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.tbLicenseKey = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.tpSmtp = new System.Windows.Forms.TabPage();
            this.label23 = new System.Windows.Forms.Label();
            this.label22 = new System.Windows.Forms.Label();
            this.tbPause = new System.Windows.Forms.TextBox();
            this.tbMaxSendsPerSession = new System.Windows.Forms.TextBox();
            this.tbMaxSimultConnections = new System.Windows.Forms.TextBox();
            this.cbDisableChunking = new System.Windows.Forms.CheckBox();
            this.label21 = new System.Windows.Forms.Label();
            this.label20 = new System.Windows.Forms.Label();
            this.label19 = new System.Windows.Forms.Label();
            this.label18 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.tbPassword = new System.Windows.Forms.TextBox();
            this.label14 = new System.Windows.Forms.Label();
            this.tbUserName = new System.Windows.Forms.TextBox();
            this.label15 = new System.Windows.Forms.Label();
            this.comboAuth = new System.Windows.Forms.ComboBox();
            this.label13 = new System.Windows.Forms.Label();
            this.label12 = new System.Windows.Forms.Label();
            this.comboSsl = new System.Windows.Forms.ComboBox();
            this.tbPort = new System.Windows.Forms.TextBox();
            this.label10 = new System.Windows.Forms.Label();
            this.tbServerName = new System.Windows.Forms.TextBox();
            this.label11 = new System.Windows.Forms.Label();
            this.tpLogging = new System.Windows.Forms.TabPage();
            this.label5 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.btnClearLog = new System.Windows.Forms.Button();
            this.btnViewLog = new System.Windows.Forms.Button();
            this.cbEnableLogging = new System.Windows.Forms.CheckBox();
            this.tbActualLogFilePath = new System.Windows.Forms.TextBox();
            this.label17 = new System.Windows.Forms.Label();
            this.tbLogFilename = new System.Windows.Forms.TextBox();
            this.label16 = new System.Windows.Forms.Label();
            this.btnSaveSettings = new System.Windows.Forms.Button();
            this.folderSelect = new System.Windows.Forms.FolderBrowserDialog();
            this.timer = new System.Windows.Forms.Timer(this.components);
            this.fswPickup = new System.IO.FileSystemWatcher();
            this.fswBad = new System.IO.FileSystemWatcher();
            this.tcControlPanel.SuspendLayout();
            this.tpMain.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nMaxThreads)).BeginInit();
            this.tpSmtp.SuspendLayout();
            this.tpLogging.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.fswPickup)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.fswBad)).BeginInit();
            this.SuspendLayout();
            // 
            // tcControlPanel
            // 
            this.tcControlPanel.Controls.Add(this.tpMain);
            this.tcControlPanel.Controls.Add(this.tpSmtp);
            this.tcControlPanel.Controls.Add(this.tpLogging);
            this.tcControlPanel.Location = new System.Drawing.Point(3, 3);
            this.tcControlPanel.Name = "tcControlPanel";
            this.tcControlPanel.SelectedIndex = 0;
            this.tcControlPanel.Size = new System.Drawing.Size(532, 223);
            this.tcControlPanel.TabIndex = 0;
            // 
            // tpMain
            // 
            this.tpMain.Controls.Add(this.btnUninstall);
            this.tpMain.Controls.Add(this.btnInstall);
            this.tpMain.Controls.Add(this.label24);
            this.tpMain.Controls.Add(this.btnRetryBads);
            this.tpMain.Controls.Add(this.btnTest);
            this.tpMain.Controls.Add(this.nMaxThreads);
            this.tpMain.Controls.Add(this.label9);
            this.tpMain.Controls.Add(this.btnStop);
            this.tpMain.Controls.Add(this.btnStart);
            this.tpMain.Controls.Add(this.lServiceStatus);
            this.tpMain.Controls.Add(this.label7);
            this.tpMain.Controls.Add(this.lBadFiles);
            this.tpMain.Controls.Add(this.tbBadFolder);
            this.tpMain.Controls.Add(this.label6);
            this.tpMain.Controls.Add(this.lPickupFiles);
            this.tpMain.Controls.Add(this.tbPickupFolder);
            this.tpMain.Controls.Add(this.label3);
            this.tpMain.Controls.Add(this.btnBrowse);
            this.tpMain.Controls.Add(this.tbQueueRoot);
            this.tpMain.Controls.Add(this.label2);
            this.tpMain.Controls.Add(this.tbLicenseKey);
            this.tpMain.Controls.Add(this.label1);
            this.tpMain.Location = new System.Drawing.Point(4, 22);
            this.tpMain.Name = "tpMain";
            this.tpMain.Padding = new System.Windows.Forms.Padding(3);
            this.tpMain.Size = new System.Drawing.Size(524, 197);
            this.tpMain.TabIndex = 0;
            this.tpMain.Text = "Main";
            this.tpMain.UseVisualStyleBackColor = true;
            // 
            // btnUninstall
            // 
            this.btnUninstall.Location = new System.Drawing.Point(229, 164);
            this.btnUninstall.Name = "btnUninstall";
            this.btnUninstall.Size = new System.Drawing.Size(65, 24);
            this.btnUninstall.TabIndex = 31;
            this.btnUninstall.Text = "Uninstall";
            this.btnUninstall.UseVisualStyleBackColor = true;
            this.btnUninstall.Click += new System.EventHandler(this.btnUninstall_Click);
            // 
            // btnInstall
            // 
            this.btnInstall.Location = new System.Drawing.Point(13, 164);
            this.btnInstall.Name = "btnInstall";
            this.btnInstall.Size = new System.Drawing.Size(69, 24);
            this.btnInstall.TabIndex = 30;
            this.btnInstall.Text = "Install";
            this.btnInstall.UseVisualStyleBackColor = true;
            this.btnInstall.Click += new System.EventHandler(this.btnInstall_Click);
            // 
            // label24
            // 
            this.label24.AutoSize = true;
            this.label24.Location = new System.Drawing.Point(155, 121);
            this.label24.Name = "label24";
            this.label24.Size = new System.Drawing.Size(46, 13);
            this.label24.TabIndex = 29;
            this.label24.Text = "-1=unlim";
            // 
            // btnRetryBads
            // 
            this.btnRetryBads.Location = new System.Drawing.Point(443, 122);
            this.btnRetryBads.Name = "btnRetryBads";
            this.btnRetryBads.Size = new System.Drawing.Size(75, 21);
            this.btnRetryBads.TabIndex = 7;
            this.btnRetryBads.Text = "Retry Bads";
            this.btnRetryBads.UseVisualStyleBackColor = true;
            this.btnRetryBads.Click += new System.EventHandler(this.btnRetryBads_Click);
            // 
            // btnTest
            // 
            this.btnTest.Location = new System.Drawing.Point(443, 12);
            this.btnTest.Name = "btnTest";
            this.btnTest.Size = new System.Drawing.Size(75, 20);
            this.btnTest.TabIndex = 1;
            this.btnTest.Text = "Test";
            this.btnTest.UseVisualStyleBackColor = true;
            this.btnTest.Click += new System.EventHandler(this.btnTest_Click);
            // 
            // nMaxThreads
            // 
            this.nMaxThreads.Location = new System.Drawing.Point(88, 119);
            this.nMaxThreads.Maximum = new decimal(new int[] {
            60,
            0,
            0,
            0});
            this.nMaxThreads.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            -2147483648});
            this.nMaxThreads.Name = "nMaxThreads";
            this.nMaxThreads.Size = new System.Drawing.Size(65, 20);
            this.nMaxThreads.TabIndex = 6;
            this.nMaxThreads.Value = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.nMaxThreads.ValueChanged += new System.EventHandler(this.nMaxThreads_ValueChanged);
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(10, 121);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(69, 13);
            this.label9.TabIndex = 20;
            this.label9.Text = "Max Threads";
            // 
            // btnStop
            // 
            this.btnStop.Location = new System.Drawing.Point(158, 164);
            this.btnStop.Name = "btnStop";
            this.btnStop.Size = new System.Drawing.Size(65, 24);
            this.btnStop.TabIndex = 9;
            this.btnStop.Text = "Stop";
            this.btnStop.UseVisualStyleBackColor = true;
            this.btnStop.Click += new System.EventHandler(this.btnStop_Click);
            // 
            // btnStart
            // 
            this.btnStart.Location = new System.Drawing.Point(84, 164);
            this.btnStart.Name = "btnStart";
            this.btnStart.Size = new System.Drawing.Size(69, 24);
            this.btnStart.TabIndex = 8;
            this.btnStart.Text = "Start";
            this.btnStart.UseVisualStyleBackColor = true;
            this.btnStart.Click += new System.EventHandler(this.btnStart_Click);
            // 
            // lServiceStatus
            // 
            this.lServiceStatus.AutoSize = true;
            this.lServiceStatus.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(204)));
            this.lServiceStatus.Location = new System.Drawing.Point(85, 145);
            this.lServiceStatus.Name = "lServiceStatus";
            this.lServiceStatus.Size = new System.Drawing.Size(54, 13);
            this.lServiceStatus.TabIndex = 17;
            this.lServiceStatus.Text = "Stopped";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(10, 145);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(76, 13);
            this.label7.TabIndex = 16;
            this.label7.Text = "Service Status";
            // 
            // lBadFiles
            // 
            this.lBadFiles.AutoSize = true;
            this.lBadFiles.Location = new System.Drawing.Point(446, 96);
            this.lBadFiles.Name = "lBadFiles";
            this.lBadFiles.Size = new System.Drawing.Size(34, 13);
            this.lBadFiles.TabIndex = 15;
            this.lBadFiles.Text = "0 files";
            // 
            // tbBadFolder
            // 
            this.tbBadFolder.Location = new System.Drawing.Point(88, 93);
            this.tbBadFolder.Name = "tbBadFolder";
            this.tbBadFolder.ReadOnly = true;
            this.tbBadFolder.Size = new System.Drawing.Size(349, 20);
            this.tbBadFolder.TabIndex = 5;
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(10, 96);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(58, 13);
            this.label6.TabIndex = 13;
            this.label6.Text = "Bad Folder";
            // 
            // lPickupFiles
            // 
            this.lPickupFiles.AutoSize = true;
            this.lPickupFiles.Location = new System.Drawing.Point(446, 70);
            this.lPickupFiles.Name = "lPickupFiles";
            this.lPickupFiles.Size = new System.Drawing.Size(34, 13);
            this.lPickupFiles.TabIndex = 12;
            this.lPickupFiles.Text = "0 files";
            // 
            // tbPickupFolder
            // 
            this.tbPickupFolder.Location = new System.Drawing.Point(88, 67);
            this.tbPickupFolder.Name = "tbPickupFolder";
            this.tbPickupFolder.ReadOnly = true;
            this.tbPickupFolder.Size = new System.Drawing.Size(349, 20);
            this.tbPickupFolder.TabIndex = 4;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(10, 70);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(72, 13);
            this.label3.TabIndex = 10;
            this.label3.Text = "Pickup Folder";
            // 
            // btnBrowse
            // 
            this.btnBrowse.Location = new System.Drawing.Point(443, 42);
            this.btnBrowse.Name = "btnBrowse";
            this.btnBrowse.Size = new System.Drawing.Size(75, 20);
            this.btnBrowse.TabIndex = 3;
            this.btnBrowse.Text = "Browse";
            this.btnBrowse.UseVisualStyleBackColor = true;
            this.btnBrowse.Click += new System.EventHandler(this.btnBrowse_Click);
            // 
            // tbQueueRoot
            // 
            this.tbQueueRoot.Location = new System.Drawing.Point(88, 41);
            this.tbQueueRoot.Name = "tbQueueRoot";
            this.tbQueueRoot.Size = new System.Drawing.Size(349, 20);
            this.tbQueueRoot.TabIndex = 2;
            this.tbQueueRoot.Text = "C:\\LabMaestroMailQueue";
            this.tbQueueRoot.TextChanged += new System.EventHandler(this.tbQueueRoot_TextChanged);
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(10, 45);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(65, 13);
            this.label2.TabIndex = 7;
            this.label2.Text = "Queue Root";
            // 
            // tbLicenseKey
            // 
            this.tbLicenseKey.Location = new System.Drawing.Point(88, 12);
            this.tbLicenseKey.Name = "tbLicenseKey";
            this.tbLicenseKey.Size = new System.Drawing.Size(349, 20);
            this.tbLicenseKey.TabIndex = 0;
            this.tbLicenseKey.Text = "MN800-2CD32C2AD370D3D5D3775BBAC7DE-4023";
            this.tbLicenseKey.TextChanged += new System.EventHandler(this.tbLicenseKey_TextChanged);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(10, 15);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(65, 13);
            this.label1.TabIndex = 5;
            this.label1.Text = "License Key";
            // 
            // tpSmtp
            // 
            this.tpSmtp.Controls.Add(this.label23);
            this.tpSmtp.Controls.Add(this.label22);
            this.tpSmtp.Controls.Add(this.tbPause);
            this.tpSmtp.Controls.Add(this.tbMaxSendsPerSession);
            this.tpSmtp.Controls.Add(this.tbMaxSimultConnections);
            this.tpSmtp.Controls.Add(this.cbDisableChunking);
            this.tpSmtp.Controls.Add(this.label21);
            this.tpSmtp.Controls.Add(this.label20);
            this.tpSmtp.Controls.Add(this.label19);
            this.tpSmtp.Controls.Add(this.label18);
            this.tpSmtp.Controls.Add(this.label8);
            this.tpSmtp.Controls.Add(this.tbPassword);
            this.tpSmtp.Controls.Add(this.label14);
            this.tpSmtp.Controls.Add(this.tbUserName);
            this.tpSmtp.Controls.Add(this.label15);
            this.tpSmtp.Controls.Add(this.comboAuth);
            this.tpSmtp.Controls.Add(this.label13);
            this.tpSmtp.Controls.Add(this.label12);
            this.tpSmtp.Controls.Add(this.comboSsl);
            this.tpSmtp.Controls.Add(this.tbPort);
            this.tpSmtp.Controls.Add(this.label10);
            this.tpSmtp.Controls.Add(this.tbServerName);
            this.tpSmtp.Controls.Add(this.label11);
            this.tpSmtp.Location = new System.Drawing.Point(4, 22);
            this.tpSmtp.Name = "tpSmtp";
            this.tpSmtp.Padding = new System.Windows.Forms.Padding(3);
            this.tpSmtp.Size = new System.Drawing.Size(524, 197);
            this.tpSmtp.TabIndex = 1;
            this.tpSmtp.Text = "SMTP";
            this.tpSmtp.UseVisualStyleBackColor = true;
            // 
            // label23
            // 
            this.label23.AutoSize = true;
            this.label23.Location = new System.Drawing.Point(475, 44);
            this.label23.Name = "label23";
            this.label23.Size = new System.Drawing.Size(46, 13);
            this.label23.TabIndex = 29;
            this.label23.Text = "-1=unlim";
            // 
            // label22
            // 
            this.label22.AutoSize = true;
            this.label22.Location = new System.Drawing.Point(475, 15);
            this.label22.Name = "label22";
            this.label22.Size = new System.Drawing.Size(46, 13);
            this.label22.TabIndex = 28;
            this.label22.Text = "-1=unlim";
            // 
            // tbPause
            // 
            this.tbPause.Location = new System.Drawing.Point(412, 66);
            this.tbPause.Name = "tbPause";
            this.tbPause.Size = new System.Drawing.Size(61, 20);
            this.tbPause.TabIndex = 8;
            this.tbPause.Text = "5000";
            this.tbPause.TextChanged += new System.EventHandler(this.tbPause_TextChanged);
            // 
            // tbMaxSendsPerSession
            // 
            this.tbMaxSendsPerSession.Location = new System.Drawing.Point(412, 40);
            this.tbMaxSendsPerSession.Name = "tbMaxSendsPerSession";
            this.tbMaxSendsPerSession.Size = new System.Drawing.Size(61, 20);
            this.tbMaxSendsPerSession.TabIndex = 7;
            this.tbMaxSendsPerSession.Text = "2";
            this.tbMaxSendsPerSession.TextChanged += new System.EventHandler(this.tbMaxSendsPerSession_TextChanged);
            // 
            // tbMaxSimultConnections
            // 
            this.tbMaxSimultConnections.Location = new System.Drawing.Point(413, 12);
            this.tbMaxSimultConnections.Name = "tbMaxSimultConnections";
            this.tbMaxSimultConnections.Size = new System.Drawing.Size(61, 20);
            this.tbMaxSimultConnections.TabIndex = 6;
            this.tbMaxSimultConnections.Text = "1";
            this.tbMaxSimultConnections.TextChanged += new System.EventHandler(this.tbMaxSimultConnections_TextChanged);
            // 
            // cbDisableChunking
            // 
            this.cbDisableChunking.AutoSize = true;
            this.cbDisableChunking.Location = new System.Drawing.Point(287, 93);
            this.cbDisableChunking.Name = "cbDisableChunking";
            this.cbDisableChunking.Size = new System.Drawing.Size(212, 17);
            this.cbDisableChunking.TabIndex = 9;
            this.cbDisableChunking.Text = "Disable CHUNKING (for MS Exchange)";
            this.cbDisableChunking.UseVisualStyleBackColor = true;
            this.cbDisableChunking.CheckedChanged += new System.EventHandler(this.cbDisableChunking_CheckedChanged);
            // 
            // label21
            // 
            this.label21.AutoSize = true;
            this.label21.Location = new System.Drawing.Point(475, 70);
            this.label21.Name = "label21";
            this.label21.Size = new System.Drawing.Size(37, 13);
            this.label21.TabIndex = 27;
            this.label21.Text = "msecs";
            // 
            // label20
            // 
            this.label20.AutoSize = true;
            this.label20.Location = new System.Drawing.Point(284, 70);
            this.label20.Name = "label20";
            this.label20.Size = new System.Drawing.Size(127, 13);
            this.label20.TabIndex = 26;
            this.label20.Text = "Pause Between Sessions";
            // 
            // label19
            // 
            this.label19.AutoSize = true;
            this.label19.Location = new System.Drawing.Point(284, 44);
            this.label19.Name = "label19";
            this.label19.Size = new System.Drawing.Size(121, 13);
            this.label19.TabIndex = 24;
            this.label19.Text = "Max E-mails per Session";
            // 
            // label18
            // 
            this.label18.AutoSize = true;
            this.label18.Location = new System.Drawing.Point(284, 15);
            this.label18.Name = "label18";
            this.label18.Size = new System.Drawing.Size(123, 13);
            this.label18.TabIndex = 22;
            this.label18.Text = "Max Simult. Connections";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(10, 176);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(207, 13);
            this.label8.TabIndex = 20;
            this.label8.Text = "IWA is Integrated Windows Authentication";
            // 
            // tbPassword
            // 
            this.tbPassword.Location = new System.Drawing.Point(88, 145);
            this.tbPassword.Name = "tbPassword";
            this.tbPassword.Size = new System.Drawing.Size(170, 20);
            this.tbPassword.TabIndex = 5;
            this.tbPassword.Text = "chevronlab123";
            this.tbPassword.TextChanged += new System.EventHandler(this.tbPassword_TextChanged);
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(10, 150);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(53, 13);
            this.label14.TabIndex = 19;
            this.label14.Text = "Password";
            // 
            // tbUserName
            // 
            this.tbUserName.Location = new System.Drawing.Point(88, 119);
            this.tbUserName.Name = "tbUserName";
            this.tbUserName.Size = new System.Drawing.Size(170, 20);
            this.tbUserName.TabIndex = 4;
            this.tbUserName.Text = "<EMAIL>";
            this.tbUserName.TextChanged += new System.EventHandler(this.tbUserName_TextChanged);
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(10, 122);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(60, 13);
            this.label15.TabIndex = 17;
            this.label15.Text = "User Name";
            // 
            // comboAuth
            // 
            this.comboAuth.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboAuth.FormattingEnabled = true;
            this.comboAuth.Items.AddRange(new object[] {
            "Anonymous",
            "Auto",
            "LOGIN",
            "PLAIN",
            "NTLM",
            "GSSAPI",
            "IWA over NTLM",
            "IWA over GSSAPI"});
            this.comboAuth.Location = new System.Drawing.Point(88, 93);
            this.comboAuth.Name = "comboAuth";
            this.comboAuth.Size = new System.Drawing.Size(170, 21);
            this.comboAuth.TabIndex = 3;
            this.comboAuth.SelectedIndexChanged += new System.EventHandler(this.comboAuth_SelectedIndexChanged);
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(10, 96);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(75, 13);
            this.label13.TabIndex = 15;
            this.label13.Text = "Authentication";
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(10, 69);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(57, 13);
            this.label12.TabIndex = 14;
            this.label12.Text = "SSL Mode";
            // 
            // comboSsl
            // 
            this.comboSsl.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboSsl.FormattingEnabled = true;
            this.comboSsl.Items.AddRange(new object[] {
            "No SSL",
            "Dedicated SSL port",
            "Try StartTLS on regular port",
            "Force StartTLS on regular port"});
            this.comboSsl.Location = new System.Drawing.Point(88, 66);
            this.comboSsl.Name = "comboSsl";
            this.comboSsl.Size = new System.Drawing.Size(170, 21);
            this.comboSsl.TabIndex = 2;
            this.comboSsl.SelectedIndexChanged += new System.EventHandler(this.comboSsl_SelectedIndexChanged);
            // 
            // tbPort
            // 
            this.tbPort.Location = new System.Drawing.Point(88, 40);
            this.tbPort.Name = "tbPort";
            this.tbPort.Size = new System.Drawing.Size(61, 20);
            this.tbPort.TabIndex = 1;
            this.tbPort.Text = "465";
            this.tbPort.TextChanged += new System.EventHandler(this.tbPort_TextChanged);
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(10, 44);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(26, 13);
            this.label10.TabIndex = 11;
            this.label10.Text = "Port";
            // 
            // tbServerName
            // 
            this.tbServerName.Location = new System.Drawing.Point(88, 12);
            this.tbServerName.Name = "tbServerName";
            this.tbServerName.Size = new System.Drawing.Size(170, 20);
            this.tbServerName.TabIndex = 0;
            this.tbServerName.Text = "smtp.gmail.com";
            this.tbServerName.TextChanged += new System.EventHandler(this.tbServerName_TextChanged);
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(10, 15);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(69, 13);
            this.label11.TabIndex = 9;
            this.label11.Text = "Server Name";
            // 
            // tpLogging
            // 
            this.tpLogging.Controls.Add(this.label5);
            this.tpLogging.Controls.Add(this.label4);
            this.tpLogging.Controls.Add(this.btnClearLog);
            this.tpLogging.Controls.Add(this.btnViewLog);
            this.tpLogging.Controls.Add(this.cbEnableLogging);
            this.tpLogging.Controls.Add(this.tbActualLogFilePath);
            this.tpLogging.Controls.Add(this.label17);
            this.tpLogging.Controls.Add(this.tbLogFilename);
            this.tpLogging.Controls.Add(this.label16);
            this.tpLogging.Location = new System.Drawing.Point(4, 22);
            this.tpLogging.Name = "tpLogging";
            this.tpLogging.Size = new System.Drawing.Size(524, 197);
            this.tpLogging.TabIndex = 2;
            this.tpLogging.Text = "Logging";
            this.tpLogging.UseVisualStyleBackColor = true;
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(10, 88);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(323, 13);
            this.label5.TabIndex = 15;
            this.label5.Text = "Actual Path shows how this will be translated to the actual file path.";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(10, 67);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(295, 13);
            this.label4.TabIndex = 14;
            this.label4.Text = "Log filename can be a pattern like C:\\Temp\\{yyyy-MM-dd}.txt";
            // 
            // btnClearLog
            // 
            this.btnClearLog.Location = new System.Drawing.Point(439, 35);
            this.btnClearLog.Name = "btnClearLog";
            this.btnClearLog.Size = new System.Drawing.Size(78, 25);
            this.btnClearLog.TabIndex = 4;
            this.btnClearLog.Text = "Clear Log";
            this.btnClearLog.UseVisualStyleBackColor = true;
            this.btnClearLog.Click += new System.EventHandler(this.btnClearLog_Click);
            // 
            // btnViewLog
            // 
            this.btnViewLog.Location = new System.Drawing.Point(354, 35);
            this.btnViewLog.Name = "btnViewLog";
            this.btnViewLog.Size = new System.Drawing.Size(78, 25);
            this.btnViewLog.TabIndex = 3;
            this.btnViewLog.Text = "View Log";
            this.btnViewLog.UseVisualStyleBackColor = true;
            this.btnViewLog.Click += new System.EventHandler(this.btnViewLog_Click);
            // 
            // cbEnableLogging
            // 
            this.cbEnableLogging.AutoSize = true;
            this.cbEnableLogging.Location = new System.Drawing.Point(354, 15);
            this.cbEnableLogging.Name = "cbEnableLogging";
            this.cbEnableLogging.Size = new System.Drawing.Size(100, 17);
            this.cbEnableLogging.TabIndex = 1;
            this.cbEnableLogging.Text = "Enable Logging";
            this.cbEnableLogging.UseVisualStyleBackColor = true;
            this.cbEnableLogging.CheckedChanged += new System.EventHandler(this.cbEnableLogging_CheckedChanged);
            // 
            // tbActualLogFilePath
            // 
            this.tbActualLogFilePath.Location = new System.Drawing.Point(88, 40);
            this.tbActualLogFilePath.Name = "tbActualLogFilePath";
            this.tbActualLogFilePath.ReadOnly = true;
            this.tbActualLogFilePath.Size = new System.Drawing.Size(248, 20);
            this.tbActualLogFilePath.TabIndex = 2;
            // 
            // label17
            // 
            this.label17.AutoSize = true;
            this.label17.Location = new System.Drawing.Point(10, 43);
            this.label17.Name = "label17";
            this.label17.Size = new System.Drawing.Size(62, 13);
            this.label17.TabIndex = 13;
            this.label17.Text = "Actual Path";
            // 
            // tbLogFilename
            // 
            this.tbLogFilename.Location = new System.Drawing.Point(88, 12);
            this.tbLogFilename.Name = "tbLogFilename";
            this.tbLogFilename.Size = new System.Drawing.Size(248, 20);
            this.tbLogFilename.TabIndex = 0;
            this.tbLogFilename.Text = "{yyyy-MM-dd}.txt";
            this.tbLogFilename.TextChanged += new System.EventHandler(this.tbLogFilename_TextChanged);
            // 
            // label16
            // 
            this.label16.AutoSize = true;
            this.label16.Location = new System.Drawing.Point(10, 15);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(70, 13);
            this.label16.TabIndex = 11;
            this.label16.Text = "Log Filename";
            // 
            // btnSaveSettings
            // 
            this.btnSaveSettings.Location = new System.Drawing.Point(439, 232);
            this.btnSaveSettings.Name = "btnSaveSettings";
            this.btnSaveSettings.Size = new System.Drawing.Size(96, 25);
            this.btnSaveSettings.TabIndex = 0;
            this.btnSaveSettings.Text = "Save Settings";
            this.btnSaveSettings.UseVisualStyleBackColor = true;
            this.btnSaveSettings.Click += new System.EventHandler(this.btnSaveSettings_Click);
            // 
            // timer
            // 
            this.timer.Enabled = true;
            this.timer.Interval = 1000;
            this.timer.Tick += new System.EventHandler(this.timer_Tick);
            // 
            // fswPickup
            // 
            this.fswPickup.EnableRaisingEvents = true;
            this.fswPickup.SynchronizingObject = this;
            this.fswPickup.Created += new System.IO.FileSystemEventHandler(this.fswPickup_Created);
            this.fswPickup.Deleted += new System.IO.FileSystemEventHandler(this.fswPickup_Deleted);
            // 
            // fswBad
            // 
            this.fswBad.EnableRaisingEvents = true;
            this.fswBad.SynchronizingObject = this;
            this.fswBad.Created += new System.IO.FileSystemEventHandler(this.fswBad_Created);
            this.fswBad.Deleted += new System.IO.FileSystemEventHandler(this.fswBad_Deleted);
            // 
            // ControlPanel
            // 
            this.AutoScaleBaseSize = new System.Drawing.Size(5, 13);
            this.ClientSize = new System.Drawing.Size(538, 261);
            this.Controls.Add(this.btnSaveSettings);
            this.Controls.Add(this.tcControlPanel);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "ControlPanel";
            this.Text = "LabMaestro Email Queue Control Panel";
            this.Load += new System.EventHandler(this.ControlPanel_Load);
            this.tcControlPanel.ResumeLayout(false);
            this.tpMain.ResumeLayout(false);
            this.tpMain.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nMaxThreads)).EndInit();
            this.tpSmtp.ResumeLayout(false);
            this.tpSmtp.PerformLayout();
            this.tpLogging.ResumeLayout(false);
            this.tpLogging.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.fswPickup)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.fswBad)).EndInit();
            this.ResumeLayout(false);

    }

    #endregion
}