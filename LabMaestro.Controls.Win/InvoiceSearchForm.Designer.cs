﻿namespace LabMaestro.Controls.Win
{
    partial class InvoiceSearchForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(InvoiceSearchForm));
            DevExpress.XtraEditors.Controls.EditorButtonImageOptions editorButtonImageOptions2 = new DevExpress.XtraEditors.Controls.EditorButtonImageOptions();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject5 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject6 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject7 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject8 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.XtraEditors.Controls.EditorButtonImageOptions editorButtonImageOptions3 = new DevExpress.XtraEditors.Controls.EditorButtonImageOptions();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject9 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject10 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject11 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject12 = new DevExpress.Utils.SerializableAppearanceObject();
            this.searchPanel = new DevExpress.XtraEditors.PanelControl();
            this.panelControl1 = new DevExpress.XtraEditors.PanelControl();
            this.labelControl9 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.lblDetailsAssocLab = new DevExpress.XtraEditors.LabelControl();
            this.lblDetailsCustomer = new DevExpress.XtraEditors.LabelControl();
            this.lblDetailsAffiliate = new DevExpress.XtraEditors.LabelControl();
            this.lblDetailsCorporate = new DevExpress.XtraEditors.LabelControl();
            this.lblDetailsAssocLabTracking = new DevExpress.XtraEditors.LabelControl();
            this.lblCorporate = new DevExpress.XtraEditors.LabelControl();
            this.chkSearchCorporate = new DevExpress.XtraEditors.CheckEdit();
            this.barManager = new DevExpress.XtraBars.BarManager(this.components);
            this.toolbar = new DevExpress.XtraBars.Bar();
            this.btnPrintInvoice = new DevExpress.XtraBars.BarButtonItem();
            this.btnPrintReqFoil = new DevExpress.XtraBars.BarButtonItem();
            this.btnFacesheet = new DevExpress.XtraBars.BarButtonItem();
            this.btnAuditTrail = new DevExpress.XtraBars.BarButtonItem();
            this.btnTxHistory = new DevExpress.XtraBars.BarButtonItem();
            this.btnCopyDemographic = new DevExpress.XtraBars.BarButtonItem();
            this.btnPatientDemographicsEdit = new DevExpress.XtraBars.BarButtonItem();
            this.btnEditLabOrder = new DevExpress.XtraBars.BarButtonItem();
            this.btnDiscount = new DevExpress.XtraBars.BarButtonItem();
            this.btnDiscountRebate = new DevExpress.XtraBars.BarButtonItem();
            this.btnRefund = new DevExpress.XtraBars.BarButtonItem();
            this.btnClose = new DevExpress.XtraBars.BarButtonItem();
            this.barAndDockingController = new DevExpress.XtraBars.BarAndDockingController(this.components);
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.barButtonItem1 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem2 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem3 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem4 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem5 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem6 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem7 = new DevExpress.XtraBars.BarButtonItem();
            this.btnDropDown = new DevExpress.XtraEditors.DropDownButton();
            this.popupMenu = new DevExpress.XtraBars.PopupMenu(this.components);
            this.lblAssocLab = new DevExpress.XtraEditors.LabelControl();
            this.chkSearchAssocLab = new DevExpress.XtraEditors.CheckEdit();
            this.btnCorporate = new DevExpress.XtraEditors.SimpleButton();
            this.btnAssocLab = new DevExpress.XtraEditors.SimpleButton();
            this.btnCopyDate = new DevExpress.XtraEditors.SimpleButton();
            this.btnResetCache = new DevExpress.XtraEditors.SimpleButton();
            this.dtTimeTo = new DevExpress.XtraEditors.TimeEdit();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.chkApplyTimeFilter = new DevExpress.XtraEditors.CheckEdit();
            this.lblReferrer = new DevExpress.XtraEditors.LabelControl();
            this.btnSelectReferrer = new DevExpress.XtraEditors.SimpleButton();
            this.chkSearchReferrer = new DevExpress.XtraEditors.CheckEdit();
            this.chkSearchBundleStage = new DevExpress.XtraEditors.CheckEdit();
            this.chkSearchOrderId = new DevExpress.XtraEditors.CheckEdit();
            this.chkSearchInvoiceId = new DevExpress.XtraEditors.CheckEdit();
            this.chkSearchPatientName = new DevExpress.XtraEditors.CheckEdit();
            this.txtPatientName = new DevExpress.XtraEditors.TextEdit();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.btnClear = new DevExpress.XtraEditors.SimpleButton();
            this.btnSearch = new DevExpress.XtraEditors.SimpleButton();
            this.cboWorkflowStatus = new DevExpress.XtraEditors.ComboBoxEdit();
            this.dtTo = new DevExpress.XtraEditors.DateEdit();
            this.dtFrom = new DevExpress.XtraEditors.DateEdit();
            this.txtOrderNum = new DevExpress.XtraEditors.TextEdit();
            this.txtInvoiceId = new DevExpress.XtraEditors.TextEdit();
            this.dtTimeFrom = new DevExpress.XtraEditors.TimeEdit();
            this.splitContainerControl = new DevExpress.XtraEditors.SplitContainerControl();
            this.grdInvoices = new DevExpress.XtraGrid.GridControl();
            this.labOrderSearchResultSliceBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.gvwInvoices = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colInvoiceId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colOrderId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colOrderDateTime = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colIsCancelled = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colPatientName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colSex = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colWorkflowStage = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colReferrerName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colNetPayable = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colDiscountAmount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colPaidAmount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colDueAmount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colRefUnknown = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colRefDisallow = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colExternalSubOrder = new DevExpress.XtraGrid.Columns.GridColumn();
            this.grdResultBundles = new DevExpress.XtraGrid.GridControl();
            this.activeResultBundleSliceBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.gvwResultBundles = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colDisplayTitle = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colComponentLabTests = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colWorkflowStageB = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colStaffName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colLastUpdated = new DevExpress.XtraGrid.Columns.GridColumn();
            this.saveFileDialog = new System.Windows.Forms.SaveFileDialog();
            this.chkSearchPhone = new DevExpress.XtraEditors.CheckEdit();
            this.txtPhoneNumber = new DevExpress.XtraEditors.TextEdit();
            this.chkSearchCustomer = new DevExpress.XtraEditors.CheckEdit();
            this.txtCustomerUpin = new DevExpress.XtraEditors.TextEdit();
            ((System.ComponentModel.ISupportInitialize)(this.searchPanel)).BeginInit();
            this.searchPanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).BeginInit();
            this.panelControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkSearchCorporate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.popupMenu)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSearchAssocLab.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtTimeTo.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkApplyTimeFilter.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSearchReferrer.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSearchBundleStage.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSearchOrderId.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSearchInvoiceId.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSearchPatientName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPatientName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cboWorkflowStatus.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtTo.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtTo.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtFrom.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtFrom.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtOrderNum.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtInvoiceId.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtTimeFrom.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl.Panel1)).BeginInit();
            this.splitContainerControl.Panel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl.Panel2)).BeginInit();
            this.splitContainerControl.Panel2.SuspendLayout();
            this.splitContainerControl.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grdInvoices)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.labOrderSearchResultSliceBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvwInvoices)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdResultBundles)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.activeResultBundleSliceBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvwResultBundles)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSearchPhone.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPhoneNumber.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSearchCustomer.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCustomerUpin.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // searchPanel
            // 
            this.searchPanel.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.searchPanel.Controls.Add(this.chkSearchCustomer);
            this.searchPanel.Controls.Add(this.txtCustomerUpin);
            this.searchPanel.Controls.Add(this.chkSearchPhone);
            this.searchPanel.Controls.Add(this.txtPhoneNumber);
            this.searchPanel.Controls.Add(this.panelControl1);
            this.searchPanel.Controls.Add(this.lblCorporate);
            this.searchPanel.Controls.Add(this.chkSearchCorporate);
            this.searchPanel.Controls.Add(this.lblAssocLab);
            this.searchPanel.Controls.Add(this.chkSearchAssocLab);
            this.searchPanel.Controls.Add(this.btnCorporate);
            this.searchPanel.Controls.Add(this.btnAssocLab);
            this.searchPanel.Controls.Add(this.btnCopyDate);
            this.searchPanel.Controls.Add(this.btnDropDown);
            this.searchPanel.Controls.Add(this.btnResetCache);
            this.searchPanel.Controls.Add(this.dtTimeTo);
            this.searchPanel.Controls.Add(this.labelControl2);
            this.searchPanel.Controls.Add(this.labelControl1);
            this.searchPanel.Controls.Add(this.chkApplyTimeFilter);
            this.searchPanel.Controls.Add(this.lblReferrer);
            this.searchPanel.Controls.Add(this.btnSelectReferrer);
            this.searchPanel.Controls.Add(this.chkSearchReferrer);
            this.searchPanel.Controls.Add(this.chkSearchBundleStage);
            this.searchPanel.Controls.Add(this.chkSearchOrderId);
            this.searchPanel.Controls.Add(this.chkSearchInvoiceId);
            this.searchPanel.Controls.Add(this.chkSearchPatientName);
            this.searchPanel.Controls.Add(this.txtPatientName);
            this.searchPanel.Controls.Add(this.labelControl4);
            this.searchPanel.Controls.Add(this.labelControl3);
            this.searchPanel.Controls.Add(this.btnClear);
            this.searchPanel.Controls.Add(this.btnSearch);
            this.searchPanel.Controls.Add(this.cboWorkflowStatus);
            this.searchPanel.Controls.Add(this.dtTo);
            this.searchPanel.Controls.Add(this.dtFrom);
            this.searchPanel.Controls.Add(this.txtOrderNum);
            this.searchPanel.Controls.Add(this.txtInvoiceId);
            this.searchPanel.Controls.Add(this.dtTimeFrom);
            this.searchPanel.Dock = System.Windows.Forms.DockStyle.Top;
            this.searchPanel.Location = new System.Drawing.Point(0, 29);
            this.searchPanel.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.searchPanel.Name = "searchPanel";
            this.searchPanel.Size = new System.Drawing.Size(1387, 207);
            this.searchPanel.TabIndex = 0;
            // 
            // panelControl1
            // 
            this.panelControl1.Appearance.BackColor = System.Drawing.Color.Snow;
            this.panelControl1.Appearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.panelControl1.Appearance.Options.UseBackColor = true;
            this.panelControl1.Appearance.Options.UseBorderColor = true;
            this.panelControl1.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.panelControl1.Controls.Add(this.labelControl9);
            this.panelControl1.Controls.Add(this.labelControl8);
            this.panelControl1.Controls.Add(this.labelControl7);
            this.panelControl1.Controls.Add(this.labelControl6);
            this.panelControl1.Controls.Add(this.labelControl5);
            this.panelControl1.Controls.Add(this.lblDetailsAssocLab);
            this.panelControl1.Controls.Add(this.lblDetailsCustomer);
            this.panelControl1.Controls.Add(this.lblDetailsAffiliate);
            this.panelControl1.Controls.Add(this.lblDetailsCorporate);
            this.panelControl1.Controls.Add(this.lblDetailsAssocLabTracking);
            this.panelControl1.Location = new System.Drawing.Point(1050, 54);
            this.panelControl1.Name = "panelControl1";
            this.panelControl1.Size = new System.Drawing.Size(324, 110);
            this.panelControl1.TabIndex = 52;
            // 
            // labelControl9
            // 
            this.labelControl9.Appearance.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl9.Appearance.ForeColor = System.Drawing.Color.Gray;
            this.labelControl9.Appearance.Options.UseFont = true;
            this.labelControl9.Appearance.Options.UseForeColor = true;
            this.labelControl9.AutoEllipsis = true;
            this.labelControl9.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None;
            this.labelControl9.Location = new System.Drawing.Point(3, 89);
            this.labelControl9.Name = "labelControl9";
            this.labelControl9.Size = new System.Drawing.Size(65, 16);
            this.labelControl9.TabIndex = 61;
            this.labelControl9.Text = "PHN";
            // 
            // labelControl8
            // 
            this.labelControl8.Appearance.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl8.Appearance.ForeColor = System.Drawing.Color.Gray;
            this.labelControl8.Appearance.Options.UseFont = true;
            this.labelControl8.Appearance.Options.UseForeColor = true;
            this.labelControl8.AutoEllipsis = true;
            this.labelControl8.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None;
            this.labelControl8.Location = new System.Drawing.Point(3, 67);
            this.labelControl8.Name = "labelControl8";
            this.labelControl8.Size = new System.Drawing.Size(65, 16);
            this.labelControl8.TabIndex = 60;
            this.labelControl8.Text = "Affiliate";
            // 
            // labelControl7
            // 
            this.labelControl7.Appearance.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl7.Appearance.ForeColor = System.Drawing.Color.Gray;
            this.labelControl7.Appearance.Options.UseFont = true;
            this.labelControl7.Appearance.Options.UseForeColor = true;
            this.labelControl7.AutoEllipsis = true;
            this.labelControl7.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None;
            this.labelControl7.Location = new System.Drawing.Point(3, 46);
            this.labelControl7.Name = "labelControl7";
            this.labelControl7.Size = new System.Drawing.Size(65, 16);
            this.labelControl7.TabIndex = 59;
            this.labelControl7.Text = "Corporate";
            // 
            // labelControl6
            // 
            this.labelControl6.Appearance.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl6.Appearance.ForeColor = System.Drawing.Color.Gray;
            this.labelControl6.Appearance.Options.UseFont = true;
            this.labelControl6.Appearance.Options.UseForeColor = true;
            this.labelControl6.AutoEllipsis = true;
            this.labelControl6.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None;
            this.labelControl6.Location = new System.Drawing.Point(3, 25);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(65, 16);
            this.labelControl6.TabIndex = 58;
            this.labelControl6.Text = "Tracking ID";
            // 
            // labelControl5
            // 
            this.labelControl5.Appearance.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl5.Appearance.ForeColor = System.Drawing.Color.Gray;
            this.labelControl5.Appearance.Options.UseFont = true;
            this.labelControl5.Appearance.Options.UseForeColor = true;
            this.labelControl5.AutoEllipsis = true;
            this.labelControl5.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None;
            this.labelControl5.Location = new System.Drawing.Point(3, 3);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(65, 16);
            this.labelControl5.TabIndex = 57;
            this.labelControl5.Text = "Assoc. Lab";
            // 
            // lblDetailsAssocLab
            // 
            this.lblDetailsAssocLab.Appearance.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblDetailsAssocLab.Appearance.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblDetailsAssocLab.Appearance.Options.UseFont = true;
            this.lblDetailsAssocLab.Appearance.Options.UseForeColor = true;
            this.lblDetailsAssocLab.AutoEllipsis = true;
            this.lblDetailsAssocLab.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None;
            this.lblDetailsAssocLab.Location = new System.Drawing.Point(74, 25);
            this.lblDetailsAssocLab.Name = "lblDetailsAssocLab";
            this.lblDetailsAssocLab.Size = new System.Drawing.Size(247, 16);
            this.lblDetailsAssocLab.TabIndex = 56;
            this.lblDetailsAssocLab.Text = "Referrer:";
            // 
            // lblDetailsCustomer
            // 
            this.lblDetailsCustomer.Appearance.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblDetailsCustomer.Appearance.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblDetailsCustomer.Appearance.Options.UseFont = true;
            this.lblDetailsCustomer.Appearance.Options.UseForeColor = true;
            this.lblDetailsCustomer.AutoEllipsis = true;
            this.lblDetailsCustomer.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None;
            this.lblDetailsCustomer.Location = new System.Drawing.Point(74, 89);
            this.lblDetailsCustomer.Name = "lblDetailsCustomer";
            this.lblDetailsCustomer.Size = new System.Drawing.Size(247, 16);
            this.lblDetailsCustomer.TabIndex = 55;
            this.lblDetailsCustomer.Text = "Referrer:";
            // 
            // lblDetailsAffiliate
            // 
            this.lblDetailsAffiliate.Appearance.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblDetailsAffiliate.Appearance.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblDetailsAffiliate.Appearance.Options.UseFont = true;
            this.lblDetailsAffiliate.Appearance.Options.UseForeColor = true;
            this.lblDetailsAffiliate.AutoEllipsis = true;
            this.lblDetailsAffiliate.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None;
            this.lblDetailsAffiliate.Location = new System.Drawing.Point(74, 67);
            this.lblDetailsAffiliate.Name = "lblDetailsAffiliate";
            this.lblDetailsAffiliate.Size = new System.Drawing.Size(247, 16);
            this.lblDetailsAffiliate.TabIndex = 54;
            this.lblDetailsAffiliate.Text = "Referrer:";
            // 
            // lblDetailsCorporate
            // 
            this.lblDetailsCorporate.Appearance.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblDetailsCorporate.Appearance.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblDetailsCorporate.Appearance.Options.UseFont = true;
            this.lblDetailsCorporate.Appearance.Options.UseForeColor = true;
            this.lblDetailsCorporate.AutoEllipsis = true;
            this.lblDetailsCorporate.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None;
            this.lblDetailsCorporate.Location = new System.Drawing.Point(74, 47);
            this.lblDetailsCorporate.Name = "lblDetailsCorporate";
            this.lblDetailsCorporate.Size = new System.Drawing.Size(247, 16);
            this.lblDetailsCorporate.TabIndex = 53;
            this.lblDetailsCorporate.Text = "Referrer:";
            // 
            // lblDetailsAssocLabTracking
            // 
            this.lblDetailsAssocLabTracking.Appearance.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblDetailsAssocLabTracking.Appearance.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblDetailsAssocLabTracking.Appearance.Options.UseFont = true;
            this.lblDetailsAssocLabTracking.Appearance.Options.UseForeColor = true;
            this.lblDetailsAssocLabTracking.AutoEllipsis = true;
            this.lblDetailsAssocLabTracking.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None;
            this.lblDetailsAssocLabTracking.Location = new System.Drawing.Point(74, 3);
            this.lblDetailsAssocLabTracking.Name = "lblDetailsAssocLabTracking";
            this.lblDetailsAssocLabTracking.Size = new System.Drawing.Size(247, 16);
            this.lblDetailsAssocLabTracking.TabIndex = 52;
            this.lblDetailsAssocLabTracking.Text = "Referrer:";
            // 
            // lblCorporate
            // 
            this.lblCorporate.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblCorporate.Appearance.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(64)))));
            this.lblCorporate.Appearance.Options.UseFont = true;
            this.lblCorporate.Appearance.Options.UseForeColor = true;
            this.lblCorporate.AutoEllipsis = true;
            this.lblCorporate.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None;
            this.lblCorporate.Location = new System.Drawing.Point(523, 119);
            this.lblCorporate.Name = "lblCorporate";
            this.lblCorporate.Size = new System.Drawing.Size(277, 16);
            this.lblCorporate.TabIndex = 50;
            this.lblCorporate.Text = "Dr. XXX";
            // 
            // chkSearchCorporate
            // 
            this.chkSearchCorporate.Location = new System.Drawing.Point(366, 117);
            this.chkSearchCorporate.MenuManager = this.barManager;
            this.chkSearchCorporate.Name = "chkSearchCorporate";
            this.chkSearchCorporate.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkSearchCorporate.Properties.Appearance.Options.UseFont = true;
            this.chkSearchCorporate.Properties.Caption = "Corporate Client:";
            this.chkSearchCorporate.Size = new System.Drawing.Size(127, 20);
            this.chkSearchCorporate.TabIndex = 49;
            this.chkSearchCorporate.Tag = "107";
            this.chkSearchCorporate.CheckedChanged += new System.EventHandler(this.checkedChangeHandler);
            // 
            // barManager
            // 
            this.barManager.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.toolbar});
            this.barManager.Controller = this.barAndDockingController;
            this.barManager.DockControls.Add(this.barDockControlTop);
            this.barManager.DockControls.Add(this.barDockControlBottom);
            this.barManager.DockControls.Add(this.barDockControlLeft);
            this.barManager.DockControls.Add(this.barDockControlRight);
            this.barManager.Form = this;
            this.barManager.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.btnPrintInvoice,
            this.btnPrintReqFoil,
            this.btnPatientDemographicsEdit,
            this.btnEditLabOrder,
            this.btnDiscount,
            this.btnDiscountRebate,
            this.btnRefund,
            this.btnFacesheet,
            this.btnAuditTrail,
            this.btnTxHistory,
            this.btnClose,
            this.btnCopyDemographic,
            this.barButtonItem1,
            this.barButtonItem2,
            this.barButtonItem3,
            this.barButtonItem4,
            this.barButtonItem5,
            this.barButtonItem6,
            this.barButtonItem7});
            this.barManager.MaxItemId = 21;
            // 
            // toolbar
            // 
            this.toolbar.BarName = "Tools";
            this.toolbar.CanDockStyle = DevExpress.XtraBars.BarCanDockStyle.Top;
            this.toolbar.DockCol = 0;
            this.toolbar.DockRow = 0;
            this.toolbar.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.toolbar.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.btnPrintInvoice, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.btnPrintReqFoil, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.btnFacesheet, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(this.btnAuditTrail, true),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.btnTxHistory, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(((DevExpress.XtraBars.BarLinkUserDefines)((DevExpress.XtraBars.BarLinkUserDefines.PaintStyle | DevExpress.XtraBars.BarLinkUserDefines.KeyTip))), this.btnCopyDemographic, "", true, true, true, 0, null, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph, "", ""),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.btnPatientDemographicsEdit, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.btnEditLabOrder, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.btnDiscount, "", true, true, true, 0, null, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.btnDiscountRebate, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.btnRefund, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.btnClose, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph)});
            this.toolbar.OptionsBar.AllowQuickCustomization = false;
            this.toolbar.OptionsBar.DrawDragBorder = false;
            this.toolbar.OptionsBar.UseWholeRow = true;
            this.toolbar.Text = "Tools";
            // 
            // btnPrintInvoice
            // 
            this.btnPrintInvoice.Caption = "Invoice";
            this.btnPrintInvoice.Hint = "Print Invoice Slip";
            this.btnPrintInvoice.Id = 0;
            this.btnPrintInvoice.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.print2;
            this.btnPrintInvoice.Name = "btnPrintInvoice";
            this.btnPrintInvoice.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnPrintInvoice_ItemClick);
            // 
            // btnPrintReqFoil
            // 
            this.btnPrintReqFoil.Caption = "Req. Slip";
            this.btnPrintReqFoil.Hint = "Print Requisition Slip";
            this.btnPrintReqFoil.Id = 1;
            this.btnPrintReqFoil.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.print_b_16;
            this.btnPrintReqFoil.Name = "btnPrintReqFoil";
            this.btnPrintReqFoil.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnPrintReqFoil_ItemClick);
            // 
            // btnFacesheet
            // 
            this.btnFacesheet.Caption = "Facesheet";
            this.btnFacesheet.Hint = "Print Facesheet";
            this.btnFacesheet.Id = 9;
            this.btnFacesheet.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.facesheet_16;
            this.btnFacesheet.Name = "btnFacesheet";
            this.btnFacesheet.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnFacesheet_ItemClick);
            // 
            // btnAuditTrail
            // 
            this.btnAuditTrail.Caption = "Audit Trail";
            this.btnAuditTrail.Hint = "View Audit Trail of Selected Lab Order";
            this.btnAuditTrail.Id = 10;
            this.btnAuditTrail.ImageOptions.Image = global::LabMaestro.Controls.Win.ResourceIcons.list_information_16;
            this.btnAuditTrail.Name = "btnAuditTrail";
            this.btnAuditTrail.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.btnAuditTrail.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnAuditTrail_ItemClick);
            // 
            // btnTxHistory
            // 
            this.btnTxHistory.Caption = "Transactions";
            this.btnTxHistory.Hint = "View Transactions History of Selected  Lab Order";
            this.btnTxHistory.Id = 11;
            this.btnTxHistory.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.money_coin16;
            this.btnTxHistory.Name = "btnTxHistory";
            this.btnTxHistory.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnTxHistory_ItemClick);
            // 
            // btnCopyDemographic
            // 
            this.btnCopyDemographic.Caption = "Copy";
            this.btnCopyDemographic.Hint = "Copy Demographic Information of Selected Lab Order";
            this.btnCopyDemographic.Id = 13;
            this.btnCopyDemographic.ImageOptions.Image = global::LabMaestro.Controls.Win.ResourceIcons.demo_paste_16;
            this.btnCopyDemographic.Name = "btnCopyDemographic";
            this.btnCopyDemographic.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnCopyDemographic_ItemClick);
            // 
            // btnPatientDemographicsEdit
            // 
            this.btnPatientDemographicsEdit.Caption = "Demographic";
            this.btnPatientDemographicsEdit.Hint = "Edit Lab Order Demographic";
            this.btnPatientDemographicsEdit.Id = 2;
            this.btnPatientDemographicsEdit.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.group_edit_16;
            this.btnPatientDemographicsEdit.Name = "btnPatientDemographicsEdit";
            this.btnPatientDemographicsEdit.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnPatientDemographicsEdit_ItemClick);
            // 
            // btnEditLabOrder
            // 
            this.btnEditLabOrder.Caption = "Edit Order";
            this.btnEditLabOrder.Hint = "Edit Selected Lab Order";
            this.btnEditLabOrder.Id = 3;
            this.btnEditLabOrder.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.list_edit_16;
            this.btnEditLabOrder.Name = "btnEditLabOrder";
            this.btnEditLabOrder.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnEditLabOrder_ItemClick);
            // 
            // btnDiscount
            // 
            this.btnDiscount.Caption = "Cash Discount";
            this.btnDiscount.Hint = "Issue Cash Discount";
            this.btnDiscount.Id = 6;
            this.btnDiscount.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.money_delete16;
            this.btnDiscount.Name = "btnDiscount";
            this.btnDiscount.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnDiscount_ItemClick);
            // 
            // btnDiscountRebate
            // 
            this.btnDiscountRebate.Caption = "Discount Rebate";
            this.btnDiscountRebate.Hint = "Issue Discount Rebate";
            this.btnDiscountRebate.Id = 7;
            this.btnDiscountRebate.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.money_coin16;
            this.btnDiscountRebate.Name = "btnDiscountRebate";
            this.btnDiscountRebate.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnDiscountRebate_ItemClick);
            // 
            // btnRefund
            // 
            this.btnRefund.Caption = "Refund";
            this.btnRefund.Hint = "Issue Refund";
            this.btnRefund.Id = 8;
            this.btnRefund.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.coins_in_hand16;
            this.btnRefund.Name = "btnRefund";
            this.btnRefund.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnRefund_ItemClick);
            // 
            // btnClose
            // 
            this.btnClose.Caption = "Close";
            this.btnClose.Hint = "Close";
            this.btnClose.Id = 12;
            this.btnClose.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.exit_16;
            this.btnClose.Name = "btnClose";
            this.btnClose.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnClose_ItemClick);
            // 
            // barAndDockingController
            // 
            this.barAndDockingController.LookAndFeel.SkinName = "Office 2010 Blue";
            this.barAndDockingController.LookAndFeel.UseDefaultLookAndFeel = false;
            this.barAndDockingController.PropertiesBar.AllowLinkLighting = false;
            // 
            // barDockControlTop
            // 
            this.barDockControlTop.CausesValidation = false;
            this.barDockControlTop.Dock = System.Windows.Forms.DockStyle.Top;
            this.barDockControlTop.Location = new System.Drawing.Point(0, 0);
            this.barDockControlTop.Manager = this.barManager;
            this.barDockControlTop.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.barDockControlTop.Size = new System.Drawing.Size(1387, 29);
            // 
            // barDockControlBottom
            // 
            this.barDockControlBottom.CausesValidation = false;
            this.barDockControlBottom.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.barDockControlBottom.Location = new System.Drawing.Point(0, 661);
            this.barDockControlBottom.Manager = this.barManager;
            this.barDockControlBottom.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.barDockControlBottom.Size = new System.Drawing.Size(1387, 0);
            // 
            // barDockControlLeft
            // 
            this.barDockControlLeft.CausesValidation = false;
            this.barDockControlLeft.Dock = System.Windows.Forms.DockStyle.Left;
            this.barDockControlLeft.Location = new System.Drawing.Point(0, 29);
            this.barDockControlLeft.Manager = this.barManager;
            this.barDockControlLeft.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.barDockControlLeft.Size = new System.Drawing.Size(0, 632);
            // 
            // barDockControlRight
            // 
            this.barDockControlRight.CausesValidation = false;
            this.barDockControlRight.Dock = System.Windows.Forms.DockStyle.Right;
            this.barDockControlRight.Location = new System.Drawing.Point(1387, 29);
            this.barDockControlRight.Manager = this.barManager;
            this.barDockControlRight.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.barDockControlRight.Size = new System.Drawing.Size(0, 632);
            // 
            // barButtonItem1
            // 
            this.barButtonItem1.Caption = "Today";
            this.barButtonItem1.Id = 14;
            this.barButtonItem1.Name = "barButtonItem1";
            this.barButtonItem1.Tag = ((short)(1));
            this.barButtonItem1.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.popMenuItemClick);
            // 
            // barButtonItem2
            // 
            this.barButtonItem2.Caption = "Yesterday";
            this.barButtonItem2.Id = 15;
            this.barButtonItem2.Name = "barButtonItem2";
            this.barButtonItem2.Tag = ((short)(2));
            this.barButtonItem2.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.popMenuItemClick);
            // 
            // barButtonItem3
            // 
            this.barButtonItem3.Caption = "Last 3 days";
            this.barButtonItem3.Id = 16;
            this.barButtonItem3.Name = "barButtonItem3";
            this.barButtonItem3.Tag = ((short)(3));
            this.barButtonItem3.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.popMenuItemClick);
            // 
            // barButtonItem4
            // 
            this.barButtonItem4.Caption = "Last 1 week";
            this.barButtonItem4.Id = 17;
            this.barButtonItem4.Name = "barButtonItem4";
            this.barButtonItem4.Tag = ((short)(5));
            this.barButtonItem4.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.popMenuItemClick);
            // 
            // barButtonItem5
            // 
            this.barButtonItem5.Caption = "This week";
            this.barButtonItem5.Id = 18;
            this.barButtonItem5.Name = "barButtonItem5";
            this.barButtonItem5.Tag = ((short)(4));
            this.barButtonItem5.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.popMenuItemClick);
            // 
            // barButtonItem6
            // 
            this.barButtonItem6.Caption = "This month";
            this.barButtonItem6.Id = 19;
            this.barButtonItem6.Name = "barButtonItem6";
            this.barButtonItem6.Tag = ((short)(6));
            this.barButtonItem6.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.popMenuItemClick);
            // 
            // barButtonItem7
            // 
            this.barButtonItem7.Caption = "Last 1 month";
            this.barButtonItem7.Id = 20;
            this.barButtonItem7.Name = "barButtonItem7";
            this.barButtonItem7.Tag = ((short)(7));
            this.barButtonItem7.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.popMenuItemClick);
            // 
            // btnDropDown
            // 
            this.btnDropDown.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnDropDown.Appearance.Options.UseFont = true;
            this.btnDropDown.DropDownArrowStyle = DevExpress.XtraEditors.DropDownArrowStyle.SplitButton;
            this.btnDropDown.DropDownControl = this.popupMenu;
            this.btnDropDown.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.calendar_export_16;
            this.btnDropDown.Location = new System.Drawing.Point(293, 64);
            this.btnDropDown.MenuManager = this.barManager;
            this.btnDropDown.Name = "btnDropDown";
            this.barManager.SetPopupContextMenu(this.btnDropDown, this.popupMenu);
            this.btnDropDown.Size = new System.Drawing.Size(41, 23);
            this.btnDropDown.TabIndex = 43;
            this.btnDropDown.TabStop = false;
            this.btnDropDown.ToolTip = "Quick Date-range";
            // 
            // popupMenu
            // 
            this.popupMenu.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItem1),
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItem2),
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItem3),
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItem5),
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItem4),
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItem6),
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItem7)});
            this.popupMenu.Manager = this.barManager;
            this.popupMenu.Name = "popupMenu";
            // 
            // lblAssocLab
            // 
            this.lblAssocLab.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblAssocLab.Appearance.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(64)))));
            this.lblAssocLab.Appearance.Options.UseFont = true;
            this.lblAssocLab.Appearance.Options.UseForeColor = true;
            this.lblAssocLab.AutoEllipsis = true;
            this.lblAssocLab.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None;
            this.lblAssocLab.Location = new System.Drawing.Point(523, 91);
            this.lblAssocLab.Name = "lblAssocLab";
            this.lblAssocLab.Size = new System.Drawing.Size(277, 16);
            this.lblAssocLab.TabIndex = 48;
            this.lblAssocLab.Text = "Dr. XXX";
            // 
            // chkSearchAssocLab
            // 
            this.chkSearchAssocLab.Location = new System.Drawing.Point(366, 89);
            this.chkSearchAssocLab.MenuManager = this.barManager;
            this.chkSearchAssocLab.Name = "chkSearchAssocLab";
            this.chkSearchAssocLab.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkSearchAssocLab.Properties.Appearance.Options.UseFont = true;
            this.chkSearchAssocLab.Properties.Caption = "Assoc. Lab:";
            this.chkSearchAssocLab.Size = new System.Drawing.Size(127, 20);
            this.chkSearchAssocLab.TabIndex = 47;
            this.chkSearchAssocLab.Tag = "106";
            this.chkSearchAssocLab.CheckedChanged += new System.EventHandler(this.checkedChangeHandler);
            // 
            // btnCorporate
            // 
            this.btnCorporate.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCorporate.Appearance.Options.UseFont = true;
            this.btnCorporate.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnCorporate.ImageOptions.Image")));
            this.btnCorporate.Location = new System.Drawing.Point(806, 120);
            this.btnCorporate.Name = "btnCorporate";
            this.btnCorporate.Size = new System.Drawing.Size(116, 27);
            this.btnCorporate.TabIndex = 46;
            this.btnCorporate.Text = "Corporate";
            this.btnCorporate.ToolTip = "Reset Memory Cache";
            this.btnCorporate.Click += new System.EventHandler(this.btnCorporate_Click);
            // 
            // btnAssocLab
            // 
            this.btnAssocLab.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnAssocLab.Appearance.Options.UseFont = true;
            this.btnAssocLab.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnAssocLab.ImageOptions.Image")));
            this.btnAssocLab.Location = new System.Drawing.Point(806, 87);
            this.btnAssocLab.Name = "btnAssocLab";
            this.btnAssocLab.Size = new System.Drawing.Size(116, 27);
            this.btnAssocLab.TabIndex = 45;
            this.btnAssocLab.Text = "Assoc. Lab";
            this.btnAssocLab.ToolTip = "Reset Memory Cache";
            this.btnAssocLab.Click += new System.EventHandler(this.btnAssocLab_Click);
            // 
            // btnCopyDate
            // 
            this.btnCopyDate.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCopyDate.Appearance.Options.UseFont = true;
            this.btnCopyDate.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.date_next;
            this.btnCopyDate.Location = new System.Drawing.Point(293, 91);
            this.btnCopyDate.Name = "btnCopyDate";
            this.btnCopyDate.Size = new System.Drawing.Size(25, 23);
            this.btnCopyDate.TabIndex = 44;
            this.btnCopyDate.ToolTip = "Set Single Date";
            this.btnCopyDate.Click += new System.EventHandler(this.btnCopyDate_Click);
            // 
            // btnResetCache
            // 
            this.btnResetCache.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnResetCache.Appearance.Options.UseFont = true;
            this.btnResetCache.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.reload16;
            this.btnResetCache.Location = new System.Drawing.Point(928, 87);
            this.btnResetCache.Name = "btnResetCache";
            this.btnResetCache.Size = new System.Drawing.Size(116, 27);
            this.btnResetCache.TabIndex = 42;
            this.btnResetCache.Text = "Reset Cache";
            this.btnResetCache.ToolTip = "Reset Memory Cache";
            this.btnResetCache.Click += new System.EventHandler(this.btnResetCache_Click);
            // 
            // dtTimeTo
            // 
            this.dtTimeTo.EditValue = null;
            this.dtTimeTo.Location = new System.Drawing.Point(628, 171);
            this.dtTimeTo.MenuManager = this.barManager;
            this.dtTimeTo.Name = "dtTimeTo";
            this.dtTimeTo.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.dtTimeTo.Properties.Appearance.Options.UseFont = true;
            this.dtTimeTo.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.dtTimeTo.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.dtTimeTo.Properties.Mask.EditMask = "t";
            this.dtTimeTo.Size = new System.Drawing.Size(102, 22);
            this.dtTimeTo.TabIndex = 41;
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(554, 174);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(55, 16);
            this.labelControl2.TabIndex = 40;
            this.labelControl2.Text = "Time Till:";
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Location = new System.Drawing.Point(554, 145);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(68, 16);
            this.labelControl1.TabIndex = 38;
            this.labelControl1.Text = "Time From:";
            // 
            // chkApplyTimeFilter
            // 
            this.chkApplyTimeFilter.Location = new System.Drawing.Point(366, 144);
            this.chkApplyTimeFilter.MenuManager = this.barManager;
            this.chkApplyTimeFilter.Name = "chkApplyTimeFilter";
            this.chkApplyTimeFilter.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkApplyTimeFilter.Properties.Appearance.Options.UseFont = true;
            this.chkApplyTimeFilter.Properties.Caption = "Apply Time-range Filter?";
            this.chkApplyTimeFilter.Size = new System.Drawing.Size(182, 20);
            this.chkApplyTimeFilter.TabIndex = 37;
            this.chkApplyTimeFilter.Tag = "105";
            this.chkApplyTimeFilter.CheckedChanged += new System.EventHandler(this.chkApplyTimeFilter_CheckedChanged);
            // 
            // lblReferrer
            // 
            this.lblReferrer.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblReferrer.Appearance.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(64)))));
            this.lblReferrer.Appearance.Options.UseFont = true;
            this.lblReferrer.Appearance.Options.UseForeColor = true;
            this.lblReferrer.AutoEllipsis = true;
            this.lblReferrer.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None;
            this.lblReferrer.Location = new System.Drawing.Point(523, 63);
            this.lblReferrer.Name = "lblReferrer";
            this.lblReferrer.Size = new System.Drawing.Size(277, 16);
            this.lblReferrer.TabIndex = 35;
            this.lblReferrer.Text = "Dr. XXX";
            // 
            // btnSelectReferrer
            // 
            this.btnSelectReferrer.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnSelectReferrer.Appearance.Options.UseFont = true;
            this.btnSelectReferrer.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.physician16;
            this.btnSelectReferrer.Location = new System.Drawing.Point(806, 54);
            this.btnSelectReferrer.Name = "btnSelectReferrer";
            this.btnSelectReferrer.Size = new System.Drawing.Size(116, 27);
            this.btnSelectReferrer.TabIndex = 34;
            this.btnSelectReferrer.Text = "Referrer";
            this.btnSelectReferrer.ToolTip = "Select Referring Physician";
            this.btnSelectReferrer.Click += new System.EventHandler(this.btnSelectReferrer_Click);
            // 
            // chkSearchReferrer
            // 
            this.chkSearchReferrer.Location = new System.Drawing.Point(366, 61);
            this.chkSearchReferrer.MenuManager = this.barManager;
            this.chkSearchReferrer.Name = "chkSearchReferrer";
            this.chkSearchReferrer.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkSearchReferrer.Properties.Appearance.Options.UseFont = true;
            this.chkSearchReferrer.Properties.Caption = "Search Referrer:";
            this.chkSearchReferrer.Size = new System.Drawing.Size(127, 20);
            this.chkSearchReferrer.TabIndex = 33;
            this.chkSearchReferrer.Tag = "105";
            this.chkSearchReferrer.CheckedChanged += new System.EventHandler(this.checkedChangeHandler);
            // 
            // chkSearchBundleStage
            // 
            this.chkSearchBundleStage.Location = new System.Drawing.Point(366, 7);
            this.chkSearchBundleStage.MenuManager = this.barManager;
            this.chkSearchBundleStage.Name = "chkSearchBundleStage";
            this.chkSearchBundleStage.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkSearchBundleStage.Properties.Appearance.Options.UseFont = true;
            this.chkSearchBundleStage.Properties.Caption = "Search Bundle Stage:";
            this.chkSearchBundleStage.Size = new System.Drawing.Size(143, 20);
            this.chkSearchBundleStage.TabIndex = 32;
            this.chkSearchBundleStage.Tag = "103";
            this.chkSearchBundleStage.CheckedChanged += new System.EventHandler(this.checkedChangeHandler);
            // 
            // chkSearchOrderId
            // 
            this.chkSearchOrderId.Location = new System.Drawing.Point(10, 35);
            this.chkSearchOrderId.MenuManager = this.barManager;
            this.chkSearchOrderId.Name = "chkSearchOrderId";
            this.chkSearchOrderId.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkSearchOrderId.Properties.Appearance.Options.UseFont = true;
            this.chkSearchOrderId.Properties.Caption = "Search Order Id:";
            this.chkSearchOrderId.Size = new System.Drawing.Size(126, 20);
            this.chkSearchOrderId.TabIndex = 31;
            this.chkSearchOrderId.Tag = "102";
            this.chkSearchOrderId.CheckedChanged += new System.EventHandler(this.checkedChangeHandler);
            // 
            // chkSearchInvoiceId
            // 
            this.chkSearchInvoiceId.Location = new System.Drawing.Point(10, 8);
            this.chkSearchInvoiceId.MenuManager = this.barManager;
            this.chkSearchInvoiceId.Name = "chkSearchInvoiceId";
            this.chkSearchInvoiceId.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkSearchInvoiceId.Properties.Appearance.Options.UseFont = true;
            this.chkSearchInvoiceId.Properties.Caption = "Search Invoice Num:";
            this.chkSearchInvoiceId.Size = new System.Drawing.Size(145, 20);
            this.chkSearchInvoiceId.TabIndex = 30;
            this.chkSearchInvoiceId.Tag = "101";
            this.chkSearchInvoiceId.CheckedChanged += new System.EventHandler(this.checkedChangeHandler);
            // 
            // chkSearchPatientName
            // 
            this.chkSearchPatientName.Location = new System.Drawing.Point(366, 34);
            this.chkSearchPatientName.MenuManager = this.barManager;
            this.chkSearchPatientName.Name = "chkSearchPatientName";
            this.chkSearchPatientName.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkSearchPatientName.Properties.Appearance.Options.UseFont = true;
            this.chkSearchPatientName.Properties.Caption = "Search Patient Name:";
            this.chkSearchPatientName.Size = new System.Drawing.Size(151, 20);
            this.chkSearchPatientName.TabIndex = 29;
            this.chkSearchPatientName.Tag = "104";
            this.chkSearchPatientName.CheckedChanged += new System.EventHandler(this.checkedChangeHandler);
            // 
            // txtPatientName
            // 
            this.txtPatientName.Location = new System.Drawing.Point(523, 33);
            this.txtPatientName.MenuManager = this.barManager;
            this.txtPatientName.Name = "txtPatientName";
            this.txtPatientName.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtPatientName.Properties.Appearance.Options.UseFont = true;
            this.txtPatientName.Size = new System.Drawing.Size(277, 22);
            this.txtPatientName.TabIndex = 28;
            this.txtPatientName.Tag = "104";
            // 
            // labelControl4
            // 
            this.labelControl4.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl4.Appearance.Options.UseFont = true;
            this.labelControl4.Location = new System.Drawing.Point(12, 95);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(52, 16);
            this.labelControl4.TabIndex = 25;
            this.labelControl4.Text = "Date Till:";
            // 
            // labelControl3
            // 
            this.labelControl3.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl3.Appearance.Options.UseFont = true;
            this.labelControl3.Location = new System.Drawing.Point(12, 68);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(65, 16);
            this.labelControl3.TabIndex = 24;
            this.labelControl3.Text = "Date From:";
            // 
            // btnClear
            // 
            this.btnClear.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnClear.Appearance.Options.UseFont = true;
            this.btnClear.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.clear;
            this.btnClear.Location = new System.Drawing.Point(928, 54);
            this.btnClear.Name = "btnClear";
            this.btnClear.Size = new System.Drawing.Size(116, 27);
            this.btnClear.TabIndex = 23;
            this.btnClear.Text = "Clear";
            this.btnClear.ToolTip = "Clear Search Parameters";
            this.btnClear.Click += new System.EventHandler(this.btnClear_Click);
            // 
            // btnSearch
            // 
            this.btnSearch.Appearance.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnSearch.Appearance.Options.UseFont = true;
            this.btnSearch.ImageOptions.Image = global::LabMaestro.Controls.Win.ResourceIcons.search_32;
            this.btnSearch.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleLeft;
            this.btnSearch.Location = new System.Drawing.Point(928, 119);
            this.btnSearch.Name = "btnSearch";
            this.btnSearch.Size = new System.Drawing.Size(116, 45);
            this.btnSearch.TabIndex = 22;
            this.btnSearch.Text = "Search";
            this.btnSearch.ToolTip = "Perform Search";
            this.btnSearch.Click += new System.EventHandler(this.btnSearch_Click);
            // 
            // cboWorkflowStatus
            // 
            this.cboWorkflowStatus.EditValue = "Approved";
            this.cboWorkflowStatus.Location = new System.Drawing.Point(523, 6);
            this.cboWorkflowStatus.Name = "cboWorkflowStatus";
            this.cboWorkflowStatus.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cboWorkflowStatus.Properties.Appearance.Options.UseFont = true;
            this.cboWorkflowStatus.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cboWorkflowStatus.Properties.Items.AddRange(new object[] {
            "Pending Result Entry",
            "Pending Validation",
            "Pending Finalization",
            "Pending Collation",
            "Pending Dispatch",
            "Repeat Procedure"});
            this.cboWorkflowStatus.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cboWorkflowStatus.Size = new System.Drawing.Size(188, 22);
            this.cboWorkflowStatus.TabIndex = 19;
            this.cboWorkflowStatus.Tag = "103";
            this.cboWorkflowStatus.ToolTip = "Select the appropriate date window";
            // 
            // dtTo
            // 
            this.dtTo.EditValue = null;
            this.dtTo.Location = new System.Drawing.Point(161, 92);
            this.dtTo.MenuManager = this.barManager;
            this.dtTo.Name = "dtTo";
            this.dtTo.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.dtTo.Properties.Appearance.Options.UseFont = true;
            editorButtonImageOptions2.Image = ((System.Drawing.Image)(resources.GetObject("editorButtonImageOptions2.Image")));
            this.dtTo.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph, "", -1, true, true, false, editorButtonImageOptions2, new DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.None), serializableAppearanceObject5, serializableAppearanceObject6, serializableAppearanceObject7, serializableAppearanceObject8, "", null, null, DevExpress.Utils.ToolTipAnchor.Default)});
            this.dtTo.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.dtTo.Properties.DisplayFormat.FormatString = "d MMM, yyyy";
            this.dtTo.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.dtTo.Size = new System.Drawing.Size(126, 24);
            this.dtTo.TabIndex = 3;
            // 
            // dtFrom
            // 
            this.dtFrom.EditValue = null;
            this.dtFrom.Location = new System.Drawing.Point(161, 64);
            this.dtFrom.MenuManager = this.barManager;
            this.dtFrom.Name = "dtFrom";
            this.dtFrom.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.dtFrom.Properties.Appearance.Options.UseFont = true;
            editorButtonImageOptions3.Image = ((System.Drawing.Image)(resources.GetObject("editorButtonImageOptions3.Image")));
            this.dtFrom.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph, "", -1, true, true, false, editorButtonImageOptions3, new DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.None), serializableAppearanceObject9, serializableAppearanceObject10, serializableAppearanceObject11, serializableAppearanceObject12, "", null, null, DevExpress.Utils.ToolTipAnchor.Default)});
            this.dtFrom.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.dtFrom.Properties.DisplayFormat.FormatString = "d MMM, yyyy";
            this.dtFrom.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.dtFrom.Size = new System.Drawing.Size(126, 24);
            this.dtFrom.TabIndex = 2;
            // 
            // txtOrderNum
            // 
            this.txtOrderNum.Location = new System.Drawing.Point(161, 34);
            this.txtOrderNum.MenuManager = this.barManager;
            this.txtOrderNum.Name = "txtOrderNum";
            this.txtOrderNum.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtOrderNum.Properties.Appearance.Options.UseFont = true;
            this.txtOrderNum.Size = new System.Drawing.Size(69, 22);
            this.txtOrderNum.TabIndex = 1;
            this.txtOrderNum.Tag = "102";
            // 
            // txtInvoiceId
            // 
            this.txtInvoiceId.Location = new System.Drawing.Point(161, 7);
            this.txtInvoiceId.MenuManager = this.barManager;
            this.txtInvoiceId.Name = "txtInvoiceId";
            this.txtInvoiceId.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtInvoiceId.Properties.Appearance.Options.UseFont = true;
            this.txtInvoiceId.Size = new System.Drawing.Size(188, 22);
            this.txtInvoiceId.TabIndex = 0;
            this.txtInvoiceId.Tag = "101";
            this.txtInvoiceId.KeyUp += new System.Windows.Forms.KeyEventHandler(this.txtInvoiceId_KeyUp);
            // 
            // dtTimeFrom
            // 
            this.dtTimeFrom.EditValue = null;
            this.dtTimeFrom.Location = new System.Drawing.Point(628, 142);
            this.dtTimeFrom.MenuManager = this.barManager;
            this.dtTimeFrom.Name = "dtTimeFrom";
            this.dtTimeFrom.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.dtTimeFrom.Properties.Appearance.Options.UseFont = true;
            this.dtTimeFrom.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.dtTimeFrom.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.dtTimeFrom.Properties.Mask.EditMask = "t";
            this.dtTimeFrom.Size = new System.Drawing.Size(102, 22);
            this.dtTimeFrom.TabIndex = 36;
            // 
            // splitContainerControl
            // 
            this.splitContainerControl.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Default;
            this.splitContainerControl.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel2;
            this.splitContainerControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl.Horizontal = false;
            this.splitContainerControl.Location = new System.Drawing.Point(0, 236);
            this.splitContainerControl.LookAndFeel.SkinName = "Office 2010 Blue";
            this.splitContainerControl.LookAndFeel.UseDefaultLookAndFeel = false;
            this.splitContainerControl.Name = "splitContainerControl";
            // 
            // splitContainerControl.Panel1
            // 
            this.splitContainerControl.Panel1.Controls.Add(this.grdInvoices);
            this.splitContainerControl.Panel1.Text = "Panel1";
            // 
            // splitContainerControl.Panel2
            // 
            this.splitContainerControl.Panel2.Controls.Add(this.grdResultBundles);
            this.splitContainerControl.Panel2.Text = "Panel2";
            this.splitContainerControl.Size = new System.Drawing.Size(1387, 425);
            this.splitContainerControl.SplitterPosition = 261;
            this.splitContainerControl.TabIndex = 5;
            // 
            // grdInvoices
            // 
            this.grdInvoices.Cursor = System.Windows.Forms.Cursors.Default;
            this.grdInvoices.DataSource = this.labOrderSearchResultSliceBindingSource;
            this.grdInvoices.Dock = System.Windows.Forms.DockStyle.Fill;
            this.grdInvoices.EmbeddedNavigator.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.grdInvoices.Location = new System.Drawing.Point(0, 0);
            this.grdInvoices.MainView = this.gvwInvoices;
            this.grdInvoices.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.grdInvoices.MenuManager = this.barManager;
            this.grdInvoices.Name = "grdInvoices";
            this.grdInvoices.Size = new System.Drawing.Size(1383, 261);
            this.grdInvoices.TabIndex = 0;
            this.grdInvoices.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvwInvoices});
            // 
            // gvwInvoices
            // 
            this.gvwInvoices.Appearance.ColumnFilterButton.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(221)))), ((int)(((byte)(236)))), ((int)(((byte)(254)))));
            this.gvwInvoices.Appearance.ColumnFilterButton.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(171)))), ((int)(((byte)(228)))));
            this.gvwInvoices.Appearance.ColumnFilterButton.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(221)))), ((int)(((byte)(236)))), ((int)(((byte)(254)))));
            this.gvwInvoices.Appearance.ColumnFilterButton.ForeColor = System.Drawing.Color.Black;
            this.gvwInvoices.Appearance.ColumnFilterButton.GradientMode = System.Drawing.Drawing2D.LinearGradientMode.Vertical;
            this.gvwInvoices.Appearance.ColumnFilterButton.Options.UseBackColor = true;
            this.gvwInvoices.Appearance.ColumnFilterButton.Options.UseBorderColor = true;
            this.gvwInvoices.Appearance.ColumnFilterButton.Options.UseForeColor = true;
            this.gvwInvoices.Appearance.ColumnFilterButtonActive.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(247)))), ((int)(((byte)(251)))), ((int)(((byte)(255)))));
            this.gvwInvoices.Appearance.ColumnFilterButtonActive.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(154)))), ((int)(((byte)(190)))), ((int)(((byte)(243)))));
            this.gvwInvoices.Appearance.ColumnFilterButtonActive.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(247)))), ((int)(((byte)(251)))), ((int)(((byte)(255)))));
            this.gvwInvoices.Appearance.ColumnFilterButtonActive.ForeColor = System.Drawing.Color.Black;
            this.gvwInvoices.Appearance.ColumnFilterButtonActive.GradientMode = System.Drawing.Drawing2D.LinearGradientMode.Vertical;
            this.gvwInvoices.Appearance.ColumnFilterButtonActive.Options.UseBackColor = true;
            this.gvwInvoices.Appearance.ColumnFilterButtonActive.Options.UseBorderColor = true;
            this.gvwInvoices.Appearance.ColumnFilterButtonActive.Options.UseForeColor = true;
            this.gvwInvoices.Appearance.Empty.BackColor = System.Drawing.Color.White;
            this.gvwInvoices.Appearance.Empty.Options.UseBackColor = true;
            this.gvwInvoices.Appearance.EvenRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(231)))), ((int)(((byte)(242)))), ((int)(((byte)(254)))));
            this.gvwInvoices.Appearance.EvenRow.ForeColor = System.Drawing.Color.Black;
            this.gvwInvoices.Appearance.EvenRow.Options.UseBackColor = true;
            this.gvwInvoices.Appearance.EvenRow.Options.UseForeColor = true;
            this.gvwInvoices.Appearance.FilterCloseButton.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(221)))), ((int)(((byte)(236)))), ((int)(((byte)(254)))));
            this.gvwInvoices.Appearance.FilterCloseButton.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(171)))), ((int)(((byte)(228)))));
            this.gvwInvoices.Appearance.FilterCloseButton.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(221)))), ((int)(((byte)(236)))), ((int)(((byte)(254)))));
            this.gvwInvoices.Appearance.FilterCloseButton.ForeColor = System.Drawing.Color.Black;
            this.gvwInvoices.Appearance.FilterCloseButton.GradientMode = System.Drawing.Drawing2D.LinearGradientMode.Vertical;
            this.gvwInvoices.Appearance.FilterCloseButton.Options.UseBackColor = true;
            this.gvwInvoices.Appearance.FilterCloseButton.Options.UseBorderColor = true;
            this.gvwInvoices.Appearance.FilterCloseButton.Options.UseForeColor = true;
            this.gvwInvoices.Appearance.FilterPanel.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(62)))), ((int)(((byte)(109)))), ((int)(((byte)(185)))));
            this.gvwInvoices.Appearance.FilterPanel.ForeColor = System.Drawing.Color.White;
            this.gvwInvoices.Appearance.FilterPanel.Options.UseBackColor = true;
            this.gvwInvoices.Appearance.FilterPanel.Options.UseForeColor = true;
            this.gvwInvoices.Appearance.FixedLine.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(59)))), ((int)(((byte)(97)))), ((int)(((byte)(156)))));
            this.gvwInvoices.Appearance.FixedLine.Options.UseBackColor = true;
            this.gvwInvoices.Appearance.FocusedCell.BackColor = System.Drawing.Color.White;
            this.gvwInvoices.Appearance.FocusedCell.ForeColor = System.Drawing.Color.Black;
            this.gvwInvoices.Appearance.FocusedCell.Options.UseBackColor = true;
            this.gvwInvoices.Appearance.FocusedCell.Options.UseForeColor = true;
            this.gvwInvoices.Appearance.FocusedRow.BackColor = System.Drawing.Color.Purple;
            this.gvwInvoices.Appearance.FocusedRow.ForeColor = System.Drawing.Color.White;
            this.gvwInvoices.Appearance.FocusedRow.Options.UseBackColor = true;
            this.gvwInvoices.Appearance.FocusedRow.Options.UseForeColor = true;
            this.gvwInvoices.Appearance.FooterPanel.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(221)))), ((int)(((byte)(236)))), ((int)(((byte)(254)))));
            this.gvwInvoices.Appearance.FooterPanel.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(171)))), ((int)(((byte)(228)))));
            this.gvwInvoices.Appearance.FooterPanel.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(221)))), ((int)(((byte)(236)))), ((int)(((byte)(254)))));
            this.gvwInvoices.Appearance.FooterPanel.ForeColor = System.Drawing.Color.Black;
            this.gvwInvoices.Appearance.FooterPanel.GradientMode = System.Drawing.Drawing2D.LinearGradientMode.Vertical;
            this.gvwInvoices.Appearance.FooterPanel.Options.UseBackColor = true;
            this.gvwInvoices.Appearance.FooterPanel.Options.UseBorderColor = true;
            this.gvwInvoices.Appearance.FooterPanel.Options.UseForeColor = true;
            this.gvwInvoices.Appearance.GroupButton.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(193)))), ((int)(((byte)(216)))), ((int)(((byte)(247)))));
            this.gvwInvoices.Appearance.GroupButton.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(193)))), ((int)(((byte)(216)))), ((int)(((byte)(247)))));
            this.gvwInvoices.Appearance.GroupButton.ForeColor = System.Drawing.Color.Black;
            this.gvwInvoices.Appearance.GroupButton.Options.UseBackColor = true;
            this.gvwInvoices.Appearance.GroupButton.Options.UseBorderColor = true;
            this.gvwInvoices.Appearance.GroupButton.Options.UseForeColor = true;
            this.gvwInvoices.Appearance.GroupFooter.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(193)))), ((int)(((byte)(216)))), ((int)(((byte)(247)))));
            this.gvwInvoices.Appearance.GroupFooter.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(193)))), ((int)(((byte)(216)))), ((int)(((byte)(247)))));
            this.gvwInvoices.Appearance.GroupFooter.ForeColor = System.Drawing.Color.Black;
            this.gvwInvoices.Appearance.GroupFooter.Options.UseBackColor = true;
            this.gvwInvoices.Appearance.GroupFooter.Options.UseBorderColor = true;
            this.gvwInvoices.Appearance.GroupFooter.Options.UseForeColor = true;
            this.gvwInvoices.Appearance.GroupPanel.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(62)))), ((int)(((byte)(109)))), ((int)(((byte)(185)))));
            this.gvwInvoices.Appearance.GroupPanel.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(221)))), ((int)(((byte)(236)))), ((int)(((byte)(254)))));
            this.gvwInvoices.Appearance.GroupPanel.Options.UseBackColor = true;
            this.gvwInvoices.Appearance.GroupPanel.Options.UseForeColor = true;
            this.gvwInvoices.Appearance.GroupRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(193)))), ((int)(((byte)(216)))), ((int)(((byte)(247)))));
            this.gvwInvoices.Appearance.GroupRow.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(193)))), ((int)(((byte)(216)))), ((int)(((byte)(247)))));
            this.gvwInvoices.Appearance.GroupRow.Font = new System.Drawing.Font("Tahoma", 8F, System.Drawing.FontStyle.Bold);
            this.gvwInvoices.Appearance.GroupRow.ForeColor = System.Drawing.Color.Black;
            this.gvwInvoices.Appearance.GroupRow.Options.UseBackColor = true;
            this.gvwInvoices.Appearance.GroupRow.Options.UseBorderColor = true;
            this.gvwInvoices.Appearance.GroupRow.Options.UseFont = true;
            this.gvwInvoices.Appearance.GroupRow.Options.UseForeColor = true;
            this.gvwInvoices.Appearance.HeaderPanel.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(221)))), ((int)(((byte)(236)))), ((int)(((byte)(254)))));
            this.gvwInvoices.Appearance.HeaderPanel.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(171)))), ((int)(((byte)(228)))));
            this.gvwInvoices.Appearance.HeaderPanel.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(221)))), ((int)(((byte)(236)))), ((int)(((byte)(254)))));
            this.gvwInvoices.Appearance.HeaderPanel.ForeColor = System.Drawing.Color.Black;
            this.gvwInvoices.Appearance.HeaderPanel.GradientMode = System.Drawing.Drawing2D.LinearGradientMode.Vertical;
            this.gvwInvoices.Appearance.HeaderPanel.Options.UseBackColor = true;
            this.gvwInvoices.Appearance.HeaderPanel.Options.UseBorderColor = true;
            this.gvwInvoices.Appearance.HeaderPanel.Options.UseForeColor = true;
            this.gvwInvoices.Appearance.HideSelectionRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(106)))), ((int)(((byte)(153)))), ((int)(((byte)(228)))));
            this.gvwInvoices.Appearance.HideSelectionRow.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(208)))), ((int)(((byte)(224)))), ((int)(((byte)(251)))));
            this.gvwInvoices.Appearance.HideSelectionRow.Options.UseBackColor = true;
            this.gvwInvoices.Appearance.HideSelectionRow.Options.UseForeColor = true;
            this.gvwInvoices.Appearance.HorzLine.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(99)))), ((int)(((byte)(127)))), ((int)(((byte)(196)))));
            this.gvwInvoices.Appearance.HorzLine.Options.UseBackColor = true;
            this.gvwInvoices.Appearance.OddRow.BackColor = System.Drawing.Color.White;
            this.gvwInvoices.Appearance.OddRow.ForeColor = System.Drawing.Color.Black;
            this.gvwInvoices.Appearance.OddRow.Options.UseBackColor = true;
            this.gvwInvoices.Appearance.OddRow.Options.UseForeColor = true;
            this.gvwInvoices.Appearance.Preview.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(249)))), ((int)(((byte)(252)))), ((int)(((byte)(255)))));
            this.gvwInvoices.Appearance.Preview.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(88)))), ((int)(((byte)(129)))), ((int)(((byte)(185)))));
            this.gvwInvoices.Appearance.Preview.Options.UseBackColor = true;
            this.gvwInvoices.Appearance.Preview.Options.UseForeColor = true;
            this.gvwInvoices.Appearance.Row.BackColor = System.Drawing.Color.White;
            this.gvwInvoices.Appearance.Row.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.gvwInvoices.Appearance.Row.ForeColor = System.Drawing.Color.Black;
            this.gvwInvoices.Appearance.Row.Options.UseBackColor = true;
            this.gvwInvoices.Appearance.Row.Options.UseFont = true;
            this.gvwInvoices.Appearance.Row.Options.UseForeColor = true;
            this.gvwInvoices.Appearance.RowSeparator.BackColor = System.Drawing.Color.White;
            this.gvwInvoices.Appearance.RowSeparator.Options.UseBackColor = true;
            this.gvwInvoices.Appearance.SelectedRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(69)))), ((int)(((byte)(126)))), ((int)(((byte)(217)))));
            this.gvwInvoices.Appearance.SelectedRow.ForeColor = System.Drawing.Color.White;
            this.gvwInvoices.Appearance.SelectedRow.Options.UseBackColor = true;
            this.gvwInvoices.Appearance.SelectedRow.Options.UseForeColor = true;
            this.gvwInvoices.Appearance.VertLine.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(99)))), ((int)(((byte)(127)))), ((int)(((byte)(196)))));
            this.gvwInvoices.Appearance.VertLine.Options.UseBackColor = true;
            this.gvwInvoices.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colInvoiceId,
            this.colOrderId,
            this.colOrderDateTime,
            this.colIsCancelled,
            this.colPatientName,
            this.colSex,
            this.colWorkflowStage,
            this.colReferrerName,
            this.colNetPayable,
            this.colDiscountAmount,
            this.colPaidAmount,
            this.colDueAmount,
            this.colRefUnknown,
            this.colRefDisallow,
            this.colExternalSubOrder});
            this.gvwInvoices.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.None;
            this.gvwInvoices.GridControl = this.grdInvoices;
            this.gvwInvoices.Name = "gvwInvoices";
            this.gvwInvoices.OptionsBehavior.Editable = false;
            this.gvwInvoices.OptionsBehavior.ReadOnly = true;
            this.gvwInvoices.OptionsCustomization.AllowFilter = false;
            this.gvwInvoices.OptionsCustomization.AllowGroup = false;
            this.gvwInvoices.OptionsView.AllowHtmlDrawGroups = false;
            this.gvwInvoices.OptionsView.EnableAppearanceEvenRow = true;
            this.gvwInvoices.OptionsView.EnableAppearanceOddRow = true;
            this.gvwInvoices.OptionsView.ShowGroupPanel = false;
            this.gvwInvoices.PopupMenuShowing += new DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventHandler(this.gvwInvoices_PopupMenuShowing);
            this.gvwInvoices.FocusedRowChanged += new DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventHandler(this.gvwInvoices_FocusedRowChanged);
            this.gvwInvoices.CustomColumnDisplayText += new DevExpress.XtraGrid.Views.Base.CustomColumnDisplayTextEventHandler(this.gvwInvoices_CustomColumnDisplayText);
            // 
            // colInvoiceId
            // 
            this.colInvoiceId.Caption = "Invoice #";
            this.colInvoiceId.FieldName = "InvoiceId";
            this.colInvoiceId.ImageOptions.Alignment = System.Drawing.StringAlignment.Center;
            this.colInvoiceId.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.Counter;
            this.colInvoiceId.Name = "colInvoiceId";
            this.colInvoiceId.OptionsColumn.AllowEdit = false;
            this.colInvoiceId.OptionsColumn.AllowFocus = false;
            this.colInvoiceId.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colInvoiceId.OptionsColumn.AllowMove = false;
            this.colInvoiceId.OptionsColumn.AllowShowHide = false;
            this.colInvoiceId.OptionsColumn.ReadOnly = true;
            this.colInvoiceId.OptionsColumn.ShowCaption = false;
            this.colInvoiceId.ToolTip = "Invoice Number";
            this.colInvoiceId.Visible = true;
            this.colInvoiceId.VisibleIndex = 0;
            this.colInvoiceId.Width = 44;
            // 
            // colOrderId
            // 
            this.colOrderId.Caption = "Order";
            this.colOrderId.FieldName = "OrderId";
            this.colOrderId.ImageOptions.Alignment = System.Drawing.StringAlignment.Center;
            this.colOrderId.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.invoice16;
            this.colOrderId.Name = "colOrderId";
            this.colOrderId.OptionsColumn.AllowEdit = false;
            this.colOrderId.OptionsColumn.AllowFocus = false;
            this.colOrderId.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colOrderId.OptionsColumn.AllowMove = false;
            this.colOrderId.OptionsColumn.AllowShowHide = false;
            this.colOrderId.OptionsColumn.ReadOnly = true;
            this.colOrderId.OptionsColumn.ShowCaption = false;
            this.colOrderId.ToolTip = "Order ID";
            this.colOrderId.Visible = true;
            this.colOrderId.VisibleIndex = 1;
            this.colOrderId.Width = 29;
            // 
            // colOrderDateTime
            // 
            this.colOrderDateTime.Caption = "Booking Time";
            this.colOrderDateTime.DisplayFormat.FormatString = "dd/MM/yy HH:mm";
            this.colOrderDateTime.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.colOrderDateTime.FieldName = "OrderDateTime";
            this.colOrderDateTime.Name = "colOrderDateTime";
            this.colOrderDateTime.OptionsColumn.AllowEdit = false;
            this.colOrderDateTime.OptionsColumn.AllowFocus = false;
            this.colOrderDateTime.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colOrderDateTime.OptionsColumn.AllowShowHide = false;
            this.colOrderDateTime.OptionsColumn.FixedWidth = true;
            this.colOrderDateTime.OptionsColumn.ReadOnly = true;
            this.colOrderDateTime.ToolTip = "Booking Date-time";
            this.colOrderDateTime.Visible = true;
            this.colOrderDateTime.VisibleIndex = 2;
            this.colOrderDateTime.Width = 85;
            // 
            // colIsCancelled
            // 
            this.colIsCancelled.FieldName = "IsCancelled";
            this.colIsCancelled.ImageOptions.Alignment = System.Drawing.StringAlignment.Center;
            this.colIsCancelled.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.flag_red;
            this.colIsCancelled.Name = "colIsCancelled";
            this.colIsCancelled.OptionsColumn.AllowFocus = false;
            this.colIsCancelled.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colIsCancelled.OptionsColumn.AllowSize = false;
            this.colIsCancelled.OptionsColumn.FixedWidth = true;
            this.colIsCancelled.OptionsColumn.ReadOnly = true;
            this.colIsCancelled.OptionsColumn.ShowCaption = false;
            this.colIsCancelled.ToolTip = "Order Is Cancelled?";
            this.colIsCancelled.Visible = true;
            this.colIsCancelled.VisibleIndex = 3;
            this.colIsCancelled.Width = 40;
            // 
            // colPatientName
            // 
            this.colPatientName.Caption = "Patient Name";
            this.colPatientName.FieldName = "PatientName";
            this.colPatientName.Name = "colPatientName";
            this.colPatientName.OptionsColumn.AllowEdit = false;
            this.colPatientName.OptionsColumn.AllowFocus = false;
            this.colPatientName.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colPatientName.OptionsColumn.AllowShowHide = false;
            this.colPatientName.OptionsColumn.ReadOnly = true;
            this.colPatientName.ToolTip = "Patient Name";
            this.colPatientName.Visible = true;
            this.colPatientName.VisibleIndex = 4;
            this.colPatientName.Width = 206;
            // 
            // colSex
            // 
            this.colSex.FieldName = "Sex";
            this.colSex.ImageOptions.Alignment = System.Drawing.StringAlignment.Center;
            this.colSex.ImageOptions.Image = global::LabMaestro.Controls.Win.ResourceIcons.people_16;
            this.colSex.Name = "colSex";
            this.colSex.OptionsColumn.AllowFocus = false;
            this.colSex.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colSex.OptionsColumn.AllowSize = false;
            this.colSex.OptionsColumn.FixedWidth = true;
            this.colSex.OptionsColumn.ReadOnly = true;
            this.colSex.OptionsColumn.ShowCaption = false;
            this.colSex.ToolTip = "Gender";
            this.colSex.Visible = true;
            this.colSex.VisibleIndex = 5;
            this.colSex.Width = 40;
            // 
            // colWorkflowStage
            // 
            this.colWorkflowStage.Caption = "LAW Stage";
            this.colWorkflowStage.FieldName = "WorkflowStage";
            this.colWorkflowStage.Name = "colWorkflowStage";
            this.colWorkflowStage.OptionsColumn.AllowEdit = false;
            this.colWorkflowStage.OptionsColumn.AllowFocus = false;
            this.colWorkflowStage.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colWorkflowStage.OptionsColumn.AllowShowHide = false;
            this.colWorkflowStage.OptionsColumn.ReadOnly = true;
            this.colWorkflowStage.ToolTip = "Workflow Stage";
            this.colWorkflowStage.Visible = true;
            this.colWorkflowStage.VisibleIndex = 6;
            this.colWorkflowStage.Width = 96;
            // 
            // colReferrerName
            // 
            this.colReferrerName.Caption = "Referred By";
            this.colReferrerName.FieldName = "ReferrerName";
            this.colReferrerName.Name = "colReferrerName";
            this.colReferrerName.OptionsColumn.AllowEdit = false;
            this.colReferrerName.OptionsColumn.AllowFocus = false;
            this.colReferrerName.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colReferrerName.OptionsColumn.AllowShowHide = false;
            this.colReferrerName.OptionsColumn.ReadOnly = true;
            this.colReferrerName.ToolTip = "Referred By";
            this.colReferrerName.Visible = true;
            this.colReferrerName.VisibleIndex = 7;
            this.colReferrerName.Width = 127;
            // 
            // colNetPayable
            // 
            this.colNetPayable.Caption = "Net";
            this.colNetPayable.DisplayFormat.FormatString = "{0:#.##;;\"\"}";
            this.colNetPayable.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colNetPayable.FieldName = "NetPayable";
            this.colNetPayable.Name = "colNetPayable";
            this.colNetPayable.OptionsColumn.AllowFocus = false;
            this.colNetPayable.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colNetPayable.OptionsColumn.FixedWidth = true;
            this.colNetPayable.OptionsColumn.ReadOnly = true;
            this.colNetPayable.ToolTip = "Net Payable";
            this.colNetPayable.Visible = true;
            this.colNetPayable.VisibleIndex = 8;
            // 
            // colDiscountAmount
            // 
            this.colDiscountAmount.Caption = "Discount";
            this.colDiscountAmount.DisplayFormat.FormatString = "{0:#.##;;\"\"}";
            this.colDiscountAmount.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colDiscountAmount.FieldName = "DiscountAmount";
            this.colDiscountAmount.Name = "colDiscountAmount";
            this.colDiscountAmount.OptionsColumn.AllowFocus = false;
            this.colDiscountAmount.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colDiscountAmount.OptionsColumn.FixedWidth = true;
            this.colDiscountAmount.OptionsColumn.ReadOnly = true;
            this.colDiscountAmount.ToolTip = "Total Discount";
            this.colDiscountAmount.Visible = true;
            this.colDiscountAmount.VisibleIndex = 9;
            // 
            // colPaidAmount
            // 
            this.colPaidAmount.Caption = "Paid";
            this.colPaidAmount.DisplayFormat.FormatString = "{0:#.##;;\"\"}";
            this.colPaidAmount.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colPaidAmount.FieldName = "PaidAmount";
            this.colPaidAmount.Name = "colPaidAmount";
            this.colPaidAmount.OptionsColumn.AllowFocus = false;
            this.colPaidAmount.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colPaidAmount.OptionsColumn.FixedWidth = true;
            this.colPaidAmount.OptionsColumn.ReadOnly = true;
            this.colPaidAmount.ToolTip = "Amount Paid";
            this.colPaidAmount.Visible = true;
            this.colPaidAmount.VisibleIndex = 10;
            // 
            // colDueAmount
            // 
            this.colDueAmount.Caption = "Due";
            this.colDueAmount.DisplayFormat.FormatString = "{0:#.##;;\"\"}";
            this.colDueAmount.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colDueAmount.FieldName = "DueAmount";
            this.colDueAmount.Name = "colDueAmount";
            this.colDueAmount.OptionsColumn.AllowFocus = false;
            this.colDueAmount.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colDueAmount.OptionsColumn.FixedWidth = true;
            this.colDueAmount.OptionsColumn.ReadOnly = true;
            this.colDueAmount.ToolTip = "Amount Due";
            this.colDueAmount.Visible = true;
            this.colDueAmount.VisibleIndex = 11;
            // 
            // colRefUnknown
            // 
            this.colRefUnknown.Caption = "Unk?";
            this.colRefUnknown.FieldName = "IsReferrerUnknown";
            this.colRefUnknown.ImageOptions.Alignment = System.Drawing.StringAlignment.Center;
            this.colRefUnknown.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.physician16;
            this.colRefUnknown.Name = "colRefUnknown";
            this.colRefUnknown.OptionsColumn.AllowEdit = false;
            this.colRefUnknown.OptionsColumn.AllowFocus = false;
            this.colRefUnknown.OptionsColumn.AllowSize = false;
            this.colRefUnknown.OptionsColumn.FixedWidth = true;
            this.colRefUnknown.OptionsColumn.ReadOnly = true;
            this.colRefUnknown.OptionsColumn.ShowCaption = false;
            this.colRefUnknown.ToolTip = "Is Referrer Unknown?";
            this.colRefUnknown.Visible = true;
            this.colRefUnknown.VisibleIndex = 12;
            this.colRefUnknown.Width = 40;
            // 
            // colRefDisallow
            // 
            this.colRefDisallow.Caption = "Ref?";
            this.colRefDisallow.FieldName = "DisallowReferral";
            this.colRefDisallow.ImageOptions.Alignment = System.Drawing.StringAlignment.Center;
            this.colRefDisallow.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.coins;
            this.colRefDisallow.Name = "colRefDisallow";
            this.colRefDisallow.OptionsColumn.AllowEdit = false;
            this.colRefDisallow.OptionsColumn.AllowFocus = false;
            this.colRefDisallow.OptionsColumn.AllowSize = false;
            this.colRefDisallow.OptionsColumn.FixedWidth = true;
            this.colRefDisallow.OptionsColumn.ReadOnly = true;
            this.colRefDisallow.OptionsColumn.ShowCaption = false;
            this.colRefDisallow.ToolTip = "Disallow Referral?";
            this.colRefDisallow.Visible = true;
            this.colRefDisallow.VisibleIndex = 13;
            this.colRefDisallow.Width = 40;
            // 
            // colExternalSubOrder
            // 
            this.colExternalSubOrder.FieldName = "IsExternalSubOrder";
            this.colExternalSubOrder.ImageOptions.Alignment = System.Drawing.StringAlignment.Center;
            this.colExternalSubOrder.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.truck_arrow_16;
            this.colExternalSubOrder.Name = "colExternalSubOrder";
            this.colExternalSubOrder.OptionsColumn.AllowEdit = false;
            this.colExternalSubOrder.OptionsColumn.AllowFocus = false;
            this.colExternalSubOrder.OptionsColumn.AllowSize = false;
            this.colExternalSubOrder.OptionsColumn.FixedWidth = true;
            this.colExternalSubOrder.OptionsColumn.ReadOnly = true;
            this.colExternalSubOrder.OptionsColumn.ShowCaption = false;
            this.colExternalSubOrder.ToolTip = "Is External Sub-order?";
            this.colExternalSubOrder.Visible = true;
            this.colExternalSubOrder.VisibleIndex = 14;
            this.colExternalSubOrder.Width = 40;
            // 
            // grdResultBundles
            // 
            this.grdResultBundles.DataSource = this.activeResultBundleSliceBindingSource;
            this.grdResultBundles.Dock = System.Windows.Forms.DockStyle.Fill;
            this.grdResultBundles.EmbeddedNavigator.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.grdResultBundles.Location = new System.Drawing.Point(0, 0);
            this.grdResultBundles.LookAndFeel.SkinName = "Seven Classic";
            this.grdResultBundles.LookAndFeel.UseDefaultLookAndFeel = false;
            this.grdResultBundles.MainView = this.gvwResultBundles;
            this.grdResultBundles.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.grdResultBundles.MenuManager = this.barManager;
            this.grdResultBundles.Name = "grdResultBundles";
            this.grdResultBundles.Size = new System.Drawing.Size(1383, 154);
            this.grdResultBundles.TabIndex = 0;
            this.grdResultBundles.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvwResultBundles});
            // 
            // gvwResultBundles
            // 
            this.gvwResultBundles.Appearance.FocusedRow.BackColor = System.Drawing.Color.Red;
            this.gvwResultBundles.Appearance.FocusedRow.ForeColor = System.Drawing.Color.Yellow;
            this.gvwResultBundles.Appearance.FocusedRow.Options.UseBackColor = true;
            this.gvwResultBundles.Appearance.FocusedRow.Options.UseForeColor = true;
            this.gvwResultBundles.Appearance.Row.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.gvwResultBundles.Appearance.Row.Options.UseFont = true;
            this.gvwResultBundles.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colId,
            this.colDisplayTitle,
            this.colComponentLabTests,
            this.colWorkflowStageB,
            this.colStaffName,
            this.colLastUpdated});
            this.gvwResultBundles.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.None;
            this.gvwResultBundles.GridControl = this.grdResultBundles;
            this.gvwResultBundles.Name = "gvwResultBundles";
            this.gvwResultBundles.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.gvwResultBundles.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.gvwResultBundles.OptionsBehavior.Editable = false;
            this.gvwResultBundles.OptionsBehavior.ReadOnly = true;
            this.gvwResultBundles.OptionsCustomization.AllowGroup = false;
            this.gvwResultBundles.OptionsDetail.EnableMasterViewMode = false;
            this.gvwResultBundles.OptionsDetail.ShowDetailTabs = false;
            this.gvwResultBundles.OptionsDetail.SmartDetailExpand = false;
            this.gvwResultBundles.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gvwResultBundles.OptionsView.AllowHtmlDrawGroups = false;
            this.gvwResultBundles.OptionsView.EnableAppearanceEvenRow = true;
            this.gvwResultBundles.OptionsView.GroupFooterShowMode = DevExpress.XtraGrid.Views.Grid.GroupFooterShowMode.Hidden;
            this.gvwResultBundles.OptionsView.ShowDetailButtons = false;
            this.gvwResultBundles.OptionsView.ShowGroupExpandCollapseButtons = false;
            this.gvwResultBundles.OptionsView.ShowGroupPanel = false;
            this.gvwResultBundles.PaintStyleName = "Skin";
            this.gvwResultBundles.PopupMenuShowing += new DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventHandler(this.gvwResultBundles_PopupMenuShowing);
            // 
            // colId
            // 
            this.colId.Caption = "Bundle #";
            this.colId.FieldName = "Id";
            this.colId.Name = "colId";
            this.colId.OptionsColumn.AllowEdit = false;
            this.colId.OptionsColumn.AllowFocus = false;
            this.colId.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colId.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colId.OptionsColumn.ReadOnly = true;
            this.colId.Visible = true;
            this.colId.VisibleIndex = 0;
            this.colId.Width = 80;
            // 
            // colDisplayTitle
            // 
            this.colDisplayTitle.Caption = "Result Bundle";
            this.colDisplayTitle.FieldName = "DisplayTitle";
            this.colDisplayTitle.Name = "colDisplayTitle";
            this.colDisplayTitle.OptionsColumn.AllowEdit = false;
            this.colDisplayTitle.OptionsColumn.AllowFocus = false;
            this.colDisplayTitle.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colDisplayTitle.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colDisplayTitle.OptionsColumn.ReadOnly = true;
            this.colDisplayTitle.Visible = true;
            this.colDisplayTitle.VisibleIndex = 1;
            this.colDisplayTitle.Width = 208;
            // 
            // colComponentLabTests
            // 
            this.colComponentLabTests.Caption = "Component Lab Test(s)";
            this.colComponentLabTests.FieldName = "ComponentLabTests";
            this.colComponentLabTests.Name = "colComponentLabTests";
            this.colComponentLabTests.OptionsColumn.AllowEdit = false;
            this.colComponentLabTests.OptionsColumn.AllowFocus = false;
            this.colComponentLabTests.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colComponentLabTests.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colComponentLabTests.OptionsColumn.ReadOnly = true;
            this.colComponentLabTests.Visible = true;
            this.colComponentLabTests.VisibleIndex = 2;
            this.colComponentLabTests.Width = 208;
            // 
            // colWorkflowStageB
            // 
            this.colWorkflowStageB.Caption = "LAW Stage";
            this.colWorkflowStageB.FieldName = "WorkflowStage";
            this.colWorkflowStageB.Name = "colWorkflowStageB";
            this.colWorkflowStageB.OptionsColumn.AllowEdit = false;
            this.colWorkflowStageB.OptionsColumn.AllowFocus = false;
            this.colWorkflowStageB.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colWorkflowStageB.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colWorkflowStageB.OptionsColumn.ReadOnly = true;
            this.colWorkflowStageB.Visible = true;
            this.colWorkflowStageB.VisibleIndex = 3;
            this.colWorkflowStageB.Width = 208;
            // 
            // colStaffName
            // 
            this.colStaffName.Caption = "Performed By";
            this.colStaffName.FieldName = "StaffName";
            this.colStaffName.Name = "colStaffName";
            this.colStaffName.OptionsColumn.AllowEdit = false;
            this.colStaffName.OptionsColumn.AllowFocus = false;
            this.colStaffName.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colStaffName.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colStaffName.OptionsColumn.ReadOnly = true;
            this.colStaffName.Visible = true;
            this.colStaffName.VisibleIndex = 4;
            this.colStaffName.Width = 208;
            // 
            // colLastUpdated
            // 
            this.colLastUpdated.DisplayFormat.FormatString = "dd/MM/yy HH:mm";
            this.colLastUpdated.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.colLastUpdated.FieldName = "LastUpdated";
            this.colLastUpdated.Name = "colLastUpdated";
            this.colLastUpdated.OptionsColumn.AllowEdit = false;
            this.colLastUpdated.OptionsColumn.AllowFocus = false;
            this.colLastUpdated.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colLastUpdated.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colLastUpdated.OptionsColumn.ReadOnly = true;
            this.colLastUpdated.Visible = true;
            this.colLastUpdated.VisibleIndex = 5;
            this.colLastUpdated.Width = 210;
            // 
            // chkSearchPhone
            // 
            this.chkSearchPhone.Location = new System.Drawing.Point(10, 124);
            this.chkSearchPhone.MenuManager = this.barManager;
            this.chkSearchPhone.Name = "chkSearchPhone";
            this.chkSearchPhone.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkSearchPhone.Properties.Appearance.Options.UseFont = true;
            this.chkSearchPhone.Properties.Caption = "Search Phone:";
            this.chkSearchPhone.Size = new System.Drawing.Size(132, 20);
            this.chkSearchPhone.TabIndex = 54;
            this.chkSearchPhone.Tag = "108";
            this.chkSearchPhone.CheckedChanged += new System.EventHandler(this.checkedChangeHandler);
            // 
            // txtPhoneNumber
            // 
            this.txtPhoneNumber.Location = new System.Drawing.Point(161, 122);
            this.txtPhoneNumber.MenuManager = this.barManager;
            this.txtPhoneNumber.Name = "txtPhoneNumber";
            this.txtPhoneNumber.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtPhoneNumber.Properties.Appearance.Options.UseFont = true;
            this.txtPhoneNumber.Size = new System.Drawing.Size(157, 22);
            this.txtPhoneNumber.TabIndex = 53;
            this.txtPhoneNumber.Tag = "104";
            // 
            // chkSearchCustomer
            // 
            this.chkSearchCustomer.Location = new System.Drawing.Point(10, 152);
            this.chkSearchCustomer.MenuManager = this.barManager;
            this.chkSearchCustomer.Name = "chkSearchCustomer";
            this.chkSearchCustomer.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkSearchCustomer.Properties.Appearance.Options.UseFont = true;
            this.chkSearchCustomer.Properties.Caption = "Patient PHN:";
            this.chkSearchCustomer.Size = new System.Drawing.Size(126, 20);
            this.chkSearchCustomer.TabIndex = 56;
            this.chkSearchCustomer.Tag = "109";
            this.chkSearchCustomer.CheckedChanged += new System.EventHandler(this.checkedChangeHandler);
            // 
            // txtCustomerUpin
            // 
            this.txtCustomerUpin.Location = new System.Drawing.Point(161, 150);
            this.txtCustomerUpin.MenuManager = this.barManager;
            this.txtCustomerUpin.Name = "txtCustomerUpin";
            this.txtCustomerUpin.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtCustomerUpin.Properties.Appearance.Options.UseFont = true;
            this.txtCustomerUpin.Size = new System.Drawing.Size(81, 22);
            this.txtCustomerUpin.TabIndex = 55;
            this.txtCustomerUpin.Tag = "104";
            // 
            // InvoiceSearchForm
            // 
            this.Appearance.Options.UseFont = true;
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.ClientSize = new System.Drawing.Size(1387, 661);
            this.Controls.Add(this.splitContainerControl);
            this.Controls.Add(this.searchPanel);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.DoubleBuffered = true;
            this.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.IconOptions.ShowIcon = false;
            this.KeyPreview = true;
            this.LookAndFeel.UseDefaultLookAndFeel = false;
            this.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.MinimizeBox = false;
            this.MinimumSize = new System.Drawing.Size(1030, 693);
            this.Name = "InvoiceSearchForm";
            this.ShowInTaskbar = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "InvoiceSearchForm";
            this.WindowState = System.Windows.Forms.FormWindowState.Maximized;
            this.KeyUp += new System.Windows.Forms.KeyEventHandler(this.InvoiceSearchForm_KeyUp);
            ((System.ComponentModel.ISupportInitialize)(this.searchPanel)).EndInit();
            this.searchPanel.ResumeLayout(false);
            this.searchPanel.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).EndInit();
            this.panelControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.chkSearchCorporate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.popupMenu)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSearchAssocLab.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtTimeTo.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkApplyTimeFilter.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSearchReferrer.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSearchBundleStage.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSearchOrderId.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSearchInvoiceId.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSearchPatientName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPatientName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cboWorkflowStatus.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtTo.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtTo.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtFrom.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtFrom.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtOrderNum.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtInvoiceId.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtTimeFrom.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl.Panel1)).EndInit();
            this.splitContainerControl.Panel1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl.Panel2)).EndInit();
            this.splitContainerControl.Panel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl)).EndInit();
            this.splitContainerControl.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.grdInvoices)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.labOrderSearchResultSliceBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvwInvoices)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdResultBundles)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.activeResultBundleSliceBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvwResultBundles)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSearchPhone.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPhoneNumber.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSearchCustomer.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCustomerUpin.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.PanelControl searchPanel;
        private DevExpress.XtraBars.BarManager barManager;
        private DevExpress.XtraBars.Bar toolbar;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraEditors.DateEdit dtTo;
        private DevExpress.XtraEditors.DateEdit dtFrom;
        private DevExpress.XtraEditors.TextEdit txtOrderNum;
        private DevExpress.XtraEditors.TextEdit txtInvoiceId;
        private DevExpress.XtraEditors.ComboBoxEdit cboWorkflowStatus;
        private DevExpress.XtraEditors.SimpleButton btnClear;
        private DevExpress.XtraEditors.SimpleButton btnSearch;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl;
        private DevExpress.XtraGrid.GridControl grdInvoices;
        private DevExpress.XtraGrid.Views.Grid.GridView gvwInvoices;
        private DevExpress.XtraGrid.GridControl grdResultBundles;
        private DevExpress.XtraGrid.Views.Grid.GridView gvwResultBundles;
        private System.Windows.Forms.BindingSource labOrderSearchResultSliceBindingSource;
        private DevExpress.XtraGrid.Columns.GridColumn colInvoiceId;
        private DevExpress.XtraGrid.Columns.GridColumn colOrderId;
        private DevExpress.XtraGrid.Columns.GridColumn colOrderDateTime;
        private DevExpress.XtraGrid.Columns.GridColumn colPatientName;
        private DevExpress.XtraGrid.Columns.GridColumn colWorkflowStage;
        private DevExpress.XtraGrid.Columns.GridColumn colReferrerName;
        private DevExpress.XtraGrid.Columns.GridColumn colIsCancelled;
        private DevExpress.XtraGrid.Columns.GridColumn colSex;
        private DevExpress.XtraGrid.Columns.GridColumn colNetPayable;
        private DevExpress.XtraGrid.Columns.GridColumn colDueAmount;
        private DevExpress.XtraGrid.Columns.GridColumn colPaidAmount;
        private DevExpress.XtraGrid.Columns.GridColumn colDiscountAmount;
        private System.Windows.Forms.BindingSource activeResultBundleSliceBindingSource;
        private DevExpress.XtraGrid.Columns.GridColumn colId;
        private DevExpress.XtraGrid.Columns.GridColumn colDisplayTitle;
        private DevExpress.XtraGrid.Columns.GridColumn colComponentLabTests;
        private DevExpress.XtraGrid.Columns.GridColumn colWorkflowStageB;
        private DevExpress.XtraGrid.Columns.GridColumn colStaffName;
        private DevExpress.XtraGrid.Columns.GridColumn colLastUpdated;
        private DevExpress.XtraBars.BarButtonItem btnPrintInvoice;
        private DevExpress.XtraBars.BarButtonItem btnPrintReqFoil;
        private DevExpress.XtraBars.BarButtonItem btnPatientDemographicsEdit;
        private DevExpress.XtraBars.BarButtonItem btnEditLabOrder;
        private DevExpress.XtraBars.BarButtonItem btnDiscount;
        private DevExpress.XtraBars.BarButtonItem btnDiscountRebate;
        private DevExpress.XtraBars.BarButtonItem btnRefund;
        private DevExpress.XtraBars.BarButtonItem btnFacesheet;
        private DevExpress.XtraGrid.Columns.GridColumn colExternalSubOrder;
        private DevExpress.XtraBars.BarButtonItem btnAuditTrail;
        private DevExpress.XtraBars.BarButtonItem btnTxHistory;
        private DevExpress.XtraEditors.TextEdit txtPatientName;
        private DevExpress.XtraEditors.CheckEdit chkSearchBundleStage;
        private DevExpress.XtraEditors.CheckEdit chkSearchOrderId;
        private DevExpress.XtraEditors.CheckEdit chkSearchInvoiceId;
        private DevExpress.XtraEditors.CheckEdit chkSearchPatientName;
        private DevExpress.XtraEditors.CheckEdit chkSearchReferrer;
        private DevExpress.XtraEditors.LabelControl lblReferrer;
        private DevExpress.XtraEditors.SimpleButton btnSelectReferrer;
        private DevExpress.XtraBars.BarButtonItem btnClose;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController;
        private DevExpress.XtraBars.BarButtonItem btnCopyDemographic;
        private DevExpress.XtraEditors.TimeEdit dtTimeFrom;
        private DevExpress.XtraEditors.CheckEdit chkApplyTimeFilter;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.TimeEdit dtTimeTo;
        private DevExpress.XtraGrid.Columns.GridColumn colRefDisallow;
        private DevExpress.XtraGrid.Columns.GridColumn colRefUnknown;
        private System.Windows.Forms.SaveFileDialog saveFileDialog;
        private DevExpress.XtraEditors.SimpleButton btnResetCache;
        private DevExpress.XtraEditors.DropDownButton btnDropDown;
        private DevExpress.XtraBars.PopupMenu popupMenu;
        private DevExpress.XtraBars.BarButtonItem barButtonItem1;
        private DevExpress.XtraBars.BarButtonItem barButtonItem2;
        private DevExpress.XtraBars.BarButtonItem barButtonItem3;
        private DevExpress.XtraBars.BarButtonItem barButtonItem5;
        private DevExpress.XtraBars.BarButtonItem barButtonItem4;
        private DevExpress.XtraBars.BarButtonItem barButtonItem6;
        private DevExpress.XtraBars.BarButtonItem barButtonItem7;
        private DevExpress.XtraEditors.SimpleButton btnCopyDate;
        private DevExpress.XtraEditors.SimpleButton btnCorporate;
        private DevExpress.XtraEditors.SimpleButton btnAssocLab;
        private DevExpress.XtraEditors.LabelControl lblCorporate;
        private DevExpress.XtraEditors.CheckEdit chkSearchCorporate;
        private DevExpress.XtraEditors.LabelControl lblAssocLab;
        private DevExpress.XtraEditors.CheckEdit chkSearchAssocLab;
        private DevExpress.XtraEditors.PanelControl panelControl1;
        private DevExpress.XtraEditors.LabelControl lblDetailsCustomer;
        private DevExpress.XtraEditors.LabelControl lblDetailsAffiliate;
        private DevExpress.XtraEditors.LabelControl lblDetailsCorporate;
        private DevExpress.XtraEditors.LabelControl lblDetailsAssocLabTracking;
        private DevExpress.XtraEditors.LabelControl lblDetailsAssocLab;
        private DevExpress.XtraEditors.LabelControl labelControl9;
        private DevExpress.XtraEditors.LabelControl labelControl8;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.CheckEdit chkSearchPhone;
        private DevExpress.XtraEditors.TextEdit txtPhoneNumber;
        private DevExpress.XtraEditors.CheckEdit chkSearchCustomer;
        private DevExpress.XtraEditors.TextEdit txtCustomerUpin;
    }
}