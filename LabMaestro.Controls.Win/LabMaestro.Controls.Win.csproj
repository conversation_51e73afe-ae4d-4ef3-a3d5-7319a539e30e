<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{DED48497-1198-41C9-9640-E7CC64A61A80}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>LabMaestro.Controls.Win</RootNamespace>
    <AssemblyName>LabMaestro.Controls.Win</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
    <DontImportPostSharp>True</DontImportPostSharp>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <SolutionDir Condition="$(SolutionDir) == '' Or $(SolutionDir) == '*Undefined*'">..\LabMaestro\</SolutionDir>
    <LangVersion>latestmajor</LangVersion>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <UseWinFormsOutOfProcDesigner>True</UseWinFormsOutOfProcDesigner>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>
    </DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <PlatformTarget>x86</PlatformTarget>
    <UseWinFormsOutOfProcDesigner>True</UseWinFormsOutOfProcDesigner>
  </PropertyGroup>
  <PropertyGroup>
    <SignAssembly>false</SignAssembly>
  </PropertyGroup>
  <PropertyGroup>
    <AssemblyOriginatorKeyFile>labmaestro.snk</AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AutomaticUpdater">
      <HintPath>..\libs\AutomaticUpdater.dll</HintPath>
    </Reference>
    <Reference Include="C1.LiveLinq.4">
      <HintPath>..\libs\C1.LiveLinq.4.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.BonusSkins.v24.2, Version=24.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Charts.v24.2.Core, Version=24.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Drawing.v24.2, Version=24.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Data.v24.2, Version=24.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Office.v24.2.Core, Version=24.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Pdf.v24.2.Core, Version=24.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.RichEdit.v24.2.Export, Version=24.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Printing.v24.2.Core, Version=24.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Docs.v24.2, Version=24.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.RichEdit.v24.2.Core, Version=24.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Sparkline.v24.2.Core, Version=24.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Data.Desktop.v24.2, Version=24.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Utils.v24.2, Version=24.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraBars.v24.2, Version=24.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraCharts.v24.2, Version=24.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraCharts.v24.2.UI, Version=24.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraCharts.v24.2.Wizard, Version=24.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraEditors.v24.2, Version=24.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraPrinting.v24.2, Version=24.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraGrid.v24.2, Version=24.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraLayout.v24.2, Version=24.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraNavBar.v24.2, Version=24.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Images.v24.2, Version=24.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraRichEdit.v24.2, Version=24.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="Stimulsoft.Report">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\libs\Stimulsoft.Report.dll</HintPath>
    </Reference>
    <Reference Include="Syncfusion.Grid.Base, Version=30.1462.37.0, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89, processorArchitecture=MSIL" />
    <Reference Include="Syncfusion.Grid.Windows, Version=30.1462.37.0, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89, processorArchitecture=MSIL" />
    <Reference Include="Syncfusion.GridConverter.Windows, Version=30.1462.37.0, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89, processorArchitecture=MSIL" />
    <Reference Include="Syncfusion.GridHelperClasses.Windows, Version=30.1462.37.0, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89, processorArchitecture=MSIL" />
    <Reference Include="Syncfusion.Shared.Base, Version=30.1462.37.0, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89, processorArchitecture=MSIL" />
    <Reference Include="Syncfusion.Shared.Windows, Version=30.1462.37.0, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89, processorArchitecture=MSIL" />
    <Reference Include="Syncfusion.Tools.Base, Version=30.1462.37.0, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89, processorArchitecture=MSIL" />
    <Reference Include="Syncfusion.Tools.Windows, Version=30.1462.37.0, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89, processorArchitecture=MSIL" />
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.Linq" />
    <Reference Include="System.Design" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Drawing.Design" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Runtime.Serialization.Formatters.Soap" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
    <Reference Include="Xceed.Editors.v2.6, Version=2.6.20076.12370, Culture=neutral, PublicKeyToken=ba83ff368b7563c6, processorArchitecture=MSIL" />
    <Reference Include="Xceed.Grid.v4.3, Version=4.3.20076.12370, Culture=neutral, PublicKeyToken=ba83ff368b7563c6, processorArchitecture=MSIL" />
    <Reference Include="Xceed.UI.v1.4, Version=1.4.20076.12370, Culture=neutral, PublicKeyToken=ba83ff368b7563c6, processorArchitecture=MSIL" />
    <Reference Include="Xceed.Validation.v1.3, Version=1.3.20076.12370, Culture=neutral, PublicKeyToken=ba83ff368b7563c6, processorArchitecture=MSIL" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Accounts\AccountsDateReferrerCategorySelectionDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Accounts\AccountsDateReferrerCategorySelectionDialog.Designer.cs">
      <DependentUpon>AccountsDateReferrerCategorySelectionDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="Accounts\DiscountOverageReportDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Accounts\DiscountOverageReportDialog.Designer.cs">
      <DependentUpon>DiscountOverageReportDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="Accounts\DuesRegisterReportDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Accounts\DuesRegisterReportDialog.Designer.cs">
      <DependentUpon>DuesRegisterReportDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="Accounts\IncomeStatementReportDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Accounts\IncomeStatementReportDialog.Designer.cs">
      <DependentUpon>IncomeStatementReportDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="Accounts\ReceivablesSummaryReportDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Accounts\ReceivablesSummaryReportDialog.Designer.cs">
      <DependentUpon>ReceivablesSummaryReportDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="Accounts\ReportDateRangeSelectorDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Accounts\ReportDateRangeSelectorDialog.Designer.cs">
      <DependentUpon>ReportDateRangeSelectorDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="BackOffice\AdminBackendDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="BackOffice\AdminBackendDialog.Designer.cs">
      <DependentUpon>AdminBackendDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="BackOffice\AppFaultsBrowserDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="BackOffice\AppFaultsBrowserDialog.Designer.cs">
      <DependentUpon>AppFaultsBrowserDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="BackOffice\BIUtilizationReportDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="BackOffice\BIUtilizationReportDialog.Designer.cs">
      <DependentUpon>BIUtilizationReportDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="BackOffice\CollatorManifestReportDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="BackOffice\CollatorManifestReportDialog.Designer.cs">
      <DependentUpon>CollatorManifestReportDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="BackOffice\LabTestUtilizationReportDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="BackOffice\LabTestUtilizationReportDialog.Designer.cs">
      <DependentUpon>LabTestUtilizationReportDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="BackOffice\ManagerDashboard.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="BackOffice\ManagerDashboard.Designer.cs">
      <DependentUpon>ManagerDashboard.cs</DependentUpon>
    </Compile>
    <Compile Include="BackOffice\MessageQueueToolDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="BackOffice\MessageQueueToolDialog.Designer.cs">
      <DependentUpon>MessageQueueToolDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="BackOffice\StaffPerformanceReportDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="BackOffice\StaffPerformanceReportDialog.Designer.cs">
      <DependentUpon>StaffPerformanceReportDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="BackOffice\UsersShiftHistoryDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="BackOffice\UsersShiftHistoryDialog.Designer.cs">
      <DependentUpon>UsersShiftHistoryDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="BackOffice\WorkstationRegistryViewerDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="BackOffice\WorkstationRegistryViewerDialog.Designer.cs">
      <DependentUpon>WorkstationRegistryViewerDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="Catalog\AppReportTemplatesEditorDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Catalog\AppReportTemplatesEditorDialog.Designer.cs">
      <DependentUpon>AppReportTemplatesEditorDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="Catalog\DiscountReferralEditorDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Catalog\DiscountReferralEditorDialog.Designer.cs">
      <DependentUpon>DiscountReferralEditorDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="Catalog\LabReportHeadersEditorDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Catalog\LabReportHeadersEditorDialog.Designer.cs">
      <DependentUpon>LabReportHeadersEditorDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="Catalog\LabsEditorDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Catalog\LabsEditorDialog.Designer.cs">
      <DependentUpon>LabsEditorDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="Catalog\LabTestQuickInfo.cs" />
    <Compile Include="Catalog\LabTestQuickSelectionDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Catalog\LabTestQuickSelectionDialog.Designer.cs">
      <DependentUpon>LabTestQuickSelectionDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="Catalog\NewTestDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Catalog\NewTestDialog.Designer.cs">
      <DependentUpon>NewTestDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="Catalog\PermissionsEditorDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Catalog\PermissionsEditorDialog.Designer.cs">
      <DependentUpon>PermissionsEditorDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="Catalog\PhysicianInfo.cs" />
    <Compile Include="Catalog\ReferrerClassCategoryEditorDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Catalog\ReferrerClassCategoryEditorDialog.Designer.cs">
      <DependentUpon>ReferrerClassCategoryEditorDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="Catalog\ReferrerClassCategorySelectorDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Catalog\ReferrerClassCategorySelectorDialog.Designer.cs">
      <DependentUpon>ReferrerClassCategorySelectorDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="Catalog\ReferrerEditorDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Catalog\ReferrerEditorDialog.Designer.cs">
      <DependentUpon>ReferrerEditorDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="Catalog\ReferrerCatalogBrowserDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Catalog\ReferrerCatalogBrowserDialog.Designer.cs">
      <DependentUpon>ReferrerCatalogBrowserDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="Catalog\ReferrerQuickSelectionDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Catalog\ReferrerQuickSelectionDialog.Designer.cs">
      <DependentUpon>ReferrerQuickSelectionDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="Catalog\ReferrersStagingAreaDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Catalog\ReferrersStagingAreaDialog.Designer.cs">
      <DependentUpon>ReferrersStagingAreaDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="Catalog\RolesEditorDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Catalog\RolesEditorDialog.Designer.cs">
      <DependentUpon>RolesEditorDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="Catalog\TemplateGroupsEditorDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Catalog\TemplateGroupsEditorDialog.Designer.cs">
      <DependentUpon>TemplateGroupsEditorDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="Catalog\TemplateReportsEditorDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Catalog\TemplateReportsEditorDialog.Designer.cs">
      <DependentUpon>TemplateReportsEditorDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="Catalog\SimpleTestAddEditDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Catalog\SimpleTestAddEditDialog.Designer.cs">
      <DependentUpon>SimpleTestAddEditDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="Catalog\TestCatalogEditorDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Catalog\TestCatalogEditorDialog.Designer.cs">
      <DependentUpon>TestCatalogEditorDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="Catalog\TestEditDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Catalog\TestEditDialog.Designer.cs">
      <DependentUpon>TestEditDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="Catalog\TestParamBulkEditorDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Catalog\TestParamBulkEditorDialog.Designer.cs">
      <DependentUpon>TestParamBulkEditorDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="Catalog\TestSelectionDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Catalog\TestSelectionDialog.Designer.cs">
      <DependentUpon>TestSelectionDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="Catalog\UserEditorDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Catalog\UserEditorDialog.Designer.cs">
      <DependentUpon>UserEditorDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="Catalog\UsersCatalogEditorDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Catalog\UsersCatalogEditorDialog.Designer.cs">
      <DependentUpon>UsersCatalogEditorDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="Common\ChangePasswordDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Common\ChangePasswordDialog.Designer.cs">
      <DependentUpon>ChangePasswordDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="Common\DateRangeSelectDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Common\DateRangeSelectDialog.Designer.cs">
      <DependentUpon>DateRangeSelectDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="Common\InputDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Common\InputDialog.Designer.cs">
      <DependentUpon>InputDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="Common\RequestingLabsDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Common\RequestingLabsDialog.Designer.cs">
      <DependentUpon>RequestingLabsDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="CRM\CustomerCreateForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="CRM\CustomerCreateForm.Designer.cs">
      <DependentUpon>CustomerCreateForm.cs</DependentUpon>
    </Compile>
    <Compile Include="CRM\CustomerInvoicesForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="CRM\CustomerInvoicesForm.Designer.cs">
      <DependentUpon>CustomerInvoicesForm.cs</DependentUpon>
    </Compile>
    <Compile Include="CRM\CustomerSearchForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="CRM\CustomerSearchForm.Designer.cs">
      <DependentUpon>CustomerSearchForm.cs</DependentUpon>
    </Compile>
    <Compile Include="CRM\NewCustomerConfirmationForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="CRM\NewCustomerConfirmationForm.Designer.cs">
      <DependentUpon>NewCustomerConfirmationForm.cs</DependentUpon>
    </Compile>
    <Compile Include="LabOrder\AccountsReceivableDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LabOrder\AccountsReceivableDialog.Designer.cs">
      <DependentUpon>AccountsReceivableDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="LabOrder\AssociatedLabDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LabOrder\AssociatedLabDialog.Designer.cs">
      <DependentUpon>AssociatedLabDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="LabOrder\AuditTrailViewerDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LabOrder\AuditTrailViewerDialog.Designer.cs">
      <DependentUpon>AuditTrailViewerDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="LabOrder\BatterySelectionDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LabOrder\BatterySelectionDialog.Designer.cs">
      <DependentUpon>BatterySelectionDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="LabOrder\BulkDispatchDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LabOrder\BulkDispatchDialog.Designer.cs">
      <DependentUpon>BulkDispatchDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="LabOrder\CollationConfirmDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LabOrder\CollationConfirmDialog.Designer.cs">
      <DependentUpon>CollationConfirmDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="LabOrder\CollationDispatchDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LabOrder\CollationDispatchDialog.Designer.cs">
      <DependentUpon>CollationDispatchDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="LabOrder\CollationDispatchDialogType.cs" />
    <Compile Include="LabOrder\CollationFilterDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LabOrder\CollationFilterDialog.Designer.cs">
      <DependentUpon>CollationFilterDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="LabOrder\ComponentLabTestsSlice.cs" />
    <Compile Include="LabOrder\DiscountCalculatorDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LabOrder\DiscountCalculatorDialog.Designer.cs">
      <DependentUpon>DiscountCalculatorDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="LabOrder\InvoiceTransactionsViewerDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LabOrder\InvoiceTransactionsViewerDialog.Designer.cs">
      <DependentUpon>InvoiceTransactionsViewerDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="LabOrder\LabOrderDemographicsEditDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LabOrder\LabOrderDemographicsEditDialog.Designer.cs">
      <DependentUpon>LabOrderDemographicsEditDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="LabOrder\LabOrderEditDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LabOrder\LabOrderEditDialog.Designer.cs">
      <DependentUpon>LabOrderEditDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="LabOrder\OrderBillableItemDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LabOrder\OrderBillableItemDialog.Designer.cs">
      <DependentUpon>OrderBillableItemDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="Common\CaptchaDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Common\CaptchaDialog.Designer.cs">
      <DependentUpon>CaptchaDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="Catalog\BillableItemsCatalogEditorDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Catalog\BillableItemsCatalogEditorDialog.Designer.cs">
      <DependentUpon>BillableItemsCatalogEditorDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="Common\ControlEditMode.cs" />
    <Compile Include="Common\DataBindHelper.cs" />
    <Compile Include="LabOrder\DateTimeDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LabOrder\DateTimeDialog.Designer.cs">
      <DependentUpon>DateTimeDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="InvoiceSearchForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="InvoiceSearchForm.Designer.cs">
      <DependentUpon>InvoiceSearchForm.cs</DependentUpon>
    </Compile>
    <Compile Include="LabOrder\LabOrderDialogAccessType.cs" />
    <Compile Include="LabOrder\OrderDemographicHistoryBrowserDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LabOrder\OrderDemographicHistoryBrowserDialog.Designer.cs">
      <DependentUpon>OrderDemographicHistoryBrowserDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="LabOrder\PatientSelectForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LabOrder\PatientSelectForm.Designer.cs">
      <DependentUpon>PatientSelectForm.cs</DependentUpon>
    </Compile>
    <Compile Include="LabOrder\PreviewPrintDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LabOrder\PreviewPrintDialog.Designer.cs">
      <DependentUpon>PreviewPrintDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="LabOrder\ReferralAllowDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LabOrder\ReferralAllowDialog.Designer.cs">
      <DependentUpon>ReferralAllowDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="LabOrder\RefundDiscountDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LabOrder\RefundDiscountDialog.Designer.cs">
      <DependentUpon>RefundDiscountDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="LabOrder\ResultBundleAuditTrailViewerDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LabOrder\ResultBundleAuditTrailViewerDialog.Designer.cs">
      <DependentUpon>ResultBundleAuditTrailViewerDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="LabOrder\TransactionSharedTypes.cs" />
    <Compile Include="Common\LoginDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Common\LoginDialog.Designer.cs">
      <DependentUpon>LoginDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="Common\MessageDlg.cs" />
    <Compile Include="LabOrder\LabOrderDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LabOrder\LabOrderDialog.Designer.cs">
      <DependentUpon>LabOrderDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="LabOrder\OrdersOnHoldDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LabOrder\OrdersOnHoldDialog.Designer.cs">
      <DependentUpon>OrdersOnHoldDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="LaunchPadAccessFlag.cs" />
    <Compile Include="LaunchPadForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LaunchPadForm.Designer.cs">
      <DependentUpon>LaunchPadForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Marketing\ReferralCriteriaDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Marketing\ReferralCriteriaDialog.Designer.cs">
      <DependentUpon>ReferralCriteriaDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="Marketing\ReferralViewerDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Marketing\ReferralViewerDialog.Designer.cs">
      <DependentUpon>ReferralViewerDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="ResourceIcons.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ResourceIcons.resx</DependentUpon>
    </Compile>
    <Compile Include="ResultEntry\BloodDifferentialsDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ResultEntry\BloodDifferentialsDialog.Designer.cs">
      <DependentUpon>BloodDifferentialsDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="ResultEntry\ResultItemMetadataChangeDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ResultEntry\ResultItemMetadataChangeDialog.Designer.cs">
      <DependentUpon>ResultItemMetadataChangeDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="ResultEntry\DiscreteResultControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ResultEntry\DiscreteResultControl.Designer.cs">
      <DependentUpon>DiscreteResultControl.cs</DependentUpon>
    </Compile>
    <Compile Include="ResultEntry\ResultEditForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ResultEntry\ResultEditForm.Designer.cs">
      <DependentUpon>ResultEditForm.cs</DependentUpon>
    </Compile>
    <Compile Include="ResultEntry\TemplateResultControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ResultEntry\TemplateResultControl.Designer.cs">
      <DependentUpon>TemplateResultControl.cs</DependentUpon>
    </Compile>
    <Compile Include="LabOrder\TransactionDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LabOrder\TransactionDialog.Designer.cs">
      <DependentUpon>TransactionDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="ShiftManagement\AdditionalBalanceEditorDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ShiftManagement\AdditionalBalanceEditorDialog.Designer.cs">
      <DependentUpon>AdditionalBalanceEditorDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="ShiftManagement\ShiftEndDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ShiftManagement\ShiftEndDialog.Designer.cs">
      <DependentUpon>ShiftEndDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="ShiftManagement\ShiftSearchDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ShiftManagement\ShiftSearchDialog.Designer.cs">
      <DependentUpon>ShiftSearchDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="ShiftManagement\ShiftStaffSelectionDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ShiftManagement\ShiftStaffSelectionDialog.Designer.cs">
      <DependentUpon>ShiftStaffSelectionDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="ShiftManagement\ShiftStartDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ShiftManagement\ShiftStartDialog.Designer.cs">
      <DependentUpon>ShiftStartDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="ShiftManagement\ShiftDetailsDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ShiftManagement\ShiftDetailsDialog.Designer.cs">
      <DependentUpon>ShiftDetailsDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="Common\SplashScreenControl.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Common\SplashScreenControl.Designer.cs">
      <DependentUpon>SplashScreenControl.cs</DependentUpon>
    </Compile>
    <Compile Include="Common\WaitFormControl.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Common\WaitFormControl.Designer.cs">
      <DependentUpon>WaitFormControl.cs</DependentUpon>
    </Compile>
    <Compile Include="Common\WinUtils.cs" />
    <Compile Include="StartupSplashScreen.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="StartupSplashScreen.Designer.cs">
      <DependentUpon>StartupSplashScreen.cs</DependentUpon>
    </Compile>
    <Compile Include="Utils\TransactionsHelper.cs" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Accounts\AccountsDateReferrerCategorySelectionDialog.resx">
      <DependentUpon>AccountsDateReferrerCategorySelectionDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Accounts\DiscountOverageReportDialog.resx">
      <DependentUpon>DiscountOverageReportDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Accounts\DuesRegisterReportDialog.resx">
      <DependentUpon>DuesRegisterReportDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Accounts\IncomeStatementReportDialog.resx">
      <DependentUpon>IncomeStatementReportDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Accounts\ReceivablesSummaryReportDialog.resx">
      <DependentUpon>ReceivablesSummaryReportDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Accounts\ReportDateRangeSelectorDialog.resx">
      <DependentUpon>ReportDateRangeSelectorDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="BackOffice\AdminBackendDialog.resx">
      <DependentUpon>AdminBackendDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="BackOffice\AppFaultsBrowserDialog.resx">
      <DependentUpon>AppFaultsBrowserDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="BackOffice\BIUtilizationReportDialog.resx">
      <DependentUpon>BIUtilizationReportDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="BackOffice\CollatorManifestReportDialog.resx">
      <DependentUpon>CollatorManifestReportDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="BackOffice\LabTestUtilizationReportDialog.resx">
      <DependentUpon>LabTestUtilizationReportDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="BackOffice\ManagerDashboard.resx">
      <DependentUpon>ManagerDashboard.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="BackOffice\MessageQueueToolDialog.resx">
      <DependentUpon>MessageQueueToolDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="BackOffice\StaffPerformanceReportDialog.resx">
      <DependentUpon>StaffPerformanceReportDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="BackOffice\UsersShiftHistoryDialog.resx">
      <DependentUpon>UsersShiftHistoryDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="BackOffice\WorkstationRegistryViewerDialog.resx">
      <DependentUpon>WorkstationRegistryViewerDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Catalog\AppReportTemplatesEditorDialog.resx">
      <DependentUpon>AppReportTemplatesEditorDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Catalog\DiscountReferralEditorDialog.resx">
      <DependentUpon>DiscountReferralEditorDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Catalog\LabReportHeadersEditorDialog.resx">
      <DependentUpon>LabReportHeadersEditorDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Catalog\LabsEditorDialog.resx">
      <DependentUpon>LabsEditorDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Catalog\LabTestQuickSelectionDialog.resx">
      <DependentUpon>LabTestQuickSelectionDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Catalog\NewTestDialog.resx">
      <DependentUpon>NewTestDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Catalog\PermissionsEditorDialog.resx">
      <DependentUpon>PermissionsEditorDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Catalog\ReferrerClassCategoryEditorDialog.resx">
      <DependentUpon>ReferrerClassCategoryEditorDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Catalog\ReferrerClassCategorySelectorDialog.resx">
      <DependentUpon>ReferrerClassCategorySelectorDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Catalog\ReferrerEditorDialog.resx">
      <DependentUpon>ReferrerEditorDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Catalog\ReferrerCatalogBrowserDialog.resx">
      <DependentUpon>ReferrerCatalogBrowserDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Catalog\ReferrerQuickSelectionDialog.resx">
      <DependentUpon>ReferrerQuickSelectionDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Catalog\ReferrersStagingAreaDialog.resx">
      <DependentUpon>ReferrersStagingAreaDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Catalog\RolesEditorDialog.resx">
      <DependentUpon>RolesEditorDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Catalog\TemplateGroupsEditorDialog.resx">
      <DependentUpon>TemplateGroupsEditorDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Catalog\TemplateReportsEditorDialog.resx">
      <DependentUpon>TemplateReportsEditorDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Catalog\SimpleTestAddEditDialog.resx">
      <DependentUpon>SimpleTestAddEditDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Catalog\TestCatalogEditorDialog.resx">
      <DependentUpon>TestCatalogEditorDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Catalog\TestEditDialog.resx">
      <DependentUpon>TestEditDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Catalog\TestParamBulkEditorDialog.resx">
      <DependentUpon>TestParamBulkEditorDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Catalog\TestSelectionDialog.resx">
      <DependentUpon>TestSelectionDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Catalog\UserEditorDialog.resx">
      <DependentUpon>UserEditorDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Catalog\UsersCatalogEditorDialog.resx">
      <DependentUpon>UsersCatalogEditorDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Common\ChangePasswordDialog.resx">
      <DependentUpon>ChangePasswordDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Common\DateRangeSelectDialog.resx">
      <DependentUpon>DateRangeSelectDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Common\InputDialog.resx">
      <DependentUpon>InputDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Common\RequestingLabsDialog.resx">
      <DependentUpon>RequestingLabsDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="CRM\CustomerCreateForm.resx">
      <DependentUpon>CustomerCreateForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="CRM\CustomerInvoicesForm.resx">
      <DependentUpon>CustomerInvoicesForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="CRM\CustomerSearchForm.resx">
      <DependentUpon>CustomerSearchForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="CRM\NewCustomerConfirmationForm.resx">
      <DependentUpon>NewCustomerConfirmationForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LabOrder\AccountsReceivableDialog.resx">
      <DependentUpon>AccountsReceivableDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LabOrder\AssociatedLabDialog.resx">
      <DependentUpon>AssociatedLabDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LabOrder\AuditTrailViewerDialog.resx">
      <DependentUpon>AuditTrailViewerDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LabOrder\BatterySelectionDialog.resx">
      <DependentUpon>BatterySelectionDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LabOrder\BulkDispatchDialog.resx">
      <DependentUpon>BulkDispatchDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LabOrder\CollationConfirmDialog.resx">
      <DependentUpon>CollationConfirmDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LabOrder\CollationDispatchDialog.resx">
      <DependentUpon>CollationDispatchDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LabOrder\CollationFilterDialog.resx">
      <DependentUpon>CollationFilterDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LabOrder\DiscountCalculatorDialog.resx">
      <DependentUpon>DiscountCalculatorDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LabOrder\InvoiceTransactionsViewerDialog.resx">
      <DependentUpon>InvoiceTransactionsViewerDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LabOrder\LabOrderDemographicsEditDialog.resx">
      <DependentUpon>LabOrderDemographicsEditDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LabOrder\LabOrderEditDialog.resx">
      <DependentUpon>LabOrderEditDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LabOrder\OrderBillableItemDialog.resx">
      <DependentUpon>OrderBillableItemDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Common\CaptchaDialog.resx">
      <DependentUpon>CaptchaDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Catalog\BillableItemsCatalogEditorDialog.resx">
      <DependentUpon>BillableItemsCatalogEditorDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LabOrder\DateTimeDialog.resx">
      <DependentUpon>DateTimeDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="InvoiceSearchForm.resx">
      <DependentUpon>InvoiceSearchForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LabOrder\OrderDemographicHistoryBrowserDialog.resx">
      <DependentUpon>OrderDemographicHistoryBrowserDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LabOrder\PatientSelectForm.resx">
      <DependentUpon>PatientSelectForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LabOrder\PreviewPrintDialog.resx">
      <DependentUpon>PreviewPrintDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LabOrder\ReferralAllowDialog.resx">
      <DependentUpon>ReferralAllowDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LabOrder\RefundDiscountDialog.resx">
      <DependentUpon>RefundDiscountDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Common\LoginDialog.en.resx">
      <DependentUpon>LoginDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Common\LoginDialog.resx">
      <DependentUpon>LoginDialog.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="LabOrder\LabOrderDialog.resx">
      <DependentUpon>LabOrderDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LabOrder\OrdersOnHoldDialog.resx">
      <DependentUpon>OrdersOnHoldDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LabOrder\ResultBundleAuditTrailViewerDialog.resx">
      <DependentUpon>ResultBundleAuditTrailViewerDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LaunchPadForm.resx">
      <DependentUpon>LaunchPadForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Marketing\ReferralCriteriaDialog.resx">
      <DependentUpon>ReferralCriteriaDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Marketing\ReferralViewerDialog.resx">
      <DependentUpon>ReferralViewerDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\licenses.licx" />
    <EmbeddedResource Include="ResourceIcons.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>ResourceIcons.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="ResultEntry\BloodDifferentialsDialog.resx">
      <DependentUpon>BloodDifferentialsDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ResultEntry\ResultItemMetadataChangeDialog.resx">
      <DependentUpon>ResultItemMetadataChangeDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ResultEntry\DiscreteResultControl.resx">
      <DependentUpon>DiscreteResultControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ResultEntry\ResultEditForm.resx">
      <DependentUpon>ResultEditForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="ResultEntry\TemplateResultControl.resx">
      <DependentUpon>TemplateResultControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LabOrder\TransactionDialog.resx">
      <DependentUpon>TransactionDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="ShiftManagement\AdditionalBalanceEditorDialog.resx">
      <DependentUpon>AdditionalBalanceEditorDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ShiftManagement\ShiftEndDialog.resx">
      <DependentUpon>ShiftEndDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ShiftManagement\ShiftSearchDialog.resx">
      <DependentUpon>ShiftSearchDialog.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="ShiftManagement\ShiftStaffSelectionDialog.resx">
      <DependentUpon>ShiftStaffSelectionDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ShiftManagement\ShiftStartDialog.resx">
      <DependentUpon>ShiftStartDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ShiftManagement\ShiftDetailsDialog.resx">
      <DependentUpon>ShiftDetailsDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Common\SplashScreenControl.resx">
      <DependentUpon>SplashScreenControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Common\WaitFormControl.resx">
      <DependentUpon>WaitFormControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="StartupSplashScreen.resx">
      <DependentUpon>StartupSplashScreen.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\LabMaestro.BusinessLogic\LabMaestro.BusinessLogic.csproj">
      <Project>{A13D1C5D-3BBF-4E7D-88FC-47BA5DCCCD83}</Project>
      <Name>LabMaestro.BusinessLogic</Name>
    </ProjectReference>
    <ProjectReference Include="..\LabMaestro.Domain\LabMaestro.Domain.csproj">
      <Project>{72642B75-E640-41A5-A394-FB8E95B17599}</Project>
      <Name>LabMaestro.Domain</Name>
    </ProjectReference>
    <ProjectReference Include="..\LabMaestro.Infrastructure.Client\LabMaestro.Infrastructure.Client.csproj">
      <Project>{0F4FD7B4-6342-44FA-B190-FF9F482E8798}</Project>
      <Name>LabMaestro.Infrastructure.Client</Name>
    </ProjectReference>
    <ProjectReference Include="..\LabMaestro.Infrastructure.Search\LabMaestro.Infrastructure.Search.csproj">
      <Project>{ABB2C9E0-BF9C-4DCF-951F-D8BA032A2E5C}</Project>
      <Name>LabMaestro.Infrastructure.Search</Name>
    </ProjectReference>
    <ProjectReference Include="..\LabMaestro.Printing\LabMaestro.Printing.csproj">
      <Project>{32d5bffa-5141-461e-985f-58a219660599}</Project>
      <Name>LabMaestro.Printing</Name>
    </ProjectReference>
    <ProjectReference Include="..\LabMaestro.Shared\LabMaestro.Shared.csproj">
      <Project>{B15584A4-1EA1-48AA-A823-29307BB8D16A}</Project>
      <Name>LabMaestro.Shared</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="labmaestro.snk" />
    <None Include="Properties\DataSources\ComponentLabTestsInResultBundle.datasource" />
    <None Include="Properties\DataSources\ComponentLabTestsSlice.datasource" />
    <None Include="Properties\DataSources\LabMaestro.BusinessLogic.ActiveResultBundleSlice.datasource" />
    <None Include="Properties\DataSources\LabMaestro.BusinessLogic.AuditEventSlice.datasource" />
    <None Include="Properties\DataSources\LabMaestro.BusinessLogic.BulkDispatchableResultBundleInfo.datasource" />
    <None Include="Properties\DataSources\LabMaestro.BusinessLogic.LabOrderDemographicInfoSlice.datasource" />
    <None Include="Properties\DataSources\LabMaestro.Domain.ActiveLabOrderSearchResultSlice.datasource" />
    <None Include="Properties\DataSources\LabMaestro.Domain.AppReportTemplateSlice.datasource" />
    <None Include="Properties\DataSources\LabMaestro.Domain.AuditTrailSlice.datasource" />
    <None Include="Properties\DataSources\LabMaestro.Domain.ReferrerCategorySlice.datasource" />
    <None Include="Properties\DataSources\LabMaestro.Domain.WorkShift.datasource" />
    <None Include="Properties\DataSources\LabMaestro.Domain.WorkshiftSearchResultSlice.datasource" />
    <None Include="Properties\DataSources\LabMaestro.Infrastructure.Search.ReferrerSearchRec.datasource" />
    <None Include="Properties\DataSources\LabTestQuickInfo.datasource" />
    <None Include="Properties\DataSources\LabTestQuickSelectionDialog.LabTestInfo.datasource" />
    <None Include="Properties\DataSources\ResultBundleAuditTrailViewerDialog.ResultBundleAuditTrailInfo.datasource" />
    <None Include="Properties\DataSources\ShiftSearchDialog.ShiftDetailsInfo.datasource" />
    <None Include="Resources\LoginFormLogo.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\calculator_16x16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\cancel_16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\exit_16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\tick_16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\flag_green.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\flag_red.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\key_16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\change_password.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\print.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\calendar.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\search_16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\date_next.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\list_accept.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\clear.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\account.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\coins.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Counter.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\new_lab_order\catalog16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\new_lab_order\physician16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\reload16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\cash.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\calculator-arrow_16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\arrow-return-left.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\money.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\tick_small.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\blue_bulb.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\green_yes.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\red_element_fire.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\star_yellow.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\preview16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\new_lab_order\edit16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\save16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\open16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\print2.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\print-all24.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\quickprint24.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\print24.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\preview24.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\mail_all24.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\mail24.png" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.5">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4.5 %28x86 and x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1 Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\ok_24.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\new_16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\done_square_16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\verified_16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\alert-triangle-red_24.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\arrow-down16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\arrow-up16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\new16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\add_16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\delete_16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\text_bold_16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\text_italics_16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\text_underline_16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\import_16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\copy_16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\question_blue_16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\tick_shield_16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\export_16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\calendar-export_16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\report_plus_16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\money_add16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\money_delete16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\money16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\table_money16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\coins_in_hand16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\coin_stack16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\money_coin16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\list_add16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\cancel2_16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\invoice16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\invoice_coin16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\order-new.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\lb_order-search_48.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\lp_cashier-coins_48.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\lp_report-design_48.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\lp_report-lock_48.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\lp_report-ok_48.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\lp_report-mail_48.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\lp_report-print_48.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\lp_business_report-print_48.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\lp_table_customer-delete_48.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\lp_table_customer-add_48.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\lp_table_customer-delay_48.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\clock-user.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\clock-add.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\clock-delete.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\1371578912_graphic-design.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\1371579180_full-time.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\1371579197_bank.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\1371579182_world.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\1371579171_user.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\lp_employees-delay_48.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\lp_employees-coins_48.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\lp_factory-coins_48.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\lp_address_book-coins_48.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\lp_address_book-customer_48.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\lp_employees-banknote_48.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\lp_inventory-banknote_48.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\lp_money-query_48.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\lp_invoice-to_cash_48.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\lp_money_coins-to_bank_48.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\1371662505_logout.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\lp_address_book-add2_48.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\facesheet_16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\document_text_accept.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\print_b_16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\list_edit_16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\group_edit_16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\male_16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\female_16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\questionb_16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\truck_arrow_16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\people_16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\list_information_16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\search_32.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\auto_update_16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\LabMaestro-Login.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\LabMaestro-Splash.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\demo_copy_16.png.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\demo_paste_16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\pdf_16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\rtf_16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\word_16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\report_go_16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\female_24.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\doctor_happy24.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\circle-arrow_24.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\minimize_dark_arrow_16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\down_arrow16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\laboratory_48.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\package_utilities_24.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\heart_toolbar_24.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\excel16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\table-import_16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\client_list24.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\run_application_48.png" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="AutoMapper">
      <Version>2.2.1</Version>
    </PackageReference>
    <PackageReference Include="CuttingEdge.Conditions">
      <Version>1.2.0</Version>
    </PackageReference>
    <PackageReference Include="FluentDateTime">
      <Version>3.0.0</Version>
    </PackageReference>
    <PackageReference Include="FluentValidation">
      <Version>11.11.0</Version>
    </PackageReference>
    <PackageReference Include="IdeaBlade.DevForce.Core">
      <Version>7.5.5</Version>
    </PackageReference>
    <PackageReference Include="SmartInspect">
      <Version>3.5.0.58</Version>
    </PackageReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="..\packages\PostSharp.2.1.7.28\tools\PostSharp.targets" Condition="Exists('..\packages\PostSharp.2.1.7.28\tools\PostSharp.targets')" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>