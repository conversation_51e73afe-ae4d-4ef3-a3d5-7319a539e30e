﻿namespace LabMaestro.Controls.Win
{
    partial class ResultEditForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ResultEditForm));
            Xceed.Grid.GradientMap gradientMap1 = new Xceed.Grid.GradientMap();
            Xceed.Grid.GradientStop gradientStop1 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientStop gradientStop2 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientMap gradientMap7 = new Xceed.Grid.GradientMap();
            Xceed.Grid.GradientStop gradientStop13 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientStop gradientStop14 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientMap gradientMap5 = new Xceed.Grid.GradientMap();
            Xceed.Grid.GradientStop gradientStop9 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientStop gradientStop10 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientMap gradientMap4 = new Xceed.Grid.GradientMap();
            Xceed.Grid.GradientStop gradientStop7 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientStop gradientStop8 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientMap gradientMap3 = new Xceed.Grid.GradientMap();
            Xceed.Grid.GradientStop gradientStop5 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientStop gradientStop6 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientMap gradientMap2 = new Xceed.Grid.GradientMap();
            Xceed.Grid.GradientStop gradientStop3 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientStop gradientStop4 = new Xceed.Grid.GradientStop();
            this.tabPagesControl = new Syncfusion.Windows.Forms.Tools.TabControlAdv();
            this.tabEntry = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabVerify = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabUnarchived = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.lbllabel5 = new System.Windows.Forms.Label();
            this.tabFinal = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.chkConfirmResultEdit = new DevExpress.XtraEditors.CheckEdit();
            this.btnPerformResultEdit = new DevExpress.XtraEditors.SimpleButton();
            this.pgOrderSearch = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.btnSearchOrderId = new DevExpress.XtraEditors.SimpleButton();
            this.label3 = new System.Windows.Forms.Label();
            this.txtOrderDate = new DevExpress.XtraEditors.DateEdit();
            this.txtOrderId = new DevExpress.XtraEditors.TextEdit();
            this.label2 = new System.Windows.Forms.Label();
            this.pgInvoice = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.btnSearchInvoiceId = new DevExpress.XtraEditors.SimpleButton();
            this.label1 = new System.Windows.Forms.Label();
            this.txtInvoiceId = new DevExpress.XtraEditors.TextEdit();
            this.tabInvoiceSearch = new Syncfusion.Windows.Forms.Tools.TabControlAdv();
            this.grdResultBundles = new Xceed.Grid.GridControl();
            this.dataRowTemplate1 = new Xceed.Grid.DataRow();
            this.visualGridElementStyle1 = new Xceed.Grid.VisualGridElementStyle();
            this.dataRowStyle1 = new Xceed.Grid.VisualGridElementStyle();
            this.columnManagerRow1 = new Xceed.Grid.ColumnManagerRow();
            this.barManager1 = new DevExpress.XtraBars.BarManager(this.components);
            this.toolBar = new DevExpress.XtraBars.Bar();
            this.btnExit = new DevExpress.XtraBars.BarButtonItem();
            this.btnEditDemographics = new DevExpress.XtraBars.BarButtonItem();
            this.barAndDockingController1 = new DevExpress.XtraBars.BarAndDockingController(this.components);
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.grdTestsOrdered = new Xceed.Grid.GridControl();
            this.dataRow1 = new Xceed.Grid.DataRow();
            this.visualGridElementStyle2 = new Xceed.Grid.VisualGridElementStyle();
            this.visualGridElementStyle3 = new Xceed.Grid.VisualGridElementStyle();
            this.columnManagerRow2 = new Xceed.Grid.ColumnManagerRow();
            this.grdAuditLog = new Xceed.Grid.GridControl();
            this.dataRow2 = new Xceed.Grid.DataRow();
            this.visualGridElementStyle4 = new Xceed.Grid.VisualGridElementStyle();
            this.visualGridElementStyle5 = new Xceed.Grid.VisualGridElementStyle();
            this.columnManagerRow3 = new Xceed.Grid.ColumnManagerRow();
            this.lbllabel4 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.btnPrint = new DevExpress.XtraEditors.SimpleButton();
            this.btnDowngradeWorkflow = new DevExpress.XtraEditors.SimpleButton();
            this.panelControl1 = new DevExpress.XtraEditors.PanelControl();
            this.lblSex = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.lblBookingDateTime = new DevExpress.XtraEditors.LabelControl();
            this.labBooking = new DevExpress.XtraEditors.LabelControl();
            this.lblRefdPhysician = new DevExpress.XtraEditors.LabelControl();
            this.labRefdBy = new DevExpress.XtraEditors.LabelControl();
            this.lblAgeDoBContent = new DevExpress.XtraEditors.LabelControl();
            this.lblAgeDoBLabel = new DevExpress.XtraEditors.LabelControl();
            this.lblPatientName = new DevExpress.XtraEditors.LabelControl();
            this.labName = new DevExpress.XtraEditors.LabelControl();
            this.lblInvoiceId = new DevExpress.XtraEditors.LabelControl();
            this.labInvoice = new DevExpress.XtraEditors.LabelControl();
            this.lblOrderNum = new DevExpress.XtraEditors.LabelControl();
            this.labOrder = new DevExpress.XtraEditors.LabelControl();
            this.btnRepeatProcedure = new DevExpress.XtraEditors.SimpleButton();
            ((System.ComponentModel.ISupportInitialize)(this.tabPagesControl)).BeginInit();
            this.tabPagesControl.SuspendLayout();
            this.tabUnarchived.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkConfirmResultEdit.Properties)).BeginInit();
            this.pgOrderSearch.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtOrderDate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtOrderDate.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtOrderId.Properties)).BeginInit();
            this.pgInvoice.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtInvoiceId.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabInvoiceSearch)).BeginInit();
            this.tabInvoiceSearch.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grdResultBundles)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataRowTemplate1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.columnManagerRow1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdTestsOrdered)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataRow1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.columnManagerRow2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdAuditLog)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataRow2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.columnManagerRow3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).BeginInit();
            this.panelControl1.SuspendLayout();
            this.SuspendLayout();
            // 
            // tabPagesControl
            // 
            this.tabPagesControl.BeforeTouchSize = new System.Drawing.Size(1030, 594);
            this.tabPagesControl.Controls.Add(this.tabEntry);
            this.tabPagesControl.Controls.Add(this.tabVerify);
            this.tabPagesControl.Controls.Add(this.tabUnarchived);
            this.tabPagesControl.Controls.Add(this.tabFinal);
            this.tabPagesControl.FocusOnTabClick = false;
            this.tabPagesControl.ItemSize = new System.Drawing.Size(112, 22);
            this.tabPagesControl.Location = new System.Drawing.Point(306, 29);
            this.tabPagesControl.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.tabPagesControl.Name = "tabPagesControl";
            this.tabPagesControl.Office2007ColorScheme = Syncfusion.Windows.Forms.Office2007Theme.Silver;
            this.tabPagesControl.Size = new System.Drawing.Size(1030, 594);
            this.tabPagesControl.TabIndex = 0;
            this.tabPagesControl.TabStyle = typeof(Syncfusion.Windows.Forms.Tools.TabRendererWorkbookMode);
            this.tabPagesControl.ThemeName = "TabRendererWorkbookMode";
            this.tabPagesControl.ThemesEnabled = true;
            this.tabPagesControl.UseMnemonic = false;
            this.tabPagesControl.SelectedIndexChanged += new System.EventHandler(this.tabPagesControl_SelectedIndexChanged);
            // 
            // tabEntry
            // 
            this.tabEntry.AutoSize = true;
            this.tabEntry.AutoSizeMode = System.Windows.Forms.AutoSizeMode.GrowAndShrink;
            this.tabEntry.Image = null;
            this.tabEntry.ImageSize = new System.Drawing.Size(16, 16);
            this.tabEntry.Location = new System.Drawing.Point(3, 27);
            this.tabEntry.Name = "tabEntry";
            this.tabEntry.ShowCloseButton = false;
            this.tabEntry.Size = new System.Drawing.Size(1023, 563);
            this.tabEntry.TabIndex = 1;
            this.tabEntry.Text = "Result Entry";
            this.tabEntry.ThemesEnabled = true;
            // 
            // tabVerify
            // 
            this.tabVerify.AutoSize = true;
            this.tabVerify.AutoSizeMode = System.Windows.Forms.AutoSizeMode.GrowAndShrink;
            this.tabVerify.Image = null;
            this.tabVerify.ImageSize = new System.Drawing.Size(16, 16);
            this.tabVerify.Location = new System.Drawing.Point(3, 27);
            this.tabVerify.Name = "tabVerify";
            this.tabVerify.ShowCloseButton = true;
            this.tabVerify.Size = new System.Drawing.Size(1023, 563);
            this.tabVerify.TabIndex = 2;
            this.tabVerify.Text = "Verification";
            this.tabVerify.ThemesEnabled = true;
            // 
            // tabUnarchived
            // 
            this.tabUnarchived.AutoSize = true;
            this.tabUnarchived.AutoSizeMode = System.Windows.Forms.AutoSizeMode.GrowAndShrink;
            this.tabUnarchived.Controls.Add(this.lbllabel5);
            this.tabUnarchived.Image = null;
            this.tabUnarchived.ImageSize = new System.Drawing.Size(16, 16);
            this.tabUnarchived.Location = new System.Drawing.Point(3, 27);
            this.tabUnarchived.Name = "tabUnarchived";
            this.tabUnarchived.ShowCloseButton = true;
            this.tabUnarchived.Size = new System.Drawing.Size(1023, 563);
            this.tabUnarchived.TabIndex = 4;
            this.tabUnarchived.Text = "Unarchived";
            this.tabUnarchived.ThemesEnabled = true;
            // 
            // lbllabel5
            // 
            this.lbllabel5.BackColor = System.Drawing.Color.Black;
            this.lbllabel5.Dock = System.Windows.Forms.DockStyle.Fill;
            this.lbllabel5.Font = new System.Drawing.Font("Tahoma", 18F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lbllabel5.ForeColor = System.Drawing.Color.LightGray;
            this.lbllabel5.Location = new System.Drawing.Point(0, 0);
            this.lbllabel5.Name = "lbllabel5";
            this.lbllabel5.Size = new System.Drawing.Size(1023, 563);
            this.lbllabel5.TabIndex = 0;
            this.lbllabel5.Text = "Results for Unarchived Tests are not tracked";
            this.lbllabel5.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // tabFinal
            // 
            this.tabFinal.AutoSize = true;
            this.tabFinal.AutoSizeMode = System.Windows.Forms.AutoSizeMode.GrowAndShrink;
            this.tabFinal.Image = null;
            this.tabFinal.ImageSize = new System.Drawing.Size(16, 16);
            this.tabFinal.Location = new System.Drawing.Point(3, 27);
            this.tabFinal.Name = "tabFinal";
            this.tabFinal.ShowCloseButton = true;
            this.tabFinal.Size = new System.Drawing.Size(1023, 563);
            this.tabFinal.TabIndex = 3;
            this.tabFinal.Text = "Finalization";
            this.tabFinal.ThemesEnabled = true;
            // 
            // chkConfirmResultEdit
            // 
            this.chkConfirmResultEdit.Cursor = System.Windows.Forms.Cursors.Hand;
            this.chkConfirmResultEdit.EditValue = true;
            this.chkConfirmResultEdit.Location = new System.Drawing.Point(996, 632);
            this.chkConfirmResultEdit.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.chkConfirmResultEdit.Name = "chkConfirmResultEdit";
            this.chkConfirmResultEdit.Properties.AllowFocused = false;
            this.chkConfirmResultEdit.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkConfirmResultEdit.Properties.Appearance.ForeColor = System.Drawing.Color.DarkGreen;
            this.chkConfirmResultEdit.Properties.Appearance.Options.UseFont = true;
            this.chkConfirmResultEdit.Properties.Appearance.Options.UseForeColor = true;
            this.chkConfirmResultEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.chkConfirmResultEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.chkConfirmResultEdit.Properties.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.chkConfirmResultEdit.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            this.chkConfirmResultEdit.Properties.Caption = "I have verified the accuracy of the results";
            this.chkConfirmResultEdit.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Style4;
            this.chkConfirmResultEdit.Properties.LookAndFeel.SkinName = "Blue";
            this.chkConfirmResultEdit.Properties.LookAndFeel.UseDefaultLookAndFeel = false;
            this.chkConfirmResultEdit.Size = new System.Drawing.Size(340, 26);
            this.chkConfirmResultEdit.TabIndex = 4;
            // 
            // btnPerformResultEdit
            // 
            this.btnPerformResultEdit.Appearance.Font = new System.Drawing.Font("Tahoma", 18F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnPerformResultEdit.Appearance.ForeColor = System.Drawing.Color.Indigo;
            this.btnPerformResultEdit.Appearance.Options.UseFont = true;
            this.btnPerformResultEdit.Appearance.Options.UseForeColor = true;
            this.btnPerformResultEdit.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.save16;
            this.btnPerformResultEdit.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleLeft;
            this.btnPerformResultEdit.Location = new System.Drawing.Point(992, 666);
            this.btnPerformResultEdit.LookAndFeel.SkinName = "Money Twins";
            this.btnPerformResultEdit.LookAndFeel.UseDefaultLookAndFeel = false;
            this.btnPerformResultEdit.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.btnPerformResultEdit.Name = "btnPerformResultEdit";
            this.btnPerformResultEdit.Size = new System.Drawing.Size(344, 50);
            this.btnPerformResultEdit.TabIndex = 5;
            this.btnPerformResultEdit.Text = "Finalize";
            this.btnPerformResultEdit.Click += new System.EventHandler(this.btnPerformResultEdit_Click);
            // 
            // pgOrderSearch
            // 
            this.pgOrderSearch.Controls.Add(this.btnSearchOrderId);
            this.pgOrderSearch.Controls.Add(this.label3);
            this.pgOrderSearch.Controls.Add(this.txtOrderDate);
            this.pgOrderSearch.Controls.Add(this.txtOrderId);
            this.pgOrderSearch.Controls.Add(this.label2);
            this.pgOrderSearch.Image = null;
            this.pgOrderSearch.ImageSize = new System.Drawing.Size(16, 16);
            this.pgOrderSearch.Location = new System.Drawing.Point(3, 21);
            this.pgOrderSearch.Name = "pgOrderSearch";
            this.pgOrderSearch.ShowCloseButton = true;
            this.pgOrderSearch.Size = new System.Drawing.Size(285, 74);
            this.pgOrderSearch.TabBackColor = System.Drawing.Color.White;
            this.pgOrderSearch.TabIndex = 2;
            this.pgOrderSearch.Text = "Order";
            this.pgOrderSearch.ThemesEnabled = true;
            // 
            // btnSearchOrderId
            // 
            this.btnSearchOrderId.Appearance.Options.UseTextOptions = true;
            this.btnSearchOrderId.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.btnSearchOrderId.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.btnSearchOrderId.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnSearchOrderId.ImageOptions.Image")));
            this.btnSearchOrderId.Location = new System.Drawing.Point(238, 27);
            this.btnSearchOrderId.LookAndFeel.SkinName = "Seven";
            this.btnSearchOrderId.LookAndFeel.UseDefaultLookAndFeel = false;
            this.btnSearchOrderId.Name = "btnSearchOrderId";
            this.btnSearchOrderId.Size = new System.Drawing.Size(44, 30);
            this.btnSearchOrderId.TabIndex = 6;
            this.btnSearchOrderId.Click += new System.EventHandler(this.btnSearchOrderId_Click);
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.BackColor = System.Drawing.SystemColors.Window;
            this.label3.Location = new System.Drawing.Point(92, 8);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(39, 16);
            this.label3.TabIndex = 5;
            this.label3.Text = "Date:";
            // 
            // txtOrderDate
            // 
            this.txtOrderDate.EditValue = new System.DateTime(((long)(0)));
            this.txtOrderDate.Location = new System.Drawing.Point(95, 27);
            this.txtOrderDate.Name = "txtOrderDate";
            this.txtOrderDate.Properties.Appearance.BackColor = System.Drawing.Color.Ivory;
            this.txtOrderDate.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtOrderDate.Properties.Appearance.ForeColor = System.Drawing.Color.Navy;
            this.txtOrderDate.Properties.Appearance.Options.UseBackColor = true;
            this.txtOrderDate.Properties.Appearance.Options.UseFont = true;
            this.txtOrderDate.Properties.Appearance.Options.UseForeColor = true;
            this.txtOrderDate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.txtOrderDate.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.txtOrderDate.Size = new System.Drawing.Size(137, 30);
            this.txtOrderDate.TabIndex = 4;
            // 
            // txtOrderId
            // 
            this.txtOrderId.EditValue = "";
            this.txtOrderId.Location = new System.Drawing.Point(11, 27);
            this.txtOrderId.Name = "txtOrderId";
            this.txtOrderId.Properties.Appearance.BackColor = System.Drawing.Color.Ivory;
            this.txtOrderId.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtOrderId.Properties.Appearance.ForeColor = System.Drawing.Color.Maroon;
            this.txtOrderId.Properties.Appearance.Options.UseBackColor = true;
            this.txtOrderId.Properties.Appearance.Options.UseFont = true;
            this.txtOrderId.Properties.Appearance.Options.UseForeColor = true;
            this.txtOrderId.Size = new System.Drawing.Size(78, 30);
            this.txtOrderId.TabIndex = 3;
            this.txtOrderId.KeyUp += new System.Windows.Forms.KeyEventHandler(this.txtOrderId_KeyUp);
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.BackColor = System.Drawing.SystemColors.Window;
            this.label2.Location = new System.Drawing.Point(8, 8);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(61, 16);
            this.label2.TabIndex = 2;
            this.label2.Text = "Order Id:";
            // 
            // pgInvoice
            // 
            this.pgInvoice.Controls.Add(this.btnSearchInvoiceId);
            this.pgInvoice.Controls.Add(this.label1);
            this.pgInvoice.Controls.Add(this.txtInvoiceId);
            this.pgInvoice.Image = null;
            this.pgInvoice.ImageSize = new System.Drawing.Size(16, 16);
            this.pgInvoice.Location = new System.Drawing.Point(3, 21);
            this.pgInvoice.Name = "pgInvoice";
            this.pgInvoice.ShowCloseButton = true;
            this.pgInvoice.Size = new System.Drawing.Size(285, 74);
            this.pgInvoice.TabIndex = 1;
            this.pgInvoice.Text = "Invoice";
            this.pgInvoice.ThemesEnabled = true;
            // 
            // btnSearchInvoiceId
            // 
            this.btnSearchInvoiceId.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnSearchInvoiceId.Appearance.Options.UseFont = true;
            this.btnSearchInvoiceId.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnSearchInvoiceId.ImageOptions.Image")));
            this.btnSearchInvoiceId.Location = new System.Drawing.Point(207, 27);
            this.btnSearchInvoiceId.LookAndFeel.SkinName = "Seven Classic";
            this.btnSearchInvoiceId.LookAndFeel.UseDefaultLookAndFeel = false;
            this.btnSearchInvoiceId.Name = "btnSearchInvoiceId";
            this.btnSearchInvoiceId.Size = new System.Drawing.Size(75, 30);
            this.btnSearchInvoiceId.TabIndex = 1;
            this.btnSearchInvoiceId.Text = "Search";
            this.btnSearchInvoiceId.Click += new System.EventHandler(this.btnSearchInvoiceId_Click);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.BackColor = System.Drawing.SystemColors.Window;
            this.label1.Location = new System.Drawing.Point(8, 8);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(68, 16);
            this.label1.TabIndex = 1;
            this.label1.Text = "Invoice Id:";
            // 
            // txtInvoiceId
            // 
            this.txtInvoiceId.EditValue = "";
            this.txtInvoiceId.Location = new System.Drawing.Point(11, 27);
            this.txtInvoiceId.Name = "txtInvoiceId";
            this.txtInvoiceId.Properties.Appearance.BackColor = System.Drawing.Color.Ivory;
            this.txtInvoiceId.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtInvoiceId.Properties.Appearance.ForeColor = System.Drawing.Color.RoyalBlue;
            this.txtInvoiceId.Properties.Appearance.Options.UseBackColor = true;
            this.txtInvoiceId.Properties.Appearance.Options.UseFont = true;
            this.txtInvoiceId.Properties.Appearance.Options.UseForeColor = true;
            this.txtInvoiceId.Size = new System.Drawing.Size(190, 30);
            this.txtInvoiceId.TabIndex = 0;
            this.txtInvoiceId.KeyUp += new System.Windows.Forms.KeyEventHandler(this.txtInvoiceId_KeyUp);
            // 
            // tabInvoiceSearch
            // 
            this.tabInvoiceSearch.BeforeTouchSize = new System.Drawing.Size(292, 99);
            this.tabInvoiceSearch.Controls.Add(this.pgInvoice);
            this.tabInvoiceSearch.Controls.Add(this.pgOrderSearch);
            this.tabInvoiceSearch.FocusOnTabClick = false;
            this.tabInvoiceSearch.ItemSize = new System.Drawing.Size(90, 16);
            this.tabInvoiceSearch.Location = new System.Drawing.Point(8, 29);
            this.tabInvoiceSearch.Name = "tabInvoiceSearch";
            this.tabInvoiceSearch.Office2007ColorScheme = Syncfusion.Windows.Forms.Office2007Theme.Silver;
            this.tabInvoiceSearch.Size = new System.Drawing.Size(292, 99);
            this.tabInvoiceSearch.SizeMode = Syncfusion.Windows.Forms.Tools.TabSizeMode.FillToRight;
            this.tabInvoiceSearch.TabIndex = 6;
            this.tabInvoiceSearch.TabStop = false;
            this.tabInvoiceSearch.TabStyle = typeof(Syncfusion.Windows.Forms.Tools.TabRendererWorkbookMode);
            this.tabInvoiceSearch.ThemeName = "TabRendererWorkbookMode";
            this.tabInvoiceSearch.ThemesEnabled = true;
            // 
            // grdResultBundles
            // 
            this.grdResultBundles.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.grdResultBundles.ClipCurrentCellSelection = false;
            this.grdResultBundles.DataRowTemplate = this.dataRowTemplate1;
            this.grdResultBundles.DataRowTemplateStyles.Add(this.visualGridElementStyle1);
            this.grdResultBundles.DataRowTemplateStyles.Add(this.dataRowStyle1);
            // 
            // 
            // 
            this.grdResultBundles.ErrorVisualStyle.OverrideUIStyle = false;
            // 
            // 
            // 
            this.grdResultBundles.FixedColumnSplitter.Visible = false;
            this.grdResultBundles.FixedHeaderRows.Add(this.columnManagerRow1);
            this.grdResultBundles.GridLineColor = System.Drawing.Color.FromArgb(((int)(((byte)(221)))), ((int)(((byte)(224)))), ((int)(((byte)(227)))));
            this.grdResultBundles.GridLineStyle = System.Drawing.Drawing2D.DashStyle.Dot;
            // 
            // 
            // 
            this.grdResultBundles.InactiveSelectionVisualStyle.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(229)))), ((int)(((byte)(229)))), ((int)(((byte)(229)))));
            this.grdResultBundles.InactiveSelectionVisualStyle.ForeColor = System.Drawing.Color.Black;
            this.grdResultBundles.InactiveSelectionVisualStyle.OverrideUIStyle = true;
            this.grdResultBundles.Location = new System.Drawing.Point(8, 150);
            this.grdResultBundles.Name = "grdResultBundles";
            this.grdResultBundles.OverrideUIStyle = false;
            this.grdResultBundles.ReadOnly = true;
            // 
            // 
            // 
            this.grdResultBundles.RowSelectorPane.AllowRowResize = false;
            gradientMap1.GradientMode = Xceed.UI.GradientMode.Horizontal;
            gradientStop1.Color = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            gradientStop1.Offset = 0D;
            gradientStop2.Color = System.Drawing.Color.FromArgb(((int)(((byte)(189)))), ((int)(((byte)(194)))), ((int)(((byte)(200)))));
            gradientStop2.Offset = 1D;
            gradientMap1.GradientStops.Add(gradientStop1);
            gradientMap1.GradientStops.Add(gradientStop2);
            this.grdResultBundles.RowSelectorPane.GradientMap = gradientMap1;
            // 
            // 
            // 
            this.grdResultBundles.RowSelectorPane.HotVisualStyle.OverrideUIStyle = true;
            this.grdResultBundles.RowSelectorPane.OverrideUIStyle = true;
            this.grdResultBundles.SelectionMode = System.Windows.Forms.SelectionMode.One;
            // 
            // 
            // 
            this.grdResultBundles.SelectionVisualStyle.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(167)))), ((int)(((byte)(205)))), ((int)(((byte)(240)))));
            this.grdResultBundles.SelectionVisualStyle.ForeColor = System.Drawing.Color.Black;
            this.grdResultBundles.SelectionVisualStyle.OverrideUIStyle = true;
            this.grdResultBundles.ShowFocusRectangle = false;
            this.grdResultBundles.ShowTreeLines = false;
            this.grdResultBundles.Size = new System.Drawing.Size(292, 240);
            this.grdResultBundles.SynchronizeDetailGrids = false;
            this.grdResultBundles.TabIndex = 7;
            this.grdResultBundles.SelectedRowsChanged += new System.EventHandler(this.grdResultBundles_SelectedRowsChanged);
            // 
            // visualGridElementStyle1
            // 
            // 
            // 
            // 
            this.visualGridElementStyle1.HotVisualStyle.OverrideUIStyle = true;
            this.visualGridElementStyle1.OverrideUIStyle = false;
            // 
            // dataRowStyle1
            // 
            this.dataRowStyle1.BackColor = System.Drawing.Color.AliceBlue;
            // 
            // columnManagerRow1
            // 
            gradientStop13.Color = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            gradientStop13.Offset = 0D;
            gradientStop14.Color = System.Drawing.Color.FromArgb(((int)(((byte)(212)))), ((int)(((byte)(215)))), ((int)(((byte)(219)))));
            gradientStop14.Offset = 1D;
            gradientMap7.GradientStops.Add(gradientStop13);
            gradientMap7.GradientStops.Add(gradientStop14);
            this.columnManagerRow1.GradientMap = gradientMap7;
            // 
            // 
            // 
            this.columnManagerRow1.HotVisualStyle.OverrideUIStyle = false;
            this.columnManagerRow1.OverrideUIStyle = true;
            // 
            // barManager1
            // 
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.toolBar});
            this.barManager1.Controller = this.barAndDockingController1;
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.btnExit,
            this.btnEditDemographics});
            this.barManager1.MaxItemId = 3;
            // 
            // toolBar
            // 
            this.toolBar.BarName = "Custom 2";
            this.toolBar.CanDockStyle = DevExpress.XtraBars.BarCanDockStyle.Top;
            this.toolBar.DockCol = 0;
            this.toolBar.DockRow = 0;
            this.toolBar.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.toolBar.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.btnExit, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.btnEditDemographics, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph)});
            this.toolBar.OptionsBar.AllowQuickCustomization = false;
            this.toolBar.OptionsBar.DrawDragBorder = false;
            this.toolBar.OptionsBar.RotateWhenVertical = false;
            this.toolBar.OptionsBar.UseWholeRow = true;
            this.toolBar.Text = "Custom 2";
            // 
            // btnExit
            // 
            this.btnExit.Caption = "Close";
            this.btnExit.Id = 1;
            this.btnExit.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.exit_16;
            this.btnExit.Name = "btnExit";
            this.btnExit.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnExit_ItemClick);
            // 
            // btnEditDemographics
            // 
            this.btnEditDemographics.Caption = "Edit Demography";
            this.btnEditDemographics.Id = 2;
            this.btnEditDemographics.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.group_edit_16;
            this.btnEditDemographics.Name = "btnEditDemographics";
            this.btnEditDemographics.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnEditDemographics_ItemClick);
            // 
            // barAndDockingController1
            // 
            this.barAndDockingController1.LookAndFeel.SkinName = "Office 2010 Silver";
            this.barAndDockingController1.LookAndFeel.UseDefaultLookAndFeel = false;
            this.barAndDockingController1.PropertiesBar.AllowLinkLighting = false;
            // 
            // barDockControlTop
            // 
            this.barDockControlTop.CausesValidation = false;
            this.barDockControlTop.Dock = System.Windows.Forms.DockStyle.Top;
            this.barDockControlTop.Location = new System.Drawing.Point(0, 0);
            this.barDockControlTop.Manager = this.barManager1;
            this.barDockControlTop.Size = new System.Drawing.Size(1344, 29);
            // 
            // barDockControlBottom
            // 
            this.barDockControlBottom.CausesValidation = false;
            this.barDockControlBottom.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.barDockControlBottom.Location = new System.Drawing.Point(0, 722);
            this.barDockControlBottom.Manager = this.barManager1;
            this.barDockControlBottom.Size = new System.Drawing.Size(1344, 0);
            // 
            // barDockControlLeft
            // 
            this.barDockControlLeft.CausesValidation = false;
            this.barDockControlLeft.Dock = System.Windows.Forms.DockStyle.Left;
            this.barDockControlLeft.Location = new System.Drawing.Point(0, 29);
            this.barDockControlLeft.Manager = this.barManager1;
            this.barDockControlLeft.Size = new System.Drawing.Size(0, 693);
            // 
            // barDockControlRight
            // 
            this.barDockControlRight.CausesValidation = false;
            this.barDockControlRight.Dock = System.Windows.Forms.DockStyle.Right;
            this.barDockControlRight.Location = new System.Drawing.Point(1344, 29);
            this.barDockControlRight.Manager = this.barManager1;
            this.barDockControlRight.Size = new System.Drawing.Size(0, 693);
            // 
            // grdTestsOrdered
            // 
            this.grdTestsOrdered.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.grdTestsOrdered.ClipCurrentCellSelection = false;
            this.grdTestsOrdered.DataRowTemplate = this.dataRow1;
            this.grdTestsOrdered.DataRowTemplateStyles.Add(this.visualGridElementStyle2);
            this.grdTestsOrdered.DataRowTemplateStyles.Add(this.visualGridElementStyle3);
            // 
            // 
            // 
            this.grdTestsOrdered.ErrorVisualStyle.OverrideUIStyle = false;
            // 
            // 
            // 
            this.grdTestsOrdered.FixedColumnSplitter.Visible = false;
            this.grdTestsOrdered.FixedHeaderRows.Add(this.columnManagerRow2);
            this.grdTestsOrdered.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.grdTestsOrdered.GridLineColor = System.Drawing.Color.FromArgb(((int)(((byte)(221)))), ((int)(((byte)(224)))), ((int)(((byte)(227)))));
            this.grdTestsOrdered.GridLineStyle = System.Drawing.Drawing2D.DashStyle.Dot;
            // 
            // 
            // 
            this.grdTestsOrdered.InactiveSelectionVisualStyle.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(229)))), ((int)(((byte)(229)))), ((int)(((byte)(229)))));
            this.grdTestsOrdered.InactiveSelectionVisualStyle.ForeColor = System.Drawing.Color.Black;
            this.grdTestsOrdered.InactiveSelectionVisualStyle.OverrideUIStyle = true;
            this.grdTestsOrdered.Location = new System.Drawing.Point(8, 412);
            this.grdTestsOrdered.Name = "grdTestsOrdered";
            this.grdTestsOrdered.OverrideUIStyle = false;
            this.grdTestsOrdered.ReadOnly = true;
            // 
            // 
            // 
            this.grdTestsOrdered.RowSelectorPane.AllowRowResize = false;
            gradientMap5.GradientMode = Xceed.UI.GradientMode.Horizontal;
            gradientStop9.Color = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            gradientStop9.Offset = 0D;
            gradientStop10.Color = System.Drawing.Color.FromArgb(((int)(((byte)(189)))), ((int)(((byte)(194)))), ((int)(((byte)(200)))));
            gradientStop10.Offset = 1D;
            gradientMap5.GradientStops.Add(gradientStop9);
            gradientMap5.GradientStops.Add(gradientStop10);
            this.grdTestsOrdered.RowSelectorPane.GradientMap = gradientMap5;
            // 
            // 
            // 
            this.grdTestsOrdered.RowSelectorPane.HotVisualStyle.OverrideUIStyle = true;
            this.grdTestsOrdered.RowSelectorPane.OverrideUIStyle = true;
            this.grdTestsOrdered.RowSelectorPane.Visible = false;
            this.grdTestsOrdered.SelectionMode = System.Windows.Forms.SelectionMode.One;
            // 
            // 
            // 
            this.grdTestsOrdered.SelectionVisualStyle.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(167)))), ((int)(((byte)(205)))), ((int)(((byte)(240)))));
            this.grdTestsOrdered.SelectionVisualStyle.ForeColor = System.Drawing.Color.Black;
            this.grdTestsOrdered.SelectionVisualStyle.OverrideUIStyle = true;
            this.grdTestsOrdered.ShowFocusRectangle = false;
            this.grdTestsOrdered.ShowTreeLines = false;
            this.grdTestsOrdered.Size = new System.Drawing.Size(292, 211);
            this.grdTestsOrdered.SynchronizeDetailGrids = false;
            this.grdTestsOrdered.TabIndex = 12;
            // 
            // visualGridElementStyle2
            // 
            // 
            // 
            // 
            this.visualGridElementStyle2.HotVisualStyle.OverrideUIStyle = true;
            this.visualGridElementStyle2.OverrideUIStyle = false;
            // 
            // visualGridElementStyle3
            // 
            this.visualGridElementStyle3.BackColor = System.Drawing.Color.AliceBlue;
            // 
            // columnManagerRow2
            // 
            gradientStop7.Color = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            gradientStop7.Offset = 0D;
            gradientStop8.Color = System.Drawing.Color.FromArgb(((int)(((byte)(212)))), ((int)(((byte)(215)))), ((int)(((byte)(219)))));
            gradientStop8.Offset = 1D;
            gradientMap4.GradientStops.Add(gradientStop7);
            gradientMap4.GradientStops.Add(gradientStop8);
            this.columnManagerRow2.GradientMap = gradientMap4;
            // 
            // 
            // 
            this.columnManagerRow2.HotVisualStyle.OverrideUIStyle = false;
            this.columnManagerRow2.OverrideUIStyle = true;
            // 
            // grdAuditLog
            // 
            this.grdAuditLog.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.grdAuditLog.CanSelect = false;
            this.grdAuditLog.CausesValidation = false;
            this.grdAuditLog.ClipCurrentCellSelection = false;
            this.grdAuditLog.ClipPartialLine = false;
            this.grdAuditLog.DataRowTemplate = this.dataRow2;
            this.grdAuditLog.DataRowTemplateStyles.Add(this.visualGridElementStyle4);
            this.grdAuditLog.DataRowTemplateStyles.Add(this.visualGridElementStyle5);
            // 
            // 
            // 
            this.grdAuditLog.ErrorVisualStyle.OverrideUIStyle = false;
            // 
            // 
            // 
            this.grdAuditLog.FixedColumnSplitter.Visible = false;
            this.grdAuditLog.FixedHeaderRows.Add(this.columnManagerRow3);
            this.grdAuditLog.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.grdAuditLog.GridLineColor = System.Drawing.Color.FromArgb(((int)(((byte)(221)))), ((int)(((byte)(224)))), ((int)(((byte)(227)))));
            this.grdAuditLog.GridLineStyle = System.Drawing.Drawing2D.DashStyle.Dot;
            // 
            // 
            // 
            this.grdAuditLog.InactiveSelectionVisualStyle.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(229)))), ((int)(((byte)(229)))), ((int)(((byte)(229)))));
            this.grdAuditLog.InactiveSelectionVisualStyle.ForeColor = System.Drawing.Color.Black;
            this.grdAuditLog.InactiveSelectionVisualStyle.OverrideUIStyle = true;
            this.grdAuditLog.Location = new System.Drawing.Point(7, 632);
            this.grdAuditLog.Name = "grdAuditLog";
            this.grdAuditLog.OverrideUIStyle = false;
            this.grdAuditLog.ReadOnly = true;
            // 
            // 
            // 
            this.grdAuditLog.RowSelectorPane.AllowRowResize = false;
            gradientMap3.GradientMode = Xceed.UI.GradientMode.Horizontal;
            gradientStop5.Color = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            gradientStop5.Offset = 0D;
            gradientStop6.Color = System.Drawing.Color.FromArgb(((int)(((byte)(189)))), ((int)(((byte)(194)))), ((int)(((byte)(200)))));
            gradientStop6.Offset = 1D;
            gradientMap3.GradientStops.Add(gradientStop5);
            gradientMap3.GradientStops.Add(gradientStop6);
            this.grdAuditLog.RowSelectorPane.GradientMap = gradientMap3;
            // 
            // 
            // 
            this.grdAuditLog.RowSelectorPane.HotVisualStyle.OverrideUIStyle = true;
            this.grdAuditLog.RowSelectorPane.OverrideUIStyle = true;
            this.grdAuditLog.RowSelectorPane.Visible = false;
            this.grdAuditLog.SelectionMode = System.Windows.Forms.SelectionMode.One;
            // 
            // 
            // 
            this.grdAuditLog.SelectionVisualStyle.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(167)))), ((int)(((byte)(205)))), ((int)(((byte)(240)))));
            this.grdAuditLog.SelectionVisualStyle.ForeColor = System.Drawing.Color.Black;
            this.grdAuditLog.SelectionVisualStyle.OverrideUIStyle = true;
            this.grdAuditLog.ShowFocusRectangle = false;
            this.grdAuditLog.ShowTreeLines = false;
            this.grdAuditLog.Size = new System.Drawing.Size(293, 84);
            this.grdAuditLog.SynchronizeDetailGrids = false;
            this.grdAuditLog.TabIndex = 13;
            // 
            // visualGridElementStyle4
            // 
            // 
            // 
            // 
            this.visualGridElementStyle4.HotVisualStyle.OverrideUIStyle = true;
            this.visualGridElementStyle4.OverrideUIStyle = false;
            // 
            // visualGridElementStyle5
            // 
            this.visualGridElementStyle5.BackColor = System.Drawing.Color.AliceBlue;
            // 
            // columnManagerRow3
            // 
            gradientStop3.Color = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            gradientStop3.Offset = 0D;
            gradientStop4.Color = System.Drawing.Color.FromArgb(((int)(((byte)(212)))), ((int)(((byte)(215)))), ((int)(((byte)(219)))));
            gradientStop4.Offset = 1D;
            gradientMap2.GradientStops.Add(gradientStop3);
            gradientMap2.GradientStops.Add(gradientStop4);
            this.columnManagerRow3.GradientMap = gradientMap2;
            // 
            // 
            // 
            this.columnManagerRow3.HotVisualStyle.OverrideUIStyle = false;
            this.columnManagerRow3.OverrideUIStyle = true;
            // 
            // lbllabel4
            // 
            this.lbllabel4.AutoSize = true;
            this.lbllabel4.Location = new System.Drawing.Point(8, 131);
            this.lbllabel4.Name = "lbllabel4";
            this.lbllabel4.Size = new System.Drawing.Size(96, 16);
            this.lbllabel4.TabIndex = 14;
            this.lbllabel4.Text = "Result Bundles:";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(4, 393);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(105, 16);
            this.label4.TabIndex = 15;
            this.label4.Text = "Test(s) Ordered:";
            // 
            // btnPrint
            // 
            this.btnPrint.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnPrint.Appearance.Options.UseFont = true;
            this.btnPrint.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.print;
            this.btnPrint.Location = new System.Drawing.Point(888, 633);
            this.btnPrint.LookAndFeel.SkinName = "Lilian";
            this.btnPrint.LookAndFeel.UseDefaultLookAndFeel = false;
            this.btnPrint.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.btnPrint.Name = "btnPrint";
            this.btnPrint.Size = new System.Drawing.Size(98, 25);
            this.btnPrint.TabIndex = 21;
            this.btnPrint.Text = "Print";
            this.btnPrint.ToolTip = "Print unofficial results";
            this.btnPrint.Click += new System.EventHandler(this.btnPrint_Click);
            // 
            // btnDowngradeWorkflow
            // 
            this.btnDowngradeWorkflow.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnDowngradeWorkflow.Appearance.Options.UseFont = true;
            this.btnDowngradeWorkflow.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.edit16;
            this.btnDowngradeWorkflow.Location = new System.Drawing.Point(888, 692);
            this.btnDowngradeWorkflow.LookAndFeel.SkinName = "Lilian";
            this.btnDowngradeWorkflow.LookAndFeel.UseDefaultLookAndFeel = false;
            this.btnDowngradeWorkflow.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.btnDowngradeWorkflow.Name = "btnDowngradeWorkflow";
            this.btnDowngradeWorkflow.Size = new System.Drawing.Size(98, 25);
            this.btnDowngradeWorkflow.TabIndex = 26;
            this.btnDowngradeWorkflow.Text = "Edit Results";
            this.btnDowngradeWorkflow.Click += new System.EventHandler(this.btnDowngradeWorkflow_Click);
            // 
            // panelControl1
            // 
            this.panelControl1.Appearance.BackColor = System.Drawing.Color.LightYellow;
            this.panelControl1.Appearance.Options.UseBackColor = true;
            this.panelControl1.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.panelControl1.Controls.Add(this.lblSex);
            this.panelControl1.Controls.Add(this.labelControl2);
            this.panelControl1.Controls.Add(this.lblBookingDateTime);
            this.panelControl1.Controls.Add(this.labBooking);
            this.panelControl1.Controls.Add(this.lblRefdPhysician);
            this.panelControl1.Controls.Add(this.labRefdBy);
            this.panelControl1.Controls.Add(this.lblAgeDoBContent);
            this.panelControl1.Controls.Add(this.lblAgeDoBLabel);
            this.panelControl1.Controls.Add(this.lblPatientName);
            this.panelControl1.Controls.Add(this.labName);
            this.panelControl1.Controls.Add(this.lblInvoiceId);
            this.panelControl1.Controls.Add(this.labInvoice);
            this.panelControl1.Controls.Add(this.lblOrderNum);
            this.panelControl1.Controls.Add(this.labOrder);
            this.panelControl1.Location = new System.Drawing.Point(306, 632);
            this.panelControl1.LookAndFeel.SkinName = "Office 2010 Silver";
            this.panelControl1.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Flat;
            this.panelControl1.LookAndFeel.UseDefaultLookAndFeel = false;
            this.panelControl1.Name = "panelControl1";
            this.panelControl1.Size = new System.Drawing.Size(576, 84);
            this.panelControl1.TabIndex = 31;
            // 
            // lblSex
            // 
            this.lblSex.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblSex.Appearance.ForeColor = System.Drawing.Color.MidnightBlue;
            this.lblSex.Appearance.Options.UseFont = true;
            this.lblSex.Appearance.Options.UseForeColor = true;
            this.lblSex.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Horizontal;
            this.lblSex.Location = new System.Drawing.Point(404, 30);
            this.lblSex.LookAndFeel.UseDefaultLookAndFeel = false;
            this.lblSex.Name = "lblSex";
            this.lblSex.Size = new System.Drawing.Size(12, 16);
            this.lblSex.TabIndex = 13;
            this.lblSex.Text = "W";
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Appearance.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Appearance.Options.UseForeColor = true;
            this.labelControl2.Location = new System.Drawing.Point(371, 30);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(26, 16);
            this.labelControl2.TabIndex = 12;
            this.labelControl2.Text = "Sex:";
            // 
            // lblBookingDateTime
            // 
            this.lblBookingDateTime.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblBookingDateTime.Appearance.ForeColor = System.Drawing.Color.MidnightBlue;
            this.lblBookingDateTime.Appearance.Options.UseFont = true;
            this.lblBookingDateTime.Appearance.Options.UseForeColor = true;
            this.lblBookingDateTime.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Horizontal;
            this.lblBookingDateTime.Location = new System.Drawing.Point(433, 3);
            this.lblBookingDateTime.LookAndFeel.UseDefaultLookAndFeel = false;
            this.lblBookingDateTime.Name = "lblBookingDateTime";
            this.lblBookingDateTime.Size = new System.Drawing.Size(103, 16);
            this.lblBookingDateTime.TabIndex = 11;
            this.lblBookingDateTime.Text = "99/99/9999 99:99";
            // 
            // labBooking
            // 
            this.labBooking.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labBooking.Appearance.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.labBooking.Appearance.Options.UseFont = true;
            this.labBooking.Appearance.Options.UseForeColor = true;
            this.labBooking.Location = new System.Drawing.Point(371, 3);
            this.labBooking.Name = "labBooking";
            this.labBooking.Size = new System.Drawing.Size(49, 16);
            this.labBooking.TabIndex = 10;
            this.labBooking.Text = "Booking:";
            // 
            // lblRefdPhysician
            // 
            this.lblRefdPhysician.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblRefdPhysician.Appearance.ForeColor = System.Drawing.Color.MidnightBlue;
            this.lblRefdPhysician.Appearance.Options.UseFont = true;
            this.lblRefdPhysician.Appearance.Options.UseForeColor = true;
            this.lblRefdPhysician.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Horizontal;
            this.lblRefdPhysician.Location = new System.Drawing.Point(71, 59);
            this.lblRefdPhysician.LookAndFeel.UseDefaultLookAndFeel = false;
            this.lblRefdPhysician.Name = "lblRefdPhysician";
            this.lblRefdPhysician.Size = new System.Drawing.Size(75, 16);
            this.lblRefdPhysician.TabIndex = 9;
            this.lblRefdPhysician.Text = "labelControl1";
            // 
            // labRefdBy
            // 
            this.labRefdBy.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labRefdBy.Appearance.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.labRefdBy.Appearance.Options.UseFont = true;
            this.labRefdBy.Appearance.Options.UseForeColor = true;
            this.labRefdBy.Location = new System.Drawing.Point(13, 59);
            this.labRefdBy.Name = "labRefdBy";
            this.labRefdBy.Size = new System.Drawing.Size(52, 16);
            this.labRefdBy.TabIndex = 8;
            this.labRefdBy.Text = "Refd. By:";
            // 
            // lblAgeDoBContent
            // 
            this.lblAgeDoBContent.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblAgeDoBContent.Appearance.ForeColor = System.Drawing.Color.MidnightBlue;
            this.lblAgeDoBContent.Appearance.Options.UseFont = true;
            this.lblAgeDoBContent.Appearance.Options.UseForeColor = true;
            this.lblAgeDoBContent.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Horizontal;
            this.lblAgeDoBContent.Location = new System.Drawing.Point(470, 30);
            this.lblAgeDoBContent.LookAndFeel.UseDefaultLookAndFeel = false;
            this.lblAgeDoBContent.Name = "lblAgeDoBContent";
            this.lblAgeDoBContent.Size = new System.Drawing.Size(66, 16);
            this.lblAgeDoBContent.TabIndex = 7;
            this.lblAgeDoBContent.Text = "99/99/9999";
            // 
            // lblAgeDoBLabel
            // 
            this.lblAgeDoBLabel.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblAgeDoBLabel.Appearance.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblAgeDoBLabel.Appearance.Options.UseFont = true;
            this.lblAgeDoBLabel.Appearance.Options.UseForeColor = true;
            this.lblAgeDoBLabel.Location = new System.Drawing.Point(437, 30);
            this.lblAgeDoBLabel.Name = "lblAgeDoBLabel";
            this.lblAgeDoBLabel.Size = new System.Drawing.Size(27, 16);
            this.lblAgeDoBLabel.TabIndex = 6;
            this.lblAgeDoBLabel.Text = "Age:";
            // 
            // lblPatientName
            // 
            this.lblPatientName.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblPatientName.Appearance.ForeColor = System.Drawing.Color.MidnightBlue;
            this.lblPatientName.Appearance.Options.UseFont = true;
            this.lblPatientName.Appearance.Options.UseForeColor = true;
            this.lblPatientName.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Horizontal;
            this.lblPatientName.Location = new System.Drawing.Point(71, 29);
            this.lblPatientName.LookAndFeel.UseDefaultLookAndFeel = false;
            this.lblPatientName.Name = "lblPatientName";
            this.lblPatientName.Size = new System.Drawing.Size(119, 16);
            this.lblPatientName.TabIndex = 5;
            this.lblPatientName.Text = "Mr. A B C Choudhury";
            // 
            // labName
            // 
            this.labName.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labName.Appearance.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.labName.Appearance.Options.UseFont = true;
            this.labName.Appearance.Options.UseForeColor = true;
            this.labName.Location = new System.Drawing.Point(13, 30);
            this.labName.Name = "labName";
            this.labName.Size = new System.Drawing.Size(38, 16);
            this.labName.TabIndex = 4;
            this.labName.Text = "Name:";
            // 
            // lblInvoiceId
            // 
            this.lblInvoiceId.Appearance.Font = new System.Drawing.Font("Tahoma", 11.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblInvoiceId.Appearance.ForeColor = System.Drawing.Color.DarkGreen;
            this.lblInvoiceId.Appearance.Options.UseFont = true;
            this.lblInvoiceId.Appearance.Options.UseForeColor = true;
            this.lblInvoiceId.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Horizontal;
            this.lblInvoiceId.Location = new System.Drawing.Point(219, 1);
            this.lblInvoiceId.LookAndFeel.UseDefaultLookAndFeel = false;
            this.lblInvoiceId.Name = "lblInvoiceId";
            this.lblInvoiceId.Size = new System.Drawing.Size(70, 18);
            this.lblInvoiceId.TabIndex = 3;
            this.lblInvoiceId.Text = "9999999";
            // 
            // labInvoice
            // 
            this.labInvoice.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labInvoice.Appearance.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.labInvoice.Appearance.Options.UseFont = true;
            this.labInvoice.Appearance.Options.UseForeColor = true;
            this.labInvoice.Location = new System.Drawing.Point(155, 3);
            this.labInvoice.Name = "labInvoice";
            this.labInvoice.Size = new System.Drawing.Size(58, 16);
            this.labInvoice.TabIndex = 2;
            this.labInvoice.Text = "Invoice #:";
            // 
            // lblOrderNum
            // 
            this.lblOrderNum.Appearance.Font = new System.Drawing.Font("Tahoma", 11.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblOrderNum.Appearance.ForeColor = System.Drawing.Color.Firebrick;
            this.lblOrderNum.Appearance.Options.UseFont = true;
            this.lblOrderNum.Appearance.Options.UseForeColor = true;
            this.lblOrderNum.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Horizontal;
            this.lblOrderNum.Location = new System.Drawing.Point(71, 3);
            this.lblOrderNum.LookAndFeel.UseDefaultLookAndFeel = false;
            this.lblOrderNum.Name = "lblOrderNum";
            this.lblOrderNum.Size = new System.Drawing.Size(30, 18);
            this.lblOrderNum.TabIndex = 1;
            this.lblOrderNum.Text = "A99";
            // 
            // labOrder
            // 
            this.labOrder.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labOrder.Appearance.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.labOrder.Appearance.Options.UseFont = true;
            this.labOrder.Appearance.Options.UseForeColor = true;
            this.labOrder.Location = new System.Drawing.Point(13, 3);
            this.labOrder.Name = "labOrder";
            this.labOrder.Size = new System.Drawing.Size(51, 16);
            this.labOrder.TabIndex = 0;
            this.labOrder.Text = "Order #:";
            // 
            // btnRepeatProcedure
            // 
            this.btnRepeatProcedure.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnRepeatProcedure.Appearance.Options.UseFont = true;
            this.btnRepeatProcedure.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.arrow_return_left;
            this.btnRepeatProcedure.Location = new System.Drawing.Point(888, 662);
            this.btnRepeatProcedure.LookAndFeel.SkinName = "Lilian";
            this.btnRepeatProcedure.LookAndFeel.UseDefaultLookAndFeel = false;
            this.btnRepeatProcedure.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.btnRepeatProcedure.Name = "btnRepeatProcedure";
            this.btnRepeatProcedure.Size = new System.Drawing.Size(98, 25);
            this.btnRepeatProcedure.TabIndex = 36;
            this.btnRepeatProcedure.Text = "Repeat";
            this.btnRepeatProcedure.ToolTip = "Repeat the Bundle";
            this.btnRepeatProcedure.Click += new System.EventHandler(this.btnRepeatProcedure_Click);
            // 
            // ResultEditForm
            // 
            this.Appearance.Options.UseFont = true;
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.ClientSize = new System.Drawing.Size(1344, 722);
            this.Controls.Add(this.btnRepeatProcedure);
            this.Controls.Add(this.panelControl1);
            this.Controls.Add(this.btnDowngradeWorkflow);
            this.Controls.Add(this.btnPrint);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.lbllabel4);
            this.Controls.Add(this.grdAuditLog);
            this.Controls.Add(this.grdTestsOrdered);
            this.Controls.Add(this.grdResultBundles);
            this.Controls.Add(this.tabInvoiceSearch);
            this.Controls.Add(this.btnPerformResultEdit);
            this.Controls.Add(this.chkConfirmResultEdit);
            this.Controls.Add(this.tabPagesControl);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.KeyPreview = true;
            this.LookAndFeel.SkinName = "Office 2010 Silver";
            this.LookAndFeel.UseDefaultLookAndFeel = false;
            this.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "ResultEditForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "ResultEditForm";
            this.Load += new System.EventHandler(this.ResultEditForm_Load);
            this.KeyUp += new System.Windows.Forms.KeyEventHandler(this.ResultEditForm_KeyUp);
            ((System.ComponentModel.ISupportInitialize)(this.tabPagesControl)).EndInit();
            this.tabPagesControl.ResumeLayout(false);
            this.tabPagesControl.PerformLayout();
            this.tabUnarchived.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.chkConfirmResultEdit.Properties)).EndInit();
            this.pgOrderSearch.ResumeLayout(false);
            this.pgOrderSearch.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtOrderDate.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtOrderDate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtOrderId.Properties)).EndInit();
            this.pgInvoice.ResumeLayout(false);
            this.pgInvoice.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtInvoiceId.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabInvoiceSearch)).EndInit();
            this.tabInvoiceSearch.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.grdResultBundles)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataRowTemplate1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.columnManagerRow1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdTestsOrdered)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataRow1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.columnManagerRow2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdAuditLog)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataRow2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.columnManagerRow3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).EndInit();
            this.panelControl1.ResumeLayout(false);
            this.panelControl1.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private Syncfusion.Windows.Forms.Tools.TabControlAdv tabPagesControl;
        private DevExpress.XtraEditors.CheckEdit chkConfirmResultEdit;
        private DevExpress.XtraEditors.SimpleButton btnPerformResultEdit;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv pgOrderSearch;
        private DevExpress.XtraEditors.SimpleButton btnSearchOrderId;
        private System.Windows.Forms.Label label3;
        private DevExpress.XtraEditors.DateEdit txtOrderDate;
        private DevExpress.XtraEditors.TextEdit txtOrderId;
        private System.Windows.Forms.Label label2;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv pgInvoice;
        private DevExpress.XtraEditors.SimpleButton btnSearchInvoiceId;
        private System.Windows.Forms.Label label1;
        private DevExpress.XtraEditors.TextEdit txtInvoiceId;
        private Syncfusion.Windows.Forms.Tools.TabControlAdv tabInvoiceSearch;
        private Xceed.Grid.GridControl grdResultBundles;
        private Xceed.Grid.DataRow dataRowTemplate1;
        private Xceed.Grid.ColumnManagerRow columnManagerRow1;
        private Xceed.Grid.VisualGridElementStyle visualGridElementStyle1;
        private DevExpress.XtraBars.BarManager barManager1;
        private Xceed.Grid.VisualGridElementStyle dataRowStyle1;
        private DevExpress.XtraBars.Bar toolBar;
        private DevExpress.XtraBars.BarButtonItem btnExit;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private Xceed.Grid.GridControl grdTestsOrdered;
        private Xceed.Grid.DataRow dataRow1;
        private Xceed.Grid.VisualGridElementStyle visualGridElementStyle2;
        private Xceed.Grid.VisualGridElementStyle visualGridElementStyle3;
        private Xceed.Grid.ColumnManagerRow columnManagerRow2;
        private Xceed.Grid.GridControl grdAuditLog;
        private Xceed.Grid.DataRow dataRow2;
        private Xceed.Grid.VisualGridElementStyle visualGridElementStyle4;
        private Xceed.Grid.VisualGridElementStyle visualGridElementStyle5;
        private Xceed.Grid.ColumnManagerRow columnManagerRow3;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label lbllabel4;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController1;
        private DevExpress.XtraEditors.SimpleButton btnPrint;
        private DevExpress.XtraEditors.SimpleButton btnDowngradeWorkflow;
        private DevExpress.XtraEditors.PanelControl panelControl1;
        private DevExpress.XtraEditors.LabelControl lblAgeDoBContent;
        private DevExpress.XtraEditors.LabelControl lblAgeDoBLabel;
        private DevExpress.XtraEditors.LabelControl lblPatientName;
        private DevExpress.XtraEditors.LabelControl labName;
        private DevExpress.XtraEditors.LabelControl lblInvoiceId;
        private DevExpress.XtraEditors.LabelControl labInvoice;
        private DevExpress.XtraEditors.LabelControl lblOrderNum;
        private DevExpress.XtraEditors.LabelControl labOrder;
        private DevExpress.XtraEditors.LabelControl lblRefdPhysician;
        private DevExpress.XtraEditors.LabelControl labRefdBy;
        private DevExpress.XtraEditors.LabelControl lblBookingDateTime;
        private DevExpress.XtraEditors.LabelControl labBooking;
        private DevExpress.XtraEditors.LabelControl lblSex;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabEntry;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabVerify;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabFinal;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabUnarchived;
        private System.Windows.Forms.Label lbllabel5;
        private DevExpress.XtraEditors.SimpleButton btnRepeatProcedure;
        private DevExpress.XtraBars.BarButtonItem btnEditDemographics;
    }
}