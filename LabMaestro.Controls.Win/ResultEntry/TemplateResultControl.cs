﻿//  * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
//  
//  Copyright (c) 2012, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
//  All rights reserved.
//  
//  Redistribution and use in source and binary forms, with or without
//  modification, are permitted provided that the following conditions are met:
//     * Redistributions of source code must retain the above copyright
//       notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above copyright
//       notice, this list of conditions and the following disclaimer in the
//       documentation and/or other materials provided with the distribution.
//     * Neither the name of the <organization> nor the
//       names of its contributors may be used to endorse or promote products
//       derived from this software without specific prior written permission.
//  
//  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
//  ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
//  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
//  DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
//  DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
//  (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
//  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
//  ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
//  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
//  SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
//  
//  $Id: TemplateResultControl.cs 1516 2014-11-18 15:57:04Z <EMAIL> $
//  
//  * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraRichEdit;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Search;
using LabMaestro.Shared;
using Syncfusion.Windows.Forms.Grid;
using Syncfusion.Windows.Forms.Tools;

namespace LabMaestro.Controls.Win
{
    public partial class TemplateResultControl : UserControl
    {
        private const int MAX_SEARCH_RESULTS = 50;
        private string _searchString;

        public TemplateResultControl()
        {
            InitializeComponent();
        }

        public bool AllowEditing { get; set; }

        public string ResultContent { get; set; }

        public TestResultType CurrentResultType { get; set; }

        public short CurrentUserId { get; set; }

        public short CurrentLabTestId { get; set; }

        public bool ValidateAndUpdateData()
        {
            ResultContent = redResultContent.RtfText;
            return !string.IsNullOrEmpty(ResultContent);
        }

        public void FocusControl()
        {
            //cboTemplates.Select();
            redResultContent.Focus();
        }

        public void RefreshCatalog()
        {
            TemplateSearchEngine.Reset();
            switch (CurrentResultType)
            {
                case TestResultType.UserTemplate:
                    var lstUserTemplates = UserTemplateReportsRepository.GetUserTemplatesListForLabTest(CurrentLabTestId, CurrentUserId);
                    if (lstUserTemplates.Count > 0) TemplateSearchEngine.AssignCatalog(lstUserTemplates);
                    break;
                case TestResultType.Template:
                    var lstTemplates = TemplateReportsRepository.GetTemplatesListForLabTest(CurrentLabTestId);
                    if (lstTemplates.Count > 0) TemplateSearchEngine.AssignCatalog(lstTemplates);
                    break;
            }
        }

        private void toggleBars(bool enable)
        {
            barCommon.Visible = enable;
            barFont.Visible = enable;
            barClipboard.Visible = enable;
            barParagraph.Visible = enable;

            pnlTemplates.Visible = enable;
            cboTemplates.Enabled = enable;

            btnLoadTemplate.Enabled = enable;
        }

        public void PopulateControls()
        {
            redResultContent.BeginUpdate();
            barManager1.BeginUpdate();
            try
            {
                redResultContent.RtfText = ResultContent;

                if (AllowEditing)
                {
                    redResultContent.ReadOnly = false;
                    toggleBars(true);
                }
                else
                {
                    redResultContent.ReadOnly = true;
                    redResultContent.ActiveViewType = RichEditViewType.Simple;
                    toggleBars(false);
                }

                redResultContent.Visible = true;
            }
            finally
            {
                barManager1.EndUpdate();
                redResultContent.EndUpdate();
            }

            RefreshCatalog();
            //redResultContent.Document.Sections[0].Page.PaperKind = System.Drawing.Printing.PaperKind.A4;
            //redResultContent.Document.Sections[0].Page.Landscape = true;
            //redResultContent.Document.Sections[0].Margins.Left = 600;
        }

        private void searchTemplates()
        {
            if (string.IsNullOrEmpty(_searchString))
            {
                _searchString = cboTemplates.Text.Trim().ToLowerInvariant();
            }

            var searchRecs = string.IsNullOrEmpty(_searchString)
                                 ? TemplateSearchEngine.GetCatalog()
                                 : TemplateSearchEngine.Search(string.Format("*{0}*", _searchString),
                                                               MAX_SEARCH_RESULTS);
            updateTemplatesGrid(searchRecs.Any() ? searchRecs : TemplateSearchEngine.GetCatalog());
        }

        private void updateTemplatesGrid(List<TemplateSearchRec> items)
        {
            grdTemplates.BeginUpdate();
            try
            {
                grdTemplates.TopIndex = 0;
                grdTemplates.SelectedIndex = -1;
                grdTemplates.MultiColumn = false;
                grdTemplates.SelectionMode = SelectionMode.One;
                grdTemplates.DisplayMember = @"Name";
                grdTemplates.ValueMember = @"Id";

                grdTemplates.DataSource = items;
            }
            finally
            {
                grdTemplates.EndUpdate();
            }
        }

        private void resetSearchComboControls(ComboBoxBase combo, GridListControl grid)
        {
            combo.Text = string.Empty;
            grid.SelectedIndex = -1;
        }

        private void handleSearchComboBoxKeyUp(ComboBoxBase combo, GridListControl grid, KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.F6:
                    if (grid.SelectedIndex != -1)
                    {
                        combo.SuppressDropDownEvent = true;
                        combo.DroppedDown = false;
                        combo.SuppressDropDownEvent = false;
                    }
                    applySelectedTemplate();
                    break;

                case Keys.Enter:
                    try
                    {
                        combo.SuppressDropDownEvent = true;
                        searchTemplates();
                        combo.DroppedDown = true;
                        combo.SuppressDropDownEvent = false;
                    }

                    catch
                    {
                        combo.Text = string.Empty;
                    }
                    break;

                case Keys.Escape:
                    combo.Text = string.Empty;
                    grid.SelectedIndex = -1;

                    _searchString = string.Empty;
                    break;
            }
        }


        private void btnLoadTemplate_Click(object sender, EventArgs e)
        {
            applySelectedTemplate();
        }

        private void applySelectedTemplate()
        {
            if (grdTemplates.SelectedValue != null)
            {
                var templateId = (int) grdTemplates.SelectedValue;
                loadTemplateContents((short) templateId);
                redResultContent.Select();
            }
            //grdTemplates.SelectedItem = null;
            //cboTemplates.Text = string.Empty;
            //_searchString = string.Empty;
        }

        private void loadTemplateContents(short templateId)
        {
            if (redResultContent.Modified)
            {
                if (!MessageDlg.Confirm("The editor was modified. If you load a new template all changes will be lost.\r\nDo you want to proceed?"))
                    return;
            }

            string content = CurrentResultType == TestResultType.UserTemplate
                                 ? UserTemplateReportsRepository.GetTemplateReportContent(templateId)
                                 : TemplateReportsRepository.GetTemplateReportContent(templateId);

            redResultContent.BeginUpdate();
            try
            {
                redResultContent.RtfText = content;
            }
            finally
            {
                redResultContent.EndUpdate();
            }
        }

        private void cboTemplates_TextChanged(object sender, EventArgs e)
        {
            _searchString = cboTemplates.Text.Trim().ToLowerInvariant();
        }

        private void cboTemplates_KeyUp(object sender, KeyEventArgs e)
        {
            handleSearchComboBoxKeyUp(cboTemplates, grdTemplates, e);
        }

        private void cboTemplates_DropDown(object sender, EventArgs e)
        {
            var text = cboTemplates.Text.Trim().ToLowerInvariant();
            if (string.IsNullOrEmpty(text))
            {
                updateTemplatesGrid(TemplateSearchEngine.GetCatalog());
            }
            else
            {
                searchTemplates();
            }
        }
    }
}