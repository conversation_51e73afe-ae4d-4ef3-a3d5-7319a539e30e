﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the organization nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: DiscreteResultControl.cs 1516 2014-11-18 15:57:04Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraRichEdit;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Search;
using Syncfusion.Windows.Forms.Grid;
using Syncfusion.Windows.Forms.Tools;
using Xceed.Grid;
using Xceed.Grid.Editors;
using GridControl = Xceed.Grid.GridControl;
using HorizontalAlignment = Xceed.Grid.HorizontalAlignment;

namespace LabMaestro.Controls.Win
{
    public partial class DiscreteResultControl : UserControl
    {
        private const int MAX_SEARCH_RESULTS = 50;
        private readonly List<DiscreteResultLineItemSlice> _changedMetadataList;
        private bool _allowEditing;
        private bool _populated;
        private string _searchString;

        public DiscreteResultControl()
        {
            InitializeComponent();
            _populated = false;
            OrderedTests = new List<OrderedTestSlice>();
            _allowEditing = true;
            _changedMetadataList = new List<DiscreteResultLineItemSlice>();
            MetadataIsDirty = false;
            createGridColumns();
        }

        public bool AllowEditing
        {
            get { return _allowEditing; }
            set
            {
                _allowEditing = value;
                if (!_allowEditing)
                {
                    grdTestResults.Columns[@"result"].ReadOnly = true;
                }
            }
        }

        public string ResultNotes { get; set; }

        public int? LabReportHeaderId { get; set; }

        public short CurrentLabId { get; set; }

        internal List<OrderedTestSlice> OrderedTests { get; private set; }

        public bool MetadataIsDirty { get; private set; }

        public static Column GridAddColumn(GridControl grid, string fieldName, string title, int index, int width)
        {
            var col = new Column(fieldName)
                {
                    Title = title,
                    VisibleIndex = index,
                    Width = width,
                    MinWidth = width,
                    CanBeGrouped = false,
                    CanBeSorted = false,
                    Visible = true
                };

            grid.Columns.Add(col);
            return col;
        }

        public void FocusControl()
        {
            /*
            if (AllowEditing)
            {
                cboHeaders.Select();
            }
            else
            */
            {
                grdTestResults.Select();
            }
        }

        private void updateLookupGrid(List<ActiveLabReportHeaderSlice> items)
        {
            grdHeaders.BeginUpdate();
            try
            {
                grdHeaders.TopIndex = 0;
                grdHeaders.SelectedIndex = -1;
                grdHeaders.MultiColumn = false;
                grdHeaders.SelectionMode = SelectionMode.One;
                grdHeaders.DisplayMember = @"Name";
                grdHeaders.ValueMember = @"Id";

                grdHeaders.DataSource = items;
            }
            finally
            {
                grdHeaders.EndUpdate();
            }
        }

        private void createGridColumns()
        {
            var i = 0;

            grdTestResults.BeginInit();
            grdTestResults.Columns.Clear();

            var col = GridAddColumn(grdTestResults, @"param", @"Parameter", i++, 320);
            col.ReadOnly = true;
            col.CanBeGrouped = false;
            col.CanBeSorted = false;
            col.Fixed = true;

            col = GridAddColumn(grdTestResults, @"result", @"Result", i++, 180);
            col.ReadOnly = false;
            col.BackColor = Color.Ivory;
            col.CanBeGrouped = false;
            col.CanBeSorted = false;
            col.Font = new Font(col.Font, FontStyle.Bold);
            col.ForeColor = Color.Navy;

            var fontSmall = new Font("Tahoma", 10f);
            col = GridAddColumn(grdTestResults, @"units", @"Units", i++, 140);
            col.ReadOnly = true;
            col.CanBeGrouped = false;
            col.CanBeSorted = false;
            col.Font = fontSmall;

            col = GridAddColumn(grdTestResults, @"refRange", @"Reference Range", i++, 290);
            col.ReadOnly = true;
            col.CanBeGrouped = false;
            col.CanBeSorted = false;
            col.Font = fontSmall;

            col = GridAddColumn(grdTestResults, @"__id__", "", i, 1);
            col.CanBeGrouped = true;
            col.CanBeSorted = false;
            col.Visible = false;

            grdTestResults.GroupTemplates.Clear();

            var grpRow = new GroupManagerRow
                {
                    Height = 32,
                    BackColor = Color.AliceBlue,
                    VerticalAlignment = VerticalAlignment.Center,
                    Font = new Font("Tahoma", 14.0f, FontStyle.Bold),
                    CanBeSelected = false,
                    CanBeCurrent = false,
                    HorizontalAlignment = HorizontalAlignment.Left,
                    TitleFormat = @"%GROUPKEY%",
                    Indented = true
                };

            var group = new Group(@"__id__");
            group.HeaderRows.Clear();
            group.HeaderRows.Add(grpRow);
            group.SideMargin.BackColor = grdTestResults.BackColor;
            group.SideMargin.Visible = false;

            grdTestResults.GroupTemplates.Add(group);
            grdTestResults.UpdateGrouping();
            grdTestResults.EndInit();

            grdTestResults.DataRowTemplate.EditCanceled += DataRowTemplate_EditCanceled;
        }

        private void DataRowTemplate_EditCanceled(object sender, EventArgs e)
        {
            var dataRow = (DataRow) sender;
            if (dataRow.ReadOnly) return;
            var cell = dataRow.Cells[@"result"];
            if (validateResultCellContent(cell, cell.Value))
                cell.ResetErrorDescription();
        }

        private bool validateGridControl()
        {
            var retVal = true;
            var dataRowsCount = grdTestResults.DataRows.Count;

            for (var i = 0; i < dataRowsCount; i++)
            {
                foreach (Cell cell in grdTestResults.DataRows[i].Cells)
                {
                    cell.ResetErrorDescription();
                }
            }

            for (var i = 0; i < dataRowsCount; i++)
            {
                var dataRow = grdTestResults.DataRows[i];
                if (dataRow.ReadOnly) continue;
                var cell = dataRow.Cells[@"result"];
                if (!validateResultCellContent(cell, cell.Value))
                    retVal = false;
            }

            grdTestResults.Invalidate();
            return retVal;
        }

        private DataRow getMatchingDataRow(string text)
        {
            var dataRowsCount = grdTestResults.DataRows.Count;

            for (var i = 0; i < dataRowsCount; i++)
            {
                var dataRow = grdTestResults.DataRows[i];
                if (dataRow.ReadOnly) continue;
                var cell = dataRow.Cells[@"param"];
                var cellTitle = (string) Convert.ChangeType(cell.Value, typeof (string));
                cellTitle = cellTitle.Trim();
                if (string.Compare(cellTitle, text, true) == 0)
                {
                    return dataRow;
                }
            }
            return null;
        }

        private static bool validateResultCellContent(Cell cell, object value)
        {
            try
            {
                var newValue = (string) Convert.ChangeType(value, typeof (string));

                if (string.IsNullOrEmpty(newValue))
                {
                    cell.ErrorDescription = "Result must not be empty!";
                    return false;
                }

                newValue = newValue.Trim();
                if (string.IsNullOrEmpty(newValue))
                {
                    cell.ErrorDescription = "Result must be not empty!";
                    return false;
                }
            }
            catch
            {
                cell.ErrorDescription = "Result is required.";
                return false;
            }

            return true;
        }

        private void cell_LeavingEdit(object sender, LeavingEditEventArgs e)
        {
            var cell = (Cell) sender;

            validateResultCellContent(cell, e.NewValue);
        }

        private void cell_EditLeft(object sender, EditLeftEventArgs e)
        {
            var cell = (Cell) sender;
            if (validateResultCellContent(cell, cell.Value))
                cell.ResetErrorDescription();
        }

        private void loadDataIntoGrid()
        {
            grdTestResults.DataRows.Clear();
            foreach (var test in OrderedTests)
            {
                foreach (var item in test.ResultItems)
                {
                    DataRow row = grdTestResults.DataRows.AddNew();
                    row.BeginEdit();
                    try
                    {
                        row.Cells[@"param"].Value = item.Parameter;
                        row.Cells[@"param"].CanBeCurrent = false;

                        var cell = row.Cells[@"result"];
                        cell.Value = item.Result;
                        cell.CanBeCurrent = true;

                        row.Cells[@"units"].Value = item.Units;
                        row.Cells[@"units"].CanBeCurrent = false;

                        row.Cells[@"refRange"].Value = item.ReferenceRange;
                        row.Cells[@"refRange"].CanBeCurrent = false;

                        row.Cells[@"__id__"].Value = item.OrderedTest.TestName;
                        row.Cells[@"__id__"].CanBeCurrent = false;

                        row.Height = 26;
                        row.VerticalAlignment = VerticalAlignment.Center;

                        if (!AllowEditing)
                        {
                            row.ReadOnly = true;
                        }
                        else
                        {
                            row.ReadOnly = !item.IsResultable;
                        }

                        if (!item.IsResultable)
                        {
                            row.ForeColor = Color.DimGray;
                            row.BackColor = Color.WhiteSmoke;
                            row.Cells[@"result"].BackColor = Color.WhiteSmoke;
                            row.CanBeCurrent = false;
                            row.CanBeSelected = false;
                        }
                        else
                        {
                            if (!row.ReadOnly && AllowEditing)
                            {
                                cell.CellEditorManager = new TextEditor();
                                cell.LeavingEdit += cell_LeavingEdit;
                                cell.EditLeft += cell_EditLeft;
                            }
                        }

                        row.Tag = item;
                    }
                    finally
                    {
                        row.EndEdit();
                    }
                }
            }
        }

        public bool ValidateAndUpdateData()
        {
            updateDataFromControls();
            return validateGridControl();
        }

        public void PopulateControls()
        {
            if (_populated) return;

            redComments.ReadOnly = !AllowEditing;
            redComments.ActiveViewType = RichEditViewType.Simple;

            loadDataIntoGrid();

            if (CurrentLabId > 0)
            {
                // get all active report header for the lab
                var activeSlices = LabReportHeadersRepository.GetActiveLabReportHeadersListForLab(CurrentLabId);
                if (activeSlices.Count > 0)
                {
                    LabReportHeaderSearchEngine.AssignCatalog(activeSlices);

                    if (LabReportHeaderId != null)
                    {
                        var id = (int) LabReportHeaderId;
                        grdHeaders.SelectedValue = id;
                        cboHeaders.Text = LabReportHeaderSearchEngine.GetCatalog().Single(x => x.Id == id).Name;
                    }
                    else if (AllowEditing)
                    {
                        // TODO: pre-select default header
                        updateLookupGrid(activeSlices);
                        grdHeaders.SelectedIndex = 0;

                        if (activeSlices.Count == 1)
                        {
                            applySelectedReportHeader();
                        }
                    }
                }
            }

            grdHeaders.Enabled = AllowEditing;
            //cboHeaders.Enabled = AllowEditing;
            cboHeaders.ReadOnly = !AllowEditing;
            btnApplyHeader.Enabled = AllowEditing;

            if (!AllowEditing) disableToolbars();

            if (!string.IsNullOrEmpty(ResultNotes))
            {
                redComments.RtfText = ResultNotes;
            }

            _populated = true;
        }

        private void disableToolbars()
        {
            fontBar1.Visible = false;
            clipboardBar1.Visible = false;
            paragraphBar1.Visible = false;
        }

        private void changeSelectedLineItemMetadata()
        {
            if (grdTestResults.SelectedRows != null && grdTestResults.SelectedRows.Count == 1)
            {
                var metadata = grdTestResults.SelectedRows[0].Tag as DiscreteResultLineItemSlice;
                if (metadata != null)
                {
                    using (var dlgChange = new ResultItemMetadataChangeDialog())
                    {
                        updateDataFromControls();
                        dlgChange.PopulateControls(metadata);
                        //frmChange.ApplySkin("Office");
                        if (dlgChange.ShowDialog(this) == DialogResult.OK)
                        {
                            MetadataIsDirty = true;
                            dlgChange.UpdateMetadata(metadata);
                            _changedMetadataList.Add(metadata);
                            loadDataIntoGrid();
                        }
                    }
                    return;
                }

                throw new InvalidOperationException(
                    "#DiscreteResultControl-cSLIM# Null metadata! Contact your administrator.");
            }
        }

        private void updateDataFromControls()
        {
            foreach (DataRow row in grdTestResults.DataRows)
            {
                var item = row.Tag as DiscreteResultLineItemSlice;
                if (item != null && item.IsResultable)
                {
                    var result = (string) row.Cells[@"result"].Value;
                    if (result != null)
                        item.Result = result.Trim();
                }
            }

            ResultNotes = redComments.RtfText;
        }

        private void grdTestResults_KeyUp(object sender, KeyEventArgs e)
        {
            if (!AllowEditing) return;
            switch (e.KeyCode)
            {
                case Keys.F2:
                    changeSelectedLineItemMetadata();
                    break;
                case Keys.F12:
                    showBloodDifferentialsDialog();
                    break;
            }
        }

        private bool setDataRowResult(string cellTitle, float value)
        {
            var row = getMatchingDataRow(cellTitle);
            if (row != null)
            {
                var cell = row.Cells[@"result"];
                if (cell != null)
                {
                    cell.Value = String.Format("{0:0.##}", value);
                }
                return true;
            }
            return false;
        }

        private void showBloodDifferentialsDialog()
        {
            if (getMatchingDataRow("Neutrophils") == null)
            {
                MessageDlg.Warning("This bundle has no items for Blood Differentials!");
                return;
            }

            using (var frm = new BloodDifferentialsDialog())
            {
                if (frm.ExecuteDialog(this))
                {
                    setDataRowResult(@"Neutrophils", frm.Neutrophils);
                    setDataRowResult(@"Lymphocytes", frm.Lymphocytes);
                    setDataRowResult(@"Monocytes", frm.Monocytes);
                    setDataRowResult(@"Eosinophils", frm.Eosinophils);
                    setDataRowResult(@"Basophils", frm.Basophils);
                }
            }
        }

        private void cboHeaders_KeyUp(object sender, KeyEventArgs e)
        {
            handleSearchComboBoxKeyUp(cboHeaders, grdHeaders, e);
        }

        private void handleSearchComboBoxKeyUp(ComboBoxBase combo, GridListControl grid, KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.F6:
                    if (grid.SelectedIndex != -1)
                    {
                        combo.SuppressDropDownEvent = true;
                        combo.DroppedDown = false;
                        combo.SuppressDropDownEvent = false;
                    }
                    applySelectedReportHeader();
                    break;

                case Keys.Enter:
                    try
                    {
                        combo.SuppressDropDownEvent = true;
                        searchTemplates();
                        combo.DroppedDown = true;
                        combo.SuppressDropDownEvent = false;
                    }

                    catch
                    {
                        combo.Text = string.Empty;
                    }
                    break;

                case Keys.Escape:
                    combo.Text = string.Empty;
                    grid.SelectedIndex = -1;

                    _searchString = string.Empty;
                    break;
            }
        }

        private void applySelectedReportHeader()
        {
            if (grdHeaders.SelectedValue != null)
            {
                var headerId = (int) grdHeaders.SelectedValue;
                LabReportHeaderId = headerId;
            }
        }

        private void searchTemplates()
        {
            if (string.IsNullOrEmpty(_searchString))
            {
                _searchString = cboHeaders.Text.Trim().ToLowerInvariant();
            }

            var searchRecs = string.IsNullOrEmpty(_searchString)
                                 ? LabReportHeaderSearchEngine.GetCatalog()
                                 : LabReportHeaderSearchEngine.Search(_searchString, MAX_SEARCH_RESULTS);
            updateLookupGrid(searchRecs.Any() ? searchRecs : LabReportHeaderSearchEngine.GetCatalog());
        }

        private void btnApplyHeader_Click(object sender, EventArgs e)
        {
            applySelectedReportHeader();
        }

        public void CommitEdit()
        {
            try
            {
                if (grdTestResults.CurrentCell != null && grdTestResults.CurrentCell.FieldName == "result")
                {
                    if (grdTestResults.CurrentCell.IsBeingEdited)
                        grdTestResults.CurrentCell.LeaveEdit(true);
                }
            }
            catch
            {
            }
        }
    }
}