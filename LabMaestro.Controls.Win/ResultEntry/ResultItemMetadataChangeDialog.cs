﻿//  * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
//  
//  Copyright (c) 2012, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
//  All rights reserved.
//  
//  Redistribution and use in source and binary forms, with or without
//  modification, are permitted provided that the following conditions are met:
//     * Redistributions of source code must retain the above copyright
//       notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above copyright
//       notice, this list of conditions and the following disclaimer in the
//       documentation and/or other materials provided with the distribution.
//     * Neither the name of the <organization> nor the
//       names of its contributors may be used to endorse or promote products
//       derived from this software without specific prior written permission.
//  
//  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
//  ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
//  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
//  DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
//  DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
//  (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
//  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
//  ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
//  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
//  SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
//  
//  $Id: ResultItemMetadataChangeDialog.cs 516 2013-04-17 08:03:02Z <EMAIL> $
//  
//  * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System.Windows.Forms;
using DevExpress.XtraEditors;
using LabMaestro.Domain;

namespace LabMaestro.Controls.Win
{
    public partial class ResultItemMetadataChangeDialog : XtraForm
    {
        private string _parameter;
        private string _referenceRanges;
        private string _units;

        public ResultItemMetadataChangeDialog()
        {
            InitializeComponent();
            WinUtils.ApplyTheme(this);
        }

        public string Parameter
        {
            get { return _parameter; }
        }

        public string Units
        {
            get { return _units; }
        }

        public string ReferenceRanges
        {
            get { return _referenceRanges; }
        }

        private void updateDataFromControls()
        {
            _parameter = txtParameter.Text.Trim();
            _units = txtUnits.Text.Trim();
            _referenceRanges = txtReferenceRanges.Text.Trim();
        }

        public void PopulateControls(DiscreteResultLineItemSlice item)
        {
            txtParameter.Text = item.RawParameter;
            txtUnits.Text = item.Units;
            txtReferenceRanges.Text = item.ReferenceRange;
        }

        private void btnSave_Click(object sender, System.EventArgs e)
        {
            if (!chkConfirm.Checked)
            {
                MessageDlg.Error("You must tick the confirmation check-box to save the modifications!");
                return;
            }
            
            updateDataFromControls();
            
            DialogResult = DialogResult.OK;
        }

        public void UpdateMetadata(DiscreteResultLineItemSlice item)
        {
            item.Update(_parameter, _units, _referenceRanges);
        }

        public void ApplySkin(string skin)
        {
            styleController.BeginUpdate();
            styleController.LookAndFeel.SkinName = skin;
            styleController.EndUpdate();
        }
    }
}