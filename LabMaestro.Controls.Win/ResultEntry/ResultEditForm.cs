﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ResultEditForm.cs 1518 2014-11-26 06:26:45Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Drawing;
using System.Globalization;
using System.Linq;
using System.Windows.Forms;
using CuttingEdge.Conditions;
using DevExpress.XtraBars;
using DevExpress.XtraEditors;
using LabMaestro.BusinessLogic;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Printing;
using LabMaestro.Shared;
using Syncfusion.Windows.Forms.Tools;
using Xceed.Grid;
using Xceed.Grid.Viewers;
using HorizontalAlignment = Xceed.Grid.HorizontalAlignment;
using PrintHelper = LabMaestro.Printing.PrintHelper;

namespace LabMaestro.Controls.Win
{
    [Flags]
    public enum ResultEditAccessLevel
    {
        ResultEntry = 0x01,
        ResultVerification = ResultEntry << 2,
        ResultFinalization = ResultVerification << 2,
        ModifyResultBundles = ResultFinalization << 2
    }

    public enum ResultEditWorkflowType : byte
    {
        ResultEntry,
        ResultVerification,
        ResultFinalization
    }

    public partial class ResultEditForm : XtraForm, IExecutableDialog
    {
        private static readonly string[] CHECKBOX_LABELS =
        {
            "Result ready for validation",
            "I confirm the accuracy of these results",
            "I confirm the accuracy of these results"
        };

        private static readonly string[] PERFORM_BUTTON_LABELS =
        {
            "Save",
            "Validate",
            "Finalize"
        };

        private static readonly string[] DOWNGRADE_BUTTON_LABELS =
        {
            "Edit Results",
            "Edit Results",
            "Invalidate"
        };

        private ResultableLabOrder _currentLabOrder;
        private ResultBundleSlice _lastSelectedBundleInGrid;
        private ResultBundleSlice _selectedResultBundle;
        private TabContext _selectedTabContext;

        public ResultEditForm()
        {
            InitializeComponent();
            createGridColumns();
            _lastSelectedBundleInGrid = null;
            WinUtils.SetFormTitle(this, "Result Edit");
        }

        public ResultEditAccessLevel AccessLevel { get; set; }

        public bool ExecuteDialog(IWin32Window parent)
        {
            UpdateControls();
            ShowDialog(parent);
            return true;
        }

        public void UpdateControls()
        {
            if (RolesRepository.UserHasPermission(CurrentUserContext.UserId, PermissionCodes.Workflow.Entry))
            {
                AccessLevel |= ResultEditAccessLevel.ResultEntry;
            }

            if (RolesRepository.UserHasPermission(CurrentUserContext.UserId, PermissionCodes.Workflow.Verify))
            {
                AccessLevel |= ResultEditAccessLevel.ResultVerification;
            }

            if (RolesRepository.UserHasPermission(CurrentUserContext.UserId, PermissionCodes.Workflow.Finalize))
            {
                AccessLevel |= ResultEditAccessLevel.ResultFinalization;
            }
        }

        private bool accessCanEnterResults()
        {
            return AccessLevel.HasFlag(ResultEditAccessLevel.ResultFinalization) ||
                   AccessLevel.HasFlag(ResultEditAccessLevel.ResultVerification) ||
                   AccessLevel.HasFlag(ResultEditAccessLevel.ResultEntry);
        }

        private bool accessCanVerifyResults()
        {
            return AccessLevel.HasFlag(ResultEditAccessLevel.ResultFinalization) ||
                   AccessLevel.HasFlag(ResultEditAccessLevel.ResultVerification);
        }

        private bool accessCanFinalizeResults()
        {
            return AccessLevel.HasFlag(ResultEditAccessLevel.ResultFinalization);
        }

        private bool accessCanModifyResultBundles()
        {
            return AccessLevel.HasFlag(ResultEditAccessLevel.ModifyResultBundles);
        }

        private bool accessCanRepeatLabProcedure()
        {
            return AccessLevel.HasFlag(ResultEditAccessLevel.ResultFinalization);
        }

        private bool accessCanPrintPrelimReport()
        {
            return AccessLevel.HasFlag(ResultEditAccessLevel.ResultFinalization)
                   || AccessLevel.HasFlag(ResultEditAccessLevel.ResultVerification)
                   || AccessLevel.HasFlag(ResultEditAccessLevel.ResultEntry);
        }

        private void createGridColumns()
        {
            grdResultBundles.Columns.Clear();

            var i = 0;
            var imageViewer = new Column(@"_img_", typeof(Image))
            {
                CellViewerManager = new ImageViewer(),
                ReadOnly = true,
                CanBeSorted = false,
                VisibleIndex = i++,
                Width = 20,
                Visible = true,
                Title = "",
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };
            grdResultBundles.Columns.Add(imageViewer);

            //grdResultBundles.GridLineColor = Color.Transparent;
            grdResultBundles.GridLineBackColor = Color.Transparent;

            gridAddColumn(grdResultBundles, @"title", @"Bundle", i++, 230);

            i = 0;
            grdTestsOrdered.Columns.Clear();
            gridAddColumn(grdTestsOrdered, @"name", @"Name", i++, 180);
            gridAddColumn(grdTestsOrdered, @"eta", @"ETA", i++, 90);

            i = 0;
            grdAuditLog.Columns.Clear();
            gridAddColumn(grdAuditLog, @"time", "Time", i++, 85);
            gridAddColumn(grdAuditLog, @"action", "Action", i++, 100);
            gridAddColumn(grdAuditLog, @"user", "User", i++, 85);
        }

        private void rowAddGridOrderedTest(OrderedTestSlice test)
        {
#if DEBUG
            AppLogger.Enter();
#endif
            var row = grdTestsOrdered.DataRows.AddNew();
            row.Cells[@"name"].Value = test.TestName;
            if (!test.ResultETA.IsEmpty)
            {
                row.Cells[@"eta"].Value = SharedUtilities.DateTimeToUltraShortString(test.ResultETA);
            }

            row.Tag = test;
            row.Height = 23;
            row.EndEdit();
#if DEBUG
            AppLogger.Leave();
#endif
        }

        private static Bitmap wfStageToBitmap(WorkflowStageType wfStage)
        {
            if (wfStage == WorkflowStageType.RepeatProcedure) return new Bitmap(Resources.arrow_return_left);
            if (wfStage == WorkflowStageType.ResultEntry) return new Bitmap(Resources.edit16);
            if (wfStage == WorkflowStageType.ResultValidation) return new Bitmap(Resources.verified_16);
            if (wfStage >= WorkflowStageType.ReportFinalization) return new Bitmap(Resources.done_square_16);
            return new Bitmap(Resources.new_16);
        }

        private void rowAddGridResultBundle(ResultBundleSlice slice)
        {
#if DEBUG
            AppLogger.Enter();
#endif
            var row = grdResultBundles.DataRows.AddNew();
            row.Cells[@"_img_"].Value = wfStageToBitmap(slice.WorkFlowStage);
            row.Cells[@"title"].Value = slice.DisplayTitle;
            row.Tag = slice;
            row.Height = 23;
            row.EndEdit();
#if DEBUG
            AppLogger.Leave();
#endif
        }

        private void rowAddGridAuditLog(AuditTrailSlice slice)
        {
#if DEBUG
            AppLogger.Enter();
#endif
            var row = grdAuditLog.DataRows.AddNew();
            row.Cells[@"time"].Value = SharedUtilities.DateTimeToShortString(slice.EventTime);
            row.Cells[@"action"].Value = EnumUtils.EnumDescription((AuditEventType)slice.EventType);
            row.Cells[@"user"].Value = slice.PerformingUserName;
            row.EndEdit();
#if DEBUG
            AppLogger.Leave();
#endif
        }

        private static Column gridAddColumn(GridControl grid, string fieldName, string title, int index, int width)
        {
            var col = new Column(fieldName)
            {
                Title = title,
                VisibleIndex = index,
                Width = width,
                MinWidth = width,
                CanBeGrouped = false,
                CanBeSorted = false,
                Visible = true,
                ReadOnly = true,
                VerticalAlignment = VerticalAlignment.Center
            };

            grid.Columns.Add(col);
            return col;
        }

        private void ResultEditForm_Load(object sender, EventArgs e)
        {
            updateCurrentLabOrder();

            txtInvoiceId.Focus();
            txtInvoiceId.Select();
            txtOrderDate.DateTime = DateTime.Now;
            btnEditDemographics.Enabled =
                CachedRolePermissionChecker.CheckPermission(PermissionCodes.LabOrders.EditDemographic);
        }

        private void populateAuditTrailsGrid()
        {
#if DEBUG
            AppLogger.Enter();
#endif
            if (_selectedResultBundle != null)
            {
                var trails = AuditTrailRepository.GetAuditRecordsForResultBundle(_selectedResultBundle.Id,
                    AuditEventCategory.Workflow);

                grdAuditLog.BeginInit();
                try
                {
                    grdAuditLog.DataRows.Clear();
                    if (trails != null && trails.Count > 0)
                    {
                        foreach (var trailSlice in trails)
                        {
                            rowAddGridAuditLog(trailSlice);
                        }
                    }
                }
                finally
                {
                    grdAuditLog.EndInit();
                    grdAuditLog.Refresh();
                }
            }
#if DEBUG
            AppLogger.Leave();
#endif
        }

        private bool haveSelectedResultBundle()
        {
            return (grdResultBundles.SelectedRows != null && grdResultBundles.SelectedRows.Count == 1);
        }

        private void grdResultBundles_SelectedRowsChanged(object sender, EventArgs e)
        {
#if DEBUG
            AppLogger.Enter();
#endif
            if (haveSelectedResultBundle())
            {
                _selectedResultBundle = grdResultBundles.SelectedRows[0].Tag as ResultBundleSlice;
                if (_lastSelectedBundleInGrid != _selectedResultBundle)
                {
                    _lastSelectedBundleInGrid = _selectedResultBundle;
                    var cur = WaitFormControl.WaitStart(this, description: "Loading lab order information...");
                    SuspendLayout();
                    try
                    {
                        loadOrderedTestsGrid();
                        createTabsForBundle();
                        applyTabPagesAccessLevel();
                        createControlsInTabs();
                        setDefaultTabPage();
                        updateButtonAndCheckControls();
                        populateAuditTrailsGrid();
                    }
                    finally
                    {
                        WaitFormControl.WaitEnd(this, cur);
                        ResumeLayout();
                    }
                }

                focusEditorControl();
                //tabPagesControl.Refresh();
            }
#if DEBUG
            AppLogger.Leave();
#endif
        }

        private void updateButtonAndCheckControls()
        {
#if DEBUG
            AppLogger.Enter();
#endif
            toggleButtonsAndCheckboxes(false);

            //TODO: toggle button/check according to result bundle workflow
            if (_selectedResultBundle.WorkFlowStage < WorkflowStageType.ResultEntry)
            {
                if (accessCanEnterResults())
                {
                    btnPerformResultEdit.Enabled = true;
                    btnPerformResultEdit.Text = PERFORM_BUTTON_LABELS[(int)ResultEditWorkflowType.ResultEntry];
                    chkConfirmResultEdit.Text = CHECKBOX_LABELS[(int)ResultEditWorkflowType.ResultEntry];
                    toggleConfirmResultCheckBox(true);
                }
            }
            else if (_selectedResultBundle.WorkFlowStage == WorkflowStageType.ResultEntry)
            {
                if (accessCanVerifyResults())
                {
                    btnPerformResultEdit.Enabled = true;
                    btnPerformResultEdit.Text = PERFORM_BUTTON_LABELS[(int)ResultEditWorkflowType.ResultVerification];
                    chkConfirmResultEdit.Text = CHECKBOX_LABELS[(int)ResultEditWorkflowType.ResultVerification];
                    toggleConfirmResultCheckBox(true);
                }

                switch (_selectedTabContext.ResultEditWorkflow)
                {
                    case ResultEditWorkflowType.ResultEntry:
                        btnPrint.Enabled = accessCanPrintPrelimReport();
                        break;
                    case ResultEditWorkflowType.ResultVerification:
                        if (accessCanVerifyResults())
                        {
                            btnPrint.Enabled = accessCanPrintPrelimReport();
                            btnDowngradeWorkflow.Text =
                                DOWNGRADE_BUTTON_LABELS[(int)ResultEditWorkflowType.ResultVerification];
                            btnDowngradeWorkflow.Enabled = true;
                        }

                        if (accessCanRepeatLabProcedure())
                            btnRepeatProcedure.Enabled = true;
                        break;
                    case ResultEditWorkflowType.ResultFinalization:
                        if (accessCanRepeatLabProcedure())
                            btnRepeatProcedure.Enabled = true;
                        break;
                }
            }
            else if (_selectedResultBundle.WorkFlowStage == WorkflowStageType.ResultValidation)
            {
                if (accessCanFinalizeResults())
                {
                    btnPerformResultEdit.Enabled = true;
                    btnPerformResultEdit.Text = PERFORM_BUTTON_LABELS[(int)ResultEditWorkflowType.ResultFinalization];
                    chkConfirmResultEdit.Text = CHECKBOX_LABELS[(int)ResultEditWorkflowType.ResultFinalization];
                    toggleConfirmResultCheckBox(true);
                }

                switch (_selectedTabContext.ResultEditWorkflow)
                {
                    case ResultEditWorkflowType.ResultEntry:
                        btnPrint.Enabled = accessCanPrintPrelimReport();
                        break;
                    case ResultEditWorkflowType.ResultVerification:
                        if (accessCanVerifyResults())
                        {
                            btnPrint.Enabled = accessCanPrintPrelimReport();
                        }

                        break;
                    case ResultEditWorkflowType.ResultFinalization:
                        if (accessCanFinalizeResults())
                        {
                            btnRepeatProcedure.Enabled = accessCanRepeatLabProcedure();
                            btnPrint.Enabled = accessCanPrintPrelimReport();
                            btnDowngradeWorkflow.Text =
                                DOWNGRADE_BUTTON_LABELS[(int)ResultEditWorkflowType.ResultFinalization];
                            btnDowngradeWorkflow.Enabled = true;
                        }

                        break;
                }
            }
            else if (_selectedResultBundle.WorkFlowStage >= WorkflowStageType.ReportFinalization)
            {
                // do nothing
            }
#if DEBUG
            AppLogger.Leave();
#endif
        }

        private void focusEditorControl()
        {
#if DEBUG
            AppLogger.Enter();
#endif
            var selTab = tabPagesControl.SelectedTab;
            var ctx = selTab?.Tag as TabContext;
            if (ctx != null)
            {
                if (ctx.DiscreteControl != null)
                {
                    ctx.DiscreteControl.FocusControl();
                }
                else if (ctx.TemplateControl != null)
                {
                    ctx.TemplateControl.FocusControl();
                }
            }
#if DEBUG
            AppLogger.Leave();
#endif
        }

        private void setTabContext(TabPageAdv tab, ResultEditWorkflowType wf, DiscreteResultControl control)
        {
            var ctx = new TabContext { ResultEditWorkflow = wf, DiscreteControl = control };
            tab.Tag = ctx;
        }

        private void setTabContext(TabPageAdv tab, ResultEditWorkflowType wf, TemplateResultControl control)
        {
            var ctx = new TabContext { ResultEditWorkflow = wf, TemplateControl = control };
            tab.Tag = ctx;
        }

        private void createControlsInTabs()
        {
#if DEBUG
            AppLogger.Enter();
#endif
            if (_selectedResultBundle.OrderedTests.Count == 0)
                return;

            if (AccessLevel.HasFlag(ResultEditAccessLevel.ResultFinalization) &&
                _selectedResultBundle.TestResultType == TestResultType.UserTemplate &&
                tabIsEnabled(tabFinal))
            {
                var ctl = createTemplateControl(tabFinal,
                    _selectedResultBundle.WorkFlowStage < WorkflowStageType.ReportFinalization);
                setTabContext(tabFinal, ResultEditWorkflowType.ResultFinalization, ctl);
                return;
            }

            if (_selectedResultBundle.TestResultType == TestResultType.Discrete)
            {
                if (AccessLevel.HasFlag(ResultEditAccessLevel.ResultEntry) && tabIsEnabled(tabEntry))
                {
                    var ctl = createDiscreteControl(tabEntry,
                        _selectedResultBundle.WorkFlowStage < WorkflowStageType.ResultEntry);
                    setTabContext(tabEntry, ResultEditWorkflowType.ResultEntry, ctl);
                }

                if (AccessLevel.HasFlag(ResultEditAccessLevel.ResultVerification) &&
                    tabIsEnabled(tabVerify))
                {
                    var ctl = createDiscreteControl(tabVerify, false);
                    setTabContext(tabVerify, ResultEditWorkflowType.ResultVerification, ctl);
                }

                if (AccessLevel.HasFlag(ResultEditAccessLevel.ResultFinalization) &&
                    tabIsEnabled(tabFinal))
                {
                    var ctl = createDiscreteControl(tabFinal, false);
                    setTabContext(tabFinal, ResultEditWorkflowType.ResultFinalization, ctl);
                }
            }
            else if (_selectedResultBundle.TestResultType == TestResultType.Template)
            {
                if (AccessLevel.HasFlag(ResultEditAccessLevel.ResultEntry) && tabIsEnabled(tabEntry))
                {
                    var ctl = createTemplateControl(tabEntry,
                        _selectedResultBundle.WorkFlowStage < WorkflowStageType.ResultEntry);
                    setTabContext(tabEntry, ResultEditWorkflowType.ResultEntry, ctl);
                }

                if (AccessLevel.HasFlag(ResultEditAccessLevel.ResultVerification) &&
                    tabIsEnabled(tabVerify))
                {
                    var ctl = createTemplateControl(tabVerify, false);
                    setTabContext(tabVerify, ResultEditWorkflowType.ResultVerification, ctl);
                }

                if (AccessLevel.HasFlag(ResultEditAccessLevel.ResultFinalization) &&
                    tabIsEnabled(tabFinal))
                {
                    var ctl = createTemplateControl(tabFinal, false);
                    setTabContext(tabFinal, ResultEditWorkflowType.ResultFinalization, ctl);
                }
            }
#if DEBUG
            AppLogger.Leave();
#endif
        }

        private DiscreteResultControl createDiscreteControl(TabPageAdv page, bool allowEdit)
        {
#if DEBUG
            AppLogger.Enter();
#endif
            var ctlDiscrete = new DiscreteResultControl
            {
                Parent = page,
                AllowEditing = allowEdit,
                Location = new Point(4, 2)
            };

            ctlDiscrete.OrderedTests.AddRange(_selectedResultBundle.OrderedTests);
            ctlDiscrete.ResultNotes = _selectedResultBundle.ResultNotes;
            ctlDiscrete.CurrentLabId = _selectedResultBundle.LabId;
            ctlDiscrete.LabReportHeaderId = _selectedResultBundle.ReportHeaderId;
#if DEBUG
            AppLogger.Checkpoint("ctlDiscrete.PopulateControls");
#endif
            ctlDiscrete.PopulateControls();
#if DEBUG
            AppLogger.Debug("done ctlDiscrete.PopulateControls");
            AppLogger.Leave();
#endif
            return ctlDiscrete;
        }

        private TemplateResultControl createTemplateControl(TabPageAdv page, bool allowEdit)
        {
#if DEBUG
            AppLogger.Enter();
#endif
            var testOrdered = _selectedResultBundle.OrderedTests.First();
            string content = null;
            if (testOrdered.TemplateResult != null)
            {
                content = testOrdered.TemplateResult.Content;
            }
            else if (_selectedResultBundle.WorkFlowStage < WorkflowStageType.ResultEntry)
            {
                // load the default template
                FaultHandler.Shield(
                    () =>
                    {
                        content =
                            TemplateReportsRepository.GetDefaultTemplateContentForLabTest(
                                testOrdered
                                    .LabTestId);
                    });
            }

            var ctlTemp = new TemplateResultControl
            {
                Parent = page,
                AllowEditing = allowEdit,
                Location = new Point(4, 2),
                Dock = DockStyle.Fill,
                CurrentResultType = _selectedResultBundle.TestResultType,
                CurrentUserId = CurrentUserContext.UserId,
                CurrentLabTestId = testOrdered.LabTestId,
                ResultContent = content
            };

            ctlTemp.PopulateControls();
#if DEBUG
            AppLogger.Leave();
#endif
            return ctlTemp;
        }

        private void pageToggle(TabPageAdv page, bool enable)
        {
            page.Visible = enable;
            page.Enabled = enable;
            page.TabEnabled = enable;
            page.TabVisible = enable;
        }

        private void pageDisable(TabPageAdv page, bool disposeContext)
        {
            pageToggle(page, false);

            if (disposeContext)
            {
                var ctx = page.Tag as TabContext;
                if (ctx != null)
                {
                    if (ctx.DiscreteControl != null)
                    {
                        ctx.DiscreteControl.Dispose();
                        ctx.DiscreteControl = null;
                    }

                    if (ctx.TemplateControl != null)
                    {
                        ctx.TemplateControl.Dispose();
                        ctx.TemplateControl = null;
                    }
                }

                page.Tag = null;
            }
        }

        private void pageEnable(TabPageAdv page)
        {
            pageToggle(page, true);
        }

        private void createTabsForBundle()
        {
#if DEBUG
            AppLogger.Enter();
#endif
            pageDisable(tabEntry, true);
            pageDisable(tabVerify, true);
            pageDisable(tabFinal, true);
            pageDisable(tabUnarchived, false);

            if (_selectedResultBundle == null) return;

            switch (_selectedResultBundle.TestResultType)
            {
                case TestResultType.UserTemplate:
                    if (AccessLevel.HasFlag(ResultEditAccessLevel.ResultFinalization))
                    {
                        pageEnable(tabFinal);
                        //tabFinal.TabVisible = false;
                    }

                    break;
                case TestResultType.Unarchived:
                    pageEnable(tabUnarchived);
                    tabUnarchived.TabVisible = false;
                    break;
                default:
                    if (_selectedResultBundle.WorkFlowStage < WorkflowStageType.ResultEntry)
                    {
                        pageEnable(tabEntry);
                    }
                    else if (_selectedResultBundle.WorkFlowStage == WorkflowStageType.ResultEntry)
                    {
                        pageEnable(tabEntry);
                        if (AccessLevel.HasFlag(ResultEditAccessLevel.ResultVerification))
                        {
                            pageEnable(tabVerify);
                        }
                    }
                    else if (_selectedResultBundle.WorkFlowStage == WorkflowStageType.ResultValidation)
                    {
                        pageEnable(tabEntry);

                        if (AccessLevel.HasFlag(ResultEditAccessLevel.ResultVerification))
                        {
                            pageEnable(tabVerify);
                        }

                        if (AccessLevel.HasFlag(ResultEditAccessLevel.ResultFinalization))
                        {
                            pageEnable(tabFinal);
                        }
                    }

                    break;
            }
#if DEBUG
            AppLogger.Leave();
#endif
        }

        private void pageTabEnabledToggle(TabPageAdv page, bool enabled)
        {
            if (page != null)
            {
                page.TabEnabled = enabled;
            }
        }

        private void applyTabPagesAccessLevel()
        {
#if DEBUG
            AppLogger.Enter();
#endif

            pageTabEnabledToggle(tabEntry, false);
            pageTabEnabledToggle(tabVerify, false);
            pageTabEnabledToggle(tabFinal, false);

            if (AccessLevel.HasFlag(ResultEditAccessLevel.ResultFinalization))
            {
                pageTabEnabledToggle(tabEntry, true);
                pageTabEnabledToggle(tabVerify, true);
                pageTabEnabledToggle(tabFinal, true);
            }
            else if (AccessLevel.HasFlag(ResultEditAccessLevel.ResultVerification))
            {
                pageTabEnabledToggle(tabEntry, true);
                pageTabEnabledToggle(tabVerify, true);
            }
            else if (AccessLevel.HasFlag(ResultEditAccessLevel.ResultEntry))
            {
                pageTabEnabledToggle(tabEntry, true);
            }

#if DEBUG
            AppLogger.Leave();
#endif
        }

        private void loadOrderedTestsGrid()
        {
#if DEBUG
            AppLogger.Enter();
#endif
            grdTestsOrdered.BeginInit();
            try
            {
                grdTestsOrdered.DataRows.Clear();
                if (_selectedResultBundle != null)
                {
                    var tests = _selectedResultBundle.OrderedTests;
                    if (tests != null)
                    {
                        foreach (var slice in tests)
                        {
                            rowAddGridOrderedTest(slice);
                        }
                    }
                }
            }
            finally
            {
                grdTestsOrdered.EndInit();
                grdTestsOrdered.Refresh();
            }
#if DEBUG
            AppLogger.Leave();
#endif
        }

        private bool tabIsEnabled(TabPageAdv tab)
        {
            return tab.Visible && tab.Enabled;
        }

        private void setDefaultTabPage()
        {
#if DEBUG
            AppLogger.Enter();
#endif
            if (_selectedResultBundle.TestResultType == TestResultType.Unarchived)
                tabPagesControl.SelectedTab = tabUnarchived;
            else if (AccessLevel.HasFlag(ResultEditAccessLevel.ResultFinalization) && tabIsEnabled(tabFinal))
                tabPagesControl.SelectedTab = tabFinal;
            else if (AccessLevel.HasFlag(ResultEditAccessLevel.ResultVerification) && tabIsEnabled(tabVerify))
                tabPagesControl.SelectedTab = tabVerify;
            else if (AccessLevel.HasFlag(ResultEditAccessLevel.ResultEntry) && tabIsEnabled(tabEntry))
                tabPagesControl.SelectedTab = tabEntry;

            tabPagesControl_SelectedIndexChanged(null, null);
#if DEBUG
            AppLogger.Leave();
#endif
        }

        private void txtInvoiceId_KeyUp(object sender, KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.Escape:
                    e.Handled = true;
                    txtInvoiceId.Text = string.Empty;
                    break;
                case Keys.Enter:
                    e.Handled = true;
                    searchInvoiceId(txtInvoiceId.Text.Trim());
                    break;
            }
        }

        private void searchInvoiceId(string text)
        {
#if DEBUG
            AppLogger.Enter();
#endif
            if (string.IsNullOrEmpty(text))
            {
                // queitly ignore empty strings - spurious key-up event is generated 
                // when the form is loaded
                //MessageDlg.Warning("Empty search string");
                return;
            }

            long invoiceId;
            var parsed = long.TryParse(text, out invoiceId);
            if (!parsed || invoiceId <= 0)
            {
                MessageDlg.Error("Invalid Invoice ID number!");
                return;
            }

            _currentLabOrder = null;
            _selectedResultBundle = null;
            _lastSelectedBundleInGrid = null;
            resetCurrentLabOrder();

            txtOrderId.Text = string.Empty;
            txtOrderDate.DateTime = DateTime.Now;

            searchCurrentLabOrder(invoiceId, true);
#if DEBUG
            AppLogger.Leave();
#endif
        }

        private void searchCurrentLabOrder(long invoiceId, bool updateUI)
        {
            Condition.Requires(invoiceId).IsGreaterThan(0);

            var oldCursor = WaitFormControl.WaitStart(this, description: "Searching invoice...");
            var msg = string.Empty;

            try
            {
                _lastSelectedBundleInGrid = null;
                try
                {
                    _currentLabOrder = ResultableLabOrderAssembler.AssembleResultBundlesChain(invoiceId);
                    if (updateUI)
                    {
                        updateCurrentLabOrder();
                    }
                }
                catch (Exception e)
                {
                    msg = e.Message;
                }
                //grdResultBundles_SelectedRowsChanged(null, null);
            }
            finally
            {
                WaitFormControl.WaitEnd(this, oldCursor);
            }

            if (!string.IsNullOrEmpty(msg))
            {
                MessageDlg.Error(msg);
            }
        }

        private void resetCurrentLabOrder()
        {
#if DEBUG
            AppLogger.Enter();
#endif

            grdAuditLog.DataRows.Clear();
            grdResultBundles.DataRows.Clear();
            grdTestsOrdered.DataRows.Clear();

            lblInvoiceId.Text = string.Empty;
            lblOrderNum.Text = string.Empty;
            lblBookingDateTime.Text = string.Empty;
            lblPatientName.Text = string.Empty;
            lblSex.Text = string.Empty;
            lblAgeDoBLabel.Text = @"Age:";
            lblAgeDoBContent.Text = string.Empty;
            lblRefdPhysician.Text = string.Empty;

            pageDisable(tabEntry, true);
            pageDisable(tabVerify, true);
            pageDisable(tabFinal, true);
            pageDisable(tabUnarchived, false);

            toggleButtonsAndCheckboxes(false);

            FaultHandler.Shield(PatientLabOrdersRepository.Reset);
#if DEBUG
            AppLogger.Leave();
#endif
        }

        private void updateCurrentLabOrder()
        {
#if DEBUG
            AppLogger.Enter();
#endif
            resetCurrentLabOrder();

            if (_currentLabOrder != null)
            {
                lblInvoiceId.Text = _currentLabOrder.InvoiceId.ToString(CultureInfo.InvariantCulture);
                lblOrderNum.Text = _currentLabOrder.OrderId;
                lblBookingDateTime.Text = SharedUtilities.DateTimeToShortString(_currentLabOrder.OrderDateTime);
                lblPatientName.Text = _currentLabOrder.PatientName;
                lblSex.Text = _currentLabOrder.Sex.ToString().Substring(0, 1);
                lblAgeDoBLabel.Text = string.Format("{0}:", _currentLabOrder.AgeDoBLabel);
                lblAgeDoBContent.Text = _currentLabOrder.AgeDoBContent;
                lblRefdPhysician.Text = _currentLabOrder.ReferringPhysician;
            }

            populateResultBundlesGrid();
#if DEBUG
            AppLogger.Leave();
#endif
        }

        private void populateResultBundlesGrid()
        {
#if DEBUG
            AppLogger.Enter();
#endif
            grdResultBundles.BeginInit();
            try
            {
                grdResultBundles.DataRows.Clear();
                //grdResultBundles.SelectedRows.Clear();

                if (_currentLabOrder != null && _currentLabOrder.HasResultBundles)
                {
                    foreach (var bundleSlice in _currentLabOrder.ResultBundles)
                    {
                        rowAddGridResultBundle(bundleSlice);
                    }
                }

                if (_lastSelectedBundleInGrid != null)
                {
                    foreach (Row dataRow in grdResultBundles.DataRows)
                    {
                        var tag = dataRow.Tag as ResultBundleSlice;
                        if (tag != null && tag == _lastSelectedBundleInGrid)
                        {
                            dataRow.IsSelected = true;
                            //grdResultBundles.SelectedRows.Add(dataRow);
                            grdResultBundles.CurrentRow = dataRow;
                            break;
                        }
                    }
                }
            }
            finally
            {
                grdResultBundles.EndInit();
                grdResultBundles.Refresh();
            }

            if (grdResultBundles.SelectedRows.Count == 0)
                grdResultBundles.Select();

            tabPagesControl_SelectedIndexChanged(null, null);
#if DEBUG
            AppLogger.Leave();
#endif
        }

        private void btnSearchInvoiceId_Click(object sender, EventArgs e)
        {
            searchInvoiceId(txtInvoiceId.Text.Trim());
        }

        private void tabPagesControl_SelectedIndexChanged(object sender, EventArgs e)
        {
#if DEBUG
            AppLogger.Enter();
#endif
            // update buttons, label controls
            _selectedTabContext = tabPagesControl.SelectedTab.Tag as TabContext;

            var cur = WaitFormControl.WaitStart(this);
            SuspendLayout();
            try
            {
                if (_selectedTabContext != null)
                {
                    updateControlsBasedOnWorkflow();
                    //updateButtonAndCheckControls();
                    focusEditorControl();
                }
            }
            finally
            {
                WaitFormControl.WaitEnd(this, cur);
                ResumeLayout();
            }

            focusEditorControl();
#if DEBUG
            AppLogger.Leave();
#endif
        }

        private void updateControlsBasedOnWorkflow()
        {
#if DEBUG
            AppLogger.Enter();
#endif
            toggleButtonsAndCheckboxes(false);

            chkConfirmResultEdit.Text = CHECKBOX_LABELS[(int)_selectedTabContext.ResultEditWorkflow];
            btnPerformResultEdit.Text = PERFORM_BUTTON_LABELS[(int)_selectedTabContext.ResultEditWorkflow];
            btnDowngradeWorkflow.Text = DOWNGRADE_BUTTON_LABELS[(int)_selectedTabContext.ResultEditWorkflow];

            switch (_selectedTabContext.ResultEditWorkflow)
            {
                case ResultEditWorkflowType.ResultEntry:
                    btnPerformResultEdit.Image = Resources.save16;
                    if (_selectedResultBundle.WorkFlowStage < WorkflowStageType.ResultEntry)
                    {
                        toggleConfirmResultCheckBox(true);
                        btnPerformResultEdit.Enabled = true;
                    }
                    else
                    {
                        _selectedTabContext.AllowEditing(false);
                    }

                    break;
                case ResultEditWorkflowType.ResultVerification:
                    btnPerformResultEdit.Image = Resources.list_accept;
                    switch (_selectedResultBundle.WorkFlowStage)
                    {
                        case WorkflowStageType.ResultEntry:
                            toggleConfirmResultCheckBox(true);
                            btnPrint.Enabled = accessCanPrintPrelimReport();
                            btnRepeatProcedure.Enabled = accessCanRepeatLabProcedure();
                            btnDowngradeWorkflow.Enabled = true;
                            btnPerformResultEdit.Enabled = true;
                            break;
                        case WorkflowStageType.ResultValidation:
                            /*
                            btnPrint.Enabled = accessCanPrintPrelimReport();
                            btnRepeatProcedure.Enabled = accessCanRepeatLabProcedure();
                            btnDowngradeWorkflow.Enabled = true;
                            */
                            break;
                    }

                    break;
                case ResultEditWorkflowType.ResultFinalization:
                    btnPerformResultEdit.Image = Resources.list_accept;
                    if (_selectedResultBundle.TestResultType == TestResultType.UserTemplate)
                    {
                        if (_selectedResultBundle.WorkFlowStage >= WorkflowStageType.ReportFinalization)
                        {
                            btnPrint.Enabled = accessCanPrintPrelimReport();
                        }
                        else
                        {
                            toggleConfirmResultCheckBox(true);
                            btnPerformResultEdit.Enabled = true;
                        }
                    }
                    else
                    {
                        btnPrint.Enabled = accessCanPrintPrelimReport();
                        btnRepeatProcedure.Enabled = accessCanRepeatLabProcedure();

                        if (_selectedResultBundle.WorkFlowStage == WorkflowStageType.ResultValidation)
                        {
                            toggleConfirmResultCheckBox(true);
                            btnPerformResultEdit.Enabled = true;
                            btnDowngradeWorkflow.Enabled = true;
                        }
                    }

                    break;
            }
#if DEBUG
            AppLogger.Leave();
#endif
        }

        private void toggleButtonsAndCheckboxes(bool enable)
        {
            toggleConfirmResultCheckBox(enable);
            btnPerformResultEdit.Enabled = enable;
            btnDowngradeWorkflow.Enabled = enable;
            btnPrint.Enabled = enable;
            btnRepeatProcedure.Enabled = enable;
        }

        private void toggleConfirmResultCheckBox(bool enable)
        {
            chkConfirmResultEdit.Enabled = enable;
            chkConfirmResultEdit.Checked = false;
            chkConfirmResultEdit.ForeColor = enable ? Color.Green : Color.Gray;
        }

        private bool bundleReadyForResultEntry()
        {
            return (_selectedResultBundle != null)
                   && (_selectedResultBundle.WorkFlowStage < WorkflowStageType.ResultEntry)
                   && (_selectedResultBundle.WorkFlowStage != WorkflowStageType.Canceled);
        }

        private bool bundleReadyForVerification()
        {
            return (_selectedResultBundle != null) &&
                   (_selectedResultBundle.WorkFlowStage == WorkflowStageType.ResultEntry);
        }

        private bool bundleReadyForFinalization()
        {
            return (_selectedResultBundle != null) &&
                   (_selectedResultBundle.WorkFlowStage == WorkflowStageType.ResultValidation);
        }

        private void btnPerformResultEdit_Click(object sender, EventArgs e)
        {
            performResultEdit(chkConfirmResultEdit.Checked);
        }

        private void performResultEdit(bool upgradeBundleWorkflow)
        {
#if DEBUG
            AppLogger.Enter();
#endif
            _lastSelectedBundleInGrid = null;

            if (_selectedTabContext != null && _selectedResultBundle != null)
            {
                switch (_selectedResultBundle.TestResultType)
                {
                    case TestResultType.Discrete:
                        if (_selectedTabContext != null && _selectedTabContext.DiscreteControl != null)
                        {
                            _selectedTabContext.DiscreteControl.CommitEdit();
                        }

                        if (bundleReadyForResultEntry())
                        {
                            saveDiscreteResults(upgradeBundleWorkflow);
                        }
                        else if (bundleReadyForVerification())
                        {
                            verifyDiscreteResults(upgradeBundleWorkflow);
                        }
                        else if (bundleReadyForFinalization())
                        {
                            finalizeDiscreteResults(upgradeBundleWorkflow);
                        }

                        break;
                    case TestResultType.Template:
                    case TestResultType.UserTemplate:
                        if (bundleReadyForResultEntry())
                        {
                            saveTemplateResults(upgradeBundleWorkflow);
                        }
                        else if (bundleReadyForVerification())
                        {
                            verifyTemplateResults(upgradeBundleWorkflow);
                        }
                        else if (bundleReadyForFinalization())
                        {
                            finalizeTemplateResults(upgradeBundleWorkflow);
                        }

                        break;
                }

                refreshCurrentLabOrder();
            }
#if DEBUG
            AppLogger.Leave();
#endif
        }

        private void refreshCurrentLabOrder()
        {
#if DEBUG
            AppLogger.Enter();
#endif
            var cur = WaitFormControl.WaitStart(this, description: "Refreshing lab order information...");
            try
            {
                updateCurrentLabOrder();
            }
            finally
            {
                WaitFormControl.WaitEnd(this, cur);
            }
#if DEBUG
            AppLogger.Leave();
#endif
        }

        private void finalizeTemplateResults(bool upgradeWorkflow)
        {
            if (!_selectedTabContext.TemplateControl.ValidateAndUpdateData())
            {
                if (
                    !MessageDlg.Confirm(
                        "The report contains incomplete result line-items. Do you still want to proceed?"))
                    return;
            }

            if (upgradeWorkflow)
            {
                bundleWorkFlowStageUpgrade();
                // TODO: reload / refresh result bundles - update the icon
                // make discrete control read only
                // disable buttons etc.
            }
            else
            {
                MessageDlg.Info("Results not finalized!\r\nYou must tick the checkbox to perform finalization.");
            }
        }

        private void verifyTemplateResults(bool upgradeWorkflow)
        {
            if (!_selectedTabContext.TemplateControl.ValidateAndUpdateData())
            {
                if (
                    !MessageDlg.Confirm(
                        "The report contains incomplete result line-items. Do you still want to proceed?"))
                    return;
            }

            if (upgradeWorkflow)
            {
                bundleWorkFlowStageUpgrade();
                // TODO: reload / refresh result bundles - update the icon
                // make discrete control read only
                // disable buttons etc.
            }
            else
            {
                MessageDlg.Info("Results not verified!\r\nYou must tick the checkbox to perform verification.");
            }
        }

        private void verifyDiscreteResults(bool upgradeWorkflow)
        {
            if (!_selectedTabContext.DiscreteControl.ValidateAndUpdateData())
            {
                if (
                    !MessageDlg.Confirm(
                        "The report contains incomplete result line-items. Do you still want to proceed?"))
                    return;
            }

            if (upgradeWorkflow)
            {
                // TODO: Audit log - verified by staff
                bundleWorkFlowStageUpgrade();
            }
            else
            {
                MessageDlg.Info("Results not verified!\r\nYou must tick the checkbox to perform verification.");
            }
        }

        private void finalizeDiscreteResults(bool upgradeWorkflow)
        {
            if (!_selectedTabContext.DiscreteControl.ValidateAndUpdateData())
            {
                if (
                    !MessageDlg.Confirm(
                        "The report contains incomplete result line-items. Do you still want to proceed?"))
                    return;
            }

            if (upgradeWorkflow)
            {
                // TODO: Audit log - finalized by staff
                bundleWorkFlowStageUpgrade();
            }
            else
            {
                MessageDlg.Info("Results not finalized!\r\nYou must tick the checkbox to perform finalization.");
            }
        }

        private void saveDiscreteResults(bool upgradeWorkflow)
        {
            if (!_selectedTabContext.DiscreteControl.ValidateAndUpdateData())
            {
                if (!MessageDlg.Confirm(
                        "The report contains incomplete result line-items. Do you still want to save?"))
                    return;
            }

            // If the result line item metadata was modified, log it
            foreach (var testSlice in _selectedResultBundle.OrderedTests)
            {
                foreach (var itemSlice in testSlice.ResultItems)
                {
                    if (itemSlice.IsMetadataDirty)
                    {
                        var len = Math.Min(19, itemSlice.RawParameter.Length);
                        var remark = itemSlice.RawParameter.Substring(0, len).Trim();
                        AuditTrailRepository.LogOrderWorkflowEvent(AuditEventType.wfResultMetadataChanged,
                            _currentLabOrder.InvoiceId,
                            _selectedResultBundle.Id,
                            testSlice.OrderedTestId,
                            _selectedResultBundle.WorkFlowStage,
                            remark);
                    }
                }
            }

            foreach (var testSlice in _selectedResultBundle.OrderedTests)
            {
                foreach (var resultItem in testSlice.ResultItems)
                {
                    DiscreteResultsRepository.UpdateDiscreteResultLineItem(
                        resultItem.Id,
                        resultItem.Parameter,
                        resultItem.Units,
                        resultItem.Result,
                        resultItem.ReferenceRange,
                        resultItem.Flag);
                }
            }

            var resultNotes = _selectedTabContext.DiscreteControl.ResultNotes;
            _selectedResultBundle.ResultNotes = resultNotes;
            ResultBundlesRepository.UpdateResultBundleResultNotes(_selectedResultBundle.Id, resultNotes);

            var headerId = _selectedTabContext.DiscreteControl.LabReportHeaderId;
            if (headerId != null)
            {
                ResultBundlesRepository.UpdateResultBundleLabReportHeader(_selectedResultBundle.Id, (int)headerId);
            }

            if (upgradeWorkflow)
            {
                bundleWorkFlowStageUpgrade();
                // TODO: reload / refresh result bundles - update the icon
                // make discrete control read only
                // disable buttons etc.
            }
            else
            {
                // TODO: audit - log - Partial result saved
                AuditTrailRepository.LogOrderWorkflowEvent(AuditEventType.wfResultSaved,
                    _currentLabOrder.InvoiceId,
                    _selectedResultBundle.Id,
                    null,
                    _selectedResultBundle.WorkFlowStage);
            }

            // FIXED issue #10: template result not loading on bundle grid click
            if (_currentLabOrder != null)
            {
                searchCurrentLabOrder(_currentLabOrder.InvoiceId, false);
            }
        }

        private void bundleWorkFlowStageUpgrade()
        {
#if DEBUG
            AppLogger.Enter();
#endif
            var auditEvent = AuditEventType.None;

            if (bundleReadyForResultEntry())
            {
                _selectedResultBundle.WorkFlowStage = _selectedResultBundle.PostResultEntryWorkflowStage;
                switch (_selectedResultBundle.PostResultEntryWorkflowStage)
                {
                    case WorkflowStageType.ResultEntry:
                        auditEvent = AuditEventType.wfResultEntered;
                        break;
                    case WorkflowStageType.ResultValidation:
                        auditEvent = AuditEventType.wfResultVerified;
                        break;
                    case WorkflowStageType.ReportFinalization:
                        auditEvent = AuditEventType.wfResultFinalized;
                        break;
                }
            }
            else if (bundleReadyForVerification())
            {
                //_selectedResultBundle.WorkFlowStage = WorkflowStageType.ResultVerification;
                _selectedResultBundle.WorkFlowStage = _selectedResultBundle.PostResultVerificationWorkflowStage;
                auditEvent = AuditEventType.wfResultVerified;
            }
            else if (bundleReadyForFinalization())
            {
                //_selectedResultBundle.WorkFlowStage = WorkflowStageType.ResultFinalization;
                _selectedResultBundle.WorkFlowStage = _selectedResultBundle.PostResultFinalizationWorkflowStage;
                auditEvent = AuditEventType.wfResultFinalized;
            }
            else
            {
                // we shouldn't even be here....
                // TODO: log the event to app_sys.error
                return;
            }

            saveResultBundleWorkflowStage();

            // Save details of the finalizing consultant user
            if (auditEvent == AuditEventType.wfResultFinalized)
            {
                ResultBundlesRepository.UpdateResultBundleFinalizingConsultant(_selectedResultBundle.Id,
                    CurrentUserContext.UserId);
            }

            // audit log
            AuditTrailRepository.LogOrderWorkflowEvent(
                auditEvent,
                _currentLabOrder.InvoiceId,
                _selectedResultBundle.Id,
                null,
                _selectedResultBundle.WorkFlowStage);

            //TODO: filter events (only for finalization?) - create a global app setting for this
            var threshold = GlobalSettingsHelper.ResultEntry.RecentlyUpdatedBundlesMinimumThreshold;
            if (_selectedResultBundle.WorkFlowStage >= threshold)
            {
                RecentlyUpdatedResultBundlesRepository.AddResultBundleToRecentUpdatesList(_selectedResultBundle.Id,
                    _currentLabOrder.InvoiceId,
                    _selectedResultBundle.WorkFlowStage,
                    SortPriorityType.Normal);
            }
#if DEBUG
            AppLogger.Leave();
#endif
        }

        private void bundleRepeatLabProcedure()
        {
            if (bundleReadyForResultEntry() || bundleReadyForVerification() || bundleReadyForFinalization())
            {
                _selectedResultBundle.WorkFlowStage = WorkflowStageType.RepeatProcedure;
            }
            else
            {
                // we shouldn't even be here....
                return;
            }

            saveResultBundleWorkflowStage();

            // audit log
            AuditTrailRepository.LogOrderWorkflowEvent(
                AuditEventType.wfRepeatProcedure,
                _currentLabOrder.InvoiceId,
                _selectedResultBundle.Id,
                null,
                _selectedResultBundle.WorkFlowStage);

            RecentlyUpdatedResultBundlesRepository.RemoveResultBundleFromRecentUpdatesList(_selectedResultBundle.Id);
        }

        private void saveTemplateResults(bool upgradeWorkflow)
        {
#if DEBUG
            AppLogger.Enter();
#endif
            _selectedTabContext.TemplateControl.ValidateAndUpdateData();
            long testId = -1;

            if (_selectedResultBundle.WorkFlowStage <= WorkflowStageType.ResultEntry)
            {
                foreach (var testSlice in _selectedResultBundle.OrderedTests)
                {
                    testId = testSlice.OrderedTestId;
                    var resultSlice = TemplateResultsRepository.GetTemplateResultForOrderedTest(testSlice.OrderedTestId,
                        _selectedResultBundle.Id);
                    if (resultSlice != null)
                    {
                        // TODO: audit log - modify
                        TemplateResultsRepository.UpdateTemplateResult(resultSlice.Id,
                            _selectedTabContext.TemplateControl.ResultContent);
                    }
                    else
                    {
                        // TODO: audit log - save new
                        TemplateResultsRepository.InsertTemplateResult(testSlice.OrderedTestId,
                            _selectedResultBundle.Id,
                            _selectedTabContext.TemplateControl.ResultContent);
                    }
                }
            }

            if (upgradeWorkflow)
            {
                bundleWorkFlowStageUpgrade();
                // TODO: reload / refresh result bundles - update the icon
                // make discrete control read only
                // disable buttons etc.
            }
            else
            {
                // TODO: audit - log - Partial result saved
                AuditTrailRepository.LogOrderWorkflowEvent(AuditEventType.wfResultSaved,
                    _currentLabOrder.InvoiceId,
                    _selectedResultBundle.Id,
                    testId,
                    _selectedResultBundle.WorkFlowStage);
            }

            // FIXED issue #10: template result not loading on bundle grid click
            //_selectedResultBundle.OrderedTests.First().TemplateResult.Content = _selectedTabContext.TemplateControl.ResultContent;
            if (_currentLabOrder != null)
            {
                searchCurrentLabOrder(_currentLabOrder.InvoiceId, false);
            }
#if DEBUG
            AppLogger.Leave();
#endif
        }

        private void saveResultBundleWorkflowStage()
        {
            if (_selectedResultBundle != null)
            {
                ResultBundlesRepository.UpdateResultBundleWorkflowStage(_selectedResultBundle.Id,
                    _selectedResultBundle.WorkFlowStage);

                if (_currentLabOrder != null)
                {
                    /*
                    var estimator = new LabOrderWorkflowStageEstimator(_currentLabOrder.InvoiceId);
                    estimator.EstimateWorkflowStage();
                     */
                    //AppSysRepository.SendLabOrderWorkflowStageEstimateRequest(_currentLabOrder.InvoiceId);
                    AsyncEsbMessenger.SendMessagesAsync(
                        EsbMessageChain.CreateNew()
                            .AddWorkflowEstimator(_currentLabOrder.InvoiceId));

                    // send delayed hooks request
                    AsyncEsbMessenger.SendMessagesAsync(
                        EsbMessageChain.CreateNewDelayed()
                            .AddResultBundleWorkflowHook(
                                _currentLabOrder
                                    .InvoiceId,
                                _selectedResultBundle
                                    .Id)
                            .AddLabOrderWorkflowHook(
                                _currentLabOrder
                                    .InvoiceId));
                }
            }
        }

        private void btnDowngradeWorkflow_Click(object sender, EventArgs e)
        {
            if (!MessageDlg.Confirm("Do you really want to un-verify the result bundle?")) return;

            _lastSelectedBundleInGrid = null;

            if (_selectedTabContext != null)
            {
                if ((_selectedTabContext.ResultEditWorkflow == ResultEditWorkflowType.ResultVerification) ||
                    (_selectedTabContext.ResultEditWorkflow == ResultEditWorkflowType.ResultFinalization))
                {
                    if (bundleReadyForFinalization() || bundleReadyForVerification())
                        bundleWorkFlowStageDowngrade();
                }
            }

            refreshCurrentLabOrder();
        }

        private void bundleWorkFlowStageDowngrade()
        {
#if DEBUG
            AppLogger.Enter();
#endif
            AuditEventType auditEvent;

            if (_selectedResultBundle.PostResultEntryWorkflowStage != WorkflowStageType.ResultEntry)
            {
                /* Edge case: this bundle doesn't follow the typical workflow progression (entry->verification->finalization).
                 * We revert back to the pre-"Result Entry" stage.
                 */
                if (_selectedResultBundle.WorkFlowStage == _selectedResultBundle.PostResultEntryWorkflowStage)
                {
                    _selectedResultBundle.WorkFlowStage = _selectedResultBundle.PostOrderEntryWorkflowStage;
                    auditEvent = AuditEventType.wfResultUnEntered;
                }
                else
                {
                    var message = string.Format("Worfklow stage downgrade for {0} is not supported!",
                        EnumUtils.EnumDescription(_selectedResultBundle.WorkFlowStage));
                    throw new InvalidOperationException(message);
                }
            }
            else
            {
                // regular workflow downgrade process
                if (bundleReadyForVerification())
                {
                    // Results were saved... make them "un-entered"
                    //_selectedResultBundle.WorkFlowStage = WorkflowStageType.OrderEntry;
                    _selectedResultBundle.WorkFlowStage = _selectedResultBundle.PostOrderEntryWorkflowStage;
                    auditEvent = AuditEventType.wfResultUnEntered;
                }
                else if (bundleReadyForFinalization())
                {
                    // Results were verified... make them "un-verified"
                    //_selectedResultBundle.WorkFlowStage = WorkflowStageType.ResultEntry;
                    _selectedResultBundle.WorkFlowStage = _selectedResultBundle.PostResultEntryWorkflowStage;
                    auditEvent = AuditEventType.wfResultUnverified;
                }
                else
                {
                    var message = string.Format("Worfklow stage downgrade for {0} is not supported!",
                        EnumUtils.EnumDescription(_selectedResultBundle.WorkFlowStage));
                    throw new InvalidOperationException(message);
                }
            }

            saveResultBundleWorkflowStage();

            // audit log
            AuditTrailRepository.LogOrderWorkflowEvent(
                auditEvent,
                _currentLabOrder.InvoiceId,
                _selectedResultBundle.Id,
                null,
                _selectedResultBundle.WorkFlowStage);


            RecentlyUpdatedResultBundlesRepository.RemoveResultBundleFromRecentUpdatesList(_selectedResultBundle.Id);

            // TODO: filter events (only for finalization?) - create a global app setting for this
            if (_selectedResultBundle.WorkFlowStage >=
                GlobalSettingsHelper.ResultEntry.RecentlyUpdatedBundlesMinimumThreshold)
            {
                RecentlyUpdatedResultBundlesRepository.AddResultBundleToRecentUpdatesList(
                    _selectedResultBundle.Id,
                    _currentLabOrder.InvoiceId,
                    _selectedResultBundle
                        .WorkFlowStage,
                    SortPriorityType.Normal);
            }
#if DEBUG
            AppLogger.Leave();
#endif
        }

        private void btnRepeatProcedure_Click(object sender, EventArgs e)
        {
            if (!accessCanRepeatLabProcedure())
            {
                MessageDlg.Error("You are not allowed to repeat lab procedures!");
                return;
            }

            if (!MessageDlg.Confirm("Do you really want to repeat the lab procedure(s)?")) return;

            _lastSelectedBundleInGrid = null;

            if (_selectedTabContext != null)
            {
                if ((_selectedTabContext.ResultEditWorkflow == ResultEditWorkflowType.ResultVerification) ||
                    (_selectedTabContext.ResultEditWorkflow == ResultEditWorkflowType.ResultFinalization))
                {
                    bundleRepeatLabProcedure();
                }
            }

            refreshCurrentLabOrder();
        }

        private void ResultEditForm_KeyUp(object sender, KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.F5:
#if DEBUG
                    AppLogger.Enter();
#endif

                    tabInvoiceSearch.SelectedIndex = 0;
                    txtInvoiceId.Select();
                    txtInvoiceId.Focus();
                    txtInvoiceId.SelectAll();
                    e.Handled = true;
#if DEBUG
                    AppLogger.Leave();
#endif
                    break;
                case Keys.F8:
#if DEBUG
                    AppLogger.Enter();
#endif
                    if (btnPerformResultEdit.Enabled)
                    {
                        if (CaptchaDialog.ConfirmCaptcha(2, false, false))
                        {
                            performResultEdit(true);
                        }
                    }

                    e.Handled = true;
#if DEBUG
                    AppLogger.Leave();
#endif
                    break;
            }
        }

        private void btnPrint_Click(object sender, EventArgs e)
        {
            if (_currentLabOrder != null &&
                _selectedResultBundle != null &&
                _selectedResultBundle.WorkFlowStage >= WorkflowStageType.ResultEntry)
            {
                printResultBundles(_currentLabOrder, _selectedResultBundle, true);
            }
        }

        private void printResultBundles(ResultableLabOrder order,
            ResultBundleSlice bundle,
            bool showPreview)
        {
            InvoiceSlicePrintDto dtoInvoice = null;
            ResultBundlePrintDto dtoBundle = null;
            ResultBundlePrintDtoCompiler compiler = null;
            var resultType = TestResultType.Unarchived;

            WaitFormControl.WaitOperation(this, () =>
            {
                dtoInvoice = InvoiceSlicePrintDto.AssembleFrom(order);

                resultType = bundle.TestResultType;
                if (resultType == TestResultType.Unarchived)
                {
                    return;
                }

                compiler = new ResultBundlePrintDtoCompiler(bundle.Id);
                dtoBundle = compiler.CompileBundle();
            });

            switch (resultType)
            {
                case TestResultType.Discrete:
                    var discreteItems = compiler.GetDiscreteResultItems();
                    PrintHelper.PrintDiscretePreliminaryReport(showPreview, dtoInvoice, dtoBundle, discreteItems);
                    break;
                case TestResultType.UserTemplate:
                case TestResultType.Template:
                    var templateItem = compiler.GetTemplateResultContent();
                    PrintHelper.PrintTemplatePreliminaryReport(showPreview, dtoInvoice, dtoBundle, templateItem);
                    break;
            }

            AuditTrailRepository.LogOrderWorkflowEvent(
                AuditEventType.wfPreliminaryReportPrinted,
                order.InvoiceId,
                bundle.Id,
                null,
                bundle.WorkFlowStage,
                null);
        }

        private void btnSearchOrderId_Click(object sender, EventArgs e)
        {
            searchOrderId();
        }

        private void searchOrderId()
        {
            var orderId = txtOrderId.Text.Trim().ToUpper();
            if (!SharedUtilities.IsValidPatientId(orderId))
            {
                MessageDlg.Error("Invalid Order ID given!");
                txtOrderId.SelectAll();
                txtOrderId.Focus();
                return;
            }
#if DEBUG
            AppLogger.Enter();
#endif
            var startDate = txtOrderDate.DateTime.Date;
            var endDate = SharedUtilities.LastInstantOfDay(startDate);

            var foundOrders = PatientLabOrdersRepository.SearchActiveLabOrdersByPatientIdAndDateRange(
                orderId, startDate,
                endDate);

            if (foundOrders == null || foundOrders.Count == 0)
            {
                MessageDlg.Warning("Order #" + orderId + " was not found on the given date!");
                txtOrderId.SelectAll();
                txtOrderId.Focus();
                return;
            }

            var invoiceId = foundOrders.First().InvoiceId;
            if (invoiceId <= 0)
            {
#if DEBUG
                AppLogger.Error(string.Format("Invalid Invoice ID number {0} for Order #{1} dated {2}!", invoiceId,
                    orderId, startDate.ToShortDateString()));
#endif
                MessageDlg.Error(string.Format("Invalid Invoice ID number {0}!", invoiceId));
                return;
            }

            _currentLabOrder = null;
            _selectedResultBundle = null;
            _lastSelectedBundleInGrid = null;
            resetCurrentLabOrder();

            txtInvoiceId.Text = string.Empty;

            searchCurrentLabOrder(invoiceId, true);
#if DEBUG
            AppLogger.Leave();
#endif
        }

        private void btnExit_ItemClick(object sender, ItemClickEventArgs e)
        {
            Close();
        }

        private void txtOrderId_KeyUp(object sender, KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.Escape:
                    e.Handled = true;
                    txtOrderId.Text = string.Empty;
                    break;
                case Keys.Enter:
                    e.Handled = true;
                    searchOrderId();
                    break;
            }
        }

        private void btnEditDemographics_ItemClick(object sender, ItemClickEventArgs e)
        {
            if (_currentLabOrder != null)
            {
                if (!orderCanBeModified(WorkflowStageType.Canceled)) return;

                //var canEdit = CachedRolePermissionChecker.CheckPermission(PermissionCodes.LabOrders.EditReferrer);
                var canEdit = false;
                new LabOrderDemographicsEditDialog(_currentLabOrder.InvoiceId, canEdit).ExecuteDialog(this);

                searchCurrentLabOrder(_currentLabOrder.InvoiceId, true);
            }
        }

        private bool orderCanBeModified(WorkflowStageType wfThreshold)
        {
            if (wfThreshold != WorkflowStageType.Unknown)
            {
                if (_currentLabOrder.WorkflowStage >= wfThreshold)
                {
                    MessageDlg.Error(
                        "Order was fulfilled.\nOrder modification beyond fulfillment stage is not permitted.");
                    return false;
                }
            }

            return true;
        }

        #region Nested type: TabContext

        private sealed class TabContext
        {
            internal ResultEditWorkflowType ResultEditWorkflow { get; set; }

            internal DiscreteResultControl DiscreteControl { get; set; }

            internal TemplateResultControl TemplateControl { get; set; }

            internal void AllowEditing(bool allow)
            {
                if (DiscreteControl != null)
                {
                    DiscreteControl.AllowEditing = allow;
                    DiscreteControl.PopulateControls();
                }
                else if (TemplateControl != null)
                {
                    TemplateControl.AllowEditing = allow;
                    TemplateControl.PopulateControls();
                }
            }
        }

        #endregion
    }
}