﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the organization nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: BloodDifferentialsDialog.cs 968 2013-10-07 02:24:38Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using LabMaestro.Infrastructure.Client;

namespace LabMaestro.Controls.Win
{
    public partial class BloodDifferentialsDialog : XtraForm, IExecutableDialog
    {
        public BloodDifferentialsDialog()
        {
            InitializeComponent();
            WinUtils.ApplyTheme(this);
        }

        public float Neutrophils { get; set; }
        public float Lymphocytes { get; set; }
        public float Monocytes { get; set; }
        public float Eosinophils { get; set; }
        public float Basophils { get; set; }

        public bool ExecuteDialog(IWin32Window parent)
        {
            UpdateControls();
            return ShowDialog(parent) == DialogResult.OK;
        }

        public void UpdateControls()
        {
            // do nothing
        }

        private float getValue(TextEdit txtControl)
        {
            var text = txtControl.Text.Trim();
            float value;
            return float.TryParse(text, out value) ? value : 0;
        }

        private bool checkRange(float value, float min, float max, string param)
        {
            if (value > max || value < min)
            {
                var msg = "{0} count is not within normal range!\nMax: {1}\nMin: {2}\nValue entered: {3}";
                MessageDlg.Error(string.Format(msg, param, max, min, value));
                resetValues();
                return false;
            }
            return true;
        }

        private void resetValues()
        {
            Neutrophils = 0;
            Lymphocytes = 0;
            Monocytes = 0;
            Eosinophils = 0;
            Basophils = 0;
        }

        private void updateValuesFromControls()
        {
            Neutrophils = getValue(txtNeutro);
            Lymphocytes = getValue(txtLympho);
            Monocytes = getValue(txtMono);
            Eosinophils = getValue(txtEo);
            Basophils = getValue(txtBaso);
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            updateValuesFromControls();

            if (!checkRange(Neutrophils, 40, 75, "Neutrophil")) return;
            if (!checkRange(Lymphocytes, 20, 45, "Lymphocyte")) return;
            if (!checkRange(Monocytes, 2, 10, "Monocyte")) return;
            if (!checkRange(Eosinophils, 1, 6, "Eosinophil")) return;
            if (!checkRange(Basophils, 0, 1, "Basophil")) return;

            if (!tallyDifferentials(true)) return;

            DialogResult = DialogResult.OK;
        }

        private bool tallyDifferentials(bool showWarning)
        {
            var total = Neutrophils + Lymphocytes + Monocytes + Eosinophils + Basophils;
            lblTally.Text = String.Format("  {0:0.##}%", total);
            if (!showWarning) return true;
            if (total == 100f) return true;
            var msg = total > 100
                          ? "Tally of Differential Counts ({0}%) is greater than 100!"
                          : "Tally of Differential Counts ({0}%) is less than 100!";
            MessageDlg.Error(string.Format(msg, total));
            return false;
        }

        private void txtNeutro_Leave(object sender, EventArgs e)
        {
            updateValuesFromControls();
            tallyDifferentials(false);
        }

        private void txtNeutro_TextChanged(object sender, EventArgs e)
        {
            updateValuesFromControls();
            tallyDifferentials(false);
        }

        private void txtBaso_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                btnSave_Click(null, null);
            }
        }
    }
}