﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <sectionGroup name="gibraltar">
      <section name="listener" type="Gibraltar.Agent.ListenerElement, Gibraltar.Agent" />
      <section name="packager" type="Gibraltar.Agent.PackagerElement, Gibraltar.Agent" />
      <section name="publisher" type="Gibraltar.Agent.PublisherElement, Gibraltar.Agent" />
      <section name="sessionFile" type="Gibraltar.Agent.SessionFileElement, Gibraltar.Agent" />
      <section name="viewer" type="Gibraltar.Agent.ViewerElement, Gibraltar.Agent" />
      <section name="email" type="Gibraltar.Agent.EmailElement, Gibraltar.Agent" />
      <section name="server" type="Gibraltar.Agent.ServerElement, Gibraltar.Agent" />
      <section name="autoSendConsent" type="Gibraltar.Agent.AutoSendConsentElement, Gibraltar.Agent" />
      <section name="networkViewer" type="Gibraltar.Agent.NetworkViewerElement, Gibraltar.Agent" />
      <section name="properties" type="System.Configuration.NameValueSectionHandler" />
    </sectionGroup>
  </configSections>
  <gibraltar>
    <!-- Here is where all of the Gibraltar configuration sections can be added. -->
  </gibraltar>
  <system.diagnostics>
    <trace>
      <listeners>
        <add name="gibraltar" type="Gibraltar.Agent.LogListener, Gibraltar.Agent" />
      </listeners>
    </trace>
  </system.diagnostics>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="NLog" publicKeyToken="5120e14c03d0593c" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.0.0" newVersion="4.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="RabbitMQ.Client" publicKeyToken="89e7d7c5feba84ce" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.3.5.0" newVersion="3.3.5.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Common.Logging" publicKeyToken="af08829b84f0328e" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.2.0.0" newVersion="2.2.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="MassTransit" publicKeyToken="b8e0e9f2f1e657fa" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.9.0.0" newVersion="2.9.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Magnum" publicKeyToken="b800c4cfcdeea87b" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.1.2.0" newVersion="2.1.2.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="AutoMapper" publicKeyToken="be96cd2c38ef1005" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.2.1.0" newVersion="2.2.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Common.Logging.Core" publicKeyToken="af08829b84f0328e" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.2.0.0" newVersion="3.2.0.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
<startup><supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" /></startup></configuration>
