﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ReferrerClassCategorySelectorDialog.cs 516 2013-04-17 08:03:02Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using LabMaestro.Domain;

namespace LabMaestro.Controls.Win
{
    public partial class ReferrerClassCategorySelectorDialog : XtraForm
    {
        private readonly bool _applyRefClass;
        private readonly int _selectedCount;

        public ReferrerClassCategorySelectorDialog(bool applyRefClass, int selectedCount)
        {
            InitializeComponent();
            WinUtils.ApplyTheme(this);
            _applyRefClass = applyRefClass;
            _selectedCount = selectedCount;
            updateControls();
        }

        public short SelectedItemId { get; private set; }

        private void updateControls()
        {
            lblCount.Text = _selectedCount.ToString();

            if (_applyRefClass)
            {
                Text = "Select Referral Class";
                var refClasses = ReferralClassesRepository.FetchAll().OrderBy(x => x.Name).ToList();
                WinUtils.PopulateLookupControl(lookupControl, refClasses, "Name", "Id",
                                               new[] {"Name", "ReferralEligible"});
            }
            else
            {
                Text = "Select Physician Category";
                var categories = ReferrerCategoryRepository.FindAllItems().OrderBy(x => x.Name).ToList();
                WinUtils.PopulateLookupControl(lookupControl, categories, "Name", "Id", new[] {"Name", "IsActive"});
            }
        }

        private void btnAccept_Click(object sender, EventArgs e)
        {
            if (lookupControl.EditValue == null)
            {
                MessageDlg.Error("You must select an appropriate item!");
                lookupControl.Focus();
                return;
            }

            SelectedItemId = (short) lookupControl.EditValue;
            DialogResult = DialogResult.OK;
        }
    }
}