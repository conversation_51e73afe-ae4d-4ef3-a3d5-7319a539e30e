﻿namespace LabMaestro.Controls.Win
{
    partial class TestCatalogEditorDialog
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            Xceed.Grid.GradientMap gradientMap3 = new Xceed.Grid.GradientMap();
            Xceed.Grid.GradientStop gradientStop5 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientStop gradientStop6 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientMap gradientMap2 = new Xceed.Grid.GradientMap();
            Xceed.Grid.GradientStop gradientStop3 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientStop gradientStop4 = new Xceed.Grid.GradientStop();
            this.barManager1 = new DevExpress.XtraBars.BarManager(this.components);
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.btnReloadTests = new DevExpress.XtraBars.BarButtonItem();
            this.btnCreateNewTest = new DevExpress.XtraBars.BarButtonItem();
            this.btnSearch = new DevExpress.XtraBars.BarButtonItem();
            this.bar3 = new DevExpress.XtraBars.Bar();
            this.stbLabsCount = new DevExpress.XtraBars.BarStaticItem();
            this.stbTestsCount = new DevExpress.XtraBars.BarStaticItem();
            this.barAndDockingController1 = new DevExpress.XtraBars.BarAndDockingController(this.components);
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.lbLabs = new DevExpress.XtraEditors.ListBoxControl();
            this.tabFilter = new Syncfusion.Windows.Forms.Tools.TabControlAdv();
            this.tabPageAdv28 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv29 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv30 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv31 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv32 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv33 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv34 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv35 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv36 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv37 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv38 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv39 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv40 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv41 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv42 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv43 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv44 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv45 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv46 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv47 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv48 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv49 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv50 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv51 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv52 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv53 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv54 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.GridControl = new Xceed.Grid.GridControl();
            this.dataRowTemplate2 = new Xceed.Grid.DataRow();
            this.visualGridElementStyle2 = new Xceed.Grid.VisualGridElementStyle();
            this.dataRowStyle1 = new Xceed.Grid.VisualGridElementStyle();
            this.columnManagerRow2 = new Xceed.Grid.ColumnManagerRow();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lbLabs)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabFilter)).BeginInit();
            this.tabFilter.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.GridControl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataRowTemplate2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.columnManagerRow2)).BeginInit();
            this.SuspendLayout();
            // 
            // barManager1
            // 
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1,
            this.bar3});
            this.barManager1.Controller = this.barAndDockingController1;
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.stbLabsCount,
            this.stbTestsCount,
            this.btnReloadTests,
            this.btnCreateNewTest,
            this.btnSearch});
            this.barManager1.MaxItemId = 5;
            this.barManager1.StatusBar = this.bar3;
            // 
            // bar1
            // 
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.btnReloadTests),
            new DevExpress.XtraBars.LinkPersistInfo(this.btnCreateNewTest),
            new DevExpress.XtraBars.LinkPersistInfo(this.btnSearch)});
            this.bar1.OptionsBar.DrawDragBorder = false;
            this.bar1.OptionsBar.UseWholeRow = true;
            this.bar1.Text = "Tools";
            // 
            // btnReloadTests
            // 
            this.btnReloadTests.Caption = "Reload Tests";
            this.btnReloadTests.Glyph = global::LabMaestro.Controls.Win.Resources.reload16;
            this.btnReloadTests.Hint = "Reload Tests";
            this.btnReloadTests.Id = 2;
            this.btnReloadTests.Name = "btnReloadTests";
            this.btnReloadTests.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnReloadTests_ItemClick);
            // 
            // btnCreateNewTest
            // 
            this.btnCreateNewTest.Caption = "New";
            this.btnCreateNewTest.Glyph = global::LabMaestro.Controls.Win.Resources.new16;
            this.btnCreateNewTest.Hint = "Create New Test...";
            this.btnCreateNewTest.Id = 3;
            this.btnCreateNewTest.Name = "btnCreateNewTest";
            this.btnCreateNewTest.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnCreateNewTest_ItemClick);
            // 
            // btnSearch
            // 
            this.btnSearch.Caption = "Search";
            this.btnSearch.Glyph = global::LabMaestro.Controls.Win.Resources.search_16;
            this.btnSearch.Id = 4;
            this.btnSearch.Name = "btnSearch";
            this.btnSearch.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnSearch_ItemClick);
            // 
            // bar3
            // 
            this.bar3.BarName = "Status bar";
            this.bar3.CanDockStyle = DevExpress.XtraBars.BarCanDockStyle.Bottom;
            this.bar3.DockCol = 0;
            this.bar3.DockRow = 0;
            this.bar3.DockStyle = DevExpress.XtraBars.BarDockStyle.Bottom;
            this.bar3.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.stbLabsCount),
            new DevExpress.XtraBars.LinkPersistInfo(this.stbTestsCount)});
            this.bar3.OptionsBar.AllowQuickCustomization = false;
            this.bar3.OptionsBar.DrawDragBorder = false;
            this.bar3.OptionsBar.UseWholeRow = true;
            this.bar3.Text = "Status bar";
            // 
            // stbLabsCount
            // 
            this.stbLabsCount.Caption = "Lab(s): 0  ";
            this.stbLabsCount.Id = 0;
            this.stbLabsCount.Name = "stbLabsCount";
            this.stbLabsCount.TextAlignment = System.Drawing.StringAlignment.Near;
            // 
            // stbTestsCount
            // 
            this.stbTestsCount.Caption = "Test(s): 0  ";
            this.stbTestsCount.Id = 1;
            this.stbTestsCount.Name = "stbTestsCount";
            this.stbTestsCount.TextAlignment = System.Drawing.StringAlignment.Near;
            // 
            // barAndDockingController1
            // 
            this.barAndDockingController1.LookAndFeel.SkinName = "Office 2010 Blue";
            this.barAndDockingController1.LookAndFeel.UseDefaultLookAndFeel = false;
            this.barAndDockingController1.PaintStyleName = "Skin";
            this.barAndDockingController1.PropertiesBar.AllowLinkLighting = false;
            this.barAndDockingController1.PropertiesBar.DefaultGlyphSize = new System.Drawing.Size(16, 16);
            this.barAndDockingController1.PropertiesBar.DefaultLargeGlyphSize = new System.Drawing.Size(32, 32);
            // 
            // barDockControlTop
            // 
            this.barDockControlTop.CausesValidation = false;
            this.barDockControlTop.Dock = System.Windows.Forms.DockStyle.Top;
            this.barDockControlTop.Location = new System.Drawing.Point(0, 0);
            this.barDockControlTop.Size = new System.Drawing.Size(1344, 29);
            // 
            // barDockControlBottom
            // 
            this.barDockControlBottom.CausesValidation = false;
            this.barDockControlBottom.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.barDockControlBottom.Location = new System.Drawing.Point(0, 696);
            this.barDockControlBottom.Size = new System.Drawing.Size(1344, 25);
            // 
            // barDockControlLeft
            // 
            this.barDockControlLeft.CausesValidation = false;
            this.barDockControlLeft.Dock = System.Windows.Forms.DockStyle.Left;
            this.barDockControlLeft.Location = new System.Drawing.Point(0, 29);
            this.barDockControlLeft.Size = new System.Drawing.Size(0, 667);
            // 
            // barDockControlRight
            // 
            this.barDockControlRight.CausesValidation = false;
            this.barDockControlRight.Dock = System.Windows.Forms.DockStyle.Right;
            this.barDockControlRight.Location = new System.Drawing.Point(1344, 29);
            this.barDockControlRight.Size = new System.Drawing.Size(0, 667);
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 29);
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.lbLabs);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Controls.Add(this.tabFilter);
            this.splitContainerControl1.Panel2.Controls.Add(this.GridControl);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(1344, 667);
            this.splitContainerControl1.SplitterPosition = 236;
            this.splitContainerControl1.TabIndex = 5;
            // 
            // lbLabs
            // 
            this.lbLabs.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lbLabs.Appearance.Options.UseFont = true;
            this.lbLabs.Dock = System.Windows.Forms.DockStyle.Fill;
            this.lbLabs.Location = new System.Drawing.Point(0, 0);
            this.lbLabs.Name = "lbLabs";
            this.lbLabs.Size = new System.Drawing.Size(236, 667);
            this.lbLabs.SortOrder = System.Windows.Forms.SortOrder.Ascending;
            this.lbLabs.TabIndex = 0;
            this.lbLabs.SelectedIndexChanged += new System.EventHandler(this.lbLabs_SelectedIndexChanged);
            // 
            // tabFilter
            // 
            this.tabFilter.Controls.Add(this.tabPageAdv28);
            this.tabFilter.Controls.Add(this.tabPageAdv29);
            this.tabFilter.Controls.Add(this.tabPageAdv30);
            this.tabFilter.Controls.Add(this.tabPageAdv31);
            this.tabFilter.Controls.Add(this.tabPageAdv32);
            this.tabFilter.Controls.Add(this.tabPageAdv33);
            this.tabFilter.Controls.Add(this.tabPageAdv34);
            this.tabFilter.Controls.Add(this.tabPageAdv35);
            this.tabFilter.Controls.Add(this.tabPageAdv36);
            this.tabFilter.Controls.Add(this.tabPageAdv37);
            this.tabFilter.Controls.Add(this.tabPageAdv38);
            this.tabFilter.Controls.Add(this.tabPageAdv39);
            this.tabFilter.Controls.Add(this.tabPageAdv40);
            this.tabFilter.Controls.Add(this.tabPageAdv41);
            this.tabFilter.Controls.Add(this.tabPageAdv42);
            this.tabFilter.Controls.Add(this.tabPageAdv43);
            this.tabFilter.Controls.Add(this.tabPageAdv44);
            this.tabFilter.Controls.Add(this.tabPageAdv45);
            this.tabFilter.Controls.Add(this.tabPageAdv46);
            this.tabFilter.Controls.Add(this.tabPageAdv47);
            this.tabFilter.Controls.Add(this.tabPageAdv48);
            this.tabFilter.Controls.Add(this.tabPageAdv49);
            this.tabFilter.Controls.Add(this.tabPageAdv50);
            this.tabFilter.Controls.Add(this.tabPageAdv51);
            this.tabFilter.Controls.Add(this.tabPageAdv52);
            this.tabFilter.Controls.Add(this.tabPageAdv53);
            this.tabFilter.Controls.Add(this.tabPageAdv54);
            this.tabFilter.Dock = System.Windows.Forms.DockStyle.Top;
            this.tabFilter.Location = new System.Drawing.Point(0, 0);
            this.tabFilter.Name = "tabFilter";
            this.tabFilter.ShowScroll = false;
            this.tabFilter.Size = new System.Drawing.Size(1102, 30);
            this.tabFilter.SizeMode = Syncfusion.Windows.Forms.Tools.TabSizeMode.ShrinkToFit;
            this.tabFilter.SwitchPagesForDialogKeys = false;
            this.tabFilter.TabGap = 4;
            this.tabFilter.TabIndex = 35;
            this.tabFilter.TabStop = false;
            this.tabFilter.TabStyle = typeof(Syncfusion.Windows.Forms.Tools.TabRendererVS2010);
            this.tabFilter.ThemesEnabled = true;
            this.tabFilter.SelectedIndexChanged += new System.EventHandler(this.tabFilter_SelectedIndexChanged);
            // 
            // tabPageAdv28
            // 
            this.tabPageAdv28.Image = null;
            this.tabPageAdv28.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv28.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv28.Name = "tabPageAdv28";
            this.tabPageAdv28.ShowCloseButton = true;
            this.tabPageAdv28.Size = new System.Drawing.Size(1095, 0);
            this.tabPageAdv28.TabIndex = 28;
            this.tabPageAdv28.Text = "*";
            this.tabPageAdv28.ThemesEnabled = true;
            // 
            // tabPageAdv29
            // 
            this.tabPageAdv29.Image = null;
            this.tabPageAdv29.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv29.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv29.Name = "tabPageAdv29";
            this.tabPageAdv29.ShowCloseButton = true;
            this.tabPageAdv29.Size = new System.Drawing.Size(1095, 0);
            this.tabPageAdv29.TabIndex = 29;
            this.tabPageAdv29.Text = "A";
            this.tabPageAdv29.ThemesEnabled = true;
            // 
            // tabPageAdv30
            // 
            this.tabPageAdv30.Image = null;
            this.tabPageAdv30.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv30.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv30.Name = "tabPageAdv30";
            this.tabPageAdv30.ShowCloseButton = true;
            this.tabPageAdv30.Size = new System.Drawing.Size(1095, 0);
            this.tabPageAdv30.TabIndex = 30;
            this.tabPageAdv30.Text = "B";
            this.tabPageAdv30.ThemesEnabled = true;
            // 
            // tabPageAdv31
            // 
            this.tabPageAdv31.Image = null;
            this.tabPageAdv31.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv31.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv31.Name = "tabPageAdv31";
            this.tabPageAdv31.ShowCloseButton = true;
            this.tabPageAdv31.Size = new System.Drawing.Size(1095, 0);
            this.tabPageAdv31.TabIndex = 31;
            this.tabPageAdv31.Text = "C";
            this.tabPageAdv31.ThemesEnabled = true;
            // 
            // tabPageAdv32
            // 
            this.tabPageAdv32.Image = null;
            this.tabPageAdv32.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv32.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv32.Name = "tabPageAdv32";
            this.tabPageAdv32.ShowCloseButton = true;
            this.tabPageAdv32.Size = new System.Drawing.Size(1095, 0);
            this.tabPageAdv32.TabIndex = 32;
            this.tabPageAdv32.Text = "D";
            this.tabPageAdv32.ThemesEnabled = true;
            // 
            // tabPageAdv33
            // 
            this.tabPageAdv33.Image = null;
            this.tabPageAdv33.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv33.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv33.Name = "tabPageAdv33";
            this.tabPageAdv33.ShowCloseButton = true;
            this.tabPageAdv33.Size = new System.Drawing.Size(1095, 0);
            this.tabPageAdv33.TabIndex = 33;
            this.tabPageAdv33.Text = "E";
            this.tabPageAdv33.ThemesEnabled = true;
            // 
            // tabPageAdv34
            // 
            this.tabPageAdv34.Image = null;
            this.tabPageAdv34.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv34.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv34.Name = "tabPageAdv34";
            this.tabPageAdv34.ShowCloseButton = true;
            this.tabPageAdv34.Size = new System.Drawing.Size(1095, 0);
            this.tabPageAdv34.TabIndex = 34;
            this.tabPageAdv34.Text = "F";
            this.tabPageAdv34.ThemesEnabled = true;
            // 
            // tabPageAdv35
            // 
            this.tabPageAdv35.Image = null;
            this.tabPageAdv35.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv35.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv35.Name = "tabPageAdv35";
            this.tabPageAdv35.ShowCloseButton = true;
            this.tabPageAdv35.Size = new System.Drawing.Size(1095, 0);
            this.tabPageAdv35.TabIndex = 35;
            this.tabPageAdv35.Text = "G";
            this.tabPageAdv35.ThemesEnabled = true;
            // 
            // tabPageAdv36
            // 
            this.tabPageAdv36.Image = null;
            this.tabPageAdv36.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv36.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv36.Name = "tabPageAdv36";
            this.tabPageAdv36.ShowCloseButton = true;
            this.tabPageAdv36.Size = new System.Drawing.Size(1095, 0);
            this.tabPageAdv36.TabIndex = 36;
            this.tabPageAdv36.Text = "H";
            this.tabPageAdv36.ThemesEnabled = true;
            // 
            // tabPageAdv37
            // 
            this.tabPageAdv37.Image = null;
            this.tabPageAdv37.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv37.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv37.Name = "tabPageAdv37";
            this.tabPageAdv37.ShowCloseButton = true;
            this.tabPageAdv37.Size = new System.Drawing.Size(1095, 0);
            this.tabPageAdv37.TabIndex = 37;
            this.tabPageAdv37.Text = "I";
            this.tabPageAdv37.ThemesEnabled = true;
            // 
            // tabPageAdv38
            // 
            this.tabPageAdv38.Image = null;
            this.tabPageAdv38.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv38.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv38.Name = "tabPageAdv38";
            this.tabPageAdv38.ShowCloseButton = true;
            this.tabPageAdv38.Size = new System.Drawing.Size(1095, 0);
            this.tabPageAdv38.TabIndex = 38;
            this.tabPageAdv38.Text = "J";
            this.tabPageAdv38.ThemesEnabled = true;
            // 
            // tabPageAdv39
            // 
            this.tabPageAdv39.Image = null;
            this.tabPageAdv39.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv39.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv39.Name = "tabPageAdv39";
            this.tabPageAdv39.ShowCloseButton = true;
            this.tabPageAdv39.Size = new System.Drawing.Size(1095, 0);
            this.tabPageAdv39.TabIndex = 39;
            this.tabPageAdv39.Text = "K";
            this.tabPageAdv39.ThemesEnabled = true;
            // 
            // tabPageAdv40
            // 
            this.tabPageAdv40.Image = null;
            this.tabPageAdv40.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv40.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv40.Name = "tabPageAdv40";
            this.tabPageAdv40.ShowCloseButton = true;
            this.tabPageAdv40.Size = new System.Drawing.Size(1095, 0);
            this.tabPageAdv40.TabIndex = 40;
            this.tabPageAdv40.Text = "L";
            this.tabPageAdv40.ThemesEnabled = true;
            // 
            // tabPageAdv41
            // 
            this.tabPageAdv41.Image = null;
            this.tabPageAdv41.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv41.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv41.Name = "tabPageAdv41";
            this.tabPageAdv41.ShowCloseButton = true;
            this.tabPageAdv41.Size = new System.Drawing.Size(1095, 0);
            this.tabPageAdv41.TabIndex = 41;
            this.tabPageAdv41.Text = "M";
            this.tabPageAdv41.ThemesEnabled = true;
            // 
            // tabPageAdv42
            // 
            this.tabPageAdv42.Image = null;
            this.tabPageAdv42.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv42.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv42.Name = "tabPageAdv42";
            this.tabPageAdv42.ShowCloseButton = true;
            this.tabPageAdv42.Size = new System.Drawing.Size(1095, 0);
            this.tabPageAdv42.TabIndex = 42;
            this.tabPageAdv42.Text = "N";
            this.tabPageAdv42.ThemesEnabled = true;
            // 
            // tabPageAdv43
            // 
            this.tabPageAdv43.Image = null;
            this.tabPageAdv43.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv43.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv43.Name = "tabPageAdv43";
            this.tabPageAdv43.ShowCloseButton = true;
            this.tabPageAdv43.Size = new System.Drawing.Size(1095, 0);
            this.tabPageAdv43.TabIndex = 43;
            this.tabPageAdv43.Text = "O";
            this.tabPageAdv43.ThemesEnabled = true;
            // 
            // tabPageAdv44
            // 
            this.tabPageAdv44.Image = null;
            this.tabPageAdv44.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv44.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv44.Name = "tabPageAdv44";
            this.tabPageAdv44.ShowCloseButton = true;
            this.tabPageAdv44.Size = new System.Drawing.Size(1095, 0);
            this.tabPageAdv44.TabIndex = 44;
            this.tabPageAdv44.Text = "P";
            this.tabPageAdv44.ThemesEnabled = true;
            // 
            // tabPageAdv45
            // 
            this.tabPageAdv45.Image = null;
            this.tabPageAdv45.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv45.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv45.Name = "tabPageAdv45";
            this.tabPageAdv45.ShowCloseButton = true;
            this.tabPageAdv45.Size = new System.Drawing.Size(1095, 0);
            this.tabPageAdv45.TabIndex = 45;
            this.tabPageAdv45.Text = "Q";
            this.tabPageAdv45.ThemesEnabled = true;
            // 
            // tabPageAdv46
            // 
            this.tabPageAdv46.Image = null;
            this.tabPageAdv46.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv46.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv46.Name = "tabPageAdv46";
            this.tabPageAdv46.ShowCloseButton = true;
            this.tabPageAdv46.Size = new System.Drawing.Size(1095, 0);
            this.tabPageAdv46.TabIndex = 46;
            this.tabPageAdv46.Text = "R";
            this.tabPageAdv46.ThemesEnabled = true;
            // 
            // tabPageAdv47
            // 
            this.tabPageAdv47.Image = null;
            this.tabPageAdv47.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv47.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv47.Name = "tabPageAdv47";
            this.tabPageAdv47.ShowCloseButton = true;
            this.tabPageAdv47.Size = new System.Drawing.Size(1095, 0);
            this.tabPageAdv47.TabIndex = 47;
            this.tabPageAdv47.Text = "S";
            this.tabPageAdv47.ThemesEnabled = true;
            // 
            // tabPageAdv48
            // 
            this.tabPageAdv48.Image = null;
            this.tabPageAdv48.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv48.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv48.Name = "tabPageAdv48";
            this.tabPageAdv48.ShowCloseButton = true;
            this.tabPageAdv48.Size = new System.Drawing.Size(1095, 0);
            this.tabPageAdv48.TabIndex = 48;
            this.tabPageAdv48.Text = "T";
            this.tabPageAdv48.ThemesEnabled = true;
            // 
            // tabPageAdv49
            // 
            this.tabPageAdv49.Image = null;
            this.tabPageAdv49.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv49.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv49.Name = "tabPageAdv49";
            this.tabPageAdv49.ShowCloseButton = true;
            this.tabPageAdv49.Size = new System.Drawing.Size(1095, 0);
            this.tabPageAdv49.TabIndex = 49;
            this.tabPageAdv49.Text = "U";
            this.tabPageAdv49.ThemesEnabled = true;
            // 
            // tabPageAdv50
            // 
            this.tabPageAdv50.Image = null;
            this.tabPageAdv50.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv50.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv50.Name = "tabPageAdv50";
            this.tabPageAdv50.ShowCloseButton = true;
            this.tabPageAdv50.Size = new System.Drawing.Size(1095, 0);
            this.tabPageAdv50.TabIndex = 50;
            this.tabPageAdv50.Text = "V";
            this.tabPageAdv50.ThemesEnabled = true;
            // 
            // tabPageAdv51
            // 
            this.tabPageAdv51.Image = null;
            this.tabPageAdv51.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv51.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv51.Name = "tabPageAdv51";
            this.tabPageAdv51.ShowCloseButton = true;
            this.tabPageAdv51.Size = new System.Drawing.Size(1095, 0);
            this.tabPageAdv51.TabIndex = 51;
            this.tabPageAdv51.Text = "W";
            this.tabPageAdv51.ThemesEnabled = true;
            // 
            // tabPageAdv52
            // 
            this.tabPageAdv52.Image = null;
            this.tabPageAdv52.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv52.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv52.Name = "tabPageAdv52";
            this.tabPageAdv52.ShowCloseButton = true;
            this.tabPageAdv52.Size = new System.Drawing.Size(1095, 0);
            this.tabPageAdv52.TabIndex = 52;
            this.tabPageAdv52.Text = "X";
            this.tabPageAdv52.ThemesEnabled = true;
            // 
            // tabPageAdv53
            // 
            this.tabPageAdv53.Image = null;
            this.tabPageAdv53.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv53.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv53.Name = "tabPageAdv53";
            this.tabPageAdv53.ShowCloseButton = true;
            this.tabPageAdv53.Size = new System.Drawing.Size(1095, 0);
            this.tabPageAdv53.TabIndex = 53;
            this.tabPageAdv53.Text = "Y";
            this.tabPageAdv53.ThemesEnabled = true;
            // 
            // tabPageAdv54
            // 
            this.tabPageAdv54.Image = null;
            this.tabPageAdv54.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv54.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv54.Name = "tabPageAdv54";
            this.tabPageAdv54.ShowCloseButton = true;
            this.tabPageAdv54.Size = new System.Drawing.Size(1095, 0);
            this.tabPageAdv54.TabIndex = 54;
            this.tabPageAdv54.Text = "Z";
            this.tabPageAdv54.ThemesEnabled = true;
            // 
            // GridControl
            // 
            this.GridControl.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.GridControl.ClipCurrentCellSelection = false;
            this.GridControl.DataRowTemplate = this.dataRowTemplate2;
            this.GridControl.DataRowTemplateStyles.Add(this.visualGridElementStyle2);
            this.GridControl.DataRowTemplateStyles.Add(this.dataRowStyle1);
            // 
            // 
            // 
            this.GridControl.ErrorVisualStyle.OverrideUIStyle = false;
            // 
            // 
            // 
            this.GridControl.FixedColumnSplitter.AllowRepositioning = false;
            this.GridControl.FixedColumnSplitter.Visible = false;
            this.GridControl.FixedHeaderRows.Add(this.columnManagerRow2);
            this.GridControl.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.GridControl.GridLineColor = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(239)))), ((int)(((byte)(255)))));
            this.GridControl.GridLineStyle = System.Drawing.Drawing2D.DashStyle.Dot;
            this.GridControl.InactiveSelectionBackColor = System.Drawing.Color.RoyalBlue;
            // 
            // 
            // 
            this.GridControl.InactiveSelectionVisualStyle.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(229)))), ((int)(((byte)(229)))), ((int)(((byte)(229)))));
            this.GridControl.InactiveSelectionVisualStyle.ForeColor = System.Drawing.Color.Black;
            this.GridControl.InactiveSelectionVisualStyle.OverrideUIStyle = true;
            this.GridControl.Location = new System.Drawing.Point(0, 30);
            this.GridControl.Margin = new System.Windows.Forms.Padding(5);
            this.GridControl.Name = "GridControl";
            this.GridControl.OverrideUIStyle = false;
            this.GridControl.ReadOnly = true;
            // 
            // 
            // 
            this.GridControl.RowSelectorPane.AllowRowResize = false;
            this.GridControl.RowSelectorPane.CurrentImageIndex = 16;
            gradientMap3.GradientMode = Xceed.UI.GradientMode.Horizontal;
            gradientStop5.Color = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(239)))), ((int)(((byte)(255)))));
            gradientStop5.Offset = 0D;
            gradientStop6.Color = System.Drawing.Color.FromArgb(((int)(((byte)(175)))), ((int)(((byte)(210)))), ((int)(((byte)(255)))));
            gradientStop6.Offset = 1D;
            gradientMap3.GradientStops.Add(gradientStop5);
            gradientMap3.GradientStops.Add(gradientStop6);
            this.GridControl.RowSelectorPane.GradientMap = gradientMap3;
            // 
            // 
            // 
            this.GridControl.RowSelectorPane.HotVisualStyle.OverrideUIStyle = true;
            this.GridControl.RowSelectorPane.OverrideUIStyle = true;
            this.GridControl.RowSelectorPane.SelectedImageIndex = 20;
            this.GridControl.RowSelectorPane.Width = 17;
            this.GridControl.SelectionBackColor = System.Drawing.Color.Indigo;
            this.GridControl.SelectionMode = System.Windows.Forms.SelectionMode.One;
            // 
            // 
            // 
            this.GridControl.SelectionVisualStyle.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(167)))), ((int)(((byte)(205)))), ((int)(((byte)(240)))));
            this.GridControl.SelectionVisualStyle.ForeColor = System.Drawing.Color.Black;
            this.GridControl.SelectionVisualStyle.OverrideUIStyle = true;
            this.GridControl.ShowFocusRectangle = false;
            // 
            // 
            // 
            this.GridControl.SideMargin.Visible = false;
            this.GridControl.Size = new System.Drawing.Size(1102, 612);
            this.GridControl.SynchronizeDetailGrids = false;
            this.GridControl.TabIndex = 30;
            this.GridControl.TabStop = false;
            this.GridControl.TreeLineColor = System.Drawing.Color.Silver;
            this.GridControl.UIStyle = Xceed.UI.UIStyle.System;
            this.GridControl.KeyUp += new System.Windows.Forms.KeyEventHandler(this.GridControl_KeyUp);
            // 
            // visualGridElementStyle2
            // 
            // 
            // 
            // 
            this.visualGridElementStyle2.HotVisualStyle.OverrideUIStyle = true;
            this.visualGridElementStyle2.OverrideUIStyle = false;
            // 
            // dataRowStyle1
            // 
            this.dataRowStyle1.BackColor = System.Drawing.Color.AliceBlue;
            // 
            // columnManagerRow2
            // 
            this.columnManagerRow2.AllowColumnReorder = false;
            this.columnManagerRow2.AllowColumnResize = true;
            this.columnManagerRow2.AllowSort = false;
            this.columnManagerRow2.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            gradientStop3.Color = System.Drawing.Color.FromArgb(((int)(((byte)(252)))), ((int)(((byte)(253)))), ((int)(((byte)(255)))));
            gradientStop3.Offset = 0D;
            gradientStop4.Color = System.Drawing.Color.FromArgb(((int)(((byte)(196)))), ((int)(((byte)(221)))), ((int)(((byte)(255)))));
            gradientStop4.Offset = 1D;
            gradientMap2.GradientStops.Add(gradientStop3);
            gradientMap2.GradientStops.Add(gradientStop4);
            this.columnManagerRow2.GradientMap = gradientMap2;
            this.columnManagerRow2.Height = 23;
            // 
            // 
            // 
            this.columnManagerRow2.HotVisualStyle.OverrideUIStyle = false;
            this.columnManagerRow2.OverrideUIStyle = true;
            // 
            // TestCatalogEditorDialog
            // 
            this.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.ClientSize = new System.Drawing.Size(1344, 721);
            this.Controls.Add(this.splitContainerControl1);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.LookAndFeel.SkinName = "Office 2010 Blue";
            this.LookAndFeel.UseDefaultLookAndFeel = false;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "TestCatalogEditorDialog";
            this.SizeGripStyle = System.Windows.Forms.SizeGripStyle.Hide;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "TestCatalogEditorDialog";
            this.Load += new System.EventHandler(this.TestCatalogEditorDialog_Load);
            this.Resize += new System.EventHandler(this.TestCatalogEditorDialog_Resize);
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.lbLabs)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabFilter)).EndInit();
            this.tabFilter.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.GridControl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataRowTemplate2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.columnManagerRow2)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.Bar bar3;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraEditors.ListBoxControl lbLabs;
        private Xceed.Grid.GridControl GridControl;
        private Xceed.Grid.DataRow dataRowTemplate2;
        private Xceed.Grid.VisualGridElementStyle visualGridElementStyle2;
        private Xceed.Grid.VisualGridElementStyle dataRowStyle1;
        private Xceed.Grid.ColumnManagerRow columnManagerRow2;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController1;
        private DevExpress.XtraBars.BarStaticItem stbLabsCount;
        private DevExpress.XtraBars.BarStaticItem stbTestsCount;
        private DevExpress.XtraBars.BarButtonItem btnReloadTests;
        private DevExpress.XtraBars.BarButtonItem btnCreateNewTest;
        private Syncfusion.Windows.Forms.Tools.TabControlAdv tabFilter;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv28;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv29;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv30;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv31;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv32;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv33;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv34;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv35;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv36;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv37;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv38;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv39;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv40;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv41;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv42;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv43;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv44;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv45;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv46;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv47;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv48;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv49;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv50;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv51;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv52;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv53;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv54;
        private DevExpress.XtraBars.BarButtonItem btnSearch;
    }
}