﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: TestParamBulkEditorDialog.cs 516 2013-04-17 08:03:02Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using LabMaestro.Domain;

namespace LabMaestro.Controls.Win
{
    public partial class TestParamBulkEditorDialog : XtraForm
    {
        public TestParamBulkEditorDialog()
        {
            InitializeComponent();
            Lines = new List<string>();
            WinUtils.ApplyTheme(this);
        }

        public List<string> Lines { get; private set; }

        public List<DiscreteReportLineItemSlice> DiscreteItems { get; set; }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            Lines.Clear();
            var lines = txtBulkEditor.Lines;

            foreach (var line in lines)
            {
                if (!string.IsNullOrEmpty(line.Trim())) Lines.Add(line.Trim());
            }

            if (Lines.Count > 0)
            {
                DialogResult = DialogResult.OK;
            }
            else
            {
                MessageDlg.Warning("Please enter some parameters!");
            }
        }

        private void btnCopy_Click(object sender, EventArgs e)
        {
            if (DiscreteItems != null)
            {
                var lines = new List<string>();
                lines.AddRange(txtBulkEditor.Lines);
                lines.AddRange(DiscreteItems.Select(slice => slice.Parameter.Trim()));

                if (lines.Count > 0)
                {
                    txtBulkEditor.Lines = lines.ToArray();
                }
            }
        }

        private void btnCopyFromTest_Click(object sender, EventArgs e)
        {
            using (var form = new TestSelectionDialog())
            {
                if (form.ShowDialog(this) == DialogResult.OK)
                {
                    var testId = form.SelectedTestId;
                    if (testId > 0)
                    {
                        var items = DiscreteReportLineItemsRepository.GetAllItemsForTest(testId);
                        txtBulkEditor.Lines = items.Select(x => x.Parameter).ToArray();
                    }
                }
            }
        }
    }
}