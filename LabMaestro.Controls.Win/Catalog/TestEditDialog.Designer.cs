﻿namespace LabMaestro.Controls.Win
{
    partial class TestEditDialog
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject1 = new DevExpress.Utils.SerializableAppearanceObject();
            Xceed.Grid.GradientMap gradientMap2 = new Xceed.Grid.GradientMap();
            Xceed.Grid.GradientStop gradientStop3 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientStop gradientStop4 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientMap gradientMap1 = new Xceed.Grid.GradientMap();
            Xceed.Grid.GradientStop gradientStop1 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientStop gradientStop2 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientMap gradientMap4 = new Xceed.Grid.GradientMap();
            Xceed.Grid.GradientStop gradientStop7 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientStop gradientStop8 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientMap gradientMap3 = new Xceed.Grid.GradientMap();
            Xceed.Grid.GradientStop gradientStop5 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientStop gradientStop6 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientMap gradientMap6 = new Xceed.Grid.GradientMap();
            Xceed.Grid.GradientStop gradientStop11 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientStop gradientStop12 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientMap gradientMap5 = new Xceed.Grid.GradientMap();
            Xceed.Grid.GradientStop gradientStop9 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientStop gradientStop10 = new Xceed.Grid.GradientStop();
            this.tabControl = new DevExpress.XtraTab.XtraTabControl();
            this.pageGeneral = new DevExpress.XtraTab.XtraTabPage();
            this.txtCostBasis = new DevExpress.XtraEditors.TextEdit();
            this.labelControl32 = new DevExpress.XtraEditors.LabelControl();
            this.txtSubOrderPrice = new DevExpress.XtraEditors.TextEdit();
            this.labelControl30 = new DevExpress.XtraEditors.LabelControl();
            this.btnEditDefaultTemplate = new DevExpress.XtraEditors.SimpleButton();
            this.btnTestGenerateCode = new DevExpress.XtraEditors.SimpleButton();
            this.btnTestGeneratePrefix = new DevExpress.XtraEditors.SimpleButton();
            this.btnSave = new DevExpress.XtraEditors.SimpleButton();
            this.txtMnemonics = new DevExpress.XtraEditors.TextEdit();
            this.labelControl27 = new DevExpress.XtraEditors.LabelControl();
            this.btnUpdateDefaultTemplatesList = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl17 = new DevExpress.XtraEditors.LabelControl();
            this.luDefaultTemplate = new DevExpress.XtraEditors.LookUpEdit();
            this.btnTestCodeCheckUnique = new DevExpress.XtraEditors.SimpleButton();
            this.btnCopyShortName = new DevExpress.XtraEditors.SimpleButton();
            this.btnCopyCanonName = new DevExpress.XtraEditors.SimpleButton();
            this.btnUpdateTemplateGroupsList = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.btnCopyPerformingLab = new DevExpress.XtraEditors.SimpleButton();
            this.btnCopyResultingLab = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl14 = new DevExpress.XtraEditors.LabelControl();
            this.luTemplateGroup = new DevExpress.XtraEditors.LookUpEdit();
            this.labelControl15 = new DevExpress.XtraEditors.LabelControl();
            this.luTatGroup = new DevExpress.XtraEditors.LookUpEdit();
            this.labelControl11 = new DevExpress.XtraEditors.LabelControl();
            this.luResultingLab = new DevExpress.XtraEditors.LookUpEdit();
            this.labelControl9 = new DevExpress.XtraEditors.LabelControl();
            this.luPerformingLab = new DevExpress.XtraEditors.LookUpEdit();
            this.labelControl13 = new DevExpress.XtraEditors.LabelControl();
            this.dteInactiveDate = new DevExpress.XtraEditors.DateEdit();
            this.lblLastModifiedDate = new DevExpress.XtraEditors.LabelControl();
            this.labelControl12 = new DevExpress.XtraEditors.LabelControl();
            this.lblCreatedDate = new DevExpress.XtraEditors.LabelControl();
            this.labelControl10 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            this.rgTestSortPriority = new DevExpress.XtraEditors.RadioGroup();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.rgReqSlip = new DevExpress.XtraEditors.RadioGroup();
            this.txtListPrice = new DevExpress.XtraEditors.TextEdit();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.chkIsActive = new DevExpress.XtraEditors.CheckEdit();
            this.txtCanonName = new DevExpress.XtraEditors.TextEdit();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.txtShortName = new DevExpress.XtraEditors.TextEdit();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.txtTestCode = new DevExpress.XtraEditors.TextEdit();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.lblTestId = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.pageDiscrete = new DevExpress.XtraTab.XtraTabPage();
            this.txtDefaultResult = new DevExpress.XtraEditors.TextEdit();
            this.labelControl33 = new DevExpress.XtraEditors.LabelControl();
            this.btnDiscreteImportParameters = new DevExpress.XtraEditors.SimpleButton();
            this.btnDiscreteClearAll = new DevExpress.XtraEditors.SimpleButton();
            this.btnDiscreteBulkAdd = new DevExpress.XtraEditors.SimpleButton();
            this.lblDiscreteSort = new DevExpress.XtraEditors.LabelControl();
            this.btnDiscreteParamUnderline = new DevExpress.XtraEditors.SimpleButton();
            this.btnDiscreteParamItalic = new DevExpress.XtraEditors.SimpleButton();
            this.chkDiscreteResultable = new DevExpress.XtraEditors.CheckEdit();
            this.btnDiscreteParamBold = new DevExpress.XtraEditors.SimpleButton();
            this.txtDiscreteRefRange = new DevExpress.XtraEditors.MemoEdit();
            this.labelControl22 = new DevExpress.XtraEditors.LabelControl();
            this.txtDiscreteUnits = new DevExpress.XtraEditors.TextEdit();
            this.labelControl21 = new DevExpress.XtraEditors.LabelControl();
            this.txtDiscreteParameter = new DevExpress.XtraEditors.TextEdit();
            this.labelControl20 = new DevExpress.XtraEditors.LabelControl();
            this.txtDiscreteIndent = new DevExpress.XtraEditors.TextEdit();
            this.labelControl19 = new DevExpress.XtraEditors.LabelControl();
            this.lblDiscreteId = new DevExpress.XtraEditors.LabelControl();
            this.labelControl18 = new DevExpress.XtraEditors.LabelControl();
            this.btnDiscreteDown = new DevExpress.XtraEditors.SimpleButton();
            this.btnDiscreteUp = new DevExpress.XtraEditors.SimpleButton();
            this.btnDiscreteDelete = new DevExpress.XtraEditors.SimpleButton();
            this.btnDiscreteNew = new DevExpress.XtraEditors.SimpleButton();
            this.btnDiscreteAdd = new DevExpress.XtraEditors.SimpleButton();
            this.btnDiscreteSave = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl16 = new DevExpress.XtraEditors.LabelControl();
            this.grdDiscreteReportItems = new Xceed.Grid.GridControl();
            this.dataRowTemplate2 = new Xceed.Grid.DataRow();
            this.visualGridElementStyle2 = new Xceed.Grid.VisualGridElementStyle();
            this.dataRowStyle1 = new Xceed.Grid.VisualGridElementStyle();
            this.columnManagerRow2 = new Xceed.Grid.ColumnManagerRow();
            this.pageReqParams = new DevExpress.XtraTab.XtraTabPage();
            this.btnReqImportParameters = new DevExpress.XtraEditors.SimpleButton();
            this.btnReqClearAll = new DevExpress.XtraEditors.SimpleButton();
            this.btnReqBulkAdd = new DevExpress.XtraEditors.SimpleButton();
            this.lblReqSort = new DevExpress.XtraEditors.LabelControl();
            this.btnReqDelete = new DevExpress.XtraEditors.SimpleButton();
            this.btnReqNew = new DevExpress.XtraEditors.SimpleButton();
            this.btnReqAdd = new DevExpress.XtraEditors.SimpleButton();
            this.btnReqSave = new DevExpress.XtraEditors.SimpleButton();
            this.txtReqParameter = new DevExpress.XtraEditors.TextEdit();
            this.labelControl25 = new DevExpress.XtraEditors.LabelControl();
            this.txtReqIndent = new DevExpress.XtraEditors.TextEdit();
            this.labelControl26 = new DevExpress.XtraEditors.LabelControl();
            this.lblReqId = new DevExpress.XtraEditors.LabelControl();
            this.labelControl28 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl29 = new DevExpress.XtraEditors.LabelControl();
            this.btnReqDown = new DevExpress.XtraEditors.SimpleButton();
            this.btnReqUp = new DevExpress.XtraEditors.SimpleButton();
            this.grdReqParams = new Xceed.Grid.GridControl();
            this.dataRow1 = new Xceed.Grid.DataRow();
            this.visualGridElementStyle1 = new Xceed.Grid.VisualGridElementStyle();
            this.visualGridElementStyle3 = new Xceed.Grid.VisualGridElementStyle();
            this.columnManagerRow1 = new Xceed.Grid.ColumnManagerRow();
            this.pageBillableItems = new DevExpress.XtraTab.XtraTabPage();
            this.btnBIDelete = new DevExpress.XtraEditors.SimpleButton();
            this.btnBINew = new DevExpress.XtraEditors.SimpleButton();
            this.btnBIAdd = new DevExpress.XtraEditors.SimpleButton();
            this.btnBISave = new DevExpress.XtraEditors.SimpleButton();
            this.txtBIQty = new DevExpress.XtraEditors.TextEdit();
            this.labelControl24 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl23 = new DevExpress.XtraEditors.LabelControl();
            this.luBillableItem = new DevExpress.XtraEditors.LookUpEdit();
            this.labelControl31 = new DevExpress.XtraEditors.LabelControl();
            this.rgBIOptimize = new DevExpress.XtraEditors.RadioGroup();
            this.grdBillableItems = new Xceed.Grid.GridControl();
            this.dataRow2 = new Xceed.Grid.DataRow();
            this.visualGridElementStyle4 = new Xceed.Grid.VisualGridElementStyle();
            this.visualGridElementStyle5 = new Xceed.Grid.VisualGridElementStyle();
            this.columnManagerRow3 = new Xceed.Grid.ColumnManagerRow();
            this.xtraTabPage1 = new DevExpress.XtraTab.XtraTabPage();
            this.xtraTabPage2 = new DevExpress.XtraTab.XtraTabPage();
            this.btnClose = new DevExpress.XtraEditors.SimpleButton();
            this.txtReportLineGroupTag = new DevExpress.XtraEditors.TextEdit();
            this.labelControl34 = new DevExpress.XtraEditors.LabelControl();
            ((System.ComponentModel.ISupportInitialize)(this.tabControl)).BeginInit();
            this.tabControl.SuspendLayout();
            this.pageGeneral.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtCostBasis.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtSubOrderPrice.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtMnemonics.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.luDefaultTemplate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.luTemplateGroup.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.luTatGroup.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.luResultingLab.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.luPerformingLab.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dteInactiveDate.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dteInactiveDate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rgTestSortPriority.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rgReqSlip.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtListPrice.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkIsActive.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCanonName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtShortName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTestCode.Properties)).BeginInit();
            this.pageDiscrete.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtDefaultResult.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkDiscreteResultable.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDiscreteRefRange.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDiscreteUnits.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDiscreteParameter.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDiscreteIndent.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdDiscreteReportItems)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataRowTemplate2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.columnManagerRow2)).BeginInit();
            this.pageReqParams.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtReqParameter.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtReqIndent.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdReqParams)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataRow1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.columnManagerRow1)).BeginInit();
            this.pageBillableItems.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtBIQty.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.luBillableItem.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rgBIOptimize.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdBillableItems)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataRow2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.columnManagerRow3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtReportLineGroupTag.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // tabControl
            // 
            this.tabControl.HeaderAutoFill = DevExpress.Utils.DefaultBoolean.True;
            this.tabControl.HeaderButtonsShowMode = DevExpress.XtraTab.TabButtonShowMode.Never;
            this.tabControl.HeaderOrientation = DevExpress.XtraTab.TabOrientation.Horizontal;
            this.tabControl.Location = new System.Drawing.Point(12, 12);
            this.tabControl.LookAndFeel.SkinName = "Seven";
            this.tabControl.LookAndFeel.UseDefaultLookAndFeel = false;
            this.tabControl.MultiLine = DevExpress.Utils.DefaultBoolean.False;
            this.tabControl.Name = "tabControl";
            this.tabControl.PaintStyleName = "Skin";
            this.tabControl.SelectedTabPage = this.pageGeneral;
            this.tabControl.ShowHeaderFocus = DevExpress.Utils.DefaultBoolean.False;
            this.tabControl.ShowTabHeader = DevExpress.Utils.DefaultBoolean.True;
            this.tabControl.Size = new System.Drawing.Size(576, 507);
            this.tabControl.TabIndex = 0;
            this.tabControl.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.pageGeneral,
            this.pageDiscrete,
            this.pageReqParams,
            this.pageBillableItems});
            // 
            // pageGeneral
            // 
            this.pageGeneral.Controls.Add(this.txtReportLineGroupTag);
            this.pageGeneral.Controls.Add(this.labelControl34);
            this.pageGeneral.Controls.Add(this.txtCostBasis);
            this.pageGeneral.Controls.Add(this.labelControl32);
            this.pageGeneral.Controls.Add(this.txtSubOrderPrice);
            this.pageGeneral.Controls.Add(this.labelControl30);
            this.pageGeneral.Controls.Add(this.btnEditDefaultTemplate);
            this.pageGeneral.Controls.Add(this.btnTestGenerateCode);
            this.pageGeneral.Controls.Add(this.btnTestGeneratePrefix);
            this.pageGeneral.Controls.Add(this.btnSave);
            this.pageGeneral.Controls.Add(this.txtMnemonics);
            this.pageGeneral.Controls.Add(this.labelControl27);
            this.pageGeneral.Controls.Add(this.btnUpdateDefaultTemplatesList);
            this.pageGeneral.Controls.Add(this.labelControl17);
            this.pageGeneral.Controls.Add(this.luDefaultTemplate);
            this.pageGeneral.Controls.Add(this.btnTestCodeCheckUnique);
            this.pageGeneral.Controls.Add(this.btnCopyShortName);
            this.pageGeneral.Controls.Add(this.btnCopyCanonName);
            this.pageGeneral.Controls.Add(this.btnUpdateTemplateGroupsList);
            this.pageGeneral.Controls.Add(this.labelControl1);
            this.pageGeneral.Controls.Add(this.btnCopyPerformingLab);
            this.pageGeneral.Controls.Add(this.btnCopyResultingLab);
            this.pageGeneral.Controls.Add(this.labelControl14);
            this.pageGeneral.Controls.Add(this.luTemplateGroup);
            this.pageGeneral.Controls.Add(this.labelControl15);
            this.pageGeneral.Controls.Add(this.luTatGroup);
            this.pageGeneral.Controls.Add(this.labelControl11);
            this.pageGeneral.Controls.Add(this.luResultingLab);
            this.pageGeneral.Controls.Add(this.labelControl9);
            this.pageGeneral.Controls.Add(this.luPerformingLab);
            this.pageGeneral.Controls.Add(this.labelControl13);
            this.pageGeneral.Controls.Add(this.dteInactiveDate);
            this.pageGeneral.Controls.Add(this.lblLastModifiedDate);
            this.pageGeneral.Controls.Add(this.labelControl12);
            this.pageGeneral.Controls.Add(this.lblCreatedDate);
            this.pageGeneral.Controls.Add(this.labelControl10);
            this.pageGeneral.Controls.Add(this.labelControl8);
            this.pageGeneral.Controls.Add(this.rgTestSortPriority);
            this.pageGeneral.Controls.Add(this.labelControl7);
            this.pageGeneral.Controls.Add(this.rgReqSlip);
            this.pageGeneral.Controls.Add(this.txtListPrice);
            this.pageGeneral.Controls.Add(this.labelControl6);
            this.pageGeneral.Controls.Add(this.chkIsActive);
            this.pageGeneral.Controls.Add(this.txtCanonName);
            this.pageGeneral.Controls.Add(this.labelControl5);
            this.pageGeneral.Controls.Add(this.txtShortName);
            this.pageGeneral.Controls.Add(this.labelControl4);
            this.pageGeneral.Controls.Add(this.txtTestCode);
            this.pageGeneral.Controls.Add(this.labelControl3);
            this.pageGeneral.Controls.Add(this.lblTestId);
            this.pageGeneral.Controls.Add(this.labelControl2);
            this.pageGeneral.Name = "pageGeneral";
            this.pageGeneral.Size = new System.Drawing.Size(570, 478);
            this.pageGeneral.Text = "General";
            // 
            // txtCostBasis
            // 
            this.txtCostBasis.Location = new System.Drawing.Point(433, 177);
            this.txtCostBasis.Name = "txtCostBasis";
            this.txtCostBasis.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtCostBasis.Properties.Appearance.Options.UseFont = true;
            this.txtCostBasis.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.txtCostBasis.Size = new System.Drawing.Size(89, 22);
            this.txtCostBasis.TabIndex = 6;
            // 
            // labelControl32
            // 
            this.labelControl32.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl32.Appearance.ForeColor = System.Drawing.Color.RoyalBlue;
            this.labelControl32.Location = new System.Drawing.Point(361, 180);
            this.labelControl32.Name = "labelControl32";
            this.labelControl32.Size = new System.Drawing.Size(66, 16);
            this.labelControl32.TabIndex = 56;
            this.labelControl32.Text = "Cost Basis";
            // 
            // txtSubOrderPrice
            // 
            this.txtSubOrderPrice.Location = new System.Drawing.Point(433, 149);
            this.txtSubOrderPrice.Name = "txtSubOrderPrice";
            this.txtSubOrderPrice.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtSubOrderPrice.Properties.Appearance.Options.UseFont = true;
            this.txtSubOrderPrice.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.txtSubOrderPrice.Size = new System.Drawing.Size(89, 22);
            this.txtSubOrderPrice.TabIndex = 4;
            // 
            // labelControl30
            // 
            this.labelControl30.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl30.Appearance.ForeColor = System.Drawing.Color.RoyalBlue;
            this.labelControl30.Location = new System.Drawing.Point(325, 152);
            this.labelControl30.Name = "labelControl30";
            this.labelControl30.Size = new System.Drawing.Size(102, 16);
            this.labelControl30.TabIndex = 54;
            this.labelControl30.Text = "Sub-order Price";
            // 
            // btnEditDefaultTemplate
            // 
            this.btnEditDefaultTemplate.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnEditDefaultTemplate.Appearance.Options.UseFont = true;
            this.btnEditDefaultTemplate.Image = global::LabMaestro.Controls.Win.Resources.list_edit_16;
            this.btnEditDefaultTemplate.Location = new System.Drawing.Point(478, 388);
            this.btnEditDefaultTemplate.Name = "btnEditDefaultTemplate";
            this.btnEditDefaultTemplate.Size = new System.Drawing.Size(23, 23);
            this.btnEditDefaultTemplate.TabIndex = 52;
            this.btnEditDefaultTemplate.TabStop = false;
            this.btnEditDefaultTemplate.ToolTip = "Edit Templates";
            this.btnEditDefaultTemplate.Click += new System.EventHandler(this.btnEditDefaultTemplate_Click);
            // 
            // btnTestGenerateCode
            // 
            this.btnTestGenerateCode.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnTestGenerateCode.Appearance.Options.UseFont = true;
            this.btnTestGenerateCode.Image = global::LabMaestro.Controls.Win.Resources.red_element_fire;
            this.btnTestGenerateCode.Location = new System.Drawing.Point(312, 64);
            this.btnTestGenerateCode.Name = "btnTestGenerateCode";
            this.btnTestGenerateCode.Size = new System.Drawing.Size(23, 23);
            this.btnTestGenerateCode.TabIndex = 51;
            this.btnTestGenerateCode.TabStop = false;
            this.btnTestGenerateCode.ToolTip = "Generate Test Code From Performing Lab and Short Name";
            this.btnTestGenerateCode.Click += new System.EventHandler(this.btnTestGenerateCode_Click);
            // 
            // btnTestGeneratePrefix
            // 
            this.btnTestGeneratePrefix.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnTestGeneratePrefix.Appearance.Options.UseFont = true;
            this.btnTestGeneratePrefix.Image = global::LabMaestro.Controls.Win.Resources.star_yellow;
            this.btnTestGeneratePrefix.Location = new System.Drawing.Point(284, 64);
            this.btnTestGeneratePrefix.Name = "btnTestGeneratePrefix";
            this.btnTestGeneratePrefix.Size = new System.Drawing.Size(23, 23);
            this.btnTestGeneratePrefix.TabIndex = 50;
            this.btnTestGeneratePrefix.TabStop = false;
            this.btnTestGeneratePrefix.ToolTip = "Generate Test Prefix";
            this.btnTestGeneratePrefix.Click += new System.EventHandler(this.btnTestGeneratePrefix_Click);
            // 
            // btnSave
            // 
            this.btnSave.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnSave.Appearance.Options.UseFont = true;
            this.btnSave.Image = global::LabMaestro.Controls.Win.Resources.save16;
            this.btnSave.Location = new System.Drawing.Point(474, 442);
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new System.Drawing.Size(77, 23);
            this.btnSave.TabIndex = 16;
            this.btnSave.Text = "Save";
            this.btnSave.Click += new System.EventHandler(this.btnSave_Click);
            // 
            // txtMnemonics
            // 
            this.txtMnemonics.Location = new System.Drawing.Point(149, 177);
            this.txtMnemonics.Name = "txtMnemonics";
            this.txtMnemonics.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtMnemonics.Properties.Appearance.Options.UseFont = true;
            this.txtMnemonics.Size = new System.Drawing.Size(186, 22);
            this.txtMnemonics.TabIndex = 5;
            // 
            // labelControl27
            // 
            this.labelControl27.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl27.Appearance.ForeColor = System.Drawing.Color.RoyalBlue;
            this.labelControl27.Location = new System.Drawing.Point(13, 180);
            this.labelControl27.Name = "labelControl27";
            this.labelControl27.Size = new System.Drawing.Size(71, 16);
            this.labelControl27.TabIndex = 49;
            this.labelControl27.Text = "Mnemonics";
            // 
            // btnUpdateDefaultTemplatesList
            // 
            this.btnUpdateDefaultTemplatesList.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnUpdateDefaultTemplatesList.Appearance.Options.UseFont = true;
            this.btnUpdateDefaultTemplatesList.Image = global::LabMaestro.Controls.Win.Resources.reload16;
            this.btnUpdateDefaultTemplatesList.Location = new System.Drawing.Point(449, 388);
            this.btnUpdateDefaultTemplatesList.Name = "btnUpdateDefaultTemplatesList";
            this.btnUpdateDefaultTemplatesList.Size = new System.Drawing.Size(23, 23);
            this.btnUpdateDefaultTemplatesList.TabIndex = 47;
            this.btnUpdateDefaultTemplatesList.TabStop = false;
            this.btnUpdateDefaultTemplatesList.ToolTip = "Update Template Groups";
            this.btnUpdateDefaultTemplatesList.Click += new System.EventHandler(this.btnUpdateDefaultTemplatesList_Click);
            // 
            // labelControl17
            // 
            this.labelControl17.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl17.Appearance.ForeColor = System.Drawing.Color.RoyalBlue;
            this.labelControl17.Location = new System.Drawing.Point(13, 392);
            this.labelControl17.Name = "labelControl17";
            this.labelControl17.Size = new System.Drawing.Size(110, 16);
            this.labelControl17.TabIndex = 46;
            this.labelControl17.Text = "Default Template";
            // 
            // luDefaultTemplate
            // 
            this.luDefaultTemplate.Location = new System.Drawing.Point(149, 389);
            this.luDefaultTemplate.Name = "luDefaultTemplate";
            this.luDefaultTemplate.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.luDefaultTemplate.Properties.Appearance.Options.UseFont = true;
            this.luDefaultTemplate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.luDefaultTemplate.Properties.NullText = "[Please select an item]";
            this.luDefaultTemplate.Properties.PopupSizeable = false;
            this.luDefaultTemplate.Properties.ShowFooter = false;
            this.luDefaultTemplate.Size = new System.Drawing.Size(294, 22);
            this.luDefaultTemplate.TabIndex = 13;
            // 
            // btnTestCodeCheckUnique
            // 
            this.btnTestCodeCheckUnique.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnTestCodeCheckUnique.Appearance.Options.UseFont = true;
            this.btnTestCodeCheckUnique.Image = global::LabMaestro.Controls.Win.Resources.tick_shield_16;
            this.btnTestCodeCheckUnique.Location = new System.Drawing.Point(255, 64);
            this.btnTestCodeCheckUnique.Name = "btnTestCodeCheckUnique";
            this.btnTestCodeCheckUnique.Size = new System.Drawing.Size(23, 23);
            this.btnTestCodeCheckUnique.TabIndex = 44;
            this.btnTestCodeCheckUnique.TabStop = false;
            this.btnTestCodeCheckUnique.ToolTip = "Check Uniqueness of Test Code";
            this.btnTestCodeCheckUnique.Click += new System.EventHandler(this.btnTestCodeCheckUnique_Click);
            // 
            // btnCopyShortName
            // 
            this.btnCopyShortName.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCopyShortName.Appearance.Options.UseFont = true;
            this.btnCopyShortName.Image = global::LabMaestro.Controls.Win.Resources.copy_16;
            this.btnCopyShortName.Location = new System.Drawing.Point(528, 120);
            this.btnCopyShortName.Name = "btnCopyShortName";
            this.btnCopyShortName.Size = new System.Drawing.Size(23, 23);
            this.btnCopyShortName.TabIndex = 43;
            this.btnCopyShortName.TabStop = false;
            this.btnCopyShortName.ToolTip = "Copy From Short Name";
            this.btnCopyShortName.Click += new System.EventHandler(this.btnCopyShortName_Click);
            // 
            // btnCopyCanonName
            // 
            this.btnCopyCanonName.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCopyCanonName.Appearance.Options.UseFont = true;
            this.btnCopyCanonName.Image = global::LabMaestro.Controls.Win.Resources.copy_16;
            this.btnCopyCanonName.Location = new System.Drawing.Point(528, 92);
            this.btnCopyCanonName.Name = "btnCopyCanonName";
            this.btnCopyCanonName.Size = new System.Drawing.Size(23, 23);
            this.btnCopyCanonName.TabIndex = 42;
            this.btnCopyCanonName.TabStop = false;
            this.btnCopyCanonName.ToolTip = "Copy From Canonical Name";
            this.btnCopyCanonName.Click += new System.EventHandler(this.btnCopyCanonName_Click);
            // 
            // btnUpdateTemplateGroupsList
            // 
            this.btnUpdateTemplateGroupsList.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnUpdateTemplateGroupsList.Appearance.Options.UseFont = true;
            this.btnUpdateTemplateGroupsList.Image = global::LabMaestro.Controls.Win.Resources.reload16;
            this.btnUpdateTemplateGroupsList.Location = new System.Drawing.Point(449, 360);
            this.btnUpdateTemplateGroupsList.Name = "btnUpdateTemplateGroupsList";
            this.btnUpdateTemplateGroupsList.Size = new System.Drawing.Size(23, 23);
            this.btnUpdateTemplateGroupsList.TabIndex = 41;
            this.btnUpdateTemplateGroupsList.TabStop = false;
            this.btnUpdateTemplateGroupsList.ToolTip = "Update Template Groups";
            this.btnUpdateTemplateGroupsList.Click += new System.EventHandler(this.btnUpdateTemplateGroupsList_Click);
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Appearance.ForeColor = System.Drawing.Color.RoyalBlue;
            this.labelControl1.Location = new System.Drawing.Point(14, 419);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(89, 16);
            this.labelControl1.TabIndex = 40;
            this.labelControl1.Text = "Active Status";
            // 
            // btnCopyPerformingLab
            // 
            this.btnCopyPerformingLab.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCopyPerformingLab.Appearance.Options.UseFont = true;
            this.btnCopyPerformingLab.Image = global::LabMaestro.Controls.Win.Resources.copy_16;
            this.btnCopyPerformingLab.Location = new System.Drawing.Point(449, 306);
            this.btnCopyPerformingLab.Name = "btnCopyPerformingLab";
            this.btnCopyPerformingLab.Size = new System.Drawing.Size(23, 23);
            this.btnCopyPerformingLab.TabIndex = 39;
            this.btnCopyPerformingLab.TabStop = false;
            this.btnCopyPerformingLab.ToolTip = "Copy From Resulting Lab";
            this.btnCopyPerformingLab.Click += new System.EventHandler(this.btnCopyPerformingLab_Click);
            // 
            // btnCopyResultingLab
            // 
            this.btnCopyResultingLab.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCopyResultingLab.Appearance.Options.UseFont = true;
            this.btnCopyResultingLab.Image = global::LabMaestro.Controls.Win.Resources.copy_16;
            this.btnCopyResultingLab.Location = new System.Drawing.Point(449, 279);
            this.btnCopyResultingLab.Name = "btnCopyResultingLab";
            this.btnCopyResultingLab.Size = new System.Drawing.Size(23, 23);
            this.btnCopyResultingLab.TabIndex = 38;
            this.btnCopyResultingLab.TabStop = false;
            this.btnCopyResultingLab.ToolTip = "Copy From Performing Lab";
            this.btnCopyResultingLab.Click += new System.EventHandler(this.btnCopyResultingLab_Click);
            // 
            // labelControl14
            // 
            this.labelControl14.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl14.Appearance.ForeColor = System.Drawing.Color.RoyalBlue;
            this.labelControl14.Location = new System.Drawing.Point(13, 364);
            this.labelControl14.Name = "labelControl14";
            this.labelControl14.Size = new System.Drawing.Size(101, 16);
            this.labelControl14.TabIndex = 34;
            this.labelControl14.Text = "Template Group";
            // 
            // luTemplateGroup
            // 
            this.luTemplateGroup.Location = new System.Drawing.Point(149, 361);
            this.luTemplateGroup.Name = "luTemplateGroup";
            this.luTemplateGroup.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.luTemplateGroup.Properties.Appearance.Options.UseFont = true;
            this.luTemplateGroup.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.luTemplateGroup.Properties.NullText = "[Please select an item]";
            this.luTemplateGroup.Properties.PopupSizeable = false;
            this.luTemplateGroup.Properties.ShowFooter = false;
            this.luTemplateGroup.Size = new System.Drawing.Size(294, 22);
            this.luTemplateGroup.TabIndex = 12;
            this.luTemplateGroup.EditValueChanged += new System.EventHandler(this.luTemplateGroup_EditValueChanged);
            // 
            // labelControl15
            // 
            this.labelControl15.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl15.Appearance.ForeColor = System.Drawing.Color.RoyalBlue;
            this.labelControl15.Location = new System.Drawing.Point(13, 336);
            this.labelControl15.Name = "labelControl15";
            this.labelControl15.Size = new System.Drawing.Size(66, 16);
            this.labelControl15.TabIndex = 32;
            this.labelControl15.Text = "TAT Group";
            // 
            // luTatGroup
            // 
            this.luTatGroup.Location = new System.Drawing.Point(149, 333);
            this.luTatGroup.Name = "luTatGroup";
            this.luTatGroup.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.luTatGroup.Properties.Appearance.Options.UseFont = true;
            this.luTatGroup.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.luTatGroup.Properties.NullText = "[Please select an item]";
            this.luTatGroup.Properties.PopupSizeable = false;
            this.luTatGroup.Properties.ShowFooter = false;
            this.luTatGroup.Size = new System.Drawing.Size(294, 22);
            this.luTatGroup.TabIndex = 11;
            // 
            // labelControl11
            // 
            this.labelControl11.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl11.Appearance.ForeColor = System.Drawing.Color.RoyalBlue;
            this.labelControl11.Location = new System.Drawing.Point(13, 310);
            this.labelControl11.Name = "labelControl11";
            this.labelControl11.Size = new System.Drawing.Size(87, 16);
            this.labelControl11.TabIndex = 30;
            this.labelControl11.Text = "Resulting Lab";
            // 
            // luResultingLab
            // 
            this.luResultingLab.Location = new System.Drawing.Point(149, 307);
            this.luResultingLab.Name = "luResultingLab";
            this.luResultingLab.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.luResultingLab.Properties.Appearance.Options.UseFont = true;
            this.luResultingLab.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.luResultingLab.Properties.NullText = "[Please select an item]";
            this.luResultingLab.Properties.PopupSizeable = false;
            this.luResultingLab.Properties.ShowFooter = false;
            this.luResultingLab.Size = new System.Drawing.Size(294, 22);
            this.luResultingLab.TabIndex = 10;
            this.luResultingLab.EditValueChanged += new System.EventHandler(this.luResultingLab_EditValueChanged);
            // 
            // labelControl9
            // 
            this.labelControl9.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl9.Appearance.ForeColor = System.Drawing.Color.RoyalBlue;
            this.labelControl9.Location = new System.Drawing.Point(13, 282);
            this.labelControl9.Name = "labelControl9";
            this.labelControl9.Size = new System.Drawing.Size(98, 16);
            this.labelControl9.TabIndex = 28;
            this.labelControl9.Text = "Performing Lab";
            // 
            // luPerformingLab
            // 
            this.luPerformingLab.Location = new System.Drawing.Point(149, 279);
            this.luPerformingLab.Name = "luPerformingLab";
            this.luPerformingLab.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.luPerformingLab.Properties.Appearance.Options.UseFont = true;
            this.luPerformingLab.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.luPerformingLab.Properties.NullText = "[Please select an item]";
            this.luPerformingLab.Properties.PopupSizeable = false;
            this.luPerformingLab.Properties.ShowFooter = false;
            this.luPerformingLab.Size = new System.Drawing.Size(294, 22);
            this.luPerformingLab.TabIndex = 9;
            // 
            // labelControl13
            // 
            this.labelControl13.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl13.Appearance.ForeColor = System.Drawing.Color.RoyalBlue;
            this.labelControl13.Location = new System.Drawing.Point(14, 446);
            this.labelControl13.Name = "labelControl13";
            this.labelControl13.Size = new System.Drawing.Size(100, 16);
            this.labelControl13.TabIndex = 26;
            this.labelControl13.Text = "Deactivated On";
            // 
            // dteInactiveDate
            // 
            this.dteInactiveDate.EditValue = null;
            this.dteInactiveDate.Location = new System.Drawing.Point(149, 443);
            this.dteInactiveDate.Name = "dteInactiveDate";
            this.dteInactiveDate.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.dteInactiveDate.Properties.Appearance.Options.UseFont = true;
            this.dteInactiveDate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph, "", -1, true, true, false, DevExpress.XtraEditors.ImageLocation.MiddleCenter, global::LabMaestro.Controls.Win.Resources.calendar, new DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.None), serializableAppearanceObject1, "", null, null, true)});
            this.dteInactiveDate.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.dteInactiveDate.Size = new System.Drawing.Size(116, 22);
            this.dteInactiveDate.TabIndex = 15;
            // 
            // lblLastModifiedDate
            // 
            this.lblLastModifiedDate.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblLastModifiedDate.Location = new System.Drawing.Point(449, 39);
            this.lblLastModifiedDate.Name = "lblLastModifiedDate";
            this.lblLastModifiedDate.Size = new System.Drawing.Size(66, 16);
            this.lblLastModifiedDate.TabIndex = 24;
            this.lblLastModifiedDate.Text = "99/99/9999";
            // 
            // labelControl12
            // 
            this.labelControl12.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl12.Appearance.ForeColor = System.Drawing.Color.RoyalBlue;
            this.labelControl12.Location = new System.Drawing.Point(312, 39);
            this.labelControl12.Name = "labelControl12";
            this.labelControl12.Size = new System.Drawing.Size(86, 16);
            this.labelControl12.TabIndex = 23;
            this.labelControl12.Text = "Last Modified";
            // 
            // lblCreatedDate
            // 
            this.lblCreatedDate.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblCreatedDate.Location = new System.Drawing.Point(149, 39);
            this.lblCreatedDate.Name = "lblCreatedDate";
            this.lblCreatedDate.Size = new System.Drawing.Size(66, 16);
            this.lblCreatedDate.TabIndex = 22;
            this.lblCreatedDate.Text = "99/99/9999";
            // 
            // labelControl10
            // 
            this.labelControl10.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl10.Appearance.ForeColor = System.Drawing.Color.RoyalBlue;
            this.labelControl10.Location = new System.Drawing.Point(14, 39);
            this.labelControl10.Name = "labelControl10";
            this.labelControl10.Size = new System.Drawing.Size(52, 16);
            this.labelControl10.TabIndex = 21;
            this.labelControl10.Text = "Created";
            // 
            // labelControl8
            // 
            this.labelControl8.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl8.Appearance.ForeColor = System.Drawing.Color.RoyalBlue;
            this.labelControl8.Location = new System.Drawing.Point(13, 250);
            this.labelControl8.Name = "labelControl8";
            this.labelControl8.Size = new System.Drawing.Size(80, 16);
            this.labelControl8.TabIndex = 20;
            this.labelControl8.Text = "Sort Priority";
            // 
            // rgTestSortPriority
            // 
            this.rgTestSortPriority.Location = new System.Drawing.Point(149, 242);
            this.rgTestSortPriority.Name = "rgTestSortPriority";
            this.rgTestSortPriority.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rgTestSortPriority.Properties.Appearance.Options.UseFont = true;
            this.rgTestSortPriority.Properties.Columns = 5;
            this.rgTestSortPriority.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.RadioGroupItem[] {
            new DevExpress.XtraEditors.Controls.RadioGroupItem(null, "Lowes&t"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(null, "&Low"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(null, "&Normal"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(null, "&High"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(null, "&Critical")});
            this.rgTestSortPriority.Size = new System.Drawing.Size(402, 31);
            this.rgTestSortPriority.TabIndex = 8;
            // 
            // labelControl7
            // 
            this.labelControl7.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl7.Appearance.ForeColor = System.Drawing.Color.RoyalBlue;
            this.labelControl7.Location = new System.Drawing.Point(13, 213);
            this.labelControl7.Name = "labelControl7";
            this.labelControl7.Size = new System.Drawing.Size(130, 16);
            this.labelControl7.TabIndex = 18;
            this.labelControl7.Text = "Req. Slip Generation";
            // 
            // rgReqSlip
            // 
            this.rgReqSlip.Location = new System.Drawing.Point(149, 205);
            this.rgReqSlip.Name = "rgReqSlip";
            this.rgReqSlip.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rgReqSlip.Properties.Appearance.Options.UseFont = true;
            this.rgReqSlip.Properties.Columns = 3;
            this.rgReqSlip.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.RadioGroupItem[] {
            new DevExpress.XtraEditors.Controls.RadioGroupItem(((byte)(0)), "Do &Not Generate"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(((byte)(10)), "&Shared Slip"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(((byte)(20)), "E&xclusive Slip")});
            this.rgReqSlip.Size = new System.Drawing.Size(402, 31);
            this.rgReqSlip.TabIndex = 7;
            // 
            // txtListPrice
            // 
            this.txtListPrice.Location = new System.Drawing.Point(149, 149);
            this.txtListPrice.Name = "txtListPrice";
            this.txtListPrice.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtListPrice.Properties.Appearance.Options.UseFont = true;
            this.txtListPrice.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.txtListPrice.Size = new System.Drawing.Size(89, 22);
            this.txtListPrice.TabIndex = 3;
            // 
            // labelControl6
            // 
            this.labelControl6.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl6.Appearance.ForeColor = System.Drawing.Color.RoyalBlue;
            this.labelControl6.Location = new System.Drawing.Point(13, 152);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(59, 16);
            this.labelControl6.TabIndex = 15;
            this.labelControl6.Text = "List Price";
            // 
            // chkIsActive
            // 
            this.chkIsActive.Location = new System.Drawing.Point(147, 416);
            this.chkIsActive.Name = "chkIsActive";
            this.chkIsActive.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkIsActive.Properties.Appearance.ForeColor = System.Drawing.Color.RoyalBlue;
            this.chkIsActive.Properties.Appearance.Options.UseFont = true;
            this.chkIsActive.Properties.Appearance.Options.UseForeColor = true;
            this.chkIsActive.Properties.Caption = "Test Is Active?";
            this.chkIsActive.Size = new System.Drawing.Size(144, 20);
            this.chkIsActive.TabIndex = 14;
            // 
            // txtCanonName
            // 
            this.txtCanonName.Location = new System.Drawing.Point(149, 121);
            this.txtCanonName.Name = "txtCanonName";
            this.txtCanonName.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtCanonName.Properties.Appearance.Options.UseFont = true;
            this.txtCanonName.Size = new System.Drawing.Size(373, 22);
            this.txtCanonName.TabIndex = 2;
            // 
            // labelControl5
            // 
            this.labelControl5.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl5.Appearance.ForeColor = System.Drawing.Color.RoyalBlue;
            this.labelControl5.Location = new System.Drawing.Point(13, 124);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(100, 16);
            this.labelControl5.TabIndex = 12;
            this.labelControl5.Text = "Canonical Name";
            // 
            // txtShortName
            // 
            this.txtShortName.Location = new System.Drawing.Point(149, 93);
            this.txtShortName.Name = "txtShortName";
            this.txtShortName.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtShortName.Properties.Appearance.Options.UseFont = true;
            this.txtShortName.Size = new System.Drawing.Size(373, 22);
            this.txtShortName.TabIndex = 1;
            // 
            // labelControl4
            // 
            this.labelControl4.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl4.Appearance.ForeColor = System.Drawing.Color.RoyalBlue;
            this.labelControl4.Location = new System.Drawing.Point(13, 96);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(75, 16);
            this.labelControl4.TabIndex = 10;
            this.labelControl4.Text = "Short Name";
            // 
            // txtTestCode
            // 
            this.txtTestCode.Location = new System.Drawing.Point(149, 65);
            this.txtTestCode.Name = "txtTestCode";
            this.txtTestCode.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtTestCode.Properties.Appearance.Options.UseFont = true;
            this.txtTestCode.Size = new System.Drawing.Size(100, 22);
            this.txtTestCode.TabIndex = 0;
            // 
            // labelControl3
            // 
            this.labelControl3.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl3.Appearance.ForeColor = System.Drawing.Color.RoyalBlue;
            this.labelControl3.Location = new System.Drawing.Point(13, 68);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(64, 16);
            this.labelControl3.TabIndex = 8;
            this.labelControl3.Text = "Test Code";
            // 
            // lblTestId
            // 
            this.lblTestId.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblTestId.Location = new System.Drawing.Point(149, 12);
            this.lblTestId.Name = "lblTestId";
            this.lblTestId.Size = new System.Drawing.Size(28, 16);
            this.lblTestId.TabIndex = 5;
            this.lblTestId.Text = "9999";
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Appearance.ForeColor = System.Drawing.Color.RoyalBlue;
            this.labelControl2.Location = new System.Drawing.Point(14, 12);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(45, 16);
            this.labelControl2.TabIndex = 4;
            this.labelControl2.Text = "Test Id";
            // 
            // pageDiscrete
            // 
            this.pageDiscrete.Controls.Add(this.txtDefaultResult);
            this.pageDiscrete.Controls.Add(this.labelControl33);
            this.pageDiscrete.Controls.Add(this.btnDiscreteImportParameters);
            this.pageDiscrete.Controls.Add(this.btnDiscreteClearAll);
            this.pageDiscrete.Controls.Add(this.btnDiscreteBulkAdd);
            this.pageDiscrete.Controls.Add(this.lblDiscreteSort);
            this.pageDiscrete.Controls.Add(this.btnDiscreteParamUnderline);
            this.pageDiscrete.Controls.Add(this.btnDiscreteParamItalic);
            this.pageDiscrete.Controls.Add(this.chkDiscreteResultable);
            this.pageDiscrete.Controls.Add(this.btnDiscreteParamBold);
            this.pageDiscrete.Controls.Add(this.txtDiscreteRefRange);
            this.pageDiscrete.Controls.Add(this.labelControl22);
            this.pageDiscrete.Controls.Add(this.txtDiscreteUnits);
            this.pageDiscrete.Controls.Add(this.labelControl21);
            this.pageDiscrete.Controls.Add(this.txtDiscreteParameter);
            this.pageDiscrete.Controls.Add(this.labelControl20);
            this.pageDiscrete.Controls.Add(this.txtDiscreteIndent);
            this.pageDiscrete.Controls.Add(this.labelControl19);
            this.pageDiscrete.Controls.Add(this.lblDiscreteId);
            this.pageDiscrete.Controls.Add(this.labelControl18);
            this.pageDiscrete.Controls.Add(this.btnDiscreteDown);
            this.pageDiscrete.Controls.Add(this.btnDiscreteUp);
            this.pageDiscrete.Controls.Add(this.btnDiscreteDelete);
            this.pageDiscrete.Controls.Add(this.btnDiscreteNew);
            this.pageDiscrete.Controls.Add(this.btnDiscreteAdd);
            this.pageDiscrete.Controls.Add(this.btnDiscreteSave);
            this.pageDiscrete.Controls.Add(this.labelControl16);
            this.pageDiscrete.Controls.Add(this.grdDiscreteReportItems);
            this.pageDiscrete.Name = "pageDiscrete";
            this.pageDiscrete.Size = new System.Drawing.Size(570, 478);
            this.pageDiscrete.Text = "Discrete Report";
            // 
            // txtDefaultResult
            // 
            this.txtDefaultResult.Location = new System.Drawing.Point(383, 342);
            this.txtDefaultResult.Name = "txtDefaultResult";
            this.txtDefaultResult.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtDefaultResult.Properties.Appearance.Options.UseFont = true;
            this.txtDefaultResult.Size = new System.Drawing.Size(141, 22);
            this.txtDefaultResult.TabIndex = 5;
            // 
            // labelControl33
            // 
            this.labelControl33.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl33.Appearance.ForeColor = System.Drawing.Color.RoyalBlue;
            this.labelControl33.Location = new System.Drawing.Point(285, 345);
            this.labelControl33.Name = "labelControl33";
            this.labelControl33.Size = new System.Drawing.Size(92, 16);
            this.labelControl33.TabIndex = 73;
            this.labelControl33.Text = "Default Result";
            // 
            // btnDiscreteImportParameters
            // 
            this.btnDiscreteImportParameters.AccessibleDescription = "";
            this.btnDiscreteImportParameters.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnDiscreteImportParameters.Appearance.Options.UseFont = true;
            this.btnDiscreteImportParameters.Image = global::LabMaestro.Controls.Win.Resources.import_16;
            this.btnDiscreteImportParameters.Location = new System.Drawing.Point(532, 176);
            this.btnDiscreteImportParameters.Name = "btnDiscreteImportParameters";
            this.btnDiscreteImportParameters.Size = new System.Drawing.Size(23, 23);
            this.btnDiscreteImportParameters.TabIndex = 71;
            this.btnDiscreteImportParameters.TabStop = false;
            this.btnDiscreteImportParameters.ToolTip = "Import Parameters from Another Test";
            this.btnDiscreteImportParameters.Click += new System.EventHandler(this.btnDiscreteImportParameters_Click);
            // 
            // btnDiscreteClearAll
            // 
            this.btnDiscreteClearAll.AccessibleDescription = "";
            this.btnDiscreteClearAll.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnDiscreteClearAll.Appearance.Options.UseFont = true;
            this.btnDiscreteClearAll.Image = global::LabMaestro.Controls.Win.Resources.clear;
            this.btnDiscreteClearAll.Location = new System.Drawing.Point(532, 205);
            this.btnDiscreteClearAll.Name = "btnDiscreteClearAll";
            this.btnDiscreteClearAll.Size = new System.Drawing.Size(23, 23);
            this.btnDiscreteClearAll.TabIndex = 70;
            this.btnDiscreteClearAll.TabStop = false;
            this.btnDiscreteClearAll.ToolTip = "Clear All Items";
            this.btnDiscreteClearAll.Click += new System.EventHandler(this.btnDiscreteClearAll_Click);
            // 
            // btnDiscreteBulkAdd
            // 
            this.btnDiscreteBulkAdd.AccessibleDescription = "";
            this.btnDiscreteBulkAdd.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnDiscreteBulkAdd.Appearance.Options.UseFont = true;
            this.btnDiscreteBulkAdd.Image = global::LabMaestro.Controls.Win.Resources.edit16;
            this.btnDiscreteBulkAdd.Location = new System.Drawing.Point(532, 234);
            this.btnDiscreteBulkAdd.Name = "btnDiscreteBulkAdd";
            this.btnDiscreteBulkAdd.Size = new System.Drawing.Size(23, 23);
            this.btnDiscreteBulkAdd.TabIndex = 69;
            this.btnDiscreteBulkAdd.TabStop = false;
            this.btnDiscreteBulkAdd.ToolTip = "Bulk Edit Parameters";
            this.btnDiscreteBulkAdd.Click += new System.EventHandler(this.btnDiscreteBulkAdd_Click);
            // 
            // lblDiscreteSort
            // 
            this.lblDiscreteSort.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblDiscreteSort.Location = new System.Drawing.Point(496, 262);
            this.lblDiscreteSort.Name = "lblDiscreteSort";
            this.lblDiscreteSort.Size = new System.Drawing.Size(28, 16);
            this.lblDiscreteSort.TabIndex = 51;
            this.lblDiscreteSort.Text = "9999";
            // 
            // btnDiscreteParamUnderline
            // 
            this.btnDiscreteParamUnderline.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnDiscreteParamUnderline.Appearance.Options.UseFont = true;
            this.btnDiscreteParamUnderline.Image = global::LabMaestro.Controls.Win.Resources.text_underline_16;
            this.btnDiscreteParamUnderline.Location = new System.Drawing.Point(501, 313);
            this.btnDiscreteParamUnderline.Name = "btnDiscreteParamUnderline";
            this.btnDiscreteParamUnderline.Size = new System.Drawing.Size(23, 23);
            this.btnDiscreteParamUnderline.TabIndex = 50;
            this.btnDiscreteParamUnderline.TabStop = false;
            this.btnDiscreteParamUnderline.ToolTip = "Underline";
            this.btnDiscreteParamUnderline.Click += new System.EventHandler(this.btnDiscreteParamUnderline_Click);
            // 
            // btnDiscreteParamItalic
            // 
            this.btnDiscreteParamItalic.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnDiscreteParamItalic.Appearance.Options.UseFont = true;
            this.btnDiscreteParamItalic.Image = global::LabMaestro.Controls.Win.Resources.text_italics_16;
            this.btnDiscreteParamItalic.Location = new System.Drawing.Point(474, 313);
            this.btnDiscreteParamItalic.Name = "btnDiscreteParamItalic";
            this.btnDiscreteParamItalic.Size = new System.Drawing.Size(23, 23);
            this.btnDiscreteParamItalic.TabIndex = 49;
            this.btnDiscreteParamItalic.TabStop = false;
            this.btnDiscreteParamItalic.ToolTip = "Italics";
            this.btnDiscreteParamItalic.Click += new System.EventHandler(this.btnDiscreteParamItalic_Click);
            // 
            // chkDiscreteResultable
            // 
            this.chkDiscreteResultable.Location = new System.Drawing.Point(299, 287);
            this.chkDiscreteResultable.Name = "chkDiscreteResultable";
            this.chkDiscreteResultable.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkDiscreteResultable.Properties.Appearance.ForeColor = System.Drawing.Color.RoyalBlue;
            this.chkDiscreteResultable.Properties.Appearance.Options.UseFont = true;
            this.chkDiscreteResultable.Properties.Appearance.Options.UseForeColor = true;
            this.chkDiscreteResultable.Properties.Caption = "Is This a Resultable Line Item?";
            this.chkDiscreteResultable.Size = new System.Drawing.Size(225, 20);
            this.chkDiscreteResultable.TabIndex = 2;
            // 
            // btnDiscreteParamBold
            // 
            this.btnDiscreteParamBold.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnDiscreteParamBold.Appearance.Options.UseFont = true;
            this.btnDiscreteParamBold.Image = global::LabMaestro.Controls.Win.Resources.text_bold_16;
            this.btnDiscreteParamBold.Location = new System.Drawing.Point(447, 313);
            this.btnDiscreteParamBold.Name = "btnDiscreteParamBold";
            this.btnDiscreteParamBold.Size = new System.Drawing.Size(23, 23);
            this.btnDiscreteParamBold.TabIndex = 48;
            this.btnDiscreteParamBold.TabStop = false;
            this.btnDiscreteParamBold.ToolTip = "Bold";
            this.btnDiscreteParamBold.Click += new System.EventHandler(this.btnDiscreteParamBold_Click);
            // 
            // txtDiscreteRefRange
            // 
            this.txtDiscreteRefRange.Location = new System.Drawing.Point(138, 371);
            this.txtDiscreteRefRange.Name = "txtDiscreteRefRange";
            this.txtDiscreteRefRange.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtDiscreteRefRange.Properties.Appearance.Options.UseFont = true;
            this.txtDiscreteRefRange.Size = new System.Drawing.Size(386, 55);
            this.txtDiscreteRefRange.TabIndex = 6;
            this.txtDiscreteRefRange.UseOptimizedRendering = true;
            // 
            // labelControl22
            // 
            this.labelControl22.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl22.Appearance.ForeColor = System.Drawing.Color.RoyalBlue;
            this.labelControl22.Location = new System.Drawing.Point(16, 373);
            this.labelControl22.Name = "labelControl22";
            this.labelControl22.Size = new System.Drawing.Size(112, 16);
            this.labelControl22.TabIndex = 47;
            this.labelControl22.Text = "Reference Range";
            // 
            // txtDiscreteUnits
            // 
            this.txtDiscreteUnits.Location = new System.Drawing.Point(138, 342);
            this.txtDiscreteUnits.Name = "txtDiscreteUnits";
            this.txtDiscreteUnits.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtDiscreteUnits.Properties.Appearance.Options.UseFont = true;
            this.txtDiscreteUnits.Size = new System.Drawing.Size(141, 22);
            this.txtDiscreteUnits.TabIndex = 4;
            // 
            // labelControl21
            // 
            this.labelControl21.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl21.Appearance.ForeColor = System.Drawing.Color.RoyalBlue;
            this.labelControl21.Location = new System.Drawing.Point(16, 345);
            this.labelControl21.Name = "labelControl21";
            this.labelControl21.Size = new System.Drawing.Size(32, 16);
            this.labelControl21.TabIndex = 45;
            this.labelControl21.Text = "Units";
            // 
            // txtDiscreteParameter
            // 
            this.txtDiscreteParameter.Location = new System.Drawing.Point(138, 314);
            this.txtDiscreteParameter.Name = "txtDiscreteParameter";
            this.txtDiscreteParameter.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtDiscreteParameter.Properties.Appearance.Options.UseFont = true;
            this.txtDiscreteParameter.Size = new System.Drawing.Size(305, 22);
            this.txtDiscreteParameter.TabIndex = 3;
            // 
            // labelControl20
            // 
            this.labelControl20.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl20.Appearance.ForeColor = System.Drawing.Color.RoyalBlue;
            this.labelControl20.Location = new System.Drawing.Point(16, 317);
            this.labelControl20.Name = "labelControl20";
            this.labelControl20.Size = new System.Drawing.Size(69, 16);
            this.labelControl20.TabIndex = 43;
            this.labelControl20.Text = "Parameter";
            // 
            // txtDiscreteIndent
            // 
            this.txtDiscreteIndent.Location = new System.Drawing.Point(138, 287);
            this.txtDiscreteIndent.Name = "txtDiscreteIndent";
            this.txtDiscreteIndent.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtDiscreteIndent.Properties.Appearance.Options.UseFont = true;
            this.txtDiscreteIndent.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.txtDiscreteIndent.Properties.Mask.EditMask = "n0";
            this.txtDiscreteIndent.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric;
            this.txtDiscreteIndent.Properties.MaxLength = 1;
            this.txtDiscreteIndent.Size = new System.Drawing.Size(53, 22);
            this.txtDiscreteIndent.TabIndex = 1;
            // 
            // labelControl19
            // 
            this.labelControl19.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl19.Appearance.ForeColor = System.Drawing.Color.RoyalBlue;
            this.labelControl19.Location = new System.Drawing.Point(16, 290);
            this.labelControl19.Name = "labelControl19";
            this.labelControl19.Size = new System.Drawing.Size(81, 16);
            this.labelControl19.TabIndex = 41;
            this.labelControl19.Text = "Indent Level";
            // 
            // lblDiscreteId
            // 
            this.lblDiscreteId.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblDiscreteId.Location = new System.Drawing.Point(138, 265);
            this.lblDiscreteId.Name = "lblDiscreteId";
            this.lblDiscreteId.Size = new System.Drawing.Size(28, 16);
            this.lblDiscreteId.TabIndex = 40;
            this.lblDiscreteId.Text = "9999";
            // 
            // labelControl18
            // 
            this.labelControl18.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl18.Appearance.ForeColor = System.Drawing.Color.RoyalBlue;
            this.labelControl18.Location = new System.Drawing.Point(16, 265);
            this.labelControl18.Name = "labelControl18";
            this.labelControl18.Size = new System.Drawing.Size(13, 16);
            this.labelControl18.TabIndex = 39;
            this.labelControl18.Text = "Id";
            // 
            // btnDiscreteDown
            // 
            this.btnDiscreteDown.AccessibleDescription = "";
            this.btnDiscreteDown.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnDiscreteDown.Appearance.Options.UseFont = true;
            this.btnDiscreteDown.Image = global::LabMaestro.Controls.Win.Resources.arrow_down16;
            this.btnDiscreteDown.Location = new System.Drawing.Point(532, 137);
            this.btnDiscreteDown.Name = "btnDiscreteDown";
            this.btnDiscreteDown.Size = new System.Drawing.Size(23, 23);
            this.btnDiscreteDown.TabIndex = 38;
            this.btnDiscreteDown.TabStop = false;
            this.btnDiscreteDown.ToolTip = "Down";
            this.btnDiscreteDown.Click += new System.EventHandler(this.btnDiscreteDown_Click);
            // 
            // btnDiscreteUp
            // 
            this.btnDiscreteUp.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnDiscreteUp.Appearance.Options.UseFont = true;
            this.btnDiscreteUp.Image = global::LabMaestro.Controls.Win.Resources.arrow_up16;
            this.btnDiscreteUp.Location = new System.Drawing.Point(532, 108);
            this.btnDiscreteUp.Name = "btnDiscreteUp";
            this.btnDiscreteUp.Size = new System.Drawing.Size(23, 23);
            this.btnDiscreteUp.TabIndex = 37;
            this.btnDiscreteUp.TabStop = false;
            this.btnDiscreteUp.ToolTip = "Up";
            this.btnDiscreteUp.Click += new System.EventHandler(this.btnDiscreteUp_Click);
            // 
            // btnDiscreteDelete
            // 
            this.btnDiscreteDelete.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnDiscreteDelete.Appearance.Options.UseFont = true;
            this.btnDiscreteDelete.Image = global::LabMaestro.Controls.Win.Resources.delete_16;
            this.btnDiscreteDelete.Location = new System.Drawing.Point(16, 441);
            this.btnDiscreteDelete.Name = "btnDiscreteDelete";
            this.btnDiscreteDelete.Size = new System.Drawing.Size(77, 23);
            this.btnDiscreteDelete.TabIndex = 10;
            this.btnDiscreteDelete.Text = "Delete";
            this.btnDiscreteDelete.ToolTip = "Delete Selected Item";
            this.btnDiscreteDelete.Click += new System.EventHandler(this.btnDiscreteDelete_Click);
            // 
            // btnDiscreteNew
            // 
            this.btnDiscreteNew.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnDiscreteNew.Appearance.Options.UseFont = true;
            this.btnDiscreteNew.Image = global::LabMaestro.Controls.Win.Resources.new16;
            this.btnDiscreteNew.Location = new System.Drawing.Point(281, 441);
            this.btnDiscreteNew.Name = "btnDiscreteNew";
            this.btnDiscreteNew.Size = new System.Drawing.Size(77, 23);
            this.btnDiscreteNew.TabIndex = 9;
            this.btnDiscreteNew.Text = "New";
            this.btnDiscreteNew.ToolTip = "Create New Item";
            this.btnDiscreteNew.Click += new System.EventHandler(this.btnDiscreteNew_Click);
            // 
            // btnDiscreteAdd
            // 
            this.btnDiscreteAdd.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnDiscreteAdd.Appearance.Options.UseFont = true;
            this.btnDiscreteAdd.Image = global::LabMaestro.Controls.Win.Resources.add_16;
            this.btnDiscreteAdd.Location = new System.Drawing.Point(364, 441);
            this.btnDiscreteAdd.Name = "btnDiscreteAdd";
            this.btnDiscreteAdd.Size = new System.Drawing.Size(77, 23);
            this.btnDiscreteAdd.TabIndex = 8;
            this.btnDiscreteAdd.Text = "Add";
            this.btnDiscreteAdd.ToolTip = "Add Item";
            this.btnDiscreteAdd.Click += new System.EventHandler(this.btnDiscreteAdd_Click);
            // 
            // btnDiscreteSave
            // 
            this.btnDiscreteSave.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnDiscreteSave.Appearance.Options.UseFont = true;
            this.btnDiscreteSave.Image = global::LabMaestro.Controls.Win.Resources.save16;
            this.btnDiscreteSave.Location = new System.Drawing.Point(447, 441);
            this.btnDiscreteSave.Name = "btnDiscreteSave";
            this.btnDiscreteSave.Size = new System.Drawing.Size(77, 23);
            this.btnDiscreteSave.TabIndex = 7;
            this.btnDiscreteSave.Text = "Save";
            this.btnDiscreteSave.ToolTip = "Save Item";
            this.btnDiscreteSave.Click += new System.EventHandler(this.btnDiscreteSave_Click);
            // 
            // labelControl16
            // 
            this.labelControl16.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl16.Appearance.ForeColor = System.Drawing.Color.RoyalBlue;
            this.labelControl16.Location = new System.Drawing.Point(374, 262);
            this.labelControl16.Name = "labelControl16";
            this.labelControl16.Size = new System.Drawing.Size(69, 16);
            this.labelControl16.TabIndex = 31;
            this.labelControl16.Text = "Sort Order";
            // 
            // grdDiscreteReportItems
            // 
            this.grdDiscreteReportItems.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.grdDiscreteReportItems.ClipCurrentCellSelection = false;
            this.grdDiscreteReportItems.DataRowTemplate = this.dataRowTemplate2;
            this.grdDiscreteReportItems.DataRowTemplateStyles.Add(this.visualGridElementStyle2);
            this.grdDiscreteReportItems.DataRowTemplateStyles.Add(this.dataRowStyle1);
            // 
            // 
            // 
            this.grdDiscreteReportItems.ErrorVisualStyle.OverrideUIStyle = false;
            // 
            // 
            // 
            this.grdDiscreteReportItems.FixedColumnSplitter.AllowRepositioning = false;
            this.grdDiscreteReportItems.FixedColumnSplitter.Visible = false;
            this.grdDiscreteReportItems.FixedHeaderRows.Add(this.columnManagerRow2);
            this.grdDiscreteReportItems.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.grdDiscreteReportItems.GridLineColor = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(239)))), ((int)(((byte)(255)))));
            this.grdDiscreteReportItems.GridLineStyle = System.Drawing.Drawing2D.DashStyle.Dot;
            this.grdDiscreteReportItems.InactiveSelectionBackColor = System.Drawing.Color.RoyalBlue;
            // 
            // 
            // 
            this.grdDiscreteReportItems.InactiveSelectionVisualStyle.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(229)))), ((int)(((byte)(229)))), ((int)(((byte)(229)))));
            this.grdDiscreteReportItems.InactiveSelectionVisualStyle.ForeColor = System.Drawing.Color.Black;
            this.grdDiscreteReportItems.InactiveSelectionVisualStyle.OverrideUIStyle = true;
            this.grdDiscreteReportItems.Location = new System.Drawing.Point(16, 18);
            this.grdDiscreteReportItems.Margin = new System.Windows.Forms.Padding(5);
            this.grdDiscreteReportItems.Name = "grdDiscreteReportItems";
            this.grdDiscreteReportItems.OverrideUIStyle = false;
            this.grdDiscreteReportItems.ReadOnly = true;
            // 
            // 
            // 
            this.grdDiscreteReportItems.RowSelectorPane.AllowRowResize = false;
            this.grdDiscreteReportItems.RowSelectorPane.CurrentImageIndex = 16;
            gradientMap2.GradientMode = Xceed.UI.GradientMode.Horizontal;
            gradientStop3.Color = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(239)))), ((int)(((byte)(255)))));
            gradientStop3.Offset = 0D;
            gradientStop4.Color = System.Drawing.Color.FromArgb(((int)(((byte)(175)))), ((int)(((byte)(210)))), ((int)(((byte)(255)))));
            gradientStop4.Offset = 1D;
            gradientMap2.GradientStops.Add(gradientStop3);
            gradientMap2.GradientStops.Add(gradientStop4);
            this.grdDiscreteReportItems.RowSelectorPane.GradientMap = gradientMap2;
            // 
            // 
            // 
            this.grdDiscreteReportItems.RowSelectorPane.HotVisualStyle.OverrideUIStyle = true;
            this.grdDiscreteReportItems.RowSelectorPane.OverrideUIStyle = true;
            this.grdDiscreteReportItems.RowSelectorPane.SelectedImageIndex = 20;
            this.grdDiscreteReportItems.RowSelectorPane.Width = 17;
            this.grdDiscreteReportItems.SelectionBackColor = System.Drawing.Color.Indigo;
            this.grdDiscreteReportItems.SelectionMode = System.Windows.Forms.SelectionMode.One;
            // 
            // 
            // 
            this.grdDiscreteReportItems.SelectionVisualStyle.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(167)))), ((int)(((byte)(205)))), ((int)(((byte)(240)))));
            this.grdDiscreteReportItems.SelectionVisualStyle.ForeColor = System.Drawing.Color.Black;
            this.grdDiscreteReportItems.SelectionVisualStyle.OverrideUIStyle = true;
            this.grdDiscreteReportItems.ShowFocusRectangle = false;
            // 
            // 
            // 
            this.grdDiscreteReportItems.SideMargin.Visible = false;
            this.grdDiscreteReportItems.Size = new System.Drawing.Size(508, 239);
            this.grdDiscreteReportItems.SynchronizeDetailGrids = false;
            this.grdDiscreteReportItems.TabIndex = 0;
            this.grdDiscreteReportItems.TabStop = false;
            this.grdDiscreteReportItems.TreeLineColor = System.Drawing.Color.Silver;
            this.grdDiscreteReportItems.UIStyle = Xceed.UI.UIStyle.System;
            this.grdDiscreteReportItems.SelectedRowsChanged += new System.EventHandler(this.grdDiscreteReportItems_SelectedRowsChanged);
            // 
            // visualGridElementStyle2
            // 
            // 
            // 
            // 
            this.visualGridElementStyle2.HotVisualStyle.OverrideUIStyle = true;
            this.visualGridElementStyle2.OverrideUIStyle = false;
            // 
            // dataRowStyle1
            // 
            this.dataRowStyle1.BackColor = System.Drawing.Color.AliceBlue;
            // 
            // columnManagerRow2
            // 
            this.columnManagerRow2.AllowColumnReorder = false;
            this.columnManagerRow2.AllowColumnResize = true;
            this.columnManagerRow2.AllowSort = false;
            this.columnManagerRow2.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            gradientStop1.Color = System.Drawing.Color.FromArgb(((int)(((byte)(252)))), ((int)(((byte)(253)))), ((int)(((byte)(255)))));
            gradientStop1.Offset = 0D;
            gradientStop2.Color = System.Drawing.Color.FromArgb(((int)(((byte)(196)))), ((int)(((byte)(221)))), ((int)(((byte)(255)))));
            gradientStop2.Offset = 1D;
            gradientMap1.GradientStops.Add(gradientStop1);
            gradientMap1.GradientStops.Add(gradientStop2);
            this.columnManagerRow2.GradientMap = gradientMap1;
            this.columnManagerRow2.Height = 23;
            // 
            // 
            // 
            this.columnManagerRow2.HotVisualStyle.OverrideUIStyle = false;
            this.columnManagerRow2.OverrideUIStyle = true;
            // 
            // pageReqParams
            // 
            this.pageReqParams.Controls.Add(this.btnReqImportParameters);
            this.pageReqParams.Controls.Add(this.btnReqClearAll);
            this.pageReqParams.Controls.Add(this.btnReqBulkAdd);
            this.pageReqParams.Controls.Add(this.lblReqSort);
            this.pageReqParams.Controls.Add(this.btnReqDelete);
            this.pageReqParams.Controls.Add(this.btnReqNew);
            this.pageReqParams.Controls.Add(this.btnReqAdd);
            this.pageReqParams.Controls.Add(this.btnReqSave);
            this.pageReqParams.Controls.Add(this.txtReqParameter);
            this.pageReqParams.Controls.Add(this.labelControl25);
            this.pageReqParams.Controls.Add(this.txtReqIndent);
            this.pageReqParams.Controls.Add(this.labelControl26);
            this.pageReqParams.Controls.Add(this.lblReqId);
            this.pageReqParams.Controls.Add(this.labelControl28);
            this.pageReqParams.Controls.Add(this.labelControl29);
            this.pageReqParams.Controls.Add(this.btnReqDown);
            this.pageReqParams.Controls.Add(this.btnReqUp);
            this.pageReqParams.Controls.Add(this.grdReqParams);
            this.pageReqParams.Name = "pageReqParams";
            this.pageReqParams.Size = new System.Drawing.Size(570, 478);
            this.pageReqParams.Text = "Req. Params";
            // 
            // btnReqImportParameters
            // 
            this.btnReqImportParameters.AccessibleDescription = "";
            this.btnReqImportParameters.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnReqImportParameters.Appearance.Options.UseFont = true;
            this.btnReqImportParameters.Image = global::LabMaestro.Controls.Win.Resources.import_16;
            this.btnReqImportParameters.Location = new System.Drawing.Point(535, 247);
            this.btnReqImportParameters.Name = "btnReqImportParameters";
            this.btnReqImportParameters.Size = new System.Drawing.Size(23, 23);
            this.btnReqImportParameters.TabIndex = 70;
            this.btnReqImportParameters.TabStop = false;
            this.btnReqImportParameters.ToolTip = "Import Parameters from Another Test";
            this.btnReqImportParameters.Click += new System.EventHandler(this.btnReqImportParameters_Click);
            // 
            // btnReqClearAll
            // 
            this.btnReqClearAll.AccessibleDescription = "";
            this.btnReqClearAll.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnReqClearAll.Appearance.Options.UseFont = true;
            this.btnReqClearAll.Image = global::LabMaestro.Controls.Win.Resources.clear;
            this.btnReqClearAll.Location = new System.Drawing.Point(535, 276);
            this.btnReqClearAll.Name = "btnReqClearAll";
            this.btnReqClearAll.Size = new System.Drawing.Size(23, 23);
            this.btnReqClearAll.TabIndex = 69;
            this.btnReqClearAll.TabStop = false;
            this.btnReqClearAll.ToolTip = "Clear All Parameters";
            this.btnReqClearAll.Click += new System.EventHandler(this.btnReqClearAll_Click);
            // 
            // btnReqBulkAdd
            // 
            this.btnReqBulkAdd.AccessibleDescription = "";
            this.btnReqBulkAdd.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnReqBulkAdd.Appearance.Options.UseFont = true;
            this.btnReqBulkAdd.Image = global::LabMaestro.Controls.Win.Resources.edit16;
            this.btnReqBulkAdd.Location = new System.Drawing.Point(535, 305);
            this.btnReqBulkAdd.Name = "btnReqBulkAdd";
            this.btnReqBulkAdd.Size = new System.Drawing.Size(23, 23);
            this.btnReqBulkAdd.TabIndex = 68;
            this.btnReqBulkAdd.TabStop = false;
            this.btnReqBulkAdd.ToolTip = "Bulk-add Parameters";
            this.btnReqBulkAdd.Click += new System.EventHandler(this.btnReqBulkAdd_Click);
            // 
            // lblReqSort
            // 
            this.lblReqSort.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblReqSort.Location = new System.Drawing.Point(135, 358);
            this.lblReqSort.Name = "lblReqSort";
            this.lblReqSort.Size = new System.Drawing.Size(28, 16);
            this.lblReqSort.TabIndex = 67;
            this.lblReqSort.Text = "9999";
            // 
            // btnReqDelete
            // 
            this.btnReqDelete.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnReqDelete.Appearance.Options.UseFont = true;
            this.btnReqDelete.Image = global::LabMaestro.Controls.Win.Resources.delete_16;
            this.btnReqDelete.Location = new System.Drawing.Point(14, 439);
            this.btnReqDelete.Name = "btnReqDelete";
            this.btnReqDelete.Size = new System.Drawing.Size(77, 23);
            this.btnReqDelete.TabIndex = 6;
            this.btnReqDelete.Text = "Delete";
            this.btnReqDelete.ToolTip = "Delete Selected Item";
            this.btnReqDelete.Click += new System.EventHandler(this.btnReqDelete_Click);
            // 
            // btnReqNew
            // 
            this.btnReqNew.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnReqNew.Appearance.Options.UseFont = true;
            this.btnReqNew.Image = global::LabMaestro.Controls.Win.Resources.new16;
            this.btnReqNew.Location = new System.Drawing.Point(284, 439);
            this.btnReqNew.Name = "btnReqNew";
            this.btnReqNew.Size = new System.Drawing.Size(77, 23);
            this.btnReqNew.TabIndex = 3;
            this.btnReqNew.Text = "New";
            this.btnReqNew.ToolTip = "Create New Item";
            this.btnReqNew.Click += new System.EventHandler(this.btnReqNew_Click);
            // 
            // btnReqAdd
            // 
            this.btnReqAdd.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnReqAdd.Appearance.Options.UseFont = true;
            this.btnReqAdd.Image = global::LabMaestro.Controls.Win.Resources.add_16;
            this.btnReqAdd.Location = new System.Drawing.Point(367, 439);
            this.btnReqAdd.Name = "btnReqAdd";
            this.btnReqAdd.Size = new System.Drawing.Size(77, 23);
            this.btnReqAdd.TabIndex = 4;
            this.btnReqAdd.Text = "Add";
            this.btnReqAdd.ToolTip = "Add Item";
            this.btnReqAdd.Click += new System.EventHandler(this.btnReqAdd_Click);
            // 
            // btnReqSave
            // 
            this.btnReqSave.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnReqSave.Appearance.Options.UseFont = true;
            this.btnReqSave.Image = global::LabMaestro.Controls.Win.Resources.save16;
            this.btnReqSave.Location = new System.Drawing.Point(450, 439);
            this.btnReqSave.Name = "btnReqSave";
            this.btnReqSave.Size = new System.Drawing.Size(77, 23);
            this.btnReqSave.TabIndex = 5;
            this.btnReqSave.Text = "Save";
            this.btnReqSave.ToolTip = "Save Item";
            this.btnReqSave.Click += new System.EventHandler(this.btnReqSave_Click);
            // 
            // txtReqParameter
            // 
            this.txtReqParameter.Location = new System.Drawing.Point(135, 411);
            this.txtReqParameter.Name = "txtReqParameter";
            this.txtReqParameter.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtReqParameter.Properties.Appearance.Options.UseFont = true;
            this.txtReqParameter.Size = new System.Drawing.Size(392, 22);
            this.txtReqParameter.TabIndex = 2;
            // 
            // labelControl25
            // 
            this.labelControl25.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl25.Appearance.ForeColor = System.Drawing.Color.RoyalBlue;
            this.labelControl25.Location = new System.Drawing.Point(13, 414);
            this.labelControl25.Name = "labelControl25";
            this.labelControl25.Size = new System.Drawing.Size(69, 16);
            this.labelControl25.TabIndex = 61;
            this.labelControl25.Text = "Parameter";
            // 
            // txtReqIndent
            // 
            this.txtReqIndent.Location = new System.Drawing.Point(135, 383);
            this.txtReqIndent.Name = "txtReqIndent";
            this.txtReqIndent.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtReqIndent.Properties.Appearance.Options.UseFont = true;
            this.txtReqIndent.Properties.Mask.EditMask = "n0";
            this.txtReqIndent.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric;
            this.txtReqIndent.Properties.MaxLength = 1;
            this.txtReqIndent.Size = new System.Drawing.Size(53, 22);
            this.txtReqIndent.TabIndex = 1;
            // 
            // labelControl26
            // 
            this.labelControl26.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl26.Appearance.ForeColor = System.Drawing.Color.RoyalBlue;
            this.labelControl26.Location = new System.Drawing.Point(13, 386);
            this.labelControl26.Name = "labelControl26";
            this.labelControl26.Size = new System.Drawing.Size(81, 16);
            this.labelControl26.TabIndex = 59;
            this.labelControl26.Text = "Indent Level";
            // 
            // lblReqId
            // 
            this.lblReqId.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblReqId.Location = new System.Drawing.Point(135, 336);
            this.lblReqId.Name = "lblReqId";
            this.lblReqId.Size = new System.Drawing.Size(28, 16);
            this.lblReqId.TabIndex = 58;
            this.lblReqId.Text = "9999";
            // 
            // labelControl28
            // 
            this.labelControl28.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl28.Appearance.ForeColor = System.Drawing.Color.RoyalBlue;
            this.labelControl28.Location = new System.Drawing.Point(13, 336);
            this.labelControl28.Name = "labelControl28";
            this.labelControl28.Size = new System.Drawing.Size(13, 16);
            this.labelControl28.TabIndex = 57;
            this.labelControl28.Text = "Id";
            // 
            // labelControl29
            // 
            this.labelControl29.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl29.Appearance.ForeColor = System.Drawing.Color.RoyalBlue;
            this.labelControl29.Location = new System.Drawing.Point(13, 358);
            this.labelControl29.Name = "labelControl29";
            this.labelControl29.Size = new System.Drawing.Size(69, 16);
            this.labelControl29.TabIndex = 51;
            this.labelControl29.Text = "Sort Order";
            // 
            // btnReqDown
            // 
            this.btnReqDown.AccessibleDescription = "";
            this.btnReqDown.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnReqDown.Appearance.Options.UseFont = true;
            this.btnReqDown.Image = global::LabMaestro.Controls.Win.Resources.arrow_down16;
            this.btnReqDown.Location = new System.Drawing.Point(535, 176);
            this.btnReqDown.Name = "btnReqDown";
            this.btnReqDown.Size = new System.Drawing.Size(23, 23);
            this.btnReqDown.TabIndex = 40;
            this.btnReqDown.TabStop = false;
            this.btnReqDown.ToolTip = "Down";
            this.btnReqDown.Click += new System.EventHandler(this.btnReqDown_Click);
            // 
            // btnReqUp
            // 
            this.btnReqUp.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnReqUp.Appearance.Options.UseFont = true;
            this.btnReqUp.Image = global::LabMaestro.Controls.Win.Resources.arrow_up16;
            this.btnReqUp.Location = new System.Drawing.Point(535, 147);
            this.btnReqUp.Name = "btnReqUp";
            this.btnReqUp.Size = new System.Drawing.Size(23, 23);
            this.btnReqUp.TabIndex = 39;
            this.btnReqUp.TabStop = false;
            this.btnReqUp.ToolTip = "Up";
            this.btnReqUp.Click += new System.EventHandler(this.btnReqUp_Click);
            // 
            // grdReqParams
            // 
            this.grdReqParams.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.grdReqParams.ClipCurrentCellSelection = false;
            this.grdReqParams.DataRowTemplate = this.dataRow1;
            this.grdReqParams.DataRowTemplateStyles.Add(this.visualGridElementStyle1);
            this.grdReqParams.DataRowTemplateStyles.Add(this.visualGridElementStyle3);
            // 
            // 
            // 
            this.grdReqParams.ErrorVisualStyle.OverrideUIStyle = false;
            // 
            // 
            // 
            this.grdReqParams.FixedColumnSplitter.AllowRepositioning = false;
            this.grdReqParams.FixedColumnSplitter.Visible = false;
            this.grdReqParams.FixedHeaderRows.Add(this.columnManagerRow1);
            this.grdReqParams.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.grdReqParams.GridLineColor = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(239)))), ((int)(((byte)(255)))));
            this.grdReqParams.GridLineStyle = System.Drawing.Drawing2D.DashStyle.Dot;
            this.grdReqParams.InactiveSelectionBackColor = System.Drawing.Color.RoyalBlue;
            // 
            // 
            // 
            this.grdReqParams.InactiveSelectionVisualStyle.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(229)))), ((int)(((byte)(229)))), ((int)(((byte)(229)))));
            this.grdReqParams.InactiveSelectionVisualStyle.ForeColor = System.Drawing.Color.Black;
            this.grdReqParams.InactiveSelectionVisualStyle.OverrideUIStyle = true;
            this.grdReqParams.Location = new System.Drawing.Point(15, 17);
            this.grdReqParams.Margin = new System.Windows.Forms.Padding(5);
            this.grdReqParams.Name = "grdReqParams";
            this.grdReqParams.OverrideUIStyle = false;
            this.grdReqParams.ReadOnly = true;
            // 
            // 
            // 
            this.grdReqParams.RowSelectorPane.AllowRowResize = false;
            this.grdReqParams.RowSelectorPane.CurrentImageIndex = 16;
            gradientMap4.GradientMode = Xceed.UI.GradientMode.Horizontal;
            gradientStop7.Color = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(239)))), ((int)(((byte)(255)))));
            gradientStop7.Offset = 0D;
            gradientStop8.Color = System.Drawing.Color.FromArgb(((int)(((byte)(175)))), ((int)(((byte)(210)))), ((int)(((byte)(255)))));
            gradientStop8.Offset = 1D;
            gradientMap4.GradientStops.Add(gradientStop7);
            gradientMap4.GradientStops.Add(gradientStop8);
            this.grdReqParams.RowSelectorPane.GradientMap = gradientMap4;
            // 
            // 
            // 
            this.grdReqParams.RowSelectorPane.HotVisualStyle.OverrideUIStyle = true;
            this.grdReqParams.RowSelectorPane.OverrideUIStyle = true;
            this.grdReqParams.RowSelectorPane.SelectedImageIndex = 20;
            this.grdReqParams.RowSelectorPane.Width = 17;
            this.grdReqParams.SelectionBackColor = System.Drawing.Color.Indigo;
            this.grdReqParams.SelectionMode = System.Windows.Forms.SelectionMode.One;
            // 
            // 
            // 
            this.grdReqParams.SelectionVisualStyle.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(167)))), ((int)(((byte)(205)))), ((int)(((byte)(240)))));
            this.grdReqParams.SelectionVisualStyle.ForeColor = System.Drawing.Color.Black;
            this.grdReqParams.SelectionVisualStyle.OverrideUIStyle = true;
            this.grdReqParams.ShowFocusRectangle = false;
            // 
            // 
            // 
            this.grdReqParams.SideMargin.Visible = false;
            this.grdReqParams.Size = new System.Drawing.Size(512, 311);
            this.grdReqParams.SynchronizeDetailGrids = false;
            this.grdReqParams.TabIndex = 0;
            this.grdReqParams.TabStop = false;
            this.grdReqParams.TreeLineColor = System.Drawing.Color.Silver;
            this.grdReqParams.UIStyle = Xceed.UI.UIStyle.System;
            this.grdReqParams.SelectedRowsChanged += new System.EventHandler(this.grdReqParams_SelectedRowsChanged);
            // 
            // visualGridElementStyle1
            // 
            // 
            // 
            // 
            this.visualGridElementStyle1.HotVisualStyle.OverrideUIStyle = true;
            this.visualGridElementStyle1.OverrideUIStyle = false;
            // 
            // visualGridElementStyle3
            // 
            this.visualGridElementStyle3.BackColor = System.Drawing.Color.AliceBlue;
            // 
            // columnManagerRow1
            // 
            this.columnManagerRow1.AllowColumnReorder = false;
            this.columnManagerRow1.AllowColumnResize = true;
            this.columnManagerRow1.AllowSort = false;
            this.columnManagerRow1.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            gradientStop5.Color = System.Drawing.Color.FromArgb(((int)(((byte)(252)))), ((int)(((byte)(253)))), ((int)(((byte)(255)))));
            gradientStop5.Offset = 0D;
            gradientStop6.Color = System.Drawing.Color.FromArgb(((int)(((byte)(196)))), ((int)(((byte)(221)))), ((int)(((byte)(255)))));
            gradientStop6.Offset = 1D;
            gradientMap3.GradientStops.Add(gradientStop5);
            gradientMap3.GradientStops.Add(gradientStop6);
            this.columnManagerRow1.GradientMap = gradientMap3;
            this.columnManagerRow1.Height = 23;
            // 
            // 
            // 
            this.columnManagerRow1.HotVisualStyle.OverrideUIStyle = false;
            this.columnManagerRow1.OverrideUIStyle = true;
            // 
            // pageBillableItems
            // 
            this.pageBillableItems.Controls.Add(this.btnBIDelete);
            this.pageBillableItems.Controls.Add(this.btnBINew);
            this.pageBillableItems.Controls.Add(this.btnBIAdd);
            this.pageBillableItems.Controls.Add(this.btnBISave);
            this.pageBillableItems.Controls.Add(this.txtBIQty);
            this.pageBillableItems.Controls.Add(this.labelControl24);
            this.pageBillableItems.Controls.Add(this.labelControl23);
            this.pageBillableItems.Controls.Add(this.luBillableItem);
            this.pageBillableItems.Controls.Add(this.labelControl31);
            this.pageBillableItems.Controls.Add(this.rgBIOptimize);
            this.pageBillableItems.Controls.Add(this.grdBillableItems);
            this.pageBillableItems.Name = "pageBillableItems";
            this.pageBillableItems.Size = new System.Drawing.Size(570, 478);
            this.pageBillableItems.Text = "Billable Items";
            // 
            // btnBIDelete
            // 
            this.btnBIDelete.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnBIDelete.Appearance.Options.UseFont = true;
            this.btnBIDelete.Image = global::LabMaestro.Controls.Win.Resources.delete_16;
            this.btnBIDelete.Location = new System.Drawing.Point(12, 434);
            this.btnBIDelete.Name = "btnBIDelete";
            this.btnBIDelete.Size = new System.Drawing.Size(77, 23);
            this.btnBIDelete.TabIndex = 7;
            this.btnBIDelete.Text = "Delete";
            this.btnBIDelete.ToolTip = "Delete Selected Item";
            this.btnBIDelete.Click += new System.EventHandler(this.btnBIDelete_Click);
            // 
            // btnBINew
            // 
            this.btnBINew.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnBINew.Appearance.Options.UseFont = true;
            this.btnBINew.Image = global::LabMaestro.Controls.Win.Resources.new16;
            this.btnBINew.Location = new System.Drawing.Point(308, 434);
            this.btnBINew.Name = "btnBINew";
            this.btnBINew.Size = new System.Drawing.Size(77, 23);
            this.btnBINew.TabIndex = 4;
            this.btnBINew.Text = "New";
            this.btnBINew.ToolTip = "Create New Item";
            this.btnBINew.Click += new System.EventHandler(this.btnBINew_Click);
            // 
            // btnBIAdd
            // 
            this.btnBIAdd.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnBIAdd.Appearance.Options.UseFont = true;
            this.btnBIAdd.Image = global::LabMaestro.Controls.Win.Resources.add_16;
            this.btnBIAdd.Location = new System.Drawing.Point(391, 434);
            this.btnBIAdd.Name = "btnBIAdd";
            this.btnBIAdd.Size = new System.Drawing.Size(77, 23);
            this.btnBIAdd.TabIndex = 5;
            this.btnBIAdd.Text = "Add";
            this.btnBIAdd.ToolTip = "Add Item";
            this.btnBIAdd.Click += new System.EventHandler(this.btnBIAdd_Click);
            // 
            // btnBISave
            // 
            this.btnBISave.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnBISave.Appearance.Options.UseFont = true;
            this.btnBISave.Image = global::LabMaestro.Controls.Win.Resources.save16;
            this.btnBISave.Location = new System.Drawing.Point(474, 434);
            this.btnBISave.Name = "btnBISave";
            this.btnBISave.Size = new System.Drawing.Size(77, 23);
            this.btnBISave.TabIndex = 6;
            this.btnBISave.Text = "Save";
            this.btnBISave.ToolTip = "Save Item";
            this.btnBISave.Click += new System.EventHandler(this.btnBISave_Click);
            // 
            // txtBIQty
            // 
            this.txtBIQty.Location = new System.Drawing.Point(148, 401);
            this.txtBIQty.Name = "txtBIQty";
            this.txtBIQty.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtBIQty.Properties.Appearance.Options.UseFont = true;
            this.txtBIQty.Properties.Mask.EditMask = "n0";
            this.txtBIQty.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric;
            this.txtBIQty.Properties.MaxLength = 2;
            this.txtBIQty.Size = new System.Drawing.Size(53, 22);
            this.txtBIQty.TabIndex = 3;
            // 
            // labelControl24
            // 
            this.labelControl24.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl24.Appearance.ForeColor = System.Drawing.Color.RoyalBlue;
            this.labelControl24.Location = new System.Drawing.Point(12, 404);
            this.labelControl24.Name = "labelControl24";
            this.labelControl24.Size = new System.Drawing.Size(56, 16);
            this.labelControl24.TabIndex = 53;
            this.labelControl24.Text = "Quantity";
            // 
            // labelControl23
            // 
            this.labelControl23.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl23.Appearance.ForeColor = System.Drawing.Color.RoyalBlue;
            this.labelControl23.Location = new System.Drawing.Point(12, 339);
            this.labelControl23.Name = "labelControl23";
            this.labelControl23.Size = new System.Drawing.Size(78, 16);
            this.labelControl23.TabIndex = 38;
            this.labelControl23.Text = "Billable Item";
            // 
            // luBillableItem
            // 
            this.luBillableItem.Location = new System.Drawing.Point(148, 336);
            this.luBillableItem.Name = "luBillableItem";
            this.luBillableItem.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.luBillableItem.Properties.Appearance.Options.UseFont = true;
            this.luBillableItem.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.luBillableItem.Properties.NullText = "[Please select an item]";
            this.luBillableItem.Properties.ShowFooter = false;
            this.luBillableItem.Size = new System.Drawing.Size(320, 22);
            this.luBillableItem.TabIndex = 1;
            // 
            // labelControl31
            // 
            this.labelControl31.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl31.Appearance.ForeColor = System.Drawing.Color.RoyalBlue;
            this.labelControl31.Location = new System.Drawing.Point(12, 372);
            this.labelControl31.Name = "labelControl31";
            this.labelControl31.Size = new System.Drawing.Size(118, 16);
            this.labelControl31.TabIndex = 34;
            this.labelControl31.Text = "Optimization Level";
            // 
            // rgBIOptimize
            // 
            this.rgBIOptimize.Location = new System.Drawing.Point(148, 364);
            this.rgBIOptimize.Name = "rgBIOptimize";
            this.rgBIOptimize.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rgBIOptimize.Properties.Appearance.Options.UseFont = true;
            this.rgBIOptimize.Properties.Columns = 5;
            this.rgBIOptimize.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.RadioGroupItem[] {
            new DevExpress.XtraEditors.Controls.RadioGroupItem(null, "Do &Not Optimize"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(null, "Optimize &Per Req"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(null, "Optimize &Globally")});
            this.rgBIOptimize.Size = new System.Drawing.Size(402, 31);
            this.rgBIOptimize.TabIndex = 2;
            // 
            // grdBillableItems
            // 
            this.grdBillableItems.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.grdBillableItems.ClipCurrentCellSelection = false;
            this.grdBillableItems.DataRowTemplate = this.dataRow2;
            this.grdBillableItems.DataRowTemplateStyles.Add(this.visualGridElementStyle4);
            this.grdBillableItems.DataRowTemplateStyles.Add(this.visualGridElementStyle5);
            // 
            // 
            // 
            this.grdBillableItems.ErrorVisualStyle.OverrideUIStyle = false;
            // 
            // 
            // 
            this.grdBillableItems.FixedColumnSplitter.AllowRepositioning = false;
            this.grdBillableItems.FixedColumnSplitter.Visible = false;
            this.grdBillableItems.FixedHeaderRows.Add(this.columnManagerRow3);
            this.grdBillableItems.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.grdBillableItems.GridLineColor = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(239)))), ((int)(((byte)(255)))));
            this.grdBillableItems.GridLineStyle = System.Drawing.Drawing2D.DashStyle.Dot;
            this.grdBillableItems.InactiveSelectionBackColor = System.Drawing.Color.RoyalBlue;
            // 
            // 
            // 
            this.grdBillableItems.InactiveSelectionVisualStyle.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(229)))), ((int)(((byte)(229)))), ((int)(((byte)(229)))));
            this.grdBillableItems.InactiveSelectionVisualStyle.ForeColor = System.Drawing.Color.Black;
            this.grdBillableItems.InactiveSelectionVisualStyle.OverrideUIStyle = true;
            this.grdBillableItems.Location = new System.Drawing.Point(16, 20);
            this.grdBillableItems.Margin = new System.Windows.Forms.Padding(5);
            this.grdBillableItems.Name = "grdBillableItems";
            this.grdBillableItems.OverrideUIStyle = false;
            this.grdBillableItems.ReadOnly = true;
            // 
            // 
            // 
            this.grdBillableItems.RowSelectorPane.AllowRowResize = false;
            this.grdBillableItems.RowSelectorPane.CurrentImageIndex = 16;
            gradientMap6.GradientMode = Xceed.UI.GradientMode.Horizontal;
            gradientStop11.Color = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(239)))), ((int)(((byte)(255)))));
            gradientStop11.Offset = 0D;
            gradientStop12.Color = System.Drawing.Color.FromArgb(((int)(((byte)(175)))), ((int)(((byte)(210)))), ((int)(((byte)(255)))));
            gradientStop12.Offset = 1D;
            gradientMap6.GradientStops.Add(gradientStop11);
            gradientMap6.GradientStops.Add(gradientStop12);
            this.grdBillableItems.RowSelectorPane.GradientMap = gradientMap6;
            // 
            // 
            // 
            this.grdBillableItems.RowSelectorPane.HotVisualStyle.OverrideUIStyle = true;
            this.grdBillableItems.RowSelectorPane.OverrideUIStyle = true;
            this.grdBillableItems.RowSelectorPane.SelectedImageIndex = 20;
            this.grdBillableItems.RowSelectorPane.Width = 17;
            this.grdBillableItems.SelectionBackColor = System.Drawing.Color.Indigo;
            this.grdBillableItems.SelectionMode = System.Windows.Forms.SelectionMode.One;
            // 
            // 
            // 
            this.grdBillableItems.SelectionVisualStyle.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(167)))), ((int)(((byte)(205)))), ((int)(((byte)(240)))));
            this.grdBillableItems.SelectionVisualStyle.ForeColor = System.Drawing.Color.Black;
            this.grdBillableItems.SelectionVisualStyle.OverrideUIStyle = true;
            this.grdBillableItems.ShowFocusRectangle = false;
            // 
            // 
            // 
            this.grdBillableItems.SideMargin.Visible = false;
            this.grdBillableItems.Size = new System.Drawing.Size(536, 308);
            this.grdBillableItems.SynchronizeDetailGrids = false;
            this.grdBillableItems.TabIndex = 0;
            this.grdBillableItems.TabStop = false;
            this.grdBillableItems.TreeLineColor = System.Drawing.Color.Silver;
            this.grdBillableItems.UIStyle = Xceed.UI.UIStyle.System;
            this.grdBillableItems.SelectedRowsChanged += new System.EventHandler(this.grdBillableItems_SelectedRowsChanged);
            // 
            // visualGridElementStyle4
            // 
            // 
            // 
            // 
            this.visualGridElementStyle4.HotVisualStyle.OverrideUIStyle = true;
            this.visualGridElementStyle4.OverrideUIStyle = false;
            // 
            // visualGridElementStyle5
            // 
            this.visualGridElementStyle5.BackColor = System.Drawing.Color.AliceBlue;
            // 
            // columnManagerRow3
            // 
            this.columnManagerRow3.AllowColumnReorder = false;
            this.columnManagerRow3.AllowColumnResize = true;
            this.columnManagerRow3.AllowSort = false;
            this.columnManagerRow3.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            gradientStop9.Color = System.Drawing.Color.FromArgb(((int)(((byte)(252)))), ((int)(((byte)(253)))), ((int)(((byte)(255)))));
            gradientStop9.Offset = 0D;
            gradientStop10.Color = System.Drawing.Color.FromArgb(((int)(((byte)(196)))), ((int)(((byte)(221)))), ((int)(((byte)(255)))));
            gradientStop10.Offset = 1D;
            gradientMap5.GradientStops.Add(gradientStop9);
            gradientMap5.GradientStops.Add(gradientStop10);
            this.columnManagerRow3.GradientMap = gradientMap5;
            this.columnManagerRow3.Height = 23;
            // 
            // 
            // 
            this.columnManagerRow3.HotVisualStyle.OverrideUIStyle = false;
            this.columnManagerRow3.OverrideUIStyle = true;
            // 
            // xtraTabPage1
            // 
            this.xtraTabPage1.Name = "xtraTabPage1";
            this.xtraTabPage1.Size = new System.Drawing.Size(570, 554);
            this.xtraTabPage1.Text = "xtraTabPage1";
            // 
            // xtraTabPage2
            // 
            this.xtraTabPage2.Name = "xtraTabPage2";
            this.xtraTabPage2.Size = new System.Drawing.Size(294, 272);
            this.xtraTabPage2.Text = "xtraTabPage2";
            // 
            // btnClose
            // 
            this.btnClose.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnClose.Appearance.Options.UseFont = true;
            this.btnClose.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnClose.Image = global::LabMaestro.Controls.Win.Resources.exit_16;
            this.btnClose.Location = new System.Drawing.Point(506, 525);
            this.btnClose.Name = "btnClose";
            this.btnClose.Size = new System.Drawing.Size(77, 23);
            this.btnClose.TabIndex = 1;
            this.btnClose.Text = "Close";
            this.btnClose.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // txtReportLineGroupTag
            // 
            this.txtReportLineGroupTag.Location = new System.Drawing.Point(434, 417);
            this.txtReportLineGroupTag.Name = "txtReportLineGroupTag";
            this.txtReportLineGroupTag.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtReportLineGroupTag.Properties.Appearance.Options.UseFont = true;
            this.txtReportLineGroupTag.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.txtReportLineGroupTag.Size = new System.Drawing.Size(117, 22);
            this.txtReportLineGroupTag.TabIndex = 57;
            // 
            // labelControl34
            // 
            this.labelControl34.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl34.Appearance.ForeColor = System.Drawing.Color.RoyalBlue;
            this.labelControl34.Location = new System.Drawing.Point(283, 419);
            this.labelControl34.Name = "labelControl34";
            this.labelControl34.Size = new System.Drawing.Size(144, 16);
            this.labelControl34.TabIndex = 58;
            this.labelControl34.Text = "Report Line Group Tag";
            // 
            // TestEditDialog
            // 
            this.AcceptButton = this.btnSave;
            this.Appearance.Options.UseFont = true;
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.CancelButton = this.btnClose;
            this.ClientSize = new System.Drawing.Size(600, 558);
            this.Controls.Add(this.btnClose);
            this.Controls.Add(this.tabControl);
            this.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.LookAndFeel.SkinName = "Seven";
            this.LookAndFeel.UseDefaultLookAndFeel = false;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "TestEditDialog";
            this.ShowInTaskbar = false;
            this.SizeGripStyle = System.Windows.Forms.SizeGripStyle.Hide;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "TestEditDialog";
            ((System.ComponentModel.ISupportInitialize)(this.tabControl)).EndInit();
            this.tabControl.ResumeLayout(false);
            this.pageGeneral.ResumeLayout(false);
            this.pageGeneral.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtCostBasis.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtSubOrderPrice.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtMnemonics.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.luDefaultTemplate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.luTemplateGroup.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.luTatGroup.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.luResultingLab.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.luPerformingLab.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dteInactiveDate.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dteInactiveDate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rgTestSortPriority.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rgReqSlip.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtListPrice.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkIsActive.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCanonName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtShortName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTestCode.Properties)).EndInit();
            this.pageDiscrete.ResumeLayout(false);
            this.pageDiscrete.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtDefaultResult.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkDiscreteResultable.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDiscreteRefRange.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDiscreteUnits.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDiscreteParameter.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDiscreteIndent.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdDiscreteReportItems)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataRowTemplate2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.columnManagerRow2)).EndInit();
            this.pageReqParams.ResumeLayout(false);
            this.pageReqParams.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtReqParameter.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtReqIndent.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdReqParams)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataRow1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.columnManagerRow1)).EndInit();
            this.pageBillableItems.ResumeLayout(false);
            this.pageBillableItems.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtBIQty.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.luBillableItem.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rgBIOptimize.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdBillableItems)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataRow2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.columnManagerRow3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtReportLineGroupTag.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraTab.XtraTabControl tabControl;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage2;
        private DevExpress.XtraEditors.SimpleButton btnClose;
        private DevExpress.XtraEditors.SimpleButton btnSave;
        private DevExpress.XtraTab.XtraTabPage pageGeneral;
        private DevExpress.XtraTab.XtraTabPage pageDiscrete;
        private DevExpress.XtraTab.XtraTabPage pageReqParams;
        private DevExpress.XtraTab.XtraTabPage pageBillableItems;
        private DevExpress.XtraEditors.LabelControl lblTestId;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.CheckEdit chkIsActive;
        private DevExpress.XtraEditors.TextEdit txtCanonName;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.TextEdit txtShortName;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.TextEdit txtTestCode;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.TextEdit txtListPrice;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.RadioGroup rgReqSlip;
        private DevExpress.XtraEditors.LabelControl labelControl8;
        private DevExpress.XtraEditors.RadioGroup rgTestSortPriority;
        private DevExpress.XtraEditors.LabelControl labelControl13;
        private DevExpress.XtraEditors.DateEdit dteInactiveDate;
        private DevExpress.XtraEditors.LabelControl lblLastModifiedDate;
        private DevExpress.XtraEditors.LabelControl labelControl12;
        private DevExpress.XtraEditors.LabelControl lblCreatedDate;
        private DevExpress.XtraEditors.LabelControl labelControl10;
        private DevExpress.XtraEditors.LabelControl labelControl11;
        private DevExpress.XtraEditors.LookUpEdit luResultingLab;
        private DevExpress.XtraEditors.LabelControl labelControl9;
        private DevExpress.XtraEditors.LookUpEdit luPerformingLab;
        private DevExpress.XtraEditors.LabelControl labelControl14;
        private DevExpress.XtraEditors.LookUpEdit luTemplateGroup;
        private DevExpress.XtraEditors.LabelControl labelControl15;
        private DevExpress.XtraEditors.LookUpEdit luTatGroup;
        private Xceed.Grid.GridControl grdDiscreteReportItems;
        private Xceed.Grid.DataRow dataRowTemplate2;
        private Xceed.Grid.VisualGridElementStyle visualGridElementStyle2;
        private Xceed.Grid.VisualGridElementStyle dataRowStyle1;
        private Xceed.Grid.ColumnManagerRow columnManagerRow2;
        private Xceed.Grid.GridControl grdReqParams;
        private Xceed.Grid.DataRow dataRow1;
        private Xceed.Grid.VisualGridElementStyle visualGridElementStyle1;
        private Xceed.Grid.VisualGridElementStyle visualGridElementStyle3;
        private Xceed.Grid.ColumnManagerRow columnManagerRow1;
        private Xceed.Grid.GridControl grdBillableItems;
        private Xceed.Grid.DataRow dataRow2;
        private Xceed.Grid.VisualGridElementStyle visualGridElementStyle4;
        private Xceed.Grid.VisualGridElementStyle visualGridElementStyle5;
        private Xceed.Grid.ColumnManagerRow columnManagerRow3;
        private DevExpress.XtraEditors.SimpleButton btnDiscreteDown;
        private DevExpress.XtraEditors.SimpleButton btnDiscreteUp;
        private DevExpress.XtraEditors.SimpleButton btnDiscreteDelete;
        private DevExpress.XtraEditors.SimpleButton btnDiscreteNew;
        private DevExpress.XtraEditors.SimpleButton btnDiscreteAdd;
        private DevExpress.XtraEditors.SimpleButton btnDiscreteSave;
        private DevExpress.XtraEditors.LabelControl labelControl16;
        private DevExpress.XtraEditors.LabelControl lblDiscreteId;
        private DevExpress.XtraEditors.LabelControl labelControl18;
        private DevExpress.XtraEditors.LabelControl labelControl22;
        private DevExpress.XtraEditors.TextEdit txtDiscreteUnits;
        private DevExpress.XtraEditors.LabelControl labelControl21;
        private DevExpress.XtraEditors.TextEdit txtDiscreteParameter;
        private DevExpress.XtraEditors.LabelControl labelControl20;
        private DevExpress.XtraEditors.TextEdit txtDiscreteIndent;
        private DevExpress.XtraEditors.LabelControl labelControl19;
        private DevExpress.XtraEditors.CheckEdit chkDiscreteResultable;
        private DevExpress.XtraEditors.MemoEdit txtDiscreteRefRange;
        private DevExpress.XtraEditors.SimpleButton btnReqDown;
        private DevExpress.XtraEditors.SimpleButton btnReqUp;
        private DevExpress.XtraEditors.TextEdit txtReqParameter;
        private DevExpress.XtraEditors.LabelControl labelControl25;
        private DevExpress.XtraEditors.TextEdit txtReqIndent;
        private DevExpress.XtraEditors.LabelControl labelControl26;
        private DevExpress.XtraEditors.LabelControl lblReqId;
        private DevExpress.XtraEditors.LabelControl labelControl28;
        private DevExpress.XtraEditors.LabelControl labelControl29;
        private DevExpress.XtraEditors.TextEdit txtBIQty;
        private DevExpress.XtraEditors.LabelControl labelControl24;
        private DevExpress.XtraEditors.LabelControl labelControl23;
        private DevExpress.XtraEditors.LookUpEdit luBillableItem;
        private DevExpress.XtraEditors.LabelControl labelControl31;
        private DevExpress.XtraEditors.RadioGroup rgBIOptimize;
        private DevExpress.XtraEditors.SimpleButton btnReqDelete;
        private DevExpress.XtraEditors.SimpleButton btnReqNew;
        private DevExpress.XtraEditors.SimpleButton btnReqAdd;
        private DevExpress.XtraEditors.SimpleButton btnReqSave;
        private DevExpress.XtraEditors.SimpleButton btnBIDelete;
        private DevExpress.XtraEditors.SimpleButton btnBINew;
        private DevExpress.XtraEditors.SimpleButton btnBIAdd;
        private DevExpress.XtraEditors.SimpleButton btnBISave;
        private DevExpress.XtraEditors.SimpleButton btnCopyPerformingLab;
        private DevExpress.XtraEditors.SimpleButton btnCopyResultingLab;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.SimpleButton btnUpdateTemplateGroupsList;
        private DevExpress.XtraEditors.SimpleButton btnDiscreteParamUnderline;
        private DevExpress.XtraEditors.SimpleButton btnDiscreteParamItalic;
        private DevExpress.XtraEditors.SimpleButton btnDiscreteParamBold;
        private DevExpress.XtraEditors.SimpleButton btnCopyShortName;
        private DevExpress.XtraEditors.SimpleButton btnCopyCanonName;
        private DevExpress.XtraEditors.SimpleButton btnTestCodeCheckUnique;
        private DevExpress.XtraEditors.LabelControl lblDiscreteSort;
        private DevExpress.XtraEditors.LabelControl lblReqSort;
        private DevExpress.XtraEditors.SimpleButton btnReqBulkAdd;
        private DevExpress.XtraEditors.SimpleButton btnDiscreteBulkAdd;
        private DevExpress.XtraEditors.SimpleButton btnUpdateDefaultTemplatesList;
        private DevExpress.XtraEditors.LabelControl labelControl17;
        private DevExpress.XtraEditors.LookUpEdit luDefaultTemplate;
        private DevExpress.XtraEditors.TextEdit txtMnemonics;
        private DevExpress.XtraEditors.LabelControl labelControl27;
        private DevExpress.XtraEditors.SimpleButton btnTestGeneratePrefix;
        private DevExpress.XtraEditors.SimpleButton btnTestGenerateCode;
        private DevExpress.XtraEditors.SimpleButton btnDiscreteClearAll;
        private DevExpress.XtraEditors.SimpleButton btnDiscreteImportParameters;
        private DevExpress.XtraEditors.SimpleButton btnReqClearAll;
        private DevExpress.XtraEditors.SimpleButton btnReqImportParameters;
        private DevExpress.XtraEditors.SimpleButton btnEditDefaultTemplate;
        private DevExpress.XtraEditors.TextEdit txtSubOrderPrice;
        private DevExpress.XtraEditors.LabelControl labelControl30;
        private DevExpress.XtraEditors.TextEdit txtCostBasis;
        private DevExpress.XtraEditors.LabelControl labelControl32;
        private DevExpress.XtraEditors.TextEdit txtDefaultResult;
        private DevExpress.XtraEditors.LabelControl labelControl33;
        private DevExpress.XtraEditors.TextEdit txtReportLineGroupTag;
        private DevExpress.XtraEditors.LabelControl labelControl34;
    }
}