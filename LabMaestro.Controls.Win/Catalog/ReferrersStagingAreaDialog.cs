﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ReferrersStagingAreaDialog.cs 516 2013-04-17 08:03:02Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using DevExpress.XtraEditors;
using LabMaestro.Domain;
using Xceed.Grid;

namespace LabMaestro.Controls.Win
{
    public partial class ReferrersStagingAreaDialog : XtraForm
    {
        public ReferrersStagingAreaDialog()
        {
            SelectedReferrers = new List<ReferrerSlice>();
            InitializeComponent();
            WinUtils.ApplyTheme(this);
            WinUtils.SetFormTitle(this, "Physicians Staging Area");
        }

        public List<ReferrerSlice> SelectedReferrers { get; private set; }

        public void UpdateControls()
        {
            GridControl.Columns.Clear();
            GridControl.GridAddColumn("name", "Physician", 0, 500);
            GridControl.GridAddColumn("id", "Id", 1, 90);
            populateGrid();
        }

        private void populateGrid()
        {
            GridControl.BeginInit();
            try
            {
                GridControl.DataRows.Clear();
                var sortedList = SelectedReferrers.OrderBy(x => x.Name).ToList();
                foreach (var info in sortedList)
                {
                    var row = GridControl.DataRows.AddNew();
                    row.Cells["name"].Value = info.Name;
                    row.Cells["id"].Value = info.Id.ToString();
                    row.Tag = info;
                    row.Height = 22;
                    row.EndEdit();
                }
            }
            finally
            {
                GridControl.EndInit();
                GridControl.Refresh();
            }
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            var msg = string.Format("Really delete the selected {0} physician(s)?", GridControl.SelectedRows.Count);

            if (GridControl.SelectedRows.Count > 0 && MessageDlg.Confirm(msg))
            {
                foreach (DataRow row in GridControl.SelectedRows)
                {
                    var item = row.Tag as ReferrerSlice;
                    if (item != null)
                    {
                        SelectedReferrers.Remove(item);
                    }
                }
                populateGrid();
            }
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            var msg = string.Format("Really delete all {0} physicians?", SelectedReferrers.Count);
            if (SelectedReferrers.Count > 0 && MessageDlg.Confirm(msg))
            {
                SelectedReferrers.Clear();
                populateGrid();
            }
        }
    }
}