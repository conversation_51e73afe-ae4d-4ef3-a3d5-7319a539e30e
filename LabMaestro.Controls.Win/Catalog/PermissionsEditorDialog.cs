﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: PermissionsEditorDialog.cs 532 2013-04-23 16:33:28Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using LabMaestro.Domain;
using LabMaestro.Shared;

namespace LabMaestro.Controls.Win
{
    public partial class PermissionsEditorDialog : XtraForm
    {
        private Permission _currentPermission;

        public PermissionsEditorDialog()
        {
            InitializeComponent();
            WinUtils.ApplyTheme(this);
            WinUtils.SetFormTitle(this, "Permissions");
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        private void PermissionsEditorDialog_Load(object sender, EventArgs e)
        {
            GridControl.Columns.Clear();
            var i = 0;
            GridControl.GridAddColumn("name", "Name", i++, 140);
            GridControl.GridAddColumn("code", "Code", i++, 140);

            gridPopulatePermissions();
        }

        private void gridPopulatePermissions()
        {
            List<Permission> permissions = null;
            WaitFormControl.WaitOperation(this, () => { permissions = PermissionsRepository.FindAll(); });
            GridControl.DataRows.Clear();
            foreach (var permission in permissions)
            {
                var row = GridControl.DataRows.AddNew();
                row.Cells["name"].Value = permission.Name;
                row.Cells["code"].Value = permission.PermCode;
                row.Tag = permission;
                row.Height = 21;
                row.EndEdit();
            }
        }

        private void GridControl_SelectedRowsChanged(object sender, EventArgs e)
        {
            if (GridControl.SelectedRows.Count == 1)
            {
                _currentPermission = GridControl.SelectedRows[0].Tag as Permission;
                populateEditorControls();
            }
        }

        private void populateEditorControls()
        {
            resetEditorControls();
            if (_currentPermission != null)
            {
                lblId.Text = _currentPermission.Id.ToString();
                txtCategory.Text = _currentPermission.Category;
                txtCode.Text = _currentPermission.PermCode;
                txtDescription.Text = _currentPermission.Description;
                txtName.Text = _currentPermission.Name;
                chkIsActive.Checked = _currentPermission.IsActive;
                
                btnNew.Enabled = true;
                btnSave.Enabled = true;
            }
        }

        private void resetEditorControls()
        {
            lblId.Text = string.Empty;
            txtCategory.Text = string.Empty;
            txtCode.Text = string.Empty;
            txtName.Text = string.Empty;
            txtDescription.Text = string.Empty;
            chkIsActive.Checked = true;

            btnNew.Enabled = true;
            btnAdd.Enabled = false;
            btnSave.Enabled = false;
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            if (_currentPermission != null)
            {
                if (validateEditorControls())
                {
                    _currentPermission.PermCode = txtCode.Text.Trim().ToLower();
                    _currentPermission.Name = txtName.Text.Trim();
                    _currentPermission.Category = txtCategory.Text.Trim();
                    _currentPermission.Description = txtDescription.Text.Trim();
                    _currentPermission.IsActive = chkIsActive.Checked;

                    performSave();
                    gridPopulatePermissions();
                }
            }
        }

        private void performSave()
        {
            WaitFormControl.WaitOperation(this, PermissionsRepository.Save, "Saving...");
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            if (_currentPermission == null)
            {
                if (validateEditorControls())
                {
                    var perm = new Permission
                        {
                            PermCode = txtCode.Text.Trim().ToLower(),
                            Name = txtName.Text.Trim(),
                            Category = txtCategory.Text.Trim(),
                            Description = txtDescription.Text.Trim(),
                            IsActive = chkIsActive.Checked
                        };
                    PermissionsRepository.Add(perm);

                    performSave();
                    gridPopulatePermissions();
                }
            }
        }

        private bool validateEditorControls()
        {
            var text = txtName.Text.Trim();
            if (!SharedUtilities.StringWithinLimit(text, 4, 40))
            {
                MessageDlg.Warning("Name must be between 4 and 40 characters!");
                txtName.Focus();
                return false;
            }
            
            text = txtCode.Text.Trim();
            if (!SharedUtilities.StringWithinLimit(text, 4, 32))
            {
                MessageDlg.Warning("Code must be between 4 and 32 characters!");
                txtCode.Focus();
                return false;
            }
            
            text = txtCategory.Text.Trim();
            if (!SharedUtilities.StringWithinLimit(text, 0, 40))
            {
                MessageDlg.Warning("Category must be less than 40 characters!");
                txtCategory.Focus();
                return false;
            }

            return true;
        }

        private void btnNew_Click(object sender, EventArgs e)
        {
            resetEditorControls();

            _currentPermission = null;
            btnAdd.Enabled = true;
            btnNew.Enabled = false;
        }
    }
}