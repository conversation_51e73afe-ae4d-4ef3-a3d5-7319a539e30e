﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ReferrerQuickSelectionDialog.cs 678 2013-06-26 09:10:55Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Search;

namespace LabMaestro.Controls.Win;

public partial class ReferrerQuickSelectionDialog : XtraForm
{
    private readonly Font _disabledFont;
    private readonly bool _showInactiveReferrers;
    private readonly SearchEntityType _entityType;
    private const int MAX_SEARCH_RESULTS = 100;
    private readonly List<ReferrerSearchRec> _filteredCatalog = [];
    private List<ReferrerSlice>? _referrerCatalog;
    private List<CorporateClientSlice>? _corporateCatalog;
    private List<AssociateOrganizationSlice>? _associateCatalog;
    private List<AffiliateSlice>? _affiliateCatalog;

    public ReferrerQuickSelectionDialog(bool showInactiveReferrers, SearchEntityType entityType)
    {
        _showInactiveReferrers = showInactiveReferrers;
        _entityType = entityType;

        InitializeComponent();
        WinUtils.ApplyTheme(this);
        WinUtils.SetFormTitle(this, $"Select {entityTypeString}");
        _disabledFont = new Font(gridControl.Font.Name, gridControl.Font.Size, FontStyle.Strikeout);
        ReferrerSearchEngine.ResetCatalog();
    }

    string entityTypeString => Enum.GetName(typeof(SearchEntityType), _entityType) ?? "Item";

    public int SelectedItemId { get; set; } = -1;
    public bool ConfirmSelection { get; set; }

    private void loadCatalog()
    {
        WaitFormControl.WaitOperation(this, () =>
        {
            switch (_entityType) {
                case SearchEntityType.Physician:
                    _referrerCatalog = _showInactiveReferrers
                        ? AllReferringPhysiciansRepository.GetCatalog(false)
                        : ActiveReferringPhysiciansRepository.GetCatalog(false);
                    ReferrerSearchEngine.AssignCatalog(_referrerCatalog);
                    break;
                case SearchEntityType.AssociateLab:
                    _associateCatalog = _showInactiveReferrers
                        ? AssociateOrganizationRepository.FetchAll().ToList()
                        : AssociateOrganizationRepository.FetchActive().ToList();
                    ReferrerSearchEngine.AssignCatalog(_associateCatalog);
                    break;
                case SearchEntityType.Corporate:
                    _corporateCatalog = _showInactiveReferrers
                        ? CorporateClientRepository.FetchAll().ToList()
                        : CorporateClientRepository.FetchActive().ToList();
                    ReferrerSearchEngine.AssignCatalog(_corporateCatalog);
                    break;
                case SearchEntityType.Affiliate:
                    _affiliateCatalog = _showInactiveReferrers
                        ? AffiliateRepository.FetchAll().ToList()
                        : AffiliateRepository.FetchActive().ToList();
                    ReferrerSearchEngine.AssignCatalog(_affiliateCatalog);
                    break;
            }
        });
    }

    private void btnClear_Click(object sender, EventArgs e)
    {
        txtSearch.Text = string.Empty;
        selectTextControl();
    }

    private void txtSearch_KeyUp(object sender, KeyEventArgs e)
    {
        switch (e.KeyCode) {
            case Keys.Escape:
                e.Handled = true;
                resetControls();
                break;
            case Keys.Down:
                e.Handled = true;
                gridControl.Focus();
                break;
        }
    }

    private void populateGrid()
    {
        gridView.BeginUpdate();
        try {
            referrerSearchRecBindingSource.DataSource = _filteredCatalog;
        }
        finally {
            gridView.EndUpdate();
            gridView.RefreshData();
            gridView.MoveFirst();
        }
    }

    public void UpdateControls()
    {
        loadCatalog();
        resetControls();
        populateGrid();
        selectTextControl();
    }

    private void resetControls()
    {
        txtSearch.Text = string.Empty;

        gridView.BeginDataUpdate();
        try {
            referrerSearchRecBindingSource.DataSource = null;
        }
        finally {
            gridView.EndDataUpdate();
        }

        resetFilteredCatalog();
    }

    private void resetFilteredCatalog()
    {
        _filteredCatalog.Clear();
        switch (_entityType) {
            case SearchEntityType.Physician:
                if (_referrerCatalog != null)
                    _filteredCatalog.AddRange(_referrerCatalog.Select(ReferrerSearchRec.AssembleFrom));
                break;
            case SearchEntityType.AssociateLab:
                if (_associateCatalog != null)
                    _filteredCatalog.AddRange(_associateCatalog.Select(ReferrerSearchRec.AssembleFrom));
                break;
            case SearchEntityType.Corporate:
                if (_corporateCatalog != null)
                    _filteredCatalog.AddRange(_corporateCatalog.Select(ReferrerSearchRec.AssembleFrom));
                break;
            case SearchEntityType.Affiliate:
                if (_affiliateCatalog != null)
                    _filteredCatalog.AddRange(_affiliateCatalog.Select(ReferrerSearchRec.AssembleFrom));
                break;
        }
    }

    private void selectTextControl()
    {
        txtSearch.SelectAll();
        txtSearch.Focus();
    }

    private void txtSearch_TextChanged(object sender, EventArgs e) =>
        performSearch(txtSearch.Text.Trim().ToLowerInvariant());

    private void performSearch(string text)
    {
        if (string.IsNullOrEmpty(text))
            resetFilteredCatalog();
        else {
            _filteredCatalog.Clear();
            var foundItems = ReferrerSearchEngine.Search($"*{text}*", MAX_SEARCH_RESULTS);
            if (foundItems.Count > 0)
                _filteredCatalog.AddRange(foundItems);
            else
                resetFilteredCatalog();
        }

        populateGrid();
    }

    private ReferrerSearchRec? getSelectedReferrer() =>
        gridView.SelectedRowsCount == 1 ? gridView.GetFocusedRow() as ReferrerSearchRec : null;

    private void btnSelect_Click(object sender, EventArgs e)
    {
        var referrer = getSelectedReferrer();
        if (referrer != null) {
            SelectedItemId = referrer.Id;
            if (ConfirmSelection && !MessageDlg.Confirm(
                    $"You've selected the following referrer:\n{referrer.Name}\nDo you want to apply the selection?"))
                return;

            DialogResult = DialogResult.OK;
            return;
        }

        MessageDlg.Warning("No Referrer Selected");
    }

    private void btnCancel_Click(object sender, EventArgs e) => resetControls();

    private void gridView_KeyUp(object sender, KeyEventArgs e)
    {
        switch (e.KeyCode) {
            case Keys.Enter:
                e.Handled = true;
                btnSelect_Click(null, null);
                break;
        }
    }

    public static int ExecuteDialog(Form parent, bool showInactiveReferrers, bool confirmSelection, SearchEntityType type)
    {
        using var frm = new ReferrerQuickSelectionDialog(showInactiveReferrers, type);
        frm.ConfirmSelection = confirmSelection;
        frm.UpdateControls();
        return frm.ShowDialog(parent) == DialogResult.OK ? frm.SelectedItemId : -1;
    }

    private void ReferrerSelectionDialog_KeyUp(object sender, KeyEventArgs e)
    {
        switch (e.KeyCode) {
            case Keys.F5:
                selectTextControl();
                e.Handled = true;
                break;
            case Keys.Escape:
                e.Handled = true;
                DialogResult = DialogResult.Cancel;
                break;
        }
    }

    private void gridView_CustomDrawCell(object sender, DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventArgs e)
    {
        if ( /*e.Column.FieldName == "Name" && */ gridView.GetRow(e.RowHandle) is ReferrerSearchRec { IsActive: false }) {
            //e.Cache.DrawImage(warningImage, e.Bounds.Location);
            var bounds = new Rectangle(e.Bounds.X + 4, e.Bounds.Y + 2, e.Bounds.Width, e.Bounds.Height);
            e.Cache.FillRectangle(Color.LightGray, e.Bounds);
            e.Appearance.DrawString(e.Cache, e.DisplayText, bounds, _disabledFont, Color.DimGray,
                                    StringFormat.GenericDefault);
            e.Handled = true;
        }
        else
            e.DefaultDraw();
    }
}