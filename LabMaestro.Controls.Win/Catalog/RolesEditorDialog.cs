﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: RolesEditorDialog.cs 557 2013-04-30 04:58:45Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using LabMaestro.Domain;
using LabMaestro.Shared;

namespace LabMaestro.Controls.Win
{
    public partial class RolesEditorDialog : XtraForm
    {
        private Role _currentRole;

        public RolesEditorDialog()
        {
            InitializeComponent();
            WinUtils.ApplyTheme(this);
            WinUtils.SetFormTitle(this, "Edit Roles");
        }

        private void resetEditorControls()
        {
            lblId.Text = string.Empty;
            txtName.Text = string.Empty;
            txtCode.Text = string.Empty;
            txtDescription.Text = string.Empty;
            chkIsActive.Checked = true;
            chkIsAdmin.Checked = false;

            luPermissions.EditValue = null;
            lbRolePermissions.Items.Clear();

            btnNew.Enabled = false;
            btnSave.Enabled = false;
            btnAdd.Enabled = false;
        }

        private void populateEditorControls()
        {
            SuspendRedraw();
            Cursor = Cursors.WaitCursor;
            try
            {
                resetEditorControls();

                if (_currentRole != null)
                {
                    lblId.Text = _currentRole.Id.ToString();
                    txtName.Text = _currentRole.Name;
                    txtCode.Text = _currentRole.RoleCode;
                    txtDescription.Text = _currentRole.Description;
                    chkIsActive.Checked = _currentRole.IsActive;
                    chkIsAdmin.Checked = _currentRole.IsAdmin;

                    foreach (var rolePermission in _currentRole.RolePermissions)
                    {
                        lbRolePermissions.Items.Add(rolePermission);
                    }

                    btnNew.Enabled = true;
                    btnSave.Enabled = true;
                }
            }
            finally
            {
                Cursor = Cursors.Default;
                ResumeRedraw();
            }
        }

        private void populateLookupControls(bool loadPermissions)
        {
            IEnumerable<Role> roles = null;
            List<Permission> perms = null;
            WaitFormControl.WaitOperation(this, () =>
                                                    {
                                                        roles = RolesRepository.FindAllRoles(false);
                                                        if (loadPermissions) perms = PermissionsRepository.FindAll();
                                                    });

            lbRoles.Items.Clear();
            foreach (var role in roles)
            {
                lbRoles.Items.Add(role);
            }

            if (loadPermissions)
            {
                WinUtils.PopulateLookupControl(luPermissions, perms, "Name", "Id",
                                               new[] {"Name", "IsActive", "PermCode"}, 0);
            }
        }

        private void RolesEditorDialog_Load(object sender, EventArgs e)
        {
            populateLookupControls(true);
        }

        private void lbRoles_SelectedIndexChanged(object sender, EventArgs e)
        {
            _currentRole = lbRoles.SelectedItem as Role;
            populateEditorControls();
        }

        private void btnAddPermission_Click(object sender, EventArgs e)
        {
            if (luPermissions.EditValue != null && _currentRole != null)
            {
                var permId = (short) luPermissions.EditValue;
                if (permId > 0)
                {
                    if (_currentRole.RolePermissions.Any(x => x.PermissionId == permId))
                    {
                        MessageDlg.Warning("Permission has already been granted to the role!");
                        return;
                    }

                    WaitFormControl.WaitOperation(this, () =>
                                                            {
                                                                _currentRole.AddPermission(permId);
                                                                RolesRepository.Save();
                                                                RolesRepository.Reset(); // reset the entity cache
                                                            }, "Saving...");
                    populateLookupControls(false);
                }
            }
        }

        private void btnRemovePermission_Click(object sender, EventArgs e)
        {
            if (_currentRole == null) return;
            var rp = lbRolePermissions.SelectedItem as RolePermission;
            if (rp != null)
            {
                if (!MessageDlg.Confirm("Really revoke this permission?")) return;

                WaitFormControl.WaitOperation(this,
                                              () =>
                                                  {
                                                      RolesRepository.RoleRemovePermission(rp.RoleId, rp.PermissionId);
                                                      RolesRepository.Reset(); // reset the entity manager cache
                                                  }, "Saving...");
                populateLookupControls(false);
            }
        }

        private void btnNew_Click(object sender, EventArgs e)
        {
            _currentRole = null;

            btnSave.Enabled = false;
            btnNew.Enabled = false;
            btnAdd.Enabled = true;
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            if (_currentRole == null)
            {
                if (!validateEditorControls()) return;
                var role = RolesRepository.CreateRole(txtCode.Text.Trim().ToUpper(),
                                                      txtName.Text.Trim(),
                                                      chkIsActive.Checked,
                                                      chkIsAdmin.Checked);
                role.Description = txtDescription.Text.Trim();
                WaitFormControl.WaitOperation(this, RolesRepository.Save, "Saving...");
                populateLookupControls(false);
            }
        }

        private bool validateEditorControls()
        {
            var text = txtName.Text.Trim();
            if (!SharedUtilities.StringWithinLimit(text, 2, 40))
            {
                MessageDlg.Error("Name must be between 2 and 40 characters!");
                txtName.Focus();
                return false;
            }

            text = txtCode.Text.Trim();
            if (!SharedUtilities.StringWithinLimit(text, 4, 40))
            {
                MessageDlg.Error("Role Code must be between 4 and 40 characters!");
                txtCode.Focus();
                return false;
            }

            return true;
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            if (_currentRole != null)
            {
                if (!validateEditorControls()) return;
                _currentRole.Name = txtName.Text.Trim();
                _currentRole.RoleCode = txtCode.Text.Trim().ToUpper();
                _currentRole.Description = txtDescription.Text.Trim();
                _currentRole.IsActive = chkIsActive.Checked;
                _currentRole.IsAdmin = chkIsAdmin.Checked;
                WaitFormControl.WaitOperation(this, RolesRepository.Save, "Saving...");
                populateLookupControls(false);
            }
        }

        private void btnExit_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        private void btnEditPermissions_Click(object sender, EventArgs e)
        {
            using (var frm = new PermissionsEditorDialog())
            {
                frm.ShowDialog(this);
            }
            populateLookupControls(true);
        }
    }
}