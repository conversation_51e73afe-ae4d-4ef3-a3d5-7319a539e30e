﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: TestSelectionDialog.cs 676 2013-06-26 08:16:05Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using LabMaestro.Domain;

namespace LabMaestro.Controls.Win
{
    public partial class TestSelectionDialog : XtraForm
    {
        public TestSelectionDialog()
        {
            InitializeComponent();
            WinUtils.ApplyTheme(this);
            SelectedTestId = -1;
        }

        public short SelectedTestId { get; private set; }

        private void TestSelectionDialog_Load(object sender, EventArgs e)
        {
            lbTests.Items.Clear();
            var tests = LabTestsRepository.FindActiveTests().OrderBy(x => x.ShortName);
            foreach (var labTest in tests)
            {
                lbTests.Items.Add(new TestInfo {Id = labTest.Id, Name = labTest.ShortName});
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        private void btnSelect_Click(object sender, EventArgs e)
        {
            if (lbTests.SelectedIndex == -1)
            {
                MessageDlg.Warning("Please select the Lab test");
                return;
            }

            var testInfo = lbTests.SelectedValue as TestInfo;
            if (testInfo != null) SelectedTestId = testInfo.Id;
            DialogResult = DialogResult.OK;
        }

        public static short ExecuteDialog(Form parent)
        {
            using (var frm = new TestSelectionDialog())
            {
                if (frm.ShowDialog(parent) == DialogResult.OK)
                {
                    return frm.SelectedTestId;
                }
            }
            return -1;
        }

        private sealed class TestInfo
        {
            internal short Id { get; set; }

            public string Name { private get; set; }

            public override string ToString()
            {
                return Name;
            }
        }
    }
}