﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: TemplateGroupsEditorDialog.cs 723 2013-07-03 06:56:29Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using LabMaestro.BusinessLogic;
using LabMaestro.Domain;
using LabMaestro.Shared;
using Xceed.Grid;

namespace LabMaestro.Controls.Win
{
    public partial class TemplateGroupsEditorDialog : XtraForm
    {
        private TemplateGroupSlice _currentSelectedGroup;
        private DataCell _oldCell;

        public TemplateGroupsEditorDialog()
        {
            InitializeComponent();
            WinUtils.ApplyTheme(this);
            WinUtils.SetFormTitle(this, "Template Groups");
            LabsRepository.PreCacheLabNames();
        }

        public void UpdateControls()
        {
            populateLookupControl();

            var i = 0;
            GridControl.GridAddColumn("id", "Id", i++, 35);
            GridControl.GridAddColumn("name", "Name", i++, 140);
            GridControl.GridAddColumn("lab", "Lab", i++, 140);
            GridControl.GridAddColumn("actv", "Y?", i++, 20);

            resetEditorControls();
            populateGrid();
        }

        private void populateLookupControl()
        {
            var labs = LabsRepository
                .GetAllLabSlices()
                .Where(
                    x =>
                    x.TestResultType == (byte) TestResultType.Template ||
                    x.TestResultType == (byte) TestResultType.UserTemplate)
                .OrderBy(x => x.Name)
                .ToList();
            WinUtils.PopulateLookupControl(luLab, labs, "Name", "Id", new[] {"Name"});
        }

        public void populateGrid()
        {
            GridControl.BeginInit();
            GridControl.DataRows.Clear();
            try
            {
                var groups = TemplateGroupsRepository.GetAllTemplateGroups();
                foreach (var groupSlice in groups)
                {
                    var row = GridControl.DataRows.AddNew();
                    row.Cells["id"].Value = groupSlice.Id.ToString();
                    row.Cells["name"].Value = groupSlice.Name;
                    row.Cells["lab"].Value = LabsRepository.GetLabName(groupSlice.LabId);
                    row.Cells["actv"].Value = groupSlice.IsActive ? "Y" : "N";
                    row.Tag = groupSlice;
                    row.Height = 21;
                    GridControl.GridWireCellToolTips(row.Cells, showToolTip);
                    row.EndEdit();
                }
            }
            finally
            {
                GridControl.EndInit();
            }
        }

        private void showToolTip(object sender, MouseEventArgs e)
        {
            var cell = (DataCell) sender;
            if (_oldCell == cell) return;

            _oldCell = cell;
            var content = _oldCell.Value;
            toolTip.SetToolTip(GridControl, (content != null) ? content.ToString() : string.Empty);
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        private void btnNew_Click(object sender, EventArgs e)
        {
            resetEditorControls();

            _currentSelectedGroup = null;
            btnNew.Enabled = false;
            btnAdd.Enabled = true;
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            if (_currentSelectedGroup == null)
            {
                if (validateEditorControls())
                {
                    var newGroup = new TemplateGroup
                                       {
                                           Name = txtName.Text.Trim(),
                                           IsActive = chkIsActive.Checked,
                                           LabId = (short) luLab.EditValue
                                       };
                    TemplateGroupsRepository.Add(newGroup);
                    TemplateGroupsRepository.Save();
                    populateGrid();
                }
            }
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            if (_currentSelectedGroup != null)
            {
                if (validateEditorControls())
                {
                    _currentSelectedGroup.Name = txtName.Text.Trim();
                    _currentSelectedGroup.IsActive = chkIsActive.Checked;
                    _currentSelectedGroup.LabId = (short) luLab.EditValue;
                    TemplateGroupsRepository.SaveSlice(_currentSelectedGroup);
                    populateGrid();
                }
            }
        }

        private void resetEditorControls()
        {
            lblId.Text = string.Empty;
            txtName.Text = string.Empty;
            luLab.EditValue = null;
            luLab.Text = string.Empty;
            chkIsActive.Checked = true;

            btnNew.Enabled = true;
            btnAdd.Enabled = false;
            btnSave.Enabled = false;
        }

        private void populateEditorControls()
        {
            resetEditorControls();

            if (_currentSelectedGroup != null)
            {
                lblId.Text = _currentSelectedGroup.Id.ToString();
                txtName.Text = _currentSelectedGroup.Name;
                chkIsActive.Checked = _currentSelectedGroup.IsActive;
                luLab.EditValue = _currentSelectedGroup.LabId;
                btnSave.Enabled = true;
            }
        }

        private bool validateEditorControls()
        {
            if (string.IsNullOrEmpty(txtName.Text.Trim()))
            {
                MessageDlg.Warning("Please enter a valid name");
                txtName.Select();
                txtName.Focus();
                return false;
            }
            return true;
        }

        private void GridControl_SelectedRowsChanged(object sender, EventArgs e)
        {
            if (GridControl.SelectedRows.Count == 1)
            {
                _currentSelectedGroup = GridControl.SelectedRows[0].Tag as TemplateGroupSlice;
                populateEditorControls();
            }
        }

        private void btnLabsEdit_Click(object sender, EventArgs e)
        {
            using (var form = new LabsEditorDialog())
            {
                form.LabsDataProvider = new LabsDataAccessProviderDb(false);
                form.DiscountsDataProvider = new DiscountLevelsDataAccessProviderDb();
                form.ReferralsDataProvider = new ReferralGroupsDataAccessProviderDb();
                form.ReportHeadersDataProvider = new LabReportHeadersDataAccessProvider();

                form.UpdateControls();
                form.ShowDialog(this);
            }
            populateLookupControl();
        }
    }
}