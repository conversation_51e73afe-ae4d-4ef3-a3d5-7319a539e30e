﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="btnDeletePhone.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAJNJREFUOE+l
        zKEOwkAQBND9o/Z3kQSDIKkDBwqHw4GrQ+Jwy+yxHdFM0nS55N1cMpcxd//L72HWt+c6PQc+iAoOvBEV
        HHghKjgwIio48EQoj6S6wIE7YrJ0Zn/bbTfEZOnM/rbbrgjlklQXOHBGKKekusCBI0I5JNUFDgwIZZdU
        FziwRyibpLrAga1ZB75Sx4E6ty8vxzT/yoquMwAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="btnNewPhone.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAlJJREFUOE91
        kstrE2EUxWeZjQsTmrhQFy5EgykoyT+grkTBheBCEEQXLVhcuNRNK40WLLEvVAioWFqTQaaV0CSTYEUa
        WvBtGhRfbZ2x7+Y1mbzT43enznQiNHAGJt/9nfvduYcD0KTz4x9aL4c/C22RpHQ1mkRb+CMujL2VzgZn
        hBPDU63/1ze9XAknhfboLDxPEnAMxrG7L6bJPhDHEf8rnBlN4OTT14KZ0R4/MqqlI56STvFvDGgnHfNP
        4vjjSek6/9JiGFBnHaZfY3MTtUYDlXoDcxkVCTndZOJ6EIfnoajdhOuIpTx0bf1wG66jVK1pBuyGSMyv
        wOqLGHXu+xEcHZrwcBdDn0SaWT+osq7lWh1FBhcqVcOANGUy2T8QxaH+kMhdi83K5g9mhvPlCr5tKOC/
        LmHo/YIm+81HWp2tT4TTNyZztCodJqkMVhicK1WQKZaxoZaxVightZpD8MsiHDf8Ru3Bu8/B0Z7NBkrZ
        DJcYXMSKUsRSXsWfXKHJ4EAPD+7Si3cy7Vn/M8vgNIPXGbyqwaoBSxnFMLDeE7HPG5C504FpkUKiGxgw
        67rMwEUGytkCfjN4Pp03DOy9ITi8vMid46fdlDDdgOYlmLpuwYoB/9rIGQZ7ugOwdY64tSBRPClhdGCe
        V2LwQiaPOQb+XM/h+1pWM2jpEWC79WwrSPTwhmcsFE9KmLV3Ai1dozvK3jkMW9eItKv9znaUdVE8KWEU
        EtqzPhZ9MJpZu/a/zrqaDEjOwbDL2R8SDvvGJdozrWrvbV5yeIOCrZt3NdeD+wu/4d/230wglgAAAABJ
        RU5ErkJggg==
</value>
  </data>
  <data name="btnDeleteAddress.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAJNJREFUOE+l
        zKEOwkAQBND9o/Z3kQSDIKkDBwqHw4GrQ+Jwy+yxHdFM0nS55N1cMpcxd//L72HWt+c6PQc+iAoOvBEV
        HHghKjgwIio48EQoj6S6wIE7YrJ0Zn/bbTfEZOnM/rbbrgjlklQXOHBGKKekusCBI0I5JNUFDgwIZZdU
        FziwRyibpLrAga1ZB75Sx4E6ty8vxzT/yoquMwAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="btnNewAddress.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAlJJREFUOE91
        kstrE2EUxWeZjQsTmrhQFy5EgykoyT+grkTBheBCEEQXLVhcuNRNK40WLLEvVAioWFqTQaaV0CSTYEUa
        WvBtGhRfbZ2x7+Y1mbzT43enznQiNHAGJt/9nfvduYcD0KTz4x9aL4c/C22RpHQ1mkRb+CMujL2VzgZn
        hBPDU63/1ze9XAknhfboLDxPEnAMxrG7L6bJPhDHEf8rnBlN4OTT14KZ0R4/MqqlI56STvFvDGgnHfNP
        4vjjSek6/9JiGFBnHaZfY3MTtUYDlXoDcxkVCTndZOJ6EIfnoajdhOuIpTx0bf1wG66jVK1pBuyGSMyv
        wOqLGHXu+xEcHZrwcBdDn0SaWT+osq7lWh1FBhcqVcOANGUy2T8QxaH+kMhdi83K5g9mhvPlCr5tKOC/
        LmHo/YIm+81HWp2tT4TTNyZztCodJqkMVhicK1WQKZaxoZaxVightZpD8MsiHDf8Ru3Bu8/B0Z7NBkrZ
        DJcYXMSKUsRSXsWfXKHJ4EAPD+7Si3cy7Vn/M8vgNIPXGbyqwaoBSxnFMLDeE7HPG5C504FpkUKiGxgw
        67rMwEUGytkCfjN4Pp03DOy9ITi8vMid46fdlDDdgOYlmLpuwYoB/9rIGQZ7ugOwdY64tSBRPClhdGCe
        V2LwQiaPOQb+XM/h+1pWM2jpEWC79WwrSPTwhmcsFE9KmLV3Ai1dozvK3jkMW9eItKv9znaUdVE8KWEU
        EtqzPhZ9MJpZu/a/zrqaDEjOwbDL2R8SDvvGJdozrWrvbV5yeIOCrZt3NdeD+wu/4d/230wglgAAAABJ
        RU5ErkJggg==
</value>
  </data>
</root>