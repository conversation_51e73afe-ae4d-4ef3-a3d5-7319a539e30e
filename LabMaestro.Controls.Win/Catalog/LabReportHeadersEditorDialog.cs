﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: LabReportHeadersEditorDialog.cs 538 2013-04-25 11:59:46Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using LabMaestro.Domain;
using LabMaestro.Shared;

namespace LabMaestro.Controls.Win
{
    public partial class LabReportHeadersEditorDialog : XtraForm
    {
        private bool _isNewItem;

        public LabReportHeadersEditorDialog()
        {
            InitializeComponent();
            _isNewItem = false;
            WinUtils.ApplyTheme(this);
            WinUtils.SetFormTitle(this, "Report Headers");
            createGridColumns();
            resetEditorControls();
        }

        private void createGridColumns()
        {
            GridControl.Columns.Clear();
            GridControl.GridAddColumn(@"id", "Id", 0, 35);
            GridControl.GridAddColumn(@"name", "Name", 1, 200);
            GridControl.GridAddColumn(@"active", "Y?", 2, 30);
            GridControl.GridAddColumn(@"priority", "Prio", 3, 35);
            GridControl.GridAddColumn(@"lab", "Lab", 4, 100);
        }

        public void UpdateControls()
        {
            _isNewItem = false;
            populateLookupControls();
            resetEditorControls();
            populateGridControl();
            GridControl.Focus();
        }

        private void populateGridControl()
        {
            GridControl.BeginInit();
            try
            {
                GridControl.DataRows.Clear();
                var items = LabReportHeadersRepository.FetchAllSlices();
                foreach (var slice in items)
                {
                    var row = GridControl.DataRows.AddNew();

                    row.Cells["id"].Value = slice.Id.ToString();
                    row.Cells["name"].Value = slice.Name;
                    row.Cells["active"].Value = slice.IsActive ? "Y" : "N";
                    row.Cells["priority"].Value = slice.SortPriority.ToString();
                    row.Cells["lab"].Value = LabsRepository.GetLabName(slice.LabId);

                    row.Tag = slice;
                    row.Height = 23;
                    row.EndEdit();
                }
            }
            finally
            {
                GridControl.EndInit();
            }
        }

        private void populateLookupControls()
        {
            var items =
                LabsRepository.GetAllLabSlices().Where(x => x.TestResultType == (byte) TestResultType.Discrete).ToList();
            populateLookupControl(luLabs, items);
        }

        private static void populateLookupControl(LookUpEdit control, List<LabSlice> items)
        {
            control.Properties.Columns.Clear();
            control.Properties.Columns.Add(new LookUpColumnInfo(@"Name", 0));

            control.Properties.ValueMember = @"Id";
            control.Properties.DisplayMember = @"Name";
            control.Properties.DataSource = items;

            //control.Properties.PopulateColumns();
        }

        private byte getSortPriority()
        {
            switch (rgSortPriority.SelectedIndex)
            {
                case 0:
                    return (byte) SortPriorityType.Critical;
                case 1:
                    return (byte) SortPriorityType.High;
                case 3:
                    return (byte) SortPriorityType.Low;
                case 4:
                    return (byte) SortPriorityType.Lowest;
                default:
                    return (byte) SortPriorityType.Normal;
            }
        }

        private void setSortPriority(SortPriorityType priority)
        {
            switch (priority)
            {
                case SortPriorityType.Critical:
                    rgSortPriority.SelectedIndex = 0;
                    break;
                case SortPriorityType.High:
                    rgSortPriority.SelectedIndex = 1;
                    break;
                case SortPriorityType.Low:
                    rgSortPriority.SelectedIndex = 3;
                    break;
                case SortPriorityType.Lowest:
                    rgSortPriority.SelectedIndex = 4;
                    break;
                default:
                    rgSortPriority.SelectedIndex = 2;
                    break;
            }
        }

        private LabReportHeaderSlice getSelectedSlice()
        {
            if ((GridControl.SelectedRows.Count == 1) && (GridControl.SelectedRows[0] != null))
            {
                return GridControl.SelectedRows[0].Tag as LabReportHeaderSlice;
            }
            return null;
        }

        private void GridControl_SelectedRowsChanged(object sender, EventArgs e)
        {
            populateEditorControls(getSelectedSlice());
        }

        private void populateEditorControls(LabReportHeaderSlice slice)
        {
            resetEditorControls();
            if (slice != null)
            {
                lblId.Text = slice.Id.ToString();
                txtName.Text = slice.Name;
                chkIsActive.Checked = slice.IsActive;
                setSortPriority((SortPriorityType) slice.SortPriority);
                luLabs.EditValue = slice.LabId;
                redReportHeader.RtfText = CompressionUtils.DecompressToString(slice.ReportHeader);
            }
        }

        private void resetEditorControls()
        {
            lblId.Text = string.Empty;
            txtName.Text = string.Empty;
            chkIsActive.Checked = false;
            rgSortPriority.SelectedIndex = -1;
            luLabs.EditValue = null;
            redReportHeader.Text = string.Empty;
            redReportHeader.ClearUndo();
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        private void btnNew_Click(object sender, EventArgs e)
        {
            _isNewItem = true;
            resetEditorControls();
            chkIsActive.Checked = true;
            txtName.Focus();
        }

        private bool checkEmptyString(string field, string value)
        {
            if (string.IsNullOrEmpty(value))
            {
                MessageDlg.Error(string.Format("{0} must not be empty!", field));
                return false;
            }
            return true;
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            var name = txtName.Text.Trim();
            if (!checkEmptyString("Name", name))
            {
                return;
            }

            var sortPriority = getSortPriority();

            short labId = 0;
            if (luLabs.EditValue != null)
            {
                labId = (short) luLabs.EditValue;
            }

            var hdrBytes = CompressionUtils.CompressString(redReportHeader.RtfText.Trim());

            if (_isNewItem)
            {
                var slice = new LabReportHeaderSlice
                    {
                        IsActive = chkIsActive.Checked,
                        Name = name,
                        LabId = labId,
                        SortPriority = sortPriority,
                        ReportHeader = hdrBytes
                    };
                try
                {
                    var id = LabReportHeadersRepository.CreateNewLabReportHeaderFromSlice(slice);
                    var header = LabReportHeadersRepository.FindById(id);
                    var note = string.Format("{0} - '{1}'", header.Id, header.Name);
                    AuditTrailRepository.LogCatalogEvent(AuditEventType.catLabReportHeaderAdded, note);
                }
                catch (Exception exc)
                {
                    MessageDlg.Error(exc.Message);
                    return;
                }
            }
            else
            {
                var slice = getSelectedSlice();
                slice.IsActive = chkIsActive.Checked;
                slice.Name = name;
                slice.SortPriority = sortPriority;
                slice.ReportHeader = hdrBytes;
                try
                {
                    LabReportHeadersRepository.UpdateLabReportHeader(slice);
                    var note = string.Format("{0} - '{1}'", slice.Id, slice.Name);
                    AuditTrailRepository.LogCatalogEvent(AuditEventType.catLabReportHeaderModified, note);
                }
                catch (Exception exc)
                {
                    MessageDlg.Error(exc.Message);
                    return;
                }
            }

            _isNewItem = false;
            resetEditorControls();
            populateGridControl();
        }

        private void btnReset_Click(object sender, EventArgs e)
        {
            if (!_isNewItem)
            {
                populateEditorControls(getSelectedSlice());
            }
            else
            {
                resetEditorControls();
            }
            txtName.Focus();
        }
    }
}