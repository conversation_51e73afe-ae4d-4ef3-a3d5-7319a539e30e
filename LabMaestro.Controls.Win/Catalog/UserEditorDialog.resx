﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="btnDepartmentsEdit.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGOfPtRkwAAACBjSFJNAAB6
        JQAAgIMAAPn/AACA6QAAdTAAAOpgAAA6mAAAF2+SX8VGAAAACXBIWXMAAAsMAAALDAE/QCLIAAACzUlE
        QVQ4T22TX0hTURzHz0M91lP0R+iPpVRQWOaDhtGDlfbPIkWKHGVSSQj9IbCCIKjMP5m6ual3zanb/DPD
        0EIUip4i7CGyB7ViKHPb3eY2t7m5rbvt2zkHbmh44MO5l3O/v/P9/s65xGg0yij6+vowODgIOgcp/tUY
        GBj4tExDiMFgYA/l/f39mJ+fx9jYGKxWK8LhMCcUCnEWFxcRCARAv5WYRoaJb5jNZni9XrDBCrhcLiST
        SSQSCU48HkcsFuOFqEjq7u4mMoRags/n42I25AKyWJIkLo5EIggGg6Aiqauri8gQk8kUGB4eZtY44+Pj
        K8TRaJSLWRy/3w8qkjo7O4kMyyGyhTL1NCp1v3lW1g/aMPT09IBuwAvTneF0OqHX66WOjg6S07B38kB1
        ajXLwQv0f3ZhaCLwLy/bdWlpiedm1hcWFuB2uyHoBCn75R5LxdvCUFZtup/ZEFkP/s/LLMudZ2KPxwOr
        3Yr8xpzk/dFLsYm4EQ8/KOKE2uEFXg1boRmdWyH+ZZvmDWZiu2hDsfYE7oyU4MsfAQ8+Xk5m1ab5iU6n
        E9n5X1NP4V6vldtkeYuU+cht2IdS5QUIrwUUNB7GrZHzGInWwex7gsyaXcH9z7ZvIFqtVmTHtjzvVUMR
        rgzloytUhZPabJzSHEHZuwL+3uq+jYM1O8NUvFGj0RAiCIJIBxzzfthcCzzvzd5SFL85ivpAOeeRpYTP
        T50KZL5IAxOr1WrCIG1tbaLdbsd1zSQqtVM8L3NU0avAWXM2qrznOHdtp6ntNNSr6iSVSkVkSGtrq8ju
        /mPTT9S8n8Ps7CxoLLS3t6OwMQ/HezNQZs1FxvNUfP0xDiqSlEolkSEtLS2emZkZ/iOxi+JwOGCz2fgP
        xYpd1J/Bodp0fJ/6BovFgubm5kTKjk3rNm9Zs5Y+8wITNEtkNejaajjyjuXt3rotZX1TUxP5Czt16Za5
        nkKXAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="btnRolesEdit.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGOfPtRkwAAACBjSFJNAAB6
        JQAAgIMAAPn/AACA6QAAdTAAAOpgAAA6mAAAF2+SX8VGAAAACXBIWXMAAAsMAAALDAE/QCLIAAACzUlE
        QVQ4T22TX0hTURzHz0M91lP0R+iPpVRQWOaDhtGDlfbPIkWKHGVSSQj9IbCCIKjMP5m6ual3zanb/DPD
        0EIUip4i7CGyB7ViKHPb3eY2t7m5rbvt2zkHbmh44MO5l3O/v/P9/s65xGg0yij6+vowODgIOgcp/tUY
        GBj4tExDiMFgYA/l/f39mJ+fx9jYGKxWK8LhMCcUCnEWFxcRCARAv5WYRoaJb5jNZni9XrDBCrhcLiST
        SSQSCU48HkcsFuOFqEjq7u4mMoRags/n42I25AKyWJIkLo5EIggGg6Aiqauri8gQk8kUGB4eZtY44+Pj
        K8TRaJSLWRy/3w8qkjo7O4kMyyGyhTL1NCp1v3lW1g/aMPT09IBuwAvTneF0OqHX66WOjg6S07B38kB1
        ajXLwQv0f3ZhaCLwLy/bdWlpiedm1hcWFuB2uyHoBCn75R5LxdvCUFZtup/ZEFkP/s/LLMudZ2KPxwOr
        3Yr8xpzk/dFLsYm4EQ8/KOKE2uEFXg1boRmdWyH+ZZvmDWZiu2hDsfYE7oyU4MsfAQ8+Xk5m1ab5iU6n
        E9n5X1NP4V6vldtkeYuU+cht2IdS5QUIrwUUNB7GrZHzGInWwex7gsyaXcH9z7ZvIFqtVmTHtjzvVUMR
        rgzloytUhZPabJzSHEHZuwL+3uq+jYM1O8NUvFGj0RAiCIJIBxzzfthcCzzvzd5SFL85ivpAOeeRpYTP
        T50KZL5IAxOr1WrCIG1tbaLdbsd1zSQqtVM8L3NU0avAWXM2qrznOHdtp6ntNNSr6iSVSkVkSGtrq8ju
        /mPTT9S8n8Ps7CxoLLS3t6OwMQ/HezNQZs1FxvNUfP0xDiqSlEolkSEtLS2emZkZ/iOxi+JwOGCz2fgP
        xYpd1J/Bodp0fJ/6BovFgubm5kTKjk3rNm9Zs5Y+8wITNEtkNejaajjyjuXt3rotZX1TUxP5Czt16Za5
        nkKXAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="btnBioClear.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAKASURBVDhPlZFbSFNxHMf/xwuWFikqhSYRhWQvhoIF
        GV20ixYKPkgkNLpRoiPwIbtJF6xE2/ShXsp2ZmEkkqikBKZktiHVMlSczaORbi43p+TlLJxn335n4Jhs
        PfTwOef/P+f7/fzOhV09xvbePM5MN7LZBK2PAmD/A6Oi5WVhOBqVEaD1IkkyAwX/hSxwtJasQ3dZJBqK
        wmWJqbKQBdVWsbtEdKCSL+xaFrt8Py944d31DR5JeR439+Qe+0plEEmBSr7I3yCIpj6+ncMttF6JQF0F
        h66mFHeLNmGOBPsClXzxLh6cZ3dqK5jb0HkQ9rFSNBdudGrTmFlbzfU/e8hKSRbrW1zBc6CbpzQqThzu
        zcW0cAm9ylQ0xq+HseUsbEIJvnRmOJ/XrBV5NcdTNj6QQKFRMXtHfZLYkb0Db/ckQBw+A8l2DpK9GNJM
        Gf7YKmDoOrzEV4c6eTV7QZ0DRJDXRJuwuu3M+PpQ9LLLVg5pugjSrHxWQprKg8uSifmJC7CPlqKvO8vd
        pImfp1ezeQUeyS2meN+cvCjNVtJ0BU2np6CyNJmO+ZGdsH7bBEEfCXN/FsYHL4JXcVOrBVUsrv5RlNPl
        qMKyJR0u826IY8mYMW6DxRCDkZ4QmD4EY3IoH/r2VBe9yqtVAhmtmhsc7TsNh+mIZ+LE5ygIH8M8RRnz
        wAkIhpPg1SELNHCzn4AuZjQ+jRN/mYoh6CK9RUEfC6uxAEO9+W5tdehvyu2X834CGXo0na4tbck6rMCP
        T4mwDObA+l0J3ZsUF/2tSSonrmT9yjIUiKbgeH9PrvRzoAD6tl1LdTVrRF7FGuhejG92VdEXCm6h3+Qg
        0TShof1W/xzYX/hDlR81LFHxAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="btnBioLoad.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAslJREFUOE+F
        kltIk2EcxkfOw+aqOZYODE3NCxFJiC4ihlGRFlqZYVQiOglEA5comuk8a9nFDPFABydU2BTdNA+VxTIK
        tKauTE2lNLXU6Zym81DJ0/e+W5Pyoj/8Lr7n+57f+4f3Y3XWRPQwoKsuEu8aotDXFI3+ZslffGyNIRj7
        miRRrH+HlGEqgn4wFUPtcSDPQ68zMTehBKC1srLwGL2Poo2W2saQwvrSdawvmlmbK8D0QCoVYeQGMGrB
        WItG9Wn4PBDC+65AbqmbBb8WrlF+zhMKKSRf0yVivTcJ6EumqOtCsbtKANfKbXAr3q6xCn7MFdKT1wwb
        kNyklWK1J9Eq6K6PhGeF00HRLR4EpRy45PGyqGB1Jh8regvT+VhmIPl8ZwJMXVIMaGMQXOeLkPu+IIeK
        bvJUnEpbOGU5GKlgeSoPpslcmL7lYskCyfWvLuHty0gE1Hqj3qDE8foAKnApcNQQAS/TDlSwOJGDxfEc
        fCeMZWOBgeSaJ+dwpkUMlaEGCbpIeJTyydoKoZwLIuCm2ZoFC1+yMT+aBeNnAnOFnzJRW3MSYa1iaExP
        EasNwf7mndhVwodr6VbsuGcW2CezzQJSMAzLYBiSYXZQhq+6FAQ1+OPFchvitEE42uZFBX4qZ3go+VTA
        LWcEF200VDA7mIGZgQzo+9Pp6h3KCByp3YPYjkCEtftsEggVXNhL2Ua2ZIs/FZDi1IermHyfRgUtt8+j
        vCIY4jJ3BLZ4WQU+1UI4FznCIZ49YhvDlMmQAil+013BRLf5D1SXncXD4nA0Vl2AuNgdB1RuVCCUcZEv
        F9ObsA5TMA48j8e4NhVjb1KooKrwGErSD6NGfgINd0KxN0cE/2oRBGkcPFOEm0/+M0yBz6DqVkdjuP0y
        ulTRSJLsS2BeeZq/YLF4Ujs+J46tsouxUViizcNITpFtyAbFaYf8LPF/hsX6DURyOPDSOpEVAAAAAElF
        TkSuQmCC
</value>
  </data>
  <data name="btnSigClear.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAKASURBVDhPlZFbSFNxHMf/xwuWFikqhSYRhWQvhoIF
        GV20ixYKPkgkNLpRoiPwIbtJF6xE2/ShXsp2ZmEkkqikBKZktiHVMlSczaORbi43p+TlLJxn335n4Jhs
        PfTwOef/P+f7/fzOhV09xvbePM5MN7LZBK2PAmD/A6Oi5WVhOBqVEaD1IkkyAwX/hSxwtJasQ3dZJBqK
        wmWJqbKQBdVWsbtEdKCSL+xaFrt8Py944d31DR5JeR439+Qe+0plEEmBSr7I3yCIpj6+ncMttF6JQF0F
        h66mFHeLNmGOBPsClXzxLh6cZ3dqK5jb0HkQ9rFSNBdudGrTmFlbzfU/e8hKSRbrW1zBc6CbpzQqThzu
        zcW0cAm9ylQ0xq+HseUsbEIJvnRmOJ/XrBV5NcdTNj6QQKFRMXtHfZLYkb0Db/ckQBw+A8l2DpK9GNJM
        Gf7YKmDoOrzEV4c6eTV7QZ0DRJDXRJuwuu3M+PpQ9LLLVg5pugjSrHxWQprKg8uSifmJC7CPlqKvO8vd
        pImfp1ezeQUeyS2meN+cvCjNVtJ0BU2np6CyNJmO+ZGdsH7bBEEfCXN/FsYHL4JXcVOrBVUsrv5RlNPl
        qMKyJR0u826IY8mYMW6DxRCDkZ4QmD4EY3IoH/r2VBe9yqtVAhmtmhsc7TsNh+mIZ+LE5ygIH8M8RRnz
        wAkIhpPg1SELNHCzn4AuZjQ+jRN/mYoh6CK9RUEfC6uxAEO9+W5tdehvyu2X834CGXo0na4tbck6rMCP
        T4mwDObA+l0J3ZsUF/2tSSonrmT9yjIUiKbgeH9PrvRzoAD6tl1LdTVrRF7FGuhejG92VdEXCm6h3+Qg
        0TShof1W/xzYX/hDlR81LFHxAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="btnSigLoad.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAslJREFUOE+F
        kltIk2EcxkfOw+aqOZYODE3NCxFJiC4ihlGRFlqZYVQiOglEA5comuk8a9nFDPFABydU2BTdNA+VxTIK
        tKauTE2lNLXU6Zym81DJ0/e+W5Pyoj/8Lr7n+57f+4f3Y3XWRPQwoKsuEu8aotDXFI3+ZslffGyNIRj7
        miRRrH+HlGEqgn4wFUPtcSDPQ68zMTehBKC1srLwGL2Poo2W2saQwvrSdawvmlmbK8D0QCoVYeQGMGrB
        WItG9Wn4PBDC+65AbqmbBb8WrlF+zhMKKSRf0yVivTcJ6EumqOtCsbtKANfKbXAr3q6xCn7MFdKT1wwb
        kNyklWK1J9Eq6K6PhGeF00HRLR4EpRy45PGyqGB1Jh8regvT+VhmIPl8ZwJMXVIMaGMQXOeLkPu+IIeK
        bvJUnEpbOGU5GKlgeSoPpslcmL7lYskCyfWvLuHty0gE1Hqj3qDE8foAKnApcNQQAS/TDlSwOJGDxfEc
        fCeMZWOBgeSaJ+dwpkUMlaEGCbpIeJTyydoKoZwLIuCm2ZoFC1+yMT+aBeNnAnOFnzJRW3MSYa1iaExP
        EasNwf7mndhVwodr6VbsuGcW2CezzQJSMAzLYBiSYXZQhq+6FAQ1+OPFchvitEE42uZFBX4qZ3go+VTA
        LWcEF200VDA7mIGZgQzo+9Pp6h3KCByp3YPYjkCEtftsEggVXNhL2Ua2ZIs/FZDi1IermHyfRgUtt8+j
        vCIY4jJ3BLZ4WQU+1UI4FznCIZ49YhvDlMmQAil+013BRLf5D1SXncXD4nA0Vl2AuNgdB1RuVCCUcZEv
        F9ObsA5TMA48j8e4NhVjb1KooKrwGErSD6NGfgINd0KxN0cE/2oRBGkcPFOEm0/+M0yBz6DqVkdjuP0y
        ulTRSJLsS2BeeZq/YLF4Ujs+J46tsouxUViizcNITpFtyAbFaYf8LPF/hsX6DURyOPDSOpEVAAAAAElF
        TkSuQmCC
</value>
  </data>
  <metadata name="openFileDialog.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
</root>