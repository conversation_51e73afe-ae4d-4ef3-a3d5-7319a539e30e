﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ReferrerEditorDialog.cs 1440 2014-10-03 04:25:17Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Shared;

namespace LabMaestro.Controls.Win
{
    public partial class ReferrerEditorDialog : XtraForm, IExecutableDialog
    {
        private Referrer _currentPhysician;
        private ReferrerAddress _selectedAddress;
        private ReferrerPhoneNumber _selectedPhoneNumber;

        public ReferrerEditorDialog(Referrer physician)
        {
            _currentPhysician = physician;
            InitializeComponent();
            WinUtils.ApplyTheme(this);
            WinUtils.SetFormTitle(this, "Edit Referrer");
        }

        public bool ExecuteDialog(IWin32Window parent)
        {
            UpdateControls();
            ShowDialog(parent);
            return true;
        }

        public void UpdateControls()
        {
            createGridColumns();
            resetControls();
            populateLookUpControls();
            if (hasCurrentPhysician())
            {
                Text = _currentPhysician.Name;
                updateControlsFromCurrentPhysician(true);
            }
            else
            {
                chkIsActive.Checked = true;
                chkSuppressNet.Checked = false;
                Text = "Add New Physician...";
            }
        }

        private void createGridColumns()
        {
            var i = 0;
            grdAddresses.GridAddColumn("addr", "Address", i++, 270);
            grdAddresses.GridAddColumn("type", "Type", i++, 60);
            grdAddresses.GridAddColumn("mail", "M?", i++, 30);
            grdAddresses.GridAddColumn("id", "Id", i++, 50);

            i = 0;
            grdPhones.GridAddColumn("num", "Number", i++, 220);
            grdPhones.GridAddColumn("type", "Type", i++, 70);
            grdPhones.GridAddColumn("id", "Id", i++, 60);
        }

        private bool hasCurrentPhysician()
        {
            return _currentPhysician != null;
        }

        private void updateControlsFromCurrentPhysician(bool updateAll)
        {
            if (hasCurrentPhysician())
            {
                lblId.Text = _currentPhysician.Id.ToString();
                lblLastModified.Text = SharedUtilities.DateTimeToString(_currentPhysician.LastUpdated);
                if (_currentPhysician.LastUpdatedByUserId != null)
                {
                    FaultHandler.Shield(() => lblLastModifiedBy.Text = UsersRepository.GetUserDisplayName(
                        (short) _currentPhysician.LastUpdatedByUserId));
                }

                txtPrefix.Text = _currentPhysician.Prefix;
                txtName.Text = _currentPhysician.Name;
                txtSuffix.Text = _currentPhysician.Suffix;
                txtTags.Text = _currentPhysician.IdentifyingTag;
                txtEmail.Text = _currentPhysician.Email;
                txtMobile.Text = _currentPhysician.MobilePhone;
                txtWebLoginId.Text = _currentPhysician.WebLoginId;
                txtWebLoginPass.Text = _currentPhysician.WebPassKey;
                chkSuppressNet.Checked = _currentPhysician.SuppressNetReferral;
                chkIsActive.Checked = _currentPhysician.IsActive;
                chkWebLogin.Checked = _currentPhysician.WebLoginEnabled;
                luRefClass.EditValue = _currentPhysician.ReferralClassId;

                List<ReferrerCategoryLink> links = null;
                FaultHandler.Shield(() => links = ReferrersRepository.GetCategoryLinksForReferrer(_currentPhysician.Id));
                lbCategories.BeginUpdate();
                try
                {
                    lbCategories.Items.Clear();
                    foreach (var categoryLink in links)
                    {
                        lbCategories.Items.Add(CategoryMembershipLinkInfo.AssembleFrom(categoryLink));
                    }
                }
                finally
                {
                    lbCategories.EndUpdate();
                }

                if (updateAll)
                {
                    populateGridControls();
                }
            }
        }

        private void populateGridControls()
        {
            List<ReferrerAddress> addresses = null;
            List<ReferrerPhoneNumber> phones = null;
            FaultHandler.Shield(() =>
                                {
                                    addresses = ReferrersRepository.GetAddressesForReferrer(_currentPhysician.Id);
                                    phones = ReferrersRepository.GetPhoneNumbersForReferrer(_currentPhysician.Id);
                                });

            grdAddresses.BeginInit();
            try
            {
                grdAddresses.DataRows.Clear();
                foreach (var item in addresses)
                {
                    var row = grdAddresses.DataRows.AddNew();
                    row.Cells["id"].Value = item.Id.ToString();
                    row.Cells["addr"].Value = item.Address;
                    row.Cells["type"].Value = item.AddressType == (byte) AddressType.Commercial ? "Comm" : "Res";
                    row.Cells["mail"].Value = SharedUtilities.BooleanToStringYN(item.IsMailingAddress);
                    row.Tag = item;
                    row.EndEdit();
                }
            }
            finally
            {
                grdAddresses.EndInit();
            }

            grdPhones.BeginInit();
            try
            {
                grdPhones.DataRows.Clear();
                foreach (var item in phones)
                {
                    var row = grdPhones.DataRows.AddNew();
                    row.Cells["id"].Value = item.Id.ToString();
                    row.Cells["num"].Value = item.PhoneNumber;
                    row.Cells["type"].Value = item.PhoneNumberType == (byte) AddressType.Commercial ? "Comm" : "Res";
                    row.Tag = item;
                    row.EndEdit();
                }
            }
            finally
            {
                grdPhones.EndInit();
            }
        }

        private void populateLookUpControls()
        {
            WaitFormControl.WaitOperation(
                this,
                () =>
                    FaultHandler.Shield(() =>
                                        {
                                            var categories = ReferrerCategoryRepository.FindAllItems()
                                                                                       .OrderBy(x => x.Name)
                                                                                       .ToList();
                                            WinUtils.PopulateLookupControl(luCategories,
                                                                           categories,
                                                                           "Name",
                                                                           "Id",
                                                                           new[] {"Name", "IsActive"});

                                            var refClasses = ReferralClassesRepository.FetchAll()
                                                                                      .OrderBy(x => x.Name)
                                                                                      .ToList();
                                            WinUtils.PopulateLookupControl(luRefClass,
                                                                           refClasses,
                                                                           "Name",
                                                                           "Id",
                                                                           new[] {"Name", "ReferralEligible"});
                                        }));
        }

        private void resetControls()
        {
            lblLastModified.Text = string.Empty;
            lblLastModifiedBy.Text = string.Empty;
            lblId.Text = string.Empty;
            resetAddressInputs();
            resetPhonesInputs();
        }

        private void resetPhonesInputs()
        {
            txtPhone.Text = string.Empty;
            txtPhoneNotes.Text = string.Empty;
            rgPhoneType.SelectedIndex = 0;

            btnNewPhone.Enabled = _currentPhysician != null;
            btnDeletePhone.Enabled = false;
            btnSavePhone.Enabled = false;
        }

        private void resetAddressInputs()
        {
            txtAddress.Text = string.Empty;
            txtAddressNotes.Text = string.Empty;
            rgAddressType.SelectedIndex = 0;
            chkAddressIsMail.Checked = false;

            btnNewAddress.Enabled = _currentPhysician != null;
            btnDeleteAddress.Enabled = false;
            btnSaveAddress.Enabled = false;
        }

        private bool validateGeneralInput()
        {
            if (!txtName.TextEditVerifyString(160))
            {
                MessageDlg.Error("Please enter a valid name (max: 160 characters)");
                return false;
            }

            if (luRefClass.EditValue == null)
            {
                MessageDlg.Error("Please select the Referral Class for the physician.");
                luRefClass.Select();
                return false;
            }

            if (lbCategories.Items.Count == 0)
            {
                MessageDlg.Warning(
                    "The physician doen't belong to any category.\nIf this is a newly added physician, please assign it to the appropriate category.");
                luCategories.Select();
                luCategories.Focus();
            }

            return true;
        }

        private void btnRefClassCatalog_Click(object sender, EventArgs e)
        {
            editClassCategoryCatalog(true);
        }

        private void editClassCategoryCatalog(bool editClass)
        {
            using (var frm = new ReferrerClassCategoryEditorDialog(editClass))
            {
                frm.UpdateControls();
                frm.ShowDialog();
            }

            populateLookUpControls();
        }

        private void btnCategoriesCatalog_Click(object sender, EventArgs e)
        {
            editClassCategoryCatalog(false);
        }

        private void btnCategoryAdd_Click(object sender, EventArgs e)
        {
            if (!hasCurrentPhysician())
            {
                MessageDlg.Error("No physician added!\nPlease save the physician before adding membership.");
                return;
            }

            if (luCategories.EditValue == null) return;

            var catId = (short) luCategories.EditValue;
            if (catId <= 0)
            {
                MessageDlg.Error(string.Format("Invalid Category Id ({0})!\nPlease select a valid category.", catId));
                return;
            }

            if (ReferrersRepository.GetReferrerCategoryLink(_currentPhysician.Id, catId) != null)
            {
                MessageDlg.Error("Referrer is already member of the category!");
                return;
            }

            WaitFormControl.WaitOperation(this,
                                          () =>
                                              FaultHandler.Shield(
                                                  () =>
                                                      ReferrersRepository.CreateReferrerCategoryLink(
                                                          _currentPhysician.Id,
                                                          catId)),
                                          "Saving data...");

            updateControlsFromCurrentPhysician(false);
        }

        private void btnCategoryDelete_Click(object sender, EventArgs e)
        {
            if (!hasCurrentPhysician())
            {
                MessageDlg.Error("No physician added!\nPlease save the physician before adding membership.");
                return;
            }

            if (lbCategories.SelectedIndex == -1)
            {
                MessageDlg.Error("No item selected!\nPlease select an item from the listbox.");
                return;
            }

            var item = lbCategories.SelectedValue as CategoryMembershipLinkInfo;
            if (item == null)
            {
                MessageDlg.Error("No item selected!\nPlease select an item from the listbox.");
                return;
            }

            WaitFormControl.WaitOperation(this,
                                          () =>
                                              FaultHandler.Shield(
                                                  () =>
                                                      ReferrersRepository.RemoveReferrerCategoryLink(
                                                          _currentPhysician.Id,
                                                          item.Id)),
                                          "Removing item...");

            updateControlsFromCurrentPhysician(false);
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            if (validateGeneralInput())
            {
                var name = txtName.Text.Trim();
                var webId = txtWebLoginId.Text.Trim().ToLowerInvariant();

                Referrer referrer = null;
                FaultHandler.Shield(() =>
                                    {
                                        if (_currentPhysician == null)
                                        {
                                            var id = ReferrersRepository.CreateNewReferrer(name, webId);
                                            referrer = ReferrersRepository.FindById(id);
                                        }
                                        else
                                        {
                                            referrer = ReferrersRepository.FindById(_currentPhysician.Id);
                                        }
                                    });

                WaitFormControl.WaitOperation(this,
                                              () => FaultHandler.Shield(
                                                  () =>
                                                      ReferrersRepository.UpdateReferrer(
                                                          referrer.Id,
                                                          chkIsActive.Checked,
                                                          txtPrefix.Text.Trim(),
                                                          name,
                                                          txtSuffix.Text.Trim(),
                                                          txtTags.Text.Trim(),
                                                          txtMobile.Text.Trim(),
                                                          txtEmail.Text.Trim().ToLowerInvariant(),
                                                          chkWebLogin.Checked,
                                                          webId,
                                                          txtWebLoginPass.Text.Trim(),
                                                          CurrentUserContext.UserId,
                                                          null,
                                                          (short?) luRefClass.EditValue,
                                                          chkSuppressNet.Checked)),
                                              "Saving data...");

                ReferrersRepository.Reset();
                _currentPhysician = ReferrersRepository.FindById(referrer.Id);
                /*

                _currentPhysician.Name = name;
                _currentPhysician.Prefix = txtPrefix.Text.Trim();
                _currentPhysician.Suffix = txtSuffix.Text.Trim();
                _currentPhysician.IdentifyingTag = txtTags.Text.Trim();
                _currentPhysician.MobilePhone = txtMobile.Text.Trim();
                _currentPhysician.Email = txtEmail.Text.Trim().ToLowerInvariant();
                _currentPhysician.IsActive = chkIsActive.Checked;
                _currentPhysician.ReferralClassId = (short?) luRefClass.EditValue;
                _currentPhysician.WebLoginEnabled = chkWebLogin.Checked;
                _currentPhysician.WebLoginId = txtWebLoginId.Text.Trim().ToLowerInvariant();
                _currentPhysician.WebPassKey = txtWebLoginPass.Text.Trim();
                _currentPhysician.LastUpdatedByUserId = CurrentUserContext.UserId;
                _currentPhysician.LastUpdated = AppSysRepository.GetServerTime();
                //if (_currentPhysician.EntityAspect.EntityState == EntityState.Detached)
                //    ReferrersRepository.Attach(_currentPhysician);
                WaitFormControl.WaitOperation(this,
                                              () => FaultHandler.Shield(ReferrersRepository.Save),
                                              "Saving data...");
                */
                updateControlsFromCurrentPhysician(true);
            }
        }

        private void btnGenerateAutoLoginId_Click(object sender, EventArgs e)
        {
            if (!string.IsNullOrEmpty(txtWebLoginId.Text.Trim()))
            {
                MessageDlg.Warning("Already has a login id!");
                return;
            }

            var name = txtName.Text.Trim().ToLowerInvariant();
            if (string.IsNullOrEmpty(name))
            {
                MessageDlg.Warning("Please enter the physicians name!");
                return;
            }

            var uid = generateUniqueLoginId(name, 6);
            var count = 1;
            FaultHandler.Shield(() =>
                                {
                                    while (!ReferrersRepository.WebLoginIdIsUnique(uid))
                                    {
                                        uid = generateUniqueLoginId(name, 8);
                                        count++;
                                        if (count > 20)
                                        {
                                            MessageDlg.Warning(
                                                "Could not generate unique login Id!\nPlease enter the login id manually.");
                                            return;
                                        }
                                    }
                                });

            txtWebLoginId.Text = uid;
            txtWebLoginPass.Text = SharedUtilities.RandomNumericString(4);
        }

        private static string generateUniqueLoginId(string input, int minLength)
        {
            if (string.IsNullOrEmpty(input)) return string.Empty;

            var parts = input.Split(new[]
                                    {
                                        ' ', ',', '.', ':', '\t',
                                        '-', '/', '\\', '(', ')', '[', ']'
                                    }, StringSplitOptions.RemoveEmptyEntries);

            var code = new StringBuilder();
            foreach (var part in parts)
            {
                var s = part.Trim();
                if (!string.IsNullOrEmpty(s))
                {
                    code.Append(s[0]);
                }
            }

            if (code.Length < minLength)
            {
                var remain = minLength - code.Length;
                code.Append(SharedUtilities.RandomNumericString(remain));
            }

            return code.ToString();
        }

        private void btnVerifyUniqueLoginId_Click(object sender, EventArgs e)
        {
            var uid = txtWebLoginId.Text.Trim().ToLowerInvariant();
            FaultHandler.Shield(() =>
                                {
                                    if (!ReferrersRepository.WebLoginIdIsUnique(uid))
                                    {
                                        MessageDlg.Warning(
                                            "Another login with the same ID already exists!\nPlease generate a new login ID.");
                                    }
                                });
        }

        private bool hasSelectedAddress()
        {
            if (grdAddresses.SelectedRows.Count == 1)
            {
                _selectedAddress = grdAddresses.SelectedRows[0].Tag as ReferrerAddress;
            }
            return _selectedAddress != null;
        }

        private bool hasSelectedPhone()
        {
            if (grdPhones.SelectedRows.Count == 1)
            {
                _selectedPhoneNumber = grdPhones.SelectedRows[0].Tag as ReferrerPhoneNumber;
            }
            return _selectedPhoneNumber != null;
        }

        private void grdAddresses_SelectedRowsChanged(object sender, EventArgs e)
        {
            if (hasSelectedAddress())
            {
                resetAddressInputs();
                txtAddress.Text = _selectedAddress.Address;
                txtAddressNotes.Text = _selectedAddress.Notes;
                rgAddressType.SelectedIndex = _selectedAddress.AddressType == (byte) AddressType.Commercial ? 0 : 1;
                chkAddressIsMail.Checked = _selectedAddress.IsMailingAddress;

                btnSaveAddress.Enabled = true;
                btnDeleteAddress.Enabled = true;
            }
        }

        private void grdPhones_SelectedRowsChanged(object sender, EventArgs e)
        {
            if (hasSelectedPhone())
            {
                resetPhonesInputs();

                txtPhone.Text = _selectedPhoneNumber.PhoneNumber;
                txtPhoneNotes.Text = _selectedPhoneNumber.Notes;
                rgPhoneType.SelectedIndex = _selectedPhoneNumber.PhoneNumberType == (byte) AddressType.Commercial
                    ? 0
                    : 1;

                btnSavePhone.Enabled = true;
                btnDeletePhone.Enabled = true;
            }
        }

        private void btnNewAddress_Click(object sender, EventArgs e)
        {
            grdAddresses.SelectedRows.Clear();
            _selectedAddress = null;
            resetAddressInputs();
            btnNewAddress.Enabled = false;
            btnSaveAddress.Enabled = true;
        }

        private void btnSaveAddress_Click(object sender, EventArgs e)
        {
            if (!hasCurrentPhysician())
            {
                MessageDlg.Error("No physician added!\nPlease save the physician before adding adresses.");
                return;
            }

            var address = txtAddress.Text.Trim();
            if (string.IsNullOrEmpty(address))
            {
                MessageDlg.Error("Please enter a valid address!");
                return;
            }
            var notes = txtAddressNotes.Text.Trim();
            var type = rgAddressType.SelectedIndex == 0 ? (byte) AddressType.Commercial : (byte) AddressType.Residential;
            var isMail = chkAddressIsMail.Checked;

            FaultHandler.Shield(() => ReferrersRepository.AddNewAddress(_currentPhysician.Id,
                                                                        address,
                                                                        isMail,
                                                                        type,
                                                                        notes));
            populateGridControls();
        }

        private void btnDeleteAddress_Click(object sender, EventArgs e)
        {
        }

        private void btnNewPhone_Click(object sender, EventArgs e)
        {
            grdPhones.SelectedRows.Clear();
            _selectedPhoneNumber = null;
            resetPhonesInputs();
            btnNewPhone.Enabled = false;
            btnSavePhone.Enabled = true;
        }

        private void btnSavePhone_Click(object sender, EventArgs e)
        {
            if (!hasCurrentPhysician())
            {
                MessageDlg.Error("No physician added!\nPlease save the physician before adding phone number.");
                return;
            }

            var number = txtPhone.Text.Trim();
            if (string.IsNullOrEmpty(number))
            {
                MessageDlg.Error("Please enter a valid phone number!");
                return;
            }
            var notes = txtPhoneNotes.Text.Trim();
            var type = rgPhoneType.SelectedIndex == 0 ? (byte) AddressType.Commercial : (byte) AddressType.Residential;

            FaultHandler.Shield(() => ReferrersRepository.AddNewPhoneNumber(_currentPhysician.Id, number, type, notes));
            populateGridControls();
        }

        private void btnDeletePhone_Click(object sender, EventArgs e)
        {
        }

        private void OnTextEditKeyUp(object sender, KeyEventArgs e)
        {
            var textEdit = sender as TextEdit;
            if (textEdit != null)
            {
                if (e.KeyCode == Keys.F5)
                {
                    var text = SharedUtilities.TitleCase(textEdit.Text.Trim());
                    textEdit.Text = text;
                }
            }
        }

        private void txtEmail_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.F5)
            {
                txtEmail.Text = txtEmail.Text.Trim().ToLowerInvariant();
            }
        }

        internal sealed class CategoryMembershipLinkInfo
        {
            public int Id { get; private set; }
            //public short CatId { get; private set; }
            public string Name { get; private set; }

            internal static CategoryMembershipLinkInfo AssembleFrom(ReferrerCategoryLink link)
            {
                return new CategoryMembershipLinkInfo
                       {
                           //CatId = link.CatergoryId,
                           Name = link.ReferrerCategory.Name,
                           Id = link.Id
                       };
            }

            public override string ToString()
            {
                return string.Format("{0} ({1})", Name, Id);
            }
        }
    }
}