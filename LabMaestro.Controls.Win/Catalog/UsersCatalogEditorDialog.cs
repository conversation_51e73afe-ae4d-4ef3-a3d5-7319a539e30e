﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: UsersCatalogEditorDialog.cs 872 2013-07-25 10:26:13Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraBars;
using DevExpress.XtraEditors;
using LabMaestro.Domain;
using LabMaestro.Shared;

namespace LabMaestro.Controls.Win
{
    public partial class UsersCatalogEditorDialog : XtraForm
    {
        private List<User> _filteredUsersList;

        public UsersCatalogEditorDialog()
        {
            InitializeComponent();
            WinUtils.ApplyTheme(this);
            WinUtils.SetFormTitle(this, "Users");
        }

        private void gridCreateColumns()
        {
            var i = 0;
            grdRoles.Columns.Clear();
            grdRoles.GridAddColumn("id", "Id", i++, 30);
            grdRoles.GridAddColumn("name", "Name", i++, 120);
            grdRoles.GridAddColumn("code", "Code", i++, 60);
            grdRoles.GridAddColumn("act", "Y?", i++, 30);
            grdRoles.GridAddColumn("adm", "Adm?", i++, 45);

            i = 0;
            grdDepartments.Columns.Clear();
            grdDepartments.GridAddColumn("name", "Name", i++, 180);

            i = 0;
            grdUsers.Columns.Clear();
            grdUsers.GridAddColumn("id", "Id", i++, 40);
            grdUsers.GridAddColumn("dispname", "Display Name", i++, 160);
            grdUsers.GridAddColumn("username", "Username", i++, 100);
            grdUsers.GridAddColumn("fname", "First Name", i++, 140);
            grdUsers.GridAddColumn("lname", "Last Name", i++, 100);
            grdUsers.GridAddColumn("act", "Y?", i++, 30);
            grdUsers.GridAddColumn("login", "Last Login", i++, 100);
        }

        private void UsersCatalogEditorDialog_Load(object sender, EventArgs e)
        {
            gridCreateColumns();
            populateLeftGrids();
        }

        private void btnExit_ItemClick(object sender, ItemClickEventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        private void btnNewUser_ItemClick(object sender, ItemClickEventArgs e)
        {
            showUserEditorDialog(null);
        }

        private void showUserEditorDialog(User user)
        {
            using (var frm = new UserEditorDialog(user))
            {
                frm.ShowDialog();
            }

            filterUsersList();
        }

        private void populateLeftGrids()
        {
            var roles = RolesRepository.FindAllRoles(false);
            grdRoles.BeginInit();
            try
            {
                grdRoles.DataRows.Clear();

                var headerRow = grdRoles.DataRows.AddNew();
                headerRow.Cells["name"].Value = "*";
                headerRow.Tag = (short) -1;
                headerRow.EndEdit();

                foreach (var role in roles)
                {
                    var row = grdRoles.DataRows.AddNew();
                    row.Cells["id"].Value = role.Id.ToString();
                    row.Cells["name"].Value = role.Name;
                    row.Cells["code"].Value = role.RoleCode;
                    row.Cells["act"].Value = SharedUtilities.BooleanToStringYN(role.IsActive);
                    row.Cells["adm"].Value = SharedUtilities.BooleanToStringYN(role.IsAdmin);
                    row.Tag = role.Id;
                    row.EndEdit();
                }
            }
            finally
            {
                grdRoles.EndInit();
            }

            // TODO: Departments grid
        }

        private List<User> getCuratedUsersList()
        {
            if (tabFilter.SelectedIndex == 0)
                return _filteredUsersList
                    .OrderBy(u => u.DisplayName)
                    .ToList();

            var filterChar = Convert.ToChar(tabFilter.SelectedIndex + 64);
            return _filteredUsersList
                .Where(x => x.DisplayName[0] == filterChar)
                .OrderBy(x => x.DisplayName)
                .ToList();
        }

        private void populateUsersGrid()
        {
            grdUsers.BeginInit();
            try
            {
                grdUsers.DataRows.Clear();

                foreach (var user in getCuratedUsersList())
                {
                    var row = grdUsers.DataRows.AddNew();
                    row.Cells["id"].Value = user.Id.ToString();
                    row.Cells["dispname"].Value = user.DisplayName;
                    row.Cells["username"].Value = user.UserName;
                    row.Cells["fname"].Value = user.FirstName;
                    row.Cells["lname"].Value = user.LastName;
                    row.Cells["act"].Value = SharedUtilities.BooleanToStringYN(user.IsActive);
                    if (user.LastLogin != null)
                        row.Cells["login"].Value = SharedUtilities.DateTimeToShortString((DateTime) user.LastLogin);
                    row.Tag = user.Id;
                    row.Height = 23;
                    row.EndEdit();
                }
            }
            finally
            {
                grdUsers.EndInit();
            }
        }

        private short getSelectedRoleId()
        {
            if (grdRoles.SelectedRows != null && grdRoles.SelectedRows.Count == 1)
            {
                return (short) grdRoles.SelectedRows[0].Tag;
            }

            return -1;
        }

        private short getSelectedUserId()
        {
            if (grdUsers.SelectedRows != null && grdUsers.SelectedRows.Count == 1)
            {
                return (short) grdUsers.SelectedRows[0].Tag;
            }

            return -1;
        }

        private void grdRoles_SelectedRowsChanged(object sender, EventArgs e)
        {
            filterUsersList();
        }

        private void filterUsersList()
        {
            var roleId = getSelectedRoleId();
            WaitFormControl.WaitOperation(this, () =>
                                                    {
                                                        _filteredUsersList = roleId < 0
                                                                                 ? UsersRepository.FindAllUsers()
                                                                                 : UsersRepository.FindAllUsersInRole(
                                                                                     roleId);
                                                    }, "Loading users...");
            populateUsersGrid();
        }

        private void grdUsers_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.F2)
            {
                var uid = getSelectedUserId();
                if (uid > 0)
                {
                    var user = UsersRepository.FindConsultantById(uid);
                    showUserEditorDialog(user);
                }
            }
        }

        private void tabFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            populateUsersGrid();
        }
    }
}