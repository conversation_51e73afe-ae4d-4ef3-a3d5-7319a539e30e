﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: UserEditorDialog.cs 847 2013-07-22 07:17:00Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Drawing;
using System.IO;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Shared;

namespace LabMaestro.Controls.Win
{
    public partial class UserEditorDialog : XtraForm
    {
        private MemoryStream _sigImageBytes;

        public UserEditorDialog(User user)
        {
            InitializeComponent();
            CurrentUser = user;
            var caption = string.Format("{0} User", user == null ? "New" : "Edit");
            WinUtils.SetFormTitle(this, caption);
            WinUtils.ApplyTheme(this);
        }

        public User CurrentUser { get; private set; }

        private void resetControls()
        {
            lblCreated.Text = string.Empty;
            lblLastLogin.Text = string.Empty;
            lblLastModified.Text = string.Empty;
            lblSigDimensions.Text = string.Empty;
            lblUserId.Text = string.Empty;

            txtDisplayName.Text = string.Empty;
            txtFirstname.Text = string.Empty;
            txtLastname.Text = string.Empty;
            txtPassword.Text = string.Empty;
            txtPasswordRepeat.Text = string.Empty;
            txtSuffix.Text = string.Empty;
            cboTitle.Text = string.Empty;

            chkIsActive.Checked = true;
            chkChangePassword.Checked = false;

            picSignature.Image = null;
            reBio.ResetText();
        }

        private void UserEditorDialog_Load(object sender, EventArgs e)
        {
            resetControls();
            populateLookUpControls();
            populateControlsFromUser();
        }

        private void populateLookUpControls()
        {
            var roles = FaultHandler.Shield(() => RolesRepository.FindAllRoles(false));
            WinUtils.PopulateLookupControl(luRole, roles, "Name", "Id",
                new[] {"Name", "RoleCode", "IsActive", "IsActive"});
        }

        private void populateControlsFromUser()
        {
            if (CurrentUser != null)
            {
                if (CurrentUser.DateCreated != null)
                    lblCreated.Text = SharedUtilities.DateTimeToString24((DateTime) CurrentUser.DateCreated);
                if (CurrentUser.LastLogin != null)
                    lblLastLogin.Text = SharedUtilities.DateTimeToString24((DateTime) CurrentUser.LastLogin);
                if (CurrentUser.LastModified != null)
                    lblLastModified.Text = SharedUtilities.DateTimeToString24((DateTime) CurrentUser.LastModified);
                lblUserId.Text = CurrentUser.Id.ToString();

                txtUsername.Text = CurrentUser.UserName;
                txtDisplayName.Text = CurrentUser.DisplayName;
                txtFirstname.Text = CurrentUser.FirstName;
                txtLastname.Text = CurrentUser.LastName;
                txtSuffix.Text = CurrentUser.Suffix;
                cboTitle.Text = CurrentUser.Title;

                chkIsActive.Checked = CurrentUser.IsActive;
                chkChangePassword.Checked = CurrentUser.MustChangePassword;

                luRole.EditValue = CurrentUser.RoleId;
                //luDepartment.EditValue = CurrentUser.DepartmentId;

                if (CurrentUser.Consultant != null)
                {
                    if (CurrentUser.Consultant.SignatureImage != null)
                    {
                        _sigImageBytes = new MemoryStream();
                        var imgBytes = CurrentUser.Consultant.SignatureImage;
                        var length = imgBytes.Length;
                        _sigImageBytes.Write(imgBytes, 0, length);
                    }

                    sigPicBoxLoad();

                    reBio.RtfText = CurrentUser.Consultant.SignatureText;
                }
            }
        }

        private void btnSigClear_Click(object sender, EventArgs e)
        {
            if (MessageDlg.Confirm("Really clear?"))
            {
                sigPicBoxReset(true);
            }
        }

        private void sigPicBoxReset(bool resetMemory)
        {
            if (resetMemory)
            {
                _sigImageBytes = null;
            }
            picSignature.Image = null;
            picSignature.Refresh();
            lblSigDimensions.Text = string.Empty;
        }

        private void btnSigLoad_Click(object sender, EventArgs e)
        {
            openFileDialog.Filter = "Image File (*.jpg;*.bmp;*.gif;*.png)|*.jpg;*.bmp;*.gif;*.png";
            if (openFileDialog.ShowDialog(this) == DialogResult.OK)
            {
                using (var file = new FileStream(openFileDialog.FileName, FileMode.Open, FileAccess.Read))
                {
                    var bytes = new byte[file.Length];
                    file.Read(bytes, 0, (int) file.Length);
                    _sigImageBytes = new MemoryStream();
                    _sigImageBytes.Write(bytes, 0, (int) file.Length);
                    _sigImageBytes.Seek(0, SeekOrigin.Begin);
                }

                sigPicBoxLoad();
            }
        }

        private void sigPicBoxLoad()
        {
            sigPicBoxReset(false);
            if (_sigImageBytes == null || _sigImageBytes.Length == 0) return;

            var newImage = new Bitmap(_sigImageBytes);
            picSignature.Image = newImage;
            picSignature.Refresh();

            lblSigDimensions.Text = string.Format("({0} x {1})", newImage.Width, newImage.Height);
        }

        private void btnSigSave_Click(object sender, EventArgs e)
        {
            saveSignatureImageText();
        }

        private void saveSignatureImageText()
        {
            if (CurrentUser == null)
            {
                MessageDlg.Warning("No existing user.\nPlease save the user first.");
                return;
            }

            byte[] imgBytes = _sigImageBytes.Length > 0 ? _sigImageBytes.ToArray() : null;
            var sigText = reBio.RtfText.Trim();

            if (MessageDlg.Confirm("Really save?"))
            {
                FaultHandler.Shield(() => WaitFormControl.WaitOperation(this, () =>
                {
                    UsersRepository.UpsertConsultantSignature(CurrentUser.Id,
                        imgBytes,
                        sigText);
                    UsersRepository.Reset();
                }, "Saving..."));
            }
        }

        private void btnBioClear_Click(object sender, EventArgs e)
        {
            if (MessageDlg.Confirm("Really clear?"))
            {
                reBio.ResetText();
            }
        }

        private void btnBioLoad_Click(object sender, EventArgs e)
        {
            reBio.LoadDocument(this);
        }

        private void btnBioSave_Click(object sender, EventArgs e)
        {
            saveSignatureImageText();
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            var isNewUser = (CurrentUser == null);
            if (!validateInputs(isNewUser)) return;
            if (isNewUser)
            {
                CurrentUser = FaultHandler.Shield(() =>
                    UsersRepository.CreateUser(getUsername(), getPassHash(), txtDisplayName.Text.Trim()));
            }

            CurrentUser.UserName = getUsername();
            CurrentUser.PassHash = getPassHash();
            CurrentUser.DisplayName = txtDisplayName.Text.Trim();
            CurrentUser.FirstName = txtFirstname.Text.Trim();
            CurrentUser.LastName = txtLastname.Text.Trim();
            CurrentUser.Title = cboTitle.Text.Trim();
            CurrentUser.Suffix = txtSuffix.Text.Trim();

            //TODO: Department
            CurrentUser.RoleId = (short?) luRole.EditValue;
            CurrentUser.IsActive = chkIsActive.Checked;
            CurrentUser.MustChangePassword = chkChangePassword.Checked;

            FaultHandler.Shield(() =>
            {
                UsersRepository.Save();
                MessageDlg.Info("User saved to database.");
            });
        }

        private string getUsername()
        {
            return txtUsername.Text.Trim().ToLower();
        }

        private string getPassHash()
        {
            return AuthUtils.EncodePassword(txtPassword.Text.Trim());
        }

        private bool validateInputs(bool checkUsernameUniqueness)
        {
            var text = getUsername();
            if (!SharedUtilities.StringWithinLimit(text, 4, 20))
            {
                MessageDlg.Error("Username must be between 4 to 20 alphabets in length!");
                txtUsername.Select();
                txtUsername.Focus();
                return false;
            }

            //TODO: validate username regex

            if (!AuthHelper.IsValidUsername(text))
            {
                MessageDlg.Error("Username doesn't conform to convention!");
                txtUsername.Select();
                txtUsername.Focus();
                return false;
            }

            if (checkUsernameUniqueness)
            {
                if (!checkUsernameIsUnique()) return false;
            }

            text = txtPassword.Text.Trim();

            if (string.Compare(text, txtPasswordRepeat.Text.Trim()) != 0)
            {
                MessageDlg.Error("Password and Password confirmation fields do not match!");
                txtPassword.Select();
                txtPassword.Focus();
                return false;
            }

            if (!SharedUtilities.StringWithinLimit(text, 4, 20))
            {
                MessageDlg.Error("Password must be between 4 to 20 alphabets in length!");
                txtPassword.Select();
                txtPassword.Focus();
                return false;
            }

            if (!AuthHelper.IsValidPassword(text))
            {
                MessageDlg.Error("Password doesn't conform to convention!");
                txtPassword.Select();
                txtPassword.Focus();
                return false;
            }

            text = txtDisplayName.Text.Trim();
            if (!SharedUtilities.StringWithinLimit(text, 2, 40))
            {
                MessageDlg.Error("Display name must be between 2 to 40 alphabets in length!");
                txtDisplayName.Select();
                txtDisplayName.Focus();
                return false;
            }

            text = txtFirstname.Text.Trim();
            if (!SharedUtilities.StringWithinLimit(text, 2, 80))
            {
                MessageDlg.Error("First name must be between 2 to 80 alphabets in length!");
                txtFirstname.Select();
                txtFirstname.Focus();
                return false;
            }

            return true;
        }

        private bool checkUsernameIsUnique()
        {
            var user = FaultHandler.Shield(() => UsersRepository.FindByUserName(getUsername()));
            if (user != null)
            {
                MessageDlg.Warning("Username already taken!");
                return false;
            }
            return true;
        }

        private void btnUsernameCheckUnique_Click(object sender, EventArgs e)
        {
            checkUsernameIsUnique();
        }
    }
}