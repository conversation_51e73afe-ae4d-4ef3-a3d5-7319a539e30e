﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: TestEditDialog.cs 1256 2014-05-18 15:51:13Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Shared;

namespace LabMaestro.Controls.Win
{
    public partial class TestEditDialog : XtraForm
    {
        private const string HTML_TPL = @"<{0}>{1}</{0}>";
        private TestBillableItemLinkSlice _currentBISlice;
        private DiscreteReportLineItemSlice _currentDiscreteSlice;
        private ReqParameterSlice _currentReqSlice;

        public TestEditDialog()
        {
            InitializeComponent();
            WinUtils.ApplyTheme(this);
            createGridColumns();
        }

        public LabTestSlice CurrentLabTest { get; set; }

        private void createGridColumns()
        {
            var i = 0;
            grdReqParams.GridAddColumn("id", "Id", i++, 50);
            grdReqParams.GridAddColumn("param", "Parameter", i++, 290);
            grdReqParams.GridAddColumn("sort", "Sort", i++, 60);
            grdReqParams.GridAddColumn("indent", "Indent", i++, 60);

            i = 0;
            //grdDiscreteReportItems.GridAddColumn("id", "Id", i++, 40);
            grdDiscreteReportItems.GridAddColumn("param", "Parameter", i++, 200);
            grdDiscreteReportItems.GridAddColumn("units", "Units", i++, 100);
            grdDiscreteReportItems.GridAddColumn("refRange", "Ref. Range", i++, 160);

            i = 0;
            grdBillableItems.GridAddColumn("name", "Name", i++, 210);
            grdBillableItems.GridAddColumn("optLevel", "Optimization", i++, 120);
            grdBillableItems.GridAddColumn("qty", "Quantity", i++, 80);
            grdBillableItems.GridAddColumn("price", "Price", i++, 80);
        }

        public void UpdateControls()
        {
            resetControls();
            populateLookUpControls();
            if (CurrentLabTest != null)
            {
                Text = "Edit - " + CurrentLabTest.ShortName;
                updateControlsFromCurrentLabTest();
            }
            else
            {
                chkIsActive.Checked = true;
                Text = "Add New Test...";
            }
        }

        private void updateControlsFromCurrentLabTest()
        {
            if (CurrentLabTest == null) return;

            // General page
            lblTestId.Text = SharedUtilities.IntToStringPositive(CurrentLabTest.Id);
            lblCreatedDate.Text = SharedUtilities.DateToStringLong(CurrentLabTest.DateCreated);
            lblLastModifiedDate.Text = SharedUtilities.DateToStringLong(CurrentLabTest.LastModified);
            chkIsActive.Checked = CurrentLabTest.IsActive;
            if (!CurrentLabTest.InactiveDate.IsEmpty)
            {
                dteInactiveDate.DateTime = CurrentLabTest.InactiveDate.Date;
            }

            txtTestCode.Text = CurrentLabTest.TestSKU;
            txtShortName.Text = CurrentLabTest.ShortName;
            txtCanonName.Text = CurrentLabTest.CanonicalName;
            txtListPrice.Text = SharedUtilities.MoneyToStringPlain(CurrentLabTest.ListPrice);
            txtSubOrderPrice.Text = SharedUtilities.MoneyToStringPlain(CurrentLabTest.SubOrderPrice);
            txtCostBasis.Text = SharedUtilities.MoneyToStringPlain(CurrentLabTest.CostBasis);
            txtMnemonics.Text = CurrentLabTest.Mnemonics;
            txtReportLineGroupTag.Text = CurrentLabTest.ReportLineGroupingTag;

            reqSlipSetItem(CurrentLabTest.ReqSlipPrintOption);
            testSortPrioritySetItem(CurrentLabTest.ReportSortPriority);

            setLookupEditValue(luPerformingLab, CurrentLabTest.PerformingLabId);
            setLookupEditValue(luResultingLab, CurrentLabTest.ResultingLabId);
            setLookupEditValue(luTatGroup, CurrentLabTest.TATGroupId);
            setLookupEditValue(luTemplateGroup, CurrentLabTest.TemplateGroupId);
            setLookupEditValue(luDefaultTemplate, CurrentLabTest.DefaultTemplateId);

            // Grids
            grdBillableItemsPopulate(CurrentLabTest.BillableItemLinks);
            grdReqParamsPopulate(CurrentLabTest.ReqParameterSlices);
            grdDiscreteReportItemsPopulate(CurrentLabTest.DiscreteReportLineItemSlices);
        }

        private void grdDiscreteReportItemsPopulate(List<DiscreteReportLineItemSlice> items)
        {
            grdDiscreteReportItems.BeginInit();
            try
            {
                grdDiscreteReportItems.DataRows.Clear();
                foreach (var slice in items)
                {
                    var row = grdDiscreteReportItems.DataRows.AddNew();
                    //row.Cells["id"].Value = slice.Id.ToString(CultureInfo.InvariantCulture);
                    row.Cells["param"].Value = slice.Parameter;
                    row.Cells["units"].Value = slice.Units;
                    row.Cells["refRange"].Value = slice.ReferenceRange;
                    row.Height = 25;
                    row.Tag = slice;
                    row.EndEdit();
                }
            }
            finally
            {
                grdDiscreteReportItems.EndInit();
            }
        }

        private void grdBillableItemsPopulate(List<TestBillableItemLinkSlice> items)
        {
            grdBillableItems.BeginInit();
            try
            {
                grdBillableItems.DataRows.Clear();
                foreach (var slice in items)
                {
                    var row = grdBillableItems.DataRows.AddNew();
                    row.Cells["name"].Value = slice.BillableItem.Name;
                    row.Cells["qty"].Value = slice.Quantity.ToString(CultureInfo.InvariantCulture);
                    row.Cells["optLevel"].Value = EnumUtils.EnumDescription(slice.OptimizationLevel);
                    row.Cells["price"].Value = SharedUtilities.MoneyToStringPlain(slice.BillableItem.UnitPrice);
                    row.Height = 25;
                    row.Tag = slice;
                    row.EndEdit();
                }
            }
            finally
            {
                grdBillableItems.EndInit();
            }
        }

        private void grdReqParamsPopulate(List<ReqParameterSlice> items)
        {
            grdReqParams.BeginInit();
            try
            {
                grdReqParams.DataRows.Clear();
                foreach (var slice in items)
                {
                    var row = grdReqParams.DataRows.AddNew();
                    row.Cells["id"].Value = slice.Id.ToString(CultureInfo.InvariantCulture);
                    row.Cells["param"].Value = slice.Parameter;
                    row.Cells["sort"].Value = slice.SortOrder.ToString();
                    row.Cells["indent"].Value = slice.IndentLevel.ToString();
                    row.Height = 25;
                    row.Tag = slice;
                    row.EndEdit();
                }
            }
            finally
            {
                grdReqParams.EndInit();
            }
        }

        private void setLookupEditValue(LookUpEdit control, short value)
        {
            if (value > 0)
            {
                control.EditValue = value;
            }
        }

        private void reqSlipSetItem(ReqSlipGenerationOptionType opt)
        {
            switch (opt)
            {
                case ReqSlipGenerationOptionType.DoNotGenerate:
                    rgReqSlip.SelectedIndex = 0;
                    break;
                case ReqSlipGenerationOptionType.GenerateSharedSlips:
                    rgReqSlip.SelectedIndex = 1;
                    break;
                case ReqSlipGenerationOptionType.GenerateExclusiveSlip:
                    rgReqSlip.SelectedIndex = 2;
                    break;
            }
        }

        private ReqSlipGenerationOptionType reqSlipGetItem()
        {
            switch (rgReqSlip.SelectedIndex)
            {
                case 0:
                    return ReqSlipGenerationOptionType.DoNotGenerate;
                case 2:
                    return ReqSlipGenerationOptionType.GenerateExclusiveSlip;
                default:
                    return ReqSlipGenerationOptionType.GenerateSharedSlips;
            }
        }

        private void testSortPrioritySetItem(SortPriorityType pri)
        {
            switch (pri)
            {
                case SortPriorityType.Lowest:
                    rgTestSortPriority.SelectedIndex = 0;
                    break;
                case SortPriorityType.Low:
                    rgTestSortPriority.SelectedIndex = 1;
                    break;
                case SortPriorityType.Normal:
                    rgTestSortPriority.SelectedIndex = 2;
                    break;
                case SortPriorityType.High:
                    rgTestSortPriority.SelectedIndex = 3;
                    break;
                case SortPriorityType.Critical:
                    rgTestSortPriority.SelectedIndex = 4;
                    break;
            }
        }

        private SortPriorityType testSortPriorityGetItem()
        {
            switch (rgTestSortPriority.SelectedIndex)
            {
                case 0:
                    return SortPriorityType.Lowest;
                case 1:
                    return SortPriorityType.Low;
                case 3:
                    return SortPriorityType.High;
                case 4:
                    return SortPriorityType.Critical;
                default:
                    return SortPriorityType.Normal;
            }
        }

        private void biOptimizationLevelSetItem(BillableItemOptimizationLevelType opt)
        {
            switch (opt)
            {
                case BillableItemOptimizationLevelType.DoNotOptimize:
                    rgBIOptimize.SelectedIndex = 0;
                    break;
                case BillableItemOptimizationLevelType.OptimizePerReq:
                    rgBIOptimize.SelectedIndex = 1;
                    break;
                case BillableItemOptimizationLevelType.OptimizeGlobally:
                    rgBIOptimize.SelectedIndex = 2;
                    break;
            }
        }

        private BillableItemOptimizationLevelType biOptimizationLevelGetItem()
        {
            switch (rgBIOptimize.SelectedIndex)
            {
                case 0:
                    return BillableItemOptimizationLevelType.DoNotOptimize;
                case 1:
                    return BillableItemOptimizationLevelType.OptimizePerReq;
                case 2:
                    return BillableItemOptimizationLevelType.OptimizeGlobally;
            }
            return BillableItemOptimizationLevelType.OptimizePerReq;
        }

        private void populateLookUpControls()
        {
            var labs = LabsRepository.GetAllLabSlices().OrderBy(x => x.Name).ToList();
            WinUtils.PopulateLookupControl(luPerformingLab, labs, "Name", "Id", new[] {"Name"});
            WinUtils.PopulateLookupControl(luResultingLab, labs, "Name", "Id", new[] {"Name"});

            var bitems = BillableItemsRepository.GetAllOrderableBillableItems().OrderBy(x => x.Name)
                                                .Select(
                                                    x => new
                                                         {
                                                             x.Id,
                                                             x.Name,
                                                             UnitPrice = SharedUtilities.MoneyToString(x.UnitPrice)
                                                         }).ToList();
            WinUtils.PopulateLookupControl(luBillableItem, bitems, "Name", "Id", new[] {"Name", "UnitPrice"});

            var tatGroups =
                TATGroupsRepository.GetAllTATGroups()
                                   .OrderByDescending(x => x.TATRank)
                                   .ThenBy(x => x.Name)
                                   .Select(
                                       x =>
                                           new
                                           {
                                               x.Id,
                                               x.Name,
                                               TATRank = EnumUtils.EnumDescription((TATRankingType) x.TATRank),
                                               x.HoursRequired,
                                               x.DaysRequired
                                           }).ToList();
            WinUtils.PopulateLookupControl(luTatGroup, tatGroups, "Name", "Id",
                                           new[] {"Name", "TATRank", "HoursRequired", "DaysRequired", "Id"});

            updateTemplateGroupsLookUpControl();
        }

        private void updateTemplateGroupsLookUpControl()
        {
            IOrderedEnumerable<TemplateGroupSlice> tplGroups = null;
            var labId = getSelectedResultingLabId();

            WaitFormControl.WaitOperation(this, () =>
            {
                if (labId > 0)
                {
                    tplGroups = TemplateGroupsRepository.GetAllActiveTemplateGroupsForLab(labId)
                                                        .OrderBy(x => x.Name);
                }

                if (tplGroups == null)
                {
                    tplGroups = TemplateGroupsRepository.GetAllActiveTemplateGroups().OrderBy(x => x.Name);
                }
            });

            WinUtils.PopulateLookupControl(luTemplateGroup, tplGroups, "Name", "Id", new[] {"Name", "Id"});
        }

        private void updateTemplateReportsLookUpControl()
        {
            var groupId = getSelectedTemplateGroupId();
            IOrderedEnumerable<TemplateReportSlice> templates = null;
            if (groupId != null && (short) groupId > 0)
            {
                WaitFormControl.WaitOperation(this,
                                              () =>
                                              {
                                                  templates = TemplateReportsRepository
                                                      .GetAllActiveTemplateReportsInGroup((short) groupId)
                                                      .OrderBy(x => x.Name);
                                              });
            }
            WinUtils.PopulateLookupControl(luDefaultTemplate, templates, "Name", "Id", new[] {"Name", "Id"});
        }

        private void resetControls()
        {
            lblCreatedDate.Text = string.Empty;
            lblLastModifiedDate.Text = string.Empty;
            lblTestId.Text = string.Empty;

            resetBillableItemEditorControls();
            resetDiscreteEditorControls();
            resetReqEditorControls();
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        private DiscreteReportLineItemSlice grdGetDiscreteReportItem()
        {
            return grdDiscreteReportItems.SelectedRows != null && grdDiscreteReportItems.SelectedRows.Count == 1
                ? grdDiscreteReportItems.SelectedRows[0].Tag as DiscreteReportLineItemSlice
                : null;
        }

        private TestBillableItemLinkSlice grdGetBillableItemLink()
        {
            return grdBillableItems.SelectedRows != null && grdBillableItems.SelectedRows.Count == 1
                ? grdBillableItems.SelectedRows[0].Tag as TestBillableItemLinkSlice
                : null;
        }

        private ReqParameterSlice grdGetReqParam()
        {
            return grdReqParams.SelectedRows != null && grdReqParams.SelectedRows.Count == 1
                ? grdReqParams.SelectedRows[0].Tag as ReqParameterSlice
                : null;
        }

        private void grdDiscreteReportItems_SelectedRowsChanged(object sender, EventArgs e)
        {
            populateDiscreteEditorControls(grdGetDiscreteReportItem());
        }

        private static string[] stringSplitMultiLine(string input)
        {
            return string.IsNullOrEmpty(input) ? new string[] {} : input.Split(new[] {'|'}, StringSplitOptions.None);
        }

        private static string stringCombineMultiLine(string[] lines)
        {
            return lines == null ? string.Empty : (lines.Length == 1 ? lines[0].Trim() : string.Join("|", lines).Trim());
        }

        private void populateDiscreteEditorControls(DiscreteReportLineItemSlice slice)
        {
            resetDiscreteEditorControls();
            if (slice != null)
            {
                lblDiscreteId.Text = slice.Id.ToString();
                txtDiscreteParameter.Text = slice.Parameter;
                txtDiscreteRefRange.Lines = stringSplitMultiLine(slice.ReferenceRange);
                txtDiscreteUnits.Text = slice.Units;
                txtDefaultResult.Text = slice.DefaultResult;
                lblDiscreteSort.Text = slice.SortOrder.ToString();
                txtDiscreteIndent.Text = slice.IndentLevel.ToString();
                chkDiscreteResultable.Checked = slice.IsResultableItem;

                btnDiscreteDelete.Enabled = true;
                btnDiscreteSave.Enabled = true;
                _currentDiscreteSlice = slice;
            }
        }

        private void resetDiscreteEditorControls()
        {
            lblDiscreteId.Text = string.Empty;
            txtDiscreteParameter.Text = string.Empty;
            txtDiscreteRefRange.Text = string.Empty;
            txtDiscreteUnits.Text = string.Empty;
            txtDefaultResult.Text = string.Empty;
            lblDiscreteSort.Text = string.Empty;
            txtDiscreteIndent.Text = string.Empty;
            chkDiscreteResultable.Checked = false;

            btnDiscreteNew.Enabled = true;
            btnDiscreteAdd.Enabled = false;
            btnDiscreteDelete.Enabled = false;
            btnDiscreteSave.Enabled = false;
        }

        private void grdReqParams_SelectedRowsChanged(object sender, EventArgs e)
        {
            populateReqEditorControls(grdGetReqParam());
        }

        private void populateReqEditorControls(ReqParameterSlice slice)
        {
            resetReqEditorControls();
            if (slice != null)
            {
                lblReqId.Text = slice.Id.ToString();
                txtReqParameter.Text = slice.Parameter;
                lblReqSort.Text = slice.SortOrder.ToString();
                txtReqIndent.Text = slice.IndentLevel.ToString();

                btnReqDelete.Enabled = true;
                btnReqSave.Enabled = true;

                _currentReqSlice = slice;
            }
        }

        private void resetReqEditorControls()
        {
            lblReqId.Text = string.Empty;
            txtReqParameter.Text = string.Empty;
            lblReqSort.Text = string.Empty;
            txtReqIndent.Text = string.Empty;

            btnReqNew.Enabled = true;
            btnReqAdd.Enabled = false;
            btnReqDelete.Enabled = false;
            btnReqSave.Enabled = false;
        }

        private void grdBillableItems_SelectedRowsChanged(object sender, EventArgs e)
        {
            populateBillableItemEditorControls(grdGetBillableItemLink());
        }

        private void populateBillableItemEditorControls(TestBillableItemLinkSlice slice)
        {
            resetBillableItemEditorControls();

            if (slice != null)
            {
                _currentBISlice = slice;

                luBillableItem.EditValue = _currentBISlice.BillableItemId;
                txtBIQty.Text = _currentBISlice.Quantity.ToString();
                biOptimizationLevelSetItem(_currentBISlice.OptimizationLevel);

                btnBISave.Enabled = true;
                btnBIDelete.Enabled = true;
            }
        }

        private void resetBillableItemEditorControls()
        {
            luBillableItem.EditValue = null;
            txtBIQty.Text = string.Empty;
            rgBIOptimize.SelectedIndex = -1;

            btnBINew.Enabled = true;
            btnBIAdd.Enabled = false;
            btnBISave.Enabled = false;
            btnBIDelete.Enabled = false;
        }

        private bool validateBillableItemEditorControls()
        {
            if (luBillableItem.EditValue == null)
            {
                MessageDlg.Warning("Please select a Billable Item");
                luBillableItem.Select();
                luBillableItem.Focus();
                return false;
            }

            if (rgBIOptimize.SelectedIndex == -1)
            {
                MessageDlg.Warning("Please select the optimization level");
                rgBIOptimize.Select();
                rgBIOptimize.Focus();
                return false;
            }

            short qty = 0;
            if (!short.TryParse(txtBIQty.Text.Trim(), out qty))
            {
                MessageDlg.Warning("Please enter a valid quantity");
                txtBIQty.Select();
                txtBIQty.Focus();
                return false;
            }

            if (qty < 1)
            {
                MessageDlg.Warning("Please enter a valid quantity");
                txtBIQty.Select();
                txtBIQty.Focus();
                return false;
            }

            return true;
        }

        private string getSelectedText(TextEdit edit)
        {
            return edit.SelectionLength > 0 ? edit.SelectedText : edit.Text;
        }

        private void replaceSelectedText(TextEdit edit, string text)
        {
            if (edit.SelectionLength > 0)
            {
                var theText = edit.Text;
                var prefix = theText.Substring(0, edit.SelectionStart);
                var suffix = theText.Substring(edit.SelectionStart + edit.SelectionLength);
                edit.Text = prefix + text + suffix;
            }
            else
            {
                edit.Text = text;
            }
        }

        private void surroundHtmlTag(TextEdit edit, string tag)
        {
            var text = getSelectedText(edit);
            text = string.Format(HTML_TPL, tag, text);
            replaceSelectedText(edit, text);
        }

        private void btnDiscreteParamBold_Click(object sender, EventArgs e)
        {
            surroundHtmlTag(txtDiscreteParameter, "b");
        }

        private void btnDiscreteParamItalic_Click(object sender, EventArgs e)
        {
            surroundHtmlTag(txtDiscreteParameter, "i");
        }

        private void btnDiscreteParamUnderline_Click(object sender, EventArgs e)
        {
            surroundHtmlTag(txtDiscreteParameter, "u");
        }

        private void btnCopyCanonName_Click(object sender, EventArgs e)
        {
            txtShortName.Text = txtCanonName.Text.Trim();
        }

        private void btnCopyShortName_Click(object sender, EventArgs e)
        {
            txtCanonName.Text = txtShortName.Text.Trim();
        }

        private void btnTestCodeCheckUnique_Click(object sender, EventArgs e)
        {
            var code = txtTestCode.Text.Trim().ToUpperInvariant();
            var count = LabTestsRepository.GetLabTestCodeCount(code);
            if (count > 0)
            {
                MessageDlg.Warning(string.Format("The test code {0} has already been used {1} time(s)", code, count));
            }
        }

        private void btnCopyPerformingLab_Click(object sender, EventArgs e)
        {
            if (luPerformingLab.EditValue != null)
            {
                luResultingLab.EditValue = luPerformingLab.EditValue;
            }
        }

        private void btnCopyResultingLab_Click(object sender, EventArgs e)
        {
            if (luResultingLab.EditValue != null)
            {
                luPerformingLab.EditValue = luResultingLab.EditValue;
            }
        }

        private bool hasCurrentLabTest()
        {
            if (CurrentLabTest == null)
            {
                MessageDlg.Error(
                    "Lab Test was not saved into the database.\nYou must save the current Lab Test before adding new item.");
                return false;
            }
            return true;
        }

        private void btnBINew_Click(object sender, EventArgs e)
        {
            if (!hasCurrentLabTest()) return;
            resetBillableItemEditorControls();
            _currentBISlice = null;

            btnBINew.Enabled = false;
            btnBIAdd.Enabled = true;
        }

        private void btnBIAdd_Click(object sender, EventArgs e)
        {
            if (!hasCurrentLabTest()) return;
            if (validateBillableItemEditorControls())
            {
                var biId = (short) luBillableItem.EditValue;
                var optLevel = (byte) biOptimizationLevelGetItem();
                var quantity = short.Parse(txtBIQty.Text.Trim());

                TestBILinksRepository.CreateNewTestBILink(CurrentLabTest.Id, biId, optLevel, quantity);
                refreshLabTestSliceFromDatabase();
            }
        }

        private void refreshLabTestSliceFromDatabase()
        {
            var cursor = WaitFormControl.WaitStart(this, "Loading data...");
            try
            {
                var test = LabTestsRepository.FindById(CurrentLabTest.Id);
                CurrentLabTest = LabTestSlice.AssembleFrom(test, true);
                UpdateControls();
            }
            finally
            {
                WaitFormControl.WaitEnd(this, cursor);
            }
        }

        private void btnBISave_Click(object sender, EventArgs e)
        {
            if (!hasCurrentLabTest()) return;
            if (validateBillableItemEditorControls())
            {
                var biId = (short) luBillableItem.EditValue;
                var optLevel = (byte) biOptimizationLevelGetItem();
                var quantity = short.Parse(txtBIQty.Text.Trim());

                TestBILinksRepository.UpdateTestBILink(_currentBISlice.Id, CurrentLabTest.Id, biId, optLevel, quantity);
                refreshLabTestSliceFromDatabase();
            }
        }

        private void btnBIDelete_Click(object sender, EventArgs e)
        {
            if (!hasCurrentLabTest()) return;
            if (_currentBISlice != null && MessageDlg.Confirm("Really delete?"))
            {
                TestBILinksRepository.Delete(_currentBISlice.Id);
                TestBILinksRepository.Save();
                refreshLabTestSliceFromDatabase();
            }
        }

        private void btnDiscreteNew_Click(object sender, EventArgs e)
        {
            if (!hasCurrentLabTest()) return;
            resetDiscreteEditorControls();
            _currentDiscreteSlice = null;

            btnDiscreteNew.Enabled = false;
            btnDiscreteAdd.Enabled = true;
            chkDiscreteResultable.Checked = true;
            txtDiscreteIndent.Text = "0";
        }

        private void btnDiscreteAdd_Click(object sender, EventArgs e)
        {
            if (!hasCurrentLabTest()) return;

            if (validateDiscreteEditorControls())
            {
                var param = txtDiscreteParameter.Text.Trim();
                var units = txtDiscreteUnits.Text.Trim();
                var defaultResult = txtDefaultResult.Text.Trim();
                var refRange = stringCombineMultiLine(txtDiscreteRefRange.Lines);
                byte indent = 0;
                byte.TryParse(txtDiscreteIndent.Text.Trim(), out indent);
                // bounds check
                if (indent > 5)
                {
                    indent = 5;
                }

                var sortOrder = (byte) (CurrentLabTest.DiscreteReportLineItemSlices.Count + 10);

                var lineItem = new DiscreteReportLineItem
                               {
                                   Parameter = param,
                                   Units = units,
                                   ReferenceRange = refRange,
                                   IndentLevel = indent,
                                   SortOrder = sortOrder,
                                   IsResultableItem = chkDiscreteResultable.Checked,
                                   LabTestId = CurrentLabTest.Id,
                                   DefaultResult = defaultResult
                               };
                DiscreteReportLineItemsRepository.Add(lineItem);
                DiscreteReportLineItemsRepository.Save();

                CurrentLabTest.DiscreteReportLineItemSlices.ReloadItems(CurrentLabTest.Id);
                CurrentLabTest.DiscreteReportLineItemSlices.ReindexItems();
                refreshLabTestSliceFromDatabase();
            }
        }

        private bool validateDiscreteEditorControls()
        {
            if (string.IsNullOrEmpty(txtDiscreteParameter.Text.Trim()))
            {
                MessageDlg.Warning("Empty parameter is not allowed!");
                txtDiscreteParameter.Select();
                return false;
            }

            return true;
        }

        private void btnDiscreteSave_Click(object sender, EventArgs e)
        {
            if (!hasCurrentLabTest()) return;

            if (validateDiscreteEditorControls() && (_currentDiscreteSlice != null))
            {
                var param = txtDiscreteParameter.Text.Trim();
                var units = txtDiscreteUnits.Text.Trim();
                var defaultResult = txtDefaultResult.Text.Trim();
                var refRange = stringCombineMultiLine(txtDiscreteRefRange.Lines);
                byte indent = 0;
                byte.TryParse(txtDiscreteIndent.Text.Trim(), out indent);
                // bounds check
                if (indent > 5)
                {
                    indent = 5;
                }

                var sortOrder = _currentDiscreteSlice.SortOrder;

                var item = DiscreteReportLineItemsRepository.FindById(_currentDiscreteSlice.Id);
                if (item != null)
                {
                    item.Parameter = param;
                    item.Units = units;
                    item.DefaultResult = defaultResult;
                    item.ReferenceRange = refRange;
                    item.SortOrder = sortOrder;
                    item.IndentLevel = indent;
                    item.IsResultableItem = chkDiscreteResultable.Checked;

                    var cursor = WaitFormControl.WaitStart(this, "Saving item...");
                    try
                    {
                        DiscreteReportLineItemsRepository.Save();

                        CurrentLabTest.DiscreteReportLineItemSlices.ReloadItems(CurrentLabTest.Id);
                        CurrentLabTest.DiscreteReportLineItemSlices.ReindexItems();
                    }
                    finally
                    {
                        WaitFormControl.WaitEnd(this, cursor);
                    }
                    refreshLabTestSliceFromDatabase();
                }
            }
        }

        private void btnDiscreteDelete_Click(object sender, EventArgs e)
        {
            if (!hasCurrentLabTest()) return;
            if (_currentDiscreteSlice != null && MessageDlg.Confirm("Really delete?"))
            {
                DiscreteReportLineItemsRepository.Delete(_currentDiscreteSlice.Id);
                DiscreteReportLineItemsRepository.Save();

                CurrentLabTest.DiscreteReportLineItemSlices.Remove(_currentDiscreteSlice);
                CurrentLabTest.DiscreteReportLineItemSlices.ReindexItems();

                refreshLabTestSliceFromDatabase();
            }
        }

        private void btnDiscreteUp_Click(object sender, EventArgs e)
        {
            if (!hasCurrentLabTest()) return;
            var slice = grdGetDiscreteReportItem();
            if (slice != null)
            {
                CurrentLabTest.DiscreteReportLineItemSlices.MoveUp(slice);
                CurrentLabTest.DiscreteReportLineItemSlices.ReindexItems();
                refreshLabTestSliceFromDatabase();
            }
        }

        private void btnDiscreteDown_Click(object sender, EventArgs e)
        {
            if (!hasCurrentLabTest()) return;
            var slice = grdGetDiscreteReportItem();
            if (slice != null)
            {
                CurrentLabTest.DiscreteReportLineItemSlices.MoveDown(slice);
                CurrentLabTest.DiscreteReportLineItemSlices.ReindexItems();
                refreshLabTestSliceFromDatabase();
            }
        }

        private bool validateReqditorControls()
        {
            if (string.IsNullOrEmpty(txtReqParameter.Text.Trim()))
            {
                MessageDlg.Warning("Empty parameter is not allowed!");
                txtReqParameter.Select();
                return false;
            }

            return true;
        }

        private void btnReqNew_Click(object sender, EventArgs e)
        {
            if (!hasCurrentLabTest()) return;
            resetReqEditorControls();
            _currentReqSlice = null;

            btnReqNew.Enabled = false;
            btnReqAdd.Enabled = true;
            txtReqIndent.Text = "0";
        }

        private void btnReqAdd_Click(object sender, EventArgs e)
        {
            if (!hasCurrentLabTest()) return;

            if (validateReqditorControls())
            {
                var param = txtReqParameter.Text.Trim();
                byte indent = 0;
                byte.TryParse(txtReqIndent.Text.Trim(), out indent);
                // bounds check
                if (indent > 5)
                {
                    indent = 5;
                }

                var sortOrder = (byte) (CurrentLabTest.ReqParameterSlices.Count + 10);

                var item = new ReqParameter
                           {
                               Parameter = param,
                               IndentLevel = indent,
                               SortOrder = sortOrder,
                               LabTestId = CurrentLabTest.Id
                           };
                ReqParametersRepository.Add(item);
                ReqParametersRepository.Save();

                CurrentLabTest.ReqParameterSlices.ReloadItems(CurrentLabTest.Id);
                CurrentLabTest.ReqParameterSlices.ReindexItems();
                refreshLabTestSliceFromDatabase();
            }
        }

        private void btnReqSave_Click(object sender, EventArgs e)
        {
            if (!hasCurrentLabTest()) return;

            if (validateReqditorControls() && (_currentReqSlice != null))
            {
                var param = txtReqParameter.Text.Trim();
                byte indent = 0;
                byte.TryParse(txtReqIndent.Text.Trim(), out indent);
                // bounds check
                if (indent > 5)
                {
                    indent = 5;
                }

                var sortOrder = _currentReqSlice.SortOrder;

                var item = ReqParametersRepository.FindById(_currentReqSlice.Id);
                if (item != null)
                {
                    item.Parameter = param;
                    item.SortOrder = sortOrder;
                    item.IndentLevel = indent;

                    var cursor = WaitFormControl.WaitStart(this, "Saving item...");
                    try
                    {
                        ReqParametersRepository.Save();

                        CurrentLabTest.ReqParameterSlices.ReloadItems(CurrentLabTest.Id);
                        CurrentLabTest.ReqParameterSlices.ReindexItems();
                    }
                    finally
                    {
                        WaitFormControl.WaitEnd(this, cursor);
                    }

                    refreshLabTestSliceFromDatabase();
                }
            }
        }

        private void btnReqDelete_Click(object sender, EventArgs e)
        {
            if (!hasCurrentLabTest()) return;
            if (_currentReqSlice != null && MessageDlg.Confirm("Really delete?"))
            {
                ReqParametersRepository.Delete(_currentReqSlice.Id);
                ReqParametersRepository.Save();

                CurrentLabTest.ReqParameterSlices.Remove(_currentReqSlice);
                CurrentLabTest.ReqParameterSlices.ReindexItems();

                refreshLabTestSliceFromDatabase();
            }
        }

        private void btnReqUp_Click(object sender, EventArgs e)
        {
            if (!hasCurrentLabTest()) return;
            var slice = grdGetReqParam();
            if (slice != null)
            {
                CurrentLabTest.ReqParameterSlices.MoveUp(slice);
                CurrentLabTest.ReqParameterSlices.ReindexItems();
                refreshLabTestSliceFromDatabase();
            }
        }

        private void btnReqDown_Click(object sender, EventArgs e)
        {
            if (!hasCurrentLabTest()) return;
            var slice = grdGetReqParam();
            if (slice != null)
            {
                CurrentLabTest.ReqParameterSlices.MoveDown(slice);
                CurrentLabTest.ReqParameterSlices.ReindexItems();
                refreshLabTestSliceFromDatabase();
            }
        }

        private void btnReqBulkAdd_Click(object sender, EventArgs e)
        {
            if (!hasCurrentLabTest()) return;
            using (var frm = new TestParamBulkEditorDialog())
            {
                frm.DiscreteItems = CurrentLabTest.DiscreteReportLineItemSlices;
                if (frm.ShowDialog() == DialogResult.OK)
                {
                    if (frm.Lines.Count > 0)
                    {
                        var sortOrder = (byte) (CurrentLabTest.ReqParameterSlices.Count + 10);

                        var cursor = WaitFormControl.WaitStart(this, "Adding parameters...");
                        try
                        {
                            foreach (var line in frm.Lines)
                            {
                                var item = new ReqParameter
                                           {
                                               Parameter = line,
                                               IndentLevel = 0,
                                               SortOrder = sortOrder,
                                               LabTestId = CurrentLabTest.Id
                                           };
                                ReqParametersRepository.Add(item);
                                sortOrder++;
                            }

                            ReqParametersRepository.Save();
                        }
                        finally
                        {
                            WaitFormControl.WaitEnd(this, cursor);
                        }

                        CurrentLabTest.ReqParameterSlices.ReloadItems(CurrentLabTest.Id);
                        CurrentLabTest.ReqParameterSlices.ReindexItems();
                        refreshLabTestSliceFromDatabase();
                    }
                }
            }
        }

        private void btnDiscreteBulkAdd_Click(object sender, EventArgs e)
        {
            if (!hasCurrentLabTest()) return;
            using (var frm = new TestParamBulkEditorDialog())
            {
                frm.DiscreteItems = CurrentLabTest.DiscreteReportLineItemSlices;
                if (frm.ShowDialog() == DialogResult.OK)
                {
                    if (frm.Lines.Count > 0)
                    {
                        var sortOrder = (byte) (CurrentLabTest.DiscreteReportLineItemSlices.Count + 10);

                        var cursor = WaitFormControl.WaitStart(this, "Adding parameters...");
                        try
                        {
                            foreach (var line in frm.Lines)
                            {
                                var lineItem = new DiscreteReportLineItem
                                               {
                                                   Parameter = line,
                                                   IndentLevel = 0,
                                                   SortOrder = sortOrder,
                                                   IsResultableItem = true,
                                                   LabTestId = CurrentLabTest.Id
                                               };
                                DiscreteReportLineItemsRepository.Add(lineItem);
                                sortOrder++;
                            }

                            DiscreteReportLineItemsRepository.Save();
                        }
                        finally
                        {
                            WaitFormControl.WaitEnd(this, cursor);
                        }

                        CurrentLabTest.DiscreteReportLineItemSlices.ReloadItems(CurrentLabTest.Id);
                        CurrentLabTest.DiscreteReportLineItemSlices.ReindexItems();
                        refreshLabTestSliceFromDatabase();
                    }
                }
            }
        }

        private bool checkTextboxContent(TextEdit txt, string field)
        {
            if (string.IsNullOrEmpty(txt.Text.Trim()))
            {
                MessageDlg.Warning(string.Format("{0} cannot be empty!", field));
                txt.Select();
                return false;
            }

            return true;
        }

        private bool validateGeneralEditorControls()
        {
            if (!checkTextboxContent(txtTestCode, "Test Code")) return false;

            if (CurrentLabTest == null)
            {
                // this is a new test. check uniqueness of test code
                var code = txtTestCode.Text.Trim().ToUpperInvariant();
                var count = LabTestsRepository.GetLabTestCodeCount(code);
                if (count > 0)
                {
                    MessageDlg.Warning(string.Format("The test code {0} has already been used {1} time(s)", code, count));
                    txtTestCode.Select();
                    return false;
                }
            }

            if (!checkTextboxContent(txtShortName, "Short Name")) return false;

            if (!checkTextboxContent(txtCanonName, "Canonical Name")) return false;

            if (!checkTextboxContent(txtListPrice, "List Price")) return false;

            var price = 0m;
            if (!decimal.TryParse(txtListPrice.Text.Trim(), out price) || price < 0m)
            {
                MessageDlg.Warning(string.Format("Invalid List Price of {0}!", price));
                txtListPrice.Select();
                return false;
            }

            if (!decimal.TryParse(txtSubOrderPrice.Text.Trim(), out price) || price < 0m)
            {
                MessageDlg.Warning(string.Format("Invalid Sub-order Price of {0}!", price));
                txtSubOrderPrice.Select();
                return false;
            }

            if (!decimal.TryParse(txtCostBasis.Text.Trim(), out price) || price < 0m)
            {
                MessageDlg.Warning(string.Format("Invalid Cost Basis price of {0}!", price));
                txtCostBasis.Select();
                return false;
            }

            if (rgReqSlip.SelectedIndex == -1)
            {
                MessageDlg.Warning("Please select Request Slip Generation option");
                rgReqSlip.Select();
                return false;
            }

            if (rgTestSortPriority.SelectedIndex == -1)
            {
                MessageDlg.Warning("Please select Test Sort Priority option");
                rgTestSortPriority.Select();
                return false;
            }

            if (luPerformingLab.EditValue == null)
            {
                MessageDlg.Warning("Please select the Performing Lab");
                luPerformingLab.Select();
                return false;
            }

            if (luTatGroup.EditValue == null)
            {
                MessageDlg.Warning("Please select the default Turn-around Time group");
                luTatGroup.Select();
                return false;
            }

            return true;
        }

        private decimal textToDecimal(TextEdit edit)
        {
            var text = edit.Text.Trim();
            var value = 0m;
            decimal.TryParse(text, out value);
            return value;
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            if (!validateGeneralEditorControls()) return;

            var shortName = txtShortName.Text.Trim();
            var price = decimal.Parse(txtListPrice.Text.Trim());

            //var test = FaultHandler.Shield(() => (CurrentLabTest != null)
            //    ? LabTestsRepository.FindById(CurrentLabTest.Id)
            //    : LabTestsRepository.CreateNew(shortName, price));
            LabTest test = null;
            FaultHandler.Shield(() =>
            {
                var id = CurrentLabTest == null
                    ? LabTestsRepository.CreateNewLabTest(shortName,
                                                          price,
                                                          getSelectedPerformingLabId(),
                                                          getSelectedResultingLabId())
                    : CurrentLabTest.Id;
                LabTestsRepository.Refresh();
                test = LabTestsRepository.FindById(id);
            });

            var mnemonics = txtMnemonics.Text.Trim().ToLowerInvariant();
            var reportLineGroupTag = txtReportLineGroupTag.Text.Trim().ToUpperInvariant();
            /*
            test.ShortName = shortName;
            test.ListPrice = textToDecimal(txtListPrice);
            test.SubOrderPrice = textToDecimal(txtSubOrderPrice);
            test.CostBasis = textToDecimal(txtCostBasis);
            test.TestSKU = txtTestCode.Text.Trim().ToUpperInvariant();
            test.CanonicalName = txtCanonName.Text.Trim();
            test.Mnemonics = string.IsNullOrEmpty(mnemonics) ? null : mnemonics;
            test.ReqSlipPrintOption = (byte) reqSlipGetItem();
            test.ReportSortPriority = (byte) testSortPriorityGetItem();
            test.IsActive = chkIsActive.Checked;
            test.PerformingLabId = getSelectedPerformingLabId();
            test.ResultingLabId = getSelectedResultingLabId();
            test.TATGroupId = (short?) luTatGroup.EditValue;
            test.TemplateGroupId = getSelectedTemplateGroupId();
            test.DefaultTemplateId = getSelectedDefaultTemplateId();
            test.InactiveDate = dteInactiveDate.EditValue == null ? (DateTime?) null : dteInactiveDate.DateTime;
            LabTestsRepository.Save();
            */

            FaultHandler.Shield(() => LabTestsRepository.UpdateLabTest(
                test.Id,
                chkIsActive.Checked,
                txtTestCode.Text.Trim().ToUpperInvariant(),
                shortName,
                txtCanonName.Text.Trim(),
                textToDecimal(txtListPrice),
                textToDecimal(txtSubOrderPrice),
                textToDecimal(txtCostBasis),
                (byte) reqSlipGetItem(),
                0,
                (byte) testSortPriorityGetItem(),
                string.IsNullOrEmpty(mnemonics) ? null : mnemonics,
                getSelectedPerformingLabId(),
                getSelectedResultingLabId(),
                getSelectedDefaultTemplateId(),
                (short?) luTatGroup.EditValue,
                getSelectedTemplateGroupId(),
                reportLineGroupTag));

            LabTestsRepository.Reset();
            // is this a newly added test?
            if (CurrentLabTest == null)
            {
                // just create a mock object with the valid object Id. it will be re-initialized 
                // by refreshLabTestSliceFromDatabase() below anyway
                CurrentLabTest = new LabTestSlice {Id = test.Id};}
            refreshLabTestSliceFromDatabase();
        }

        private void luResultingLab_EditValueChanged(object sender, EventArgs e)
        {
            var isTemplate = false;
            var labId = getSelectedResultingLabId();
            if (labId > 0)
            {
                var lab = LabsRepository.FindById(labId);
                isTemplate = (lab.TestResultType == (byte) TestResultType.Template) ||
                             (lab.TestResultType == (byte) TestResultType.UserTemplate);
            }

            luTemplateGroup.Enabled = isTemplate;
            luDefaultTemplate.Enabled = isTemplate;
            updateTemplateGroupsLookUpControl();
        }

        private short getSelectedResultingLabId()
        {
            return luResultingLab.EditValue == null ? (short) 0 : (short) luResultingLab.EditValue;
        }

        private short? getSelectedTemplateGroupId()
        {
            return luTemplateGroup.EditValue == null ? (short?) null : (short) luTemplateGroup.EditValue;
        }

        private short getSelectedPerformingLabId()
        {
            return luPerformingLab.EditValue == null ? (short) 0 : (short) luPerformingLab.EditValue;
        }

        private short getSelectedDefaultTemplateId()
        {
            return luDefaultTemplate.EditValue == null ? (short) 0 : (short) luDefaultTemplate.EditValue;
        }

        private void btnTestGeneratePrefix_Click(object sender, EventArgs e)
        {
            var labId = getSelectedPerformingLabId();
            if (labId > 0)
            {
                var lab = LabsRepository.FindById(labId);
                var prefix = lab.LabCode.ToUpperInvariant();
                var suffix = txtTestCode.Text.Trim().ToUpperInvariant();
                txtTestCode.Text = prefix + "." + suffix;
                txtTestCode.Select();
            }
        }

        private static string generateTestCode(string input)
        {
            input = input.Trim().ToUpperInvariant();
            if (string.IsNullOrEmpty(input)) return string.Empty;

            var parts = input.Split(new[]
                                    {
                                        ' ', ',', '.', ':', '\t',
                                        '-', '/', '\\', '(', ')', '[', ']'
                                    });
            var code = new StringBuilder();
            foreach (var part in parts)
            {
                var s = part.Trim();
                if (!string.IsNullOrEmpty(s))
                {
                    code.Append(s[0]);
                }
            }
            return code.ToString();
        }

        private void btnTestGenerateCode_Click(object sender, EventArgs e)
        {
            var labId = luPerformingLab.EditValue == null ? (short) 0 : (short) luPerformingLab.EditValue;
            if (labId > 0)
            {
                var lab = LabsRepository.FindById(labId);
                var prefix = lab.LabCode.ToUpperInvariant();
                var code = generateTestCode(txtShortName.Text);
                var suffix = txtTestCode.Text.Trim().ToUpperInvariant();
                txtTestCode.Text = string.Format("{0}.{1}.{2}", prefix, code, suffix).Trim();
                txtTestCode.Select();
            }
        }

        private void btnDiscreteClearAll_Click(object sender, EventArgs e)
        {
            if (!hasCurrentLabTest()) return;

            if (MessageDlg.Confirm("Really clear all parameter(s)?") && CaptchaDialog.ConfirmCaptcha(4))
            {
                var slices = CurrentLabTest.DiscreteReportLineItemSlices.ToList();
                foreach (var slice in slices)
                {
                    DiscreteReportLineItemsRepository.Delete(slice.Id);
                }

                WaitFormControl.WaitStart(this, description: "Updating database...");
                try
                {
                    DiscreteReportLineItemsRepository.Save();

                    CurrentLabTest.DiscreteReportLineItemSlices.Clear();
                    CurrentLabTest.DiscreteReportLineItemSlices.ReloadItems(CurrentLabTest.Id);
                    CurrentLabTest.DiscreteReportLineItemSlices.ReindexItems();
                    refreshLabTestSliceFromDatabase();
                }
                finally
                {
                    WaitFormControl.WaitEnd(this, DefaultCursor);
                }
            }
        }

        private void btnDiscreteImportParameters_Click(object sender, EventArgs e)
        {
            if (!hasCurrentLabTest()) return;

            using (var form = new TestSelectionDialog())
            {
                if (form.ShowDialog(this) == DialogResult.OK)
                {
                    var testId = form.SelectedTestId;
                    if (testId > 0)
                    {
                        var lineItems = DiscreteReportLineItemsRepository.GetAllItemsForTest(testId);

                        // confirm before importing...
                        var sb = new StringBuilder("Do you want to import the following parameter(s)?\n");
                        foreach (var item in lineItems)
                        {
                            sb.AppendLine("  > " + item.Parameter);
                        }

                        if (!MessageDlg.Confirm(sb.ToString()))
                            return;

                        var sortOrder = (byte) (CurrentLabTest.DiscreteReportLineItemSlices.Count + 20);

                        foreach (var item in lineItems)
                        {
                            var newItem = new DiscreteReportLineItem
                                          {
                                              Parameter = item.Parameter,
                                              Units = item.Units,
                                              ReferenceRange = item.ReferenceRange,
                                              IndentLevel = item.IndentLevel,
                                              SortOrder = sortOrder++,
                                              IsResultableItem = item.IsResultableItem,
                                              LabTestId = CurrentLabTest.Id
                                          };
                            DiscreteReportLineItemsRepository.Add(newItem);
                        }

                        WaitFormControl.WaitStart(this, description: "Updating database...");
                        try
                        {
                            DiscreteReportLineItemsRepository.Save();

                            CurrentLabTest.DiscreteReportLineItemSlices.ReloadItems(CurrentLabTest.Id);
                            CurrentLabTest.DiscreteReportLineItemSlices.ReindexItems();
                            refreshLabTestSliceFromDatabase();
                        }
                        finally
                        {
                            WaitFormControl.WaitEnd(this, DefaultCursor);
                        }
                    }
                }
            }
        }

        private void btnReqClearAll_Click(object sender, EventArgs e)
        {
            if (!hasCurrentLabTest()) return;

            if (MessageDlg.Confirm("Really clear all parameter(s)?") && CaptchaDialog.ConfirmCaptcha(4))
            {
                var slices = CurrentLabTest.ReqParameterSlices.ToList();
                foreach (var slice in slices)
                {
                    ReqParametersRepository.Delete(slice.Id);
                }

                WaitFormControl.WaitStart(this, description: "Updating database...");
                try
                {
                    ReqParametersRepository.Save();

                    CurrentLabTest.ReqParameterSlices.Clear();
                    CurrentLabTest.ReqParameterSlices.ReloadItems(CurrentLabTest.Id);
                    CurrentLabTest.ReqParameterSlices.ReindexItems();
                    refreshLabTestSliceFromDatabase();
                }
                finally
                {
                    WaitFormControl.WaitEnd(this, DefaultCursor);
                }
            }
        }

        private void btnReqImportParameters_Click(object sender, EventArgs e)
        {
            if (!hasCurrentLabTest()) return;

            using (var form = new TestSelectionDialog())
            {
                if (form.ShowDialog(this) == DialogResult.OK)
                {
                    var testId = form.SelectedTestId;
                    if (testId > 0)
                    {
                        var lineItems = ReqParametersRepository.GetAllReqParametersForTest(testId);

                        // confirm before importing...
                        var sb = new StringBuilder("Do you want to import the following parameter(s)?\n");
                        foreach (var item in lineItems)
                        {
                            sb.AppendLine("  > " + item.Parameter);
                        }

                        if (!MessageDlg.Confirm(sb.ToString()))
                            return;

                        var sortOrder = (byte) (CurrentLabTest.ReqParameterSlices.Count + 20);

                        foreach (var item in lineItems)
                        {
                            var newItem = new ReqParameter
                                          {
                                              Parameter = item.Parameter,
                                              IndentLevel = item.IndentLevel,
                                              SortOrder = sortOrder++,
                                              LabTestId = CurrentLabTest.Id
                                          };
                            ReqParametersRepository.Add(newItem);
                        }

                        WaitFormControl.WaitStart(this, description: "Updating database...");
                        try
                        {
                            ReqParametersRepository.Save();

                            CurrentLabTest.ReqParameterSlices.ReloadItems(CurrentLabTest.Id);
                            CurrentLabTest.ReqParameterSlices.ReindexItems();
                            refreshLabTestSliceFromDatabase();
                        }
                        finally
                        {
                            WaitFormControl.WaitEnd(this, DefaultCursor);
                        }
                    }
                }
            }
        }

        private void luTemplateGroup_EditValueChanged(object sender, EventArgs e)
        {
            updateTemplateReportsLookUpControl();
        }

        private void btnUpdateTemplateGroupsList_Click(object sender, EventArgs e)
        {
            updateTemplateGroupsLookUpControl();
        }

        private void btnUpdateDefaultTemplatesList_Click(object sender, EventArgs e)
        {
            updateTemplateReportsLookUpControl();
        }

        private void btnEditDefaultTemplate_Click(object sender, EventArgs e)
        {
            new TemplateReportsEditorDialog().ExecuteDialog(this);
        }
    }
}