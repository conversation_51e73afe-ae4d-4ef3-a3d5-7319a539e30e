﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ReferrerCatalogBrowserDialog.cs 1140 2014-01-29 13:18:10Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using C1.LiveLinq;
using C1.LiveLinq.Collections;
using DevExpress.XtraBars;
using DevExpress.XtraEditors;
using LabMaestro.BusinessLogic;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Shared;
using Xceed.Grid;

namespace LabMaestro.Controls.Win;

[Flags]
public enum ReferrerCatalogAccessLevel
{
    None = 0,
    BrowseCatalog = 0x01,
    GenerateReport = BrowseCatalog << 2,
    EditCatalog = GenerateReport << 2
}

public partial class ReferrerCatalogBrowserDialog : XtraForm, IExecutableDialog
{
    private readonly ReferrerCatalogAccessLevel _accessLevel;

    private readonly IndexedCollection<ReferrerSlice> _referrerCatalog;

    //private List<ReferrerSlice> _currentFilteredCatalog;
    private bool _filterByClass;
    private short _selectedCategoryId;
    private short _selectedClassId;

    public ReferrerCatalogBrowserDialog(ReferrerCatalogAccessLevel accessLevel)
    {
        _referrerCatalog = new IndexedCollection<ReferrerSlice>();
        InitializeComponent();

        WinUtils.ApplyTheme(this);
        WinUtils.SetFormTitle(this, "Referring Physicians Catalog");

        _accessLevel = accessLevel;
        _selectedCategoryId = 0;
        _selectedClassId = 0;
    }

    public bool ExecuteDialog(IWin32Window parent)
    {
        UpdateControls();
        ShowDialog(parent);
        return true;
    }

    public void UpdateControls()
    {
        updateToolBarByAccessLevel();
        gridCreateColumns();
        gridPopulateLeftGrids();
        updateStatusBar();
    }

    private IEnumerable<ReferrerSlice> getCuratedReferrersList()
    {
        if (tabFilter.SelectedIndex == 0)
            return _referrerCatalog.AsIndexed()
                .OrderBy(x => x.Name)
                .ToList();

        if (tabFilter.SelectedIndex == 1)
            return _referrerCatalog.AsIndexed()
                .OrderBy(x => x.Id)
                .ToList();

        var filterChar = Convert.ToChar(tabFilter.SelectedIndex + 63);
        return _referrerCatalog.AsIndexed()
            .Where(x => x.Name[0] == filterChar)
            .OrderBy(x => x.Name)
            .ToList();
    }

    private void updateToolBarByAccessLevel()
    {
        btnCategoryEditCatalog.Enabled = accessCanEditCatalog();
        btnRefClassEditCatalog.Enabled = accessCanEditCatalog();
        btnPhysicianAdd.Enabled = accessCanEditCatalog();
    }

    private bool accessCanGenerateReport() =>
        _accessLevel.HasFlag(ReferrerCatalogAccessLevel.EditCatalog) ||
        _accessLevel.HasFlag(ReferrerCatalogAccessLevel.GenerateReport);

    private bool accessCanEditCatalog() => _accessLevel.HasFlag(ReferrerCatalogAccessLevel.EditCatalog);

    private void gridCreateColumns()
    {
        var i = 0;

        grdCategories.Columns.Clear();
        grdCategories.GridAddColumn("name", "Category", i++, 180);
        grdCategories.GridAddColumn("active", "Active?", i++, 60);
        grdCategories.GridAddColumn("id", "Id", i++, 30);

        i = 0;
        grdRefClasses.Columns.Clear();
        grdRefClasses.GridAddColumn("name", "Name", i++, 180);
        grdRefClasses.GridAddColumn("eligible", "Eligible", i++, 60);
        grdRefClasses.GridAddColumn("id", "Id", i++, 30);

        i = 0;
        grdPhysicians.Columns.Clear();

        grdPhysicians.GridAddColumn("id", "Id", i++, 70, true);
        grdPhysicians.GridAddColumn("prefix", "Prefix", i++, 90, true);
        grdPhysicians.GridAddColumn("name", "Name", i++, 280, true);
        grdPhysicians.GridAddColumn("suffix", "Suffix", i++, 120, true);
        grdPhysicians.GridAddColumn("tags", "Tags", i++, 160);
        grdPhysicians.GridAddColumn("refClass", "Category/Class", i++, 140, true);
        var column = new Column("active", typeof(Image))
        {
            Title = "Act?",
            VisibleIndex = i++,
            Width = 40,
            MaxWidth = 40,
            Visible = true
        };
        grdPhysicians.Columns.Add(column);
        //TODO: login
    }

    private void gridPopulateLeftGrids()
    {
        WaitFormControl.WaitStart(this);
        try
        {
            var categories = ReferrerCategoryRepository.FetchAllSlices().OrderBy(x => x.Name).ToList();
            grdCategories.BeginInit();
            try
            {
                grdCategories.DataRows.Clear();

                foreach (var item in categories)
                {
                    var row = grdCategories.DataRows.AddNew();
                    row.Cells["name"].Value = item.Name;
                    row.Cells["id"].Value = item.Id.ToString();
                    row.Cells["active"].Value = SharedUtilities.BooleanToStringYN(item.IsActive);
                    row.Tag = item;
                    row.EndEdit();
                }
            }
            finally
            {
                grdCategories.EndInit();
            }

            var refClasses = ReferralClassesRepository.FetchAll().OrderBy(x => x.Name).ToList();
            grdRefClasses.BeginInit();
            try
            {
                grdRefClasses.DataRows.Clear();

                foreach (var refClass in refClasses)
                {
                    var row = grdRefClasses.DataRows.AddNew();
                    row.Cells["name"].Value = refClass.Name;
                    row.Cells["id"].Value = refClass.Id.ToString();
                    row.Cells["eligible"].Value = SharedUtilities.BooleanToStringYN(refClass.ReferralEligible);
                    row.Tag = refClass;
                    row.EndEdit();
                }
            }
            finally
            {
                grdRefClasses.EndInit();
            }
        }
        finally
        {
            WaitFormControl.WaitEnd(this, DefaultCursor);
        }
    }

    private void gridPopulatePhysicians()
    {
        if (_referrerCatalog.Count == 0)
            return;

        grdPhysicians.BeginInit();
        try
        {
            grdPhysicians.DataRows.Clear();

            foreach (var item in getCuratedReferrersList())
            {
                var row = grdPhysicians.DataRows.AddNew();
                row.Cells["name"].Value = item.Name;
                row.Cells["prefix"].Value = item.Prefix;
                row.Cells["suffix"].Value = item.Suffix;
                row.Cells["tags"].Value = item.IdentifyingTag;
                row.Cells["refClass"].Value = _filterByClass ? item.ReferralClassName : item.CategoryName;
                row.Cells["id"].Value = item.Id.ToString();

                if (!item.IsActive)
                {
                    row.Cells["active"].Value = Resources.cancel2_16;
                }

                row.Height = 21;
                row.Tag = item;

                row.EndEdit();
            }
        }
        finally
        {
            grdPhysicians.EndInit();
        }
    }

    private void gridResetPhysicians(bool resetTabs)
    {
        populateCatalog(null);

        if (resetTabs)
        {
            tabFilter.SelectedIndex = 0;
        }

        grdPhysicians.BeginInit();
        try
        {
            grdPhysicians.DataRows.Clear();
        }
        finally
        {
            grdPhysicians.EndInit();
        }
    }

    private ReferralClass getSelectedRefClass()
    {
        var item = (grdRefClasses.SelectedRows.Count == 1)
            ? grdRefClasses.SelectedRows[0].Tag as ReferralClass
            : null;
        if (item != null) _selectedClassId = item.Id;
        return item;
    }

    private ReferrerCategorySlice getSelectedCategory()
    {
        var slice = (grdCategories.SelectedRows.Count == 1)
            ? grdCategories.SelectedRows[0].Tag as ReferrerCategorySlice
            : null;
        if (slice != null) _selectedCategoryId = slice.Id;
        return slice;
    }

    private void resetSelectedIds()
    {
        _selectedCategoryId = 0;
        _selectedClassId = 0;
    }

    private void grdCategories_SelectedRowsChanged(object sender, EventArgs e) =>
        handleLeftGridsSelectedRowsChanged(false, true);

    private void handleLeftGridsSelectedRowsChanged(bool filterByClass, bool resetTabs)
    {
        resetSelectedIds();
        gridResetPhysicians(resetTabs);

        if (filterByClass)
        {
            var refClass = getSelectedRefClass();
            if (refClass != null)
            {
                var cursor = WaitFormControl.WaitStart(this);
                try
                {
                    var catalog = ReferrersRepository
                        .GetAllReferrersInReferralClass(refClass.Id)
                        .OrderBy(x => x.Name)
                        .ToList();

                    populateCatalog(catalog);
                }
                finally
                {
                    WaitFormControl.WaitEnd(this, cursor);
                }

                _filterByClass = true;
            }
        }
        else
        {
            var cat = getSelectedCategory();
            if (cat != null)
            {
                WaitFormControl.WaitStart(this);
                try
                {
                    var catalog = ReferrersRepository
                        .GetAllReferrersInCategory(cat.Id)
                        .OrderBy(x => x.Name)
                        .ToList();

                    populateCatalog(catalog);
                }
                finally
                {
                    WaitFormControl.WaitEnd(this, DefaultCursor);
                }

                _filterByClass = false;
            }
        }

        updateStatusBar();
        gridPopulatePhysicians();
    }

    private void populateCatalog(List<ReferrerSlice> catalog)
    {
        _referrerCatalog.BeginUpdate();
        try
        {
            _referrerCatalog.Clear();
            if (catalog != null && catalog.Any())
            {
                _referrerCatalog.AddRange(catalog);
            }
        }
        finally
        {
            _referrerCatalog.EndUpdate();
        }
    }

    private void grdRefClasses_SelectedRowsChanged(object sender, EventArgs e) =>
        handleLeftGridsSelectedRowsChanged(true, true);

    private void updateStatusBar()
    {
        var count = _referrerCatalog != null ? _referrerCatalog.Count : 0;
        lblPhysiciansCount.Caption = $" Total Physicians: {count} ";
        lblRefClassesCount.Caption = $" Total Referral Classes: {grdRefClasses.DataRows.Count} ";
        lblCategoriesCount.Caption = $" Total Physician Categories: {grdCategories.DataRows.Count} ";
    }

    private void editCategoryClassCatalog(bool editRefClass)
    {
        using (var frm = new ReferrerClassCategoryEditorDialog(editRefClass))
        {
            frm.UpdateControls();
            frm.ShowDialog();
        }

        gridPopulateLeftGrids();
    }

    private void btnRefClassEditCatalog_ItemClick(object sender, ItemClickEventArgs e) =>
        editCategoryClassCatalog(true);

    private void btnCategoryEditCatalog_ItemClick(object sender, ItemClickEventArgs e) =>
        editCategoryClassCatalog(false);

    private void tabFilter_SelectedIndexChanged(object sender, EventArgs e) =>
        handleLeftGridsSelectedRowsChanged(_filterByClass, false);

    private void grdPhysicians_KeyUp(object sender, KeyEventArgs e)
    {
        switch (e.KeyCode)
        {
            case Keys.F2:
                editSelectedPhysician();
                e.Handled = true;
                break;
            case Keys.F5:
                grdPhysicians.BeginInit();
                try
                {
                    var count = grdPhysicians.DataRows.Count;
                    for (var i = 0; i < count; i++)
                    {
                        grdPhysicians.SelectedRows.Add(grdPhysicians.DataRows[i]);
                    }
                }
                finally
                {
                    grdPhysicians.EndInit();
                }

                e.Handled = true;
                break;
            case Keys.F9:
                grdPhysicians.BeginInit();
                try
                {
                    var count = grdPhysicians.DataRows.Count;

                    for (var i = 0; i < count; i++)
                    {
                        Row row = grdPhysicians.DataRows[i];
                        if (grdPhysicians.SelectedRows.Contains(row))
                        {
                            grdPhysicians.SelectedRows.Remove(row);
                        }
                        else
                        {
                            grdPhysicians.SelectedRows.Add(row);
                        }
                    }
                }
                finally
                {
                    grdPhysicians.EndInit();
                }

                e.Handled = true;
                break;
            case Keys.Escape:
                grdPhysicians.SelectedRows.Clear();
                e.Handled = true;
                break;
        }
    }

    private ReferrerSlice getSelectedPhysician()
    {
        if (grdPhysicians.SelectedRows.Count == 1)
            return grdPhysicians.SelectedRows[0].Tag as ReferrerSlice;

        return null;
    }

    private void editSelectedPhysician() => addEditPhysician(getSelectedPhysician());

    private void addEditPhysician(ReferrerSlice slice)
    {
        if (accessCanEditCatalog())
        {
            Referrer physician = null;

            if (slice != null)
            {
                FaultHandler.Shield(() => physician = ReferrersRepository.FindById(slice.Id));
            }

            try
            {
                new ReferrerEditorDialog(physician).ExecuteDialog(this);
            }
            finally
            {
                handleLeftGridsSelectedRowsChanged(_filterByClass, true);
            }
        }
    }

    private void btnPhysicianAdd_ItemClick(object sender, ItemClickEventArgs e)
    {
        addEditPhysician(null);
    }

    private void btnCategoryAddPhysician_ItemClick(object sender, ItemClickEventArgs e)
    {
        performCategoryClassBulkProcess(false);
    }

    private List<ReferrerSlice> getSelectedReferrers()
    {
        var list = new List<ReferrerSlice>();

        if (grdPhysicians.SelectedRows.Count > 0)
        {
            foreach (DataRow row in grdPhysicians.SelectedRows)
                if (row.Tag is ReferrerSlice slice)
                    list.Add(slice);
        }

        return list;
    }

    private short executeCategoryClassSelectorDialog(bool showRefClass, int selCount)
    {
        using var frm = new ReferrerClassCategorySelectorDialog(showRefClass, selCount);
        return frm.ShowDialog(this) == DialogResult.OK ? frm.SelectedItemId : (short)0;
    }

    private void btnRefClassMovePhysician_ItemClick(object sender, ItemClickEventArgs e)
    {
        performCategoryClassBulkProcess(true);
    }

    private List<ReferrerSlice> executeStagingAreaDialog(List<ReferrerSlice> referrers)
    {
        var list = new List<ReferrerSlice>();
        using var frm = new ReferrersStagingAreaDialog();
        frm.SelectedReferrers.AddRange(referrers);
        frm.UpdateControls();
        if (frm.ShowDialog(this) == DialogResult.OK)
        {
            list.AddRange(frm.SelectedReferrers);
        }

        return list;
    }

    private void performCategoryClassBulkProcess(bool applyRefClass)
    {
        if (!accessCanEditCatalog()) return;

        var physicians = getSelectedReferrers();
        if (physicians == null || physicians.Count == 0) return;

        physicians = executeStagingAreaDialog(physicians);
        if (physicians == null || physicians.Count == 0) return;

        var itemId = executeCategoryClassSelectorDialog(applyRefClass, physicians.Count);
        if (itemId <= 0) return;

        WaitFormControl.WaitStart(this, description: "Applying changes...");
        try
        {
            foreach (var slice in physicians)
            {
                if (applyRefClass)
                {
                    var physician = ReferrersRepository.FindById(slice.Id);
                    physician.ReferralClassId = itemId;
                }
                else
                {
                    /*
                    // verify that the physician doesn't already belong to the category
                    if (ReferrersRepository.GetReferrerCategoryLink(slice.Id, itemId) == null)
                    {
                        // add the link if not done already
                        ReferrersRepository.CreateReferrerCategoryLink(slice.Id, itemId, false);
                    }
                    */
                    ReferrerCategoryRepository.CreateReferrerCategoryLink(itemId, slice.Id);
                }
            }

            // commit all modifications
            if (applyRefClass)
                ReferrersRepository.Save();
        }
        finally
        {
            WaitFormControl.WaitEnd(this, DefaultCursor);
        }

        handleLeftGridsSelectedRowsChanged(_filterByClass, true);
    }

    private void btnExit_ItemClick(object sender, ItemClickEventArgs e) => Close();

    private void btnExcelExport_ItemClick(object sender, ItemClickEventArgs e)
    {
        saveFileDialog.FileName = Path.Combine(saveFileDialog.InitialDirectory,
            "referrers_" + SharedUtilities.DateToStringOnlyDot(DateTime.Now).Replace(".", "") + ".xlsx");

        if (saveFileDialog.ShowDialog(this) != DialogResult.OK) return;

        var filename = saveFileDialog.FileName;
        var count = ReferrerCatalogExporter.ExportCatalogToExcelDocument(filename, false);
        if (count < 0)
        {
            MessageDlg.Warning("No records exported!");
        }
        else
        {
            MessageDlg.Info($"Exported {count} records.");
            Process.Start(filename);
        }
    }

    private void btnExcelImport_ItemClick(object sender, ItemClickEventArgs e)
    {
    }

    private void btnReferrerSearch_ItemClick(object sender, ItemClickEventArgs e)
    {
        MessageDlg.Info("Feature not implemented.");
    }
}