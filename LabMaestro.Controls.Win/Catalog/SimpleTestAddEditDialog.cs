﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: SimpleTestAddEditDialog.cs 1256 2014-05-18 15:51:13Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Linq;
using System.Text;
using DevExpress.XtraEditors;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Shared;

namespace LabMaestro.Controls.Win
{
    public partial class SimpleTestAddEditDialog : XtraForm
    {
        public SimpleTestAddEditDialog()
        {
            InitializeComponent();
            WinUtils.ApplyTheme(this);
            WinUtils.SetFormTitle(this, "Add/Edit Test");
        }

        public LabTestSlice CurrentLabTest { get; set; }

        private void btnCopyResultingLab_Click(object sender, EventArgs e)
        {
            if (luResultingLab.EditValue != null)
            {
                luPerformingLab.EditValue = luResultingLab.EditValue;
            }
        }

        private void btnCopyPerformingLab_Click(object sender, EventArgs e)
        {
            if (luPerformingLab.EditValue != null)
            {
                luResultingLab.EditValue = luPerformingLab.EditValue;
            }
        }

        private void btnTestCodeCheckUnique_Click(object sender, EventArgs e)
        {
            var code = txtTestCode.Text.Trim().ToUpperInvariant();
            var count = LabTestsRepository.GetLabTestCodeCount(code);
            if (count > 0)
            {
                MessageDlg.Warning(string.Format("The test code {0} has already been used {1} time(s)", code, count));
            }
        }

        public void UpdateControls()
        {
            resetControls();
            populateLookUpControls();
            if (CurrentLabTest != null)
            {
                Text = "Edit - " + CurrentLabTest.ShortName;
                updateControlsFromCurrentLabTest();
            }
            else
            {
                Text = "Add New Test...";
            }
        }

        private void populateLookUpControls()
        {
            var labs = LabsRepository.GetAllLabSlices().OrderBy(x => x.Name).ToList();
            WinUtils.PopulateLookupControl(luPerformingLab, labs, "Name", "Id", new[] {"Name"});
            WinUtils.PopulateLookupControl(luResultingLab, labs, "Name", "Id", new[] {"Name"});
            var tatGroups = TATGroupsRepository.GetAllTATGroups()
                .OrderByDescending(x => x.TATRank)
                .ThenBy(x => x.Name)
                .Select(
                    x =>
                        new
                        {
                            x.Id,
                            x.Name,
                            TATRank = EnumUtils.EnumDescription((TATRankingType) x.TATRank),
                            x.HoursRequired,
                            x.DaysRequired
                        }).ToList();
            WinUtils.PopulateLookupControl(luTatGroup, tatGroups, "Name", "Id",
                new[] {"Name", "TATRank", "HoursRequired", "DaysRequired", "Id"});
        }

        private void resetControls()
        {
            lblCreatedDate.Text = string.Empty;
            lblLastModifiedDate.Text = string.Empty;
            lblTestId.Text = string.Empty;
        }

        private void updateControlsFromCurrentLabTest()
        {
            if (CurrentLabTest == null) return;

            lblTestId.Text = SharedUtilities.IntToStringPositive(CurrentLabTest.Id);
            lblCreatedDate.Text = SharedUtilities.DateTimeToStringDMHM(CurrentLabTest.DateCreated);
            lblLastModifiedDate.Text = SharedUtilities.DateTimeToStringDMHM(CurrentLabTest.LastModified);

            txtTestCode.Text = CurrentLabTest.TestSKU;
            txtTestName.Text = CurrentLabTest.ShortName;
            txtListPrice.Text = SharedUtilities.MoneyToStringPlain(CurrentLabTest.ListPrice);

            setLookupEditValue(luPerformingLab, CurrentLabTest.PerformingLabId);
            setLookupEditValue(luResultingLab, CurrentLabTest.ResultingLabId);
            setLookupEditValue(luTatGroup, CurrentLabTest.TATGroupId);
        }

        private void setLookupEditValue(LookUpEdit control, short value)
        {
            if (value > 0)
            {
                control.EditValue = value;
            }
        }

        private static string generateTestCode(string input)
        {
            input = input.Trim().ToUpperInvariant();
            if (string.IsNullOrEmpty(input)) return string.Empty;

            var parts = input.Split(new[]
            {
                ' ', ',', '.', ':', '\t',
                '-', '/', '\\', '(', ')', '[', ']'
            });
            var code = new StringBuilder();
            foreach (var part in parts)
            {
                var s = part.Trim();
                if (!string.IsNullOrEmpty(s))
                {
                    code.Append(s[0]);
                }
            }
            return code.ToString();
        }

        private void btnTestGenerateCode_Click(object sender, EventArgs e)
        {
            var labId = luPerformingLab.EditValue == null ? (short) 0 : (short) luPerformingLab.EditValue;
            if (labId > 0)
            {
                var lab = LabsRepository.FindById(labId);
                var prefix = lab.LabCode.ToUpperInvariant();
                var code = generateTestCode(txtTestName.Text);
                var suffix = txtTestCode.Text.Trim().ToUpperInvariant();
                if (!string.IsNullOrEmpty(suffix)) suffix = "." + suffix;
                txtTestCode.Text = string.Format("{0}.{1}{2}", prefix, code, suffix).Trim();
                txtTestCode.Select();
            }
        }

        private decimal textToDecimal(TextEdit edit)
        {
            var text = edit.Text.Trim();
            var value = 0m;
            decimal.TryParse(text, out value);
            return value;
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            if (!validateGeneralEditorControls()) return;

            var shortName = txtTestName.Text.Trim();
            var price = textToDecimal(txtListPrice);

            //var test = FaultHandler.Shield(() => (CurrentLabTest != null)
            //    ? LabTestsRepository.FindById(CurrentLabTest.Id)
            //    : LabTestsRepository.CreateNew(shortName, price));
            LabTest test = null;
            FaultHandler.Shield(() =>
            {
                var id = CurrentLabTest == null
                    ? LabTestsRepository.CreateNewLabTest(shortName,
                        price,
                        getSelectedPerformingLabId(),
                        getSelectedResultingLabId())
                    : CurrentLabTest.Id;
                LabTestsRepository.Refresh();
                test = LabTestsRepository.FindById(id);

                LabTestsRepository.UpdateLabTest(
                    test.Id,
                    true,
                    txtTestCode.Text.Trim().ToUpperInvariant(),
                    shortName,
                    shortName,
                    price,
                    0m,
                    0m,
                    (byte) ReqSlipGenerationOptionType.GenerateExclusiveSlip,
                    0,
                    (byte) SortPriorityType.Normal,
                    null,
                    getSelectedPerformingLabId(),
                    getSelectedResultingLabId(),
                    null,
                    (short?) luTatGroup.EditValue,
                    null,
                    null);
            });

            LabTestsRepository.Reset();
            // is this a newly added test?
            if (CurrentLabTest == null)
            {
                // just create a mock object with the valid object Id. it will be re-initialized 
                // by refreshLabTestSliceFromDatabase() below anyway
                CurrentLabTest = new LabTestSlice {Id = test.Id};
            }
            refreshLabTestSliceFromDatabase();
        }

        private short getSelectedPerformingLabId()
        {
            return luPerformingLab.EditValue == null ? (short) 0 : (short) luPerformingLab.EditValue;
        }

        private short getSelectedResultingLabId()
        {
            return luResultingLab.EditValue == null ? (short) 0 : (short) luResultingLab.EditValue;
        }

        private bool checkTextboxContent(TextEdit txt, string field)
        {
            if (string.IsNullOrEmpty(txt.Text.Trim()))
            {
                MessageDlg.Warning(string.Format("{0} cannot be empty!", field));
                txt.Select();
                return false;
            }

            return true;
        }

        private bool validateGeneralEditorControls()
        {
            if (!checkTextboxContent(txtTestCode, "Test Code")) return false;

            if (CurrentLabTest == null)
            {
                // this is a new test. check uniqueness of test code
                var code = txtTestCode.Text.Trim().ToUpperInvariant();
                var count = LabTestsRepository.GetLabTestCodeCount(code);
                if (count > 0)
                {
                    MessageDlg.Warning(string.Format("The test code {0} has already been used {1} time(s)", code, count));
                    txtTestCode.Select();
                    return false;
                }
            }

            if (!checkTextboxContent(txtTestName, "Short Name")) return false;

            if (!checkTextboxContent(txtListPrice, "List Price")) return false;

            var price = 0m;
            if (!decimal.TryParse(txtListPrice.Text.Trim(), out price) || price < 0m)
            {
                MessageDlg.Warning(string.Format("Invalid List Price of {0}!", price));
                txtListPrice.Select();
                return false;
            }

            if (luPerformingLab.EditValue == null)
            {
                MessageDlg.Warning("Please select the Performing Lab");
                luPerformingLab.Select();
                return false;
            }

            if (luTatGroup.EditValue == null)
            {
                MessageDlg.Warning("Please select the default Turn-around Time group");
                luTatGroup.Select();
                return false;
            }

            return true;
        }

        private void refreshLabTestSliceFromDatabase()
        {
            var cursor = WaitFormControl.WaitStart(this, "Loading data...");
            try
            {
                var test = LabTestsRepository.FindById(CurrentLabTest.Id);
                CurrentLabTest = LabTestSlice.AssembleFrom(test, true);
                UpdateControls();
            }
            finally
            {
                WaitFormControl.WaitEnd(this, cursor);
            }
        }
    }
}