﻿namespace LabMaestro.Controls.Win
{
    partial class AppReportTemplatesEditorDialog
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.gridControl = new DevExpress.XtraGrid.GridControl();
            this.appReportTemplateSliceBindingSource = new System.Windows.Forms.BindingSource();
            this.gridView = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colReportCode = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colTemplateId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.btnExit = new DevExpress.XtraEditors.SimpleButton();
            this.btnExport = new DevExpress.XtraEditors.SimpleButton();
            this.btnReplaceOld = new DevExpress.XtraEditors.SimpleButton();
            this.openFileDialog = new System.Windows.Forms.OpenFileDialog();
            this.saveFileDialog = new System.Windows.Forms.SaveFileDialog();
            this.btnSaveNew = new DevExpress.XtraEditors.SimpleButton();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.appReportTemplateSliceBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).BeginInit();
            this.SuspendLayout();
            // 
            // gridControl
            // 
            this.gridControl.DataSource = this.appReportTemplateSliceBindingSource;
            this.gridControl.Location = new System.Drawing.Point(12, 12);
            this.gridControl.MainView = this.gridView;
            this.gridControl.Name = "gridControl";
            this.gridControl.Size = new System.Drawing.Size(687, 350);
            this.gridControl.TabIndex = 0;
            this.gridControl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView});
            // 
            // appReportTemplateSliceBindingSource
            // 
            this.appReportTemplateSliceBindingSource.DataSource = typeof(LabMaestro.Domain.AppReportTemplateSlice);
            // 
            // gridView
            // 
            this.gridView.Appearance.EvenRow.BackColor = System.Drawing.Color.Honeydew;
            this.gridView.Appearance.EvenRow.Options.UseBackColor = true;
            this.gridView.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colReportCode,
            this.colTemplateId,
            this.colId});
            this.gridView.GridControl = this.gridControl;
            this.gridView.Name = "gridView";
            this.gridView.OptionsView.EnableAppearanceEvenRow = true;
            this.gridView.OptionsView.ShowGroupPanel = false;
            this.gridView.FocusedRowChanged += new DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventHandler(this.gridView_FocusedRowChanged);
            // 
            // colReportCode
            // 
            this.colReportCode.FieldName = "ReportCode";
            this.colReportCode.Name = "colReportCode";
            this.colReportCode.OptionsColumn.AllowEdit = false;
            this.colReportCode.OptionsColumn.AllowFocus = false;
            this.colReportCode.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colReportCode.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colReportCode.OptionsColumn.AllowShowHide = false;
            this.colReportCode.OptionsColumn.ReadOnly = true;
            this.colReportCode.Visible = true;
            this.colReportCode.VisibleIndex = 0;
            this.colReportCode.Width = 174;
            // 
            // colTemplateId
            // 
            this.colTemplateId.FieldName = "TemplateId";
            this.colTemplateId.Name = "colTemplateId";
            this.colTemplateId.OptionsColumn.AllowEdit = false;
            this.colTemplateId.OptionsColumn.AllowFocus = false;
            this.colTemplateId.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colTemplateId.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colTemplateId.OptionsColumn.AllowShowHide = false;
            this.colTemplateId.OptionsColumn.ReadOnly = true;
            this.colTemplateId.Visible = true;
            this.colTemplateId.VisibleIndex = 1;
            this.colTemplateId.Width = 174;
            // 
            // colId
            // 
            this.colId.FieldName = "Id";
            this.colId.Name = "colId";
            this.colId.OptionsColumn.AllowEdit = false;
            this.colId.OptionsColumn.AllowFocus = false;
            this.colId.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colId.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colId.OptionsColumn.AllowShowHide = false;
            this.colId.OptionsColumn.FixedWidth = true;
            this.colId.OptionsColumn.ReadOnly = true;
            this.colId.Visible = true;
            this.colId.VisibleIndex = 2;
            this.colId.Width = 50;
            // 
            // btnExit
            // 
            this.btnExit.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnExit.Appearance.Options.UseFont = true;
            this.btnExit.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.btnExit.Image = global::LabMaestro.Controls.Win.Resources.exit_16;
            this.btnExit.Location = new System.Drawing.Point(12, 368);
            this.btnExit.Name = "btnExit";
            this.btnExit.Size = new System.Drawing.Size(77, 23);
            this.btnExit.TabIndex = 7;
            this.btnExit.Text = "Exit";
            // 
            // btnExport
            // 
            this.btnExport.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnExport.Appearance.Options.UseFont = true;
            this.btnExport.Image = global::LabMaestro.Controls.Win.Resources.export_16;
            this.btnExport.Location = new System.Drawing.Point(257, 368);
            this.btnExport.Name = "btnExport";
            this.btnExport.Size = new System.Drawing.Size(150, 23);
            this.btnExport.TabIndex = 4;
            this.btnExport.Text = "Export to Disk (F6)";
            this.btnExport.ToolTip = "Save App Report Template To Disk";
            this.btnExport.Click += new System.EventHandler(this.btnContentExport_Click);
            // 
            // btnReplaceOld
            // 
            this.btnReplaceOld.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnReplaceOld.Appearance.Options.UseFont = true;
            this.btnReplaceOld.Image = global::LabMaestro.Controls.Win.Resources.open16;
            this.btnReplaceOld.Location = new System.Drawing.Point(413, 368);
            this.btnReplaceOld.Name = "btnReplaceOld";
            this.btnReplaceOld.Size = new System.Drawing.Size(156, 23);
            this.btnReplaceOld.TabIndex = 3;
            this.btnReplaceOld.Text = "Replace In-place (F7)";
            this.btnReplaceOld.ToolTip = "Import and Replace App Report Template From Disk";
            this.btnReplaceOld.Click += new System.EventHandler(this.btnReplaceOld_Click);
            // 
            // openFileDialog
            // 
            this.openFileDialog.FileName = "openFileDialog1";
            // 
            // btnSaveNew
            // 
            this.btnSaveNew.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnSaveNew.Appearance.Options.UseFont = true;
            this.btnSaveNew.Image = global::LabMaestro.Controls.Win.Resources.new16;
            this.btnSaveNew.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleLeft;
            this.btnSaveNew.Location = new System.Drawing.Point(575, 368);
            this.btnSaveNew.LookAndFeel.UseDefaultLookAndFeel = false;
            this.btnSaveNew.Name = "btnSaveNew";
            this.btnSaveNew.Size = new System.Drawing.Size(124, 23);
            this.btnSaveNew.TabIndex = 5;
            this.btnSaveNew.Text = "Save New (F8)";
            this.btnSaveNew.ToolTip = "Import App Report Template From Disk and Create New Item";
            this.btnSaveNew.Click += new System.EventHandler(this.btnSaveNew_Click);
            // 
            // AppReportTemplatesEditorDialog
            // 
            this.Appearance.Options.UseFont = true;
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.ClientSize = new System.Drawing.Size(711, 403);
            this.Controls.Add(this.btnReplaceOld);
            this.Controls.Add(this.btnExport);
            this.Controls.Add(this.btnSaveNew);
            this.Controls.Add(this.btnExit);
            this.Controls.Add(this.gridControl);
            this.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.KeyPreview = true;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "AppReportTemplatesEditorDialog";
            this.SizeGripStyle = System.Windows.Forms.SizeGripStyle.Hide;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "AppReportTemplatesEditorDialog";
            this.KeyUp += new System.Windows.Forms.KeyEventHandler(this.AppReportTemplatesEditorDialog_KeyUp);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.appReportTemplateSliceBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControl;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView;
        private System.Windows.Forms.BindingSource appReportTemplateSliceBindingSource;
        private DevExpress.XtraEditors.SimpleButton btnExit;
        private DevExpress.XtraEditors.SimpleButton btnExport;
        private DevExpress.XtraEditors.SimpleButton btnReplaceOld;
        private System.Windows.Forms.OpenFileDialog openFileDialog;
        private System.Windows.Forms.SaveFileDialog saveFileDialog;
        private DevExpress.XtraGrid.Columns.GridColumn colReportCode;
        private DevExpress.XtraGrid.Columns.GridColumn colTemplateId;
        private DevExpress.XtraGrid.Columns.GridColumn colId;
        private DevExpress.XtraEditors.SimpleButton btnSaveNew;
    }
}