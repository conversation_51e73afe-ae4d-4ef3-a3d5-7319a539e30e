﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ReferrerClassCategoryEditorDialog.cs 516 2013-04-17 08:03:02Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Globalization;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using LabMaestro.Domain;
using LabMaestro.Shared;

namespace LabMaestro.Controls.Win
{
    public partial class ReferrerClassCategoryEditorDialog : XtraForm
    {
        private readonly bool _editRefClass;
        private bool _isNewItem;

        public ReferrerClassCategoryEditorDialog(bool editRefClass)
        {
            //AuthHelper.GuardAccess();
            _editRefClass = editRefClass;
            InitializeComponent();
            WinUtils.ApplyTheme(this);
            Text = _editRefClass ? "Physician Referral Class" : "Referring Physician Category";
            chkIsActive.Text = _editRefClass ? "Eligible for Referral?" : "Is Active?";

            _isNewItem = false;
            GridControl.Columns.Clear();
            GridControl.GridAddColumn(@"id", "Id", 0, 40);
            GridControl.GridAddColumn(@"name", "Name", 1, 170);
            GridControl.GridAddColumn(@"active", _editRefClass ? "Eligible?" : "Active?", 2, 70);
        }

        public void UpdateControls()
        {
            _isNewItem = false;
            resetEditorControls();
            populateGridControl();
            GridControl.Focus();
        }

        private void GridControl_SelectedRowsChanged(object sender, EventArgs e)
        {
            populateEditorControls();
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        private void btnNew_Click(object sender, EventArgs e)
        {
            _isNewItem = true;
            resetEditorControls();
            chkIsActive.Checked = true;
            enableButtons();
            txtName.Focus();
        }

        private void btnReset_Click(object sender, EventArgs e)
        {
            if (!_isNewItem)
            {
                populateEditorControls();
            }
            else
            {
                resetEditorControls();
            }

            enableButtons();
            txtName.Focus();
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            var name = txtName.Text.Trim();

            if (string.IsNullOrEmpty(name))
            {
                MessageDlg.Error("Name must not be empty!");
                return;
            }

            if (_isNewItem)
            {
                if (_editRefClass)
                {
                    var item = new ReferralClass {ReferralEligible = chkIsActive.Checked, Name = name};
                    ReferralClassesRepository.Add(item);
                    ReferralClassesRepository.Save();
                }
                else
                {
                    var item = new ReferrerCategory { IsActive = chkIsActive.Checked, Name = name };
                    ReferrerCategoryRepository.Add(item);
                    ReferrerCategoryRepository.Save();
                }
            }
            else
            {
                if (_editRefClass)
                {
                    var item = getSelectedClass();
                    item.ReferralEligible = chkIsActive.Checked;
                    item.Name = name;
                    ReferralClassesRepository.Save();
                }
                else
                {
                    var item = getSelectedCategory();
                    item.IsActive = chkIsActive.Checked;
                    item.Name = name;
                    ReferrerCategoryRepository.Save();
                }
            }

            UpdateControls();
        }

        private void enableButtons()
        {
            if (_isNewItem)
            {
                btnNew.Enabled = false;
                btnReset.Enabled = false;
            }
            else
            {
                btnNew.Enabled = true;
                btnReset.Enabled = true;
            }
        }

        private ReferrerCategory getSelectedCategory()
        {
            if ((GridControl.SelectedRows.Count == 1) && (GridControl.SelectedRows[0] != null))
            {
                return GridControl.SelectedRows[0].Tag as ReferrerCategory;
            }
            return null;
        }

        private ReferralClass getSelectedClass()
        {
            if ((GridControl.SelectedRows.Count == 1) && (GridControl.SelectedRows[0] != null))
            {
                return GridControl.SelectedRows[0].Tag as ReferralClass;
            }
            return null;
        }

        private void populateEditorControls()
        {
            resetEditorControls();

            if (_editRefClass)
            {
                var item = getSelectedClass();
                if (item != null)
                {
                    lblId.Text = item.Id.ToString(CultureInfo.InvariantCulture);
                    txtName.Text = item.Name;
                    chkIsActive.Checked = item.ReferralEligible;
                }
            }
            else
            {
                var item = getSelectedCategory();
                if (item != null)
                {
                    lblId.Text = item.Id.ToString(CultureInfo.InvariantCulture);
                    txtName.Text = item.Name;
                    chkIsActive.Checked = item.IsActive;
                }
            }
            enableButtons();
        }

        private void populateGridControl()
        {
            GridControl.DataRows.Clear();
            if (_editRefClass)
            {
                var items = ReferralClassesRepository.FetchAll().OrderBy(x => x.Name).ToList();
                foreach (var item in items)
                {
                    var row = GridControl.DataRows.AddNew();
                    row.Cells["id"].Value = item.Id.ToString();
                    row.Cells["name"].Value = item.Name;
                    row.Cells["active"].Value = SharedUtilities.BooleanToStringYN(item.ReferralEligible);
                    row.Tag = item;
                    row.Height = 23;
                    row.EndEdit();
                }
            }
            else
            {
                var items = ReferrerCategoryRepository.FindAllItems().OrderBy(x => x.Name).ToList();
                foreach (var item in items)
                {
                    var row = GridControl.DataRows.AddNew();
                    row.Cells["id"].Value = item.Id.ToString();
                    row.Cells["name"].Value = item.Name;
                    row.Cells["active"].Value = SharedUtilities.BooleanToStringYN(item.IsActive);
                    row.Tag = item;
                    row.Height = 23;
                    row.EndEdit();
                }
            }
        }

        private void resetEditorControls()
        {
            lblId.Text = string.Empty;
            txtName.Text = string.Empty;
            chkIsActive.Checked = false;
        }
    }
}