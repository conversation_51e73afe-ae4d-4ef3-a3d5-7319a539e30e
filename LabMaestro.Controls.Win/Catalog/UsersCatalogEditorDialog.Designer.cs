﻿namespace LabMaestro.Controls.Win
{
    partial class UsersCatalogEditorDialog
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            Xceed.Grid.GradientMap gradientMap2 = new Xceed.Grid.GradientMap();
            Xceed.Grid.GradientStop gradientStop3 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientStop gradientStop4 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientMap gradientMap1 = new Xceed.Grid.GradientMap();
            Xceed.Grid.GradientStop gradientStop1 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientStop gradientStop2 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientMap gradientMap4 = new Xceed.Grid.GradientMap();
            Xceed.Grid.GradientStop gradientStop7 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientStop gradientStop8 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientMap gradientMap3 = new Xceed.Grid.GradientMap();
            Xceed.Grid.GradientStop gradientStop5 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientStop gradientStop6 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientMap gradientMap6 = new Xceed.Grid.GradientMap();
            Xceed.Grid.GradientStop gradientStop11 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientStop gradientStop12 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientMap gradientMap5 = new Xceed.Grid.GradientMap();
            Xceed.Grid.GradientStop gradientStop9 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientStop gradientStop10 = new Xceed.Grid.GradientStop();
            this.splitContainerControl = new DevExpress.XtraEditors.SplitContainerControl();
            this.grdRoles = new Xceed.Grid.GridControl();
            this.dataRow1 = new Xceed.Grid.DataRow();
            this.visualGridElementStyle1 = new Xceed.Grid.VisualGridElementStyle();
            this.visualGridElementStyle3 = new Xceed.Grid.VisualGridElementStyle();
            this.columnManagerRow1 = new Xceed.Grid.ColumnManagerRow();
            this.grdDepartments = new Xceed.Grid.GridControl();
            this.dataRow2 = new Xceed.Grid.DataRow();
            this.visualGridElementStyle4 = new Xceed.Grid.VisualGridElementStyle();
            this.visualGridElementStyle5 = new Xceed.Grid.VisualGridElementStyle();
            this.columnManagerRow3 = new Xceed.Grid.ColumnManagerRow();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.tabFilter = new Syncfusion.Windows.Forms.Tools.TabControlAdv();
            this.tabPageAdv28 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv29 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv30 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv31 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv32 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv33 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv34 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv35 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv36 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv37 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv38 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv39 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv40 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv41 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv42 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv43 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv44 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv45 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv46 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv47 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv48 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv49 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv50 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv51 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv52 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv53 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv54 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.grdUsers = new Xceed.Grid.GridControl();
            this.dataRowTemplate2 = new Xceed.Grid.DataRow();
            this.visualGridElementStyle2 = new Xceed.Grid.VisualGridElementStyle();
            this.dataRowStyle1 = new Xceed.Grid.VisualGridElementStyle();
            this.columnManagerRow2 = new Xceed.Grid.ColumnManagerRow();
            this.barManager = new DevExpress.XtraBars.BarManager(this.components);
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.btnNewUser = new DevExpress.XtraBars.BarButtonItem();
            this.btnExit = new DevExpress.XtraBars.BarButtonItem();
            this.bar3 = new DevExpress.XtraBars.Bar();
            this.barStaticItem1 = new DevExpress.XtraBars.BarStaticItem();
            this.barAndDockingController = new DevExpress.XtraBars.BarAndDockingController(this.components);
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl)).BeginInit();
            this.splitContainerControl.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grdRoles)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataRow1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.columnManagerRow1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdDepartments)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataRow2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.columnManagerRow3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabFilter)).BeginInit();
            this.tabFilter.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grdUsers)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataRowTemplate2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.columnManagerRow2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController)).BeginInit();
            this.SuspendLayout();
            // 
            // splitContainerControl
            // 
            this.splitContainerControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl.IsSplitterFixed = true;
            this.splitContainerControl.Location = new System.Drawing.Point(0, 31);
            this.splitContainerControl.LookAndFeel.UseDefaultLookAndFeel = false;
            this.splitContainerControl.Name = "splitContainerControl";
            this.splitContainerControl.Panel1.Controls.Add(this.grdRoles);
            this.splitContainerControl.Panel1.Controls.Add(this.grdDepartments);
            this.splitContainerControl.Panel1.Controls.Add(this.labelControl2);
            this.splitContainerControl.Panel1.Controls.Add(this.labelControl1);
            this.splitContainerControl.Panel1.Text = "Panel1";
            this.splitContainerControl.Panel2.Controls.Add(this.tabFilter);
            this.splitContainerControl.Panel2.Controls.Add(this.grdUsers);
            this.splitContainerControl.Panel2.Text = "Panel2";
            this.splitContainerControl.Size = new System.Drawing.Size(1051, 625);
            this.splitContainerControl.SplitterPosition = 310;
            this.splitContainerControl.TabIndex = 5;
            this.splitContainerControl.Text = "splitContainerControl1";
            // 
            // grdRoles
            // 
            this.grdRoles.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.grdRoles.ClipCurrentCellSelection = false;
            this.grdRoles.DataRowTemplate = this.dataRow1;
            this.grdRoles.DataRowTemplateStyles.Add(this.visualGridElementStyle1);
            this.grdRoles.DataRowTemplateStyles.Add(this.visualGridElementStyle3);
            // 
            // 
            // 
            this.grdRoles.ErrorVisualStyle.OverrideUIStyle = false;
            // 
            // 
            // 
            this.grdRoles.FixedColumnSplitter.AllowRepositioning = false;
            this.grdRoles.FixedColumnSplitter.Visible = false;
            this.grdRoles.FixedHeaderRows.Add(this.columnManagerRow1);
            this.grdRoles.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.grdRoles.GridLineColor = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(239)))), ((int)(((byte)(255)))));
            this.grdRoles.GridLineStyle = System.Drawing.Drawing2D.DashStyle.Dot;
            this.grdRoles.InactiveSelectionBackColor = System.Drawing.Color.RoyalBlue;
            // 
            // 
            // 
            this.grdRoles.InactiveSelectionVisualStyle.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(229)))), ((int)(((byte)(229)))), ((int)(((byte)(229)))));
            this.grdRoles.InactiveSelectionVisualStyle.ForeColor = System.Drawing.Color.Black;
            this.grdRoles.InactiveSelectionVisualStyle.OverrideUIStyle = true;
            this.grdRoles.Location = new System.Drawing.Point(0, 29);
            this.grdRoles.Margin = new System.Windows.Forms.Padding(5);
            this.grdRoles.Name = "grdRoles";
            this.grdRoles.OverrideUIStyle = false;
            this.grdRoles.ReadOnly = true;
            // 
            // 
            // 
            this.grdRoles.RowSelectorPane.AllowRowResize = false;
            this.grdRoles.RowSelectorPane.CurrentImageIndex = 16;
            gradientMap2.GradientMode = Xceed.UI.GradientMode.Horizontal;
            gradientStop3.Color = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(239)))), ((int)(((byte)(255)))));
            gradientStop3.Offset = 0D;
            gradientStop4.Color = System.Drawing.Color.FromArgb(((int)(((byte)(175)))), ((int)(((byte)(210)))), ((int)(((byte)(255)))));
            gradientStop4.Offset = 1D;
            gradientMap2.GradientStops.Add(gradientStop3);
            gradientMap2.GradientStops.Add(gradientStop4);
            this.grdRoles.RowSelectorPane.GradientMap = gradientMap2;
            // 
            // 
            // 
            this.grdRoles.RowSelectorPane.HotVisualStyle.OverrideUIStyle = true;
            this.grdRoles.RowSelectorPane.OverrideUIStyle = true;
            this.grdRoles.RowSelectorPane.SelectedImageIndex = 20;
            this.grdRoles.RowSelectorPane.Visible = false;
            this.grdRoles.RowSelectorPane.Width = 17;
            this.grdRoles.ScrollBars = Xceed.Grid.GridScrollBars.Both;
            this.grdRoles.SelectionBackColor = System.Drawing.Color.Indigo;
            this.grdRoles.SelectionMode = System.Windows.Forms.SelectionMode.One;
            // 
            // 
            // 
            this.grdRoles.SelectionVisualStyle.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(167)))), ((int)(((byte)(205)))), ((int)(((byte)(240)))));
            this.grdRoles.SelectionVisualStyle.ForeColor = System.Drawing.Color.Black;
            this.grdRoles.SelectionVisualStyle.OverrideUIStyle = true;
            this.grdRoles.ShowFocusRectangle = false;
            // 
            // 
            // 
            this.grdRoles.SideMargin.Visible = false;
            this.grdRoles.Size = new System.Drawing.Size(310, 379);
            this.grdRoles.SynchronizeDetailGrids = false;
            this.grdRoles.TabIndex = 35;
            this.grdRoles.TabStop = false;
            this.grdRoles.TreeLineColor = System.Drawing.Color.Silver;
            this.grdRoles.UIStyle = Xceed.UI.UIStyle.System;
            this.grdRoles.SelectedRowsChanged += new System.EventHandler(this.grdRoles_SelectedRowsChanged);
            // 
            // visualGridElementStyle1
            // 
            // 
            // 
            // 
            this.visualGridElementStyle1.HotVisualStyle.OverrideUIStyle = true;
            this.visualGridElementStyle1.OverrideUIStyle = false;
            // 
            // visualGridElementStyle3
            // 
            this.visualGridElementStyle3.BackColor = System.Drawing.Color.AliceBlue;
            // 
            // columnManagerRow1
            // 
            this.columnManagerRow1.AllowColumnReorder = false;
            this.columnManagerRow1.AllowColumnResize = true;
            this.columnManagerRow1.AllowSort = false;
            this.columnManagerRow1.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            gradientStop1.Color = System.Drawing.Color.FromArgb(((int)(((byte)(252)))), ((int)(((byte)(253)))), ((int)(((byte)(255)))));
            gradientStop1.Offset = 0D;
            gradientStop2.Color = System.Drawing.Color.FromArgb(((int)(((byte)(196)))), ((int)(((byte)(221)))), ((int)(((byte)(255)))));
            gradientStop2.Offset = 1D;
            gradientMap1.GradientStops.Add(gradientStop1);
            gradientMap1.GradientStops.Add(gradientStop2);
            this.columnManagerRow1.GradientMap = gradientMap1;
            this.columnManagerRow1.Height = 23;
            // 
            // 
            // 
            this.columnManagerRow1.HotVisualStyle.OverrideUIStyle = false;
            this.columnManagerRow1.OverrideUIStyle = true;
            // 
            // grdDepartments
            // 
            this.grdDepartments.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.grdDepartments.ClipCurrentCellSelection = false;
            this.grdDepartments.DataRowTemplate = this.dataRow2;
            this.grdDepartments.DataRowTemplateStyles.Add(this.visualGridElementStyle4);
            this.grdDepartments.DataRowTemplateStyles.Add(this.visualGridElementStyle5);
            // 
            // 
            // 
            this.grdDepartments.ErrorVisualStyle.OverrideUIStyle = false;
            // 
            // 
            // 
            this.grdDepartments.FixedColumnSplitter.AllowRepositioning = false;
            this.grdDepartments.FixedColumnSplitter.Visible = false;
            this.grdDepartments.FixedHeaderRows.Add(this.columnManagerRow3);
            this.grdDepartments.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.grdDepartments.GridLineColor = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(239)))), ((int)(((byte)(255)))));
            this.grdDepartments.GridLineStyle = System.Drawing.Drawing2D.DashStyle.Dot;
            this.grdDepartments.InactiveSelectionBackColor = System.Drawing.Color.RoyalBlue;
            // 
            // 
            // 
            this.grdDepartments.InactiveSelectionVisualStyle.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(229)))), ((int)(((byte)(229)))), ((int)(((byte)(229)))));
            this.grdDepartments.InactiveSelectionVisualStyle.ForeColor = System.Drawing.Color.Black;
            this.grdDepartments.InactiveSelectionVisualStyle.OverrideUIStyle = true;
            this.grdDepartments.Location = new System.Drawing.Point(0, 445);
            this.grdDepartments.Margin = new System.Windows.Forms.Padding(5);
            this.grdDepartments.Name = "grdDepartments";
            this.grdDepartments.OverrideUIStyle = false;
            this.grdDepartments.ReadOnly = true;
            // 
            // 
            // 
            this.grdDepartments.RowSelectorPane.AllowRowResize = false;
            this.grdDepartments.RowSelectorPane.CurrentImageIndex = 16;
            gradientMap4.GradientMode = Xceed.UI.GradientMode.Horizontal;
            gradientStop7.Color = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(239)))), ((int)(((byte)(255)))));
            gradientStop7.Offset = 0D;
            gradientStop8.Color = System.Drawing.Color.FromArgb(((int)(((byte)(175)))), ((int)(((byte)(210)))), ((int)(((byte)(255)))));
            gradientStop8.Offset = 1D;
            gradientMap4.GradientStops.Add(gradientStop7);
            gradientMap4.GradientStops.Add(gradientStop8);
            this.grdDepartments.RowSelectorPane.GradientMap = gradientMap4;
            // 
            // 
            // 
            this.grdDepartments.RowSelectorPane.HotVisualStyle.OverrideUIStyle = true;
            this.grdDepartments.RowSelectorPane.OverrideUIStyle = true;
            this.grdDepartments.RowSelectorPane.SelectedImageIndex = 20;
            this.grdDepartments.RowSelectorPane.Visible = false;
            this.grdDepartments.RowSelectorPane.Width = 17;
            this.grdDepartments.ScrollBars = Xceed.Grid.GridScrollBars.Both;
            this.grdDepartments.SelectionBackColor = System.Drawing.Color.Indigo;
            this.grdDepartments.SelectionMode = System.Windows.Forms.SelectionMode.One;
            // 
            // 
            // 
            this.grdDepartments.SelectionVisualStyle.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(167)))), ((int)(((byte)(205)))), ((int)(((byte)(240)))));
            this.grdDepartments.SelectionVisualStyle.ForeColor = System.Drawing.Color.Black;
            this.grdDepartments.SelectionVisualStyle.OverrideUIStyle = true;
            this.grdDepartments.ShowFocusRectangle = false;
            // 
            // 
            // 
            this.grdDepartments.SideMargin.Visible = false;
            this.grdDepartments.Size = new System.Drawing.Size(310, 179);
            this.grdDepartments.SynchronizeDetailGrids = false;
            this.grdDepartments.TabIndex = 34;
            this.grdDepartments.TabStop = false;
            this.grdDepartments.TreeLineColor = System.Drawing.Color.Silver;
            this.grdDepartments.UIStyle = Xceed.UI.UIStyle.System;
            // 
            // visualGridElementStyle4
            // 
            // 
            // 
            // 
            this.visualGridElementStyle4.HotVisualStyle.OverrideUIStyle = true;
            this.visualGridElementStyle4.OverrideUIStyle = false;
            // 
            // visualGridElementStyle5
            // 
            this.visualGridElementStyle5.BackColor = System.Drawing.Color.AliceBlue;
            // 
            // columnManagerRow3
            // 
            this.columnManagerRow3.AllowColumnReorder = false;
            this.columnManagerRow3.AllowColumnResize = true;
            this.columnManagerRow3.AllowSort = false;
            this.columnManagerRow3.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            gradientStop5.Color = System.Drawing.Color.FromArgb(((int)(((byte)(252)))), ((int)(((byte)(253)))), ((int)(((byte)(255)))));
            gradientStop5.Offset = 0D;
            gradientStop6.Color = System.Drawing.Color.FromArgb(((int)(((byte)(196)))), ((int)(((byte)(221)))), ((int)(((byte)(255)))));
            gradientStop6.Offset = 1D;
            gradientMap3.GradientStops.Add(gradientStop5);
            gradientMap3.GradientStops.Add(gradientStop6);
            this.columnManagerRow3.GradientMap = gradientMap3;
            this.columnManagerRow3.Height = 23;
            // 
            // 
            // 
            this.columnManagerRow3.HotVisualStyle.OverrideUIStyle = false;
            this.columnManagerRow3.OverrideUIStyle = true;
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.BackColor = System.Drawing.Color.Honeydew;
            this.labelControl2.Appearance.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Appearance.ForeColor = System.Drawing.Color.Crimson;
            this.labelControl2.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None;
            this.labelControl2.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.labelControl2.LineColor = System.Drawing.Color.DimGray;
            this.labelControl2.LineLocation = DevExpress.XtraEditors.LineLocation.Center;
            this.labelControl2.LineStyle = System.Drawing.Drawing2D.DashStyle.Dot;
            this.labelControl2.LineVisible = true;
            this.labelControl2.Location = new System.Drawing.Point(0, 416);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(310, 30);
            this.labelControl2.TabIndex = 33;
            this.labelControl2.Text = "  Departments  ";
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.labelControl1.Appearance.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Appearance.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.labelControl1.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None;
            this.labelControl1.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.labelControl1.Dock = System.Windows.Forms.DockStyle.Top;
            this.labelControl1.LineColor = System.Drawing.Color.DimGray;
            this.labelControl1.LineLocation = DevExpress.XtraEditors.LineLocation.Center;
            this.labelControl1.LineStyle = System.Drawing.Drawing2D.DashStyle.Dot;
            this.labelControl1.LineVisible = true;
            this.labelControl1.Location = new System.Drawing.Point(0, 0);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(310, 30);
            this.labelControl1.TabIndex = 32;
            this.labelControl1.Text = "  Roles  ";
            // 
            // tabFilter
            // 
            this.tabFilter.Controls.Add(this.tabPageAdv28);
            this.tabFilter.Controls.Add(this.tabPageAdv29);
            this.tabFilter.Controls.Add(this.tabPageAdv30);
            this.tabFilter.Controls.Add(this.tabPageAdv31);
            this.tabFilter.Controls.Add(this.tabPageAdv32);
            this.tabFilter.Controls.Add(this.tabPageAdv33);
            this.tabFilter.Controls.Add(this.tabPageAdv34);
            this.tabFilter.Controls.Add(this.tabPageAdv35);
            this.tabFilter.Controls.Add(this.tabPageAdv36);
            this.tabFilter.Controls.Add(this.tabPageAdv37);
            this.tabFilter.Controls.Add(this.tabPageAdv38);
            this.tabFilter.Controls.Add(this.tabPageAdv39);
            this.tabFilter.Controls.Add(this.tabPageAdv40);
            this.tabFilter.Controls.Add(this.tabPageAdv41);
            this.tabFilter.Controls.Add(this.tabPageAdv42);
            this.tabFilter.Controls.Add(this.tabPageAdv43);
            this.tabFilter.Controls.Add(this.tabPageAdv44);
            this.tabFilter.Controls.Add(this.tabPageAdv45);
            this.tabFilter.Controls.Add(this.tabPageAdv46);
            this.tabFilter.Controls.Add(this.tabPageAdv47);
            this.tabFilter.Controls.Add(this.tabPageAdv48);
            this.tabFilter.Controls.Add(this.tabPageAdv49);
            this.tabFilter.Controls.Add(this.tabPageAdv50);
            this.tabFilter.Controls.Add(this.tabPageAdv51);
            this.tabFilter.Controls.Add(this.tabPageAdv52);
            this.tabFilter.Controls.Add(this.tabPageAdv53);
            this.tabFilter.Controls.Add(this.tabPageAdv54);
            this.tabFilter.Dock = System.Windows.Forms.DockStyle.Top;
            this.tabFilter.Location = new System.Drawing.Point(0, 0);
            this.tabFilter.Name = "tabFilter";
            this.tabFilter.ShowScroll = false;
            this.tabFilter.Size = new System.Drawing.Size(736, 30);
            this.tabFilter.SizeMode = Syncfusion.Windows.Forms.Tools.TabSizeMode.ShrinkToFit;
            this.tabFilter.SwitchPagesForDialogKeys = false;
            this.tabFilter.TabGap = 4;
            this.tabFilter.TabIndex = 34;
            this.tabFilter.TabStop = false;
            this.tabFilter.TabStyle = typeof(Syncfusion.Windows.Forms.Tools.TabRendererVS2010);
            this.tabFilter.ThemesEnabled = true;
            this.tabFilter.SelectedIndexChanged += new System.EventHandler(this.tabFilter_SelectedIndexChanged);
            // 
            // tabPageAdv28
            // 
            this.tabPageAdv28.Image = null;
            this.tabPageAdv28.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv28.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv28.Name = "tabPageAdv28";
            this.tabPageAdv28.ShowCloseButton = true;
            this.tabPageAdv28.Size = new System.Drawing.Size(729, 0);
            this.tabPageAdv28.TabIndex = 28;
            this.tabPageAdv28.Text = "*";
            this.tabPageAdv28.ThemesEnabled = true;
            // 
            // tabPageAdv29
            // 
            this.tabPageAdv29.Image = null;
            this.tabPageAdv29.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv29.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv29.Name = "tabPageAdv29";
            this.tabPageAdv29.ShowCloseButton = true;
            this.tabPageAdv29.Size = new System.Drawing.Size(729, 0);
            this.tabPageAdv29.TabIndex = 29;
            this.tabPageAdv29.Text = "A";
            this.tabPageAdv29.ThemesEnabled = true;
            // 
            // tabPageAdv30
            // 
            this.tabPageAdv30.Image = null;
            this.tabPageAdv30.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv30.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv30.Name = "tabPageAdv30";
            this.tabPageAdv30.ShowCloseButton = true;
            this.tabPageAdv30.Size = new System.Drawing.Size(729, 0);
            this.tabPageAdv30.TabIndex = 30;
            this.tabPageAdv30.Text = "B";
            this.tabPageAdv30.ThemesEnabled = true;
            // 
            // tabPageAdv31
            // 
            this.tabPageAdv31.Image = null;
            this.tabPageAdv31.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv31.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv31.Name = "tabPageAdv31";
            this.tabPageAdv31.ShowCloseButton = true;
            this.tabPageAdv31.Size = new System.Drawing.Size(729, 0);
            this.tabPageAdv31.TabIndex = 31;
            this.tabPageAdv31.Text = "C";
            this.tabPageAdv31.ThemesEnabled = true;
            // 
            // tabPageAdv32
            // 
            this.tabPageAdv32.Image = null;
            this.tabPageAdv32.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv32.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv32.Name = "tabPageAdv32";
            this.tabPageAdv32.ShowCloseButton = true;
            this.tabPageAdv32.Size = new System.Drawing.Size(729, 0);
            this.tabPageAdv32.TabIndex = 32;
            this.tabPageAdv32.Text = "D";
            this.tabPageAdv32.ThemesEnabled = true;
            // 
            // tabPageAdv33
            // 
            this.tabPageAdv33.Image = null;
            this.tabPageAdv33.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv33.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv33.Name = "tabPageAdv33";
            this.tabPageAdv33.ShowCloseButton = true;
            this.tabPageAdv33.Size = new System.Drawing.Size(729, 0);
            this.tabPageAdv33.TabIndex = 33;
            this.tabPageAdv33.Text = "E";
            this.tabPageAdv33.ThemesEnabled = true;
            // 
            // tabPageAdv34
            // 
            this.tabPageAdv34.Image = null;
            this.tabPageAdv34.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv34.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv34.Name = "tabPageAdv34";
            this.tabPageAdv34.ShowCloseButton = true;
            this.tabPageAdv34.Size = new System.Drawing.Size(729, 0);
            this.tabPageAdv34.TabIndex = 34;
            this.tabPageAdv34.Text = "F";
            this.tabPageAdv34.ThemesEnabled = true;
            // 
            // tabPageAdv35
            // 
            this.tabPageAdv35.Image = null;
            this.tabPageAdv35.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv35.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv35.Name = "tabPageAdv35";
            this.tabPageAdv35.ShowCloseButton = true;
            this.tabPageAdv35.Size = new System.Drawing.Size(729, 0);
            this.tabPageAdv35.TabIndex = 35;
            this.tabPageAdv35.Text = "G";
            this.tabPageAdv35.ThemesEnabled = true;
            // 
            // tabPageAdv36
            // 
            this.tabPageAdv36.Image = null;
            this.tabPageAdv36.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv36.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv36.Name = "tabPageAdv36";
            this.tabPageAdv36.ShowCloseButton = true;
            this.tabPageAdv36.Size = new System.Drawing.Size(729, 0);
            this.tabPageAdv36.TabIndex = 36;
            this.tabPageAdv36.Text = "H";
            this.tabPageAdv36.ThemesEnabled = true;
            // 
            // tabPageAdv37
            // 
            this.tabPageAdv37.Image = null;
            this.tabPageAdv37.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv37.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv37.Name = "tabPageAdv37";
            this.tabPageAdv37.ShowCloseButton = true;
            this.tabPageAdv37.Size = new System.Drawing.Size(729, 0);
            this.tabPageAdv37.TabIndex = 37;
            this.tabPageAdv37.Text = "I";
            this.tabPageAdv37.ThemesEnabled = true;
            // 
            // tabPageAdv38
            // 
            this.tabPageAdv38.Image = null;
            this.tabPageAdv38.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv38.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv38.Name = "tabPageAdv38";
            this.tabPageAdv38.ShowCloseButton = true;
            this.tabPageAdv38.Size = new System.Drawing.Size(729, 0);
            this.tabPageAdv38.TabIndex = 38;
            this.tabPageAdv38.Text = "J";
            this.tabPageAdv38.ThemesEnabled = true;
            // 
            // tabPageAdv39
            // 
            this.tabPageAdv39.Image = null;
            this.tabPageAdv39.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv39.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv39.Name = "tabPageAdv39";
            this.tabPageAdv39.ShowCloseButton = true;
            this.tabPageAdv39.Size = new System.Drawing.Size(729, 0);
            this.tabPageAdv39.TabIndex = 39;
            this.tabPageAdv39.Text = "K";
            this.tabPageAdv39.ThemesEnabled = true;
            // 
            // tabPageAdv40
            // 
            this.tabPageAdv40.Image = null;
            this.tabPageAdv40.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv40.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv40.Name = "tabPageAdv40";
            this.tabPageAdv40.ShowCloseButton = true;
            this.tabPageAdv40.Size = new System.Drawing.Size(729, 0);
            this.tabPageAdv40.TabIndex = 40;
            this.tabPageAdv40.Text = "L";
            this.tabPageAdv40.ThemesEnabled = true;
            // 
            // tabPageAdv41
            // 
            this.tabPageAdv41.Image = null;
            this.tabPageAdv41.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv41.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv41.Name = "tabPageAdv41";
            this.tabPageAdv41.ShowCloseButton = true;
            this.tabPageAdv41.Size = new System.Drawing.Size(729, 0);
            this.tabPageAdv41.TabIndex = 41;
            this.tabPageAdv41.Text = "M";
            this.tabPageAdv41.ThemesEnabled = true;
            // 
            // tabPageAdv42
            // 
            this.tabPageAdv42.Image = null;
            this.tabPageAdv42.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv42.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv42.Name = "tabPageAdv42";
            this.tabPageAdv42.ShowCloseButton = true;
            this.tabPageAdv42.Size = new System.Drawing.Size(729, 0);
            this.tabPageAdv42.TabIndex = 42;
            this.tabPageAdv42.Text = "N";
            this.tabPageAdv42.ThemesEnabled = true;
            // 
            // tabPageAdv43
            // 
            this.tabPageAdv43.Image = null;
            this.tabPageAdv43.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv43.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv43.Name = "tabPageAdv43";
            this.tabPageAdv43.ShowCloseButton = true;
            this.tabPageAdv43.Size = new System.Drawing.Size(729, 0);
            this.tabPageAdv43.TabIndex = 43;
            this.tabPageAdv43.Text = "O";
            this.tabPageAdv43.ThemesEnabled = true;
            // 
            // tabPageAdv44
            // 
            this.tabPageAdv44.Image = null;
            this.tabPageAdv44.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv44.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv44.Name = "tabPageAdv44";
            this.tabPageAdv44.ShowCloseButton = true;
            this.tabPageAdv44.Size = new System.Drawing.Size(729, 0);
            this.tabPageAdv44.TabIndex = 44;
            this.tabPageAdv44.Text = "P";
            this.tabPageAdv44.ThemesEnabled = true;
            // 
            // tabPageAdv45
            // 
            this.tabPageAdv45.Image = null;
            this.tabPageAdv45.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv45.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv45.Name = "tabPageAdv45";
            this.tabPageAdv45.ShowCloseButton = true;
            this.tabPageAdv45.Size = new System.Drawing.Size(729, 0);
            this.tabPageAdv45.TabIndex = 45;
            this.tabPageAdv45.Text = "Q";
            this.tabPageAdv45.ThemesEnabled = true;
            // 
            // tabPageAdv46
            // 
            this.tabPageAdv46.Image = null;
            this.tabPageAdv46.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv46.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv46.Name = "tabPageAdv46";
            this.tabPageAdv46.ShowCloseButton = true;
            this.tabPageAdv46.Size = new System.Drawing.Size(729, 0);
            this.tabPageAdv46.TabIndex = 46;
            this.tabPageAdv46.Text = "R";
            this.tabPageAdv46.ThemesEnabled = true;
            // 
            // tabPageAdv47
            // 
            this.tabPageAdv47.Image = null;
            this.tabPageAdv47.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv47.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv47.Name = "tabPageAdv47";
            this.tabPageAdv47.ShowCloseButton = true;
            this.tabPageAdv47.Size = new System.Drawing.Size(729, 0);
            this.tabPageAdv47.TabIndex = 47;
            this.tabPageAdv47.Text = "S";
            this.tabPageAdv47.ThemesEnabled = true;
            // 
            // tabPageAdv48
            // 
            this.tabPageAdv48.Image = null;
            this.tabPageAdv48.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv48.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv48.Name = "tabPageAdv48";
            this.tabPageAdv48.ShowCloseButton = true;
            this.tabPageAdv48.Size = new System.Drawing.Size(729, 0);
            this.tabPageAdv48.TabIndex = 48;
            this.tabPageAdv48.Text = "T";
            this.tabPageAdv48.ThemesEnabled = true;
            // 
            // tabPageAdv49
            // 
            this.tabPageAdv49.Image = null;
            this.tabPageAdv49.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv49.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv49.Name = "tabPageAdv49";
            this.tabPageAdv49.ShowCloseButton = true;
            this.tabPageAdv49.Size = new System.Drawing.Size(729, 0);
            this.tabPageAdv49.TabIndex = 49;
            this.tabPageAdv49.Text = "U";
            this.tabPageAdv49.ThemesEnabled = true;
            // 
            // tabPageAdv50
            // 
            this.tabPageAdv50.Image = null;
            this.tabPageAdv50.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv50.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv50.Name = "tabPageAdv50";
            this.tabPageAdv50.ShowCloseButton = true;
            this.tabPageAdv50.Size = new System.Drawing.Size(729, 0);
            this.tabPageAdv50.TabIndex = 50;
            this.tabPageAdv50.Text = "V";
            this.tabPageAdv50.ThemesEnabled = true;
            // 
            // tabPageAdv51
            // 
            this.tabPageAdv51.Image = null;
            this.tabPageAdv51.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv51.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv51.Name = "tabPageAdv51";
            this.tabPageAdv51.ShowCloseButton = true;
            this.tabPageAdv51.Size = new System.Drawing.Size(729, 0);
            this.tabPageAdv51.TabIndex = 51;
            this.tabPageAdv51.Text = "W";
            this.tabPageAdv51.ThemesEnabled = true;
            // 
            // tabPageAdv52
            // 
            this.tabPageAdv52.Image = null;
            this.tabPageAdv52.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv52.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv52.Name = "tabPageAdv52";
            this.tabPageAdv52.ShowCloseButton = true;
            this.tabPageAdv52.Size = new System.Drawing.Size(729, 0);
            this.tabPageAdv52.TabIndex = 52;
            this.tabPageAdv52.Text = "X";
            this.tabPageAdv52.ThemesEnabled = true;
            // 
            // tabPageAdv53
            // 
            this.tabPageAdv53.Image = null;
            this.tabPageAdv53.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv53.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv53.Name = "tabPageAdv53";
            this.tabPageAdv53.ShowCloseButton = true;
            this.tabPageAdv53.Size = new System.Drawing.Size(729, 0);
            this.tabPageAdv53.TabIndex = 53;
            this.tabPageAdv53.Text = "Y";
            this.tabPageAdv53.ThemesEnabled = true;
            // 
            // tabPageAdv54
            // 
            this.tabPageAdv54.Image = null;
            this.tabPageAdv54.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv54.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv54.Name = "tabPageAdv54";
            this.tabPageAdv54.ShowCloseButton = true;
            this.tabPageAdv54.Size = new System.Drawing.Size(729, 0);
            this.tabPageAdv54.TabIndex = 54;
            this.tabPageAdv54.Text = "Z";
            this.tabPageAdv54.ThemesEnabled = true;
            // 
            // grdUsers
            // 
            this.grdUsers.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.grdUsers.ClipCurrentCellSelection = false;
            this.grdUsers.DataRowTemplate = this.dataRowTemplate2;
            this.grdUsers.DataRowTemplateStyles.Add(this.visualGridElementStyle2);
            this.grdUsers.DataRowTemplateStyles.Add(this.dataRowStyle1);
            // 
            // 
            // 
            this.grdUsers.ErrorVisualStyle.OverrideUIStyle = false;
            // 
            // 
            // 
            this.grdUsers.FixedColumnSplitter.AllowRepositioning = false;
            this.grdUsers.FixedColumnSplitter.Visible = false;
            this.grdUsers.FixedHeaderRows.Add(this.columnManagerRow2);
            this.grdUsers.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.grdUsers.GridLineColor = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(239)))), ((int)(((byte)(255)))));
            this.grdUsers.GridLineStyle = System.Drawing.Drawing2D.DashStyle.Dot;
            this.grdUsers.InactiveSelectionBackColor = System.Drawing.Color.RoyalBlue;
            // 
            // 
            // 
            this.grdUsers.InactiveSelectionVisualStyle.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(229)))), ((int)(((byte)(229)))), ((int)(((byte)(229)))));
            this.grdUsers.InactiveSelectionVisualStyle.ForeColor = System.Drawing.Color.Black;
            this.grdUsers.InactiveSelectionVisualStyle.OverrideUIStyle = true;
            this.grdUsers.Location = new System.Drawing.Point(0, 30);
            this.grdUsers.Margin = new System.Windows.Forms.Padding(5);
            this.grdUsers.Name = "grdUsers";
            this.grdUsers.OverrideUIStyle = false;
            this.grdUsers.ReadOnly = true;
            // 
            // 
            // 
            this.grdUsers.RowSelectorPane.AllowRowResize = false;
            this.grdUsers.RowSelectorPane.CurrentImageIndex = 16;
            gradientMap6.GradientMode = Xceed.UI.GradientMode.Horizontal;
            gradientStop11.Color = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(239)))), ((int)(((byte)(255)))));
            gradientStop11.Offset = 0D;
            gradientStop12.Color = System.Drawing.Color.FromArgb(((int)(((byte)(175)))), ((int)(((byte)(210)))), ((int)(((byte)(255)))));
            gradientStop12.Offset = 1D;
            gradientMap6.GradientStops.Add(gradientStop11);
            gradientMap6.GradientStops.Add(gradientStop12);
            this.grdUsers.RowSelectorPane.GradientMap = gradientMap6;
            // 
            // 
            // 
            this.grdUsers.RowSelectorPane.HotVisualStyle.OverrideUIStyle = true;
            this.grdUsers.RowSelectorPane.OverrideUIStyle = true;
            this.grdUsers.RowSelectorPane.SelectedImageIndex = 20;
            this.grdUsers.RowSelectorPane.Width = 17;
            this.grdUsers.SelectionBackColor = System.Drawing.Color.Indigo;
            this.grdUsers.SelectionMode = System.Windows.Forms.SelectionMode.One;
            // 
            // 
            // 
            this.grdUsers.SelectionVisualStyle.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(167)))), ((int)(((byte)(205)))), ((int)(((byte)(240)))));
            this.grdUsers.SelectionVisualStyle.ForeColor = System.Drawing.Color.Black;
            this.grdUsers.SelectionVisualStyle.OverrideUIStyle = true;
            this.grdUsers.ShowFocusRectangle = false;
            // 
            // 
            // 
            this.grdUsers.SideMargin.Visible = false;
            this.grdUsers.Size = new System.Drawing.Size(736, 594);
            this.grdUsers.SynchronizeDetailGrids = false;
            this.grdUsers.TabIndex = 30;
            this.grdUsers.TabStop = false;
            this.grdUsers.TreeLineColor = System.Drawing.Color.Silver;
            this.grdUsers.UIStyle = Xceed.UI.UIStyle.System;
            this.grdUsers.KeyUp += new System.Windows.Forms.KeyEventHandler(this.grdUsers_KeyUp);
            // 
            // visualGridElementStyle2
            // 
            // 
            // 
            // 
            this.visualGridElementStyle2.HotVisualStyle.OverrideUIStyle = true;
            this.visualGridElementStyle2.OverrideUIStyle = false;
            // 
            // dataRowStyle1
            // 
            this.dataRowStyle1.BackColor = System.Drawing.Color.AliceBlue;
            // 
            // columnManagerRow2
            // 
            this.columnManagerRow2.AllowColumnReorder = false;
            this.columnManagerRow2.AllowColumnResize = true;
            this.columnManagerRow2.AllowSort = false;
            this.columnManagerRow2.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            gradientStop9.Color = System.Drawing.Color.FromArgb(((int)(((byte)(252)))), ((int)(((byte)(253)))), ((int)(((byte)(255)))));
            gradientStop9.Offset = 0D;
            gradientStop10.Color = System.Drawing.Color.FromArgb(((int)(((byte)(196)))), ((int)(((byte)(221)))), ((int)(((byte)(255)))));
            gradientStop10.Offset = 1D;
            gradientMap5.GradientStops.Add(gradientStop9);
            gradientMap5.GradientStops.Add(gradientStop10);
            this.columnManagerRow2.GradientMap = gradientMap5;
            this.columnManagerRow2.Height = 23;
            // 
            // 
            // 
            this.columnManagerRow2.HotVisualStyle.OverrideUIStyle = false;
            this.columnManagerRow2.OverrideUIStyle = true;
            // 
            // barManager
            // 
            this.barManager.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1,
            this.bar3});
            this.barManager.Controller = this.barAndDockingController;
            this.barManager.DockControls.Add(this.barDockControlTop);
            this.barManager.DockControls.Add(this.barDockControlBottom);
            this.barManager.DockControls.Add(this.barDockControlLeft);
            this.barManager.DockControls.Add(this.barDockControlRight);
            this.barManager.Form = this;
            this.barManager.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.btnNewUser,
            this.barStaticItem1,
            this.btnExit});
            this.barManager.MaxItemId = 3;
            this.barManager.StatusBar = this.bar3;
            // 
            // bar1
            // 
            this.bar1.BarName = "Tools";
            this.bar1.CanDockStyle = DevExpress.XtraBars.BarCanDockStyle.Top;
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.btnNewUser),
            new DevExpress.XtraBars.LinkPersistInfo(this.btnExit)});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DisableClose = true;
            this.bar1.OptionsBar.DrawDragBorder = false;
            this.bar1.OptionsBar.UseWholeRow = true;
            this.bar1.Text = "Tools";
            // 
            // btnNewUser
            // 
            this.btnNewUser.Caption = "btnNewUser";
            this.btnNewUser.Glyph = global::LabMaestro.Controls.Win.Resources.new_16;
            this.btnNewUser.Hint = "Create new user";
            this.btnNewUser.Id = 0;
            this.btnNewUser.Name = "btnNewUser";
            this.btnNewUser.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnNewUser_ItemClick);
            // 
            // btnExit
            // 
            this.btnExit.Caption = "Exit";
            this.btnExit.Glyph = global::LabMaestro.Controls.Win.Resources.exit_16;
            this.btnExit.Id = 2;
            this.btnExit.Name = "btnExit";
            this.btnExit.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnExit_ItemClick);
            // 
            // bar3
            // 
            this.bar3.BarName = "Status bar";
            this.bar3.CanDockStyle = DevExpress.XtraBars.BarCanDockStyle.Bottom;
            this.bar3.DockCol = 0;
            this.bar3.DockRow = 0;
            this.bar3.DockStyle = DevExpress.XtraBars.BarDockStyle.Bottom;
            this.bar3.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barStaticItem1)});
            this.bar3.OptionsBar.AllowQuickCustomization = false;
            this.bar3.OptionsBar.DrawDragBorder = false;
            this.bar3.OptionsBar.UseWholeRow = true;
            this.bar3.Text = "Status bar";
            // 
            // barStaticItem1
            // 
            this.barStaticItem1.Caption = "Roles: 0";
            this.barStaticItem1.Id = 1;
            this.barStaticItem1.Name = "barStaticItem1";
            this.barStaticItem1.TextAlignment = System.Drawing.StringAlignment.Near;
            // 
            // barAndDockingController
            // 
            this.barAndDockingController.PropertiesBar.DefaultGlyphSize = new System.Drawing.Size(16, 16);
            this.barAndDockingController.PropertiesBar.DefaultLargeGlyphSize = new System.Drawing.Size(32, 32);
            // 
            // barDockControlTop
            // 
            this.barDockControlTop.CausesValidation = false;
            this.barDockControlTop.Dock = System.Windows.Forms.DockStyle.Top;
            this.barDockControlTop.Location = new System.Drawing.Point(0, 0);
            this.barDockControlTop.Size = new System.Drawing.Size(1051, 31);
            // 
            // barDockControlBottom
            // 
            this.barDockControlBottom.CausesValidation = false;
            this.barDockControlBottom.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.barDockControlBottom.Location = new System.Drawing.Point(0, 656);
            this.barDockControlBottom.Size = new System.Drawing.Size(1051, 25);
            // 
            // barDockControlLeft
            // 
            this.barDockControlLeft.CausesValidation = false;
            this.barDockControlLeft.Dock = System.Windows.Forms.DockStyle.Left;
            this.barDockControlLeft.Location = new System.Drawing.Point(0, 31);
            this.barDockControlLeft.Size = new System.Drawing.Size(0, 625);
            // 
            // barDockControlRight
            // 
            this.barDockControlRight.CausesValidation = false;
            this.barDockControlRight.Dock = System.Windows.Forms.DockStyle.Right;
            this.barDockControlRight.Location = new System.Drawing.Point(1051, 31);
            this.barDockControlRight.Size = new System.Drawing.Size(0, 625);
            // 
            // UsersCatalogEditorDialog
            // 
            this.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.ClientSize = new System.Drawing.Size(1051, 681);
            this.Controls.Add(this.splitContainerControl);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "UsersCatalogEditorDialog";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "UsersCatalogEditorDialog";
            this.Load += new System.EventHandler(this.UsersCatalogEditorDialog_Load);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl)).EndInit();
            this.splitContainerControl.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.grdRoles)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataRow1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.columnManagerRow1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdDepartments)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataRow2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.columnManagerRow3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabFilter)).EndInit();
            this.tabFilter.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.grdUsers)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataRowTemplate2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.columnManagerRow2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl;
        private Xceed.Grid.GridControl grdRoles;
        private Xceed.Grid.DataRow dataRow1;
        private Xceed.Grid.VisualGridElementStyle visualGridElementStyle1;
        private Xceed.Grid.VisualGridElementStyle visualGridElementStyle3;
        private Xceed.Grid.ColumnManagerRow columnManagerRow1;
        private Xceed.Grid.GridControl grdDepartments;
        private Xceed.Grid.DataRow dataRow2;
        private Xceed.Grid.VisualGridElementStyle visualGridElementStyle4;
        private Xceed.Grid.VisualGridElementStyle visualGridElementStyle5;
        private Xceed.Grid.ColumnManagerRow columnManagerRow3;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private Syncfusion.Windows.Forms.Tools.TabControlAdv tabFilter;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv28;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv29;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv30;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv31;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv32;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv33;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv34;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv35;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv36;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv37;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv38;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv39;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv40;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv41;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv42;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv43;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv44;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv45;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv46;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv47;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv48;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv49;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv50;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv51;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv52;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv53;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv54;
        private Xceed.Grid.GridControl grdUsers;
        private Xceed.Grid.DataRow dataRowTemplate2;
        private Xceed.Grid.VisualGridElementStyle visualGridElementStyle2;
        private Xceed.Grid.VisualGridElementStyle dataRowStyle1;
        private Xceed.Grid.ColumnManagerRow columnManagerRow2;
        private DevExpress.XtraBars.BarManager barManager;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.Bar bar3;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController;
        private DevExpress.XtraBars.BarButtonItem btnNewUser;
        private DevExpress.XtraBars.BarStaticItem barStaticItem1;
        private DevExpress.XtraBars.BarButtonItem btnExit;
    }
}