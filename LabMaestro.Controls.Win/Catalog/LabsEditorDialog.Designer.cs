﻿namespace LabMaestro.Controls.Win
{
    partial class LabsEditorDialog
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            Xceed.Grid.GradientMap gradientMap2 = new Xceed.Grid.GradientMap();
            Xceed.Grid.GradientStop gradientStop3 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientStop gradientStop4 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientMap gradientMap1 = new Xceed.Grid.GradientMap();
            Xceed.Grid.GradientStop gradientStop1 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientStop gradientStop2 = new Xceed.Grid.GradientStop();
            this.chkIsActive = new DevExpress.XtraEditors.CheckEdit();
            this.txtName = new DevExpress.XtraEditors.TextEdit();
            this.lblId = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.btnNew = new DevExpress.XtraEditors.SimpleButton();
            this.btnReset = new DevExpress.XtraEditors.SimpleButton();
            this.btnSave = new DevExpress.XtraEditors.SimpleButton();
            this.btnClose = new DevExpress.XtraEditors.SimpleButton();
            this.GridControl = new Xceed.Grid.GridControl();
            this.dataRowTemplate2 = new Xceed.Grid.DataRow();
            this.visualGridElementStyle2 = new Xceed.Grid.VisualGridElementStyle();
            this.dataRowStyle1 = new Xceed.Grid.VisualGridElementStyle();
            this.columnManagerRow2 = new Xceed.Grid.ColumnManagerRow();
            this.txtCode = new DevExpress.XtraEditors.TextEdit();
            this.txtPrintName = new DevExpress.XtraEditors.TextEdit();
            this.chkPrintCanon = new DevExpress.XtraEditors.CheckEdit();
            this.rgResultType = new DevExpress.XtraEditors.RadioGroup();
            this.chkIsAux = new DevExpress.XtraEditors.CheckEdit();
            this.luDiscount = new DevExpress.XtraEditors.LookUpEdit();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl9 = new DevExpress.XtraEditors.LabelControl();
            this.btnDiscountEdit = new DevExpress.XtraEditors.SimpleButton();
            this.btnReferralEdit = new DevExpress.XtraEditors.SimpleButton();
            this.luReferral = new DevExpress.XtraEditors.LookUpEdit();
            this.cboPostOE = new DevExpress.XtraEditors.ComboBoxEdit();
            this.cboPostRE = new DevExpress.XtraEditors.ComboBoxEdit();
            this.labelControl10 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl11 = new DevExpress.XtraEditors.LabelControl();
            this.cboPostRV = new DevExpress.XtraEditors.ComboBoxEdit();
            this.cboPostRF = new DevExpress.XtraEditors.ComboBoxEdit();
            this.labelControl12 = new DevExpress.XtraEditors.LabelControl();
            this.cboPostRC = new DevExpress.XtraEditors.ComboBoxEdit();
            this.labelControl13 = new DevExpress.XtraEditors.LabelControl();
            this.btnAutoWireStage = new DevExpress.XtraEditors.SimpleButton();
            this.btnAutoFinalizeStage = new DevExpress.XtraEditors.SimpleButton();
            this.btnReportHeaderEdit = new DevExpress.XtraEditors.SimpleButton();
            this.luReportHeader = new DevExpress.XtraEditors.LookUpEdit();
            this.labelControl14 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl15 = new DevExpress.XtraEditors.LabelControl();
            this.cboRefGrpTitle = new DevExpress.XtraEditors.ComboBoxEdit();
            this.cboAccGrpTitle = new DevExpress.XtraEditors.ComboBoxEdit();
            this.labelControl16 = new DevExpress.XtraEditors.LabelControl();
            ((System.ComponentModel.ISupportInitialize)(this.chkIsActive.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.GridControl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataRowTemplate2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.columnManagerRow2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCode.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPrintName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkPrintCanon.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rgResultType.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkIsAux.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.luDiscount.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.luReferral.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cboPostOE.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cboPostRE.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cboPostRV.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cboPostRF.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cboPostRC.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.luReportHeader.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cboRefGrpTitle.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cboAccGrpTitle.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // chkIsActive
            // 
            this.chkIsActive.Location = new System.Drawing.Point(508, 283);
            this.chkIsActive.Name = "chkIsActive";
            this.chkIsActive.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkIsActive.Properties.Appearance.Options.UseFont = true;
            this.chkIsActive.Properties.AutoWidth = true;
            this.chkIsActive.Properties.Caption = "Is Active?";
            this.chkIsActive.Size = new System.Drawing.Size(76, 20);
            this.chkIsActive.TabIndex = 18;
            this.chkIsActive.ToolTip = "Lab is enabled?";
            // 
            // txtName
            // 
            this.txtName.Location = new System.Drawing.Point(143, 282);
            this.txtName.Name = "txtName";
            this.txtName.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtName.Properties.Appearance.Options.UseFont = true;
            this.txtName.Size = new System.Drawing.Size(354, 22);
            this.txtName.TabIndex = 16;
            this.txtName.ToolTip = "Name of the lab";
            // 
            // lblId
            // 
            this.lblId.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblId.Location = new System.Drawing.Point(143, 260);
            this.lblId.Name = "lblId";
            this.lblId.Size = new System.Drawing.Size(75, 16);
            this.lblId.TabIndex = 23;
            this.lblId.Text = "labelControl1";
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Location = new System.Drawing.Point(13, 257);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(13, 16);
            this.labelControl2.TabIndex = 24;
            this.labelControl2.Text = "Id";
            // 
            // labelControl4
            // 
            this.labelControl4.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl4.Location = new System.Drawing.Point(14, 570);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(95, 16);
            this.labelControl4.TabIndex = 25;
            this.labelControl4.Text = "Referral Group";
            // 
            // labelControl3
            // 
            this.labelControl3.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl3.Location = new System.Drawing.Point(12, 542);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(94, 16);
            this.labelControl3.TabIndex = 26;
            this.labelControl3.Text = "Discount Level";
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Location = new System.Drawing.Point(13, 285);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(35, 16);
            this.labelControl1.TabIndex = 27;
            this.labelControl1.Text = "Name";
            // 
            // btnNew
            // 
            this.btnNew.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnNew.Appearance.Options.UseFont = true;
            this.btnNew.Image = global::LabMaestro.Controls.Win.Resources.star_yellow;
            this.btnNew.Location = new System.Drawing.Point(549, 632);
            this.btnNew.Name = "btnNew";
            this.btnNew.Size = new System.Drawing.Size(75, 23);
            this.btnNew.TabIndex = 20;
            this.btnNew.Text = "New";
            this.btnNew.ToolTip = "Create new billable item";
            this.btnNew.Click += new System.EventHandler(this.btnNew_Click);
            // 
            // btnReset
            // 
            this.btnReset.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnReset.Appearance.Options.UseFont = true;
            this.btnReset.Image = global::LabMaestro.Controls.Win.Resources.reload16;
            this.btnReset.Location = new System.Drawing.Point(468, 632);
            this.btnReset.Name = "btnReset";
            this.btnReset.Size = new System.Drawing.Size(75, 23);
            this.btnReset.TabIndex = 21;
            this.btnReset.Text = "Reset";
            this.btnReset.ToolTip = "Discard the modification";
            this.btnReset.Click += new System.EventHandler(this.btnReset_Click);
            // 
            // btnSave
            // 
            this.btnSave.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnSave.Appearance.Options.UseFont = true;
            this.btnSave.Image = global::LabMaestro.Controls.Win.Resources.save16;
            this.btnSave.Location = new System.Drawing.Point(630, 632);
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new System.Drawing.Size(75, 23);
            this.btnSave.TabIndex = 19;
            this.btnSave.Text = "Save";
            this.btnSave.ToolTip = "Save the billable item";
            this.btnSave.Click += new System.EventHandler(this.btnSave_Click);
            // 
            // btnClose
            // 
            this.btnClose.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnClose.Appearance.Options.UseFont = true;
            this.btnClose.Image = global::LabMaestro.Controls.Win.Resources.exit_16;
            this.btnClose.Location = new System.Drawing.Point(11, 632);
            this.btnClose.Name = "btnClose";
            this.btnClose.Size = new System.Drawing.Size(75, 23);
            this.btnClose.TabIndex = 22;
            this.btnClose.Text = "Close";
            this.btnClose.Click += new System.EventHandler(this.btnClose_Click);
            // 
            // GridControl
            // 
            this.GridControl.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.GridControl.ClipCurrentCellSelection = false;
            this.GridControl.DataRowTemplate = this.dataRowTemplate2;
            this.GridControl.DataRowTemplateStyles.Add(this.visualGridElementStyle2);
            this.GridControl.DataRowTemplateStyles.Add(this.dataRowStyle1);
            // 
            // 
            // 
            this.GridControl.ErrorVisualStyle.OverrideUIStyle = false;
            // 
            // 
            // 
            this.GridControl.FixedColumnSplitter.AllowRepositioning = false;
            this.GridControl.FixedColumnSplitter.Visible = false;
            this.GridControl.FixedHeaderRows.Add(this.columnManagerRow2);
            this.GridControl.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.GridControl.GridLineColor = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(239)))), ((int)(((byte)(255)))));
            this.GridControl.GridLineStyle = System.Drawing.Drawing2D.DashStyle.Dot;
            this.GridControl.InactiveSelectionBackColor = System.Drawing.Color.RoyalBlue;
            // 
            // 
            // 
            this.GridControl.InactiveSelectionVisualStyle.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(229)))), ((int)(((byte)(229)))), ((int)(((byte)(229)))));
            this.GridControl.InactiveSelectionVisualStyle.ForeColor = System.Drawing.Color.Black;
            this.GridControl.InactiveSelectionVisualStyle.OverrideUIStyle = true;
            this.GridControl.Location = new System.Drawing.Point(14, 14);
            this.GridControl.Margin = new System.Windows.Forms.Padding(5);
            this.GridControl.Name = "GridControl";
            this.GridControl.OverrideUIStyle = false;
            this.GridControl.ReadOnly = true;
            // 
            // 
            // 
            this.GridControl.RowSelectorPane.AllowRowResize = false;
            this.GridControl.RowSelectorPane.CurrentImageIndex = 16;
            gradientMap2.GradientMode = Xceed.UI.GradientMode.Horizontal;
            gradientStop3.Color = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(239)))), ((int)(((byte)(255)))));
            gradientStop3.Offset = 0D;
            gradientStop4.Color = System.Drawing.Color.FromArgb(((int)(((byte)(175)))), ((int)(((byte)(210)))), ((int)(((byte)(255)))));
            gradientStop4.Offset = 1D;
            gradientMap2.GradientStops.Add(gradientStop3);
            gradientMap2.GradientStops.Add(gradientStop4);
            this.GridControl.RowSelectorPane.GradientMap = gradientMap2;
            // 
            // 
            // 
            this.GridControl.RowSelectorPane.HotVisualStyle.OverrideUIStyle = true;
            this.GridControl.RowSelectorPane.OverrideUIStyle = true;
            this.GridControl.RowSelectorPane.SelectedImageIndex = 20;
            this.GridControl.RowSelectorPane.Width = 17;
            this.GridControl.SelectionBackColor = System.Drawing.Color.Indigo;
            this.GridControl.SelectionMode = System.Windows.Forms.SelectionMode.One;
            // 
            // 
            // 
            this.GridControl.SelectionVisualStyle.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(167)))), ((int)(((byte)(205)))), ((int)(((byte)(240)))));
            this.GridControl.SelectionVisualStyle.ForeColor = System.Drawing.Color.Black;
            this.GridControl.SelectionVisualStyle.OverrideUIStyle = true;
            this.GridControl.ShowFocusRectangle = false;
            // 
            // 
            // 
            this.GridControl.SideMargin.Visible = false;
            this.GridControl.Size = new System.Drawing.Size(692, 238);
            this.GridControl.SynchronizeDetailGrids = false;
            this.GridControl.TabIndex = 29;
            this.GridControl.TabStop = false;
            this.GridControl.TreeLineColor = System.Drawing.Color.Silver;
            this.GridControl.UIStyle = Xceed.UI.UIStyle.System;
            this.GridControl.SelectedRowsChanged += new System.EventHandler(this.GridControl_SelectedRowsChanged);
            // 
            // visualGridElementStyle2
            // 
            // 
            // 
            // 
            this.visualGridElementStyle2.HotVisualStyle.OverrideUIStyle = true;
            this.visualGridElementStyle2.OverrideUIStyle = false;
            // 
            // dataRowStyle1
            // 
            this.dataRowStyle1.BackColor = System.Drawing.Color.AliceBlue;
            // 
            // columnManagerRow2
            // 
            this.columnManagerRow2.AllowColumnReorder = false;
            this.columnManagerRow2.AllowColumnResize = true;
            this.columnManagerRow2.AllowSort = false;
            this.columnManagerRow2.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            gradientStop1.Color = System.Drawing.Color.FromArgb(((int)(((byte)(252)))), ((int)(((byte)(253)))), ((int)(((byte)(255)))));
            gradientStop1.Offset = 0D;
            gradientStop2.Color = System.Drawing.Color.FromArgb(((int)(((byte)(196)))), ((int)(((byte)(221)))), ((int)(((byte)(255)))));
            gradientStop2.Offset = 1D;
            gradientMap1.GradientStops.Add(gradientStop1);
            gradientMap1.GradientStops.Add(gradientStop2);
            this.columnManagerRow2.GradientMap = gradientMap1;
            this.columnManagerRow2.Height = 23;
            // 
            // 
            // 
            this.columnManagerRow2.HotVisualStyle.OverrideUIStyle = false;
            this.columnManagerRow2.OverrideUIStyle = true;
            // 
            // txtCode
            // 
            this.txtCode.Location = new System.Drawing.Point(143, 310);
            this.txtCode.Name = "txtCode";
            this.txtCode.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtCode.Properties.Appearance.Options.UseFont = true;
            this.txtCode.Size = new System.Drawing.Size(354, 22);
            this.txtCode.TabIndex = 16;
            this.txtCode.ToolTip = "Unique codename for the lab";
            // 
            // txtPrintName
            // 
            this.txtPrintName.Location = new System.Drawing.Point(143, 337);
            this.txtPrintName.Name = "txtPrintName";
            this.txtPrintName.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtPrintName.Properties.Appearance.Options.UseFont = true;
            this.txtPrintName.Size = new System.Drawing.Size(354, 22);
            this.txtPrintName.TabIndex = 16;
            this.txtPrintName.ToolTip = "Proper name to be printed on requisition foil";
            // 
            // chkPrintCanon
            // 
            this.chkPrintCanon.Location = new System.Drawing.Point(508, 338);
            this.chkPrintCanon.Name = "chkPrintCanon";
            this.chkPrintCanon.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkPrintCanon.Properties.Appearance.Options.UseFont = true;
            this.chkPrintCanon.Properties.AutoWidth = true;
            this.chkPrintCanon.Properties.Caption = "Print Canonical Test Names?";
            this.chkPrintCanon.Size = new System.Drawing.Size(185, 20);
            this.chkPrintCanon.TabIndex = 18;
            this.chkPrintCanon.ToolTip = "Print proper canonical names of tests on requisition foils - If false short names" +
    " will be used";
            // 
            // rgResultType
            // 
            this.rgResultType.Location = new System.Drawing.Point(143, 366);
            this.rgResultType.Name = "rgResultType";
            this.rgResultType.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rgResultType.Properties.Appearance.Options.UseFont = true;
            this.rgResultType.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.RadioGroupItem[] {
            new DevExpress.XtraEditors.Controls.RadioGroupItem(((byte)(0)), "&Unarchived"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(((byte)(10)), "&Discrete"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(((byte)(20)), "&Template"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(((byte)(30)), "U&serTemplate")});
            this.rgResultType.Size = new System.Drawing.Size(563, 36);
            this.rgResultType.TabIndex = 28;
            // 
            // chkIsAux
            // 
            this.chkIsAux.Location = new System.Drawing.Point(508, 311);
            this.chkIsAux.Name = "chkIsAux";
            this.chkIsAux.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkIsAux.Properties.Appearance.Options.UseFont = true;
            this.chkIsAux.Properties.Caption = "Is Auxilliary Procedure?";
            this.chkIsAux.Size = new System.Drawing.Size(163, 20);
            this.chkIsAux.TabIndex = 18;
            this.chkIsAux.ToolTip = "Lab contains non-resultable auxilliary items?";
            // 
            // luDiscount
            // 
            this.luDiscount.Location = new System.Drawing.Point(143, 539);
            this.luDiscount.Name = "luDiscount";
            this.luDiscount.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.luDiscount.Properties.Appearance.Options.UseFont = true;
            this.luDiscount.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.luDiscount.Properties.BestFitRowCount = 5;
            this.luDiscount.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.luDiscount.Properties.DropDownItemHeight = 23;
            this.luDiscount.Properties.DropDownRows = 5;
            this.luDiscount.Properties.ImmediatePopup = true;
            this.luDiscount.Properties.LookAndFeel.SkinName = "Office 2010 Silver";
            this.luDiscount.Properties.NullText = "";
            this.luDiscount.Properties.PopupBorderStyle = DevExpress.XtraEditors.Controls.PopupBorderStyles.Flat;
            this.luDiscount.Properties.ShowFooter = false;
            this.luDiscount.Size = new System.Drawing.Size(195, 22);
            this.luDiscount.TabIndex = 30;
            this.luDiscount.ToolTip = "Select the discount level for the tests/procedures in this lab";
            // 
            // labelControl5
            // 
            this.labelControl5.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl5.Location = new System.Drawing.Point(12, 313);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(67, 16);
            this.labelControl5.TabIndex = 27;
            this.labelControl5.Text = "Codename";
            // 
            // labelControl6
            // 
            this.labelControl6.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl6.Location = new System.Drawing.Point(12, 375);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(108, 16);
            this.labelControl6.TabIndex = 27;
            this.labelControl6.Text = "Test Result Type";
            // 
            // labelControl7
            // 
            this.labelControl7.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl7.Location = new System.Drawing.Point(14, 341);
            this.labelControl7.Name = "labelControl7";
            this.labelControl7.Size = new System.Drawing.Size(70, 16);
            this.labelControl7.TabIndex = 27;
            this.labelControl7.Text = "Print Name";
            // 
            // labelControl8
            // 
            this.labelControl8.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl8.Appearance.ForeColor = System.Drawing.Color.MidnightBlue;
            this.labelControl8.Location = new System.Drawing.Point(26, 450);
            this.labelControl8.LookAndFeel.UseDefaultLookAndFeel = false;
            this.labelControl8.Name = "labelControl8";
            this.labelControl8.Size = new System.Drawing.Size(76, 16);
            this.labelControl8.TabIndex = 27;
            this.labelControl8.Text = "Order Entry";
            // 
            // labelControl9
            // 
            this.labelControl9.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl9.Appearance.ForeColor = System.Drawing.Color.MidnightBlue;
            this.labelControl9.Location = new System.Drawing.Point(381, 450);
            this.labelControl9.LookAndFeel.UseDefaultLookAndFeel = false;
            this.labelControl9.Name = "labelControl9";
            this.labelControl9.Size = new System.Drawing.Size(80, 16);
            this.labelControl9.TabIndex = 27;
            this.labelControl9.Text = "Result Entry";
            // 
            // btnDiscountEdit
            // 
            this.btnDiscountEdit.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnDiscountEdit.Appearance.Options.UseFont = true;
            this.btnDiscountEdit.Image = global::LabMaestro.Controls.Win.Resources.edit16;
            this.btnDiscountEdit.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleCenter;
            this.btnDiscountEdit.Location = new System.Drawing.Point(344, 539);
            this.btnDiscountEdit.Name = "btnDiscountEdit";
            this.btnDiscountEdit.Size = new System.Drawing.Size(22, 22);
            this.btnDiscountEdit.TabIndex = 31;
            this.btnDiscountEdit.ToolTip = "Edit Discount Levels";
            this.btnDiscountEdit.Click += new System.EventHandler(this.btnDiscountEdit_Click);
            // 
            // btnReferralEdit
            // 
            this.btnReferralEdit.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnReferralEdit.Appearance.Options.UseFont = true;
            this.btnReferralEdit.Image = global::LabMaestro.Controls.Win.Resources.edit16;
            this.btnReferralEdit.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleCenter;
            this.btnReferralEdit.Location = new System.Drawing.Point(344, 567);
            this.btnReferralEdit.Name = "btnReferralEdit";
            this.btnReferralEdit.Size = new System.Drawing.Size(22, 22);
            this.btnReferralEdit.TabIndex = 31;
            this.btnReferralEdit.ToolTip = "Edit Referral Groups";
            this.btnReferralEdit.Click += new System.EventHandler(this.btnReferralEdit_Click);
            // 
            // luReferral
            // 
            this.luReferral.Location = new System.Drawing.Point(143, 567);
            this.luReferral.Name = "luReferral";
            this.luReferral.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.luReferral.Properties.Appearance.Options.UseFont = true;
            this.luReferral.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.luReferral.Properties.BestFitRowCount = 5;
            this.luReferral.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.luReferral.Properties.DropDownItemHeight = 23;
            this.luReferral.Properties.DropDownRows = 5;
            this.luReferral.Properties.ImmediatePopup = true;
            this.luReferral.Properties.LookAndFeel.SkinName = "Office 2010 Silver";
            this.luReferral.Properties.NullText = "";
            this.luReferral.Properties.PopupBorderStyle = DevExpress.XtraEditors.Controls.PopupBorderStyles.Flat;
            this.luReferral.Properties.ShowFooter = false;
            this.luReferral.Size = new System.Drawing.Size(195, 22);
            this.luReferral.TabIndex = 30;
            this.luReferral.ToolTip = "Select the referral group for the tests/procedures in this lab";
            // 
            // cboPostOE
            // 
            this.cboPostOE.Location = new System.Drawing.Point(155, 450);
            this.cboPostOE.Name = "cboPostOE";
            this.cboPostOE.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cboPostOE.Properties.Appearance.Options.UseFont = true;
            this.cboPostOE.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cboPostOE.Properties.DropDownItemHeight = 23;
            this.cboPostOE.Properties.Items.AddRange(new object[] {
            "",
            "Order Entry",
            "Result Entry",
            "Result Validation",
            "Result Finalization",
            "Report Collation",
            "Report Dispatch",
            "Order Fulfillment"});
            this.cboPostOE.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cboPostOE.Size = new System.Drawing.Size(195, 22);
            this.cboPostOE.TabIndex = 33;
            // 
            // cboPostRE
            // 
            this.cboPostRE.Location = new System.Drawing.Point(510, 450);
            this.cboPostRE.Name = "cboPostRE";
            this.cboPostRE.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cboPostRE.Properties.Appearance.Options.UseFont = true;
            this.cboPostRE.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cboPostRE.Properties.DropDownItemHeight = 23;
            this.cboPostRE.Properties.Items.AddRange(new object[] {
            "",
            "Order Entry",
            "Result Entry",
            "Result Validation",
            "Result Finalization",
            "Report Collation",
            "Report Dispatch",
            "Order Fulfillment"});
            this.cboPostRE.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cboPostRE.Size = new System.Drawing.Size(195, 22);
            this.cboPostRE.TabIndex = 33;
            // 
            // labelControl10
            // 
            this.labelControl10.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl10.Appearance.ForeColor = System.Drawing.Color.MidnightBlue;
            this.labelControl10.Location = new System.Drawing.Point(26, 478);
            this.labelControl10.LookAndFeel.UseDefaultLookAndFeel = false;
            this.labelControl10.Name = "labelControl10";
            this.labelControl10.Size = new System.Drawing.Size(109, 16);
            this.labelControl10.TabIndex = 27;
            this.labelControl10.Text = "Result Validation";
            // 
            // labelControl11
            // 
            this.labelControl11.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl11.Appearance.ForeColor = System.Drawing.Color.MidnightBlue;
            this.labelControl11.Location = new System.Drawing.Point(381, 478);
            this.labelControl11.LookAndFeel.UseDefaultLookAndFeel = false;
            this.labelControl11.Name = "labelControl11";
            this.labelControl11.Size = new System.Drawing.Size(116, 16);
            this.labelControl11.TabIndex = 27;
            this.labelControl11.Text = "Result Finalization";
            // 
            // cboPostRV
            // 
            this.cboPostRV.Location = new System.Drawing.Point(155, 478);
            this.cboPostRV.Name = "cboPostRV";
            this.cboPostRV.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cboPostRV.Properties.Appearance.Options.UseFont = true;
            this.cboPostRV.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cboPostRV.Properties.DropDownItemHeight = 23;
            this.cboPostRV.Properties.Items.AddRange(new object[] {
            "",
            "Order Entry",
            "Result Entry",
            "Result Validation",
            "Result Finalization",
            "Report Collation",
            "Report Dispatch",
            "Order Fulfillment"});
            this.cboPostRV.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cboPostRV.Size = new System.Drawing.Size(195, 22);
            this.cboPostRV.TabIndex = 33;
            // 
            // cboPostRF
            // 
            this.cboPostRF.Location = new System.Drawing.Point(510, 478);
            this.cboPostRF.Name = "cboPostRF";
            this.cboPostRF.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cboPostRF.Properties.Appearance.Options.UseFont = true;
            this.cboPostRF.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cboPostRF.Properties.DropDownItemHeight = 23;
            this.cboPostRF.Properties.Items.AddRange(new object[] {
            "",
            "Order Entry",
            "Result Entry",
            "Result Validation",
            "Result Finalization",
            "Report Collation",
            "Report Dispatch",
            "Order Fulfillment"});
            this.cboPostRF.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cboPostRF.Size = new System.Drawing.Size(195, 22);
            this.cboPostRF.TabIndex = 33;
            // 
            // labelControl12
            // 
            this.labelControl12.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl12.Appearance.ForeColor = System.Drawing.Color.MidnightBlue;
            this.labelControl12.Location = new System.Drawing.Point(26, 506);
            this.labelControl12.LookAndFeel.UseDefaultLookAndFeel = false;
            this.labelControl12.Name = "labelControl12";
            this.labelControl12.Size = new System.Drawing.Size(104, 16);
            this.labelControl12.TabIndex = 27;
            this.labelControl12.Text = "Report Collation";
            // 
            // cboPostRC
            // 
            this.cboPostRC.Location = new System.Drawing.Point(155, 506);
            this.cboPostRC.Name = "cboPostRC";
            this.cboPostRC.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cboPostRC.Properties.Appearance.Options.UseFont = true;
            this.cboPostRC.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cboPostRC.Properties.DropDownItemHeight = 23;
            this.cboPostRC.Properties.Items.AddRange(new object[] {
            "",
            "Order Entry",
            "Result Entry",
            "Result Validation",
            "Result Finalization",
            "Report Collation",
            "Report Dispatch",
            "Order Fulfillment"});
            this.cboPostRC.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cboPostRC.Size = new System.Drawing.Size(195, 22);
            this.cboPostRC.TabIndex = 33;
            // 
            // labelControl13
            // 
            this.labelControl13.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl13.Appearance.ForeColor = System.Drawing.Color.MidnightBlue;
            this.labelControl13.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None;
            this.labelControl13.LineLocation = DevExpress.XtraEditors.LineLocation.Center;
            this.labelControl13.LineOrientation = DevExpress.XtraEditors.LabelLineOrientation.Horizontal;
            this.labelControl13.LineVisible = true;
            this.labelControl13.Location = new System.Drawing.Point(13, 419);
            this.labelControl13.LookAndFeel.UseDefaultLookAndFeel = false;
            this.labelControl13.Name = "labelControl13";
            this.labelControl13.Size = new System.Drawing.Size(690, 16);
            this.labelControl13.TabIndex = 34;
            this.labelControl13.Text = "Post-operative Workflow Stages  ";
            // 
            // btnAutoWireStage
            // 
            this.btnAutoWireStage.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnAutoWireStage.Appearance.Options.UseFont = true;
            this.btnAutoWireStage.Image = global::LabMaestro.Controls.Win.Resources.blue_bulb;
            this.btnAutoWireStage.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleCenter;
            this.btnAutoWireStage.Location = new System.Drawing.Point(643, 505);
            this.btnAutoWireStage.Name = "btnAutoWireStage";
            this.btnAutoWireStage.Size = new System.Drawing.Size(28, 23);
            this.btnAutoWireStage.TabIndex = 35;
            this.btnAutoWireStage.ToolTip = "Auto-wire Workflow Stages";
            this.btnAutoWireStage.Click += new System.EventHandler(this.btnAutoWireStage_Click);
            // 
            // btnAutoFinalizeStage
            // 
            this.btnAutoFinalizeStage.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnAutoFinalizeStage.Appearance.Options.UseFont = true;
            this.btnAutoFinalizeStage.Image = global::LabMaestro.Controls.Win.Resources.star_yellow;
            this.btnAutoFinalizeStage.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleCenter;
            this.btnAutoFinalizeStage.Location = new System.Drawing.Point(677, 505);
            this.btnAutoFinalizeStage.Name = "btnAutoFinalizeStage";
            this.btnAutoFinalizeStage.Size = new System.Drawing.Size(28, 23);
            this.btnAutoFinalizeStage.TabIndex = 36;
            this.btnAutoFinalizeStage.ToolTip = "Auto-wire Workflow Stages to Finalization";
            this.btnAutoFinalizeStage.Click += new System.EventHandler(this.btnAutoFinalizeStage_Click);
            // 
            // btnReportHeaderEdit
            // 
            this.btnReportHeaderEdit.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnReportHeaderEdit.Appearance.Options.UseFont = true;
            this.btnReportHeaderEdit.Image = global::LabMaestro.Controls.Win.Resources.edit16;
            this.btnReportHeaderEdit.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleCenter;
            this.btnReportHeaderEdit.Location = new System.Drawing.Point(344, 595);
            this.btnReportHeaderEdit.Name = "btnReportHeaderEdit";
            this.btnReportHeaderEdit.Size = new System.Drawing.Size(22, 22);
            this.btnReportHeaderEdit.TabIndex = 39;
            this.btnReportHeaderEdit.ToolTip = "Edit Lab Report Headers";
            this.btnReportHeaderEdit.Click += new System.EventHandler(this.btnReportHeaderEdit_Click);
            // 
            // luReportHeader
            // 
            this.luReportHeader.Location = new System.Drawing.Point(143, 595);
            this.luReportHeader.Name = "luReportHeader";
            this.luReportHeader.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.luReportHeader.Properties.Appearance.Options.UseFont = true;
            this.luReportHeader.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.luReportHeader.Properties.BestFitRowCount = 5;
            this.luReportHeader.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.luReportHeader.Properties.DropDownItemHeight = 23;
            this.luReportHeader.Properties.DropDownRows = 5;
            this.luReportHeader.Properties.ImmediatePopup = true;
            this.luReportHeader.Properties.LookAndFeel.SkinName = "Office 2010 Silver";
            this.luReportHeader.Properties.NullText = "";
            this.luReportHeader.Properties.PopupBorderStyle = DevExpress.XtraEditors.Controls.PopupBorderStyles.Flat;
            this.luReportHeader.Properties.ShowFooter = false;
            this.luReportHeader.Size = new System.Drawing.Size(195, 22);
            this.luReportHeader.TabIndex = 38;
            this.luReportHeader.ToolTip = "Select the referral group for the tests/procedures in this lab";
            // 
            // labelControl14
            // 
            this.labelControl14.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl14.Location = new System.Drawing.Point(14, 598);
            this.labelControl14.Name = "labelControl14";
            this.labelControl14.Size = new System.Drawing.Size(96, 16);
            this.labelControl14.TabIndex = 37;
            this.labelControl14.Text = "Report Header";
            // 
            // labelControl15
            // 
            this.labelControl15.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl15.Location = new System.Drawing.Point(381, 570);
            this.labelControl15.Name = "labelControl15";
            this.labelControl15.Size = new System.Drawing.Size(95, 16);
            this.labelControl15.TabIndex = 43;
            this.labelControl15.Text = "Referral Group";
            // 
            // cboRefGrpTitle
            // 
            this.cboRefGrpTitle.Location = new System.Drawing.Point(523, 567);
            this.cboRefGrpTitle.Name = "cboRefGrpTitle";
            this.cboRefGrpTitle.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cboRefGrpTitle.Properties.Appearance.Options.UseFont = true;
            this.cboRefGrpTitle.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cboRefGrpTitle.Properties.DropDownItemHeight = 23;
            this.cboRefGrpTitle.Properties.Items.AddRange(new object[] {
            "",
            "Path",
            "Biochem",
            "Haem",
            "Sero",
            "Radio",
            "Sono"});
            this.cboRefGrpTitle.Size = new System.Drawing.Size(180, 22);
            this.cboRefGrpTitle.TabIndex = 44;
            // 
            // cboAccGrpTitle
            // 
            this.cboAccGrpTitle.Location = new System.Drawing.Point(523, 595);
            this.cboAccGrpTitle.Name = "cboAccGrpTitle";
            this.cboAccGrpTitle.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cboAccGrpTitle.Properties.Appearance.Options.UseFont = true;
            this.cboAccGrpTitle.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cboAccGrpTitle.Properties.DropDownItemHeight = 23;
            this.cboAccGrpTitle.Properties.Items.AddRange(new object[] {
            "",
            "Path",
            "Biochem",
            "Haem",
            "Sero",
            "Radio",
            "Sono"});
            this.cboAccGrpTitle.Size = new System.Drawing.Size(180, 22);
            this.cboAccGrpTitle.TabIndex = 46;
            // 
            // labelControl16
            // 
            this.labelControl16.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl16.Location = new System.Drawing.Point(381, 598);
            this.labelControl16.Name = "labelControl16";
            this.labelControl16.Size = new System.Drawing.Size(115, 16);
            this.labelControl16.TabIndex = 45;
            this.labelControl16.Text = "Accounting Group";
            // 
            // LabsEditorDialog
            // 
            this.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 16F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(720, 667);
            this.Controls.Add(this.cboAccGrpTitle);
            this.Controls.Add(this.labelControl16);
            this.Controls.Add(this.cboRefGrpTitle);
            this.Controls.Add(this.labelControl15);
            this.Controls.Add(this.btnReportHeaderEdit);
            this.Controls.Add(this.luReportHeader);
            this.Controls.Add(this.labelControl14);
            this.Controls.Add(this.btnAutoFinalizeStage);
            this.Controls.Add(this.btnAutoWireStage);
            this.Controls.Add(this.labelControl13);
            this.Controls.Add(this.cboPostRC);
            this.Controls.Add(this.cboPostRF);
            this.Controls.Add(this.cboPostRV);
            this.Controls.Add(this.cboPostRE);
            this.Controls.Add(this.cboPostOE);
            this.Controls.Add(this.btnReferralEdit);
            this.Controls.Add(this.btnDiscountEdit);
            this.Controls.Add(this.luReferral);
            this.Controls.Add(this.luDiscount);
            this.Controls.Add(this.GridControl);
            this.Controls.Add(this.rgResultType);
            this.Controls.Add(this.chkIsAux);
            this.Controls.Add(this.chkPrintCanon);
            this.Controls.Add(this.chkIsActive);
            this.Controls.Add(this.txtCode);
            this.Controls.Add(this.txtPrintName);
            this.Controls.Add(this.txtName);
            this.Controls.Add(this.lblId);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.labelControl4);
            this.Controls.Add(this.labelControl11);
            this.Controls.Add(this.labelControl12);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.labelControl10);
            this.Controls.Add(this.labelControl9);
            this.Controls.Add(this.labelControl8);
            this.Controls.Add(this.labelControl6);
            this.Controls.Add(this.labelControl7);
            this.Controls.Add(this.labelControl5);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.btnNew);
            this.Controls.Add(this.btnReset);
            this.Controls.Add(this.btnSave);
            this.Controls.Add(this.btnClose);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.LookAndFeel.SkinName = "Office 2010 Silver";
            this.LookAndFeel.UseDefaultLookAndFeel = false;
            this.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "LabsEditorDialog";
            this.ShowInTaskbar = false;
            this.SizeGripStyle = System.Windows.Forms.SizeGripStyle.Hide;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "Edit Labs...";
            ((System.ComponentModel.ISupportInitialize)(this.chkIsActive.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.GridControl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataRowTemplate2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.columnManagerRow2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCode.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPrintName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkPrintCanon.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rgResultType.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkIsAux.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.luDiscount.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.luReferral.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cboPostOE.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cboPostRE.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cboPostRV.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cboPostRF.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cboPostRC.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.luReportHeader.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cboRefGrpTitle.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cboAccGrpTitle.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.CheckEdit chkIsActive;
        private DevExpress.XtraEditors.TextEdit txtName;
        private DevExpress.XtraEditors.LabelControl lblId;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.SimpleButton btnNew;
        private DevExpress.XtraEditors.SimpleButton btnReset;
        private DevExpress.XtraEditors.SimpleButton btnSave;
        private DevExpress.XtraEditors.SimpleButton btnClose;
        private Xceed.Grid.GridControl GridControl;
        private Xceed.Grid.DataRow dataRowTemplate2;
        private Xceed.Grid.VisualGridElementStyle visualGridElementStyle2;
        private Xceed.Grid.VisualGridElementStyle dataRowStyle1;
        private Xceed.Grid.ColumnManagerRow columnManagerRow2;
        private DevExpress.XtraEditors.TextEdit txtCode;
        private DevExpress.XtraEditors.TextEdit txtPrintName;
        private DevExpress.XtraEditors.CheckEdit chkPrintCanon;
        private DevExpress.XtraEditors.RadioGroup rgResultType;
        private DevExpress.XtraEditors.CheckEdit chkIsAux;
        private DevExpress.XtraEditors.LookUpEdit luDiscount;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.LabelControl labelControl8;
        private DevExpress.XtraEditors.LabelControl labelControl9;
        private DevExpress.XtraEditors.SimpleButton btnDiscountEdit;
        private DevExpress.XtraEditors.SimpleButton btnReferralEdit;
        private DevExpress.XtraEditors.LookUpEdit luReferral;
        private DevExpress.XtraEditors.ComboBoxEdit cboPostOE;
        private DevExpress.XtraEditors.ComboBoxEdit cboPostRE;
        private DevExpress.XtraEditors.LabelControl labelControl10;
        private DevExpress.XtraEditors.LabelControl labelControl11;
        private DevExpress.XtraEditors.ComboBoxEdit cboPostRV;
        private DevExpress.XtraEditors.ComboBoxEdit cboPostRF;
        private DevExpress.XtraEditors.LabelControl labelControl12;
        private DevExpress.XtraEditors.ComboBoxEdit cboPostRC;
        private DevExpress.XtraEditors.LabelControl labelControl13;
        private DevExpress.XtraEditors.SimpleButton btnAutoWireStage;
        private DevExpress.XtraEditors.SimpleButton btnAutoFinalizeStage;
        private DevExpress.XtraEditors.SimpleButton btnReportHeaderEdit;
        private DevExpress.XtraEditors.LookUpEdit luReportHeader;
        private DevExpress.XtraEditors.LabelControl labelControl14;
        private DevExpress.XtraEditors.LabelControl labelControl15;
        private DevExpress.XtraEditors.ComboBoxEdit cboRefGrpTitle;
        private DevExpress.XtraEditors.ComboBoxEdit cboAccGrpTitle;
        private DevExpress.XtraEditors.LabelControl labelControl16;
    }
}