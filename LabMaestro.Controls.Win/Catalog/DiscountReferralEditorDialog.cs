﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: DiscountReferralEditorDialog.cs 627 2013-06-20 05:50:16Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Globalization;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using LabMaestro.BusinessLogic;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Shared;
using Xceed.Grid;

namespace LabMaestro.Controls.Win
{
    public partial class DiscountReferralEditorDialog : XtraForm
    {
        private bool _isNewItem;

        public DiscountReferralEditorDialog()
        {
            AuthHelper.GuardAccess();
            InitializeComponent();
            WinUtils.ApplyTheme(this);

            GridControl.Columns.Clear();
            GridControl.GridAddColumn(@"Id", "Id", 0, 60);
            GridControl.GridAddColumn(@"Name", "Name", 1, 140);
            GridControl.GridAddColumn(@"Mode", "Mode", 2, 100);
            GridControl.GridAddColumn(@"Amount", "Amount", 3, 70);
        }

        public ICatalogDataAccessProvider<DiscountLevelSlice> DiscountDataProvider { get; set; }

        public bool IsDiscountLevelEditor { get; set; }

        public ICatalogDataAccessProvider<ReferralGroupSlice> ReferralDataProvider { get; set; }

        public void UpdateControls()
        {
            _isNewItem = false;
            WinUtils.SetFormTitle(this, IsDiscountLevelEditor ? "Discount Levels" : "Referral Groups");

            chkIsActive.Visible = !IsDiscountLevelEditor;
            populateGridControl();
            populateEditorControls(null as DiscountLevelSlice);
            GridControl.Select();
        }

        private void GridControl_SelectedRowsChanged(object sender, EventArgs e)
        {
            _isNewItem = false;
            autoPopulateEditorControls();
        }

        private void autoPopulateEditorControls()
        {
            if (IsDiscountLevelEditor)
            {
                populateEditorControls(getSelectedDiscountItem());
            }
            else
            {
                populateEditorControls(getSelectedReferralItem());
            }
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        private void btnNew_Click(object sender, EventArgs e)
        {
            _isNewItem = true;
            populateEditorControls(null as DiscountLevelSlice);
            txtName.Focus();
        }

        private void btnReset_Click(object sender, EventArgs e)
        {
            if (!_isNewItem)
            {
                autoPopulateEditorControls();
            }
            txtName.Focus();
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            var name = txtName.Text.Trim();
            var displayName = txtDisplayName.Text.Trim();
            if (string.IsNullOrEmpty(name))
            {
                MessageDlg.Error("Name must not be empty!");
                return;
            }

            IncentiveType mode;
            double percent = 0;
            decimal amount = 0m;

            switch (rgMode.SelectedIndex)
            {
                case 1:
                    if (!double.TryParse(txtAmount.Text.Trim(), out percent))
                    {
                        MessageDlg.Error("Invalid discount percent given!");
                        return;
                    }
                    mode = IncentiveType.Percentage;
                    break;
                case 2:
                    if (!decimal.TryParse(txtAmount.Text.Trim(), out amount))
                    {
                        MessageDlg.Error("Invalid discount amount given!");
                        return;
                    }
                    mode = IncentiveType.FlatRate;
                    break;
                default:
                    mode = IncentiveType.None;
                    break;
            }

            if (_isNewItem)
            {
                if (IsDiscountLevelEditor)
                {
                    var item = new DiscountLevelSlice
                        {
                            Name = name,
                            DiscountMode = (byte) mode,
                            DiscountPercent = percent,
                            DiscountAmount = amount
                        };

                    DiscountDataProvider.AddNew(item);
                }
                else
                {
                    var item = new ReferralGroupSlice
                        {
                            Name = name,
                            ReferralMode = (byte) mode,
                            ReferralPercent = percent,
                            ReferralAmount = amount,
                            IsActive = chkIsActive.Checked
                        };

                    ReferralDataProvider.AddNew(item);
                }
            }
            else
            {
                if (IsDiscountLevelEditor)
                {
                    var item = getSelectedDiscountItem();
                    if (item != null)
                    {
                        item.Name = name;
                        item.DiscountMode = (byte) mode;
                        item.DiscountPercent = percent;
                        item.DiscountAmount = amount;
                    }

                    DiscountDataProvider.Save(item);
                }
                else
                {
                    var item = getSelectedReferralItem();
                    if (item != null)
                    {
                        item.Name = name;
                        //item. = displayName;
                        item.ReferralMode = (byte) mode;
                        item.ReferralPercent = percent;
                        item.ReferralAmount = amount;
                        item.IsActive = chkIsActive.Checked;
                    }

                    ReferralDataProvider.Save(item);
                }
            }

            UpdateControls();
        }

        private void enableControls()
        {
            btnReset.Enabled = !_isNewItem;
            btnNew.Enabled = !_isNewItem;
            chkIsActive.Visible = !IsDiscountLevelEditor;
        }

        private DiscountLevelSlice getSelectedDiscountItem()
        {
            if ((GridControl.SelectedRows.Count == 1) && (GridControl.SelectedRows[0] != null))
            {
                return GridControl.SelectedRows[0].Tag as DiscountLevelSlice;
            }
            return null;
        }

        private ReferralGroupSlice getSelectedReferralItem()
        {
            if ((GridControl.SelectedRows.Count == 1) && (GridControl.SelectedRows[0] != null))
            {
                return GridControl.SelectedRows[0].Tag as ReferralGroupSlice;
            }
            return null;
        }

        private DataRow internalCreateGridRow(short id, string name, IncentiveType mode, decimal amount, double percent)
        {
            var row = GridControl.DataRows.AddNew();
            row.Cells[@"Id"].Value = id.ToString(CultureInfo.InvariantCulture);
            row.Cells[@"Name"].Value = name;

            switch (mode)
            {
                case IncentiveType.None:
                    row.Cells[@"Mode"].Value = "None";
                    row.Cells[@"Amount"].Value = string.Empty;
                    break;
                case IncentiveType.Percentage:
                    row.Cells[@"Mode"].Value = "Percentage";
                    row.Cells[@"Amount"].Value = SharedUtilities.DoubleToString(percent, "%");
                    break;
                case IncentiveType.FlatRate:
                    row.Cells[@"Mode"].Value = "Flat-rate";
                    row.Cells[@"Amount"].Value = SharedUtilities.MoneyToString(amount);
                    break;
            }

            row.Height = 23;
            return row;
        }

        private void internalPopulateEditorControls(short id,
                                                    string name,
                                                    IncentiveType mode,
                                                    decimal amount,
                                                    double percent,
                                                    bool active)
        {
            txtName.Text = name;
            lblItemId.Text = id.ToString(CultureInfo.InvariantCulture);
            switch (mode)
            {
                case IncentiveType.None:
                    rgMode.SelectedIndex = 0;
                    txtAmount.Text = string.Empty;
                    txtAmount.Enabled = false;
                    break;
                case IncentiveType.Percentage:
                    rgMode.SelectedIndex = 1;
                    txtAmount.Text = SharedUtilities.DoubleToStringPlain(percent);
                    lblAmtPct.Text = @"Percent";
                    txtAmount.Enabled = true;
                    break;
                case IncentiveType.FlatRate:
                    rgMode.SelectedIndex = 2;
                    txtAmount.Text = SharedUtilities.MoneyToStringPlain(amount);
                    lblAmtPct.Text = @"Amount";
                    txtAmount.Enabled = true;
                    break;
            }
            chkIsActive.Checked = active;
        }

        private void populateEditorControls(DiscountLevelSlice item)
        {
            resetEditorControls();

            if (item != null)
            {
                internalPopulateEditorControls(
                    item.Id,
                    item.Name,
                    (IncentiveType) item.DiscountMode,
                    item.DiscountAmount,
                    item.DiscountPercent,
                    false);
            }

            enableControls();
        }

        private void populateEditorControls(ReferralGroupSlice item)
        {
            resetEditorControls();

            if (item != null)
            {
                internalPopulateEditorControls(
                    item.Id,
                    item.Name,
                    (IncentiveType) item.ReferralMode,
                    item.ReferralAmount,
                    item.ReferralPercent,
                    item.IsActive);
            }

            enableControls();
        }

        private void populateGridControl()
        {
            GridControl.DataRows.Clear();
            if (IsDiscountLevelEditor)
            {
                var items = DiscountDataProvider.GetAllItems().OrderBy(x => x.Name).ToList();
                foreach (var item in items)
                {
                    var row = internalCreateGridRow(
                        item.Id,
                        item.Name,
                        (IncentiveType) item.DiscountMode,
                        item.DiscountAmount,
                        item.DiscountPercent);
                    row.Tag = item;
                    row.EndEdit();
                }
            }
            else
            {
                var items = ReferralDataProvider.GetAllItems().OrderBy(x => x.Name).ToList();
                foreach (var item in items)
                {
                    var row = internalCreateGridRow(
                        item.Id,
                        item.Name,
                        (IncentiveType) item.ReferralMode,
                        item.ReferralAmount,
                        item.ReferralPercent);
                    row.Tag = item;
                    row.EndEdit();
                }
            }
        }

        private void resetEditorControls()
        {
            txtName.Text = string.Empty;
            txtDisplayName.Text = string.Empty;
            lblItemId.Text = string.Empty;
            txtAmount.Enabled = true;
            txtAmount.Text = string.Empty;
            rgMode.SelectedIndex = -1;
            chkIsActive.Checked = true;
        }
    }
}