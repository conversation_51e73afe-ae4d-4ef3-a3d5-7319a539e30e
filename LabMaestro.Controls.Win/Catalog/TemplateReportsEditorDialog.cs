﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: TemplateReportsEditorDialog.cs 793 2013-07-11 08:25:56Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Linq;
using System.Windows.Forms;
using AutoMapper;
using DevExpress.XtraEditors;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Shared;
using Xceed.Grid;

namespace LabMaestro.Controls.Win
{
    public partial class TemplateReportsEditorDialog : XtraForm, IExecutableDialog
    {
        private DataCell _oldCell;
        private MembershipInfo _selectedGroup;
        private TemplateReportSlice _selectedReport;

        public TemplateReportsEditorDialog()
        {
            InitializeComponent();
            WinUtils.ApplyTheme(this);
            WinUtils.SetFormTitle(this, "Template Reports");
        }

        public bool ExecuteDialog(IWin32Window parent)
        {
            UpdateControls();
            ShowDialog(parent);
            return true;
        }

        public void UpdateControls()
        {
        }

        private void TemplateReportsEditorDialog_Load(object sender, EventArgs e)
        {
            createGridColumns();
            resetEditorControls();
            populateLookUp();
            populateGrid();
        }

        private void btnGroupEdit_Click(object sender, EventArgs e)
        {
            using (var frm = new TemplateGroupsEditorDialog())
            {
                frm.UpdateControls();
                frm.ShowDialog();
            }

            populateGrid();
            populateLookUp();
        }

        private void populateLookUp()
        {
            var groups = TemplateGroupsRepository
                .GetAllTemplateGroups()
                .OrderBy(x => x.Name)
                .ToList();

            WinUtils.PopulateLookupControl(luGroup, groups, "Name", "Id", new[] {"Name"});
        }

        private void createGridColumns()
        {
            var i = 0;

            gridControl.GridAddColumn("id", "Id", i++, 40);
            gridControl.GridAddColumn("name", "Name", i++, 210);
            gridControl.GridAddColumn("sort", "Sort", i++, 60);
            gridControl.GridAddColumn("act", "Y?", i++, 30);
        }

        private void populateGrid()
        {
            gridControl.BeginInit();
            WaitFormControl.WaitStart(this);
            try
            {
                gridControl.DataRows.Clear();
                var reports = TemplateReportsRepository.GetAllTemplateReports().OrderBy(x => x.Name).ToList();
                foreach (var templateReport in reports)
                {
                    var row = gridControl.DataRows.AddNew();
                    row.Cells["id"].Value = templateReport.Id.ToString();
                    row.Cells["name"].Value = templateReport.Name;
                    row.Cells["act"].Value = templateReport.IsActive ? "Y" : "N";
                    row.Cells["sort"].Value = EnumUtils.EnumDescription((SortPriorityType) templateReport.SortPriority);
                    row.Tag = templateReport;
                    gridControl.GridWireCellToolTips(row.Cells, showToolTip);
                    row.EndEdit();
                }
            }
            finally
            {
                WaitFormControl.WaitEnd(this, DefaultCursor);
                gridControl.EndInit();
            }
        }

        private void showToolTip(object sender, MouseEventArgs e)
        {
            var cell = (DataCell) sender;
            if (_oldCell == cell) return;

            _oldCell = cell;
            var content = _oldCell.Value;
            toolTip.SetToolTip(gridControl, (content != null) ? content.ToString() : string.Empty);
        }

        private void gridControl_SelectedRowsChanged(object sender, EventArgs e)
        {
            _selectedReport = null;
            if (gridControl.SelectedRows != null && gridControl.SelectedRows.Count == 1)
            {
                _selectedReport = gridControl.SelectedRows[0].Tag as TemplateReportSlice;
            }
            populateEditorControls();
        }

        private void resetEditorControls()
        {
            txtName.Text = string.Empty;
            txtTags.Text = string.Empty;
            lblId.Text = string.Empty;
            richEdit.Text = string.Empty;
            chkIsActive.Checked = true;
            luGroup.EditValue = null;
            listBox.Items.Clear();
            rgSort.SelectedIndex = -1;
            _selectedGroup = null;

            btnNew.Enabled = true;
            btnAdd.Enabled = false;
            btnSave.Enabled = false;
        }

        private void populateEditorControls()
        {
            resetEditorControls();
            if (_selectedReport != null)
            {
                WaitFormControl.WaitStart(this);
                try
                {
                    lblId.Text = _selectedReport.Id.ToString();
                    txtName.Text = _selectedReport.Name;
                    txtTags.Text = _selectedReport.Tags;
                    chkIsActive.Checked = _selectedReport.IsActive;
                    setSortPriority((SortPriorityType) _selectedReport.SortPriority);
                    luGroup.EditValue = null;
                    var membershipSlices =
                        TemplateReportsRepository.GetGroupMembershipsForTemplateReport(_selectedReport.Id);

                    listBox.BeginUpdate();
                    foreach (var membershipSlice in membershipSlices)
                    {
                        listBox.Items.Add(MembershipInfo.AssembleFrom(membershipSlice));
                    }
                    listBox.EndUpdate();

                    var content = TemplateReportsRepository.GetTemplateReportContent(_selectedReport.Id);
                    richEdit.BeginUpdate();
                    richEdit.RtfText = content;
                    richEdit.EndUpdate();

                    btnSave.Enabled = true;
                }
                finally
                {
                    WaitFormControl.WaitEnd(this, DefaultCursor);
                }
            }
        }

        private void listBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            _selectedGroup = listBox.SelectedItem as MembershipInfo;
        }

        private void btnGroupAdd_Click(object sender, EventArgs e)
        {
            var groupToAdd = (short) luGroup.EditValue;
            if (groupToAdd > 0 && _selectedReport != null)
            {
                WaitFormControl.WaitStart(this, description: "Saving data...");
                try
                {
                    TemplateReportsRepository.CreateNewTemplateReportGroupLink(_selectedReport.Id, groupToAdd);
                }
                finally
                {
                    WaitFormControl.WaitEnd(this, DefaultCursor);
                }

                populateEditorControls();
            }
        }

        private void btnGroupDelete_Click(object sender, EventArgs e)
        {
            if (_selectedGroup != null && _selectedReport != null)
            {
                WaitFormControl.WaitStart(this, description: "Deleting...");
                try
                {
                    TemplateReportsRepository.DeleteMembershipLink(_selectedGroup.LinkId);
                }
                finally
                {
                    WaitFormControl.WaitEnd(this, DefaultCursor);
                }
            }

            populateEditorControls();
        }

        private void btnNew_Click(object sender, EventArgs e)
        {
            resetEditorControls();
            _selectedReport = null;

            btnNew.Enabled = false;
            btnAdd.Enabled = true;
        }

        private SortPriorityType getSortPriority()
        {
            switch (rgSort.SelectedIndex)
            {
                case 0:
                    return SortPriorityType.Critical;
                case 1:
                    return SortPriorityType.High;
                case 3:
                    return SortPriorityType.Low;
                case 4:
                    return SortPriorityType.Lowest;
                default:
                    return SortPriorityType.Normal;
            }
        }

        private void setSortPriority(SortPriorityType sort)
        {
            switch (sort)
            {
                case SortPriorityType.Critical:
                    rgSort.SelectedIndex = 0;
                    break;
                case SortPriorityType.High:
                    rgSort.SelectedIndex = 1;
                    break;
                case SortPriorityType.Normal:
                    rgSort.SelectedIndex = 2;
                    break;
                case SortPriorityType.Low:
                    rgSort.SelectedIndex = 3;
                    break;
                case SortPriorityType.Lowest:
                    rgSort.SelectedIndex = 4;
                    break;
            }
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            if (_selectedReport != null)
            {
                MessageDlg.Error("Selected template report cannot be duplicated!");
                return;
            }

            if (validateEditorControls())
            {
                var name = txtName.Text.Trim();
                var tags = getReportTags();
                var content = richEdit.RtfText;
                var sort = getSortPriority();

                WaitFormControl.WaitStart(this, description: "Saving report to database...");
                try
                {
                    TemplateReportsRepository.CreateNewTemplateReport(name, tags, content, sort);
                }
                finally
                {
                    WaitFormControl.WaitEnd(this, DefaultCursor);
                }
            }

            populateGrid();
        }

        private string getReportTags()
        {
            var tags = txtTags.Text.Trim().ToLower().Split(new[] {','}, StringSplitOptions.RemoveEmptyEntries);
            return string.Join(",", tags.Select(x => x.Trim()));
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            if (_selectedReport == null)
            {
                MessageDlg.Error("No template report selected!");
                return;
            }

            if (validateEditorControls())
            {
                var name = txtName.Text.Trim();
                var tags = getReportTags();
                var content = richEdit.RtfText;
                var active = chkIsActive.Checked;
                var sort = getSortPriority();

                WaitFormControl.WaitStart(this, description: "Saving report to database...");
                try
                {
                    TemplateReportsRepository.UpdateTemplateReport(_selectedReport.Id, active, name, sort, tags);
                    TemplateReportsRepository.UpdateTemplateReportContent(_selectedReport.Id, content);
                }
                finally
                {
                    WaitFormControl.WaitEnd(this, DefaultCursor);
                }
            }

            populateGrid();
        }

        private bool validateEditorControls()
        {
            if (string.IsNullOrEmpty(txtName.Text.Trim()))
            {
                MessageDlg.Error("Please enter a valid name.");
                txtName.Select();
                txtName.Focus();
                return false;
            }

            if (string.IsNullOrEmpty(richEdit.Text.Trim()))
            {
                MessageDlg.Error("Report template content cannot be empty!");
                richEdit.Select();
                richEdit.Focus();
                return false;
            }

            return true;
        }

        private void btnExit_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        private void btnGenerateTags_Click(object sender, EventArgs e)
        {
            var name = txtName.Text.Trim().ToLower();
            var words = name.Split(new[] {' ', ',', '-', ':'}, StringSplitOptions.RemoveEmptyEntries);
            txtTags.Text = string.Join(",", words.Select(x => x.Trim())).Trim();
        }

        internal sealed class MembershipInfo
        {
            public short GroupId { get; set; }
            public short LinkId { get; set; }
            public string GroupName { get; set; }
            public bool GroupIsActive { get; set; }

            public static MembershipInfo AssembleFrom(TemplateReportGroupMembershipSlice slice)
            {
                Mapper.CreateMap<TemplateReportGroupMembershipSlice, MembershipInfo>();
                return Mapper.Map<TemplateReportGroupMembershipSlice, MembershipInfo>(slice);
            }

            public override string ToString()
            {
                return string.Format("{0} [{1}]", GroupName, LinkId);
            }
        }
    }
}