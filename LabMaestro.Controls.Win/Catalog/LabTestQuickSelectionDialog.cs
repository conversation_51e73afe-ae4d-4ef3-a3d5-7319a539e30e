﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: LabTestQuickSelectionDialog.cs 680 2013-06-26 12:12:27Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Search;

namespace LabMaestro.Controls.Win;

public partial class LabTestQuickSelectionDialog : XtraForm
{
    private const int MAX_SEARCH_RESULTS = 100;
    private readonly List<LabTestQuickInfo> _filteredCatalog = new();
    private readonly Dictionary<short, LabTestQuickInfo> _selectedTests = new();
    private OrderableTestSliceCatalog _catalog;

    public LabTestQuickSelectionDialog()
    {
        InitializeComponent();
        WinUtils.ApplyTheme(this);
        WinUtils.SetFormTitle(this, "Select Lab Test");
    }

    public List<short> SelectedLabTestIds => _selectedTests.Keys.ToList();

    public bool ConfirmSelection { get; set; }

    private void loadCatalog()
    {
        WaitFormControl.WaitOperation(this, () =>
        {
            _catalog = OrderableTestSliceCatalogRepository.GetCatalog(false);
            LabTestSearchEngine.AssignCatalog(_catalog);
        });
    }

    private void btnClear_Click(object sender, EventArgs e)
    {
        txtSearch.Text = string.Empty;
        selectTextControl();
    }

    private void txtSearch_KeyUp(object sender, KeyEventArgs e)
    {
        switch (e.KeyCode) {
            case Keys.Escape:
                e.Handled = true;
                resetControls();
                break;
            case Keys.Down:
                e.Handled = true;
                grdCatalog.Focus();
                break;
        }
    }

    private void populateGrid()
    {
        gvCatalog.BeginUpdate();
        try {
            catalogLabTestQuickInfoBindingSource.DataSource = _filteredCatalog;
        }
        finally {
            gvCatalog.EndUpdate();
            gvCatalog.RefreshData();
            gvCatalog.MoveFirst();
        }
    }

    public void UpdateControls()
    {
        loadCatalog();
        resetControls();
        populateGrid();
        selectTextControl();
    }

    private void resetControls()
    {
        txtSearch.Text = string.Empty;

        resetFilteredCatalog();

        gvCatalog.BeginDataUpdate();
        try {
            catalogLabTestQuickInfoBindingSource.DataSource = _filteredCatalog;
        }
        finally {
            gvCatalog.EndDataUpdate();
        }
    }

    private void resetFilteredCatalog()
    {
        _filteredCatalog.Clear();
        if (_catalog != null) _filteredCatalog.AddRange(_catalog.Select(LabTestQuickInfo.AssembleFrom));
    }

    private void selectTextControl()
    {
        txtSearch.SelectAll();
        txtSearch.Focus();
    }

    private void txtSearch_TextChanged(object sender, EventArgs e) => performSearch(txtSearch.Text.Trim().ToLowerInvariant());

    private void performSearch(string text)
    {
        if (string.IsNullOrEmpty(text)) {
            resetFilteredCatalog();
        }
        else {
            _filteredCatalog.Clear();
            var foundItems = LabTestSearchEngine.Search($"*{text}*", MAX_SEARCH_RESULTS);
            if (foundItems is { Count: > 0 })
                _filteredCatalog.AddRange(foundItems.Select(LabTestQuickInfo.AssembleFrom));
            else
                resetFilteredCatalog();
        }

        populateGrid();
    }

    private LabTestQuickInfo? catalogGetFocusedTest() => gvCatalog.SelectedRowsCount == 1 ? gvCatalog.GetFocusedRow() as LabTestQuickInfo : null;

    private LabTestQuickInfo? selectionGetFocusedTest() => gvSelected.SelectedRowsCount == 1 ? gvSelected.GetFocusedRow() as LabTestQuickInfo : null;

    private bool checkTestAlreadySelected(short testId)
    {
        if (!_selectedTests.ContainsKey(testId)) return true;

        MessageDlg.Warning("Test was already added to selection.");
        return false;
    }

    private void addTestToSelection(LabTestQuickInfo testInfo)
    {
        _selectedTests.Add(testInfo.TestId, testInfo);
        updateSelectedGridView();
    }

    private void updateSelectedGridView()
    {
        gvSelected.BeginDataUpdate();
        try {
            selectedLabTestQuickInfoBindingSource.DataSource = _selectedTests.Values.ToList();
        }
        finally {
            gvSelected.EndDataUpdate();
        }
    }

    private void addCatalogFocusedTestToSelected()
    {
        var testInfo = catalogGetFocusedTest();
        if (testInfo == null) return;
        if (checkTestAlreadySelected(testInfo.TestId))
            addTestToSelection(testInfo);
    }

    private void btnSelect_Click(object sender, EventArgs e)
    {
        if (SelectedLabTestIds.Count == 0) {
            MessageDlg.Warning("No Lab Test Selected");
            return;
        }

        DialogResult = DialogResult.OK;
    }

    private void btnCancel_Click(object sender, EventArgs e) => resetControls();

    private void gridView_KeyUp(object sender, KeyEventArgs e)
    {
        switch (e.KeyCode) {
            case Keys.Enter:
                e.Handled = true;
                addCatalogFocusedTestToSelected();
                break;
        }
    }

    public static List<short>? ExecuteDialog(Form parent, bool confirmSelection)
    {
        using var frm = new LabTestQuickSelectionDialog();
        frm.ConfirmSelection = confirmSelection;
        frm.UpdateControls();
        return frm.ShowDialog(parent) == DialogResult.OK ? frm.SelectedLabTestIds : null;
    }

    private void LabTestQuickSelectionDialog_KeyUp(object sender, KeyEventArgs e)
    {
        switch (e.KeyCode) {
            case Keys.F5:
                selectTextControl();
                e.Handled = true;
                break;
            case Keys.Escape:
                e.Handled = true;
                resetControls();
                selectTextControl();
                break;
        }
    }

    private void gvCatalog_DoubleClick(object sender, EventArgs e) => addCatalogFocusedTestToSelected();

    private void btnDeleteSelectedTest_Click(object sender, EventArgs e)
    {
        var testInfo = selectionGetFocusedTest();
        if (testInfo != null) {
            _selectedTests.Remove(testInfo.TestId);
            updateSelectedGridView();
        }
    }

    private void btnClearSelectedTests_Click(object sender, EventArgs e)
    {
        _selectedTests.Clear();
        updateSelectedGridView();
    }
}