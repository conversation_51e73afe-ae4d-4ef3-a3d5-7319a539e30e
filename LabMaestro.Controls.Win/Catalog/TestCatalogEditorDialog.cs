﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: TestCatalogEditorDialog.cs 1535 2014-11-29 14:16:26Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using C1.LiveLinq;
using C1.LiveLinq.Collections;
using DevExpress.XtraBars;
using DevExpress.XtraEditors;
using LabMaestro.BusinessLogic;
using LabMaestro.Domain;
using LabMaestro.Shared;

namespace LabMaestro.Controls.Win
{
    public partial class TestCatalogEditorDialog : XtraForm
    {
        private readonly IndexedCollection<LabTestSlice> _testsInLab;
        private LabInfoSlice _currentlySelectedLab;
        private string _searchString;
        private bool _suppressTabIndexChange;

        public TestCatalogEditorDialog()
        {
            _suppressTabIndexChange = false;
            _testsInLab = new IndexedCollection<LabTestSlice>();
            InitializeComponent();
            WinUtils.ApplyTheme(this);
            WinUtils.SetFormTitle(this, "Edit Test Catalog");
            createGridColumns();
            LabsRepository.PreCacheLabNames();
        }

        public ICatalogDataAccessProvider<LabSlice> LabsDataProvider { get; set; }

        private void populateLabsListbox()
        {
            lbLabs.BeginUpdate();
            try
            {
                lbLabs.Items.Clear();
                var labs = LabsDataProvider.GetAllItems();
                if (labs != null)
                {
                    lbLabs.Items.Add(new LabInfoSlice {LabId = -1, Name = "<ALL>"});
                    foreach (var lab in labs)
                    {
                        lbLabs.Items.Add(LabInfoSlice.AssembleFrom(lab));
                    }
                }
                lbLabs.SelectedIndex = 0;
            }
            finally
            {
                lbLabs.EndUpdate();
                lbLabs.Refresh();
            }
        }

        private void assignTests(List<LabTestSlice> list)
        {
            _testsInLab.BeginUpdate();
            try
            {
                _testsInLab.Clear();
                _testsInLab.AddRange(list);
            }
            finally
            {
                _testsInLab.EndUpdate();
            }
        }

        private List<LabTestSlice> getCuratedTests()
        {
            if (tabFilter.SelectedIndex == 0)
                return _testsInLab.AsIndexed()
                                  .OrderBy(t => t.ShortName.Indexed())
                                  .ToList();

            var filterChar = Convert.ToChar(tabFilter.SelectedIndex + 64);
            return _testsInLab.AsIndexed()
                              .Where(x => x.ShortName.Indexed()[0] == filterChar)
                              .OrderBy(x => x.ShortName)
                              .ToList();
        }

        private void findTestsInLab()
        {
            resetControls();
            if (_currentlySelectedLab == null || _currentlySelectedLab.LabId == -1)
            {
                var testsInLab = LabTestsRepository.FindAllTests().ToList();
                var slices = testsInLab.Select(labTest => LabTestSlice.AssembleFrom(labTest, false)).ToList();
                assignTests(slices);
            }
            else
            {
                var testsInLab = LabTestsRepository.FindAllTestsForPerformingLab(_currentlySelectedLab.LabId);
                var slices = testsInLab.Select(labTest => LabTestSlice.AssembleFrom(labTest, false)).ToList();
                assignTests(slices);
            }
        }

        private void searchForTests(string searchString)
        {
            resetControls();
            _searchString = searchString;
            var list = _testsInLab.AsIndexed()
                .Where(x => x.ShortName.Indexed().ToLower().Contains(_searchString))
                .ToList();
            assignTests(list);
        }

        private void resetControls()
        {
            _suppressTabIndexChange = true;
            tabFilter.SelectedIndex = 0;
            _searchString = string.Empty;
            _suppressTabIndexChange = false;
        }

        private void gridPopulateRows()
        {
            GridControl.BeginInit();
            try
            {
                GridControl.DataRows.Clear();
                foreach (var testSlice in getCuratedTests())
                {
                    var row = GridControl.DataRows.AddNew();
                    row.Cells["sku"].Value = testSlice.TestSKU;
                    row.Cells["name"].Value = testSlice.ShortName;
                    row.Cells["active"].Value = testSlice.IsActive ? "Y" : "N";
                    row.Cells["bi"].Value = testSlice.BillableItemLinks.Count > 0 ? "Y" : "N";
                    row.Cells["rc"].Value = testSlice.DiscreteReportLineItemSlices.Count > 0 ? "Y" : "N";
                    row.Cells["sort"].Value = EnumUtils.EnumDescription(testSlice.ReportSortPriority);
                    row.Cells["result"].Value = LabsRepository.GetLabName(testSlice.ResultingLabId);
                    row.Cells["price"].Value = SharedUtilities.MoneyToStringPlain(testSlice.ListPrice);
                    row.Tag = testSlice;
                    row.Height = 23;
                    row.EndEdit();
                }
            }
            finally
            {
                GridControl.EndInit();
                GridControl.Refresh();
            }
        }

        private void createGridColumns()
        {
            GridControl.BeginInit();
            try
            {
                GridControl.Columns.Clear();
                var i = 0;
                GridControl.GridAddColumn("sku", "SKU", i++, 120);
                GridControl.GridAddColumn("name", "Name", i++, 220);
                GridControl.GridAddColumn("price", "Price", i++, 80);
                GridControl.GridAddColumn("result", "Result", i++, 120);
                GridControl.GridAddColumn("active", "Ac?", i++, 40);
                GridControl.GridAddColumn("bi", "BI?", i++, 40);
                GridControl.GridAddColumn("rc", "RC?", i++, 40);
                GridControl.GridAddColumn("sort", "Sort", i++, 70);
            }
            finally
            {
                GridControl.EndInit();
            }
        }

        private void lbLabs_SelectedIndexChanged(object sender, EventArgs e)
        {
            _currentlySelectedLab = lbLabs.SelectedItem as LabInfoSlice;
            if (_currentlySelectedLab != null)
            {
                var cursor = WaitFormControl.WaitStart(this);
                try
                {
                    findTestsInLab();
                    gridPopulateRows();
                    updateStatusLabels();
                }
                finally
                {
                    WaitFormControl.WaitEnd(this, cursor);
                }
            }
        }

        private void updateStatusLabels()
        {
            stbLabsCount.Caption = string.Format("Lab(s): {0}  ", lbLabs.ItemCount);
            stbTestsCount.Caption = string.Format("Test(s): {0}  ", GridControl.DataRows.Count);
        }

        private void TestCatalogEditorDialog_Load(object sender, EventArgs e)
        {
            populateLabsListbox();
            //populateTestsGrid();
            updateStatusLabels();
        }

        private LabTestSlice getSelectedTestSlice()
        {
            if (GridControl.SelectedRows != null && GridControl.SelectedRows.Count == 1)
            {
                return GridControl.SelectedRows[0].Tag as LabTestSlice;
            }

            return null;
        }

        private void GridControl_KeyUp(object sender, KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.F2:
                    var slice = getSelectedTestSlice();
                    if (slice != null)
                    {
                        slice.InitializeChildItems();
                        editLabTest(slice);
                    }
                    e.Handled = true;
                    break;
            }
        }

        private void editLabTest(LabTestSlice slice)
        {
            using (var form = new TestEditDialog())
            {
                form.CurrentLabTest = slice;
                form.UpdateControls();
                form.ShowDialog();
            }
        }

        private void btnReloadTests_ItemClick(object sender, ItemClickEventArgs e)
        {
            WaitFormControl.WaitStart(this);
            try
            {
                findTestsInLab();
                gridPopulateRows();
            }
            finally
            {
                WaitFormControl.WaitEnd(this, DefaultCursor);
            }
        }

        private void btnCreateNewTest_ItemClick(object sender, ItemClickEventArgs e)
        {
            if (_currentlySelectedLab == null)
            {
                MessageDlg.Warning("Please select a Lab to create the test.");
                return;
            }

            LabTest newTest = null;
            using (var frm = new NewTestDialog())
            {
                if (frm.ShowDialog() == DialogResult.OK)
                {
                    var cursor = WaitFormControl.WaitStart(this, description: "Creating New Test...");
                    try
                    {
                        newTest = LabTestsRepository.CreateNew(frm.TestName, frm.TestPrice);
                        newTest.TestSKU = string.Format("{0}.{1}",
                                                        SharedUtilities.Chomp(_currentlySelectedLab.Name, 15),
                                                        SharedUtilities.RandomNumericString(4));
                        newTest.PerformingLabId = _currentlySelectedLab.LabId;
                        newTest.ResultingLabId = _currentlySelectedLab.LabId;
                        LabTestsRepository.Save();
                        LabTestsRepository.Refresh();
                    }
                    finally
                    {
                        WaitFormControl.WaitEnd(this, cursor);
                    }
                }
            }

            if (newTest != null)
            {
                editLabTest(LabTestSlice.AssembleFrom(newTest, true));
            }
        }

        private void TestCatalogEditorDialog_Resize(object sender, EventArgs e)
        {
            //GridControl.Left = 0;
            //GridControl.Top = tabFilter.Top + tabFilter.Height + 2;
            //GridControl.Height = splitContainerControl1.Panel2.ClientSize.Height - GridControl.Top;
            //GridControl.Width = splitContainerControl1.Panel2.ClientSize.Width;
        }

        private void tabFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (!_suppressTabIndexChange)
            {
                gridPopulateRows();
            }
        }

        private void btnSearch_ItemClick(object sender, ItemClickEventArgs e)
        {
            var searchString = WinUtils.InputBox("Search String", _searchString).Trim().ToLower();
            if (!string.IsNullOrEmpty(searchString))
            {
                searchForTests(searchString);
                gridPopulateRows();
            }
        }

        internal class LabInfoSlice
        {
            internal short LabId { get; set; }
            internal string Name { get; set; }

            internal static LabInfoSlice AssembleFrom(LabSlice slice)
            {
                return new LabInfoSlice {LabId = slice.Id, Name = slice.Name};
            }

            public override string ToString()
            {
                return Name;
            }
        }
    }
}