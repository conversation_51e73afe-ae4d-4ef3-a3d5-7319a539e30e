﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the organization nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: LabsEditorDialog.cs 960 2013-09-30 14:08:44Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using LabMaestro.BusinessLogic;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Shared;

namespace LabMaestro.Controls.Win
{
    public partial class LabsEditorDialog : XtraForm
    {
        private bool _isNewItem;

        public LabsEditorDialog()
        {
            AuthHelper.GuardAccess();
            InitializeComponent();
            WinUtils.ApplyTheme(this);
            WinUtils.SetFormTitle(this, "Labs");

            _isNewItem = false;
            GridControl.Columns.Clear();
            GridControl.GridAddColumn(@"Id", "Id", 0, 70);
            GridControl.GridAddColumn(@"Name", "Name", 1, 190);
            GridControl.GridAddColumn(@"Code", "Code", 2, 90);
            GridControl.GridAddColumn(@"Result", "Result Type", 3, 120);
            GridControl.GridAddColumn(@"Active", "Active?", 4, 60);
            GridControl.GridAddColumn(@"Aux", "Aux.?", 5, 60);
        }

        public ICatalogDataAccessProvider<DiscountLevelSlice> DiscountsDataProvider { get; set; }

        public ICatalogDataAccessProvider<LabSlice> LabsDataProvider { get; set; }

        public ICatalogDataAccessProvider<ReferralGroupSlice> ReferralsDataProvider { get; set; }

        public ICatalogDataAccessProvider<LabReportHeaderSlice> ReportHeadersDataProvider { get; set; }

        public void UpdateControls()
        {
            _isNewItem = false;
            populateLookupControls();
            resetEditorControls();
            populateGridControl();
            GridControl.Focus();
        }

        private static byte? getWorkflowStageType(ComboBoxEdit cbo)
        {
            switch (cbo.SelectedIndex)
            {
                case 1:
                    return (byte) WorkflowStageType.OrderEntry;
                case 2:
                    return (byte) WorkflowStageType.ResultEntry;
                case 3:
                    return (byte) WorkflowStageType.ResultValidation;
                case 4:
                    return (byte) WorkflowStageType.ReportFinalization;
                case 5:
                    return (byte) WorkflowStageType.ReportCollation;
                case 6:
                    return (byte) WorkflowStageType.ReportDispatch;
                case 7:
                    return (byte) WorkflowStageType.OrderFulfillment;
                default:
                    return null;
            }
        }

        private static int workflowStageToInt(WorkflowStageType stage)
        {
            switch (stage)
            {
                case WorkflowStageType.OrderEntry:
                    return 1;
                case WorkflowStageType.ResultEntry:
                    return 2;
                case WorkflowStageType.ResultValidation:
                    return 3;
                case WorkflowStageType.ReportFinalization:
                    return 4;
                case WorkflowStageType.ReportCollation:
                    return 5;
                case WorkflowStageType.ReportDispatch:
                    return 6;
                case WorkflowStageType.OrderFulfillment:
                    return 7;
                default:
                    return -1;
            }
        }

        private static void populateLookupControl(LookUpEdit control, List<LookupEntry> items)
        {
            control.Properties.Columns.Clear();
            control.Properties.Columns.Add(new LookUpColumnInfo(@"Name", 0));
            control.Properties.Columns.Add(new LookUpColumnInfo(@"Type", 0));
            control.Properties.Columns.Add(new LookUpColumnInfo(@"Amount", 0));

            control.Properties.ValueMember = @"Id";
            control.Properties.DisplayMember = @"Name";
            control.Properties.DataSource = items;

            //control.Properties.PopulateColumns();
        }

        private static void setWorkflowStage(ComboBoxEdit cbo, WorkflowStageType stage)
        {
            cbo.SelectedIndex = workflowStageToInt(stage);
        }

        private void GridControl_SelectedRowsChanged(object sender, EventArgs e)
        {
            populateEditorControls(getSelectedLab());
        }

        private void applyWorkFlowStage(ComboBoxEdit cbo, byte? stage)
        {
            if (stage != null)
            {
                setWorkflowStage(cbo, (WorkflowStageType) stage);
            }
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        private void btnDiscountEdit_Click(object sender, EventArgs e)
        {
            using (var frm = new DiscountReferralEditorDialog())
            {
                frm.IsDiscountLevelEditor = true;
                frm.DiscountDataProvider = DiscountsDataProvider;
                frm.UpdateControls();
                frm.ShowDialog(this);
            }
            UpdateControls();
        }

        private void btnNew_Click(object sender, EventArgs e)
        {
            _isNewItem = true;
            resetEditorControls();
            updateReportHeaderLookupControl();
            txtName.Focus();
        }

        private void btnReferralEdit_Click(object sender, EventArgs e)
        {
            using (var frm = new DiscountReferralEditorDialog())
            {
                frm.IsDiscountLevelEditor = false;
                frm.ReferralDataProvider = ReferralsDataProvider;
                frm.UpdateControls();
                frm.ShowDialog(this);
            }
            UpdateControls();
        }

        private void btnReset_Click(object sender, EventArgs e)
        {
            if (!_isNewItem)
            {
                populateEditorControls(getSelectedLab());
            }
            else
            {
                resetEditorControls();
            }
            txtName.Focus();
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            var name = txtName.Text.Trim();
            if (!checkEmptyString("Name", name))
            {
                return;
            }

            var code = txtCode.Text.Trim();
            if (!checkEmptyString("Lab Code", code))
            {
                return;
            }

            var printName = txtPrintName.Text.Trim();
            if (!checkEmptyString("Req. Foil Print Name", printName))
            {
                return;
            }

            var resType = getResultType();
            var postOe = getWorkflowStageType(cboPostOE);
            var postRe = getWorkflowStageType(cboPostRE);
            var postRv = getWorkflowStageType(cboPostRV);
            var postRf = getWorkflowStageType(cboPostRF);
            var postRc = getWorkflowStageType(cboPostRC);

            short? discLevel = null;
            if (luDiscount.EditValue != null)
            {
                discLevel = (short) luDiscount.EditValue;
            }

            short? refGroup = null;
            if (luReferral.EditValue != null)
            {
                refGroup = (short) luReferral.EditValue;
            }

            int? hdrId = null;
            if (resType == (byte) TestResultType.Discrete && luReportHeader.EditValue != null)
            {
                hdrId = (int) luReportHeader.EditValue;
            }

            if (_isNewItem)
            {
                var slice = new LabSlice
                    {
                        IsActive = chkIsActive.Checked,
                        IsAuxProcedure = chkIsAux.Checked,
                        ReqPrintCanonicalTestName = chkPrintCanon.Checked,
                        Name = name,
                        LabCode = code,
                        ReqPrintName = printName,
                        TestResultType = resType,
                        PostOrderEntryWorkflowStage = postOe,
                        PostResultEntryWorkflowStage = postRe,
                        PostResultVerificationWorkflowStage = postRv,
                        PostResultFinalizationWorkflowStage = postRf,
                        PostReportCollationWorkflowStage = postRc,
                        DiscountLevelId = discLevel,
                        ReferralGroupId = refGroup,
                        ReferralGroupDisplayName = cboRefGrpTitle.Text.Trim(),
                        AccountingGroupDisplayName = cboAccGrpTitle.Text.Trim(),
                        DefaultReportHeaderId = hdrId
                    };

                LabsDataProvider.AddNew(slice);
            }
            else
            {
                var slice = getSelectedLab();
                slice.IsActive = chkIsActive.Checked;
                slice.IsAuxProcedure = chkIsAux.Checked;
                slice.ReqPrintCanonicalTestName = chkPrintCanon.Checked;
                slice.Name = name;
                slice.LabCode = code;
                slice.ReqPrintName = printName;
                slice.TestResultType = resType;
                slice.PostOrderEntryWorkflowStage = postOe;
                slice.PostResultEntryWorkflowStage = postRe;
                slice.PostResultVerificationWorkflowStage = postRv;
                slice.PostResultFinalizationWorkflowStage = postRf;
                slice.PostReportCollationWorkflowStage = postRc;
                slice.DiscountLevelId = discLevel;
                slice.ReferralGroupId = refGroup;
                slice.ReferralGroupDisplayName = cboRefGrpTitle.Text.Trim();
                slice.AccountingGroupDisplayName = cboAccGrpTitle.Text.Trim();
                slice.DefaultReportHeaderId = hdrId;
                LabsDataProvider.Save(slice);
            }

            _isNewItem = false;
            resetEditorControls();
            populateGridControl();
        }

        private bool checkEmptyString(string field, string value)
        {
            if (string.IsNullOrEmpty(value))
            {
                MessageDlg.Error(string.Format("{0} must not be empty!", field));
                return false;
            }
            return true;
        }

        private byte getResultType()
        {
            switch (rgResultType.SelectedIndex)
            {
                case 1:
                    return (byte) TestResultType.Discrete;
                case 2:
                    return (byte) TestResultType.Template;
                case 3:
                    return (byte) TestResultType.UserTemplate;
                default:
                    return (byte) TestResultType.Unarchived;
            }
        }

        private LabSlice getSelectedLab()
        {
            if ((GridControl.SelectedRows.Count == 1) && (GridControl.SelectedRows[0] != null))
            {
                return GridControl.SelectedRows[0].Tag as LabSlice;
            }
            return null;
        }

        private void populateEditorControls(LabSlice slice)
        {
            resetEditorControls();
            if (slice != null)
            {
                lblId.Text = slice.Id.ToString(CultureInfo.InvariantCulture);
                txtName.Text = slice.Name;
                txtCode.Text = slice.LabCode;
                txtPrintName.Text = slice.ReqPrintName;
                chkIsActive.Checked = slice.IsActive;
                chkIsAux.Checked = slice.IsAuxProcedure;
                chkPrintCanon.Checked = slice.ReqPrintCanonicalTestName;
                cboRefGrpTitle.Text = slice.ReferralGroupDisplayName;
                cboAccGrpTitle.Text = slice.AccountingGroupDisplayName;

                applyWorkFlowStage(cboPostOE, slice.PostOrderEntryWorkflowStage);
                applyWorkFlowStage(cboPostRE, slice.PostResultEntryWorkflowStage);
                applyWorkFlowStage(cboPostRV, slice.PostResultVerificationWorkflowStage);
                applyWorkFlowStage(cboPostRF, slice.PostResultFinalizationWorkflowStage);
                applyWorkFlowStage(cboPostRC, slice.PostReportCollationWorkflowStage);

                setResultType((TestResultType) slice.TestResultType);
                luDiscount.EditValue = slice.DiscountLevelId;
                luReferral.EditValue = slice.ReferralGroupId;
                luReportHeader.EditValue = slice.DefaultReportHeaderId;
                updateReportHeaderLookupControl();
            }
        }

        private void populateGridControl()
        {
            GridControl.BeginInit();
            try
            {
                GridControl.DataRows.Clear();
                var items = LabsDataProvider.GetAllItems().OrderBy(x => x.Name).ToList();
                ;
                foreach (var lab in items)
                {
                    var row = GridControl.DataRows.AddNew();
                    row.Cells[0].Value = lab.Id.ToString();
                    row.Cells[1].Value = lab.Name;
                    row.Cells[2].Value = lab.LabCode;
                    row.Cells[3].Value = EnumUtils.EnumDescription((TestResultType) lab.TestResultType);
                    row.Cells[4].Value = SharedUtilities.BooleanToStringYN(lab.IsActive);
                    row.Cells[5].Value = SharedUtilities.BooleanToStringYN(lab.IsAuxProcedure);
                    row.Tag = lab;
                    row.Height = 23;
                    row.EndEdit();
                }
            }
            finally
            {
                GridControl.EndInit();
            }
        }

        private void populateLookupControls()
        {
            var discounts =
                DiscountsDataProvider.GetAllItems().Select(LookupEntry.AssembleFrom).OrderBy(x => x.Name).ToList();
            populateLookupControl(luDiscount, discounts);

            var referrals =
                ReferralsDataProvider.GetAllItems().Select(LookupEntry.AssembleFrom).OrderBy(x => x.Name).ToList();
            populateLookupControl(luReferral, referrals);

            populateReportHeaderLookupControl();
        }

        private void updateReportHeaderLookupControl()
        {
            if (_isNewItem)
            {
                luReportHeader.Enabled = true;
            }
            else
            {
                var lab = getSelectedLab();
                luReportHeader.Enabled = (lab != null) && (lab.TestResultType == (byte) TestResultType.Discrete);
            }
        }

        private void populateReportHeaderLookupControl()
        {
            var headers = ReportHeadersDataProvider.GetAllItems();
            luReportHeader.Properties.Columns.Clear();
            luReportHeader.Properties.Columns.Add(new LookUpColumnInfo(@"Name", 0));

            luReportHeader.Properties.ValueMember = @"Id";
            luReportHeader.Properties.DisplayMember = @"Name";
            luReportHeader.Properties.DataSource = headers;
        }

        private void resetEditorControls()
        {
            lblId.Text = string.Empty;
            txtName.Text = string.Empty;
            txtCode.Text = string.Empty;
            txtPrintName.Text = string.Empty;
            chkIsActive.Checked = false;
            chkIsAux.Checked = false;
            chkPrintCanon.Checked = false;
            cboPostOE.SelectedIndex = -1;
            cboPostRE.SelectedIndex = -1;
            cboPostRV.SelectedIndex = -1;
            cboPostRF.SelectedIndex = -1;
            cboPostRC.SelectedIndex = -1;
            cboRefGrpTitle.SelectedIndex = -1;
            cboAccGrpTitle.SelectedIndex = -1;
            rgResultType.SelectedIndex = -1;
            luDiscount.EditValue = null;
            luReferral.EditValue = null;
            luReportHeader.EditValue = null;
        }

        private void setResultType(TestResultType resultType)
        {
            switch (resultType)
            {
                case TestResultType.Discrete:
                    rgResultType.SelectedIndex = 1;
                    break;
                case TestResultType.Template:
                    rgResultType.SelectedIndex = 2;
                    break;
                case TestResultType.UserTemplate:
                    rgResultType.SelectedIndex = 3;
                    break;
                case TestResultType.Unarchived:
                    rgResultType.SelectedIndex = 0;
                    break;
            }
        }

        private void btnAutoWireStage_Click(object sender, EventArgs e)
        {
            setWorkflowStage(cboPostOE, WorkflowStageType.OrderEntry);
            setWorkflowStage(cboPostRC, WorkflowStageType.ReportCollation);
            setWorkflowStage(cboPostRE, WorkflowStageType.ResultEntry);
            setWorkflowStage(cboPostRF, WorkflowStageType.ReportFinalization);
            setWorkflowStage(cboPostRV, WorkflowStageType.ResultValidation);
        }

        private void btnAutoFinalizeStage_Click(object sender, EventArgs e)
        {
            setWorkflowStage(cboPostOE, WorkflowStageType.ReportFinalization);
            setWorkflowStage(cboPostRE, WorkflowStageType.ReportFinalization);
            setWorkflowStage(cboPostRV, WorkflowStageType.ReportFinalization);
            setWorkflowStage(cboPostRF, WorkflowStageType.ReportFinalization);
            setWorkflowStage(cboPostRC, WorkflowStageType.ReportCollation);
        }

        private void btnReportHeaderEdit_Click(object sender, EventArgs e)
        {
            using (var frm = new LabReportHeadersEditorDialog())
            {
                frm.UpdateControls();
                frm.ShowDialog(this);
            }
            UpdateControls();
        }

        public sealed class LookupEntry
        {
            public string Amount { get; private set; }

            public short Id { get; private set; }

            public string Name { get; private set; }

            public string Type { get; private set; }


            internal static LookupEntry AssembleFrom(DiscountLevelSlice slice)
            {
                var mode = (IncentiveType) slice.DiscountMode;
                var result = new LookupEntry
                    {
                        Id = slice.Id,
                        Name = slice.Name,
                        Type = EnumUtils.EnumDescription(mode)
                    };
                switch (mode)
                {
                    case IncentiveType.Percentage:
                        result.Amount = SharedUtilities.DoubleToString(slice.DiscountPercent, "%");
                        break;
                    case IncentiveType.FlatRate:
                        result.Amount = SharedUtilities.MoneyToStringPlain(slice.DiscountAmount);
                        break;
                    case IncentiveType.None:
                        result.Amount = string.Empty;
                        break;
                }
                return result;
            }

            internal static LookupEntry AssembleFrom(ReferralGroupSlice slice)
            {
                var mode = (IncentiveType) slice.ReferralMode;
                var result = new LookupEntry
                    {Id = slice.Id, Name = slice.Name, Type = EnumUtils.EnumDescription(mode)};
                switch (mode)
                {
                    case IncentiveType.Percentage:
                        result.Amount = SharedUtilities.DoubleToString(slice.ReferralPercent, "%");
                        break;
                    case IncentiveType.FlatRate:
                        result.Amount = SharedUtilities.MoneyToStringPlain(slice.ReferralAmount);
                        break;
                    case IncentiveType.None:
                        result.Amount = string.Empty;
                        break;
                }
                return result;
            }
        }
    }
}