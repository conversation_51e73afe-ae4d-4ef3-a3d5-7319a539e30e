﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2012, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: BillableItemsCatalogEditorDialog.cs 602 2013-06-05 15:13:28Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Globalization;
using System.Windows.Forms;

using DevExpress.XtraEditors;

using LabMaestro.BusinessLogic;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Shared;

namespace LabMaestro.Controls.Win
{
    public partial class BillableItemsCatalogEditorDialog : XtraForm
    {
        #region Fields

        private bool _isNewItem;

        #endregion

        #region Constructors and Destructors

        public BillableItemsCatalogEditorDialog()
        {
            AuthHelper.GuardAccess();
            InitializeComponent();
            WinUtils.ApplyTheme(this);
            WinUtils.SetFormTitle(this, "Billable Items");

            grdBillableItems.Columns.Clear();
            grdBillableItems.GridAddColumn(@"Id", "Id", 0, 70);
            grdBillableItems.GridAddColumn(@"Name", "Name", 1, 200);
            grdBillableItems.GridAddColumn(@"Price", "Price", 2, 90);
            grdBillableItems.GridAddColumn(@"Active", "Active", 3, 60);
        }

        #endregion

        #region Public Properties

        public ICatalogDataAccessProvider<BillableItemSlice> DataProvider { get; set; }

        #endregion

        #region Public Methods and Operators

        public void UpdateControls()
        {
            _isNewItem = false;
            populateBillableItemsGrid();
            populateEditorControls(null);
            grdBillableItems.Select();
        }

        #endregion

        #region Methods

        private void btnClose_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        private void btnNew_Click(object sender, EventArgs e)
        {
            _isNewItem = true;
            populateEditorControls(null);
        }

        private void btnReset_Click(object sender, EventArgs e)
        {
            if (!_isNewItem)
            {
                _isNewItem = false;
                populateEditorControls(getSelectedBillableItemSlice());
            }
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            var name = txtItemName.Text.Trim();
            if (string.IsNullOrEmpty(name))
            {
                MessageDlg.Error("Name must not be empty!");
                return;
            }

            decimal price = 0m;
            if (!decimal.TryParse(txtItemPrice.Text.Trim(), out price))
            {
                MessageDlg.Error("Invalid unit price given!");
                return;
            }

            if (_isNewItem)
            {
                DataProvider.AddNew(
                    new BillableItemSlice { Name = name, UnitPrice = price, IsActive = chkItemIsActive.Checked });
            }
            else
            {
                var slice = getSelectedBillableItemSlice();
                if (slice != null)
                {
                    slice.Name = name;
                    slice.UnitPrice = price;
                    slice.IsActive = chkItemIsActive.Checked;
                    DataProvider.Save(slice);
                }
            }
            UpdateControls();
        }

        private void enableControls()
        {
            btnReset.Enabled = !_isNewItem;
            btnNew.Enabled = !_isNewItem;
        }

        private BillableItemSlice getSelectedBillableItemSlice()
        {
            if ((grdBillableItems.SelectedRows.Count == 1) && (grdBillableItems.SelectedRows[0] != null))
            {
                return grdBillableItems.SelectedRows[0].Tag as BillableItemSlice;
            }
            return null;
        }

        private void grdBillableItems_SelectedRowsChanged(object sender, EventArgs e)
        {
            _isNewItem = false;
            populateEditorControls(getSelectedBillableItemSlice());
        }

        private void populateBillableItemsGrid()
        {
            grdBillableItems.DataRows.Clear();
            grdBillableItems.BeginInit();
            try
            {
                var items = DataProvider.GetAllItems();
                foreach (var slice in items)
                {
                    var row = grdBillableItems.DataRows.AddNew();
                    row.Cells[@"Id"].Value = slice.Id.ToString(CultureInfo.InvariantCulture);
                    row.Cells[@"Name"].Value = slice.Name;
                    row.Cells[@"Price"].Value = SharedUtilities.MoneyToString(slice.UnitPrice, false);
                    row.Cells[@"Active"].Value = SharedUtilities.BooleanToStringYN(slice.IsActive);
                    row.Tag = slice;
                    row.Height = 23;
                    row.EndEdit();
                }
            }
            finally
            {
                grdBillableItems.EndInit();
            }
        }

        private void populateEditorControls(BillableItemSlice slice)
        {
            if (slice == null)
            {
                lblItemId.Text = string.Empty;
                txtItemName.Text = string.Empty;
                txtItemPrice.Text = string.Empty;
                chkItemIsActive.Checked = false;
            }
            else
            {
                lblItemId.Text = slice.Id.ToString();
                txtItemName.Text = slice.Name;
                txtItemPrice.Text = SharedUtilities.MoneyToString(slice.UnitPrice, false);
                chkItemIsActive.Checked = slice.IsActive;
            }
            enableControls();
        }

        #endregion
    }
}