﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2015, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id$
// 
// Last modified: 2015.08.21 3:35 PM
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Views.Base;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.Controls.Win;

public partial class AppReportTemplatesEditorDialog : XtraForm, IExecutableDialog
{
    private static readonly string[] TEMPLATE_CODES =
    {
        AppReportTemplateCodes.ReportFaceSheetA4,
        AppReportTemplateCodes.ReportDiscreteA4,
        AppReportTemplateCodes.ReportDiscretePreliminaryA4,
        AppReportTemplateCodes.ReportDiscreteDraftA4,
        AppReportTemplateCodes.ReportDiscreteWebA4,
        AppReportTemplateCodes.ReportDiscreteRemoteA4,
        AppReportTemplateCodes.ReportDiscreteRemotePlainA4,
        AppReportTemplateCodes.ReportTemplateA4,
        AppReportTemplateCodes.ReportTemplatePreliminaryA4,
        AppReportTemplateCodes.ReportTemplateWebA4,
        AppReportTemplateCodes.ReportTemplateRemoteA4,
        AppReportTemplateCodes.ReportTemplateRemotePlainA4,
        //AppReportTemplateCodes.InvoiceMain6x9,
        AppReportTemplateCodes.InvoiceMain6x9v2,
        AppReportTemplateCodes.InvoiceRemote6x9,
        AppReportTemplateCodes.RequisitionMain6x9v2,
        AppReportTemplateCodes.ShiftInvoices6x9,
        AppReportTemplateCodes.ShiftTransactions6x9,
        AppReportTemplateCodes.FinanceReceivablesSummaryA4,
        AppReportTemplateCodes.FinanceReceptionistSummaryA4,
        AppReportTemplateCodes.FinanceIncomeStatementA4,
        AppReportTemplateCodes.FinanceOutstandingInvoicesStatementA4,
        AppReportTemplateCodes.FinanceIncomeOutstandingRadioA4,
        AppReportTemplateCodes.FinanceOutstandingDuesA4,
        AppReportTemplateCodes.FinanceRefundDiscountA4,
        AppReportTemplateCodes.FinanceTestUtilizationA4,
        AppReportTemplateCodes.FinanceBillableItemUtilizationA4,
        AppReportTemplateCodes.FinancePhlebotomyUtilizationA4,
        AppReportTemplateCodes.FinanceInvoiceAuditTrailA4,
        AppReportTemplateCodes.FinanceDailyReceivablesSummaryTrailA4,
        AppReportTemplateCodes.FinanceDailyDuesRegisterA4,
        AppReportTemplateCodes.FinanceServicesCancellationA4,
        AppReportTemplateCodes.FinanceReferrerBusinessContributionsA4,
        AppReportTemplateCodes.FinanceReferrerEligibilitySummaryA4,
        AppReportTemplateCodes.FinanceDiscountOveragesA4,
        AppReportTemplateCodes.FinanceLabwiseDailyIncomeSummaryA4,
        AppReportTemplateCodes.SysDynamicTabulatedReportA4,
        AppReportTemplateCodes.MarketingReferralDetailsA4,
        AppReportTemplateCodes.MarketingReferralSummaryA4,
        AppReportTemplateCodes.MarketingUnknownReferrersA4,
        AppReportTemplateCodes.CollationManifestA4,
        AppReportTemplateCodes.CollationPendingOrdersManifestA4,
        AppReportTemplateCodes.StaffPerformanceReportA4
    };

    private AppReportTemplateSlice _currentSlice;
    private string _importFilename;

    public AppReportTemplatesEditorDialog()
    {
        InitializeComponent();
        WinUtils.ApplyTheme(this);
        WinUtils.SetFormTitle(this, "Application Report Templates");
    }

    public bool ExecuteDialog(IWin32Window parent)
    {
        UpdateControls();
        ShowDialog(parent);
        return true;
    }

    public void UpdateControls() => populateGridControl();

    private void populateGridControl()
    {
        gridControl.BeginUpdate();
        try {
            appReportTemplateSliceBindingSource.DataSource = loadTemplateSlices();
        }
        finally {
            gridControl.EndUpdate();
        }
    }

    private List<AppReportTemplateSlice> loadTemplateSlices()
    {
        GlobalSettingsRepository.Refresh();
        var list = new List<AppReportTemplateSlice>();
        foreach (var code in TEMPLATE_CODES.OrderBy(s => s)) {
            var slice = AppReportTemplatesRepository.FindTemplateSlice(code.ToLowerInvariant());
            if (slice != null) list.Add(slice);
        }

        return list;
    }

    private string generateFilenameFromReportCode(string code) => Path.ChangeExtension(code.Replace('.', '_'), "mrt").ToLowerInvariant();

    private static string generateUniqueTemplateId(string reportCode) =>
        string.Format("{0}.{1:yyMMdd.HHmmss}.{2}",
                      reportCode, DateTime.Now,
                      SharedUtilities.RandomStringLower(4)).ToLowerInvariant();

    private bool validateEditControls(string tplId, bool checkFile)
    {
        if (string.IsNullOrEmpty(tplId)) {
            MessageDlg.Error("Template ID cannot be blank!");
            return false;
        }

        if (checkFile) {
            if (string.IsNullOrEmpty(_importFilename)) {
                MessageDlg.Error("No report template file specified!\nPlease import a valid report template file.");
                return false;
            }

            var fi = new FileInfo(_importFilename);

            if (!fi.Exists) {
                MessageDlg.Error("Report template file does not exist!\nPlease import a valid report template file.");
                return false;
            }

            if (fi.Length <= 0) {
                MessageDlg.Error("Report template file is empty (0 bytes)!");
                return false;
            }
        }

        return true;
    }

    private AppReportTemplateSlice getSelectedReportSlice() => gridView.SelectedRowsCount == 1 ? gridView.GetFocusedRow() as AppReportTemplateSlice : null;

    private void populateEditControls() => btnSaveNew.Enabled = _currentSlice != null;

    private void btnContentExport_Click(object sender, EventArgs e)
    {
        if (_currentSlice != null) {
            saveFileDialog.FileName = generateFilenameFromReportCode(_currentSlice.ReportCode);
            if (saveFileDialog.ShowDialog(this) == DialogResult.OK) {
                WaitFormControl.WaitOperation(
                    this,
                    () =>
                    {
                        var content =
                            AppReportTemplatesRepository.GetAppTemplateReport(_currentSlice.ReportCode);
                        File.WriteAllBytes(saveFileDialog.FileName, content);
                    },
                    "Saving Report Template to disk...");
            }
        }
    }

    private void gridView_FocusedRowChanged(object sender, FocusedRowChangedEventArgs e)
    {
        _currentSlice = getSelectedReportSlice();
        populateEditControls();
    }

    private void btnSaveNew_Click(object sender, EventArgs e)
    {
        openFileDialog.FileName = null;
        if (openFileDialog.ShowDialog(this) == DialogResult.OK) {
            _importFilename = openFileDialog.FileName;
            var reportCode = _currentSlice.ReportCode;

            if (!validateEditControls(reportCode, true)) return;

            var content = File.ReadAllBytes(_importFilename);
            var templateId = generateUniqueTemplateId(reportCode);

            WaitFormControl.WaitOperation(
                this,
                () => AppReportTemplatesRepository.CreateNewAppReportTemplate(reportCode, templateId, content),
                "Saving...");
        }

        populateGridControl();
    }

    private void btnReplaceOld_Click(object sender, EventArgs e)
    {
        openFileDialog.FileName = null;
        if (openFileDialog.ShowDialog(this) == DialogResult.OK) {
            _importFilename = openFileDialog.FileName;
            if (!validateEditControls(_currentSlice.ReportCode, true)) return;

            var content = File.ReadAllBytes(_importFilename);
            WaitFormControl.WaitOperation(
                this,
                () => AppReportTemplatesRepository.UpdateAppTemplateReport(_currentSlice.ReportCode, content),
                "Saving...");
        }

        populateGridControl();
    }

    private void AppReportTemplatesEditorDialog_KeyUp(object sender, KeyEventArgs e)
    {
        switch (e.KeyCode) {
            case Keys.F6:
                btnContentExport_Click(null, null);
                break;
            case Keys.F7:
                btnReplaceOld_Click(null, null);
                break;
            case Keys.F8:
                btnSaveNew_Click(null, null);
                break;
        }
    }

    /*
    private void load(string code, string fname)
    {
        var fbytes = File.ReadAllBytes(fname);
        var tid = generateUniqueTemplateId(code);
        AppReportTemplatesRepository.CreateNewAppReportTemplate(code, tid, fbytes);
    }

    private void simpleButton1_Click(object sender, EventArgs e)
    {
        load(AppReportTemplateCodes.FinanceIncomeOutstandingA4, "Chevron_IncomeOutstanding_A4_Landscape.mrt");
        load(AppReportTemplateCodes.FinanceIncomeOutstandingRadioA4, "Chevron_IncomeOutstanding_RAD_A4_Landscape.mrt");
        load(AppReportTemplateCodes.FinanceOutstandingDuesA4, "Chevron_OutstandingDuesCollection_A4.mrt");
        load(AppReportTemplateCodes.FinanceReceivablesSummaryA4, "Chevron_ReceivablesSummary_A4.mrt");
        load(AppReportTemplateCodes.FinanceRefundDiscountA4, "Chevron_RefundDiscountList_A4.mrt");
    }
    */
}