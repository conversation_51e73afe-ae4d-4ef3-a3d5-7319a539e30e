﻿namespace LabMaestro.Controls.Win
{
    partial class LaunchPadForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            DevExpress.XtraEditors.TileItemElement tileItemElement1 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement2 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement3 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement4 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement5 = new DevExpress.XtraEditors.TileItemElement();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(LaunchPadForm));
            DevExpress.XtraEditors.TileItemElement tileItemElement6 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement7 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement8 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement9 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement10 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement11 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement12 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement13 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement14 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement15 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement16 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement17 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement18 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement19 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement20 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement21 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement22 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement23 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement24 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement25 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement26 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement27 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement28 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement29 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement30 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement31 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement32 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement33 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement34 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement35 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement36 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement37 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement38 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement39 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement40 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement41 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement42 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement43 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement44 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement45 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement46 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement47 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement48 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement49 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement50 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement51 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement52 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement53 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement54 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement55 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement56 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement57 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement58 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement59 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement60 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement61 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement62 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement63 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement64 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement65 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement66 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement67 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement68 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement69 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement70 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement71 = new DevExpress.XtraEditors.TileItemElement();
            this.tileControl = new DevExpress.XtraEditors.TileControl();
            this.grpOrderEntry = new DevExpress.XtraEditors.TileGroup();
            this.tiNewLabOrder = new DevExpress.XtraEditors.TileItem();
            this.tiProcessReceivables = new DevExpress.XtraEditors.TileItem();
            this.tiSearchInvoices = new DevExpress.XtraEditors.TileItem();
            this.tiNewExternalOrder = new DevExpress.XtraEditors.TileItem();
            this.tiCustomers = new DevExpress.XtraEditors.TileItem();
            this.grpWorkflow = new DevExpress.XtraEditors.TileGroup();
            this.tiResultEntry = new DevExpress.XtraEditors.TileItem();
            this.tiResultsVerification = new DevExpress.XtraEditors.TileItem();
            this.tiReportFinalization = new DevExpress.XtraEditors.TileItem();
            this.tiReportCollation = new DevExpress.XtraEditors.TileItem();
            this.tiReportDispatch = new DevExpress.XtraEditors.TileItem();
            this.tiFacesheet = new DevExpress.XtraEditors.TileItem();
            this.tiThermalLabelPrint = new DevExpress.XtraEditors.TileItem();
            this.tiBulkDispatch = new DevExpress.XtraEditors.TileItem();
            this.grpUserShift = new DevExpress.XtraEditors.TileGroup();
            this.tiShiftOpen = new DevExpress.XtraEditors.TileItem();
            this.tiShiftClose = new DevExpress.XtraEditors.TileItem();
            this.tiUserShiftHistory = new DevExpress.XtraEditors.TileItem();
            this.tiUserShiftReports = new DevExpress.XtraEditors.TileItem();
            this.tiStaffShiftHistory = new DevExpress.XtraEditors.TileItem();
            this.grpBackOffice = new DevExpress.XtraEditors.TileGroup();
            this.tiBOShiftReporting = new DevExpress.XtraEditors.TileItem();
            this.tiBOUserSummaries = new DevExpress.XtraEditors.TileItem();
            this.tiBODailyReceivables = new DevExpress.XtraEditors.TileItem();
            this.tiBOReceivablesSummary = new DevExpress.XtraEditors.TileItem();
            this.tiBOOutstandingCollection = new DevExpress.XtraEditors.TileItem();
            this.tiBOIncomeStatement = new DevExpress.XtraEditors.TileItem();
            this.tiBODailyDuesCollection = new DevExpress.XtraEditors.TileItem();
            this.tiBOFinancialAudit = new DevExpress.XtraEditors.TileItem();
            this.tiDuesRegister = new DevExpress.XtraEditors.TileItem();
            this.tiBOCancellationReport = new DevExpress.XtraEditors.TileItem();
            this.tiCollatorManifest = new DevExpress.XtraEditors.TileItem();
            this.tiBOOutstandingStatement = new DevExpress.XtraEditors.TileItem();
            this.tiStaffPerformanceReport = new DevExpress.XtraEditors.TileItem();
            this.tiBOReferrerBizContrib = new DevExpress.XtraEditors.TileItem();
            this.tiTestUtilizationReport = new DevExpress.XtraEditors.TileItem();
            this.tiMarketingReports = new DevExpress.XtraEditors.TileItem();
            this.tiBIUtilizationReport = new DevExpress.XtraEditors.TileItem();
            this.tiPhleboUtilization = new DevExpress.XtraEditors.TileItem();
            this.tiBORefEligibilitySum = new DevExpress.XtraEditors.TileItem();
            this.tiBODiscountOverageReport = new DevExpress.XtraEditors.TileItem();
            this.tiBOLabwiseDailyIncomeReport = new DevExpress.XtraEditors.TileItem();
            this.grpCatalog = new DevExpress.XtraEditors.TileGroup();
            this.tiCatReferrerAdd = new DevExpress.XtraEditors.TileItem();
            this.tiCatReferrerBrowse = new DevExpress.XtraEditors.TileItem();
            this.tiAdminBackend = new DevExpress.XtraEditors.TileItem();
            this.tiMQHeartbeat = new DevExpress.XtraEditors.TileItem();
            this.tiTestNew = new DevExpress.XtraEditors.TileItem();
            this.tiTestEdit = new DevExpress.XtraEditors.TileItem();
            this.tiWorkstationRegistry = new DevExpress.XtraEditors.TileItem();
            this.grpProfile = new DevExpress.XtraEditors.TileGroup();
            this.tiProfilePasswordChange = new DevExpress.XtraEditors.TileItem();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.lblVersion = new DevExpress.XtraEditors.LabelControl();
            this.lblGreeting = new DevExpress.XtraEditors.LabelControl();
            this.lblIpAddress = new DevExpress.XtraEditors.LabelControl();
            this.lblLoginTime = new DevExpress.XtraEditors.LabelControl();
            this.lblStaffname = new DevExpress.XtraEditors.LabelControl();
            this.lblCopyright = new DevExpress.XtraEditors.LabelControl();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.tileItem2 = new DevExpress.XtraEditors.TileItem();
            this.autoUpdater = new wyDay.Controls.AutomaticUpdater();
            this.btnCheckUpdate = new DevExpress.XtraEditors.SimpleButton();
            this.btnMinimize = new DevExpress.XtraEditors.SimpleButton();
            this.btnLogOut = new DevExpress.XtraEditors.SimpleButton();
            this.pictureBox4 = new System.Windows.Forms.PictureBox();
            this.pictureBox3 = new System.Windows.Forms.PictureBox();
            this.pictureBox2 = new System.Windows.Forms.PictureBox();
            this.pictureBox1 = new System.Windows.Forms.PictureBox();
            this.tiBOReferrerAdd = new DevExpress.XtraEditors.TileItem();
            this.tileItem1 = new DevExpress.XtraEditors.TileItem();
            this.tileItem4 = new DevExpress.XtraEditors.TileItem();
            this.tileItem5 = new DevExpress.XtraEditors.TileItem();
            this.tileItem6 = new DevExpress.XtraEditors.TileItem();
            this.tileItem7 = new DevExpress.XtraEditors.TileItem();
            ((System.ComponentModel.ISupportInitialize)(this.autoUpdater)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).BeginInit();
            this.SuspendLayout();
            // 
            // tileControl
            // 
            this.tileControl.AllowDrag = false;
            this.tileControl.AppearanceGroupText.Font = new System.Drawing.Font("Segoe UI Semibold", 15.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileControl.AppearanceGroupText.Options.UseFont = true;
            this.tileControl.AppearanceText.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileControl.AppearanceText.Options.UseFont = true;
            this.tileControl.Cursor = System.Windows.Forms.Cursors.Default;
            this.tileControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tileControl.Groups.Add(this.grpOrderEntry);
            this.tileControl.Groups.Add(this.grpWorkflow);
            this.tileControl.Groups.Add(this.grpUserShift);
            this.tileControl.Groups.Add(this.grpBackOffice);
            this.tileControl.Groups.Add(this.grpCatalog);
            this.tileControl.Groups.Add(this.grpProfile);
            this.tileControl.IndentBetweenGroups = 36;
            this.tileControl.IndentBetweenItems = 12;
            this.tileControl.ItemTextShowMode = DevExpress.XtraEditors.TileItemContentShowMode.Always;
            this.tileControl.Location = new System.Drawing.Point(0, 0);
            this.tileControl.LookAndFeel.SkinName = "Darkroom";
            this.tileControl.LookAndFeel.UseDefaultLookAndFeel = false;
            this.tileControl.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.tileControl.MaxId = 56;
            this.tileControl.Name = "tileControl";
            this.tileControl.Padding = new System.Windows.Forms.Padding(21, 24, 21, 24);
            this.tileControl.RowCount = 3;
            this.tileControl.ShowGroupText = true;
            this.tileControl.Size = new System.Drawing.Size(934, 668);
            this.tileControl.TabIndex = 0;
            // 
            // grpOrderEntry
            // 
            this.grpOrderEntry.Items.Add(this.tiNewLabOrder);
            this.grpOrderEntry.Items.Add(this.tiProcessReceivables);
            this.grpOrderEntry.Items.Add(this.tiSearchInvoices);
            this.grpOrderEntry.Items.Add(this.tiNewExternalOrder);
            this.grpOrderEntry.Items.Add(this.tiCustomers);
            this.grpOrderEntry.Name = "grpOrderEntry";
            this.grpOrderEntry.Text = "PR/OE";
            // 
            // tiNewLabOrder
            // 
            this.tiNewLabOrder.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tiNewLabOrder.AppearanceItem.Normal.Options.UseFont = true;
            tileItemElement1.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.lp_order_new_48;
            tileItemElement1.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement1.Text = "New Lab Order";
            tileItemElement1.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter;
            this.tiNewLabOrder.Elements.Add(tileItemElement1);
            this.tiNewLabOrder.Id = 101;
            this.tiNewLabOrder.Name = "tiNewLabOrder";
            this.tiNewLabOrder.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiNewLabOrder_ItemClick);
            // 
            // tiProcessReceivables
            // 
            this.tiProcessReceivables.AppearanceItem.Hovered.BorderColor = System.Drawing.Color.Red;
            this.tiProcessReceivables.AppearanceItem.Hovered.Options.UseBorderColor = true;
            this.tiProcessReceivables.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tiProcessReceivables.AppearanceItem.Normal.Options.UseFont = true;
            tileItemElement2.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.lp_cashier_coins_48;
            tileItemElement2.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement2.Text = "Process Receivables";
            tileItemElement2.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter;
            this.tiProcessReceivables.Elements.Add(tileItemElement2);
            this.tiProcessReceivables.Id = 102;
            this.tiProcessReceivables.Name = "tiProcessReceivables";
            this.tiProcessReceivables.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiProcessReceivables_ItemClick);
            // 
            // tiSearchInvoices
            // 
            this.tiSearchInvoices.AppearanceItem.Hovered.BorderColor = System.Drawing.Color.Red;
            this.tiSearchInvoices.AppearanceItem.Hovered.Options.UseBorderColor = true;
            this.tiSearchInvoices.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tiSearchInvoices.AppearanceItem.Normal.Options.UseFont = true;
            tileItemElement3.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.lp_order_search_48;
            tileItemElement3.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement3.Text = "Search Invoices";
            tileItemElement3.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter;
            this.tiSearchInvoices.Elements.Add(tileItemElement3);
            this.tiSearchInvoices.Id = 103;
            this.tiSearchInvoices.ItemSize = DevExpress.XtraEditors.TileItemSize.Wide;
            this.tiSearchInvoices.Name = "tiSearchInvoices";
            this.tiSearchInvoices.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiSearchInvoices_ItemClick);
            // 
            // tiNewExternalOrder
            // 
            tileItemElement4.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.lp_order_new_48;
            tileItemElement4.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement4.Text = "New External Lab Order";
            tileItemElement4.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter;
            this.tiNewExternalOrder.Elements.Add(tileItemElement4);
            this.tiNewExternalOrder.Id = 50;
            this.tiNewExternalOrder.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium;
            this.tiNewExternalOrder.Name = "tiNewExternalOrder";
            this.tiNewExternalOrder.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiNewExternalOrder_ItemClick);
            // 
            // tiCustomers
            // 
            tileItemElement5.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image")));
            tileItemElement5.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement5.Text = "Registered Patients";
            this.tiCustomers.Elements.Add(tileItemElement5);
            this.tiCustomers.Id = 54;
            this.tiCustomers.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium;
            this.tiCustomers.Name = "tiCustomers";
            this.tiCustomers.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiCustomers_ItemClick);
            // 
            // grpWorkflow
            // 
            this.grpWorkflow.Items.Add(this.tiResultEntry);
            this.grpWorkflow.Items.Add(this.tiResultsVerification);
            this.grpWorkflow.Items.Add(this.tiReportFinalization);
            this.grpWorkflow.Items.Add(this.tiReportCollation);
            this.grpWorkflow.Items.Add(this.tiReportDispatch);
            this.grpWorkflow.Items.Add(this.tiFacesheet);
            this.grpWorkflow.Items.Add(this.tiThermalLabelPrint);
            this.grpWorkflow.Items.Add(this.tiBulkDispatch);
            this.grpWorkflow.Name = "grpWorkflow";
            this.grpWorkflow.Text = "Lab Analytical Workflow";
            // 
            // tiResultEntry
            // 
            this.tiResultEntry.AppearanceItem.Normal.BackColor = System.Drawing.Color.Indigo;
            this.tiResultEntry.AppearanceItem.Normal.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(0)))), ((int)(((byte)(64)))));
            this.tiResultEntry.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tiResultEntry.AppearanceItem.Normal.GradientMode = System.Drawing.Drawing2D.LinearGradientMode.ForwardDiagonal;
            this.tiResultEntry.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tiResultEntry.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tiResultEntry.AppearanceItem.Normal.Options.UseFont = true;
            tileItemElement6.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.lp_report_design_48;
            tileItemElement6.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement6.Text = "Results Entry";
            tileItemElement6.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter;
            this.tiResultEntry.Elements.Add(tileItemElement6);
            this.tiResultEntry.Id = 201;
            this.tiResultEntry.Name = "tiResultEntry";
            this.tiResultEntry.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiResultEntry_ItemClick);
            // 
            // tiResultsVerification
            // 
            this.tiResultsVerification.AppearanceItem.Normal.BackColor = System.Drawing.Color.Indigo;
            this.tiResultsVerification.AppearanceItem.Normal.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(0)))), ((int)(((byte)(64)))));
            this.tiResultsVerification.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tiResultsVerification.AppearanceItem.Normal.GradientMode = System.Drawing.Drawing2D.LinearGradientMode.ForwardDiagonal;
            this.tiResultsVerification.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tiResultsVerification.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tiResultsVerification.AppearanceItem.Normal.Options.UseFont = true;
            tileItemElement7.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.lp_report_ok_48;
            tileItemElement7.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement7.Text = "Results Verification";
            tileItemElement7.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter;
            this.tiResultsVerification.Elements.Add(tileItemElement7);
            this.tiResultsVerification.Id = 202;
            this.tiResultsVerification.Name = "tiResultsVerification";
            this.tiResultsVerification.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiResultsVerification_ItemClick);
            // 
            // tiReportFinalization
            // 
            this.tiReportFinalization.AppearanceItem.Normal.BackColor = System.Drawing.Color.Indigo;
            this.tiReportFinalization.AppearanceItem.Normal.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(0)))), ((int)(((byte)(64)))));
            this.tiReportFinalization.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tiReportFinalization.AppearanceItem.Normal.GradientMode = System.Drawing.Drawing2D.LinearGradientMode.ForwardDiagonal;
            this.tiReportFinalization.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tiReportFinalization.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tiReportFinalization.AppearanceItem.Normal.Options.UseFont = true;
            tileItemElement8.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.lp_report_lock_48;
            tileItemElement8.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement8.Text = "Report Finalization";
            tileItemElement8.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter;
            this.tiReportFinalization.Elements.Add(tileItemElement8);
            this.tiReportFinalization.Id = 203;
            this.tiReportFinalization.Name = "tiReportFinalization";
            this.tiReportFinalization.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiReportFinalization_ItemClick);
            // 
            // tiReportCollation
            // 
            this.tiReportCollation.AppearanceItem.Normal.BackColor = System.Drawing.Color.Indigo;
            this.tiReportCollation.AppearanceItem.Normal.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(0)))), ((int)(((byte)(64)))));
            this.tiReportCollation.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tiReportCollation.AppearanceItem.Normal.GradientMode = System.Drawing.Drawing2D.LinearGradientMode.ForwardDiagonal;
            this.tiReportCollation.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tiReportCollation.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tiReportCollation.AppearanceItem.Normal.Options.UseFont = true;
            tileItemElement9.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.lp_report_print_48;
            tileItemElement9.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement9.Text = "Report Collation";
            tileItemElement9.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter;
            this.tiReportCollation.Elements.Add(tileItemElement9);
            this.tiReportCollation.Id = 204;
            this.tiReportCollation.Name = "tiReportCollation";
            this.tiReportCollation.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiReportCollation_ItemClick);
            // 
            // tiReportDispatch
            // 
            this.tiReportDispatch.AppearanceItem.Normal.BackColor = System.Drawing.Color.Indigo;
            this.tiReportDispatch.AppearanceItem.Normal.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(0)))), ((int)(((byte)(64)))));
            this.tiReportDispatch.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tiReportDispatch.AppearanceItem.Normal.GradientMode = System.Drawing.Drawing2D.LinearGradientMode.ForwardDiagonal;
            this.tiReportDispatch.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tiReportDispatch.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tiReportDispatch.AppearanceItem.Normal.Options.UseFont = true;
            tileItemElement10.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.lp_report_mail_48;
            tileItemElement10.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement10.Text = "Report Dispatch";
            tileItemElement10.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter;
            this.tiReportDispatch.Elements.Add(tileItemElement10);
            this.tiReportDispatch.Id = 205;
            this.tiReportDispatch.Name = "tiReportDispatch";
            this.tiReportDispatch.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiReportDispatch_ItemClick);
            // 
            // tiFacesheet
            // 
            this.tiFacesheet.AppearanceItem.Normal.BackColor = System.Drawing.Color.Indigo;
            this.tiFacesheet.AppearanceItem.Normal.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(0)))), ((int)(((byte)(64)))));
            this.tiFacesheet.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tiFacesheet.AppearanceItem.Normal.GradientMode = System.Drawing.Drawing2D.LinearGradientMode.ForwardDiagonal;
            this.tiFacesheet.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tiFacesheet.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tiFacesheet.AppearanceItem.Normal.Options.UseFont = true;
            tileItemElement11.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.lp_business_report_print_48;
            tileItemElement11.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement11.Text = "Generate Facesheets";
            tileItemElement11.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter;
            this.tiFacesheet.Elements.Add(tileItemElement11);
            this.tiFacesheet.Id = 206;
            this.tiFacesheet.Name = "tiFacesheet";
            this.tiFacesheet.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiFacesheet_ItemClick);
            // 
            // tiThermalLabelPrint
            // 
            this.tiThermalLabelPrint.AppearanceItem.Normal.BackColor = System.Drawing.Color.Indigo;
            this.tiThermalLabelPrint.AppearanceItem.Normal.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(0)))), ((int)(((byte)(64)))));
            this.tiThermalLabelPrint.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tiThermalLabelPrint.AppearanceItem.Normal.GradientMode = System.Drawing.Drawing2D.LinearGradientMode.ForwardDiagonal;
            this.tiThermalLabelPrint.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tiThermalLabelPrint.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tiThermalLabelPrint.AppearanceItem.Normal.Options.UseFont = true;
            tileItemElement12.ImageOptions.Image = global::LabMaestro.Controls.Win.ResourceIcons.laboratory_48;
            tileItemElement12.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement12.Text = "Print Themal Labels";
            tileItemElement12.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter;
            this.tiThermalLabelPrint.Elements.Add(tileItemElement12);
            this.tiThermalLabelPrint.Id = 30;
            this.tiThermalLabelPrint.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium;
            this.tiThermalLabelPrint.Name = "tiThermalLabelPrint";
            this.tiThermalLabelPrint.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiThermalLabelPrint_ItemClick);
            // 
            // tiBulkDispatch
            // 
            this.tiBulkDispatch.AppearanceItem.Normal.BackColor = System.Drawing.Color.Indigo;
            this.tiBulkDispatch.AppearanceItem.Normal.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(0)))), ((int)(((byte)(64)))));
            this.tiBulkDispatch.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tiBulkDispatch.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tiBulkDispatch.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tiBulkDispatch.AppearanceItem.Normal.Options.UseFont = true;
            tileItemElement13.ImageOptions.Image = global::LabMaestro.Controls.Win.ResourceIcons.run_application_48;
            tileItemElement13.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement13.Text = "Bulk Disptach";
            tileItemElement13.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter;
            this.tiBulkDispatch.Elements.Add(tileItemElement13);
            this.tiBulkDispatch.Id = 45;
            this.tiBulkDispatch.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium;
            this.tiBulkDispatch.Name = "tiBulkDispatch";
            this.tiBulkDispatch.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiBulkDispatch_ItemClick);
            // 
            // grpUserShift
            // 
            this.grpUserShift.Items.Add(this.tiShiftOpen);
            this.grpUserShift.Items.Add(this.tiShiftClose);
            this.grpUserShift.Items.Add(this.tiUserShiftHistory);
            this.grpUserShift.Items.Add(this.tiUserShiftReports);
            this.grpUserShift.Items.Add(this.tiStaffShiftHistory);
            this.grpUserShift.Name = "grpUserShift";
            this.grpUserShift.Text = "User Shift Management";
            // 
            // tiShiftOpen
            // 
            this.tiShiftOpen.AppearanceItem.Normal.BackColor = System.Drawing.Color.SteelBlue;
            this.tiShiftOpen.AppearanceItem.Normal.BorderColor = System.Drawing.Color.RoyalBlue;
            this.tiShiftOpen.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tiShiftOpen.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tiShiftOpen.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tiShiftOpen.AppearanceItem.Normal.Options.UseFont = true;
            tileItemElement14.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.lp_clock_add_48;
            tileItemElement14.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement14.Text = "Open New Shift";
            tileItemElement14.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter;
            this.tiShiftOpen.Elements.Add(tileItemElement14);
            this.tiShiftOpen.Id = 301;
            this.tiShiftOpen.Name = "tiShiftOpen";
            this.tiShiftOpen.Tag = 301;
            this.tiShiftOpen.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiShiftOpen_ItemClick);
            // 
            // tiShiftClose
            // 
            this.tiShiftClose.AppearanceItem.Normal.BackColor = System.Drawing.Color.SteelBlue;
            this.tiShiftClose.AppearanceItem.Normal.BorderColor = System.Drawing.Color.RoyalBlue;
            this.tiShiftClose.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tiShiftClose.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tiShiftClose.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tiShiftClose.AppearanceItem.Normal.Options.UseFont = true;
            tileItemElement15.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.lp_clock_delete_48;
            tileItemElement15.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement15.Text = "Close Current Shift";
            tileItemElement15.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter;
            this.tiShiftClose.Elements.Add(tileItemElement15);
            this.tiShiftClose.Id = 302;
            this.tiShiftClose.Name = "tiShiftClose";
            this.tiShiftClose.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiShiftClose_ItemClick);
            // 
            // tiUserShiftHistory
            // 
            this.tiUserShiftHistory.AppearanceItem.Normal.BackColor = System.Drawing.Color.SteelBlue;
            this.tiUserShiftHistory.AppearanceItem.Normal.BorderColor = System.Drawing.Color.RoyalBlue;
            this.tiUserShiftHistory.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tiUserShiftHistory.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tiUserShiftHistory.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tiUserShiftHistory.AppearanceItem.Normal.Options.UseFont = true;
            tileItemElement16.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.lp_table_customer_delay_48;
            tileItemElement16.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement16.Text = "View Shift History";
            tileItemElement16.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter;
            this.tiUserShiftHistory.Elements.Add(tileItemElement16);
            this.tiUserShiftHistory.Id = 303;
            this.tiUserShiftHistory.Name = "tiUserShiftHistory";
            this.tiUserShiftHistory.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiUserShiftHistory_ItemClick);
            // 
            // tiUserShiftReports
            // 
            this.tiUserShiftReports.AppearanceItem.Normal.BackColor = System.Drawing.Color.SteelBlue;
            this.tiUserShiftReports.AppearanceItem.Normal.BorderColor = System.Drawing.Color.SteelBlue;
            this.tiUserShiftReports.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tiUserShiftReports.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tiUserShiftReports.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tiUserShiftReports.AppearanceItem.Normal.Options.UseFont = true;
            tileItemElement17.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.lp_clock_user_48;
            tileItemElement17.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement17.Text = "Current Shift Details";
            tileItemElement17.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter;
            this.tiUserShiftReports.Elements.Add(tileItemElement17);
            this.tiUserShiftReports.Id = 304;
            this.tiUserShiftReports.Name = "tiUserShiftReports";
            this.tiUserShiftReports.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiUserShiftReports_ItemClick);
            // 
            // tiStaffShiftHistory
            // 
            this.tiStaffShiftHistory.AppearanceItem.Normal.BackColor = System.Drawing.Color.SteelBlue;
            this.tiStaffShiftHistory.AppearanceItem.Normal.BorderColor = System.Drawing.Color.SteelBlue;
            this.tiStaffShiftHistory.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tiStaffShiftHistory.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tiStaffShiftHistory.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tiStaffShiftHistory.AppearanceItem.Normal.Options.UseFont = true;
            this.tiStaffShiftHistory.AppearanceItem.Normal.Options.UseTextOptions = true;
            this.tiStaffShiftHistory.AppearanceItem.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.tiStaffShiftHistory.AppearanceItem.Normal.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Bottom;
            tileItemElement18.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.lp_employees_delay_48;
            tileItemElement18.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement18.Text = "Staff Shift History";
            tileItemElement18.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter;
            this.tiStaffShiftHistory.Elements.Add(tileItemElement18);
            this.tiStaffShiftHistory.Id = 49;
            this.tiStaffShiftHistory.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium;
            this.tiStaffShiftHistory.Name = "tiStaffShiftHistory";
            this.tiStaffShiftHistory.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiStaffShiftHistory_ItemClick);
            // 
            // grpBackOffice
            // 
            this.grpBackOffice.Items.Add(this.tiBOShiftReporting);
            this.grpBackOffice.Items.Add(this.tiBOUserSummaries);
            this.grpBackOffice.Items.Add(this.tiBODailyReceivables);
            this.grpBackOffice.Items.Add(this.tiBOReceivablesSummary);
            this.grpBackOffice.Items.Add(this.tiBOOutstandingCollection);
            this.grpBackOffice.Items.Add(this.tiBOIncomeStatement);
            this.grpBackOffice.Items.Add(this.tiBODailyDuesCollection);
            this.grpBackOffice.Items.Add(this.tiBOFinancialAudit);
            this.grpBackOffice.Items.Add(this.tiDuesRegister);
            this.grpBackOffice.Items.Add(this.tiBOCancellationReport);
            this.grpBackOffice.Items.Add(this.tiCollatorManifest);
            this.grpBackOffice.Items.Add(this.tiBOOutstandingStatement);
            this.grpBackOffice.Items.Add(this.tiStaffPerformanceReport);
            this.grpBackOffice.Items.Add(this.tiBOReferrerBizContrib);
            this.grpBackOffice.Items.Add(this.tiTestUtilizationReport);
            this.grpBackOffice.Items.Add(this.tiMarketingReports);
            this.grpBackOffice.Items.Add(this.tiBIUtilizationReport);
            this.grpBackOffice.Items.Add(this.tiPhleboUtilization);
            this.grpBackOffice.Items.Add(this.tiBORefEligibilitySum);
            this.grpBackOffice.Items.Add(this.tiBODiscountOverageReport);
            this.grpBackOffice.Items.Add(this.tiBOLabwiseDailyIncomeReport);
            this.grpBackOffice.Name = "grpBackOffice";
            this.grpBackOffice.Text = "Back-office Reporting";
            // 
            // tiBOShiftReporting
            // 
            this.tiBOShiftReporting.AppearanceItem.Normal.BackColor = System.Drawing.Color.Maroon;
            this.tiBOShiftReporting.AppearanceItem.Normal.BorderColor = System.Drawing.Color.SaddleBrown;
            this.tiBOShiftReporting.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tiBOShiftReporting.AppearanceItem.Normal.ForeColor = System.Drawing.Color.White;
            this.tiBOShiftReporting.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tiBOShiftReporting.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tiBOShiftReporting.AppearanceItem.Normal.Options.UseFont = true;
            this.tiBOShiftReporting.AppearanceItem.Normal.Options.UseForeColor = true;
            tileItemElement19.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.lp_employees_delay_48;
            tileItemElement19.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement19.Text = "Group Shift Reporting";
            tileItemElement19.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter;
            this.tiBOShiftReporting.Elements.Add(tileItemElement19);
            this.tiBOShiftReporting.Enabled = false;
            this.tiBOShiftReporting.Id = 13;
            this.tiBOShiftReporting.Name = "tiBOShiftReporting";
            // 
            // tiBOUserSummaries
            // 
            this.tiBOUserSummaries.AppearanceItem.Normal.BackColor = System.Drawing.Color.Maroon;
            this.tiBOUserSummaries.AppearanceItem.Normal.BorderColor = System.Drawing.Color.SaddleBrown;
            this.tiBOUserSummaries.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tiBOUserSummaries.AppearanceItem.Normal.ForeColor = System.Drawing.Color.White;
            this.tiBOUserSummaries.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tiBOUserSummaries.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tiBOUserSummaries.AppearanceItem.Normal.Options.UseFont = true;
            this.tiBOUserSummaries.AppearanceItem.Normal.Options.UseForeColor = true;
            tileItemElement20.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.lp_factory_coins_48;
            tileItemElement20.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement20.Text = "Receptionist Income";
            tileItemElement20.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter;
            tileItemElement21.Appearance.Normal.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            tileItemElement21.Appearance.Normal.Options.UseFont = true;
            tileItemElement21.Text = "RIS";
            tileItemElement21.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.TopRight;
            this.tiBOUserSummaries.Elements.Add(tileItemElement20);
            this.tiBOUserSummaries.Elements.Add(tileItemElement21);
            this.tiBOUserSummaries.Enabled = false;
            this.tiBOUserSummaries.Id = 14;
            this.tiBOUserSummaries.Name = "tiBOUserSummaries";
            this.tiBOUserSummaries.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiBOUserSummaries_ItemClick);
            // 
            // tiBODailyReceivables
            // 
            this.tiBODailyReceivables.AppearanceItem.Normal.BackColor = System.Drawing.Color.Maroon;
            this.tiBODailyReceivables.AppearanceItem.Normal.BorderColor = System.Drawing.Color.SaddleBrown;
            this.tiBODailyReceivables.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tiBODailyReceivables.AppearanceItem.Normal.ForeColor = System.Drawing.Color.White;
            this.tiBODailyReceivables.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tiBODailyReceivables.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tiBODailyReceivables.AppearanceItem.Normal.Options.UseFont = true;
            this.tiBODailyReceivables.AppearanceItem.Normal.Options.UseForeColor = true;
            tileItemElement22.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.lp_employees_coins_48;
            tileItemElement22.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement22.Text = "Daily Receivables";
            tileItemElement22.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter;
            tileItemElement23.Text = "BRS";
            tileItemElement23.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.TopRight;
            this.tiBODailyReceivables.Elements.Add(tileItemElement22);
            this.tiBODailyReceivables.Elements.Add(tileItemElement23);
            this.tiBODailyReceivables.Enabled = false;
            this.tiBODailyReceivables.Id = 16;
            this.tiBODailyReceivables.Name = "tiBODailyReceivables";
            this.tiBODailyReceivables.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiBODailyReceivables_ItemClick);
            // 
            // tiBOReceivablesSummary
            // 
            this.tiBOReceivablesSummary.AppearanceItem.Normal.BackColor = System.Drawing.Color.Maroon;
            this.tiBOReceivablesSummary.AppearanceItem.Normal.BorderColor = System.Drawing.Color.SaddleBrown;
            this.tiBOReceivablesSummary.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tiBOReceivablesSummary.AppearanceItem.Normal.ForeColor = System.Drawing.Color.White;
            this.tiBOReceivablesSummary.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tiBOReceivablesSummary.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tiBOReceivablesSummary.AppearanceItem.Normal.Options.UseFont = true;
            this.tiBOReceivablesSummary.AppearanceItem.Normal.Options.UseForeColor = true;
            tileItemElement24.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.lp_employees_banknote_48;
            tileItemElement24.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement24.Text = "Receivables Summary";
            tileItemElement24.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter;
            tileItemElement25.Text = "URS";
            tileItemElement25.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.TopRight;
            this.tiBOReceivablesSummary.Elements.Add(tileItemElement24);
            this.tiBOReceivablesSummary.Elements.Add(tileItemElement25);
            this.tiBOReceivablesSummary.Enabled = false;
            this.tiBOReceivablesSummary.Id = 17;
            this.tiBOReceivablesSummary.Name = "tiBOReceivablesSummary";
            this.tiBOReceivablesSummary.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiBOReceivablesSummary_ItemClick);
            // 
            // tiBOOutstandingCollection
            // 
            this.tiBOOutstandingCollection.AppearanceItem.Normal.BackColor = System.Drawing.Color.Maroon;
            this.tiBOOutstandingCollection.AppearanceItem.Normal.BorderColor = System.Drawing.Color.SaddleBrown;
            this.tiBOOutstandingCollection.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tiBOOutstandingCollection.AppearanceItem.Normal.ForeColor = System.Drawing.Color.White;
            this.tiBOOutstandingCollection.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tiBOOutstandingCollection.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tiBOOutstandingCollection.AppearanceItem.Normal.Options.UseFont = true;
            this.tiBOOutstandingCollection.AppearanceItem.Normal.Options.UseForeColor = true;
            tileItemElement26.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.lp_address_book_coins_48;
            tileItemElement26.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement26.Text = "ASO Collection";
            tileItemElement26.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter;
            tileItemElement27.Text = "ASO";
            tileItemElement27.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.TopRight;
            this.tiBOOutstandingCollection.Elements.Add(tileItemElement26);
            this.tiBOOutstandingCollection.Elements.Add(tileItemElement27);
            this.tiBOOutstandingCollection.Enabled = false;
            this.tiBOOutstandingCollection.Id = 18;
            this.tiBOOutstandingCollection.Name = "tiBOOutstandingCollection";
            this.tiBOOutstandingCollection.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiBOOutstandingCollection_ItemClick);
            // 
            // tiBOIncomeStatement
            // 
            this.tiBOIncomeStatement.AppearanceItem.Normal.BackColor = System.Drawing.Color.Maroon;
            this.tiBOIncomeStatement.AppearanceItem.Normal.BorderColor = System.Drawing.Color.SaddleBrown;
            this.tiBOIncomeStatement.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tiBOIncomeStatement.AppearanceItem.Normal.ForeColor = System.Drawing.Color.White;
            this.tiBOIncomeStatement.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tiBOIncomeStatement.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tiBOIncomeStatement.AppearanceItem.Normal.Options.UseFont = true;
            this.tiBOIncomeStatement.AppearanceItem.Normal.Options.UseForeColor = true;
            tileItemElement28.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.lp_money_query_48;
            tileItemElement28.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement28.Text = "Income Statement";
            tileItemElement28.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter;
            tileItemElement29.Text = "IIS";
            tileItemElement29.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.TopRight;
            this.tiBOIncomeStatement.Elements.Add(tileItemElement28);
            this.tiBOIncomeStatement.Elements.Add(tileItemElement29);
            this.tiBOIncomeStatement.Enabled = false;
            this.tiBOIncomeStatement.Id = 19;
            this.tiBOIncomeStatement.Name = "tiBOIncomeStatement";
            this.tiBOIncomeStatement.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiBOIncomeStatement_ItemClick);
            // 
            // tiBODailyDuesCollection
            // 
            this.tiBODailyDuesCollection.AppearanceItem.Normal.BackColor = System.Drawing.Color.Maroon;
            this.tiBODailyDuesCollection.AppearanceItem.Normal.BorderColor = System.Drawing.Color.SaddleBrown;
            this.tiBODailyDuesCollection.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tiBODailyDuesCollection.AppearanceItem.Normal.ForeColor = System.Drawing.Color.White;
            this.tiBODailyDuesCollection.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tiBODailyDuesCollection.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tiBODailyDuesCollection.AppearanceItem.Normal.Options.UseFont = true;
            this.tiBODailyDuesCollection.AppearanceItem.Normal.Options.UseForeColor = true;
            tileItemElement30.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.lp_money_coins_to_bank_48;
            tileItemElement30.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement30.Text = "DSO Collection";
            tileItemElement30.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter;
            tileItemElement31.Text = "DSO";
            tileItemElement31.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.TopRight;
            this.tiBODailyDuesCollection.Elements.Add(tileItemElement30);
            this.tiBODailyDuesCollection.Elements.Add(tileItemElement31);
            this.tiBODailyDuesCollection.Enabled = false;
            this.tiBODailyDuesCollection.Id = 20;
            this.tiBODailyDuesCollection.Name = "tiBODailyDuesCollection";
            this.tiBODailyDuesCollection.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiBODailyDuesCollection_ItemClick);
            // 
            // tiBOFinancialAudit
            // 
            this.tiBOFinancialAudit.AppearanceItem.Normal.BackColor = System.Drawing.Color.Maroon;
            this.tiBOFinancialAudit.AppearanceItem.Normal.BorderColor = System.Drawing.Color.SaddleBrown;
            this.tiBOFinancialAudit.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tiBOFinancialAudit.AppearanceItem.Normal.ForeColor = System.Drawing.Color.White;
            this.tiBOFinancialAudit.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tiBOFinancialAudit.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tiBOFinancialAudit.AppearanceItem.Normal.Options.UseFont = true;
            this.tiBOFinancialAudit.AppearanceItem.Normal.Options.UseForeColor = true;
            tileItemElement32.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.lp_employees_delay_48;
            tileItemElement32.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement32.Text = "Financial Audit Trail";
            tileItemElement32.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter;
            tileItemElement33.Text = "FAT";
            tileItemElement33.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.TopRight;
            this.tiBOFinancialAudit.Elements.Add(tileItemElement32);
            this.tiBOFinancialAudit.Elements.Add(tileItemElement33);
            this.tiBOFinancialAudit.Enabled = false;
            this.tiBOFinancialAudit.Id = 28;
            this.tiBOFinancialAudit.Name = "tiBOFinancialAudit";
            this.tiBOFinancialAudit.RowCount = 2;
            this.tiBOFinancialAudit.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiBOFinancialAudit_ItemClick);
            // 
            // tiDuesRegister
            // 
            this.tiDuesRegister.AppearanceItem.Normal.BackColor = System.Drawing.Color.Maroon;
            this.tiDuesRegister.AppearanceItem.Normal.BorderColor = System.Drawing.Color.SaddleBrown;
            this.tiDuesRegister.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tiDuesRegister.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tiDuesRegister.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tiDuesRegister.AppearanceItem.Normal.Options.UseFont = true;
            tileItemElement34.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.lp_cashier_coins_48;
            tileItemElement34.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement34.Text = "Daily Dues Register";
            tileItemElement34.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter;
            tileItemElement35.Text = "DDR";
            tileItemElement35.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.TopRight;
            this.tiDuesRegister.Elements.Add(tileItemElement34);
            this.tiDuesRegister.Elements.Add(tileItemElement35);
            this.tiDuesRegister.Enabled = false;
            this.tiDuesRegister.Id = 39;
            this.tiDuesRegister.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium;
            this.tiDuesRegister.Name = "tiDuesRegister";
            this.tiDuesRegister.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiDuesRegister_ItemClick);
            // 
            // tiBOCancellationReport
            // 
            this.tiBOCancellationReport.AppearanceItem.Normal.BackColor = System.Drawing.Color.Maroon;
            this.tiBOCancellationReport.AppearanceItem.Normal.BorderColor = System.Drawing.Color.SaddleBrown;
            this.tiBOCancellationReport.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tiBOCancellationReport.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tiBOCancellationReport.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tiBOCancellationReport.AppearanceItem.Normal.Options.UseFont = true;
            tileItemElement36.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.lp_table_customer_delete_48;
            tileItemElement36.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement36.Text = "Cancellations";
            tileItemElement36.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter;
            this.tiBOCancellationReport.Elements.Add(tileItemElement36);
            this.tiBOCancellationReport.Enabled = false;
            this.tiBOCancellationReport.Id = 40;
            this.tiBOCancellationReport.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium;
            this.tiBOCancellationReport.Name = "tiBOCancellationReport";
            this.tiBOCancellationReport.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiBOCancellationReport_ItemClick);
            // 
            // tiCollatorManifest
            // 
            this.tiCollatorManifest.AppearanceItem.Normal.BackColor = System.Drawing.Color.Maroon;
            this.tiCollatorManifest.AppearanceItem.Normal.BorderColor = System.Drawing.Color.SaddleBrown;
            this.tiCollatorManifest.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tiCollatorManifest.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tiCollatorManifest.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tiCollatorManifest.AppearanceItem.Normal.Options.UseFont = true;
            tileItemElement37.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.lp_table_customer_delay_48;
            tileItemElement37.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement37.Text = "Collation Manifest";
            tileItemElement37.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter;
            this.tiCollatorManifest.Elements.Add(tileItemElement37);
            this.tiCollatorManifest.Enabled = false;
            this.tiCollatorManifest.Id = 37;
            this.tiCollatorManifest.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium;
            this.tiCollatorManifest.Name = "tiCollatorManifest";
            this.tiCollatorManifest.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiCollatorManifest_ItemClick);
            // 
            // tiBOOutstandingStatement
            // 
            this.tiBOOutstandingStatement.AppearanceItem.Normal.BackColor = System.Drawing.Color.Maroon;
            this.tiBOOutstandingStatement.AppearanceItem.Normal.BorderColor = System.Drawing.Color.SaddleBrown;
            this.tiBOOutstandingStatement.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tiBOOutstandingStatement.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tiBOOutstandingStatement.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tiBOOutstandingStatement.AppearanceItem.Normal.Options.UseFont = true;
            tileItemElement38.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.lp_inventory_banknote_48;
            tileItemElement38.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement38.Text = "Outstanding Statement";
            tileItemElement38.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter;
            tileItemElement39.Text = "OIS";
            tileItemElement39.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.TopRight;
            this.tiBOOutstandingStatement.Elements.Add(tileItemElement38);
            this.tiBOOutstandingStatement.Elements.Add(tileItemElement39);
            this.tiBOOutstandingStatement.Enabled = false;
            this.tiBOOutstandingStatement.Id = 42;
            this.tiBOOutstandingStatement.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium;
            this.tiBOOutstandingStatement.Name = "tiBOOutstandingStatement";
            this.tiBOOutstandingStatement.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiBOOutstandingStatement_ItemClick);
            // 
            // tiStaffPerformanceReport
            // 
            this.tiStaffPerformanceReport.AppearanceItem.Normal.BackColor = System.Drawing.Color.Maroon;
            this.tiStaffPerformanceReport.AppearanceItem.Normal.BorderColor = System.Drawing.Color.SaddleBrown;
            this.tiStaffPerformanceReport.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tiStaffPerformanceReport.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tiStaffPerformanceReport.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tiStaffPerformanceReport.AppearanceItem.Normal.Options.UseFont = true;
            tileItemElement40.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.lp_clock_user_48;
            tileItemElement40.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement40.Text = "Staff Performance";
            tileItemElement40.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter;
            tileItemElement41.Text = "SPR";
            this.tiStaffPerformanceReport.Elements.Add(tileItemElement40);
            this.tiStaffPerformanceReport.Elements.Add(tileItemElement41);
            this.tiStaffPerformanceReport.Enabled = false;
            this.tiStaffPerformanceReport.Id = 43;
            this.tiStaffPerformanceReport.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium;
            this.tiStaffPerformanceReport.Name = "tiStaffPerformanceReport";
            this.tiStaffPerformanceReport.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiStaffPerformanceReport_ItemClick);
            // 
            // tiBOReferrerBizContrib
            // 
            this.tiBOReferrerBizContrib.AppearanceItem.Normal.BackColor = System.Drawing.Color.Maroon;
            this.tiBOReferrerBizContrib.AppearanceItem.Normal.BorderColor = System.Drawing.Color.SaddleBrown;
            this.tiBOReferrerBizContrib.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tiBOReferrerBizContrib.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tiBOReferrerBizContrib.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tiBOReferrerBizContrib.AppearanceItem.Normal.Options.UseFont = true;
            tileItemElement42.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.lp_business_report_print_48;
            tileItemElement42.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement42.Text = "Referrer Contrib";
            tileItemElement42.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter;
            tileItemElement43.Text = "BCR";
            this.tiBOReferrerBizContrib.Elements.Add(tileItemElement42);
            this.tiBOReferrerBizContrib.Elements.Add(tileItemElement43);
            this.tiBOReferrerBizContrib.Enabled = false;
            this.tiBOReferrerBizContrib.Id = 41;
            this.tiBOReferrerBizContrib.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium;
            this.tiBOReferrerBizContrib.Name = "tiBOReferrerBizContrib";
            this.tiBOReferrerBizContrib.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiBOReferrerBizContrib_ItemClick);
            // 
            // tiTestUtilizationReport
            // 
            this.tiTestUtilizationReport.AppearanceItem.Normal.BackColor = System.Drawing.Color.Maroon;
            this.tiTestUtilizationReport.AppearanceItem.Normal.BorderColor = System.Drawing.Color.SaddleBrown;
            this.tiTestUtilizationReport.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tiTestUtilizationReport.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tiTestUtilizationReport.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tiTestUtilizationReport.AppearanceItem.Normal.Options.UseFont = true;
            tileItemElement44.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.lp_business_report_print_48;
            tileItemElement44.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement44.Text = "Tests Utilization";
            tileItemElement44.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter;
            tileItemElement45.Text = "TUS";
            this.tiTestUtilizationReport.Elements.Add(tileItemElement44);
            this.tiTestUtilizationReport.Elements.Add(tileItemElement45);
            this.tiTestUtilizationReport.Enabled = false;
            this.tiTestUtilizationReport.Id = 47;
            this.tiTestUtilizationReport.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium;
            this.tiTestUtilizationReport.Name = "tiTestUtilizationReport";
            this.tiTestUtilizationReport.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiTestUtilizationReport_ItemClick);
            // 
            // tiMarketingReports
            // 
            this.tiMarketingReports.AppearanceItem.Normal.BackColor = System.Drawing.Color.Maroon;
            this.tiMarketingReports.AppearanceItem.Normal.BorderColor = System.Drawing.Color.SaddleBrown;
            this.tiMarketingReports.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tiMarketingReports.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tiMarketingReports.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tiMarketingReports.AppearanceItem.Normal.Options.UseFont = true;
            tileItemElement46.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.lp_inventory_banknote_48;
            tileItemElement46.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement46.Text = "Referral Reports";
            tileItemElement46.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter;
            this.tiMarketingReports.Elements.Add(tileItemElement46);
            this.tiMarketingReports.Enabled = false;
            this.tiMarketingReports.Id = 32;
            this.tiMarketingReports.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium;
            this.tiMarketingReports.Name = "tiMarketingReports";
            this.tiMarketingReports.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiMarketingReports_ItemClick);
            // 
            // tiBIUtilizationReport
            // 
            this.tiBIUtilizationReport.AppearanceItem.Normal.BackColor = System.Drawing.Color.Maroon;
            this.tiBIUtilizationReport.AppearanceItem.Normal.BorderColor = System.Drawing.Color.SaddleBrown;
            this.tiBIUtilizationReport.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tiBIUtilizationReport.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tiBIUtilizationReport.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tiBIUtilizationReport.AppearanceItem.Normal.Options.UseFont = true;
            tileItemElement47.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.lp_factory_coins_48;
            tileItemElement47.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement47.Text = "BI Utilization";
            tileItemElement47.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter;
            tileItemElement48.Text = "BUS";
            this.tiBIUtilizationReport.Elements.Add(tileItemElement47);
            this.tiBIUtilizationReport.Elements.Add(tileItemElement48);
            this.tiBIUtilizationReport.Enabled = false;
            this.tiBIUtilizationReport.Id = 48;
            this.tiBIUtilizationReport.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium;
            this.tiBIUtilizationReport.Name = "tiBIUtilizationReport";
            this.tiBIUtilizationReport.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiBIUtilizationReport_ItemClick);
            // 
            // tiPhleboUtilization
            // 
            this.tiPhleboUtilization.AppearanceItem.Normal.BackColor = System.Drawing.Color.Maroon;
            this.tiPhleboUtilization.AppearanceItem.Normal.BorderColor = System.Drawing.Color.SaddleBrown;
            this.tiPhleboUtilization.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tiPhleboUtilization.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tiPhleboUtilization.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tiPhleboUtilization.AppearanceItem.Normal.Options.UseFont = true;
            tileItemElement49.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.lp_order_new_48;
            tileItemElement49.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement49.Text = "Phlebo Util Report";
            tileItemElement49.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter;
            this.tiPhleboUtilization.Elements.Add(tileItemElement49);
            this.tiPhleboUtilization.Enabled = false;
            this.tiPhleboUtilization.Id = 33;
            this.tiPhleboUtilization.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium;
            this.tiPhleboUtilization.Name = "tiPhleboUtilization";
            this.tiPhleboUtilization.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiPhleboUtilization_ItemClick);
            // 
            // tiBORefEligibilitySum
            // 
            this.tiBORefEligibilitySum.AppearanceItem.Normal.BackColor = System.Drawing.Color.Maroon;
            this.tiBORefEligibilitySum.AppearanceItem.Normal.BorderColor = System.Drawing.Color.SaddleBrown;
            this.tiBORefEligibilitySum.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tiBORefEligibilitySum.AppearanceItem.Normal.ForeColor = System.Drawing.Color.White;
            this.tiBORefEligibilitySum.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tiBORefEligibilitySum.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tiBORefEligibilitySum.AppearanceItem.Normal.Options.UseFont = true;
            this.tiBORefEligibilitySum.AppearanceItem.Normal.Options.UseForeColor = true;
            tileItemElement50.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.lp_money_coins_to_bank_48;
            tileItemElement50.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement50.Text = "Referral Eligibilty Summary";
            tileItemElement50.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter;
            tileItemElement51.Text = "RES";
            tileItemElement51.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.TopRight;
            this.tiBORefEligibilitySum.Elements.Add(tileItemElement50);
            this.tiBORefEligibilitySum.Elements.Add(tileItemElement51);
            this.tiBORefEligibilitySum.Enabled = false;
            this.tiBORefEligibilitySum.Id = 20;
            this.tiBORefEligibilitySum.Name = "tiBORefEligibilitySum";
            this.tiBORefEligibilitySum.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiBORefEligibilitySum_ItemClick);
            // 
            // tiBODiscountOverageReport
            // 
            this.tiBODiscountOverageReport.AppearanceItem.Normal.BackColor = System.Drawing.Color.Maroon;
            this.tiBODiscountOverageReport.AppearanceItem.Normal.BorderColor = System.Drawing.Color.SaddleBrown;
            this.tiBODiscountOverageReport.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tiBODiscountOverageReport.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tiBODiscountOverageReport.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tiBODiscountOverageReport.AppearanceItem.Normal.Options.UseFont = true;
            tileItemElement52.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.lp_factory_coins_48;
            tileItemElement52.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement52.Text = "Discount Overage";
            tileItemElement52.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter;
            tileItemElement53.Text = "DOR";
            tileItemElement53.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.TopRight;
            this.tiBODiscountOverageReport.Elements.Add(tileItemElement52);
            this.tiBODiscountOverageReport.Elements.Add(tileItemElement53);
            this.tiBODiscountOverageReport.Enabled = false;
            this.tiBODiscountOverageReport.Id = 51;
            this.tiBODiscountOverageReport.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium;
            this.tiBODiscountOverageReport.Name = "tiBODiscountOverageReport";
            this.tiBODiscountOverageReport.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiBODiscountOverageReport_ItemClick);
            // 
            // tiBOLabwiseDailyIncomeReport
            // 
            this.tiBOLabwiseDailyIncomeReport.AppearanceItem.Normal.BackColor = System.Drawing.Color.Maroon;
            this.tiBOLabwiseDailyIncomeReport.AppearanceItem.Normal.BorderColor = System.Drawing.Color.SaddleBrown;
            this.tiBOLabwiseDailyIncomeReport.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tiBOLabwiseDailyIncomeReport.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tiBOLabwiseDailyIncomeReport.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tiBOLabwiseDailyIncomeReport.AppearanceItem.Normal.Options.UseFont = true;
            tileItemElement54.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.lp_money_query_48;
            tileItemElement54.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement54.Text = "Lab-wise Daily Income Summary";
            tileItemElement54.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter;
            tileItemElement55.Text = "LDIS";
            tileItemElement55.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.TopRight;
            this.tiBOLabwiseDailyIncomeReport.Elements.Add(tileItemElement54);
            this.tiBOLabwiseDailyIncomeReport.Elements.Add(tileItemElement55);
            this.tiBOLabwiseDailyIncomeReport.Enabled = false;
            this.tiBOLabwiseDailyIncomeReport.Id = 52;
            this.tiBOLabwiseDailyIncomeReport.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium;
            this.tiBOLabwiseDailyIncomeReport.Name = "tiBOLabwiseDailyIncomeReport";
            this.tiBOLabwiseDailyIncomeReport.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiBOLabwiseDailyIncomeReport_ItemClick);
            // 
            // grpCatalog
            // 
            this.grpCatalog.Items.Add(this.tiCatReferrerAdd);
            this.grpCatalog.Items.Add(this.tiCatReferrerBrowse);
            this.grpCatalog.Items.Add(this.tiAdminBackend);
            this.grpCatalog.Items.Add(this.tiMQHeartbeat);
            this.grpCatalog.Items.Add(this.tiTestNew);
            this.grpCatalog.Items.Add(this.tiTestEdit);
            this.grpCatalog.Items.Add(this.tiWorkstationRegistry);
            this.grpCatalog.Name = "grpCatalog";
            this.grpCatalog.Text = "Catalog";
            // 
            // tiCatReferrerAdd
            // 
            this.tiCatReferrerAdd.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(0)))), ((int)(((byte)(64)))));
            this.tiCatReferrerAdd.AppearanceItem.Normal.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(0)))), ((int)(((byte)(192)))));
            this.tiCatReferrerAdd.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tiCatReferrerAdd.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tiCatReferrerAdd.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tiCatReferrerAdd.AppearanceItem.Normal.Options.UseFont = true;
            this.tiCatReferrerAdd.AppearanceItem.Normal.Options.UseTextOptions = true;
            this.tiCatReferrerAdd.AppearanceItem.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.tiCatReferrerAdd.AppearanceItem.Normal.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Bottom;
            tileItemElement56.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.lp_address_book_add2_48;
            tileItemElement56.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement56.ImageOptions.ImageToTextAlignment = DevExpress.XtraEditors.TileControlImageToTextAlignment.Top;
            tileItemElement56.Text = "Add Referrer";
            tileItemElement56.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter;
            this.tiCatReferrerAdd.Elements.Add(tileItemElement56);
            this.tiCatReferrerAdd.Id = 24;
            this.tiCatReferrerAdd.Name = "tiCatReferrerAdd";
            this.tiCatReferrerAdd.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiCatReferrerAdd_ItemClick);
            // 
            // tiCatReferrerBrowse
            // 
            this.tiCatReferrerBrowse.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(0)))), ((int)(((byte)(64)))));
            this.tiCatReferrerBrowse.AppearanceItem.Normal.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(0)))), ((int)(((byte)(192)))));
            this.tiCatReferrerBrowse.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tiCatReferrerBrowse.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tiCatReferrerBrowse.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tiCatReferrerBrowse.AppearanceItem.Normal.Options.UseFont = true;
            this.tiCatReferrerBrowse.AppearanceItem.Normal.Options.UseTextOptions = true;
            this.tiCatReferrerBrowse.AppearanceItem.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.tiCatReferrerBrowse.AppearanceItem.Normal.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Bottom;
            tileItemElement57.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.lp_address_book_customer_48;
            tileItemElement57.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement57.Text = "Browse Referrers";
            tileItemElement57.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter;
            this.tiCatReferrerBrowse.Elements.Add(tileItemElement57);
            this.tiCatReferrerBrowse.Id = 27;
            this.tiCatReferrerBrowse.Name = "tiCatReferrerBrowse";
            this.tiCatReferrerBrowse.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiCatReferrerBrowse_ItemClick);
            // 
            // tiAdminBackend
            // 
            this.tiAdminBackend.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.tiAdminBackend.AppearanceItem.Normal.BorderColor = System.Drawing.Color.Black;
            this.tiAdminBackend.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tiAdminBackend.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tiAdminBackend.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tiAdminBackend.AppearanceItem.Normal.Options.UseFont = true;
            tileItemElement58.ImageOptions.Image = global::LabMaestro.Controls.Win.ResourceIcons.package_utilities_24;
            tileItemElement58.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement58.Text = "";
            tileItemElement58.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomRight;
            this.tiAdminBackend.Elements.Add(tileItemElement58);
            this.tiAdminBackend.Id = 29;
            this.tiAdminBackend.ItemSize = DevExpress.XtraEditors.TileItemSize.Small;
            this.tiAdminBackend.Name = "tiAdminBackend";
            this.tiAdminBackend.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiAdminBackend_ItemClick);
            // 
            // tiMQHeartbeat
            // 
            this.tiMQHeartbeat.AppearanceItem.Normal.BackColor = System.Drawing.Color.Maroon;
            this.tiMQHeartbeat.AppearanceItem.Normal.BorderColor = System.Drawing.Color.Crimson;
            this.tiMQHeartbeat.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tiMQHeartbeat.AppearanceItem.Normal.Options.UseBorderColor = true;
            tileItemElement59.ImageOptions.Image = global::LabMaestro.Controls.Win.ResourceIcons.heart_toolbar_24;
            tileItemElement59.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement59.Text = "";
            this.tiMQHeartbeat.Elements.Add(tileItemElement59);
            this.tiMQHeartbeat.Id = 31;
            this.tiMQHeartbeat.ItemSize = DevExpress.XtraEditors.TileItemSize.Small;
            this.tiMQHeartbeat.Name = "tiMQHeartbeat";
            this.tiMQHeartbeat.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiMQHeartbeat_ItemClick);
            // 
            // tiTestNew
            // 
            this.tiTestNew.AppearanceItem.Normal.BackColor = System.Drawing.Color.DarkOliveGreen;
            this.tiTestNew.AppearanceItem.Normal.BorderColor = System.Drawing.Color.Black;
            this.tiTestNew.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tiTestNew.AppearanceItem.Normal.Options.UseBorderColor = true;
            tileItemElement60.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.new_16;
            tileItemElement60.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement60.Text = "";
            this.tiTestNew.Elements.Add(tileItemElement60);
            this.tiTestNew.Id = 34;
            this.tiTestNew.ItemSize = DevExpress.XtraEditors.TileItemSize.Small;
            this.tiTestNew.Name = "tiTestNew";
            this.tiTestNew.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiTestNew_ItemClick);
            // 
            // tiTestEdit
            // 
            this.tiTestEdit.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(64)))));
            this.tiTestEdit.AppearanceItem.Normal.BorderColor = System.Drawing.Color.MidnightBlue;
            this.tiTestEdit.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tiTestEdit.AppearanceItem.Normal.Options.UseBorderColor = true;
            tileItemElement61.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.list_edit_16;
            tileItemElement61.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement61.Text = "";
            this.tiTestEdit.Elements.Add(tileItemElement61);
            this.tiTestEdit.Id = 35;
            this.tiTestEdit.ItemSize = DevExpress.XtraEditors.TileItemSize.Small;
            this.tiTestEdit.Name = "tiTestEdit";
            this.tiTestEdit.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiTestEdit_ItemClick);
            // 
            // tiWorkstationRegistry
            // 
            tileItemElement62.ImageOptions.Image = global::LabMaestro.Controls.Win.ResourceIcons.client_list24;
            tileItemElement62.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement62.Text = "";
            this.tiWorkstationRegistry.Elements.Add(tileItemElement62);
            this.tiWorkstationRegistry.Id = 46;
            this.tiWorkstationRegistry.ItemSize = DevExpress.XtraEditors.TileItemSize.Small;
            this.tiWorkstationRegistry.Name = "tiWorkstationRegistry";
            this.tiWorkstationRegistry.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiWorkstationRegistry_ItemClick);
            // 
            // grpProfile
            // 
            this.grpProfile.Items.Add(this.tiProfilePasswordChange);
            this.grpProfile.Name = "grpProfile";
            this.grpProfile.Text = "User Profile";
            // 
            // tiProfilePasswordChange
            // 
            this.tiProfilePasswordChange.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tiProfilePasswordChange.AppearanceItem.Normal.Options.UseFont = true;
            this.tiProfilePasswordChange.AppearanceItem.Normal.Options.UseTextOptions = true;
            this.tiProfilePasswordChange.AppearanceItem.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.tiProfilePasswordChange.AppearanceItem.Normal.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Bottom;
            tileItemElement63.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.lp_address_book_customer_48;
            tileItemElement63.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement63.ImageOptions.ImageToTextAlignment = DevExpress.XtraEditors.TileControlImageToTextAlignment.Top;
            tileItemElement63.Text = "Change Password";
            this.tiProfilePasswordChange.Elements.Add(tileItemElement63);
            this.tiProfilePasswordChange.Id = 25;
            this.tiProfilePasswordChange.Name = "tiProfilePasswordChange";
            this.tiProfilePasswordChange.Tag = 401;
            this.tiProfilePasswordChange.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiProfilePasswordChange_ItemClick);
            // 
            // labelControl1
            // 
            this.labelControl1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.labelControl1.Appearance.Font = new System.Drawing.Font("Segoe UI", 18F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Appearance.ForeColor = System.Drawing.Color.Gray;
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Appearance.Options.UseForeColor = true;
            this.labelControl1.Location = new System.Drawing.Point(661, 10);
            this.labelControl1.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(265, 32);
            this.labelControl1.TabIndex = 1;
            this.labelControl1.Text = "LabMaestro Launchpad";
            // 
            // lblVersion
            // 
            this.lblVersion.Appearance.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblVersion.Appearance.ForeColor = System.Drawing.Color.DimGray;
            this.lblVersion.Appearance.Options.UseFont = true;
            this.lblVersion.Appearance.Options.UseForeColor = true;
            this.lblVersion.Location = new System.Drawing.Point(34, 12);
            this.lblVersion.Name = "lblVersion";
            this.lblVersion.Size = new System.Drawing.Size(54, 17);
            this.lblVersion.TabIndex = 2;
            this.lblVersion.Text = "v13.6.17B";
            // 
            // lblGreeting
            // 
            this.lblGreeting.Appearance.Font = new System.Drawing.Font("Segoe UI", 24F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblGreeting.Appearance.ForeColor = System.Drawing.Color.Goldenrod;
            this.lblGreeting.Appearance.Options.UseFont = true;
            this.lblGreeting.Appearance.Options.UseForeColor = true;
            this.lblGreeting.Location = new System.Drawing.Point(219, 12);
            this.lblGreeting.Name = "lblGreeting";
            this.lblGreeting.Size = new System.Drawing.Size(287, 45);
            this.lblGreeting.TabIndex = 3;
            this.lblGreeting.Text = "Hello {User Name}!";
            // 
            // lblIpAddress
            // 
            this.lblIpAddress.Appearance.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblIpAddress.Appearance.ForeColor = System.Drawing.Color.DimGray;
            this.lblIpAddress.Appearance.Options.UseFont = true;
            this.lblIpAddress.Appearance.Options.UseForeColor = true;
            this.lblIpAddress.Location = new System.Drawing.Point(34, 34);
            this.lblIpAddress.Name = "lblIpAddress";
            this.lblIpAddress.Size = new System.Drawing.Size(65, 17);
            this.lblIpAddress.TabIndex = 7;
            this.lblIpAddress.Text = "***********";
            // 
            // lblLoginTime
            // 
            this.lblLoginTime.Appearance.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblLoginTime.Appearance.ForeColor = System.Drawing.Color.DimGray;
            this.lblLoginTime.Appearance.Options.UseFont = true;
            this.lblLoginTime.Appearance.Options.UseForeColor = true;
            this.lblLoginTime.Location = new System.Drawing.Point(33, 80);
            this.lblLoginTime.Name = "lblLoginTime";
            this.lblLoginTime.Size = new System.Drawing.Size(55, 17);
            this.lblLoginTime.TabIndex = 8;
            this.lblLoginTime.Text = "09:55 AM";
            // 
            // lblStaffname
            // 
            this.lblStaffname.Appearance.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblStaffname.Appearance.ForeColor = System.Drawing.Color.DimGray;
            this.lblStaffname.Appearance.Options.UseFont = true;
            this.lblStaffname.Appearance.Options.UseForeColor = true;
            this.lblStaffname.Location = new System.Drawing.Point(34, 57);
            this.lblStaffname.Name = "lblStaffname";
            this.lblStaffname.Size = new System.Drawing.Size(71, 17);
            this.lblStaffname.TabIndex = 10;
            this.lblStaffname.Text = "Receptionist";
            // 
            // lblCopyright
            // 
            this.lblCopyright.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.lblCopyright.Appearance.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblCopyright.Appearance.ForeColor = System.Drawing.Color.DimGray;
            this.lblCopyright.Appearance.Options.UseFont = true;
            this.lblCopyright.Appearance.Options.UseForeColor = true;
            this.lblCopyright.Location = new System.Drawing.Point(11, 639);
            this.lblCopyright.Name = "lblCopyright";
            this.lblCopyright.Size = new System.Drawing.Size(370, 17);
            this.lblCopyright.TabIndex = 12;
            this.lblCopyright.Text = "Copyright © 2012-13 Dr. Masroor Ehsan. All rights reserved.";
            // 
            // labelControl4
            // 
            this.labelControl4.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.labelControl4.Appearance.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl4.Appearance.ForeColor = System.Drawing.Color.DimGray;
            this.labelControl4.Appearance.Options.UseFont = true;
            this.labelControl4.Appearance.Options.UseForeColor = true;
            this.labelControl4.Location = new System.Drawing.Point(389, 642);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(628, 13);
            this.labelControl4.TabIndex = 13;
            this.labelControl4.Text = "Reproduction, modification, storage or retransmission, in any form or by any mean" +
    "s, is strictly prohibited without prior written permission.";
            // 
            // tileItem2
            // 
            this.tileItem2.AppearanceItem.Normal.BackColor = System.Drawing.Color.Maroon;
            this.tileItem2.AppearanceItem.Normal.BorderColor = System.Drawing.Color.SaddleBrown;
            this.tileItem2.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileItem2.AppearanceItem.Normal.ForeColor = System.Drawing.Color.White;
            this.tileItem2.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tileItem2.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tileItem2.AppearanceItem.Normal.Options.UseFont = true;
            this.tileItem2.AppearanceItem.Normal.Options.UseForeColor = true;
            tileItemElement64.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement64.Text = "tileItem2";
            tileItemElement64.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter;
            this.tileItem2.Elements.Add(tileItemElement64);
            this.tileItem2.Id = 21;
            this.tileItem2.Name = "tileItem2";
            // 
            // autoUpdater
            // 
            this.autoUpdater.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.autoUpdater.BackColor = System.Drawing.Color.Silver;
            this.autoUpdater.ContainerForm = this;
            this.autoUpdater.DaysBetweenChecks = 1;
            this.autoUpdater.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(45)))), ((int)(((byte)(45)))), ((int)(((byte)(45)))));
            this.autoUpdater.GUID = "0D68A0BC-18E5-41D2-B05F-6BADFBFBF4E1";
            this.autoUpdater.Location = new System.Drawing.Point(825, 616);
            this.autoUpdater.Name = "autoUpdater";
            this.autoUpdater.Size = new System.Drawing.Size(16, 16);
            this.autoUpdater.TabIndex = 14;
            this.autoUpdater.Visible = false;
            this.autoUpdater.WaitBeforeCheckSecs = 5;
            this.autoUpdater.wyUpdateCommandline = null;
            // 
            // btnCheckUpdate
            // 
            this.btnCheckUpdate.AllowHtmlDraw = DevExpress.Utils.DefaultBoolean.False;
            this.btnCheckUpdate.AllowHtmlTextInToolTip = DevExpress.Utils.DefaultBoolean.False;
            this.btnCheckUpdate.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCheckUpdate.Appearance.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCheckUpdate.Appearance.Options.UseFont = true;
            this.btnCheckUpdate.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleLeft;
            this.btnCheckUpdate.Location = new System.Drawing.Point(847, 613);
            this.btnCheckUpdate.LookAndFeel.SkinName = "Darkroom";
            this.btnCheckUpdate.LookAndFeel.UseDefaultLookAndFeel = false;
            this.btnCheckUpdate.Name = "btnCheckUpdate";
            this.btnCheckUpdate.Size = new System.Drawing.Size(79, 23);
            this.btnCheckUpdate.TabIndex = 15;
            this.btnCheckUpdate.Text = "Update";
            this.btnCheckUpdate.ToolTip = "Check for Update";
            this.btnCheckUpdate.Click += new System.EventHandler(this.btnCheckUpdate_Click);
            // 
            // btnMinimize
            // 
            this.btnMinimize.AllowHtmlDraw = DevExpress.Utils.DefaultBoolean.False;
            this.btnMinimize.AllowHtmlTextInToolTip = DevExpress.Utils.DefaultBoolean.False;
            this.btnMinimize.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnMinimize.Appearance.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnMinimize.Appearance.Options.UseFont = true;
            this.btnMinimize.ImageOptions.Image = global::LabMaestro.Controls.Win.ResourceIcons.down_arrow16;
            this.btnMinimize.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleLeft;
            this.btnMinimize.Location = new System.Drawing.Point(816, 49);
            this.btnMinimize.LookAndFeel.SkinName = "Darkroom";
            this.btnMinimize.LookAndFeel.UseDefaultLookAndFeel = false;
            this.btnMinimize.Name = "btnMinimize";
            this.btnMinimize.Size = new System.Drawing.Size(25, 23);
            this.btnMinimize.TabIndex = 16;
            this.btnMinimize.ToolTip = "Minimize Window";
            this.btnMinimize.Click += new System.EventHandler(this.btnMinimize_Click);
            // 
            // btnLogOut
            // 
            this.btnLogOut.AllowHtmlDraw = DevExpress.Utils.DefaultBoolean.False;
            this.btnLogOut.AllowHtmlTextInToolTip = DevExpress.Utils.DefaultBoolean.False;
            this.btnLogOut.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnLogOut.Appearance.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnLogOut.Appearance.Options.UseFont = true;
            this.btnLogOut.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources._1371662505_logout;
            this.btnLogOut.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleLeft;
            this.btnLogOut.Location = new System.Drawing.Point(847, 49);
            this.btnLogOut.LookAndFeel.SkinName = "Darkroom";
            this.btnLogOut.LookAndFeel.UseDefaultLookAndFeel = false;
            this.btnLogOut.Name = "btnLogOut";
            this.btnLogOut.Size = new System.Drawing.Size(79, 23);
            this.btnLogOut.TabIndex = 11;
            this.btnLogOut.Text = "Log out";
            this.btnLogOut.ToolTip = "Log out";
            this.btnLogOut.Click += new System.EventHandler(this.btnLogOut_Click);
            // 
            // pictureBox4
            // 
            this.pictureBox4.Image = global::LabMaestro.Controls.Win.Resources._1371579171_user;
            this.pictureBox4.Location = new System.Drawing.Point(12, 58);
            this.pictureBox4.Name = "pictureBox4";
            this.pictureBox4.Size = new System.Drawing.Size(16, 16);
            this.pictureBox4.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox4.TabIndex = 9;
            this.pictureBox4.TabStop = false;
            // 
            // pictureBox3
            // 
            this.pictureBox3.Image = global::LabMaestro.Controls.Win.Resources._1371579197_bank;
            this.pictureBox3.Location = new System.Drawing.Point(12, 13);
            this.pictureBox3.Name = "pictureBox3";
            this.pictureBox3.Size = new System.Drawing.Size(16, 16);
            this.pictureBox3.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox3.TabIndex = 6;
            this.pictureBox3.TabStop = false;
            // 
            // pictureBox2
            // 
            this.pictureBox2.Image = global::LabMaestro.Controls.Win.Resources._1371579182_world;
            this.pictureBox2.Location = new System.Drawing.Point(12, 35);
            this.pictureBox2.Name = "pictureBox2";
            this.pictureBox2.Size = new System.Drawing.Size(16, 16);
            this.pictureBox2.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox2.TabIndex = 5;
            this.pictureBox2.TabStop = false;
            // 
            // pictureBox1
            // 
            this.pictureBox1.Image = global::LabMaestro.Controls.Win.Resources._1371579180_full_time;
            this.pictureBox1.Location = new System.Drawing.Point(11, 81);
            this.pictureBox1.Name = "pictureBox1";
            this.pictureBox1.Size = new System.Drawing.Size(16, 16);
            this.pictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBox1.TabIndex = 4;
            this.pictureBox1.TabStop = false;
            // 
            // tiBOReferrerAdd
            // 
            this.tiBOReferrerAdd.AppearanceItem.Normal.BackColor = System.Drawing.Color.Maroon;
            this.tiBOReferrerAdd.AppearanceItem.Normal.BorderColor = System.Drawing.Color.SaddleBrown;
            this.tiBOReferrerAdd.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tiBOReferrerAdd.AppearanceItem.Normal.ForeColor = System.Drawing.Color.White;
            this.tiBOReferrerAdd.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tiBOReferrerAdd.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tiBOReferrerAdd.AppearanceItem.Normal.Options.UseFont = true;
            this.tiBOReferrerAdd.AppearanceItem.Normal.Options.UseForeColor = true;
            tileItemElement65.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.lp_address_book_add2_48;
            tileItemElement65.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement65.Text = "Add Referrer";
            tileItemElement65.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter;
            this.tiBOReferrerAdd.Elements.Add(tileItemElement65);
            this.tiBOReferrerAdd.Id = 22;
            this.tiBOReferrerAdd.Name = "tiBOReferrerAdd";
            // 
            // tileItem1
            // 
            this.tileItem1.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(0)))), ((int)(((byte)(64)))));
            this.tileItem1.AppearanceItem.Normal.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(0)))), ((int)(((byte)(192)))));
            this.tileItem1.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileItem1.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tileItem1.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tileItem1.AppearanceItem.Normal.Options.UseFont = true;
            this.tileItem1.AppearanceItem.Normal.Options.UseTextOptions = true;
            this.tileItem1.AppearanceItem.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.tileItem1.AppearanceItem.Normal.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Bottom;
            tileItemElement66.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.lp_address_book_add2_48;
            tileItemElement66.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement66.ImageOptions.ImageToTextAlignment = DevExpress.XtraEditors.TileControlImageToTextAlignment.Top;
            tileItemElement66.Text = "Add Referrer";
            this.tileItem1.Elements.Add(tileItemElement66);
            this.tileItem1.Id = 24;
            this.tileItem1.Name = "tileItem1";
            // 
            // tileItem4
            // 
            this.tileItem4.AppearanceItem.Normal.BackColor = System.Drawing.Color.Maroon;
            this.tileItem4.AppearanceItem.Normal.BorderColor = System.Drawing.Color.SaddleBrown;
            this.tileItem4.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileItem4.AppearanceItem.Normal.ForeColor = System.Drawing.Color.White;
            this.tileItem4.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tileItem4.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tileItem4.AppearanceItem.Normal.Options.UseFont = true;
            this.tileItem4.AppearanceItem.Normal.Options.UseForeColor = true;
            tileItemElement67.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.lp_money_coins_to_bank_48;
            tileItemElement67.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement67.Text = "Daily Dues Collection";
            tileItemElement67.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter;
            this.tileItem4.Elements.Add(tileItemElement67);
            this.tileItem4.Enabled = false;
            this.tileItem4.Id = 20;
            this.tileItem4.Name = "tileItem4";
            // 
            // tileItem5
            // 
            this.tileItem5.AppearanceItem.Normal.BackColor = System.Drawing.Color.Indigo;
            this.tileItem5.AppearanceItem.Normal.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(0)))), ((int)(((byte)(64)))));
            this.tileItem5.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileItem5.AppearanceItem.Normal.GradientMode = System.Drawing.Drawing2D.LinearGradientMode.ForwardDiagonal;
            this.tileItem5.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tileItem5.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tileItem5.AppearanceItem.Normal.Options.UseFont = true;
            tileItemElement68.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.lp_business_report_print_48;
            tileItemElement68.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement68.Text = "Generate Facesheets";
            tileItemElement68.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter;
            this.tileItem5.Elements.Add(tileItemElement68);
            this.tileItem5.Id = 206;
            this.tileItem5.Name = "tileItem5";
            // 
            // tileItem6
            // 
            this.tileItem6.AppearanceItem.Normal.BackColor = System.Drawing.Color.Maroon;
            this.tileItem6.AppearanceItem.Normal.BorderColor = System.Drawing.Color.SaddleBrown;
            this.tileItem6.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tileItem6.AppearanceItem.Normal.Options.UseBorderColor = true;
            tileItemElement69.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.lp_order_new_48;
            tileItemElement69.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement69.Text = "Phlebo Util Report";
            tileItemElement69.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter;
            this.tileItem6.Elements.Add(tileItemElement69);
            this.tileItem6.Id = 33;
            this.tileItem6.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium;
            this.tileItem6.Name = "tileItem6";
            // 
            // tileItem7
            // 
            this.tileItem7.AppearanceItem.Normal.BackColor = System.Drawing.Color.Maroon;
            this.tileItem7.AppearanceItem.Normal.BorderColor = System.Drawing.Color.SaddleBrown;
            this.tileItem7.AppearanceItem.Normal.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileItem7.AppearanceItem.Normal.ForeColor = System.Drawing.Color.White;
            this.tileItem7.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tileItem7.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tileItem7.AppearanceItem.Normal.Options.UseFont = true;
            this.tileItem7.AppearanceItem.Normal.Options.UseForeColor = true;
            tileItemElement70.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.lp_money_coins_to_bank_48;
            tileItemElement70.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement70.Text = "Referral Eligibilty Summary";
            tileItemElement70.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.BottomCenter;
            tileItemElement71.Text = "RES";
            tileItemElement71.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.TopRight;
            this.tileItem7.Elements.Add(tileItemElement70);
            this.tileItem7.Elements.Add(tileItemElement71);
            this.tileItem7.Enabled = false;
            this.tileItem7.Id = 20;
            this.tileItem7.Name = "tileItem7";
            // 
            // LaunchPadForm
            // 
            this.Appearance.Options.UseFont = true;
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.ClientSize = new System.Drawing.Size(934, 668);
            this.Controls.Add(this.btnMinimize);
            this.Controls.Add(this.btnCheckUpdate);
            this.Controls.Add(this.autoUpdater);
            this.Controls.Add(this.labelControl4);
            this.Controls.Add(this.lblCopyright);
            this.Controls.Add(this.btnLogOut);
            this.Controls.Add(this.lblStaffname);
            this.Controls.Add(this.pictureBox4);
            this.Controls.Add(this.lblLoginTime);
            this.Controls.Add(this.lblIpAddress);
            this.Controls.Add(this.pictureBox3);
            this.Controls.Add(this.pictureBox2);
            this.Controls.Add(this.pictureBox1);
            this.Controls.Add(this.lblGreeting);
            this.Controls.Add(this.lblVersion);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.tileControl);
            this.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
            this.LookAndFeel.SkinName = "Darkroom";
            this.LookAndFeel.UseDefaultLookAndFeel = false;
            this.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "LaunchPadForm";
            this.SizeGripStyle = System.Windows.Forms.SizeGripStyle.Hide;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "LaunchPadForm";
            this.Load += new System.EventHandler(this.LaunchPadForm_Load);
            ((System.ComponentModel.ISupportInitialize)(this.autoUpdater)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.TileControl tileControl;
        private DevExpress.XtraEditors.TileGroup grpOrderEntry;
        private DevExpress.XtraEditors.TileItem tiNewLabOrder;
        private DevExpress.XtraEditors.TileItem tiProcessReceivables;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.LabelControl lblVersion;
        private DevExpress.XtraEditors.LabelControl lblGreeting;
        private DevExpress.XtraEditors.TileItem tiSearchInvoices;
        private DevExpress.XtraEditors.TileGroup grpWorkflow;
        private DevExpress.XtraEditors.TileItem tiResultEntry;
        private DevExpress.XtraEditors.TileItem tiResultsVerification;
        private DevExpress.XtraEditors.TileItem tiReportFinalization;
        private DevExpress.XtraEditors.TileItem tiReportCollation;
        private DevExpress.XtraEditors.TileItem tiReportDispatch;
        private DevExpress.XtraEditors.TileItem tiFacesheet;
        private DevExpress.XtraEditors.TileGroup grpUserShift;
        private DevExpress.XtraEditors.TileItem tiShiftOpen;
        private DevExpress.XtraEditors.TileGroup grpBackOffice;
        private DevExpress.XtraEditors.TileItem tiShiftClose;
        private DevExpress.XtraEditors.TileItem tiUserShiftHistory;
        private System.Windows.Forms.PictureBox pictureBox1;
        private System.Windows.Forms.PictureBox pictureBox2;
        private System.Windows.Forms.PictureBox pictureBox3;
        private DevExpress.XtraEditors.LabelControl lblIpAddress;
        private DevExpress.XtraEditors.LabelControl lblLoginTime;
        private DevExpress.XtraEditors.LabelControl lblStaffname;
        private System.Windows.Forms.PictureBox pictureBox4;
        private DevExpress.XtraEditors.TileItem tiUserShiftReports;
        private DevExpress.XtraEditors.TileItem tiBOShiftReporting;
        private DevExpress.XtraEditors.TileItem tiBOUserSummaries;
        private DevExpress.XtraEditors.SimpleButton btnLogOut;
        private DevExpress.XtraEditors.TileItem tiBODailyReceivables;
        private DevExpress.XtraEditors.TileItem tiBOReceivablesSummary;
        private DevExpress.XtraEditors.TileItem tiBOOutstandingCollection;
        private DevExpress.XtraEditors.TileItem tiBOIncomeStatement;
        private DevExpress.XtraEditors.TileItem tiBODailyDuesCollection;
        private DevExpress.XtraEditors.TileItem tiBORefEligibilitySum;
        private DevExpress.XtraEditors.LabelControl lblCopyright;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.TileItem tiBOReferrerAdd;
        private DevExpress.XtraEditors.TileItem tileItem2;
        private DevExpress.XtraEditors.TileGroup grpCatalog;
        private DevExpress.XtraEditors.TileItem tiCatReferrerAdd;
        private DevExpress.XtraEditors.TileGroup grpProfile;
        private DevExpress.XtraEditors.TileItem tiProfilePasswordChange;
        private wyDay.Controls.AutomaticUpdater autoUpdater;
        private DevExpress.XtraEditors.SimpleButton btnCheckUpdate;
        private DevExpress.XtraEditors.TileItem tileItem1;
        private DevExpress.XtraEditors.TileItem tiCatReferrerBrowse;
        private DevExpress.XtraEditors.SimpleButton btnMinimize;
        private DevExpress.XtraEditors.TileItem tiBOFinancialAudit;
        private DevExpress.XtraEditors.TileItem tileItem4;
        private DevExpress.XtraEditors.TileItem tiAdminBackend;
        private DevExpress.XtraEditors.TileItem tiThermalLabelPrint;
        private DevExpress.XtraEditors.TileItem tileItem5;
        private DevExpress.XtraEditors.TileItem tiMQHeartbeat;
        private DevExpress.XtraEditors.TileItem tiMarketingReports;
        private DevExpress.XtraEditors.TileItem tiPhleboUtilization;
        private DevExpress.XtraEditors.TileItem tiTestNew;
        private DevExpress.XtraEditors.TileItem tiTestEdit;
        private DevExpress.XtraEditors.TileItem tiCollatorManifest;
        private DevExpress.XtraEditors.TileItem tiDuesRegister;
        private DevExpress.XtraEditors.TileItem tiBOCancellationReport;
        private DevExpress.XtraEditors.TileItem tileItem6;
        private DevExpress.XtraEditors.TileItem tiBOReferrerBizContrib;
        private DevExpress.XtraEditors.TileItem tiBOOutstandingStatement;
        private DevExpress.XtraEditors.TileItem tiStaffPerformanceReport;
        private DevExpress.XtraEditors.TileItem tiBulkDispatch;
        private DevExpress.XtraEditors.TileItem tiWorkstationRegistry;
        private DevExpress.XtraEditors.TileItem tiTestUtilizationReport;
        private DevExpress.XtraEditors.TileItem tiBIUtilizationReport;
        private DevExpress.XtraEditors.TileItem tiStaffShiftHistory;
        private DevExpress.XtraEditors.TileItem tiNewExternalOrder;
        private DevExpress.XtraEditors.TileItem tiBODiscountOverageReport;
        private DevExpress.XtraEditors.TileItem tileItem7;
        private DevExpress.XtraEditors.TileItem tiBOLabwiseDailyIncomeReport;
        private DevExpress.XtraEditors.TileItem tiCustomers;
    }
}