﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ReferralViewerDialog.cs 1352 2014-06-02 13:56:33Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraBars;
using DevExpress.XtraEditors;
using LabMaestro.BusinessLogic;
using LabMaestro.BusinessLogic.ReferralCalc;
using LabMaestro.Domain;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.Controls.Win
{
    public partial class ReferralViewerDialog : XtraForm
    {
        private ReferrerSortMode _currentSortMode;
        private ReferralCalculator _refCalculator;

        public ReferralViewerDialog()
        {
            InitializeComponent();
            WinUtils.ApplyTheme(this);
            WinUtils.SetFormTitle(this, "Referral Reports");
            _currentSortMode = ReferrerSortMode.Id;
            resetReferrerLabels();
            createGridColumns();
        }

        private string calcPercentage(decimal total, decimal divisor)
        {
            if (total == 0 || divisor == 0)
                return "0";

            var pct = divisor / total * 100m;
            return $"{pct:0.##}";
        }

        private void btnGenerateReport_ItemClick(object sender, ItemClickEventArgs e)
        {
            var proceed = false;
            using (var frm = new ReferralCriteriaDialog())
            {
                if (frm.ShowDialog(this) == DialogResult.OK)
                {
                    _refCalculator = new ReferralCalculator
                    {
                        StartDate = frm.DateFrom,
                        EndDate = frm.DateTo
                    };
                    if (!frm.AllReferrers)
                        if (frm.ReferrerCategoryId > 0)
                        {
                            _refCalculator.ReferrerCategory = frm.ReferrerCategoryId;
                        }
                        else if (frm.ReferrerId > 0)
                        {
                            _refCalculator.SetSingleReferrerId(frm.ReferrerId);
                        }
                        else
                        {
                            if (frm.ReferrerIdRangeStart > 0)
                                _refCalculator.ReferrerIdRangeBegin = frm.ReferrerIdRangeStart;
                            if (frm.ReferrerIdRangeEnd > 0)
                                _refCalculator.ReferrerIdRangeEnd = frm.ReferrerIdRangeEnd;
                        }

                    WaitFormControl.WaitOperation(this, () => _refCalculator.Execute());
                    proceed = _refCalculator.ReferrersList.FilterReferrersWithActiveReferrals().Count > 0;
                }
            }

            if (proceed)
                populateListBox();
        }

        private List<ReferrerInfo> getSortedReferrers()
        {
            switch (_currentSortMode)
            {
                case ReferrerSortMode.Name:
                    return _refCalculator.ReferrersList.FilterReferrersWithActiveReferrals()
                        .OrderBy(x => x.ReferrerName).ToList();
                case ReferrerSortMode.Business:
                    return _refCalculator.ReferrersList.FilterReferrersWithActiveReferrals()
                        .OrderByDescending(x => x.NetBillTotal).ToList();
                case ReferrerSortMode.Referral:
                    return _refCalculator.ReferrersList.FilterReferrersWithActiveReferrals()
                        .OrderByDescending(x => x.NetReferralTotal).ToList();
                case ReferrerSortMode.Invoices:
                    return _refCalculator.ReferrersList.FilterReferrersWithActiveReferrals()
                        .OrderByDescending(x => x.NumInvoices).ToList();
                default:
                    return _refCalculator.ReferrersList.FilterReferrersWithActiveReferrals()
                        .OrderBy(x => x.ReferrerId).ToList();
            }
        }

        private void populateListBox()
        {
            lbPhysicians.BeginUpdate();
            try
            {
                lbPhysicians.Items.Clear();
                foreach (var info in getSortedReferrers())
                    lbPhysicians.Items.Add(ReferrerListItem.Assemble(info));
            }
            finally
            {
                lbPhysicians.EndUpdate();
            }
        }

        private void btnClose_ItemClick(object sender, ItemClickEventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        private void updateReferrerLabels(ReferrerInfo referrer)
        {
            lblDateFrom.Text = SharedUtilities.DateToString(_refCalculator.StartDate);
            lblDateTill.Text = SharedUtilities.DateToString(_refCalculator.EndDate);
            lblReferrerId.Text = SharedUtilities.IntToStringPositive(referrer.ReferrerId);
            lblReferrerName.Text = referrer.ReferrerName;
            lblNumInvoices.Text = SharedUtilities.IntToStringPositive(referrer.NumInvoices);
            var ptPerDay = referrer.NumInvoices / (decimal) _refCalculator.DaysInPeriod;
            lblInvoicesPerDay.Text = ptPerDay.ToString("0.#");
            lblDiscount.Text = string.Format("{0} ({1}%)",
                SharedUtilities.MoneyToStringPlainCulture(referrer.DiscountTotal),
                calcPercentage(referrer.GrossBillTotal, referrer.DiscountTotal));
            lblGrossBusiness.Text = SharedUtilities.MoneyToStringPlainCulture(referrer.NetBillTotal);
            lblGrossReferral.Text = SharedUtilities.MoneyToStringPlainCulture(referrer.GrossReferralTotal);
            lblNetReferral.Text = string.Format("{0} ({1}%)",
                SharedUtilities.MoneyToStringPlainCulture(referrer.NetReferralTotal),
                calcPercentage(referrer.GrossBillTotal, referrer.NetReferralTotal));
            lblDue.Text = string.Format("{0} ({1}%)",
                SharedUtilities.MoneyToStringPlainCulture(referrer.DueTotal),
                calcPercentage(referrer.NetBillTotal, referrer.DueTotal));
            var count = referrer.Invoices.Count(x => x.DiscountAmount > 0);
            lblNumDiscounts.Text = string.Format("{0} ({1}%)",
                count,
                calcPercentage(referrer.NumInvoices, count));
        }

        private void resetReferrerLabels()
        {
            lblReferrerId.Text = string.Empty;
            lblReferrerName.Text = string.Empty;
            lblNumInvoices.Text = string.Empty;
            lblInvoicesPerDay.Text = string.Empty;
            lblDiscount.Text = string.Empty;
            lblGrossBusiness.Text = string.Empty;
            lblGrossReferral.Text = string.Empty;
            lblNetReferral.Text = string.Empty;
            lblDue.Text = string.Empty;
            lblNumDiscounts.Text = string.Empty;
            lblDateFrom.Text = string.Empty;
            lblDateTill.Text = string.Empty;
        }

        private void labelControl4_Click(object sender, EventArgs e)
        {
        }

        private void lbPhysicians_SelectedIndexChanged(object sender, EventArgs e)
        {
            var referrer = lbPhysicians.SelectedValue as ReferrerListItem;
            if (referrer != null && referrer.Info != null)
            {
                updateReferrerLabels(referrer.Info);
                gridPopulateRows(referrer.Info);
            }
        }

        private void btnSortId_Click(object sender, EventArgs e)
        {
            updateSortMode(ReferrerSortMode.Id);
        }

        private void updateSortMode(ReferrerSortMode sortMode)
        {
            _currentSortMode = sortMode;
            populateListBox();
        }

        private void btnSortNumInvoices_Click(object sender, EventArgs e)
        {
            updateSortMode(ReferrerSortMode.Invoices);
        }

        private void btnSortName_Click(object sender, EventArgs e)
        {
            updateSortMode(ReferrerSortMode.Name);
        }

        private void btnSortBusiness_Click(object sender, EventArgs e)
        {
            updateSortMode(ReferrerSortMode.Business);
        }

        private void btnSortReferral_Click(object sender, EventArgs e)
        {
            updateSortMode(ReferrerSortMode.Referral);
        }

        private void createGridColumns()
        {
            GridControl.BeginInit();
            try
            {
                GridControl.Columns.Clear();
                var i = 0;
                GridControl.GridAddColumn("id", "OID", i++, 70);
                GridControl.GridAddColumn("name", "Name", i++, 220);
                GridControl.GridAddColumn("date", "Date", i++, 80);
                GridControl.GridAddColumn("numTests", "# Tests", i++, 70);
                GridControl.GridAddColumn("grossBill", "Gross Bill", i++, 80);
                GridControl.GridAddColumn("disc", "Discount", i++, 70);
                GridControl.GridAddColumn("netBill", "Net Bill", i++, 80);
                GridControl.GridAddColumn("due", "Due", i++, 60);
                GridControl.GridAddColumn("grossRef", "Gross Ref.", i++, 80);
                GridControl.GridAddColumn("netRef", "Net Ref.", i++, 80);
            }
            finally
            {
                GridControl.EndInit();
            }
        }

        private void gridPopulateRows(ReferrerInfo info)
        {
            GridControl.BeginInit();
            try
            {
                GridControl.DataRows.Clear();
                foreach (var invoice in info.Invoices.OrderBy(x => x.InvoiceId))
                {
                    var row = GridControl.DataRows.AddNew();
                    row.Cells["id"].Value = invoice.OrderId;
                    row.Cells["name"].Value = invoice.PatientName;
                    row.Cells["date"].Value = SharedUtilities.DateToStringOnlyDot(invoice.OrderDateTime);
                    row.Cells["numTests"].Value =
                        SharedUtilities.IntToStringPositive(invoice.ReferralSubCategories.NumTestsTotal);
                    row.Cells["grossBill"].Value = SharedUtilities.MoneyToStringPlainCultureNonZero(invoice.GrossPayable);
                    row.Cells["netBill"].Value = SharedUtilities.MoneyToStringPlainCultureNonZero(invoice.NetPayable);
                    row.Cells["disc"].Value = SharedUtilities.MoneyToStringPlainCultureNonZero(invoice.DiscountAmount);
                    row.Cells["due"].Value = SharedUtilities.MoneyToStringPlainCultureNonZero(invoice.DueAmount);
                    row.Cells["grossRef"].Value = SharedUtilities.MoneyToStringPlainCultureNonZero(invoice.GrossReferral);
                    row.Cells["netRef"].Value = SharedUtilities.MoneyToStringPlainCultureNonZero(invoice.NetReferral);
                    row.Tag = invoice;
                    row.Height = 25;
                    row.EndEdit();
                }
            }
            finally
            {
                GridControl.EndInit();
                GridControl.Refresh();
            }
        }

        private void btnPrintAll_ItemClick(object sender, ItemClickEventArgs e)
        {
            if (_refCalculator == null) return;
            foreach (var info in getSortedReferrers())
                printReferralDetails(info, true);
        }

        private void btnPrintSelected_ItemClick(object sender, ItemClickEventArgs e)
        {
            var referrer = lbPhysicians.SelectedValue as ReferrerListItem;
            if (referrer != null && referrer.Info != null)
                printReferralDetails(referrer.Info, true);
        }

        private void printReferralDetails(ReferrerInfo refDetails, bool showPreview = false)
        {
            var dto = ReferralDetailPrintDto.AssembleFrom(refDetails);
            dto.DateFrom = SharedUtilities.DateToStringOnlySlash(_refCalculator.StartDate);
            dto.DateTo = SharedUtilities.DateToStringOnlySlash(_refCalculator.EndDate);
            PrintHelper.PrintMarketingDetailsReport(dto, showPreview);
        }

        private void btnPrintSummary_ItemClick(object sender, ItemClickEventArgs e)
        {
            if (_refCalculator == null) return;
            PrintHelper.PrintMarketingSummaryReport(_refCalculator.ToPrintDto());
        }

        private void btnPrintAllNoPreview_ItemClick(object sender, ItemClickEventArgs e)
        {
            if (_refCalculator == null) return;
            foreach (var info in getSortedReferrers())
                printReferralDetails(info, false);
        }

        private void btnUnknownReferrers_ItemClick(object sender, ItemClickEventArgs e)
        {
            var start = DateTime.Now;
            var end = start;

            using (var frm = new ReportDateRangeSelectorDialog())
            {
                //frm.SingleDate = true;
                frm.UpdateControls();
                if (frm.ShowDialog(this) == DialogResult.OK)
                {
                    start = frm.DateFrom;
                    end = frm.DateTo;
                }
            }

            var report = new UnknownReferrersReportBuilder {DateFrom = start, DateTo = end};
            WaitFormControl.WaitOperation(this, report.CompileReport);
            var dto = report.ToPrintDto();
            PrintHelper.PrintMarketingUnknownReferrers(dto);
        }

        private void btnExport_ItemClick(object sender, ItemClickEventArgs e)
        {
            if (string.IsNullOrEmpty(saveFileDialog.InitialDirectory))
                saveFileDialog.InitialDirectory = SharedUtilities.AssemblyDirectory;
            var exporter = new ReferralSummaryExporter(_refCalculator);
            saveFileDialog.FileName = $"Referral-{DateTime.Now:yy-MM-dd}.xlsx";
            if (saveFileDialog.ShowDialog(this) == DialogResult.OK)
                exporter.ExportExcel(saveFileDialog.FileName);
        }

        public class ReferrerListItem
        {
            public ReferrerListItem(ReferrerInfo info)
            {
                Info = info;
                Name = $"{info.ReferrerId} - {info.ReferrerName}";
            }

            public string Name { get; set; }

            internal ReferrerInfo Info { get; set; }

            public static ReferrerListItem Assemble(ReferrerInfo info)
            {
                return new ReferrerListItem(info);
            }

            public override string ToString()
            {
                return Name;
            }
        }

        private enum ReferrerSortMode
        {
            Id,
            Name,
            Invoices,
            Business,
            Referral
        }
    }
}