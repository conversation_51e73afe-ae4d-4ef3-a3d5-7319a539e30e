﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ReferralCriteriaDialog.cs 1104 2013-12-03 10:23:40Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Search;

namespace LabMaestro.Controls.Win;

public partial class ReferralCriteriaDialog : XtraForm
{
    private bool _suppressCheckedChangeEvent;
    private IEnumerable<CorporateClientSlice> _corporateClients = [];
    private IEnumerable<AssociateOrganizationSlice> _associateLabs = [];
    private IEnumerable<AffiliateSlice> _affiliates = [];

    public ReferralCriteriaDialog()
    {
        InitializeComponent();
        WinUtils.ApplyTheme(this);
        WinUtils.SetFormTitle(this, "Referrer Selection Criteria");
        resetControls();
        toggleRefControls(false);
        AllReferrers = false;
        _suppressCheckedChangeEvent = false;
    }

    public DateTime DateFrom { get; private set; }
    public DateTime DateTo { get; private set; }
    public int ReferrerId { get; private set; }
    public int ReferrerIdRangeStart { get; private set; }
    public int ReferrerIdRangeEnd { get; private set; }
    public short ReferrerCategoryId { get; private set; }
    public short AssociateLabId { get; private set; }
    public short CorporateClientId { get; private set; }
    public short AffiliateId { get; private set; }
    public bool AllReferrers { get; private set; }

    private void resetControls()
    {
        lblReferrer.Text = string.Empty;

        chkAllReferrers.Checked = false;
        chkRefCategory.Checked = false;
        chkRefIndividual.Checked = false;
        chkAssocLab.Checked = false;
        chkCorporate.Checked = false;
        chkAffiliate.Checked = false;

        luCategories.EditValue = null;

        cboAssocLabs.EditValue = null;
        cboAssocLabs.SelectedIndex = -1;

        cboCorporate.EditValue = null;
        cboCorporate.SelectedIndex = -1;

        cboAffiliate.EditValue = null;
        cboAffiliate.SelectedIndex = -1;
    }

    private void ReferralCriteriaDialog_Load(object sender, EventArgs e)
    {
        resetControls();
        dteFrom.DateTime = DateTime.Now;
        dteTo.DateTime = DateTime.Now;
        WaitFormControl.WaitOperation(
            this,
            () =>
            {
                referrerCategorySliceBindingSource.DataSource = ReferrerCategoryRepository.FetchAllSlices();
                _associateLabs = AssociateOrganizationRepository.FetchActive();
                _corporateClients = CorporateClientRepository.FetchActive();
                _affiliates = AffiliateRepository.FetchActive();
            });
        comboBoxSetItems(cboAssocLabs, _associateLabs.ToArray());
        comboBoxSetItems(cboCorporate, _corporateClients.ToArray());
        comboBoxSetItems(cboAffiliate, _affiliates.ToArray());
    }

    private void comboBoxSetItems(ComboBoxEdit combo, object[] items)
    {
        combo.Clear();
        combo.SelectedIndex = -1;
        combo.Properties.Items.Clear();

        if (!items.Any()) return;

        try {
            combo.Properties.Items.BeginUpdate();
            combo.Properties.Items.AddRange(items);
        }
        finally {
            combo.Properties.Items.EndUpdate();
        }
    }

    private void toggleRefControls(bool enable)
    {
        btnSelectReferrer.Enabled = enable;
        txtRefId.Enabled = enable;
        txtRefIdFrom.Enabled = enable;
        txtRefIdTo.Enabled = enable;
        luCategories.Enabled = enable;
        cboAssocLabs.Enabled = enable;
        cboCorporate.Enabled = enable;
        cboAffiliate.Enabled = enable;
    }

    private void uncheckAllBoxes()
    {
        chkAllReferrers.CheckState = CheckState.Unchecked;
        chkRefCategory.CheckState = CheckState.Unchecked;
        chkRefId.CheckState = CheckState.Unchecked;
        chkRefIdRange.CheckState = CheckState.Unchecked;
        chkRefIndividual.CheckState = CheckState.Unchecked;
        chkAssocLab.CheckState = CheckState.Unchecked;
        chkCorporate.CheckState = CheckState.Unchecked;
        chkAffiliate.CheckState = CheckState.Unchecked;
    }

    private void checkControls_CheckedChanged(object sender, EventArgs e)
    {
        if (_suppressCheckedChangeEvent) return;

        _suppressCheckedChangeEvent = true;

        toggleRefControls(false);
        if (sender == chkAllReferrers) {
            AllReferrers = true;
            uncheckAllBoxes();
            chkAllReferrers.Checked = true;
        }
        else if (sender == chkRefCategory) {
            luCategories.Enabled = true;
            uncheckAllBoxes();
            chkRefCategory.Checked = true;
        }
        else if (sender == chkRefId) {
            txtRefId.Enabled = true;
            txtRefId.Focus();
            uncheckAllBoxes();
            chkRefId.Checked = true;
        }
        else if (sender == chkRefIndividual) {
            btnSelectReferrer.Enabled = true;
            uncheckAllBoxes();
            chkRefIndividual.Checked = true;
        }
        else if (sender == chkRefIdRange) {
            txtRefIdFrom.Enabled = true;
            txtRefIdTo.Enabled = true;
            txtRefIdFrom.Focus();
            uncheckAllBoxes();
            chkRefIdRange.Checked = true;
        }
        else if (sender == chkAssocLab) {
            cboAssocLabs.Enabled = true;
            uncheckAllBoxes();
            chkAssocLab.Checked = true;
        }
        else if (sender == chkCorporate) {
            cboCorporate.Enabled = true;
            uncheckAllBoxes();
            chkCorporate.Checked = true;
        }
        else if (sender == chkAffiliate) {
            cboAffiliate.Enabled = true;
            uncheckAllBoxes();
            chkAffiliate.Checked = true;
        }

        _suppressCheckedChangeEvent = false;
    }

    private int textToInt(TextEdit ed)
    {
        int result;
        int.TryParse(ed.Text.Trim(), out result);
        return result;
    }

    private void btnOk_Click(object sender, EventArgs e)
    {
        DateFrom = dteFrom.DateTime;
        DateTo = dteTo.DateTime;

        if (DateFrom > DateTo) {
            MessageDlg.Warning("'Date From' cannot be later than 'Date To'!");
            return;
        }

        if (chkAllReferrers.Checked) {
            AllReferrers = true;
        }
        else if (chkRefCategory.Checked) {
            ReferrerCategoryId = 0;
            if (luCategories.EditValue != null) {
                ReferrerCategoryId = (short)luCategories.EditValue;
            }

            if (ReferrerCategoryId == 0) {
                MessageDlg.Warning("Please choose a referrer category.");
                return;
            }
        }
        else if (chkRefId.Checked) {
            ReferrerId = textToInt(txtRefId);
            if (ReferrerId == 0) {
                MessageDlg.Warning("Please enter a referrer id.");
                return;
            }
        }
        else if (chkRefIndividual.Checked) {
            //ReferrerId = textToInt(txtRefId);
            if (ReferrerId == 0) {
                MessageDlg.Warning("Please select a referrer.");
                return;
            }
        }
        else if (chkRefIdRange.Checked) {
            ReferrerIdRangeStart = textToInt(txtRefIdFrom);
            ReferrerIdRangeEnd = textToInt(txtRefIdTo);
            if (ReferrerIdRangeStart == 0 || ReferrerIdRangeEnd == 0) {
                MessageDlg.Warning("Please enter valid referrer id range.");
                return;
            }
        }
        else if (chkAssocLab.Checked) {
            AssociateLabId = 0;

            if (cboAssocLabs.SelectedItem is AssociateOrganizationSlice slice)
                AssociateLabId = slice.Id;

            if (AssociateLabId == 0) {
                MessageDlg.Warning("Please choose an associate laboratory.");
                return;
            }
        }
        else if (chkCorporate.Checked) {
            CorporateClientId = 0;

            if (cboCorporate.SelectedItem is CorporateClientSlice slice)
                CorporateClientId = slice.Id;

            if (CorporateClientId == 0) {
                MessageDlg.Warning("Please choose a corporate client.");
                return;
            }
        }
        else if (chkAffiliate.Checked) {
            AffiliateId = 0;

            if (cboAffiliate.SelectedItem is AffiliateSlice slice)
                AffiliateId = slice.Id;

            if (AffiliateId == 0) {
                MessageDlg.Warning("Please choose an affiliate.");
                return;
            }
        }
        else {
            MessageDlg.Warning("Please choose a referrer selection criteria.");
            return;
        }

        DialogResult = DialogResult.OK;
    }

    private void btnSelectReferrer_Click(object sender, EventArgs e)
    {
        ReferrerId = 0;
        var refId = ReferrerQuickSelectionDialog.ExecuteDialog(this, false, false, SearchEntityType.Physician);
        if (refId > 0) {
            ReferrerId = refId;
            lblReferrer.Text = ReferrersRepository.GetReferrerFullName(refId);
        }
    }

    private void cboAssocLabs_KeyUp(object sender, KeyEventArgs e)
    {
        if (e.KeyCode != Keys.F8) return;
        e.Handled = true;

        var selectedId = ReferrerQuickSelectionDialog.ExecuteDialog(this, false, false, SearchEntityType.AssociateLab);

        if (selectedId > 0) {
            AssociateLabId = (short)selectedId;

            foreach (var item in cboAssocLabs.Properties.Items)
                if (item is AssociateOrganizationSlice obj && obj.Id == selectedId) {
                    cboAssocLabs.SelectedItem = item;
                    break;
                }
        }
        else {
            AssociateLabId = 0;
        }
    }
}