﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: LabTestUtilizationReportDialog.cs 1336 2014-05-29 11:54:45Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using LabMaestro.BusinessLogic;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.Controls.Win
{
    public partial class LabTestUtilizationReportDialog : XtraForm
    {
        private readonly LabTestUtilizationReportCompiler _compiler;

        public LabTestUtilizationReportDialog()
        {
            _compiler = new LabTestUtilizationReportCompiler();
            InitializeComponent();
            WinUtils.ApplyTheme(this);
            initializeControls();
        }

        private void initializeControls()
        {
            _compiler.PopulateLabs();

            cboLabs.Enabled = false;
            var coll = cboLabs.Properties.Items;
            coll.BeginUpdate();
            foreach (var lab in _compiler.AllUtilizedLabs)
            {
                coll.Add(new ComboLabInfo(lab.Name, lab.Id));
            }
            coll.EndUpdate();
            rgSortOrder.SelectedIndex = 0;
            dteStart.DateTime = DateTime.Now;
            dteEnd.DateTime = DateTime.Now;
        }

        private void chkFilterLab_CheckedChanged(object sender, EventArgs e)
        {
            cboLabs.Enabled = !cboLabs.Enabled;
            cboLabs.SelectedIndex = -1;
        }

        private short getSelectedLabId()
        {
            if (cboLabs.SelectedIndex != -1)
            {
                var item = (ComboLabInfo) cboLabs.SelectedItem;
                return item.Id;
            }
            return -1;
        }

        private void btnExit_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        public static void ExecuteDialog(IWin32Window parent)
        {
            using (var frm = new LabTestUtilizationReportDialog())
            {
                frm.ShowDialog(parent);
            }
        }

        private void btnCompileReport_Click(object sender, EventArgs e)
        {
            resetCompiler();

            var dtStart = dteStart.DateTime;
            var dtEnd = dteEnd.DateTime;

            if (dtStart > dtEnd)
            {
                MessageDlg.Error("Start date must be less than End date!");
                return;
            }

            if (chkFilterLab.Checked)
            {
                if (cboLabs.SelectedIndex == -1)
                {
                    MessageDlg.Error("Please select a Lab!");
                    return;
                }

                _compiler.FilteredLabId = getSelectedLabId();
            }

            _compiler.ExcludeAuxLabs = !chkIncludeAuxLabs.Checked;
            _compiler.ExcludeEmptyLabs = chkRemoveUnutilizedLabs.Checked;

            switch (rgSortOrder.SelectedIndex)
            {
                case 1:
                    // by name
                    _compiler.SortType = UtilizationSortType.Name;
                    break;
                case 2:
                    // by value
                    _compiler.SortType = UtilizationSortType.Value;
                    break;
                default:
                    // by volume
                    _compiler.SortType = UtilizationSortType.Volume;
                    break;
            }

            // re-populate labs
            _compiler.PopulateLabs();

            _compiler.DateStart = dtStart;
            _compiler.DateEnd = dtEnd;

            WaitFormControl.WaitOperation(this,
                () => _compiler.CompileReport(),
                "Compiling report...");
            _compiler.UpdateSortOrder();

            var dto = _compiler.ToPrintDto();
            dto.PrintDate = SharedUtilities.DateTimeToShortString(AppSysRepository.GetServerTime());
            dto.UserName = CurrentUserContext.UserDisplayName;
            PrintHelper.PrintLabTestUtilizationReport(dto);
        }

        private void resetCompiler()
        {
            _compiler.FilteredLabId = -1;
            _compiler.ExcludeAuxLabs = false;
            _compiler.ExcludeEmptyLabs = false;
        }

        public sealed class ComboLabInfo
        {
            public ComboLabInfo(string name, short id)
            {
                Name = name;
                Id = id;
            }

            public string Name { get; private set; }
            public short Id { get; private set; }

            public override string ToString()
            {
                return Name;
            }
        }
    }
}