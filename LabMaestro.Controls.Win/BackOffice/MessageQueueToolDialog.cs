﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id$
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using LabMaestro.BusinessLogic;
using LabMaestro.Shared;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace LabMaestro.Controls.Win;

public partial class MessageQueueToolDialog : XtraForm
{
    public MessageQueueToolDialog()
    {
        InitializeComponent();
        WinUtils.ApplyTheme(this);
    }

    private void btnHeartbeat_Click(object sender, EventArgs e)
    {
        memLog.Text += ">> Sending heartbeat request to service bus...\r\n";
        string resp = string.Empty;
        WaitFormControl.WaitOperation(this,
                                      () => resp = ServiceBusDiagnosticUtilities.SendHeartBeatRequest(),
                                      "Wait...");
        logReply(resp, true);
    }

    private void logReply(string response, bool isHeartbeat)
    {
        var sb = new StringBuilder();
        sb.AppendLine("<< Received response from job server:\r\n");
        //var json = JValue.Parse(response).ToString(Formatting.Indented);
        var json = JsonConvert.SerializeObject(response, Formatting.Indented);
        sb.AppendLine(json);
        memLog.Text += sb;
    }


    private void logReply(ServiceBusResponse resp, bool isHeartbeat)
    {
        memLog.Text += "<< Received response from bus:\r\n";
        var sb = new StringBuilder();
        sb.AppendFormat("\t CorrelationId: {0}\r\n", resp.CorrelationId);
        if (isHeartbeat) {
            sb.AppendFormat("\t ErrorCount: {0}\r\n", resp.ErrorCount);
            sb.AppendFormat("\t MessagesSent: {0}\r\n", resp.MessagesSent);
            sb.AppendFormat("\t MessagesReceived: {0}\r\n", resp.MessagesReceived);
            sb.AppendFormat("\t RunningSince: {0}\r\n", SharedUtilities.DateTimeToString24(resp.RunningSince));
        }
        else {
            sb.AppendFormat("\t ServerTime: {0}\r\n", SharedUtilities.DateTimeToString24(resp.ServerTime));
        }

        memLog.Text += sb;
    }

    private void btnCheckDbConn_Click(object sender, EventArgs e)
    {
        memLog.Text += ">> Sending heartbeat request to service bus...\r\n";
        ServiceBusResponse resp = null;
        WaitFormControl.WaitOperation(this,
                                      () => resp = ServiceBusDiagnosticUtilities.SendDatabaseConnectivityCheckRequest(),
                                      "Wait...");
        logReply(resp, false);
    }

    public static void ExecuteDialog(IWin32Window parent)
    {
        using (var frm = new MessageQueueToolDialog()) {
            frm.ShowDialog(parent);
        }
    }
}