﻿namespace LabMaestro.Controls.Win
{
    partial class UsersShiftHistoryDialog
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject1 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject3 = new DevExpress.Utils.SerializableAppearanceObject();
            this.gridControl = new DevExpress.XtraGrid.GridControl();
            this.workshiftSearchResultSliceBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.gridView = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colUserName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colRoleName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colIsClosed = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colStartDate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colStartTime = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colEndDate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colEndTime = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colNumOrders = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colAdditionalBalance = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colReceiveAmount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colDiscountAmount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colDiscountRebateAmount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colRefundAmount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colFinalBalance = new DevExpress.XtraGrid.Columns.GridColumn();
            this.barManager = new DevExpress.XtraBars.BarManager(this.components);
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.barButtonItem1 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem2 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem3 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem4 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem5 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem6 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem7 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem8 = new DevExpress.XtraBars.BarButtonItem();
            this.dteFrom = new DevExpress.XtraEditors.DateEdit();
            this.dteTo = new DevExpress.XtraEditors.DateEdit();
            this.clbRoles = new DevExpress.XtraEditors.CheckedListBoxControl();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.btnSearch = new DevExpress.XtraEditors.SimpleButton();
            this.btnClear = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.btnRolesClearAll = new DevExpress.XtraEditors.SimpleButton();
            this.btnRolesCheckAll = new DevExpress.XtraEditors.SimpleButton();
            this.popReports = new DevExpress.XtraBars.PopupMenu(this.components);
            this.btnPrint = new DevExpress.XtraEditors.DropDownButton();
            this.dropDownButton1 = new DevExpress.XtraEditors.DropDownButton();
            this.popDateRange = new DevExpress.XtraBars.PopupMenu(this.components);
            this.miRepReceivablesSummary = new DevExpress.XtraBars.BarButtonItem();
            this.miRepUserCredit = new DevExpress.XtraBars.BarButtonItem();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.workshiftSearchResultSliceBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dteFrom.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dteFrom.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dteTo.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dteTo.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.clbRoles)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.popReports)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.popDateRange)).BeginInit();
            this.SuspendLayout();
            // 
            // gridControl
            // 
            this.gridControl.DataSource = this.workshiftSearchResultSliceBindingSource;
            this.gridControl.Location = new System.Drawing.Point(12, 76);
            this.gridControl.MainView = this.gridView;
            this.gridControl.MenuManager = this.barManager;
            this.gridControl.Name = "gridControl";
            this.gridControl.Size = new System.Drawing.Size(913, 439);
            this.gridControl.TabIndex = 0;
            this.gridControl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView});
            // 
            // workshiftSearchResultSliceBindingSource
            // 
            this.workshiftSearchResultSliceBindingSource.DataSource = typeof(LabMaestro.Domain.WorkshiftSearchResultSlice);
            // 
            // gridView
            // 
            this.gridView.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colId,
            this.colUserName,
            this.colRoleName,
            this.colIsClosed,
            this.colStartDate,
            this.colStartTime,
            this.colEndDate,
            this.colEndTime,
            this.colNumOrders,
            this.colAdditionalBalance,
            this.colReceiveAmount,
            this.colDiscountAmount,
            this.colDiscountRebateAmount,
            this.colRefundAmount,
            this.colFinalBalance});
            this.gridView.GridControl = this.gridControl;
            this.gridView.Name = "gridView";
            this.gridView.OptionsView.EnableAppearanceEvenRow = true;
            this.gridView.PaintStyleName = "Skin";
            this.gridView.PopupMenuShowing += new DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventHandler(this.gridView_PopupMenuShowing);
            this.gridView.CustomColumnDisplayText += new DevExpress.XtraGrid.Views.Base.CustomColumnDisplayTextEventHandler(this.gridView_CustomColumnDisplayText);
            // 
            // colId
            // 
            this.colId.Caption = "Shift #";
            this.colId.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colId.FieldName = "Id";
            this.colId.Name = "colId";
            this.colId.OptionsColumn.AllowEdit = false;
            this.colId.OptionsColumn.AllowFocus = false;
            this.colId.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colId.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colId.OptionsColumn.FixedWidth = true;
            this.colId.OptionsColumn.ReadOnly = true;
            this.colId.OptionsColumn.ShowInCustomizationForm = false;
            this.colId.OptionsColumn.ShowInExpressionEditor = false;
            this.colId.OptionsColumn.TabStop = false;
            this.colId.OptionsFilter.AllowAutoFilter = false;
            this.colId.OptionsFilter.AllowFilter = false;
            this.colId.OptionsFilter.AllowFilterModeChanging = DevExpress.Utils.DefaultBoolean.False;
            this.colId.Visible = true;
            this.colId.VisibleIndex = 0;
            this.colId.Width = 50;
            // 
            // colUserName
            // 
            this.colUserName.Caption = "Staff";
            this.colUserName.FieldName = "UserName";
            this.colUserName.Name = "colUserName";
            this.colUserName.OptionsColumn.AllowEdit = false;
            this.colUserName.OptionsColumn.AllowFocus = false;
            this.colUserName.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colUserName.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colUserName.OptionsColumn.ReadOnly = true;
            this.colUserName.OptionsColumn.ShowInCustomizationForm = false;
            this.colUserName.OptionsColumn.ShowInExpressionEditor = false;
            this.colUserName.OptionsColumn.TabStop = false;
            this.colUserName.OptionsFilter.AllowAutoFilter = false;
            this.colUserName.OptionsFilter.AllowFilter = false;
            this.colUserName.OptionsFilter.AllowFilterModeChanging = DevExpress.Utils.DefaultBoolean.False;
            this.colUserName.Visible = true;
            this.colUserName.VisibleIndex = 1;
            this.colUserName.Width = 141;
            // 
            // colRoleName
            // 
            this.colRoleName.Caption = "Role";
            this.colRoleName.FieldName = "RoleName";
            this.colRoleName.Name = "colRoleName";
            this.colRoleName.OptionsColumn.AllowEdit = false;
            this.colRoleName.OptionsColumn.AllowFocus = false;
            this.colRoleName.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.True;
            this.colRoleName.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colRoleName.OptionsColumn.ReadOnly = true;
            this.colRoleName.OptionsColumn.ShowInCustomizationForm = false;
            this.colRoleName.OptionsColumn.ShowInExpressionEditor = false;
            this.colRoleName.OptionsColumn.TabStop = false;
            this.colRoleName.OptionsFilter.AllowAutoFilter = false;
            this.colRoleName.OptionsFilter.AllowFilter = false;
            this.colRoleName.OptionsFilter.AllowFilterModeChanging = DevExpress.Utils.DefaultBoolean.False;
            this.colRoleName.Visible = true;
            this.colRoleName.VisibleIndex = 2;
            this.colRoleName.Width = 78;
            // 
            // colIsClosed
            // 
            this.colIsClosed.Caption = "X?";
            this.colIsClosed.FieldName = "IsClosed";
            this.colIsClosed.Image = global::LabMaestro.Controls.Win.Resources.green_yes;
            this.colIsClosed.ImageAlignment = System.Drawing.StringAlignment.Center;
            this.colIsClosed.Name = "colIsClosed";
            this.colIsClosed.OptionsColumn.AllowEdit = false;
            this.colIsClosed.OptionsColumn.AllowFocus = false;
            this.colIsClosed.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.True;
            this.colIsClosed.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colIsClosed.OptionsColumn.FixedWidth = true;
            this.colIsClosed.OptionsColumn.ReadOnly = true;
            this.colIsClosed.OptionsColumn.ShowCaption = false;
            this.colIsClosed.OptionsColumn.ShowInCustomizationForm = false;
            this.colIsClosed.OptionsColumn.ShowInExpressionEditor = false;
            this.colIsClosed.OptionsColumn.TabStop = false;
            this.colIsClosed.OptionsFilter.AllowAutoFilter = false;
            this.colIsClosed.OptionsFilter.AllowFilter = false;
            this.colIsClosed.OptionsFilter.AllowFilterModeChanging = DevExpress.Utils.DefaultBoolean.False;
            this.colIsClosed.ToolTip = "Shift Is Closed?";
            this.colIsClosed.Visible = true;
            this.colIsClosed.VisibleIndex = 3;
            this.colIsClosed.Width = 30;
            // 
            // colStartDate
            // 
            this.colStartDate.Caption = "Started";
            this.colStartDate.DisplayFormat.FormatString = "dd/MM/yy";
            this.colStartDate.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.colStartDate.FieldName = "StartTime";
            this.colStartDate.Name = "colStartDate";
            this.colStartDate.OptionsColumn.AllowEdit = false;
            this.colStartDate.OptionsColumn.AllowFocus = false;
            this.colStartDate.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.True;
            this.colStartDate.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colStartDate.OptionsColumn.FixedWidth = true;
            this.colStartDate.OptionsColumn.ReadOnly = true;
            this.colStartDate.OptionsColumn.ShowInCustomizationForm = false;
            this.colStartDate.OptionsColumn.ShowInExpressionEditor = false;
            this.colStartDate.OptionsColumn.TabStop = false;
            this.colStartDate.OptionsFilter.AllowAutoFilter = false;
            this.colStartDate.OptionsFilter.AllowFilter = false;
            this.colStartDate.OptionsFilter.AllowFilterModeChanging = DevExpress.Utils.DefaultBoolean.False;
            this.colStartDate.Visible = true;
            this.colStartDate.VisibleIndex = 4;
            this.colStartDate.Width = 56;
            // 
            // colStartTime
            // 
            this.colStartTime.Caption = "At";
            this.colStartTime.DisplayFormat.FormatString = "HH:mm";
            this.colStartTime.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.colStartTime.FieldName = "StartTime";
            this.colStartTime.Name = "colStartTime";
            this.colStartTime.OptionsColumn.AllowEdit = false;
            this.colStartTime.OptionsColumn.AllowFocus = false;
            this.colStartTime.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colStartTime.OptionsColumn.FixedWidth = true;
            this.colStartTime.OptionsColumn.ReadOnly = true;
            this.colStartTime.OptionsFilter.AllowAutoFilter = false;
            this.colStartTime.OptionsFilter.AllowFilter = false;
            this.colStartTime.OptionsFilter.AllowFilterModeChanging = DevExpress.Utils.DefaultBoolean.False;
            this.colStartTime.Visible = true;
            this.colStartTime.VisibleIndex = 5;
            this.colStartTime.Width = 39;
            // 
            // colEndDate
            // 
            this.colEndDate.Caption = "Ended";
            this.colEndDate.DisplayFormat.FormatString = "dd/MM/yy";
            this.colEndDate.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.colEndDate.FieldName = "EndTime";
            this.colEndDate.Name = "colEndDate";
            this.colEndDate.OptionsColumn.AllowEdit = false;
            this.colEndDate.OptionsColumn.AllowFocus = false;
            this.colEndDate.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.True;
            this.colEndDate.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colEndDate.OptionsColumn.FixedWidth = true;
            this.colEndDate.OptionsColumn.ReadOnly = true;
            this.colEndDate.OptionsColumn.ShowInCustomizationForm = false;
            this.colEndDate.OptionsColumn.ShowInExpressionEditor = false;
            this.colEndDate.OptionsColumn.TabStop = false;
            this.colEndDate.OptionsFilter.AllowAutoFilter = false;
            this.colEndDate.OptionsFilter.AllowFilter = false;
            this.colEndDate.OptionsFilter.AllowFilterModeChanging = DevExpress.Utils.DefaultBoolean.False;
            this.colEndDate.Visible = true;
            this.colEndDate.VisibleIndex = 6;
            this.colEndDate.Width = 56;
            // 
            // colEndTime
            // 
            this.colEndTime.Caption = "At";
            this.colEndTime.DisplayFormat.FormatString = "HH:mm";
            this.colEndTime.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.colEndTime.FieldName = "EndTime";
            this.colEndTime.Name = "colEndTime";
            this.colEndTime.OptionsColumn.AllowEdit = false;
            this.colEndTime.OptionsColumn.AllowFocus = false;
            this.colEndTime.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colEndTime.OptionsColumn.FixedWidth = true;
            this.colEndTime.OptionsColumn.ReadOnly = true;
            this.colEndTime.OptionsFilter.AllowAutoFilter = false;
            this.colEndTime.OptionsFilter.AllowFilter = false;
            this.colEndTime.OptionsFilter.AllowFilterModeChanging = DevExpress.Utils.DefaultBoolean.False;
            this.colEndTime.Visible = true;
            this.colEndTime.VisibleIndex = 7;
            this.colEndTime.Width = 36;
            // 
            // colNumOrders
            // 
            this.colNumOrders.Caption = "Orders";
            this.colNumOrders.FieldName = "NumOrders";
            this.colNumOrders.Image = global::LabMaestro.Controls.Win.Resources.Counter;
            this.colNumOrders.Name = "colNumOrders";
            this.colNumOrders.OptionsColumn.AllowEdit = false;
            this.colNumOrders.OptionsColumn.AllowFocus = false;
            this.colNumOrders.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colNumOrders.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colNumOrders.OptionsColumn.ReadOnly = true;
            this.colNumOrders.OptionsColumn.ShowInCustomizationForm = false;
            this.colNumOrders.OptionsColumn.ShowInExpressionEditor = false;
            this.colNumOrders.OptionsColumn.TabStop = false;
            this.colNumOrders.OptionsFilter.AllowAutoFilter = false;
            this.colNumOrders.OptionsFilter.AllowFilter = false;
            this.colNumOrders.OptionsFilter.AllowFilterModeChanging = DevExpress.Utils.DefaultBoolean.False;
            this.colNumOrders.ToolTip = "Number of Invoices";
            this.colNumOrders.Visible = true;
            this.colNumOrders.VisibleIndex = 8;
            this.colNumOrders.Width = 60;
            // 
            // colAdditionalBalance
            // 
            this.colAdditionalBalance.Caption = "Addl. Bal.";
            this.colAdditionalBalance.DisplayFormat.FormatString = "0,0";
            this.colAdditionalBalance.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colAdditionalBalance.FieldName = "AdditionalBalance";
            this.colAdditionalBalance.Name = "colAdditionalBalance";
            this.colAdditionalBalance.OptionsColumn.AllowEdit = false;
            this.colAdditionalBalance.OptionsColumn.AllowFocus = false;
            this.colAdditionalBalance.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colAdditionalBalance.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colAdditionalBalance.OptionsColumn.ReadOnly = true;
            this.colAdditionalBalance.OptionsColumn.ShowInCustomizationForm = false;
            this.colAdditionalBalance.OptionsColumn.ShowInExpressionEditor = false;
            this.colAdditionalBalance.OptionsColumn.TabStop = false;
            this.colAdditionalBalance.OptionsFilter.AllowAutoFilter = false;
            this.colAdditionalBalance.OptionsFilter.AllowFilter = false;
            this.colAdditionalBalance.OptionsFilter.AllowFilterModeChanging = DevExpress.Utils.DefaultBoolean.False;
            this.colAdditionalBalance.ToolTip = "Additional Balance";
            this.colAdditionalBalance.Visible = true;
            this.colAdditionalBalance.VisibleIndex = 9;
            this.colAdditionalBalance.Width = 55;
            // 
            // colReceiveAmount
            // 
            this.colReceiveAmount.Caption = "Received";
            this.colReceiveAmount.DisplayFormat.FormatString = "0,0";
            this.colReceiveAmount.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colReceiveAmount.FieldName = "ReceiveAmount";
            this.colReceiveAmount.Name = "colReceiveAmount";
            this.colReceiveAmount.OptionsColumn.AllowEdit = false;
            this.colReceiveAmount.OptionsColumn.AllowFocus = false;
            this.colReceiveAmount.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colReceiveAmount.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colReceiveAmount.OptionsColumn.ReadOnly = true;
            this.colReceiveAmount.OptionsColumn.ShowInCustomizationForm = false;
            this.colReceiveAmount.OptionsColumn.ShowInExpressionEditor = false;
            this.colReceiveAmount.OptionsColumn.TabStop = false;
            this.colReceiveAmount.OptionsFilter.AllowAutoFilter = false;
            this.colReceiveAmount.OptionsFilter.AllowFilter = false;
            this.colReceiveAmount.OptionsFilter.AllowFilterModeChanging = DevExpress.Utils.DefaultBoolean.False;
            this.colReceiveAmount.Visible = true;
            this.colReceiveAmount.VisibleIndex = 10;
            this.colReceiveAmount.Width = 55;
            // 
            // colDiscountAmount
            // 
            this.colDiscountAmount.Caption = "Discount";
            this.colDiscountAmount.DisplayFormat.FormatString = "0,0";
            this.colDiscountAmount.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colDiscountAmount.FieldName = "DiscountAmount";
            this.colDiscountAmount.Name = "colDiscountAmount";
            this.colDiscountAmount.OptionsColumn.AllowEdit = false;
            this.colDiscountAmount.OptionsColumn.AllowFocus = false;
            this.colDiscountAmount.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colDiscountAmount.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colDiscountAmount.OptionsColumn.ReadOnly = true;
            this.colDiscountAmount.OptionsColumn.ShowInCustomizationForm = false;
            this.colDiscountAmount.OptionsColumn.ShowInExpressionEditor = false;
            this.colDiscountAmount.OptionsColumn.TabStop = false;
            this.colDiscountAmount.OptionsFilter.AllowAutoFilter = false;
            this.colDiscountAmount.OptionsFilter.AllowFilter = false;
            this.colDiscountAmount.OptionsFilter.AllowFilterModeChanging = DevExpress.Utils.DefaultBoolean.False;
            this.colDiscountAmount.Visible = true;
            this.colDiscountAmount.VisibleIndex = 11;
            this.colDiscountAmount.Width = 55;
            // 
            // colDiscountRebateAmount
            // 
            this.colDiscountRebateAmount.Caption = "Rebate";
            this.colDiscountRebateAmount.DisplayFormat.FormatString = "0,0";
            this.colDiscountRebateAmount.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colDiscountRebateAmount.FieldName = "DiscountRebateAmount";
            this.colDiscountRebateAmount.Name = "colDiscountRebateAmount";
            this.colDiscountRebateAmount.OptionsColumn.AllowEdit = false;
            this.colDiscountRebateAmount.OptionsColumn.AllowFocus = false;
            this.colDiscountRebateAmount.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colDiscountRebateAmount.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colDiscountRebateAmount.OptionsColumn.ReadOnly = true;
            this.colDiscountRebateAmount.OptionsColumn.ShowInCustomizationForm = false;
            this.colDiscountRebateAmount.OptionsColumn.ShowInExpressionEditor = false;
            this.colDiscountRebateAmount.OptionsColumn.TabStop = false;
            this.colDiscountRebateAmount.OptionsFilter.AllowAutoFilter = false;
            this.colDiscountRebateAmount.OptionsFilter.AllowFilter = false;
            this.colDiscountRebateAmount.OptionsFilter.AllowFilterModeChanging = DevExpress.Utils.DefaultBoolean.False;
            this.colDiscountRebateAmount.Visible = true;
            this.colDiscountRebateAmount.VisibleIndex = 12;
            this.colDiscountRebateAmount.Width = 55;
            // 
            // colRefundAmount
            // 
            this.colRefundAmount.Caption = "Refund";
            this.colRefundAmount.DisplayFormat.FormatString = "0,0";
            this.colRefundAmount.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colRefundAmount.FieldName = "RefundAmount";
            this.colRefundAmount.Name = "colRefundAmount";
            this.colRefundAmount.OptionsColumn.AllowEdit = false;
            this.colRefundAmount.OptionsColumn.AllowFocus = false;
            this.colRefundAmount.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colRefundAmount.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colRefundAmount.OptionsColumn.ReadOnly = true;
            this.colRefundAmount.OptionsColumn.ShowInCustomizationForm = false;
            this.colRefundAmount.OptionsColumn.ShowInExpressionEditor = false;
            this.colRefundAmount.OptionsColumn.TabStop = false;
            this.colRefundAmount.OptionsFilter.AllowAutoFilter = false;
            this.colRefundAmount.OptionsFilter.AllowFilter = false;
            this.colRefundAmount.OptionsFilter.AllowFilterModeChanging = DevExpress.Utils.DefaultBoolean.False;
            this.colRefundAmount.Visible = true;
            this.colRefundAmount.VisibleIndex = 13;
            this.colRefundAmount.Width = 55;
            // 
            // colFinalBalance
            // 
            this.colFinalBalance.Caption = "Total";
            this.colFinalBalance.DisplayFormat.FormatString = "0,0";
            this.colFinalBalance.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colFinalBalance.FieldName = "FinalBalance";
            this.colFinalBalance.Image = global::LabMaestro.Controls.Win.Resources.coins;
            this.colFinalBalance.ImageAlignment = System.Drawing.StringAlignment.Far;
            this.colFinalBalance.Name = "colFinalBalance";
            this.colFinalBalance.OptionsColumn.AllowEdit = false;
            this.colFinalBalance.OptionsColumn.AllowFocus = false;
            this.colFinalBalance.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colFinalBalance.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colFinalBalance.OptionsColumn.ReadOnly = true;
            this.colFinalBalance.OptionsColumn.ShowInCustomizationForm = false;
            this.colFinalBalance.OptionsColumn.ShowInExpressionEditor = false;
            this.colFinalBalance.OptionsColumn.TabStop = false;
            this.colFinalBalance.OptionsFilter.AllowAutoFilter = false;
            this.colFinalBalance.OptionsFilter.AllowFilter = false;
            this.colFinalBalance.OptionsFilter.AllowFilterModeChanging = DevExpress.Utils.DefaultBoolean.False;
            this.colFinalBalance.Visible = true;
            this.colFinalBalance.VisibleIndex = 14;
            this.colFinalBalance.Width = 74;
            // 
            // barManager
            // 
            this.barManager.DockControls.Add(this.barDockControlTop);
            this.barManager.DockControls.Add(this.barDockControlBottom);
            this.barManager.DockControls.Add(this.barDockControlLeft);
            this.barManager.DockControls.Add(this.barDockControlRight);
            this.barManager.Form = this;
            this.barManager.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barButtonItem1,
            this.barButtonItem2,
            this.barButtonItem3,
            this.barButtonItem4,
            this.barButtonItem5,
            this.barButtonItem6,
            this.barButtonItem7,
            this.barButtonItem8,
            this.miRepReceivablesSummary,
            this.miRepUserCredit});
            this.barManager.MaxItemId = 11;
            // 
            // barDockControlTop
            // 
            this.barDockControlTop.CausesValidation = false;
            this.barDockControlTop.Dock = System.Windows.Forms.DockStyle.Top;
            this.barDockControlTop.Location = new System.Drawing.Point(0, 0);
            this.barDockControlTop.Size = new System.Drawing.Size(933, 0);
            // 
            // barDockControlBottom
            // 
            this.barDockControlBottom.CausesValidation = false;
            this.barDockControlBottom.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.barDockControlBottom.Location = new System.Drawing.Point(0, 527);
            this.barDockControlBottom.Size = new System.Drawing.Size(933, 0);
            // 
            // barDockControlLeft
            // 
            this.barDockControlLeft.CausesValidation = false;
            this.barDockControlLeft.Dock = System.Windows.Forms.DockStyle.Left;
            this.barDockControlLeft.Location = new System.Drawing.Point(0, 0);
            this.barDockControlLeft.Size = new System.Drawing.Size(0, 527);
            // 
            // barDockControlRight
            // 
            this.barDockControlRight.CausesValidation = false;
            this.barDockControlRight.Dock = System.Windows.Forms.DockStyle.Right;
            this.barDockControlRight.Location = new System.Drawing.Point(933, 0);
            this.barDockControlRight.Size = new System.Drawing.Size(0, 527);
            // 
            // barButtonItem1
            // 
            this.barButtonItem1.Caption = "Reconcile Shift";
            this.barButtonItem1.Id = 0;
            this.barButtonItem1.Name = "barButtonItem1";
            // 
            // barButtonItem2
            // 
            this.barButtonItem2.Caption = "Shift Details";
            this.barButtonItem2.Id = 1;
            this.barButtonItem2.Name = "barButtonItem2";
            // 
            // barButtonItem3
            // 
            this.barButtonItem3.Caption = "Yesterday";
            this.barButtonItem3.Id = 2;
            this.barButtonItem3.Name = "barButtonItem3";
            this.barButtonItem3.Tag = 1;
            this.barButtonItem3.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.popQuickDateRange_Click);
            // 
            // barButtonItem4
            // 
            this.barButtonItem4.Caption = "Last 3 days";
            this.barButtonItem4.Id = 3;
            this.barButtonItem4.Name = "barButtonItem4";
            this.barButtonItem4.Tag = 2;
            this.barButtonItem4.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.popQuickDateRange_Click);
            // 
            // barButtonItem5
            // 
            this.barButtonItem5.Caption = "Last 1 week";
            this.barButtonItem5.Id = 4;
            this.barButtonItem5.Name = "barButtonItem5";
            this.barButtonItem5.Tag = 3;
            this.barButtonItem5.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.popQuickDateRange_Click);
            // 
            // barButtonItem6
            // 
            this.barButtonItem6.Caption = "Last 2 weeks";
            this.barButtonItem6.Id = 5;
            this.barButtonItem6.Name = "barButtonItem6";
            this.barButtonItem6.Tag = 4;
            this.barButtonItem6.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.popQuickDateRange_Click);
            // 
            // barButtonItem7
            // 
            this.barButtonItem7.Caption = "Last 1 month";
            this.barButtonItem7.Id = 6;
            this.barButtonItem7.Name = "barButtonItem7";
            this.barButtonItem7.Tag = 5;
            this.barButtonItem7.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.popQuickDateRange_Click);
            // 
            // barButtonItem8
            // 
            this.barButtonItem8.Caption = "Last 3 months";
            this.barButtonItem8.Id = 7;
            this.barButtonItem8.Name = "barButtonItem8";
            this.barButtonItem8.Tag = 6;
            this.barButtonItem8.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.popQuickDateRange_Click);
            // 
            // dteFrom
            // 
            this.dteFrom.EditValue = null;
            this.dteFrom.Location = new System.Drawing.Point(83, 12);
            this.dteFrom.Name = "dteFrom";
            this.dteFrom.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.dteFrom.Properties.Appearance.Options.UseFont = true;
            this.dteFrom.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph, "", -1, true, true, false, DevExpress.XtraEditors.ImageLocation.MiddleCenter, global::LabMaestro.Controls.Win.Resources.calendar, new DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.None), serializableAppearanceObject1, "", null, null, true)});
            this.dteFrom.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.dteFrom.Properties.CalendarTimeProperties.CloseUpKey = new DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.F4);
            this.dteFrom.Properties.CalendarTimeProperties.PopupBorderStyle = DevExpress.XtraEditors.Controls.PopupBorderStyles.Default;
            this.dteFrom.Size = new System.Drawing.Size(125, 20);
            this.dteFrom.TabIndex = 1;
            // 
            // dteTo
            // 
            this.dteTo.EditValue = null;
            this.dteTo.Location = new System.Drawing.Point(277, 12);
            this.dteTo.Name = "dteTo";
            this.dteTo.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.dteTo.Properties.Appearance.Options.UseFont = true;
            this.dteTo.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph, "", -1, true, true, false, DevExpress.XtraEditors.ImageLocation.MiddleCenter, global::LabMaestro.Controls.Win.Resources.calendar, new DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.None), serializableAppearanceObject3, "", null, null, true)});
            this.dteTo.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.dteTo.Properties.CalendarTimeProperties.CloseUpKey = new DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.F4);
            this.dteTo.Properties.CalendarTimeProperties.PopupBorderStyle = DevExpress.XtraEditors.Controls.PopupBorderStyles.Default;
            this.dteTo.Size = new System.Drawing.Size(125, 20);
            this.dteTo.TabIndex = 2;
            // 
            // clbRoles
            // 
            this.clbRoles.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.clbRoles.Appearance.Options.UseFont = true;
            this.clbRoles.CheckOnClick = true;
            this.clbRoles.Items.AddRange(new DevExpress.XtraEditors.Controls.CheckedListBoxItem[] {
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem(((byte)(10)), "Receptionist"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem(((byte)(20)), "Process Receivables"),
            new DevExpress.XtraEditors.Controls.CheckedListBoxItem(((byte)(30)), "Supervisors")});
            this.clbRoles.Location = new System.Drawing.Point(743, 11);
            this.clbRoles.Name = "clbRoles";
            this.clbRoles.Size = new System.Drawing.Size(153, 59);
            this.clbRoles.TabIndex = 3;
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Location = new System.Drawing.Point(12, 12);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(65, 16);
            this.labelControl1.TabIndex = 4;
            this.labelControl1.Text = "Date From:";
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Location = new System.Drawing.Point(221, 12);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(50, 16);
            this.labelControl2.TabIndex = 5;
            this.labelControl2.Text = "Date To:";
            // 
            // btnSearch
            // 
            this.btnSearch.Appearance.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnSearch.Appearance.Options.UseFont = true;
            this.btnSearch.Image = global::LabMaestro.Controls.Win.ResourceIcons.search_32;
            this.btnSearch.Location = new System.Drawing.Point(601, 34);
            this.btnSearch.Name = "btnSearch";
            this.btnSearch.Size = new System.Drawing.Size(136, 36);
            this.btnSearch.TabIndex = 6;
            this.btnSearch.Text = "Search";
            this.btnSearch.Click += new System.EventHandler(this.btnSearch_Click);
            // 
            // btnClear
            // 
            this.btnClear.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnClear.Appearance.Options.UseFont = true;
            this.btnClear.Image = global::LabMaestro.Controls.Win.Resources.clear;
            this.btnClear.Location = new System.Drawing.Point(12, 47);
            this.btnClear.Name = "btnClear";
            this.btnClear.Size = new System.Drawing.Size(87, 23);
            this.btnClear.TabIndex = 7;
            this.btnClear.Text = "Clear";
            this.btnClear.Click += new System.EventHandler(this.btnClear_Click);
            // 
            // labelControl3
            // 
            this.labelControl3.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl3.Location = new System.Drawing.Point(701, 12);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(36, 16);
            this.labelControl3.TabIndex = 8;
            this.labelControl3.Text = "Roles:";
            // 
            // btnRolesClearAll
            // 
            this.btnRolesClearAll.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnRolesClearAll.Appearance.Options.UseFont = true;
            this.btnRolesClearAll.Image = global::LabMaestro.Controls.Win.Resources.clear;
            this.btnRolesClearAll.Location = new System.Drawing.Point(902, 12);
            this.btnRolesClearAll.Name = "btnRolesClearAll";
            this.btnRolesClearAll.Size = new System.Drawing.Size(23, 23);
            this.btnRolesClearAll.TabIndex = 11;
            this.btnRolesClearAll.Click += new System.EventHandler(this.btnRolesClearAll_Click);
            // 
            // btnRolesCheckAll
            // 
            this.btnRolesCheckAll.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnRolesCheckAll.Appearance.Options.UseFont = true;
            this.btnRolesCheckAll.Image = global::LabMaestro.Controls.Win.Resources.list_accept;
            this.btnRolesCheckAll.Location = new System.Drawing.Point(902, 47);
            this.btnRolesCheckAll.Name = "btnRolesCheckAll";
            this.btnRolesCheckAll.Size = new System.Drawing.Size(23, 23);
            this.btnRolesCheckAll.TabIndex = 12;
            this.btnRolesCheckAll.Click += new System.EventHandler(this.btnRolesCheckAll_Click);
            // 
            // popReports
            // 
            this.popReports.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.miRepReceivablesSummary),
            new DevExpress.XtraBars.LinkPersistInfo(this.miRepUserCredit)});
            this.popReports.Manager = this.barManager;
            this.popReports.Name = "popReports";
            // 
            // btnPrint
            // 
            this.btnPrint.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnPrint.Appearance.Options.UseFont = true;
            this.btnPrint.DropDownControl = this.popReports;
            this.btnPrint.Image = global::LabMaestro.Controls.Win.Resources.print_b_16;
            this.btnPrint.Location = new System.Drawing.Point(105, 47);
            this.btnPrint.MenuManager = this.barManager;
            this.btnPrint.Name = "btnPrint";
            this.btnPrint.Size = new System.Drawing.Size(87, 23);
            this.btnPrint.TabIndex = 17;
            this.btnPrint.Text = "Print";
            this.btnPrint.Click += new System.EventHandler(this.btnPrint_Click);
            // 
            // dropDownButton1
            // 
            this.dropDownButton1.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.dropDownButton1.Appearance.Options.UseFont = true;
            this.dropDownButton1.DropDownControl = this.popDateRange;
            this.dropDownButton1.Image = global::LabMaestro.Controls.Win.Resources.calendar_export_16;
            this.dropDownButton1.Location = new System.Drawing.Point(408, 10);
            this.dropDownButton1.MenuManager = this.barManager;
            this.dropDownButton1.Name = "dropDownButton1";
            this.dropDownButton1.Size = new System.Drawing.Size(48, 23);
            this.dropDownButton1.TabIndex = 22;
            this.dropDownButton1.ToolTip = "Quick Date-range Filter";
            // 
            // popDateRange
            // 
            this.popDateRange.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItem3),
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItem4),
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItem5),
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItem6),
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItem7),
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItem8)});
            this.popDateRange.Manager = this.barManager;
            this.popDateRange.Name = "popDateRange";
            // 
            // miRepReceivablesSummary
            // 
            this.miRepReceivablesSummary.Caption = "Receivables Summary";
            this.miRepReceivablesSummary.Id = 9;
            this.miRepReceivablesSummary.Name = "miRepReceivablesSummary";
            this.miRepReceivablesSummary.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.miRepReceivablesSummary_ItemClick);
            // 
            // miRepUserCredit
            // 
            this.miRepUserCredit.Caption = "User-wise Credits / Refunds";
            this.miRepUserCredit.Id = 10;
            this.miRepUserCredit.Name = "miRepUserCredit";
            this.miRepUserCredit.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.miRepUserCredit_ItemClick);
            // 
            // UsersShiftHistoryDialog
            // 
            this.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.ClientSize = new System.Drawing.Size(933, 527);
            this.Controls.Add(this.dropDownButton1);
            this.Controls.Add(this.btnPrint);
            this.Controls.Add(this.btnRolesCheckAll);
            this.Controls.Add(this.btnRolesClearAll);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.btnClear);
            this.Controls.Add(this.btnSearch);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.clbRoles);
            this.Controls.Add(this.dteTo);
            this.Controls.Add(this.dteFrom);
            this.Controls.Add(this.gridControl);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "UsersShiftHistoryDialog";
            this.SizeGripStyle = System.Windows.Forms.SizeGripStyle.Hide;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "UsersShiftHistoryDialog";
            this.Load += new System.EventHandler(this.UsersShiftHistoryDialog_Load);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.workshiftSearchResultSliceBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dteFrom.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dteFrom.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dteTo.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dteTo.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.clbRoles)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.popReports)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.popDateRange)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControl;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView;
        private DevExpress.XtraEditors.DateEdit dteFrom;
        private DevExpress.XtraEditors.DateEdit dteTo;
        private DevExpress.XtraEditors.CheckedListBoxControl clbRoles;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.SimpleButton btnSearch;
        private DevExpress.XtraEditors.SimpleButton btnClear;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private System.Windows.Forms.BindingSource workshiftSearchResultSliceBindingSource;
        private DevExpress.XtraGrid.Columns.GridColumn colId;
        private DevExpress.XtraGrid.Columns.GridColumn colUserName;
        private DevExpress.XtraGrid.Columns.GridColumn colRoleName;
        private DevExpress.XtraGrid.Columns.GridColumn colIsClosed;
        private DevExpress.XtraGrid.Columns.GridColumn colStartDate;
        private DevExpress.XtraGrid.Columns.GridColumn colEndDate;
        private DevExpress.XtraGrid.Columns.GridColumn colNumOrders;
        private DevExpress.XtraGrid.Columns.GridColumn colAdditionalBalance;
        private DevExpress.XtraGrid.Columns.GridColumn colReceiveAmount;
        private DevExpress.XtraGrid.Columns.GridColumn colDiscountAmount;
        private DevExpress.XtraGrid.Columns.GridColumn colDiscountRebateAmount;
        private DevExpress.XtraGrid.Columns.GridColumn colRefundAmount;
        private DevExpress.XtraGrid.Columns.GridColumn colFinalBalance;
        private DevExpress.XtraGrid.Columns.GridColumn colStartTime;
        private DevExpress.XtraGrid.Columns.GridColumn colEndTime;
        private DevExpress.XtraEditors.SimpleButton btnRolesClearAll;
        private DevExpress.XtraEditors.SimpleButton btnRolesCheckAll;
        private DevExpress.XtraBars.BarManager barManager;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarButtonItem barButtonItem1;
        private DevExpress.XtraBars.BarButtonItem barButtonItem2;
        private DevExpress.XtraBars.PopupMenu popReports;
        private DevExpress.XtraEditors.DropDownButton btnPrint;
        private DevExpress.XtraEditors.DropDownButton dropDownButton1;
        private DevExpress.XtraBars.BarButtonItem barButtonItem3;
        private DevExpress.XtraBars.BarButtonItem barButtonItem4;
        private DevExpress.XtraBars.BarButtonItem barButtonItem5;
        private DevExpress.XtraBars.BarButtonItem barButtonItem6;
        private DevExpress.XtraBars.BarButtonItem barButtonItem7;
        private DevExpress.XtraBars.BarButtonItem barButtonItem8;
        private DevExpress.XtraBars.PopupMenu popDateRange;
        private DevExpress.XtraBars.BarButtonItem miRepReceivablesSummary;
        private DevExpress.XtraBars.BarButtonItem miRepUserCredit;
    }
}