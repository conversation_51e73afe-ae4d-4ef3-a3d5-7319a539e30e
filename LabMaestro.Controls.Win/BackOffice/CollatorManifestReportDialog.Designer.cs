﻿namespace LabMaestro.Controls.Win
{
    partial class CollatorManifestReportDialog
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.dteInvoiceDate = new DevExpress.XtraEditors.DateEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.txtOIDFrom = new DevExpress.XtraEditors.TextEdit();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.chkFilterOIDRange = new DevExpress.XtraEditors.CheckEdit();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.txtOIDTo = new DevExpress.XtraEditors.TextEdit();
            this.btnPrintManifest = new DevExpress.XtraEditors.SimpleButton();
            this.btnPrintPendingOrders = new DevExpress.XtraEditors.SimpleButton();
            this.btnClose = new DevExpress.XtraEditors.SimpleButton();
            this.cboWorkflowStatus = new DevExpress.XtraEditors.ComboBoxEdit();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            ((System.ComponentModel.ISupportInitialize)(this.dteInvoiceDate.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dteInvoiceDate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtOIDFrom.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkFilterOIDRange.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtOIDTo.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cboWorkflowStatus.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // dteInvoiceDate
            // 
            this.dteInvoiceDate.EditValue = null;
            this.dteInvoiceDate.Location = new System.Drawing.Point(103, 14);
            this.dteInvoiceDate.Name = "dteInvoiceDate";
            this.dteInvoiceDate.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.dteInvoiceDate.Properties.Appearance.Options.UseFont = true;
            this.dteInvoiceDate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.dteInvoiceDate.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.dteInvoiceDate.Size = new System.Drawing.Size(172, 22);
            this.dteInvoiceDate.TabIndex = 0;
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Location = new System.Drawing.Point(12, 17);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(75, 16);
            this.labelControl1.TabIndex = 1;
            this.labelControl1.Text = "Invoice Date:";
            // 
            // txtOIDFrom
            // 
            this.txtOIDFrom.Location = new System.Drawing.Point(103, 70);
            this.txtOIDFrom.Name = "txtOIDFrom";
            this.txtOIDFrom.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtOIDFrom.Properties.Appearance.Options.UseFont = true;
            this.txtOIDFrom.Size = new System.Drawing.Size(80, 22);
            this.txtOIDFrom.TabIndex = 2;
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Location = new System.Drawing.Point(12, 73);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(60, 16);
            this.labelControl2.TabIndex = 3;
            this.labelControl2.Text = "OID From:";
            // 
            // chkFilterOIDRange
            // 
            this.chkFilterOIDRange.Location = new System.Drawing.Point(10, 44);
            this.chkFilterOIDRange.Name = "chkFilterOIDRange";
            this.chkFilterOIDRange.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkFilterOIDRange.Properties.Appearance.Options.UseFont = true;
            this.chkFilterOIDRange.Properties.Caption = "Filter By Order ID Range?";
            this.chkFilterOIDRange.Size = new System.Drawing.Size(171, 20);
            this.chkFilterOIDRange.TabIndex = 1;
            this.chkFilterOIDRange.CheckedChanged += new System.EventHandler(this.chkFilterOIDRange_CheckedChanged);
            // 
            // labelControl3
            // 
            this.labelControl3.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl3.Location = new System.Drawing.Point(12, 101);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(45, 16);
            this.labelControl3.TabIndex = 6;
            this.labelControl3.Text = "OID To:";
            // 
            // txtOIDTo
            // 
            this.txtOIDTo.Location = new System.Drawing.Point(103, 98);
            this.txtOIDTo.Name = "txtOIDTo";
            this.txtOIDTo.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtOIDTo.Properties.Appearance.Options.UseFont = true;
            this.txtOIDTo.Size = new System.Drawing.Size(80, 22);
            this.txtOIDTo.TabIndex = 3;
            // 
            // btnPrintManifest
            // 
            this.btnPrintManifest.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnPrintManifest.Appearance.Options.UseFont = true;
            this.btnPrintManifest.Image = global::LabMaestro.Controls.Win.Resources.print2;
            this.btnPrintManifest.Location = new System.Drawing.Point(12, 179);
            this.btnPrintManifest.Name = "btnPrintManifest";
            this.btnPrintManifest.Size = new System.Drawing.Size(127, 37);
            this.btnPrintManifest.TabIndex = 6;
            this.btnPrintManifest.Text = "Print Manifest";
            this.btnPrintManifest.Click += new System.EventHandler(this.btnPrintManifest_Click);
            // 
            // btnPrintPendingOrders
            // 
            this.btnPrintPendingOrders.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnPrintPendingOrders.Appearance.Options.UseFont = true;
            this.btnPrintPendingOrders.Image = global::LabMaestro.Controls.Win.Resources.print_b_16;
            this.btnPrintPendingOrders.Location = new System.Drawing.Point(148, 179);
            this.btnPrintPendingOrders.Name = "btnPrintPendingOrders";
            this.btnPrintPendingOrders.Size = new System.Drawing.Size(127, 37);
            this.btnPrintPendingOrders.TabIndex = 5;
            this.btnPrintPendingOrders.Text = "Pending Orders";
            this.btnPrintPendingOrders.Click += new System.EventHandler(this.btnPrintPendingOrders_Click);
            // 
            // btnClose
            // 
            this.btnClose.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnClose.Appearance.Options.UseFont = true;
            this.btnClose.Image = global::LabMaestro.Controls.Win.Resources.exit_16;
            this.btnClose.Location = new System.Drawing.Point(200, 222);
            this.btnClose.Name = "btnClose";
            this.btnClose.Size = new System.Drawing.Size(75, 23);
            this.btnClose.TabIndex = 7;
            this.btnClose.Text = "Close";
            this.btnClose.Click += new System.EventHandler(this.btnClose_Click);
            // 
            // cboWorkflowStatus
            // 
            this.cboWorkflowStatus.Location = new System.Drawing.Point(12, 151);
            this.cboWorkflowStatus.Name = "cboWorkflowStatus";
            this.cboWorkflowStatus.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cboWorkflowStatus.Properties.Appearance.Options.UseFont = true;
            this.cboWorkflowStatus.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cboWorkflowStatus.Properties.DropDownRows = 5;
            this.cboWorkflowStatus.Properties.Items.AddRange(new object[] {
            "Pending Result Entry",
            "Pending Validation",
            "Pending Finalization",
            "Pending Collation",
            "Pending Dispatch"});
            this.cboWorkflowStatus.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cboWorkflowStatus.Size = new System.Drawing.Size(263, 22);
            this.cboWorkflowStatus.TabIndex = 4;
            // 
            // labelControl4
            // 
            this.labelControl4.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl4.Location = new System.Drawing.Point(12, 129);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(233, 16);
            this.labelControl4.TabIndex = 11;
            this.labelControl4.Text = "Filter By Report Bundle Workflow Status:";
            // 
            // CollatorManifestReportDialog
            // 
            this.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.ClientSize = new System.Drawing.Size(289, 259);
            this.Controls.Add(this.labelControl4);
            this.Controls.Add(this.cboWorkflowStatus);
            this.Controls.Add(this.btnClose);
            this.Controls.Add(this.btnPrintPendingOrders);
            this.Controls.Add(this.btnPrintManifest);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.txtOIDTo);
            this.Controls.Add(this.chkFilterOIDRange);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.txtOIDFrom);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.dteInvoiceDate);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "CollatorManifestReportDialog";
            this.ShowIcon = false;
            this.ShowInTaskbar = false;
            this.SizeGripStyle = System.Windows.Forms.SizeGripStyle.Hide;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "Collation Manifest Report";
            this.Load += new System.EventHandler(this.CollatorManifestReportDialog_Load);
            ((System.ComponentModel.ISupportInitialize)(this.dteInvoiceDate.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dteInvoiceDate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtOIDFrom.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkFilterOIDRange.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtOIDTo.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cboWorkflowStatus.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.DateEdit dteInvoiceDate;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.TextEdit txtOIDFrom;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.CheckEdit chkFilterOIDRange;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.TextEdit txtOIDTo;
        private DevExpress.XtraEditors.SimpleButton btnPrintManifest;
        private DevExpress.XtraEditors.SimpleButton btnPrintPendingOrders;
        private DevExpress.XtraEditors.SimpleButton btnClose;
        private DevExpress.XtraEditors.ComboBoxEdit cboWorkflowStatus;
        private DevExpress.XtraEditors.LabelControl labelControl4;
    }
}