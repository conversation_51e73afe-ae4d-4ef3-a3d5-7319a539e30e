﻿namespace LabMaestro.Controls.Win
{
    partial class MessageQueueToolDialog
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.btnHeartbeat = new DevExpress.XtraEditors.SimpleButton();
            this.memLog = new DevExpress.XtraEditors.MemoEdit();
            this.btnCheckDbConn = new DevExpress.XtraEditors.SimpleButton();
            ((System.ComponentModel.ISupportInitialize)(this.memLog.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Location = new System.Drawing.Point(12, 41);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(137, 16);
            this.labelControl1.TabIndex = 0;
            this.labelControl1.Text = "Reply from Service Bus:";
            // 
            // btnHeartbeat
            // 
            this.btnHeartbeat.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnHeartbeat.Appearance.Options.UseFont = true;
            this.btnHeartbeat.Location = new System.Drawing.Point(12, 12);
            this.btnHeartbeat.Name = "btnHeartbeat";
            this.btnHeartbeat.Size = new System.Drawing.Size(179, 23);
            this.btnHeartbeat.TabIndex = 1;
            this.btnHeartbeat.Text = "Check Bus Heartbeat";
            this.btnHeartbeat.Click += new System.EventHandler(this.btnHeartbeat_Click);
            // 
            // memLog
            // 
            this.memLog.Location = new System.Drawing.Point(12, 63);
            this.memLog.Name = "memLog";
            this.memLog.Properties.Appearance.BackColor = System.Drawing.Color.Black;
            this.memLog.Properties.Appearance.Font = new System.Drawing.Font("Consolas", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.memLog.Properties.Appearance.ForeColor = System.Drawing.Color.Lime;
            this.memLog.Properties.Appearance.Options.UseBackColor = true;
            this.memLog.Properties.Appearance.Options.UseFont = true;
            this.memLog.Properties.Appearance.Options.UseForeColor = true;
            this.memLog.Properties.ReadOnly = true;
            this.memLog.Size = new System.Drawing.Size(537, 276);
            this.memLog.TabIndex = 2;
            this.memLog.UseOptimizedRendering = true;
            // 
            // btnCheckDbConn
            // 
            this.btnCheckDbConn.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCheckDbConn.Appearance.Options.UseFont = true;
            this.btnCheckDbConn.Location = new System.Drawing.Point(197, 12);
            this.btnCheckDbConn.Name = "btnCheckDbConn";
            this.btnCheckDbConn.Size = new System.Drawing.Size(179, 23);
            this.btnCheckDbConn.TabIndex = 3;
            this.btnCheckDbConn.Text = "Check Database Connectivity";
            this.btnCheckDbConn.Click += new System.EventHandler(this.btnCheckDbConn_Click);
            // 
            // MessageQueueToolDialog
            // 
            this.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.ClientSize = new System.Drawing.Size(561, 351);
            this.Controls.Add(this.btnCheckDbConn);
            this.Controls.Add(this.memLog);
            this.Controls.Add(this.btnHeartbeat);
            this.Controls.Add(this.labelControl1);
            this.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "MessageQueueToolDialog";
            this.ShowInTaskbar = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "Message Queue / Service Bus Diagnostics";
            ((System.ComponentModel.ISupportInitialize)(this.memLog.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.SimpleButton btnHeartbeat;
        private DevExpress.XtraEditors.MemoEdit memLog;
        private DevExpress.XtraEditors.SimpleButton btnCheckDbConn;
    }
}