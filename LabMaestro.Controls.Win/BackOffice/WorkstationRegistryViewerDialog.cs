﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: WorkstationRegistryViewerDialog.cs 1288 2014-05-22 10:34:41Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using DevExpress.XtraEditors;
using LabMaestro.Domain;
using LabMaestro.Shared;

namespace LabMaestro.Controls.Win
{
    public partial class WorkstationRegistryViewerDialog : XtraForm
    {
        private readonly List<WorkstationInfo> _workstations;

        public WorkstationRegistryViewerDialog()
        {
            _workstations = new List<WorkstationInfo>();
            InitializeComponent();
            WinUtils.ApplyTheme(this);
        }

        private void WorkstationRegistryViewerDialog_Load(object sender, EventArgs e)
        {
            var registries = AppSysRepository.GeAllWorkstationRegistries();
            _workstations.Clear();
            foreach (var slice in registries)
            {
                var ws = new WorkstationInfo(
                    slice.MACAddress,
                    SharedUtilities.IpAddressToString(slice.IPAddress),
                    slice.AppVersion,
                    slice.CurrentUser,
                    slice.OSVersion,
                    SharedUtilities.DateTimeToString24(slice.LastSeen));
                _workstations.Add(ws);
            }
            grdRegistry.DataSource = _workstations;
        }

        public sealed class WorkstationInfo
        {
            public WorkstationInfo(string macAddress, string ipAddress, string version, string user, string osVersion,
                string lastSeen)
            {
                LastSeen = lastSeen;
                OsVersion = osVersion;
                User = user;
                Version = version;
                IpAddress = ipAddress;
                MacAddress = macAddress;
            }

            public string MacAddress { get; private set; }
            public string IpAddress { get; private set; }
            public string Version { get; private set; }
            public string User { get; private set; }
            public string OsVersion { get; private set; }
            public string LastSeen { get; private set; }
        }
    }
}