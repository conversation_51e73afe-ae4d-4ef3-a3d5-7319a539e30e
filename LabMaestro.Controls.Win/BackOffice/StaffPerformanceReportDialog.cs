﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: StaffPerformanceReportDialog.cs 1278 2014-05-20 15:25:03Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using LabMaestro.BusinessLogic;
using LabMaestro.Infrastructure.Client;

namespace LabMaestro.Controls.Win
{
    public partial class StaffPerformanceReportDialog : XtraForm
    {
        public StaffPerformanceReportDialog()
        {
            InitializeComponent();
            WinUtils.ApplyTheme(this);
        }

        public string SelectedRoleCode { get; set; }
        public string SelectedRoleName { get; set; }
        public WorkflowAuditEventType SelectedAuditEventType { get; set; }
        public DateTime DateFrom { get; set; }
        public DateTime DateTill { get; set; }

        private void labelControl2_Click(object sender, EventArgs e)
        {
        }

        private void btnOk_Click(object sender, EventArgs e)
        {
            switch (rgRoles.SelectedIndex)
            {
                case 0:
                    SelectedRoleCode = RoleCodes.ResultEntry;
                    SelectedRoleName = "Result Entry";
                    SelectedAuditEventType = WorkflowAuditEventType.ResultEntry;
                    break;
                case 1:
                    SelectedRoleCode = RoleCodes.ResultVerifier;
                    SelectedRoleName = "Result Validator";
                    SelectedAuditEventType = WorkflowAuditEventType.ResultValidation;
                    break;
                case 2:
                    SelectedRoleCode = RoleCodes.ConsultantPhysician;
                    SelectedRoleName = "Consultant";
                    SelectedAuditEventType = WorkflowAuditEventType.ResultFinalization;
                    break;
                case 3:
                    SelectedRoleCode = RoleCodes.ReportCollator;
                    SelectedRoleName = "Report Collator";
                    SelectedAuditEventType = WorkflowAuditEventType.ReportCollation;
                    break;
                case 4:
                    SelectedRoleCode = RoleCodes.ReportDispatcher;
                    SelectedRoleName = "Report Dispatcher";
                    SelectedAuditEventType = WorkflowAuditEventType.ReportDispatch;
                    break;
                default:
                    MessageDlg.Warning("Please select a staff role!");
                    return;
            }

            DateFrom = dteFrom.DateTime.Date;
            DateTill = dteTill.DateTime.Date;
            DialogResult = DialogResult.OK;
        }

        private void StaffPerformanceReportDialog_Load(object sender, EventArgs e)
        {
            dteFrom.DateTime = DateTime.Now;
            dteTill.DateTime = DateTime.Now;
            rgRoles.SelectedIndex = 3;
        }
    }
}