﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: AdminBackendDialog.cs 1076 2013-11-11 13:22:24Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System.Windows.Forms;
using DevExpress.XtraEditors;
using LabMaestro.BusinessLogic;

namespace LabMaestro.Controls.Win
{
    public partial class AdminBackendDialog : XtraForm
    {
        public AdminBackendDialog()
        {
            InitializeComponent();
            //WinUtils.ApplyTheme(this);
            WinUtils.SetFormTitle(this, "Admin Backend");
        }

        public static void ExecuteDialog(IWin32Window parent)
        {
            using (var frm = new AdminBackendDialog())
            {
                frm.ShowDialog(parent);
            }
        }

        private void tiAppRepTpl_ItemClick(object sender, TileItemEventArgs e)
        {
            using (var frm = new AppReportTemplatesEditorDialog())
            {
                frm.UpdateControls();
                frm.ExecuteDialog(this);
            }
        }

        private void tiBillableItems_ItemClick(object sender, TileItemEventArgs e)
        {
            using (var frm = new BillableItemsCatalogEditorDialog
            {
                DataProvider = new BillableItemsDataAccessProviderDb()
            })
            {
                frm.UpdateControls();
                frm.ShowDialog(this);
            }
        }

        private void tiDiscLevel_ItemClick(object sender, TileItemEventArgs e)
        {
            using (var frm = new DiscountReferralEditorDialog
            {
                IsDiscountLevelEditor = true,
                DiscountDataProvider = new DiscountLevelsDataAccessProviderDb()
            })
            {
                frm.UpdateControls();
                frm.ShowDialog(this);
            }
        }

        private void tiReferralGroups_ItemClick(object sender, TileItemEventArgs e)
        {
            using (var frm = new DiscountReferralEditorDialog
            {
                IsDiscountLevelEditor = false,
                ReferralDataProvider = new ReferralGroupsDataAccessProviderDb()
            })
            {
                frm.IsDiscountLevelEditor = false;
                frm.UpdateControls();
                frm.ShowDialog(this);
            }
        }

        private void tiUsers_ItemClick(object sender, TileItemEventArgs e)
        {
            using (var frm = new UsersCatalogEditorDialog())
            {
                frm.ShowDialog(this);
            }
        }

        private void tiLabs_ItemClick(object sender, TileItemEventArgs e)
        {
            using (var frm = new LabsEditorDialog
            {
                LabsDataProvider = new LabsDataAccessProviderDb(false),
                ReferralsDataProvider = new ReferralGroupsDataAccessProviderDb(),
                DiscountsDataProvider = new DiscountLevelsDataAccessProviderDb(),
                ReportHeadersDataProvider = new LabReportHeadersDataAccessProvider()
            })
            {
                frm.UpdateControls();
                frm.ShowDialog(this);
            }
        }

        private void tiTests_ItemClick(object sender, TileItemEventArgs e)
        {
            using (var frm = new TestCatalogEditorDialog {LabsDataProvider = new LabsDataAccessProviderDb(false)})
            {
                frm.ShowDialog(this);
            }
        }

        private void tiRoles_ItemClick(object sender, TileItemEventArgs e)
        {
            using (var frm = new RolesEditorDialog())
            {
                frm.ShowDialog(this);
            }
        }

        private void tiPerms_ItemClick(object sender, TileItemEventArgs e)
        {
            using (var frm = new PermissionsEditorDialog())
            {
                frm.ShowDialog(this);
            }
        }

        private void tiTplRep_ItemClick(object sender, TileItemEventArgs e)
        {
            using (var frm = new TemplateReportsEditorDialog())
            {
                frm.ShowDialog(this);
            }
        }

        private void tiTplGrps_ItemClick(object sender, TileItemEventArgs e)
        {
            using (var frm = new TemplateGroupsEditorDialog())
            {
                frm.UpdateControls();
                frm.ShowDialog();
            }
        }

        private void tiQuit_ItemClick(object sender, TileItemEventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        private void AdminBackendDialog_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape)
            {
                e.Handled = true;
                DialogResult = DialogResult.OK;
            }
        }
    }
}