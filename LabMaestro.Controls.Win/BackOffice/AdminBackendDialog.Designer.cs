﻿namespace LabMaestro.Controls.Win
{
    partial class AdminBackendDialog
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            DevExpress.XtraEditors.TileItemElement tileItemElement13 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement14 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement15 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement16 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement17 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement18 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement19 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement20 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement21 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement22 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement23 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement24 = new DevExpress.XtraEditors.TileItemElement();
            this.tileControl = new DevExpress.XtraEditors.TileControl();
            this.tileGroup2 = new DevExpress.XtraEditors.TileGroup();
            this.tiAppRepTpl = new DevExpress.XtraEditors.TileItem();
            this.tiBillableItems = new DevExpress.XtraEditors.TileItem();
            this.tiLabs = new DevExpress.XtraEditors.TileItem();
            this.tiTests = new DevExpress.XtraEditors.TileItem();
            this.tiDiscLevel = new DevExpress.XtraEditors.TileItem();
            this.tiReferralGroups = new DevExpress.XtraEditors.TileItem();
            this.tiRoles = new DevExpress.XtraEditors.TileItem();
            this.tiPerms = new DevExpress.XtraEditors.TileItem();
            this.tiUsers = new DevExpress.XtraEditors.TileItem();
            this.tiTplRep = new DevExpress.XtraEditors.TileItem();
            this.tiTplGrps = new DevExpress.XtraEditors.TileItem();
            this.tiQuit = new DevExpress.XtraEditors.TileItem();
            this.SuspendLayout();
            // 
            // tileControl
            // 
            this.tileControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tileControl.Groups.Add(this.tileGroup2);
            this.tileControl.ItemSize = 160;
            this.tileControl.Location = new System.Drawing.Point(0, 0);
            this.tileControl.MaxId = 12;
            this.tileControl.Name = "tileControl";
            this.tileControl.Size = new System.Drawing.Size(580, 213);
            this.tileControl.TabIndex = 0;
            this.tileControl.Text = "tileControl1";
            // 
            // tileGroup2
            // 
            this.tileGroup2.Items.Add(this.tiAppRepTpl);
            this.tileGroup2.Items.Add(this.tiBillableItems);
            this.tileGroup2.Items.Add(this.tiLabs);
            this.tileGroup2.Items.Add(this.tiTests);
            this.tileGroup2.Items.Add(this.tiDiscLevel);
            this.tileGroup2.Items.Add(this.tiReferralGroups);
            this.tileGroup2.Items.Add(this.tiRoles);
            this.tileGroup2.Items.Add(this.tiPerms);
            this.tileGroup2.Items.Add(this.tiUsers);
            this.tileGroup2.Items.Add(this.tiTplRep);
            this.tileGroup2.Items.Add(this.tiTplGrps);
            this.tileGroup2.Items.Add(this.tiQuit);
            this.tileGroup2.Name = "tileGroup2";
            this.tileGroup2.Text = "Catalog";
            // 
            // tiAppRepTpl
            // 
            tileItemElement13.Text = "App Report Templates";
            this.tiAppRepTpl.Elements.Add(tileItemElement13);
            this.tiAppRepTpl.Id = 0;
            this.tiAppRepTpl.ItemSize = DevExpress.XtraEditors.TileItemSize.Small;
            this.tiAppRepTpl.Name = "tiAppRepTpl";
            this.tiAppRepTpl.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiAppRepTpl_ItemClick);
            // 
            // tiBillableItems
            // 
            tileItemElement14.Text = "Billable Items";
            this.tiBillableItems.Elements.Add(tileItemElement14);
            this.tiBillableItems.Id = 1;
            this.tiBillableItems.ItemSize = DevExpress.XtraEditors.TileItemSize.Small;
            this.tiBillableItems.Name = "tiBillableItems";
            this.tiBillableItems.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiBillableItems_ItemClick);
            // 
            // tiLabs
            // 
            tileItemElement15.Text = "Labs";
            this.tiLabs.Elements.Add(tileItemElement15);
            this.tiLabs.Id = 2;
            this.tiLabs.ItemSize = DevExpress.XtraEditors.TileItemSize.Small;
            this.tiLabs.Name = "tiLabs";
            this.tiLabs.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiLabs_ItemClick);
            // 
            // tiTests
            // 
            tileItemElement16.Text = "Tests";
            this.tiTests.Elements.Add(tileItemElement16);
            this.tiTests.Id = 3;
            this.tiTests.ItemSize = DevExpress.XtraEditors.TileItemSize.Small;
            this.tiTests.Name = "tiTests";
            this.tiTests.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiTests_ItemClick);
            // 
            // tiDiscLevel
            // 
            tileItemElement17.Text = "Discount Levels";
            this.tiDiscLevel.Elements.Add(tileItemElement17);
            this.tiDiscLevel.Id = 4;
            this.tiDiscLevel.ItemSize = DevExpress.XtraEditors.TileItemSize.Small;
            this.tiDiscLevel.Name = "tiDiscLevel";
            this.tiDiscLevel.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiDiscLevel_ItemClick);
            // 
            // tiReferralGroups
            // 
            tileItemElement18.Text = "Ref. Groups";
            this.tiReferralGroups.Elements.Add(tileItemElement18);
            this.tiReferralGroups.Id = 5;
            this.tiReferralGroups.ItemSize = DevExpress.XtraEditors.TileItemSize.Small;
            this.tiReferralGroups.Name = "tiReferralGroups";
            this.tiReferralGroups.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiReferralGroups_ItemClick);
            // 
            // tiRoles
            // 
            tileItemElement19.Text = "Roles";
            this.tiRoles.Elements.Add(tileItemElement19);
            this.tiRoles.Id = 6;
            this.tiRoles.ItemSize = DevExpress.XtraEditors.TileItemSize.Small;
            this.tiRoles.Name = "tiRoles";
            this.tiRoles.Tag = "";
            this.tiRoles.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiRoles_ItemClick);
            // 
            // tiPerms
            // 
            tileItemElement20.Text = "Permissions";
            this.tiPerms.Elements.Add(tileItemElement20);
            this.tiPerms.Id = 7;
            this.tiPerms.ItemSize = DevExpress.XtraEditors.TileItemSize.Small;
            this.tiPerms.Name = "tiPerms";
            this.tiPerms.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiPerms_ItemClick);
            // 
            // tiUsers
            // 
            tileItemElement21.Text = "Users";
            this.tiUsers.Elements.Add(tileItemElement21);
            this.tiUsers.Id = 8;
            this.tiUsers.ItemSize = DevExpress.XtraEditors.TileItemSize.Small;
            this.tiUsers.Name = "tiUsers";
            this.tiUsers.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiUsers_ItemClick);
            // 
            // tiTplRep
            // 
            tileItemElement22.Text = "Template Reports";
            this.tiTplRep.Elements.Add(tileItemElement22);
            this.tiTplRep.Id = 9;
            this.tiTplRep.ItemSize = DevExpress.XtraEditors.TileItemSize.Small;
            this.tiTplRep.Name = "tiTplRep";
            this.tiTplRep.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiTplRep_ItemClick);
            // 
            // tiTplGrps
            // 
            tileItemElement23.Text = "Template Groups";
            this.tiTplGrps.Elements.Add(tileItemElement23);
            this.tiTplGrps.Id = 10;
            this.tiTplGrps.ItemSize = DevExpress.XtraEditors.TileItemSize.Small;
            this.tiTplGrps.Name = "tiTplGrps";
            this.tiTplGrps.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiTplGrps_ItemClick);
            // 
            // tiQuit
            // 
            this.tiQuit.AppearanceItem.Normal.BackColor = System.Drawing.Color.Purple;
            this.tiQuit.AppearanceItem.Normal.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(0)))), ((int)(((byte)(64)))));
            this.tiQuit.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tiQuit.AppearanceItem.Normal.Options.UseBorderColor = true;
            tileItemElement24.Text = "Quit";
            this.tiQuit.Elements.Add(tileItemElement24);
            this.tiQuit.Id = 11;
            this.tiQuit.ItemSize = DevExpress.XtraEditors.TileItemSize.Small;
            this.tiQuit.Name = "tiQuit";
            this.tiQuit.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tiQuit_ItemClick);
            // 
            // AdminBackendDialog
            // 
            this.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.ClientSize = new System.Drawing.Size(580, 213);
            this.ControlBox = false;
            this.Controls.Add(this.tileControl);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
            this.KeyPreview = true;
            this.LookAndFeel.SkinName = "Darkroom";
            this.LookAndFeel.UseDefaultLookAndFeel = false;
            this.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "AdminBackendDialog";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "AdminBackendDialog";
            this.KeyUp += new System.Windows.Forms.KeyEventHandler(this.AdminBackendDialog_KeyUp);
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.TileControl tileControl;
        private DevExpress.XtraEditors.TileGroup tileGroup2;
        private DevExpress.XtraEditors.TileItem tiAppRepTpl;
        private DevExpress.XtraEditors.TileItem tiBillableItems;
        private DevExpress.XtraEditors.TileItem tiLabs;
        private DevExpress.XtraEditors.TileItem tiTests;
        private DevExpress.XtraEditors.TileItem tiDiscLevel;
        private DevExpress.XtraEditors.TileItem tiReferralGroups;
        private DevExpress.XtraEditors.TileItem tiRoles;
        private DevExpress.XtraEditors.TileItem tiPerms;
        private DevExpress.XtraEditors.TileItem tiUsers;
        private DevExpress.XtraEditors.TileItem tiTplRep;
        private DevExpress.XtraEditors.TileItem tiTplGrps;
        private DevExpress.XtraEditors.TileItem tiQuit;
    }
}