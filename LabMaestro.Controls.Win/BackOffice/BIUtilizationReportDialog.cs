﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: BIUtilizationReportDialog.cs 951 2013-09-22 08:31:44Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using LabMaestro.BusinessLogic;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.Controls.Win
{
    public partial class BIUtilizationReportDialog : XtraForm
    {
        private readonly BillableItemUtilizationReportCompiler _compiler;

        public BIUtilizationReportDialog()
        {
            _compiler = new BillableItemUtilizationReportCompiler();
            InitializeComponent();
            WinUtils.ApplyTheme(this);
            initializeControls();
        }

        private void initializeControls()
        {
            dteStart.DateTime = DateTime.Now;
            dteEnd.DateTime = DateTime.Now;
            rgSortOrder.SelectedIndex = 0;
        }

        public static void ExecuteDialog(IWin32Window parent)
        {
            using (var frm = new BIUtilizationReportDialog())
            {
                frm.ShowDialog(parent);
            }
        }

        private void btnCompileReport_Click(object sender, EventArgs e)
        {
            var dtStart = dteStart.DateTime;
            var dtEnd = dteEnd.DateTime;

            if (dtStart > dtEnd)
            {
                MessageDlg.Error("Start date must be less than End date!");
                return;
            }

            switch (rgSortOrder.SelectedIndex)
            {
                case 1:
                    // by name
                    _compiler.SortType = UtilizationSortType.Name;
                    break;
                case 2:
                    // by value
                    _compiler.SortType = UtilizationSortType.Value;
                    break;
                default:
                    // by volume
                    _compiler.SortType = UtilizationSortType.Volume;
                    break;
            }

            _compiler.DateStart = dtStart;
            _compiler.DateEnd = dtEnd;

            WaitFormControl.WaitOperation(this,
                                          () => _compiler.CompileReport(),
                                          "Compiling report...");

            var dto = _compiler.ToPrintDto();
            dto.PrintDate = SharedUtilities.DateTimeToShortString(AppSysRepository.GetServerTime());
            dto.UserName = CurrentUserContext.UserDisplayName;
            PrintHelper.PrintBillableItemUtilizationReport(dto);
        }

        private void btnExit_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }
    }
}