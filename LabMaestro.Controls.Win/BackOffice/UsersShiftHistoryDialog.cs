﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the organization nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: UsersShiftHistoryDialog.cs 1314 2014-05-24 19:15:37Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using DevExpress.Utils;
using DevExpress.Utils.Menu;
using DevExpress.XtraBars;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraEditors.Repository;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraGrid.Views.Grid;
using LabMaestro.BusinessLogic;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.Controls.Win;

public partial class UsersShiftHistoryDialog : XtraForm, IExecutableDialog
{
    public UsersShiftHistoryDialog()
    {
        InitializeComponent();
        WinUtils.ApplyTheme(this);
        WinUtils.SetFormTitle(this, "Users Shift History");
    }

    public bool ExecuteDialog(IWin32Window parent)
    {
        UpdateControls();
        ShowDialog(parent);
        return true;
    }

    public void UpdateControls()
    {
    }

    private void UsersShiftHistoryDialog_Load(object sender, EventArgs e)
    {
        resetSearchControls();

        var shiftImages = new ImageCollection();
        shiftImages.AddImage(Resources.tick_shield_16);

        var shiftCombo = gridControl.RepositoryItems.Add("ImageComboBoxEdit") as RepositoryItemImageComboBox;
        shiftCombo.SmallImages = shiftImages;
        shiftCombo.Items.Add(new ImageComboBoxItem(true, 0));
        shiftCombo.GlyphAlignment = HorzAlignment.Center;
        gridView.Columns[@"IsClosed"].ColumnEdit = shiftCombo;
    }

    private void resetSearchControls()
    {
        dteFrom.DateTime = DateTime.Now;
        dteTo.DateTime = DateTime.Now;
        //cboQuickDateRange.SelectedIndex = 0;
        clbRoles.CheckAll();
    }

    private short getRoleId(string roleCode)
    {
        var role = RolesRepository.FindRoleByCode(roleCode);
        return (short)(role != null ? role.Id : -1);
    }

    private void btnSearch_Click(object sender, EventArgs e) => performSearch();

    private Tuple<DateTime, DateTime> updateDateRange(int index)
    {
        var dtStart = DateTime.Now.Date;
        var dtEnd = SharedUtilities.LastInstantOfDay(dtStart);

        if (index == 0)
        {
            dtStart = dteFrom.DateTime.Date;
            dtEnd = SharedUtilities.LastInstantOfDay(dteTo.DateTime);
        }
        else
        {
            dtEnd = SharedUtilities.LastInstantOfDay(DateTime.Now);
            switch (index)
            {
                case 1:
                    dtStart = DateTime.Now.AddDays(-1).Date;
                    dtEnd = SharedUtilities.LastInstantOfDay(dtStart);
                    break;
                case 2:
                    dtStart = DateTime.Now.AddDays(-3).Date;
                    break;
                case 3:
                    dtStart = DateTime.Now.AddDays(-7).Date;
                    break;
                case 4:
                    dtStart = DateTime.Now.AddDays(-14).Date;
                    break;
                case 5:
                    dtStart = DateTime.Now.AddMonths(-1).Date;
                    break;
                case 6:
                    dtStart = DateTime.Now.AddMonths(-3).Date;
                    break;
            }

            dteFrom.DateTime = dtStart;
            dteTo.DateTime = dtEnd;
        }

        return new Tuple<DateTime, DateTime>(dtStart, dtEnd);
    }

    private Tuple<DateTime, DateTime> getSelectedDateRange()
    {
        var dtStart = dteFrom.DateTime.Date;
        var dtEnd = SharedUtilities.LastMinuteOfDay(dteTo.DateTime);
        return new Tuple<DateTime, DateTime>(dtStart, dtEnd);
    }

    private void performSearch()
    {
        var range = getSelectedDateRange(); // getDateRange(cboQuickDateRange.SelectedIndex);
        var dtStart = range.Item1;
        var dtEnd = range.Item2;

        var roles = new List<short>();
        foreach (var index in clbRoles.CheckedIndices)
        {
            switch (index)
            {
                case 0:
                    roles.Add(getRoleId(UserRoles.Receptionist));
                    break;
                case 1:
                    roles.Add(getRoleId(UserRoles.AccountsReceivable));
                    break;
                case 2:
                    roles.Add(getRoleId(UserRoles.Supervisor));
                    break;
            }
        }

        List<WorkshiftSearchResultSlice> list = null;
        FaultHandler.Shield(() =>
            WaitFormControl.WaitOperation(this,
                () => list = roles.Count == 0
                    ? WorkShiftsRepository.FindWorkshiftByDateRange(dtStart, dtEnd)
                    : WorkShiftsRepository.FindWorkshiftByDateRangeRoles(dtStart, dtEnd, roles),
                "Searching..."));

        list = list.OrderBy(w => w.RoleName).ThenBy(w => w.Id).ToList();
        populateGridView(list);
    }

    private void populateGridView(List<WorkshiftSearchResultSlice> list)
    {
        gridView.BeginUpdate();
        try
        {
            workshiftSearchResultSliceBindingSource.DataSource = list;
        }
        finally
        {
            gridView.EndUpdate();
        }
    }

    private void btnClear_Click(object sender, EventArgs e)
    {
        resetSearchControls();
        populateGridView(null);
    }

    private void btnRolesClearAll_Click(object sender, EventArgs e) => clbRoles.UnCheckAll();

    private void btnRolesCheckAll_Click(object sender, EventArgs e) => clbRoles.CheckAll();

    private bool isDecimalColum(string colName)
    {
        return (colName == "AdditionalBalance" ||
                colName == "ReceiveAmount" ||
                colName == "DiscountAmount" ||
                colName == "DiscountRebateAmount" ||
                colName == "RefundAmount" ||
                colName == "FinalBalance"
            );
    }

    private void gridView_CustomColumnDisplayText(object sender, CustomColumnDisplayTextEventArgs e)
    {
        if (isDecimalColum(e.Column.FieldName))
        {
            var amount = (decimal)e.Value;
            e.DisplayText = amount > 0 ? amount.ToString("0,0") : string.Empty;
        }
    }

    private void gridView_PopupMenuShowing(object sender, PopupMenuShowingEventArgs e)
    {
        if (e.MenuType == GridMenuType.Row)
        {
            var menu = e.Menu;
            if (menu.Items.Count > 0) return;

            menu.Items.Clear();
            menu.Items.Add(createMenuItem("Reconcile Shift Finances", popReconcileShift, Resources.coin_stack16));
            menu.Items.Add(createMenuItem("View Shift Details", popViewShiftDetails, Resources.invoice_coin16));
        }
    }

    private DXMenuItem createMenuItem(string caption, EventHandler onClick, Image image)
    {
        var item = new DXMenuItem(caption, onClick, image);
        return item;
    }

    private int getSelectedShiftId()
    {
        if (gridView.SelectedRowsCount == 1 &&
            gridView.GetFocusedRow() as WorkshiftSearchResultSlice is { } item)
            return item.Id;

        return -1;
    }

    private void popReconcileShift(object sender, EventArgs eventArgs)
    {
        var shiftId = getSelectedShiftId();
        FaultHandler.Shield(() =>
        {
            if (shiftId > 0)
            {
                var shift = WorkShiftsRepository.FindShiftById(shiftId);
                if (shift is { IsClosed: false })
                {
                    WaitFormControl.WaitOperation(this, shift.ReconcileShiftFinances, "Reconciling...");
                    performSearch();
                }
            }
        });
    }

    private void popViewShiftDetails(object sender, EventArgs eventArgs)
    {
        var shiftId = getSelectedShiftId();
        if (shiftId > 0)
        {
            new ShiftDetailsDialog(shiftId, false).ExecuteDialog(this);
            performSearch();
        }
    }

    private void btnPrint_Click(object sender, EventArgs e)
    {
    }

    private void generateReport(ReportType reportType, bool showPreview)
    {
        var range = getSelectedDateRange();
        var rsRep = new ReceivablesSummaryReportBuilder { BeginDate = range.Item1, EndDate = range.Item2 };
        rsRep.CompileAllTransactionsReport();
        var dto = rsRep.GetReceivablesSummaryReportPrintDto(CurrentUserContext.UserDisplayName);
        switch (reportType)
        {
            case ReportType.ReceivablesSummary:
                PrintHelper.PrintReceivablesSummaryReport(showPreview, dto);
                break;
            case ReportType.RefundDiscountReport:
                PrintHelper.PrintRefundDiscountReport(showPreview, dto);
                break;
        }
    }

    private void popQuickDateRange_Click(object sender, ItemClickEventArgs e)
    {
        try
        {
            var index = (int)e.Item.Tag;
            updateDateRange(index);
        }
        catch
        {
        }
    }

    private void miRepUserCredit_ItemClick(object sender, ItemClickEventArgs e) =>
        generateReport(ReportType.RefundDiscountReport, true);

    private void miRepReceivablesSummary_ItemClick(object sender, ItemClickEventArgs e) =>
        generateReport(ReportType.ReceivablesSummary, true);

    private enum ReportType
    {
        ReceivablesSummary,
        RefundDiscountReport
    }
}