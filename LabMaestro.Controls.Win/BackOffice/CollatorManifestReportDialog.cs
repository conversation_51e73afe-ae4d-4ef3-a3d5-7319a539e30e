﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: CollatorManifestReportDialog.cs 1459 2014-10-12 11:21:09Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using LabMaestro.BusinessLogic;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.Controls.Win
{
    public partial class CollatorManifestReportDialog : XtraForm
    {
        public CollatorManifestReportDialog()
        {
            InitializeComponent();
            WinUtils.ApplyTheme(this);
        }

        private void CollatorManifestReportDialog_Load(object sender, EventArgs e)
        {
            chkFilterOIDRange_CheckedChanged(null, null);
            dteInvoiceDate.DateTime = DateTime.Now;
            cboWorkflowStatus.SelectedIndex = 3;
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        private CollatorsManifestReportPrintDto compileReport(bool pendingReport)
        {
            if (!validateControls(pendingReport)) return null;
            var report = new CollatorsManifestReportBuilder
            {
                InvoiceDateFrom = dteInvoiceDate.DateTime.Date,
                InvoiceDateTill = dteInvoiceDate.DateTime.Date
            };

            if (!string.IsNullOrEmpty(txtOIDFrom.Text)) report.OrderIdFrom = txtOIDFrom.Text;
            if (!string.IsNullOrEmpty(txtOIDTo.Text)) report.OrderIdTo = txtOIDTo.Text;

            if (pendingReport)
            {
                switch (cboWorkflowStatus.SelectedIndex)
                {
                    case 0:
                        // Pending Result Entry
                        report.BundleCutoffStage = WorkflowStageType.ResultEntry;
                        break;
                    case 1:
                        // Pending Validation
                        report.BundleCutoffStage = WorkflowStageType.ResultValidation;
                        break;
                    case 2:
                        // Pending Finalization
                        report.BundleCutoffStage = WorkflowStageType.ReportFinalization;
                        break;
                    case 3:
                        // Pending Collation
                        report.BundleCutoffStage = WorkflowStageType.ReportCollation;
                        break;
                    case 4:
                        // Pending Dispatch
                        report.BundleCutoffStage = WorkflowStageType.ReportDispatch;
                        break;
                }
            }

            WaitFormControl.WaitOperation(this, () => FaultHandler.Shield(report.CompileReport));
            return report.ToPrintDto();
        }

        private void btnPrintManifest_Click(object sender, EventArgs e)
        {
            var dto = compileReport(false);
            if (dto == null)
            {
                MessageDlg.Info("No lab orders found with matching criteria.");
                return;
            }
            PrintHelper.PrintCollationManifest(dto);
        }

        private string getText(TextEdit txt)
        {
            return txt.Text.Trim();
        }

        private bool validateControls(bool pendingReport)
        {
            if (chkFilterOIDRange.Checked)
            {
                var sfrom = getText(txtOIDFrom);
                if (!string.IsNullOrEmpty(sfrom))
                    txtOIDFrom.Text = sfrom.ToUpperInvariant();
                var sto = getText(txtOIDTo);
                if (!string.IsNullOrEmpty(sto))
                    txtOIDTo.Text = sto.ToUpperInvariant();
            }

            if (pendingReport && cboWorkflowStatus.SelectedIndex == -1)
            {
                MessageDlg.Warning("Please select a workflow status!");
                cboWorkflowStatus.Focus();
                cboWorkflowStatus.Select();
                return false;
            }
            return true;
        }

        private void textEnable(TextEdit txt, bool enable)
        {
            txt.Enabled = enable;
            if (!enable)
                txt.Text = string.Empty;
        }

        private void chkFilterOIDRange_CheckedChanged(object sender, EventArgs e)
        {
            textEnable(txtOIDFrom, chkFilterOIDRange.Checked);
            textEnable(txtOIDTo, chkFilterOIDRange.Checked);
        }

        private void btnPrintPendingOrders_Click(object sender, EventArgs e)
        {
            var dto = compileReport(true);
            if (dto == null)
            {
                MessageDlg.Info("No lab orders found with matching criteria.");
                return;
            }
            PrintHelper.PrintCollationPendingOrdersManifest(dto);
        }
    }
}