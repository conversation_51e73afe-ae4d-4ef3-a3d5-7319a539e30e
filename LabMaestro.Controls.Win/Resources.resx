﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="search_16" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAldJREFUOE+l
        k21PUmEcxvsIfgQ/AuMlr/gGaA8wtkrANTCUB2eUgBsYgpYm4DwUMrIa6Is6TGuVs6Q3NJsS2cYylxU+
        TQE9ig+o4Lm6D5xmTF1rXdu1c5+z+/rtOvf5n3N/ytHTL3b7n0Q8gXCCevyMsTg90cab7Qal1lTFbzld
        7Xf7qjo9/shAeASjE3FEp+YxQUy/+Ygu3xBU+jbmfF2jgN9+Us7e+5HQyFt8/pbFcraIVYbFygaLVLqA
        +NcN3LpHo67JytReuX4S0u17VNM/+BSzqU1ktoBMDsgSc+s08eomMDaVgdEVglx9I8rHjmXpcEeek9pc
        YDEL/EwDS+tlwG/Q3AoLiv6Cay0O1F7VVvPRsszOPmY6uYj1bWBzF9gi5tYcLJUhLUiD72ukRXwLOpsP
        0nqjmI+W1WC6jenkQim4nQd29svX3B6wsQP8II1ml4EPc4docT3E5QZTJUCha0uMx2awS4L5Q2CfOH+A
        0j0H4VrNLgGx5C7MdwKo11srAZdU+t4uX5iEjlA4AgpF4KBQhnBtuIOcWwFevF+ApZNi+NixtJbuau4T
        vZucAcuyYAEUCYhrkiOvwp3Fp/k8qNArtDrcNj5WqQuKphqV0Y7R8Rhy23sEwJYaLK8XMZlM48HwGFo7
        vDC1uyMymez0gbqo1AlIk2iz3YPA8EsM0hMYGH6NHv8QY3Z6bc12b8JssUIikTAajebsqVTpzAK92akg
        82GzurziYDBY+g9omlZQFAWpVPp3yFlSq9UVELlc/n8QkUiU5h//m5RKpcJgMEAoFCp+AXfw3IBZQRsL
        AAAAAElFTkSuQmCC
</value>
  </data>
  <data name="flag_red" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAALRSURBVDhPlZNbSJNhGMff6yC6N8iz7qRzLnd0KhJI
        YEdEKzS6SNumyyMkgrh05lhpoYZQBElBuYM0TWWZfnPzcKGhmRflhRdpkSiF8zDndP/eT6cZFNELP154
        v+/58X+e73uJtVZhNxvksJdInrulvIIhaew7JikKg+IIDCZGgBFHwimN3XSpBMbhVCH5jbQEQix6Oab7
        tHALY/ApPxuLzQZsuvqAr3PA0gLweRZr3S/hlERjNENGXAqeyCXn5AzJOG/dqjgQs16GqR4NGH448H0J
        XvMjeJ82wt9SDbSb4DOWAovzGBCGwp0Sj4mrFzB54wpWpsZB0+4JJnvUVBCGLfcbLJ0TYUWTCZ/+OgL3
        K7BRkoXtcRf6+SHwzs3C/20B2/M03Ze53faCAppAEAHv6w4sZUng0V2Er06NQHMlNspzsDXQBQf/OGBp
        Q8CghudaOrbGBsGcjAoKeqkgLhLrz9qwnC2H5+YhQcVl+Drb4aAtsMU7pmJ4Ck7D5+gEI4k5JBBGY7XV
        gOVLSqwWZ8FfrwUeVmHzVi6dyQM4RBEI3CnEzr1SeNSZdFZPwBzMgAqcolh46svwIzcF3ooceGvy4dGr
        sVmZB19rHfrF0Qg0FFFBGTyaM1h/fBeMjLsnmOrVwiXm4WM6DxOnEsEoBWAU/CD0LD0BA1IOYNQh0FSO
        taLzWG+sglPJ3xO8txXALRWgM4lrr+ac0BBCxPu4U4Vw0c/nSuYDND5aKrGsPouZDBGsEq6NmGtkmO7W
        4kWDHLQgjHJ0ODVh9y9jqeOH6dxpCX5W4gwmsyVxHbrIkDz6buiewK5BR60MZlMyGZPGkxEVLQ5K6DpG
        SaQcpKKEU47sPmcFH15pwO4Wg5KYTSoyJoknowohGUn5leRvEAstnOkqBHsnbLVKYqUSizGZdDSpyKiS
        vTR/LtyHWG8rdovZnRXsS9h22BT/FAR72uc/FyE/AaGdBUdjS7x/AAAAAElFTkSuQmCC
</value>
  </data>
  <data name="list_accept" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGOfPtRkwAAACBjSFJNAAB6
        JQAAgIMAAPn/AACA6QAAdTAAAOpgAAA6mAAAF2+SX8VGAAAACXBIWXMAAAsMAAALDAE/QCLIAAACzUlE
        QVQ4T22TX0hTURzHz0M91lP0R+iPpVRQWOaDhtGDlfbPIkWKHGVSSQj9IbCCIKjMP5m6ual3zanb/DPD
        0EIUip4i7CGyB7ViKHPb3eY2t7m5rbvt2zkHbmh44MO5l3O/v/P9/s65xGg0yij6+vowODgIOgcp/tUY
        GBj4tExDiMFgYA/l/f39mJ+fx9jYGKxWK8LhMCcUCnEWFxcRCARAv5WYRoaJb5jNZni9XrDBCrhcLiST
        SSQSCU48HkcsFuOFqEjq7u4mMoRags/n42I25AKyWJIkLo5EIggGg6Aiqauri8gQk8kUGB4eZtY44+Pj
        K8TRaJSLWRy/3w8qkjo7O4kMyyGyhTL1NCp1v3lW1g/aMPT09IBuwAvTneF0OqHX66WOjg6S07B38kB1
        ajXLwQv0f3ZhaCLwLy/bdWlpiedm1hcWFuB2uyHoBCn75R5LxdvCUFZtup/ZEFkP/s/LLMudZ2KPxwOr
        3Yr8xpzk/dFLsYm4EQ8/KOKE2uEFXg1boRmdWyH+ZZvmDWZiu2hDsfYE7oyU4MsfAQ8+Xk5m1ab5iU6n
        E9n5X1NP4V6vldtkeYuU+cht2IdS5QUIrwUUNB7GrZHzGInWwex7gsyaXcH9z7ZvIFqtVmTHtjzvVUMR
        rgzloytUhZPabJzSHEHZuwL+3uq+jYM1O8NUvFGj0RAiCIJIBxzzfthcCzzvzd5SFL85ivpAOeeRpYTP
        T50KZL5IAxOr1WrCIG1tbaLdbsd1zSQqtVM8L3NU0avAWXM2qrznOHdtp6ntNNSr6iSVSkVkSGtrq8ju
        /mPTT9S8n8Ps7CxoLLS3t6OwMQ/HezNQZs1FxvNUfP0xDiqSlEolkSEtLS2emZkZ/iOxi+JwOGCz2fgP
        xYpd1J/Bodp0fJ/6BovFgubm5kTKjk3rNm9Zs5Y+8wITNEtkNejaajjyjuXt3rotZX1TUxP5Czt16Za5
        nkKXAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="tick_16" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAkxJREFUOE+V
        kl1Ik2EUxx+0UCN1Jd1EFwUWUZFrrNSWbukqsy6qmyQopAsr+kQKmlG9hHOOlvtwOrec7ztkttFwbTad
        LhKiLqRCwbnC2CaSQ+bHLkbX/x7tJTTWsh88PBfnPM/5/8855L8wk+0Zlgw225o9nM/lsVu6CwR8ZA2Y
        iYA+jp4bOo2bH+pwlt7berYO89F/k2nJZCp9UugnmqH4fBv6kBp7XIXgw+lZ/3ydUMDl49mEEvWf6paP
        7ouKfrBrbR/kUM+3Rq6gdVKNqx8vwBox4uK78yjq3avlU/7Ohq6cWpG7CNyUCfeD19Ee1kAZfAihe19U
        9Go/38Q2IiQmUks66L2CXHajgHY7YaCVWyMqaMIMbNMdkPVLIPYIZb+y2oi20L4DJ/vlKOA2gXb7tyzq
        m730vgY9M51ojjSgN27HtZHLOOQ9sEJ6O0HT2CNoaYNaQk1YkpvVmcVutglku1/uhGu2G8bvKrjmbDBG
        NSj2iqKlfeIV8zcRGOhIHozeATN+b9nricEK0CWB+usTOOJWsHEDPAsvUBWoxOHXB1fZJNS3tsQjhvmb
        DsqQAk8nH8MRY8EEFehbcKJrTo83SS/ujt3AEV8xw79azdJ6Sn0S2KY6oA03QkX9eucdsM+bMZB0gY2Z
        UOYrGeXTU5PH5bKnAsfhjHGwzLRAH2uEM2HFYNKNM2+rcXRA8of0FNDdZmklOGc5uBY5DP/woWG8HpX+
        stTSU0HXk60OyOFddMI8rYPcX55eeiroqJhyX2nimF8arRqqSCOdkJ/Vkzelnz8vZQAAAABJRU5ErkJg
        gg==
</value>
  </data>
  <data name="cancel_16" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAIrSURBVDhPjZLdS1NhHMfPH7C5nePZTsw13CpsFVSE
        wzcWZJFdBDoKoqBANDBDRhPqwupmYJywFaP1QtDoxVAzipBleVEEmRIp0QpaK2IjKaO2deXVt9/zuLPO
        WbvoCx/OeX7P9/PwXDwCAAOTikkiThD3iC9ElnhAnCJs5X3DIqGYBqdW2ZHs7kDmTBC/Rs4hNxZBRj2G
        9z17MbVmBaij6p3Sz4Riik7vqEf+bgSFq0Hkzh7Az5M7OTl1PwqX+1AYj2B2dwuoGzMc8NBuDk+3NSJ/
        4zQW+7fitsOG78FGA3wW8iMfH6BD/CCH30S4bzfXTLgVLF7sx8LRBiTattMc/LvQ6+OUz37EQkjUOcFc
        YdxmDs93BZA90oRM9xZe1MIETdbCOtmeBsx3toO5wqitau5jMIDPBzdyvkX6itV/w/a0Xjq0B+QmhWG5
        ail92I/UvvUlvqq9ReVv2EzfSXc1g7nCTdny+227F+866kqUX5uFzfSdZMALcpeEuGyZm9nmwZtdy1SS
        tbA9rfeq1QNyU8K1akv4Ub0Hr1tdHH3SA50cfbTeE58b5EaFK9XWmrhTwcsmB2ZbHPhw/BAvsi9bV5rN
        NDtw3WkHuev4Q4pJVvVOrYwXPuW/GHHLIOc8c0tPOSpZY7dWini2ScbzzZVhe8MuCayreaUDGBckUY3Z
        JIzWiphcK+LphmUee0WM0eySXQJ1hvSO4QBGRBRdxCCRIlDkEzFErDb2IfwBOK+BSDw3WyIAAAAASUVO
        RK5CYII=
</value>
  </data>
  <data name="LOGIN_INVALID_USER" xml:space="preserve">
    <value>Invalid username!</value>
  </data>
  <data name="change_password" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAALrSURBVDhPbZLdS5NRHMfPn1CZaYlFf0DpnG+l7pmi
        WOb0wotuwovATKGGTrfp5pxzTSumIKKZkCQEQaViOveirqkL37bhy9KsTOzNiy6sVJbNffs9kwzFBz4c
        fuc538/5nec8bFgvOpQhvZjZakU6q1YEq5aDtYaDTceB5nXDhlRmr0sLcni4NoVZq5O/utuysOUuBN7d
        Bj5I8XuuCDOPcjBYK/72T0KL+cB+KKxzt12hYAl2FqVYH78BF9WehxJsunlJLgapk11BTco+BrXJzKxK
        hG9OCv9bGXaWZJhuzQJjTMjjaZfgz4IseJxhAwlG6rl9OOo4klyk3cvgX1QgsKTEVEsWzOrESLPqQqS7
        LRv4WA6rJhnDeo5RQLzHKx4DR0dIArxS2l1NqPBj8jZJMuFqycDmdDG9u4mBSiFs2kTGzBXRzFIpZBZV
        LLNVxTML0a+MQ2DyOgLva0hQRd1UAD9NAL4Qn4GtBSz06LDUd4dRWMCsFLaq45hNkxAU9MmFCMzcwtbQ
        VfjnpcD3bsx7HDDcb4ZG34h6YzM62h/09nY95wUxewKrOoGZlLErnrbL2PGWwufMx0YXB/zyUtCI7qeP
        gbVxPOnsgNbQCE1983+BpTKWWheueloz4J8txLanAP6ZAqy8yMH6yiyK5PWYsJs/PWuQ1kREnuVKNE1Y
        bji+K+DDJkXMqqs5Df7pfGxPXcOOKx+9MgH65DFY7swMeO+GwnsvDIvGU0EWjBF4YzwD+ogC1i8XrE43
        ieB7nUdt56GnNAr9CiHMqvjg/U/oTyLgEAFjYlw6xwBnKgIjHMb1p8FMcsGauykJWw4JNuzZ8I1KQMK9
        H8dZG87s6jD6+hqSiFGczoIjX9vV4WD95VHwj+cGg74xCZx1iTAphbBU0q1UJ7NB9Qk2oAil3dOAUcKZ
        vjtSzc9TB1Hok53HSxk/RsOkiA7ublHx13mECGG9ZSHoKTmG7pKje/A1P09rd1s9wMHnsDUEE/4Fom8q
        z01mv4MAAAAASUVORK5CYII=
</value>
  </data>
  <data name="exit_16" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAf1JREFUOE+l
        kt1LU2Ecxx8IwW686P3CtDlLlrrZMp0pJmm7kJWjBvONzWytmHqcOncQXxhDxcgbQQRBaoJoeBPWQInl
        pgOZC3UgBiFI3XWVf8K337OJeuYRBL/wOQ/8nuf74XCew86dH6IWZ+WgIg3f2Bo1IOR9spdygek5NNYR
        aiKbuEFcPFUQdd9DyHUff7+ICH/oCJ4mMIxeAptkJyXv3XYsOzX4NfII/4LvMNGsCdJYKhhnM/GynCDi
        KkBAUOOnV4c/vpd4U3SFHzoSDLJ2XhyODSYEyax1avCtNR/bnmL89tmkgl4m8kPeLQ9c0Q44I63o2xDh
        jQ1AWH8L1kWCsDMfi45cxPoLsTvZKBVQ2bM5AP3SY5T6i1C1VA5DQA/zihFC1J4QrLTlwW9XYaNHi50x
        k+wbNITMqAlUwxKuhX3NGi/3x7oTgmXHXXx+lYOIS4MJdz1E6zPZb+Bcb0kUkhnpsmHeko3V9jzMCRVo
        ri6RCo7fAhUo/HEIW7TdxmydAt8dKqyKpXj94PJJAf0HVUNp8oKvTUr4TBngooCghUOXdI0HAn6OVkk5
        LuD5+Dx9e8GigN+mQlvJVT7lZRWhJDIILkkh5AVTxvSsT7WZ+wtWJTrLrvFpJVFOPCTuEKlEPLICnukX
        N43zdbfQU3GdT83EU0LB945HKgD7D4ytYxOsYMAnAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="calculator_16x16" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAGLSURBVDhPfZPXbsJAFET56XwJmN4MUr4iEv2ZanoR
        RRG996KB2bCWlXVi6TzZZ+7uXNkBQBD/eRCLxUx0XRdEo1FBJBIRhMPhuPSsARiNRoLH44H7/Y7b7Sa4
        Xq+4XC7o9XrodrsIhUIv5VcAJw6HQ3x89v+l0+n8HTAYDMRH2te3LXzXbrcRDAbVAN6137efaqXZbCIQ
        CKgBLIkBvOv5fMbpdBIcj0ccDgfs93vsdjs0Gg34/X77AJZEOZfLmWImkxHidrtFOp1GvV6Hz+dTA7ge
        Nkw5m82aMpEyqdVq8Hq9asBrt6Jh63Epks1mg/V6jdVqhWq1Co/HowZwNWyYspxKkVMpLpdLpFIpVCoV
        +wCuptVqmceWMiUpJ5NJlMtluN1uNYCrYcPW41JcLBaC+XyO2WyGUqkETdPsA9gwZTmVIqdSJIlEAsVi
        ES6XSw3gbtkwZSJlMp1OhUwKhYJ9AHfLhg3DMI9LKE8mE4zHYyHn83k4nU414LXbONslLInwroQTCcU3
        798ZjifgKz0KQADcQwAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="flag_green" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAKjSURBVDhPlZPbS1RRGMXXcxD9AT2UrwmBReWMaXmZ
        cRAvL2YhBEUk2KBCL/WQOuN00XEas6tWUJKVqWcuQeqUtygxUxusaZBx8G6pRRYaBVKr78wlfKigDYt9
        OPvs315rf9+BYk5wu8sTedWypTHNhgJ9FYbTzoEpZ8HkM+FZ3n3PPI/KbDuQXRORPOfIjNZyDcdcJdRa
        wRPOrbw7eJje91f4+Ucnv/JJSH2TVuoqwX2X1iHDhjhDNfYbrOgUKEOAgKOIu+Skbz8X2Dt1kh0TuVSC
        MXz8Lobtcxu5vBpgkgXMtIHHbsfyeONO+mZ7VWcRgCIAsev/qLC0C7S/BG++BVsmwPvjYHDJSU05OLM0
        xA8rfi6ujPDLqo+pEjUcQQDx4mBg7hpNPWDtIHjLDyqT4AOBjCzWMb4MfLWsY0MArOoHA5+UtYBiagTQ
        PWGmuRe8OBQGOKbAZoH0z1ZQI4BH82DjGGh7AXrn66ledhjgKKZWAO7RQlqegpeHwYZR0DUtLkRd40Ym
        SIQ2AdwTgH0AfDZtoS4KCApgt9xyky+XVX3g9dfgjRGZvQIQF23jeUwyg+0LYUCN3FFH0Ei97IkASpgk
        ZTzaBObXg+myEJVeTjlQJ/1wGvQsyiHBcETHaB4NahVaBOBtLaBOSrS3DO64fBRKe2yPKssu5ZN6Z1SD
        PUugcwa0PgeP3BHoKSgCiKffZWRb6R7Khs2i9WqnqV2maschFEnXraoQ9UTVlWz0xObgoHy7Cc0CeOMq
        pLNMS48pBam1QMYFICsCkLFBtE3025UoRrQutB4FqE7c5kR4TKlIFki6QDIjkH8pFMH30CgADeXHgqsi
        EW3mZHSbdDAIZG2cPwmtZi1bTBqqs1KRAFUqpEPi6MVJNMrfpI612f5zAL8A8cfSMFVbYL4AAAAASUVO
        RK5CYII=
</value>
  </data>
  <data name="date_next" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAKUSURBVDhPpZFdSJNhFMePGoUZ3QV1F+2iEAZCNNeH
        xjJr6QTtxgqGqO2q4XQzR+p0aurUwr4tlwklQRCUcxYUS4W+wIwoywj8qLZEsc3tnU2b27/nfbYs6abo
        D7/3/zznOedwDi+9KcmmcGYCIT2Wrt9zoPWmDdi/ghZVawn74n6LxfE8Mf+dTkUjukwOvTbkIKxcCewh
        dN13oKilDUgnhMRY2vJYWLkKYv6wIZveRiGvPAbBng7gziV0PeiHqqQGsF3Bos0K3G1D18OBpVjI3gmf
        PA7+XfHkT4lAkxsJHt1BuNYRulcTbGsIkxsIn9cTvjC62XZibIqdv+nVcG+KgXdzPPm2JHDIYr2BEIBx
        jx9OIQCXfwGfmE/4AvjIcPrm4RK+Y2oOqH2Uh21nCTsvEqVeJtrNoKbWc6wcmJyegdsrwNE3gK/M3V4/
        ZlkDn3cRghdYEABZLWHY2QlFE0F5mkh1hjVobD7FG3hmZ6FQKCCVShEKh7FdvgOa/GOQFcYgmRXKzIQT
        twjX+givJqxIayRksiZU19jEG/gEAU6XC0lJSfx+KPcw9xQLYdB5BM8m1Hg6VoD2x4SrTwjPxxqgbCZQ
        TV0DT/TPsSWZxAlm3G4cLdQgOyMXqWzcoWk1Ol6ywheE9kHmQwTHh9JIgypzHS8MBOa5JyYmcv8puSYW
        crZCcjVboZfQOULoGdFgL1uBLbCVyivNPDEYDHKXSCSw2+3QarXIzyuAvsjI46JkVYTb77OWirNa2ddY
        URl9Xq7R0VGMj41HbxFZenOQGi0+wP5AhvgXjOWm6PPfyVTRgpM156m+9gKHdPrj/QZjOYoNZdBFEc9/
        YkSpsQLF+rJ+U3U9mcwRREnEkf4BMf+X2FT/AegHxdx2SopYyv0AAAAASUVORK5CYII=
</value>
  </data>
  <data name="print" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAGJJREFUOE+1
        kVEKwCAMQ3v03nwSaSVjGrS4B0HR9H2oMe7+rBIVDYoztiVKkGtU5ygBJ+pfVgLmHwEOKxnDVbokBWk9
        4SWocF+A/U4S7I/fgHtYx0/whYK7fZjhS5Wo38CsAVi2dh9REF3kAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="LOGIN_INVALID_PASS" xml:space="preserve">
    <value>Invalid password!</value>
  </data>
  <data name="calendar" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGOfPtRkwAAACBjSFJNAACH
        DwAAjA8AAP1SAACBQAAAfXkAAOmLAAA85QAAGcxzPIV3AAAKOWlDQ1BQaG90b3Nob3AgSUNDIHByb2Zp
        bGUAAEjHnZZ3VFTXFofPvXd6oc0wAlKG3rvAANJ7k15FYZgZYCgDDjM0sSGiAhFFRJoiSFDEgNFQJFZE
        sRAUVLAHJAgoMRhFVCxvRtaLrqy89/Ly++Osb+2z97n77L3PWhcAkqcvl5cGSwGQyhPwgzyc6RGRUXTs
        AIABHmCAKQBMVka6X7B7CBDJy82FniFyAl8EAfB6WLwCcNPQM4BOB/+fpFnpfIHomAARm7M5GSwRF4g4
        JUuQLrbPipgalyxmGCVmvihBEcuJOWGRDT77LLKjmNmpPLaIxTmns1PZYu4V8bZMIUfEiK+ICzO5nCwR
        3xKxRoowlSviN+LYVA4zAwAUSWwXcFiJIjYRMYkfEuQi4uUA4EgJX3HcVyzgZAvEl3JJS8/hcxMSBXQd
        li7d1NqaQffkZKVwBALDACYrmcln013SUtOZvBwAFu/8WTLi2tJFRbY0tba0NDQzMv2qUP91829K3NtF
        ehn4uWcQrf+L7a/80hoAYMyJarPziy2uCoDOLQDI3fti0zgAgKSobx3Xv7oPTTwviQJBuo2xcVZWlhGX
        wzISF/QP/U+Hv6GvvmckPu6P8tBdOfFMYYqALq4bKy0lTcinZ6QzWRy64Z+H+B8H/nUeBkGceA6fwxNF
        hImmjMtLELWbx+YKuGk8Opf3n5r4D8P+pMW5FonS+BFQY4yA1HUqQH7tBygKESDR+8Vd/6NvvvgwIH55
        4SqTi3P/7zf9Z8Gl4iWDm/A5ziUohM4S8jMX98TPEqABAUgCKpAHykAd6ABDYAasgC1wBG7AG/iDEBAJ
        VgMWSASpgA+yQB7YBApBMdgJ9oBqUAcaQTNoBcdBJzgFzoNL4Bq4AW6D+2AUTIBnYBa8BgsQBGEhMkSB
        5CEVSBPSh8wgBmQPuUG+UBAUCcVCCRAPEkJ50GaoGCqDqqF6qBn6HjoJnYeuQIPQXWgMmoZ+h97BCEyC
        qbASrAUbwwzYCfaBQ+BVcAK8Bs6FC+AdcCXcAB+FO+Dz8DX4NjwKP4PnEIAQERqiihgiDMQF8UeikHiE
        j6xHipAKpAFpRbqRPuQmMorMIG9RGBQFRUcZomxRnqhQFAu1BrUeVYKqRh1GdaB6UTdRY6hZ1Ec0Ga2I
        1kfboL3QEegEdBa6EF2BbkK3oy+ib6Mn0K8xGAwNo42xwnhiIjFJmLWYEsw+TBvmHGYQM46Zw2Kx8lh9
        rB3WH8vECrCF2CrsUexZ7BB2AvsGR8Sp4Mxw7rgoHA+Xj6vAHcGdwQ3hJnELeCm8Jt4G749n43PwpfhG
        fDf+On4Cv0CQJmgT7AghhCTCJkIloZVwkfCA8JJIJKoRrYmBRC5xI7GSeIx4mThGfEuSIemRXEjRJCFp
        B+kQ6RzpLuklmUzWIjuSo8gC8g5yM/kC+RH5jQRFwkjCS4ItsUGiRqJDYkjiuSReUlPSSXK1ZK5kheQJ
        yeuSM1J4KS0pFymm1HqpGqmTUiNSc9IUaVNpf+lU6RLpI9JXpKdksDJaMm4ybJkCmYMyF2TGKQhFneJC
        YVE2UxopFykTVAxVm+pFTaIWU7+jDlBnZWVkl8mGyWbL1sielh2lITQtmhcthVZKO04bpr1borTEaQln
        yfYlrUuGlszLLZVzlOPIFcm1yd2WeydPl3eTT5bfJd8p/1ABpaCnEKiQpbBf4aLCzFLqUtulrKVFS48v
        vacIK+opBimuVTyo2K84p6Ss5KGUrlSldEFpRpmm7KicpFyufEZ5WoWiYq/CVSlXOavylC5Ld6Kn0Cvp
        vfRZVUVVT1Whar3qgOqCmrZaqFq+WpvaQ3WCOkM9Xr1cvUd9VkNFw08jT6NF454mXpOhmai5V7NPc15L
        Wytca6tWp9aUtpy2l3audov2Ax2yjoPOGp0GnVu6GF2GbrLuPt0berCehV6iXo3edX1Y31Kfq79Pf9AA
        bWBtwDNoMBgxJBk6GWYathiOGdGMfI3yjTqNnhtrGEcZ7zLuM/5oYmGSYtJoct9UxtTbNN+02/R3Mz0z
        llmN2S1zsrm7+QbzLvMXy/SXcZbtX3bHgmLhZ7HVosfig6WVJd+y1XLaSsMq1qrWaoRBZQQwShiXrdHW
        ztYbrE9Zv7WxtBHYHLf5zdbQNtn2iO3Ucu3lnOWNy8ft1OyYdvV2o/Z0+1j7A/ajDqoOTIcGh8eO6o5s
        xybHSSddpySno07PnU2c+c7tzvMuNi7rXM65Iq4erkWuA24ybqFu1W6P3NXcE9xb3Gc9LDzWepzzRHv6
        eO7yHPFS8mJ5NXvNelt5r/Pu9SH5BPtU+zz21fPl+3b7wX7efrv9HqzQXMFb0ekP/L38d/s/DNAOWBPw
        YyAmMCCwJvBJkGlQXlBfMCU4JvhI8OsQ55DSkPuhOqHC0J4wybDosOaw+XDX8LLw0QjjiHUR1yIVIrmR
        XVHYqLCopqi5lW4r96yciLaILoweXqW9KnvVldUKq1NWn46RjGHGnIhFx4bHHol9z/RnNjDn4rziauNm
        WS6svaxnbEd2OXuaY8cp40zG28WXxU8l2CXsTphOdEisSJzhunCruS+SPJPqkuaT/ZMPJX9KCU9pS8Wl
        xqae5Mnwknm9acpp2WmD6frphemja2zW7Fkzy/fhN2VAGasyugRU0c9Uv1BHuEU4lmmfWZP5Jiss60S2
        dDYvuz9HL2d7zmSue+63a1FrWWt78lTzNuWNrXNaV78eWh+3vmeD+oaCDRMbPTYe3kTYlLzpp3yT/LL8
        V5vDN3cXKBVsLBjf4rGlpVCikF84stV2a9021DbutoHt5turtn8sYhddLTYprih+X8IqufqN6TeV33za
        Eb9joNSydP9OzE7ezuFdDrsOl0mX5ZaN7/bb3VFOLy8qf7UnZs+VimUVdXsJe4V7Ryt9K7uqNKp2Vr2v
        Tqy+XeNc01arWLu9dn4fe9/Qfsf9rXVKdcV17w5wD9yp96jvaNBqqDiIOZh58EljWGPft4xvm5sUmoqb
        PhziHRo9HHS4t9mqufmI4pHSFrhF2DJ9NProje9cv+tqNWytb6O1FR8Dx4THnn4f+/3wcZ/jPScYJ1p/
        0Pyhtp3SXtQBdeR0zHYmdo52RXYNnvQ+2dNt293+o9GPh06pnqo5LXu69AzhTMGZT2dzz86dSz83cz7h
        /HhPTM/9CxEXbvUG9g5c9Ll4+ZL7pQt9Tn1nL9tdPnXF5srJq4yrndcsr3X0W/S3/2TxU/uA5UDHdavr
        XTesb3QPLh88M+QwdP6m681Lt7xuXbu94vbgcOjwnZHokdE77DtTd1PuvriXeW/h/sYH6AdFD6UeVjxS
        fNTws+7PbaOWo6fHXMf6Hwc/vj/OGn/2S8Yv7ycKnpCfVEyqTDZPmU2dmnafvvF05dOJZ+nPFmYKf5X+
        tfa5zvMffnP8rX82YnbiBf/Fp99LXsq/PPRq2aueuYC5R69TXy/MF72Rf3P4LeNt37vwd5MLWe+x7ys/
        6H7o/ujz8cGn1E+f/gUDmPP8usTo0wAAAAlwSFlzAAALDAAACwwBP0AiyAAAARlJREFUOE+djF1HRFEU
        huc/nf/SxZDEOGZUYro4JMPMxVwVFUlJKUOpiC4imr6/1M0QqXFyTI2j48xHKd2+WYu9zdrti85sHvtd
        7177SQFIqeO6Loj/Zj4kUGU/aMHcZVuXxcN3nfN7rzpnKi86Dyw/8S0E06cxytWIBVMHIbz9JgvGdhrI
        bQUsGFr3kV6t/xWUd+9R2r5DYfMWk5UbeBvXmFi7wvjKBUaWzpFdPENm4QTD88cYnK0iPXMkBaN5LzFC
        EHV+EiMEYfzNOI4jMHs1E0LwFn0xtKSyOZtvQhCEn1boky0TQuA3PzS0qDD73lkInhtdK/TJlgkheAza
        DC2pbM7mmxA8+C2Glnqx9aoTglo9TowQ9IeLX6XfvWSnnOg7AAAAAElFTkSuQmCC
</value>
  </data>
  <data name="clear" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAKASURBVDhPlZFbSFNxHMf/xwuWFikqhSYRhWQvhoIF
        GV20ixYKPkgkNLpRoiPwIbtJF6xE2/ShXsp2ZmEkkqikBKZktiHVMlSczaORbi43p+TlLJxn335n4Jhs
        PfTwOef/P+f7/fzOhV09xvbePM5MN7LZBK2PAmD/A6Oi5WVhOBqVEaD1IkkyAwX/hSxwtJasQ3dZJBqK
        wmWJqbKQBdVWsbtEdKCSL+xaFrt8Py944d31DR5JeR439+Qe+0plEEmBSr7I3yCIpj6+ncMttF6JQF0F
        h66mFHeLNmGOBPsClXzxLh6cZ3dqK5jb0HkQ9rFSNBdudGrTmFlbzfU/e8hKSRbrW1zBc6CbpzQqThzu
        zcW0cAm9ylQ0xq+HseUsbEIJvnRmOJ/XrBV5NcdTNj6QQKFRMXtHfZLYkb0Db/ckQBw+A8l2DpK9GNJM
        Gf7YKmDoOrzEV4c6eTV7QZ0DRJDXRJuwuu3M+PpQ9LLLVg5pugjSrHxWQprKg8uSifmJC7CPlqKvO8vd
        pImfp1ezeQUeyS2meN+cvCjNVtJ0BU2np6CyNJmO+ZGdsH7bBEEfCXN/FsYHL4JXcVOrBVUsrv5RlNPl
        qMKyJR0u826IY8mYMW6DxRCDkZ4QmD4EY3IoH/r2VBe9yqtVAhmtmhsc7TsNh+mIZ+LE5ygIH8M8RRnz
        wAkIhpPg1SELNHCzn4AuZjQ+jRN/mYoh6CK9RUEfC6uxAEO9+W5tdehvyu2X834CGXo0na4tbck6rMCP
        T4mwDObA+l0J3ZsUF/2tSSonrmT9yjIUiKbgeH9PrvRzoAD6tl1LdTVrRF7FGuhejG92VdEXCm6h3+Qg
        0TShof1W/xzYX/hDlR81LFHxAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="account" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAIpSURBVDhPnctPSJNhAMfx56D071IHo4tYEGGUax1y
        RH/oEB06dKg2IupUITgKhk1HZlbL8BDNtrfeNZvpWjJZTVGczKHDxAbOlcm0ckbmZrNtbutpa2tSv95X
        2MIYhT7wgYfn93xJh8Mh5DAcukx8IySW/n5myu9PJxcWsBx8w7fkmd1Ov6fTWAm+JebeXqxUm81GSavV
        SmkqhVy+hIPQN8hRe/EorpQdQmujcsnOt8TQ2UljySRyadJUofLcQcjOiiCVCHD+2FZ0WbTZnW/J4/Z2
        GkkkkItUXAxVxQG8sT+Es0OFCkkhLp/Znt35ljSazTQUjyMX2fECJLxd+DryCDMOBhN9OtSdzue2b4s7
        3xLWZKJzlCKXl2wpIi4NIu4nmLI1YOx5NQZV27I73xKN0Ug/x2LI5e0QC9f9EkTdTfANPIDz3k54+m5n
        d74lquZm6otG8bcBbxhyyyzabu2BvSYP9mt5eKrcB6nJj25PaPEP35I7ej39ND+PDPt4AGUtkyhUeCBQ
        +fBuJoSeF050D7rwfjoAoXoWm69OoNwwiRrWECf1Oh39GA4j44LWjR3KcYi0cxAwAdwdosgcFXcv0QRQ
        ym3FtWM4ojClSB3L0g+hEDI2SVpQVPkKG2V/bFGMoqhqdOmbfAQFJ3Q/yU2GYWzDw2lvMIjX036sP1yP
        tWIr1pz8t3XiHqzefyNNrqvVQg7DoTzRqeofq3ZLf/1P/q7y9Ia9l4y/Afk1FwHFbaOUAAAAAElFTkSu
        QmCC
</value>
  </data>
  <data name="coins" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAgVJREFUOE+N
        kd1Lk3EYhnfYQZAU9QfUYYarWFOQIGYfFha5SktdKIxoZA5ZA83sY0W10VCEimaLtM1qzSXlQkwHUURi
        TFAjWrmtUvqQjVF50KSrd6+bazTnHriP3pvnfa7rJ1lsxjt2SIWcSRNpvLLwCCXPh4f7CQ3XEZ1ogqCQ
        qbP8em9msv8Yse/xavoJdiuZ/XIdfnT9n1AH3c0FxKvp56Pw9z+jOmYjz+D3a5gZgJ9P+ORt5Y2tCLt+
        U+YFgTtb+ebazfQLHZHBCsaurqOtZhnm6pVYjufiTFwQExIXM5+3tp1G/w05kd4S3t0qwm0oxHlxF3Zj
        GZ3GQwy7NHMIQjmjqAnHHiaf1jLyQMPgtQqi4UepCNmI8t3eznNDHtbKVVwqW5qKkI2o0XYFPU1yzDV5
        tNRuTkUQFoSn+1VEvM1pRTlO5ovFBMJQ19FUhKnH5TlCCA2oGHdW09tSjv28EktDMT1XlIzcLBSLCYT6
        LUswqVaIy+82yOckBl37yHTFvcZ8EeH03tWcq5TSWq9IIgScSqKf25jxmQi/0jHmUnPfsE18LsupEix1
        MgJuzTzCy041o259EiEbif++QgzhwoHl4mU2vSy8qMRYyWdVeBy69d7LVWvRFq/xnijN9ZjUG7XtWlmO
        KPF7X5X2a99hz5C11B8733ikwN94cEOytOBIJH8BuBc4dUnxry4AAAAASUVORK5CYII=
</value>
  </data>
  <data name="Counter" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAADHSURBVDhPrYwxDoJQEEQ5BjWxNIRQQkNHQUGBlLS/
        Ua+gHsKbeABLb0HtLYadYg1/EyObOMnLzm5mJxFVwkN4OWC+AiBDlhACLtfbZpiXv7cWzNM0wQv/PgXj
        OMJLVND3PRTVr1tU0LYtCGW9TuujgqZpQCjrdVofFWRZBkJ985TuZF1wP57O8MI/LZjzPAehrNdpPf9k
        /VNBURQgGqI0aG+ajQrKsoSXdcGz6zoMw2EzzPNPC2ouwuyA+VoLUmEn7B0wnwJIFiAx1P6r1bJ1AAAA
        AElFTkSuQmCC
</value>
  </data>
  <data name="catalog16" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAzVJREFUOE9l
        kllME1EYhVETNT64vvgm+ER8MXGJGkNiNPENE1NoKbalFNFEFKG1C61CjSy1iYoKxqhIW0QJRqvIIlZZ
        LCgCgmAHpCBlKd3EiNAG7JQ5zoyNCJ7k5ube+58vc/75I5ZKpZLt1OSoSy7kqIms84pppVr+S5ElA7Mz
        Z+aeeVdoMneFLQtSqc4d1F/RBdo72ubtQwNwuV0Yd47B4RiG0zkOr9eDCdcE+voJSn+1IMDUh61/JFfL
        Mnt6P80HAn7WSBeiqbkB5Q/LUFtXjY7OD/DQEEaDg3ZKmSVTsUaxWLw6LiEuRnJcok89mYICXS4Kb1xF
        ReUjeDxuzMzMwO/3s2tqagrT09Og40Aul29mAXw+Nzaex6kKBoOzk5Pf4PN5MTo6AqOpFEXF11FhvI3q
        6mdoaX2Lz7ZeeH0eMH1hzYzoL1omEgukVqvVRpJBhEIkSJLE3Zs6vBGsQ5cgAl3C5ejOiELv81tsjEUA
        RvHx8RtTT0jMLrpJrxssGLL3o1Cvhdd4GLMVB/DTGANH/g4Q0kg21iKAVqtdLhIl5lgsrxoH7AN4ZalH
        Z5sVL549xuOSa/jxsQrf3z2A9+kFEIpouD2uxQBhkjClqLiohqIoZ9uH9yyg0nATg7YOPMjh47V0G5zp
        KzBwnI5Cx2F+718Ah8PZmiE9Y56dCzhCFEm1+AzI69sNZWcULjbuw2hnLQrTDuFL2qp/ABMLgOSUpGyC
        sLXdfS8bbx4ux/BMO/p/NqDJexv61qOoqE1D+5NCVJ6MRp/kD2DC5VwACIT8yrm5uR6ecQOZa98O/diu
        v0vzKRqppi0Y63gJXXIMutIjYbufiZFRBz3a50gWwE/kmoNksDvBtJE8a42iLoUhzJ7eHIkEwya43W5k
        qZX0HFhRU1dD5eZr/dladS4LSE5JVtTXv7R8/dbdc772sI9Xuj545M5KcO6tnT9l2BOqaiwPGYz3cSbj
        9C9NtsqhyVbeUWike1kzo9jY2DUiiaA4X5dnJvqIJoIgWlpam98ZDCVWk6m0Ua1RPRWIjxUzdWHL/6KH
        aAWXy91PN/SyQMQv4/I45gR+XNkxIe8yc8+8h0uXKCLiNyAFPwuWzR75AAAAAElFTkSuQmCC
</value>
  </data>
  <data name="physician16" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAAK/INwWK6QAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAALwSURBVDhPZZJ5SNNxGMa/DZdmGAXZnXbflGmBpJYK
        hVF0CmaHHY60448OIq2wu6xVVFZKt2UtTEqyMLJVk9YtYrGZOo9wlm5pOvNqm59++6Ph7AsPPC88z4f3
        C68AXDTIS0xeFdr76LkdM9SqExG6+ydn6S7sGJMfFeKhHNRHTOuedxn8fMRS9Y04i61BB3Yz9jYNdfoU
        7DVH+flRQUbS+NYpw0RU147TeHuKCdlnl5slLz0LdKppqT/I5k0biI6cT8ETBWV5MZxQDK72lAsfR8ch
        J2BBgDyxufqtZKG18SPN5pvUVp0jXjGbsaM9SN49k4qXsTxI9idwjEj413MCtkdPUmFrkqyNEn0hqWlp
        pKdfxlI1j7YKQV1BEEU563hyPoRFAbI7LgDpuR+KC35N529pdSt2awvafA13M67Q0XQJ/ihp/XaW4rx4
        ci+GERXo9lzqyJyAVbN7XSx5lQbt9bT/qsBcqaHpu5Y/TcXYLZ/4UZJB6Qcl73K2cjlpOokrBuA/Qux0
        AhQR3jc7mw3QUYexKJsja/pzZpMP1gYtxe9PsztSRsIyObeO+3F+z1RObfMjaJzstBMQOtljl7nsGbQY
        sTcYMBY+pLboFrR9Qf/2EPl351L8dA363BiyU8I5EjuMUf1FjBMwdqCYfyMpwlb5KUv6hkkCVUFzAVjL
        MZVfxWJIpL1iL6XP15OZHEhCZD9r315iphPgUD83Ea5Srm3BKp2CpQyrSUtVYSa6F/tprzxAa2kCBvV6
        ElcOrfXtKzb2EELuAnBodfjQex2mL9Lq1TQaHqNOj0N7bwN240k6yvfw+VEUwRPkqV07LoABniJMk3nM
        Dj/5JQHeZO3kw4PNWL4eBGMS1/YF2LzkYk7XjgvAoegw36zfNe+kc/wM9blgvg2NFyjXxBIyzu1+97zL
        4JC7EOMPx4d+s9bkgSkHaq9j0e1jy5Lheul4fLvnXYZ/6tNT+AVP9EpZHDREtTDQW+U/0l3pLhOT/s8i
        /gLafLBNyttCSAAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="reload16" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAAK/INwWK6QAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAF0SURBVDhPhZOxS0JRFMbPvyAIb3DQxcFZV9EQFXRQ
        BMFdwUUXlzAInkMhNLy51miqoIYiCHIuaOkt4dJWW//C6fsu3sf1ojT8uOed77vnXe/nk/5z/z90wy5N
        pPPQEVXdC3Rd/i6Vq6/1nnoizdumecAagAisgbpwAGHtDuDLpXxZJjUQzz5nidll/jU3sIYvGdK+b4sU
        z4sBiClO46mO3kZ7GbwMFN5kQOOmIVKIChFFXIhWripauigpeltQa921TG03E3OJ2ZOs+c1YSQiqrol9
        q7t9bK6CUILjQDccuAaL1f0+Nq94MkkdphSsfANj2xUdQf/A6hwQgq1j2+j82PBcBSH74XdIfS35s3xi
        IIzJxsfapfvY1cnHxGjD1yF7keROc8lmRkTRzd1n8bPQ8fuYccb8C0hmkRHka7CR+flbqPH48MWgBoS3
        nHwYxMZGM2uPNYhAsPGKpI/SWwMIY6tf1018vubClzOFfTBeskvbkJI/vTcazsvSdHkAAAAASUVORK5C
        YII=
</value>
  </data>
  <data name="cash" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALlrZELNbAAAACBjSFJNAACH
        CgAAjCAAAP5TAACDgAAAfvwAAOlWAAA+EQAAIt5KhyhXAAAE1WlDQ1BJQ0MgUHJvZmlsZQAASMfllntM
        FEccx3+79wYO7uA8pDw8KUWkB70CSk8I4VGkIAICglhy5V7CCXdslgNBWoFCCr7wAVVpaFGCgpLWBsUi
        xfqiQiChiClSrCKexgcPFQsJD73Oco0kNTXp33yTyXzmtzO/2Znd784CsM/KCSIDBwCtTk/GhgWLNiVt
        FrH6gQlsEIAN2MiVWURQTEwk/Kem+gGj6hseVK7GyLXquy131joPK3aOnfdQwdvFJdGEAJgIsSDVxD4U
        K0z8CcXb9IQecTLFyjQ5yokRiMVkfGwI4mrEc6nzjB+hWDHPtEqKc5SpaCx9P2KJTqXRIb6Erlmq1FlK
        AFYviu9SEiTqw6bye2i1mSg/ew9iN2ovTLeZWQIgPYnyOy7EUlD5MRVA+HQhtiIWtW0BWoULsRfL5vcH
        ++inrC3eXvMhjN+K5nAxGserAMzKAV6uMRpn+EbjXB8AQwzQ6KTMJnP+2aNcVGhgBrawEoJABkVwCgYx
        PhaNVWLDuC9+CH9FI2ijdJJBZxxjhjNfsS6y93DUZhHm/hZruCGWiVY5vFr+mM0mwV1h0dLVdtP27Y77
        lsUspzu3ueS7BrtZrbz//mWPEx9UfFjmXbqq3PeotM3PECAMTAiu/Xg2LCW8LzI6amCDNt4ioTlJk+wq
        G5W3qvanZmyN1voSLlmCHFbuXP7zHQ+KbpZ0lrbsath7ZH9ZRf4hokpTvaVGU0seL274rrH9h8kmn+bi
        FkPbxou3r2Z3CLuu9OT1+fdz/xi9PTg88ODRE/PxoIm9U89m043GN9ZuAfYggXWgha+hHaax1dh27Bpu
        h+vxAVoorZUupV9mxDGeMg+x1rIxdgfnsBlpnmgRxg2yDLWK5Wn45dbtAvMlCmHX0iC7TnuZI+Z0RkQ4
        +7vYumIrZtxBLPD0lsi8Knyu+wqlCr+mAHrgpuCzoY5hFRFLImuj/TYMxe9NDN3MSO757FtFljoqzTOd
        p50kBrN+yTmWV/h5UoG4aKrkXKl2l8OeX/elHWRV1h0OrLpTvaPG5dj1utL6iFMO38Pp6TOcc6vOF7SN
        Xipod+sY6q7/rfRG8c2aW4a7cfcnH/eOj/wVOT37xtpx4CAfvgu+EAM6OAg/wxNMhCVjR7ERPACvwl/S
        0mkGuor+nFHGdGcOsA6wEzluZgyzcfN7FsPcUSvgOfJjresFdkvqbdfbsd7pdahzyhdFO7u7WLuy3Dju
        tmKJZ5yk0KvZ57mvtzTP70oAFz33E6EQpkLPPSKqa0N83MOEL5Pe+7RXViz3V85tuaIpz5BlepNc/eNt
        ndtPfrG7UFcc9ZX7TvruW+VNB8oqUw5Lv7Gtnqkx1P5+vLdhoHHstHVTeHNly1QbeYlz9WzH1m6PHmOf
        of/PwZEhgSHp4YXR0GcTk50z3Uajyasmh5i+KZQeFS3wi7TXjGqTnynRmAB1pQAbDQDrrgFURQC4+gPY
        IC/HcAHipYDdUwI2vBywJ3z0RuUCvthctdictNjcA2A60+bF06yXK0Uh8gyNgpTr1a+PYR5oYD3IQQki
        CEF1BmorgESkBzWo3jb0/0mvzp33ckgmkUdqUtP0oiD0d6AWhWRqiWy9mhSLwnVKT7HISyLxpvqZzl1K
        TB5A9WaKLkzI/pUV4G9ud9tQ9PDCzAAACMlJREFUWEfFlwlUk1cWx6PUghXFsW4z7dBprZ3juNTWZbQd
        PaOiVqXiFkAK2oIIKAXBhUVFQNnFDQQKCAqI4IKKAiJbgLAFCGtICCAhYGRHUIRA4D/vewLqtDI9Z6ad
        d87vvO/ed9+9/+/mW/Kx3jbY19lKQ4e//zByY3+634192NB5i7eJ++aVjo6OY4eWfp9heELr4r2MK70x
        aSGDJ0OsO0w82Fx9x42Ou5y3Lh8K+e3GhgsblE1ddSQcfiw8go+jRJSJ9me1irTCmD7Pq3btxq5bOQbH
        NtqZOG5ePLTlfzv0nTetOBVyCPVNAqzavQQ5/PsoEaaiqVWEvv4WPGkTKhLyouRuV4607nHdlqTnuOmQ
        sQt74dD2/35o260LSObdwp2UcBz0MIGgmosSMRe80ocoreSiXJyBnp5m9ClaUd8sUCTkRsldQg81G53U
        itN30rQ0dv527lCqXz/2eegamXvtLNjvqVdo6q7T5xRsCRNXHdj77YO9/z4cumAE6/M/KI6S42MB+3E8
        wByOQZY4eckKLqEH4XblMJwvHRi09TMZtDhj0LfPU7tjv4dO3b7TuuVM3l+CqUmLsx3Zqge8d6GzW4rA
        66fp/FsyXMPMU7eACtjrzlazJAKyihPwrdkq8EoSRqiozqWzuLZwhOKK1DdiXqdUmP5G7LCfX54MUQ0P
        0XEBtAYjwNRNGyMCzL30qZNZzC2OQ74gjRLHjUKRiIvbaWEjpBXcg1jCh/BRPnLLkilVdcXUTsm780Zs
        SWUW+MIMZPDjEZt+FaJHvBEBxi47Xgkwc9cbEVAnE8Lvhge8I07A5oIJHAOtqO9hTgycgg7idLgDohKD
        Ec+9jtxSIoBwLyMSEfH+cAu1g8eVo8jkP4DksQBWZwzxo6cBrtzzpWtMnmEBhs7bhgTYsNVMXHRQJS2k
        i4WCTFyMdqPBRUIu+WkeEiFm+OnWaZRV5VF/TX0ZuTtykFeWSKkg7ZU8rqBrBYJ0nL92Cg7+liisyEAx
        6QLjd7lkQ3MzNZhau09ovRTAttFQ2+O8g6pKzA6nwa4hNvAMt4dzsBUsvHaBV5ZK/VlFsTRmmNQUj5/Z
        jCAmlumQg9dmOARa4OINFxz3t6D+hzkRtJb+Mc1XAr532EqdD7IjEBDjBZ+ok+Ri44AvSMWZCAf433Sn
        a0kZ/kgN10SOrzpEF1RQGzYHIh8VCHxUia08Ygt9J760w+eh0ncCykPnIdLnK1xPOI/EnKu0lq7thlcC
        DI5pUWdIrBc8L9uju6sBHS3l6GgV0mPnn6zwMHIHKi9OQgvXFj31qWjPdxvVRm8LIG/HYE8TXkiT0Jxu
        jUqfCci8Y0hrsQ+veyVgp60mdR48Y4TM/Pu0sEzKoXR2VIN3TQuSq0sgl2Vh8LkUT+K3j2rLG+6hVxIM
        RUM4FLIIDLQmU39vA4d0aT4aOBbYarVmSMBeDTXtwxuoAAvP75GcfROd7VWQ1aVRmgrO4lHoZxh4WkEK
        pEJ6e8WodmO6AToKrNFTdYYICINC6geFxI1wHIMdBVC0laA6WB12dvNfCtAgArZbraUCAmI8YOKoh6cd
        teh6ylAD4TlldIsj0d+YhZqbC/6jXR+ngZYsIyS4/QUP3D9GXtAcKGodoKjZD0XVLiKiGM8EwbjrMnVY
        wCI1LcvVVEACLwrO/rbQO7QZ4bd9ERf2HWSx2zHYXgpxxEcQBU4Z1a4M+xNyAmaDF/xXSO4vRHf+cjxJ
        XkSKW0FR+R0UQk30izbT+IZb6yE6q7SDCtDc/08qwMHPGpJGMY6cMYeD7xEkeH2MLr43usq8UBmqigpy
        ZY9mCy+poveRJ7kGLuOF8AC6shejIeHzl8Ur1qK/bBn6S7/AwONIPM1zQrn3uKtUwDemK0feBZImMZwC
        bOks8PsQ8ppbaMtcTxSTAufeHdWWRKmiOuylkM78HWjnLIDk7hxauK94HvqKZkNe+AnpgjF6RFdQ4T2u
        lKXBXqS2zvhr2oFhAR6hThS+13vol97H8+wF6OJMhujcuFHtrvRX1NycjeakuXTuyZn2BvLijeitioTg
        9Ngq1iIiYI3hshEBnLJ4KiIlPx5c7w/wQhCIvordkPM+QHXA+LfaiqYQyPPV6RnKi+ahKPhDPImbRWdp
        tBKaYpXQ9uBddCSpoLfcHM+KzqLcSymFtXDLwsmrflg68i5I4MUgpzINeWIOxAlmaEnei0FZIkn8GWQ3
        pr/VHmi9BTl/Lh6cfB9JrlNRFDQT9bf/TGfGZvzSKCXIbilh8EkaGuP1UeYx1o41Q13tkxUGX9IO/Dtt
        skyI/dWhqI9Dv9AYz7lfvGlnLULNZXJRNXEx+KwU8hINtPGP4kUj52e08e1RFaKEdq4B+iV3IfKdCdP1
        rKXMnbhGffGM8Pman0p/CS/LKV2Pb28hZ5lCzno1HkfPxohdrEXugPPo65QAA31EmB8RoU06wSbsRGfG
        JsjuLERN2HRUBilDHDKR7qu/vhbnTCeUkdpsRgDzj3YbQZegMzQPs5OZY6yUOO2ZNmRzMhTi05CGz8KL
        uiSgWwb0d2JQ0YPBgX4iQk46UQlFYyz668lLJ30jmu4vgSTyI3Ty7On+1lRzRP+olETyMsVXElgTCNMI
        M19jxutzsMk732SfGBtdF7kCfTU30V16kVxYGqi/sR6KZvJ47aojDXiOPvL07G9IoYUGpHfIE/AaFHV3
        qS2viiLvgb8jyFql0luX9dVQbjXCW8cYAvNJpkKYTvj88h7WDb67KvgBy8AJ1EHiqTkoOftHCCK3o6WW
        h9yQHag4NxWM0JaUfWjjWKElyRSSiK8h8BoPd+PJj6bNmrSB5JpEeJcw6icfI+AdwvuEvxHWEAwmv8c6
        vnUx667ZWlaZ/j9YNVpfsiTaS1nSlDu+z3WXsxrJmmzvalad584xEh/9MTXeemNKd61kxfxBhWU/foKy
        9VCeOYQpBEbErxrDYpgNDExXmJ9u4hBMK5mEUwmMYOaY8TFnyqwzscweZQKzn8nF5Px/DxbrX6/chy4+
        paQdAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="calculator_arrow_16" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAISSURBVDhPjc7da1IBGAbwdyqEW3XTRe3Cq8S7TUJC
        szKQ7Kp1oyKUebMoJq0PF431BYuFqYyDzbY2BoMsBhJ0U1tEieU3HjXFQpLqYlngP/F03sWxc+GwAz+e
        i/d5zjnkcrm2ud3uMcnF/zAmbxg5nU7ZpU6ng3a7vSO+c0+xIRoYGJAlPB4P+uGeYkOkUqlkT6OZKISP
        AuZT84gkIwi9CyH4Noi5zTn4oj7MvpoF9xQbIrVaLXuS+JbA+td1xL/EsVZfw0p5BYvFRfCLJ1YnEH4f
        BvcUGyKNRiMTAoEA+uGeYkPkcDhkk61WC/V6fUd8555iQ2S322VXm80mlpeXUa1WeybfuafYENlsNtn1
        RqMBViqVeibj3okrMTrmF8g8HiKyWq2yQK1WQywWQ6FQ6Jl8P373Zd62+ttrif2g0WCdyGw2y27wr1Yq
        FeRyuZ7Jd0uksiWNRfPCd+/2C0wmk+ymKIoQBAHpdLqb7tAGTi9UYX+YwZnHNRjvffi51AaksTjy4JOX
        9Ho9GY1GNl0sFsFSqVQ3T4YyeLQF+D8D52v/TDWBgzNpkfjR6XRkMBhu5/N5hMNhJJPJbp6afoYjtzYw
        eu0Fjt55g+Hx521LFjjgfy0OX9708n5Qsl967vNXy+Uystlsz+T7XtfSrz1n47VB+/SF3YfP8Z72SUa0
        Wm1gaGgo2M8u62RJfcg383dH9AclnfxSsJ4CNgAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="arrow_return_left" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAKRSURBVDhPjdNdSFNhGAfwp2VSassPms7NsFYqaGBi
        pYKlXYSQhd0U1inMLjSzDKVaqZMsJKZ9GFmOylBKDTVwzjS1dGks11kyrMRULFZ+5IXYmg3Knp5z5hZp
        Ri/8eD/O8//fHQgr6oONxYMQeeMDRN8agZiycYaott4ZA86ma0PzhF95B+LtR4FfcwoYws6CiJL3CxZs
        KHwL6wuMfxQwhFV9QuT2zdeH/xrmzC+4OsBQCR8u7JtB7vwXeqImCirwdxTQIY6wpR8RM59+xWzdNJ7v
        sfLyX9mcM9ic1VmsyZqJ0Yjifm2o8k0sXxByoSeOsHkvv2GqZhKz2s2o0E9jbvc05ryw4UrlXRbMaJ3C
        9OYpPNY2aQ4v7NVSzh+C8w0QlKdnAhXdbLbOQsMW5M5zhRYYjPFl/abEqqGZhLv9uKt8cITeFXwBHUAm
        f8asOa1ls558Rm5fm/0c1uXoHGTyTgm9K7fdfG3aUjaAOyqHrXRXOwroAn4nWhlpRgs7C2RnuhwFq0/x
        3yUyeYcxsnYMo+rGkWb0ID3+GHzTm8AnTQPitEbaGxmiEqWoweNQLQgPVPO8Dj8EUWqDRJrZagyoN2Mg
        8T6i0YPbvvu85UwVP+ieVMOzB+08kmoknsl1Sr9LvSY39Q8U105Z6a7+XbC/UkUl7ELcU+qNIqXRJGz4
        PuPUgOh1uW+E3hWwbE+5HZtgQAzrRAzRIga0I65qQ/RpQfRsRhQ2ITprEBdTWFgxYXY5WK112Vvhz/8P
        gpVBsGT3bTa4wzbsQVZQgAu5PUJ0JS6an+j64IvV9aJx1DnxntYp+mQsl7MvqSAqs0Kws5T9B70gvqRR
        EJNbtEgcGkeZYOLOp2kJiRfxJr7/SQQAS38BA7Ldye/vOWgAAAAASUVORK5CYII=
</value>
  </data>
  <data name="money" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAABfZJREFUWEft
        lQlMVFcUhgcarU1Na10KSEFRiloEpWoUDLSCCCIIAwOUVURhIiiLwyZUkKLggghFI2I1RcUNRa24gVpF
        iciiMBUZBmRYBRmWgWERt7/nPSZpmtbEttqkDV/yZWbue+/8J/e+e4czzDD/GzpkLaEdslYjxc9/F/Gj
        itak1ATs/zEVvzwsEXfLpW6KS+8WWXfHvNuF17HS1xWr+C4ICl+DYHJTfARO/3QUFVVlqYpb3z7y7r6g
        3XuSscx2MUIiAiCpF6GpVYIzOcewNSkW6yP8WLfR92OnMqqOn84wUDz6z8iSLNU6Vx7au9Y/EE7OPFy4
        eB7y3h4U3b+BiHg+1m/ygiDWC3fv3cSps0cQFStA2LcBCN8YiOv5lySHj+83VJT66xypXZCeWMlBYjUH
        SaKRyGsMgrRHjL7eAfT0dOPitWzEJAaiuOwG+gZkEFUL4b7SHqGR63Dl6jm4ezvA0W2ZuaLcm3O1xe+j
        fbWqT3fUDIWnScYho2EGTjQZsp8Xml3xqP1nyGRd6JJ1oEfeSetfDrcV9hBE+KNSXAaus8UrWydzrODz
        5irKvhmShhofa97XMPYYgZBLHCTXcpBeNwGHG/SQ/dgMF1tdcEMaQAbi2hN/CNsyIKwog4sHhYevpVko
        x3LeEjh6WDPh0NXTOUhlZ5Fj2IDXIZNL5928ndvuH7gaPKfliE/YjLCoEPhGWCPhvAEym/VxtsUCV6Se
        uN0RilLZdlT1ZqD5eR5qn57G1Ypd8BO4Q2OSGpw8bOC2yh4W1ibQ1ta6TuWdSGYp5jBZv0Mmb/uYpqwi
        enMYrLmmiIkLx5P2ZvT1y/H82SAKCgqwKyUZgig+UrJWIfcxH4XdkRD2fQ/J4Em0owAv0Ah+lBmoHAxN
        5sJ9tQOsbBfBwEC3VFlZiUfjxqQuOZYcoqOrZYywojgnfns0ljsshrevC4pLb6Gnj9a0tx1FZTcRuzMY
        ohohXr58gaoqEfam7UFQ6BqkHApHUVMaWnAe/SiHZ4QeG77AbjQ8fZxoq5pCX1/3hpKSkh2NzyenkR+Q
        HI606/H0HrmsKTo2DFZ2i9jwAIEv7gkL0TvQxTbQ0CxGeLwvgmM8ERTtibxb5/Ds+QBevBxEfb0EpubG
        0NSaiMi4NeAGTGTDR47lwCtBHZbLFmGW/szzykrK1jQ+j5xKvkcOUS7KN2qR1mFw8BnKhPew2s+dmjBn
        DY8KQFFpPuT9nZB2NiPlwHfYtmcDaLbwdLAHVTUP4EpvuqOLDXKv5WCm/gy8T8GjxnEw/gsOjOzGYZKm
        5g8UY0p+SWqSSuRvPBAXzCfxoPoO2jobwdDQUIcNG4NhY2/GGhYZgJzL2bS3u9hZYcw6kwk7Rwt4ePNQ
        IbqP3emJ+FxnMiYbcjBBl4MpC5WhNVnjMEV8Rc4m1Zm8PyCqLZpfWVuEykd38bCGsRAt0lpq4xXk8m7E
        JUTDmm1kMXven805gaSUeHCdLOmQcWDOe6TsTYCbNxfTZmhB24QDfcsRmDJlUgaVZ142PXI8k/WnpKXv
        XL9layTKKvNRVVcCppmHbDOFaHoiZhvpH+jHocwDbBO2PHMKtwB/7Qq0tjfSH5AvXLxs6WVzoFAN6Bp9
        Au0pU9Oo9EKSedNHMzmvxcrCbERMbGiJhaUx1of4oqD4CsTUCM2MYlYK0ahohOHw0YO07nZ4VF+BgNDV
        +MZrOW0ze1qOJdDR0YKmxmfbqewCcgY5isl4Y+I2R2a7uXPh5sFF5sk0iOtLIZIUDzVCNrVVs00wrBV4
        wdmTOWC4cHRdBkOjOY3q6mqbqAyzzXRIZabm3yJuywY/H74rllgYIzl1M8oe3kSVpIQsRjU1dfl6Fnju
        VnD1tqOtawojo7l1qqoqofQoc85rsUXeBjt2fKfvv27lc6aRYIEP7pTkora5HHn5Z+C8wkYRPqdOTU1F
        QLczx6oa++Db5uiJ/ZpRGwUtS61M4LfOC/sO7oQN1xx6M6cLJ6qqMuHMNpvA3vyuSUmNLw8O4WO2gV66
        isqn9jTETPmH7MVhhvnvweH8Ch3q0YyO+f6nAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="tick_small" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAD6SURBVDhPY/j//z9FGKsgKRirICkYqyApGFNgiQgD
        w0pRLyBeBsRHobQXSBxdLVg9hsBiES++9UoXvG/GfA5/kPHX80b0Z751ihdA4uhqweoxBOYKLXO+EPrZ
        9qjff+PdLv8XPVsNFAZKAMVhapAxpsBUgaNA+r/YSvX/S0+u+n/v3r3/okvk/kHFMdVjCPTwLes40/H/
        6tWrYM1Ca6X+C2xS+AwSR1cLVo8h0MLjxdIncGHVmVX/BbdI/uU7IPeZuZf/AkgcXS1YPYZAMw8DQy6b
        F0Mm6zIgPgqmQXygOLpasHpsgqRgrIKkYKyCpGCsgqRgrILE4/8MAEzjwsbTUD04AAAAAElFTkSuQmCC
</value>
  </data>
  <data name="blue_bulb" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAL
        DAAACwwBP0AiyAAAACB0RVh0U29mdHdhcmUATWFjcm9tZWRpYSBGaXJld29ya3MgTVi7kSokAAACY0lE
        QVQ4T2VSXUhTYRg+d3mjTpTsph8IVCoXlbuNKOhGo+hCL6QICmqx6MKQkdJMXaylpS63aLWszmZnZMd0
        P+7MP3Q1w2y62UVlghyJiCAL9PbpvN8845y6eMbO+73P8/49HAAd6l6njOejafHSSEa2xNK4GJlHvTgr
        nwzOiEf5hPHffN3HhWhGNMcWYXr6BqX3R7G2bIV7woWtrlHs806iOpDAsWdTopbDfpZ+ref1JqUfloiE
        ou54Dv7EXdQH/brYAe84WsL8z8zC7dKcAFX+vnQDoXdO7HJHcslENnqHdAIk+kfprNY/EGMCV+IfTU3j
        M3BIHkx/sDMc7huAZ6IHv5VEGoP+E5nGIQESPeiOwugKmbizoQWJZtZWeT59D1ifBTbSQKoA+FoLYawV
        NX4hl7O9ZwRlXcMSdzW+uEoL0wpQVWzMKyIp4L0y5dwWrH25hsImXy6nuFtCRae4ytGptGTCyqdmRmLk
        TYGVxUbkW+7o8nY7X4KjO6uB/cpsJwICrocfAcv12fbZCHVo9NlgftjO3gm07B23BHDnhuZWT/UHAbmB
        LVCd0z/pYDHCVMKKfLMDhS1+XB7sy3aoxMud/Deu+kVSOs5H/7v3TmXLqsAeqx0FDb269zP9T1DjfZzk
        TgeTVeQw7aOK5unPDFS9qCOseytp64fBxlcxI5E9yWHaBIJOQBMvdryC4WaAWZoJtIbf5h3xjcl7PXoB
        xeIMWgFGtvGyEsvLCag49CAmksPIJHRnIhCZUNIxnG17s7IKnQChwhWpLO8aFss6B2W6M51qm12QS9oF
        0dAmVOrzwf0Fgz2fRlPicTIAAAAASUVORK5CYII=
</value>
  </data>
  <data name="green_yes" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAL
        DAAACwwBP0AiyAAAACB0RVh0U29mdHdhcmUATWFjcm9tZWRpYSBGaXJld29ya3MgTVi7kSokAAACJ0lE
        QVQ4T41SS2gTURR900iJYJOiWVbwgyLSmcykE2MrNUFBuqh2UQiNtqQIuhMXLhV076LduHZlt6OSfpJK
        UxM01kw/lEmgG7OYVKmLBgMutIvj3DeZOBMs+OC8ee/OPffz7mEAPLhgjEtSZUJTjNumakxC2U7h4ta4
        eXbjltanj0id/p5LuJLSItU7CG1eh0+/BKYPcPj0KIL6VZwpj+Lk2ojm5vDtkTnjV6tT5qntm23SYTix
        lkDfxxtmbHnC3w5Amf+H7KC3NIxQMcErYWp1Mkpl/8uxE9O1Z4jv3OPn0Ps4jueHo6zfSOao505nNxI7
        91H7tYt07SlYQQb7oMBfiqFndSjHopWpuvvBqBW5mmrfZ/fmrEqBh1+eg2VEO4BlF8oqAsuDdUajcpwJ
        +aaO2W9z6C3HoTXynKx9XwF73Q+2apMdHFuKgdGcHQNldtb+wQ/+bRw0EZy/DJaVPGTC0cUomLiVtFqw
        DpZhZu8VJ7lXev0x2Fsr+2cvWSgPoHs+UmenN0ZzJBIyBjfjePn1TYsK1H7u2qW3+najq2gJLCvn2Hl9
        TCWFtX9amdLGEzR+NzH26YH9cC6igyMLMoSMqHIhkTxJYW6H8HrSLr2oeOyErhUFwoJkC4m2a4W7fpIn
        KczjXIp47xY4OSOZ7MW5v1J2QPIkhZFIaM4OiR6MeuZltzI78AQgBApXxEB+SOt5N2jSnGlU3YsR07ck
        a0I2LHr9wf4AciFJXMEEkj8AAAAASUVORK5CYII=
</value>
  </data>
  <data name="red_element_fire" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAL
        DAAACwwBP0AiyAAAACB0RVh0U29mdHdhcmUATWFjcm9tZWRpYSBGaXJld29ya3MgTVi7kSokAAACtUlE
        QVQ4T2VTSUxTURR9pQliIrQRdiqDQEGwLcVWdIEkEhBiCCK6MnHr0gV7N65csTImRgPEBQWCv1AZ7AA0
        GDAMwbaEBDWg+VUDRJEQxA5wfKcDgja5v3nvnnvu+feeLwAcCX9jqSnQbFICLRbV32rFQosF89fL1Nm6
        QmWm5rTpX/yRQ/CGWQnerMTs5RxMlWkxWSIScU6LKasO07UFmK45oxyuiT8+P2rLWLxlVRdq8/FGFvgM
        AmMyvDI8yX+eeT95MRuT1afU1y1VGQcE7MxidhuXIHexwKgMd6Ueqx3tGC5KnmUwP3FBD19VTlyJkJ1t
        lM3OTLokiAUjsvjn0gK+uBR05QsMyjveM0+cx5YNr/WkTQSazrv4zpTHDgQNFAqsvx2XDYDlvg48yxPo
        l3e8Z564sfJj8FRkukTwti3EgfEdKZOd/A/vx4v521JX0WXUw342QcI8cd4SDUbMWSHBVfHdOSiyD1n0
        iGxtYn8fiO3tIyLja2AedrMevZLEkVRBvNN4AoJ7JgGnzcT0vWZEZVE4tofd6B52ojFsR2L49G4urkJJ
        EhA/WH4cYr7JGOKeUwoW2x/EC/3dz/Ft5SO2wlFs/o7g+24E9mvmAwUegwaO0vSQmK0tcNEkqRkEJcGP
        jQ08LddhfW0d3fUmbPwKY20njJ7W6vgciBstScMrg9YlZuoNVjostYW5trv44HPjSa6cR6MJnZdysfJ+
        GaHtXbyoM+GlJCDOYdBioEhjjRuJ9qTDUj5YcvahtyEhl4PzD/RiRunBY0nqlPKHDGksThiJD9+dqxm0
        Jx1GEq9Fh/4GE5wVOriv5MHvsKNTmomGYrFSpFEl2V8rp4L2pMNoErcc0pDsRkUjxRp0FsihJWT//zEd
        jonKLKPHkqWMmjNV7pmrGixNV50GrTJcrDEexUP8AfJaTbt/W48zAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="star_yellow" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAL
        DAAACwwBP0AiyAAAACB0RVh0U29mdHdhcmUATWFjcm9tZWRpYSBGaXJld29ya3MgTVi7kSokAAACBUlE
        QVQ4T3VSz0sbURB+ESOhQumpSECsWLzpRUMvBYsXDx4KhaIHbWgVeu3dQ/sf2P+g917WS9GmyEZbiY0J
        0aamjTXZ4q4UJb+IG02yJl/f7M/saga+Zd6+mW9m3nwMgBt/ZseRmxPauUW5lX+J6+MFaNmnspaZFrR0
        aNwb7zogPy8g/wJIjwLJPiDBfxOSfrT2B9FIT6F+8EjozDGcq2wAf5dk/H7sJHVBMzWCy9SEfJZ6HXAI
        qPJtydknBjz/G8lB1BKjeicM0quQ3rYnSEdVxHVFhPr15p0af4jq7nCI4fhZRJ/ZE4AfD3gBw5RN7nvu
        m/G7uIgNRFhbWlbsB6N2T94AZ++BhmSmA01VQkt5Z9yZI7UTPSjvBBVGq7KZpbCZ0t3UTNgeqbR9H4z2
        bBNw1H51JzmNh3H4kaEsGrHF6D0w7eg5H8HvIqnLq2aKY8WjVVdye8+HgtivMO1wKkIi6SSg1/daVRHw
        77MTU4/1orLlj7BGZmaSFOYiMB/w/OdbvTLZVTmFk09OTFHsQ2HTN8lPYCRPUphNUPiA/MaQ3vL5F47t
        Ib2D3Jpxr37rRUn0mULin2JmJUDyJIVZJKfrfNe7JiEH+dQBJfPKMifvkLIJkicpjERCe7aS6cFoZmrb
        qmzBdizUvgfHLmJBobIzINOeaVWl6B25HPULlahvzB0P9h+UW/urAFSu/QAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="edit16" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAL
        DAAACwwBP0AiyAAAAAd0SU1FB9YJAg04Mng6gUAAAAAddEVYdENvbW1lbnQAQ3JlYXRlZCB3aXRoIFRo
        ZSBHSU1Q72QlbgAAAUZJREFUOE+VkD1LAzEYx+OXchFc9Dt06TdwdHJw8q06CeIiiOCkFdTFSaVooTo5
        OVVwuUvukhzc1Fuu3GOevFwivav1gR8NSX7/f64kmJXe0R4swLq9/3vwsKqqGcqy1BRFoX9bQ1xAlmUg
        pQQpJAghgAsOb6MR3N7fQEzj9hAXgLIRBaScQ5qmigTOL84gz3MdguB9q5pxAYIL11DDGIPr/pUOOTk9
        rvetagY3MAAbkwRJFAwoo7px/DWGp+dHeB2+wEeXOLpW9wEouoYmUJx8Epjereq11X0APpdS0xrHMURR
        VBPKky3zCqv7AKqksNHRJCu2re4DwsZ5zf3NteY/EYWwGUEhlLPhpd5X2pKx1eBG0wtQUMew0VnW6+/3
        B31vbgCuHeqoFl0o3js43G3/hL8IXuDHBSzKfm9nJmCAIf9gQAghPyyagJ+oL/oAAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="preview16" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAc1JREFUOE+V
        kV1LImEYhvsJ/i4PZMEDSTroIBAsyj0wCJIiS/tYmGwhaIViF405qEyqWTRrqAaaWLdZIgdbk83aLKvJ
        UjRzsrt5c1Y3MsMLbmbmZe6LZ55pYhgGb4VlWXKdaaoHebEW5LxYLILjuPqSegLCu5L3BAQiIc9q5SVv
        CXiefw67sQXf0nfQNN2Y4ELKgA0fgg7+wtQcB+org45eF9Vs7teo1TL/BOzPWCUBXsQyF8W2mIYQz+Hg
        VEaQP8KQexWtHx2sWi1Ta4LAtohIIo/jS+DkCvgrAT8iKfDRPLocXjSb+z6o9doTzIcExJJ3+HNeQPSk
        8CzZ2Usifg5ML4lo6bT3q/WqoFQqVTK9sIHr7CNucuVIWSAcSSqfAvg2E2jrHnst+H+CEfcCzqQ8rm4L
        uLy5RyYPiPEUYmfAbGAf7b0ui1qvCmRZrmRlfQfM5q5y/wD54RG5grKDvQTCsawiX0x3O9zVP1FriQTv
        4hpm5kOIHJ4impCUxR7A9Y1J9wyOQ6/X+6xWa1ni9/tBURScTuerDDhGYf80ieHxLxgam4D9M63xeDys
        yWSCwWAQKpJGMBqNGpvNJhCJTqcT1OPGIBKLxSJotdrfT3BdZRVbVNgaAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="save16" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAKDSURBVDhPjZM9TFNhFIbvYIzGwUTRSOLOonEwcXCy
        iSQEXAyD0Q0HXTCRCVvxBwfRaByEKAGhLRg10RiDoV1YDIaEKuAPpYUoubSWttDS3rbc2/vTvp5zbAwD
        id7k7Xfa8zznu7nfrXLoTrCrrjuIvTfHsadrHLv/EWaYZYdd5QAVMxlgMungfy9m2WFX2Xc7gK+ZCpS7
        S9KMJjRKARHKQjyPcCyHyC8NC5x4Thhm2WFXBkylq1BcHdL0uN3weDzw+0fg9XqxnNHR2dlJ3/3weYeF
        UVxXxZEB+7sDmIg72NE2CNOpILVZQbJURaJgU5zaaiOuWUhoJgyL2QFx2FXqaMq7H2Uc7ldRtm1cv+bG
        iG8Ea7oDN9Wj/lH4vT6kSxZ8Q0PQTVNYdtiVAS+jOhqeqXRzNoq2g5zpYKNsI0+7cTZMG9myCc20UK1a
        aBhUxfkzgG7DN1/CCf8KgssFGuKgQoMcCq9bwxswwyw77NIxBjDwpYjGVwmcehGnZgzHhmM4OrSCI7Vw
        zb9xjxlm2WFXBvTNFnH27SpaXifwfHqVH/SWq0qpyMq9ljcJYdmRAQfp49HnIi6MpaiRJNBBRM1gic59
        id6JsJrF959rCC+nqWcLwyw77MqA+580XAymcW4sCZseVsGowH2rB67TzVRXEY1vYDGWhW2UiUkJy87f
        AT2hIq5MZNEWXIexWaadAFdjM+497JNaTWlQk3kYJUMYZtmRAfSHwIMZHTemCuj4kKddLJGams7gcW+/
        1Gs5A+s5HZZuCsMsO+wqde1Pn3DBZ7rT8x7f5iKYno3i0uX2VuXk+dbZuUVMhsL4GJqn3oIwzIpDrkJX
        PeX4NtlVy3a9WpT633WX8Y7EeigwAAAAAElFTkSuQmCC
</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="open16" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\open16.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="print2" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\print2.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="preview24" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\preview24.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="print24" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\print24.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="print_all24" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\print-all24.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="quickprint24" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\quickprint24.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="mail24" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\mail24.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="mail_all24" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\mail_all24.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="alert_triangle_red_24" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\alert-triangle-red_24.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="done_square_16" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\done_square_16.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="new_16" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\new_16.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="ok_24" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\ok_24.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="verified_16" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\verified_16.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="arrow_down16" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\arrow-down16.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="arrow_up16" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\arrow-up16.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="add_16" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\add_16.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="delete_16" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\delete_16.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="new16" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\new16.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="text_bold_16" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\text_bold_16.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="text_italics_16" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\text_italics_16.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="text_underline_16" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\text_underline_16.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="copy_16" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\copy_16.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="import_16" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\import_16.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="question_blue_16" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\question_blue_16.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="tick_shield_16" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\tick_shield_16.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="calendar_export_16" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\calendar-export_16.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="export_16" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\export_16.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="report_plus_16" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\report_plus_16.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="cancel2_16" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\cancel2_16.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="coins_in_hand16" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\coins_in_hand16.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="coin_stack16" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\coin_stack16.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="list_add16" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\list_add16.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="money16" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\money16.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="money_add16" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\money_add16.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="money_coin16" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\money_coin16.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="money_delete16" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\money_delete16.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="table_money16" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\table_money16.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="invoice16" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\invoice16.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="invoice_coin16" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\invoice_coin16.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="lp_business_report_print_48" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\lp_business_report-print_48.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="lp_cashier_coins_48" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\lp_cashier-coins_48.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="lp_clock_add_48" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\clock-add.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="lp_clock_delete_48" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\clock-delete.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="lp_clock_user_48" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\clock-user.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="lp_employees_delay_48" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\lp_employees-delay_48.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="lp_order_new_48" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\order-new.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="lp_order_search_48" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\lb_order-search_48.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="lp_report_design_48" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\lp_report-design_48.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="lp_report_lock_48" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\lp_report-lock_48.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="lp_report_mail_48" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\lp_report-mail_48.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="lp_report_ok_48" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\lp_report-ok_48.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="lp_report_print_48" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\lp_report-print_48.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="lp_table_customer_add_48" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\lp_table_customer-add_48.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="lp_table_customer_delay_48" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\lp_table_customer-delay_48.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="lp_table_customer_delete_48" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\lp_table_customer-delete_48.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="_1371579171_user" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\1371579171_user.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="_1371579180_full_time" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\1371579180_full-time.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="_1371579182_world" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\1371579182_world.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="_1371579197_bank" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\1371579197_bank.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="lp_employees_coins_48" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\lp_employees-coins_48.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="lp_factory_coins_48" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\lp_factory-coins_48.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="lp_address_book_add2_48" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\lp_address_book-add2_48.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="lp_address_book_coins_48" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\lp_address_book-coins_48.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="lp_address_book_customer_48" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\lp_address_book-customer_48.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="lp_employees_banknote_48" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\lp_employees-banknote_48.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="lp_inventory_banknote_48" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\lp_inventory-banknote_48.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="lp_invoice_to_cash_48" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\lp_invoice-to_cash_48.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="lp_money_coins_to_bank_48" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\lp_money_coins-to_bank_48.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="lp_money_query_48" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\lp_money-query_48.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="_1371662505_logout" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\1371662505_logout.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="document_text_accept" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\document_text_accept.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="facesheet_16" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\facesheet_16.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="female_16" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\female_16.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="group_edit_16" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\group_edit_16.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="list_edit_16" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\list_edit_16.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="male_16" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\male_16.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="print_b_16" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\print_b_16.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="questionb_16" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\questionb_16.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="truck_arrow_16" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\truck_arrow_16.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
</root>