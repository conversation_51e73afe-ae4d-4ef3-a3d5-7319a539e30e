﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2015, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id$
// 
// Last modified: 2015.08.22 1:17 PM
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using CuttingEdge.Conditions;
using DevExpress.XtraBars;
using DevExpress.XtraEditors;
using FluentDateTime;
using LabMaestro.BusinessLogic;
using LabMaestro.BusinessLogic.GroupedIncomeStatement;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.Controls.Win;

public partial class IncomeStatementReportDialog : XtraForm
{
    private readonly IncomeStatementReportBuilder _incomeReportBuilder;
    private readonly bool _isDailyIncomeReport;
    private readonly bool _isIncomeReport;
    private readonly OutstandingStatementReportBuilder _outstandingReportBuilder;
    private readonly Dictionary<int, short> _refCategoryMap;
    private short _labCT;
    private short _labMRI;
    private short _labRadMisc, _labRadMiscCT, _labRadMiscMRI, _labRadMiscSpect;
    private short _labSpect;
    private DateTime _origFrom, _origTo;

    public IncomeStatementReportDialog(bool isIncomeReport)
    {
        _isIncomeReport = isIncomeReport;
        _refCategoryMap = new Dictionary<int, short>();
        InitializeComponent();

        if (_isIncomeReport)
        {
            _incomeReportBuilder = new IncomeStatementReportBuilder();
            //if (_isDailyIncomeReport) { }

            WinUtils.SetFormTitle(this, "Income Statement Report");
        }
        else
        {
            _outstandingReportBuilder = new OutstandingStatementReportBuilder();
            WinUtils.SetFormTitle(this, "Outstanding Statement Report");
            chkDynamicReport.Enabled = false;
        }

        WinUtils.ApplyTheme(this);

        dteFrom.DateTime = DateTime.Now;
        dteTo.DateTime = DateTime.Now;
    }

    public IncomeStatementReportBuilder IncomeReportBuilder => _incomeReportBuilder;

    public OutstandingStatementReportBuilder OutstandingReportBuilder => _outstandingReportBuilder;

    public string ReportHeading { get; private set; }

    public bool GenerateDynamicReport
    {
        get => chkDynamicReport.Checked;
        set => chkDynamicReport.Checked = value;
    }

    public bool IncludeAuxiliaryLabs
    {
        get => chkIncludeAuxLabs.Checked;
        set => chkIncludeAuxLabs.Checked = value;
    }

    public bool SummateCanceledInvoices
    {
        get => chkSumCanceledInvoices.Checked;
        set => chkSumCanceledInvoices.Checked = value;
    }

    public GroupedLabOrdersCollection GroupedOrdersCollection { get; set; }

    public bool ExecuteDialog(IWin32Window parent, ref DateTime dtFrom, ref DateTime dtTo)
    {
        updateControls();
        _origFrom = dtFrom;
        _origTo = dtTo;
        dteFrom.DateTime = _origFrom;
        dteTo.DateTime = _origTo;
        var result = ShowDialog(parent) == DialogResult.OK;
        dtFrom = dteFrom.DateTime;
        dtTo = dteTo.DateTime;
        return result;
    }

    private void updateControls()
    {
        var labs = LabsRepository.GetAllLabSlices();
        foreach (var lab in labs)
        {
            if (lab.LabCode.CompareTo("MRI") == 0) _labMRI = lab.Id;
            else if (lab.LabCode.CompareTo("CT") == 0) _labCT = lab.Id;
            else if (lab.LabCode.CompareTo("RDM") == 0) _labRadMisc = lab.Id;
            else if (lab.LabCode.CompareTo("RDM_CT") == 0) _labRadMiscCT = lab.Id;
            else if (lab.LabCode.CompareTo("RDM_MR") == 0) _labRadMiscMRI = lab.Id;
            else if (lab.LabCode.CompareTo("RDM_SPECT") == 0) _labRadMiscSpect = lab.Id;
            else if (lab.LabCode.CompareTo("SPECT") == 0) _labSpect = lab.Id;
        }

        var cats = ReferrerCategoryRepository.FetchAllSlices();
        _refCategoryMap.Clear();
        cboReferrerCategory.Properties.Items.Clear();
        foreach (var cat in cats.Where(cat => cat.IsActive)) addReferrerCategory(cat);

        addReferrerCategory(null);
    }

    public IncomeStatementReportPrintDto IncomeStatementPrintDto()
    {
        WaitFormControl.WaitOperation(this, () => _incomeReportBuilder.CompileInvoicesList(), "Compiling report...");
        return _incomeReportBuilder.GenerateIncomeOutstandingReportPrintDto(ReportHeading);
    }

    public LabwiseDailyIncomeReportPrintDto LabwiseDailyIncomePrintDto()
    {
        WaitFormControl.WaitOperation(this, () => _incomeReportBuilder.CompileInvoicesList(), "Compiling report...");
        var ldi = new LabwiseDailyIncomeReportBuilder();
        foreach (var slice in _incomeReportBuilder.Invoices)
            ldi.Add(slice.OrderDateTime,
                _incomeReportBuilder.ProcessPrimalSnapshot
                    ? slice.PrimalInvoiceSnapshot
                    : slice.CurrentInvoiceSnapshot);

        return ldi.ToPrintDto(_incomeReportBuilder.BeginDate, _incomeReportBuilder.EndDate, ReportHeading);
    }

    public OutstandingStatementReportPrintDto OutstandingStatementPrintDto()
    {
        WaitFormControl.WaitOperation(this, () => _outstandingReportBuilder.CompileInvoicesList(),
            "Compiling report...");
        return _outstandingReportBuilder.GenerateIncomeOutstandingReportPrintDto(ReportHeading);
    }

    private void addReferrerCategory(ReferrerCategorySlice slice)
    {
        if (slice != null)
            _refCategoryMap.Add(cboReferrerCategory.Properties.Items.Add(slice.Name), slice.Id);
        else
            _refCategoryMap.Add(cboReferrerCategory.Properties.Items.Add("All"), -1);
    }

    private short comboIndexToReferrerCategory(int index)
    {
        if (index < 0) return -1;

        _refCategoryMap.TryGetValue(index, out var value);
        return value;
    }

    private void btnOk_Click(object sender, EventArgs e)
    {
        if (validateAndInitialize()) DialogResult = DialogResult.OK;
    }

    private bool validateAndInitialize() =>
        _isIncomeReport ? validateAndInitializeIncomeReport() : validateAndInitializeOutstandingReport();

    private bool validateAndInitializeIncomeReport()
    {
        try
        {
            var dtFrom = dteFrom.DateTime.Date;
            var dtTo = SharedUtilities.LastMinuteOfDay(dteTo.DateTime);
            Condition.Requires(dtFrom).IsLessOrEqual(dtTo);

            _incomeReportBuilder.BeginDate = dtFrom;
            _incomeReportBuilder.EndDate = dtTo;
            _incomeReportBuilder.ProcessPrimalSnapshot = chkDynamicReport.Checked == false;
            _incomeReportBuilder.LabIds.Clear();
            _incomeReportBuilder.IncludeAuxiliaryLabs = IncludeAuxiliaryLabs;
            if (GroupedOrdersCollection != null &&
                GroupedOrdersCollection.IncludeAuxiliaryLabs != IncludeAuxiliaryLabs)
            {
                // re-initialize the GroupedOrdersCollection
                GroupedOrdersCollection = null;
            }

            switch (cboLabGroups.SelectedIndex)
            {
                case 0:
                    _incomeReportBuilder.LabGroup = "PATH";
                    _incomeReportBuilder.LabsFilterMode = FilterMode.ExcludeItems;
                    ReportHeading = "Pathology";
                    _incomeReportBuilder.LabIds.Add(_labMRI);
                    _incomeReportBuilder.LabIds.Add(_labCT);
                    if (IncludeAuxiliaryLabs)
                    {
                        _incomeReportBuilder.LabIds.Add(_labRadMisc);
                        _incomeReportBuilder.LabIds.Add(_labRadMiscCT);
                        _incomeReportBuilder.LabIds.Add(_labRadMiscMRI);
                        _incomeReportBuilder.LabIds.Add(_labRadMiscSpect);
                    }

                    _incomeReportBuilder.LabIds.Add(_labSpect);
                    break;
                case 1:
                    // CT
                    _incomeReportBuilder.LabGroup = "CT";
                    _incomeReportBuilder.LabsFilterMode = FilterMode.IncludeItems;
                    _incomeReportBuilder.LabIds.Add(_labCT);
                    if (IncludeAuxiliaryLabs)
                    {
                        _incomeReportBuilder.LabIds.Add(_labRadMiscCT);
                    }

                    ReportHeading = "CT";
                    break;
                case 2:
                    // MRI
                    _incomeReportBuilder.LabGroup = "MR";
                    _incomeReportBuilder.LabsFilterMode = FilterMode.IncludeItems;
                    _incomeReportBuilder.LabIds.Add(_labMRI);
                    if (IncludeAuxiliaryLabs)
                    {
                        _incomeReportBuilder.LabIds.Add(_labRadMiscMRI);
                    }

                    ReportHeading = "MRI";
                    break;
                case 3:
                    // SPECT
                    _incomeReportBuilder.LabGroup = "SPECT";
                    _incomeReportBuilder.LabsFilterMode = FilterMode.IncludeItems;
                    _incomeReportBuilder.LabIds.Add(_labSpect);
                    if (IncludeAuxiliaryLabs)
                    {
                        _incomeReportBuilder.LabIds.Add(_labRadMiscSpect);
                    }

                    ReportHeading = "SPECT";
                    break;
                case 4:
                    _incomeReportBuilder.LabGroup = string.Empty;
                    _incomeReportBuilder.LabsFilterMode = FilterMode.ExcludeItems;
                    _incomeReportBuilder.LabIds.Clear();
                    ReportHeading = "All Labs";
                    break;
                default:
                    throw new Exception("No Lab Group selected!");
            }

            // recompile the report if any of the dates have changed or generating for first time
            if (dtFrom.Date != _origFrom.Date || dtTo.Date != _origTo.Date || GroupedOrdersCollection == null)
            {
                GroupedOrdersCollection = new GroupedLabOrdersCollection(IncludeAuxiliaryLabs);
                WaitFormControl.WaitOperation(this, () =>
                    FaultHandler.Shield(() => GroupedOrdersCollection.ScanLabOrders(dtFrom, dtTo)));
            }

            _incomeReportBuilder.GroupedOrdersCollection = GroupedOrdersCollection;
            _incomeReportBuilder.ReferrerCategory = comboIndexToReferrerCategory(cboReferrerCategory.SelectedIndex);
        }
        catch (Exception exc)
        {
            MessageDlg.Error(exc.Message);
            return false;
        }

        return true;
    }

    private bool validateAndInitializeOutstandingReport()
    {
        try
        {
            var dtFrom = dteFrom.DateTime.Date;
            var dtTo = SharedUtilities.LastMinuteOfDay(dteTo.DateTime);
            Condition.Requires(dtFrom).IsLessOrEqual(dtTo);

            _outstandingReportBuilder.BeginDate = dtFrom;
            _outstandingReportBuilder.EndDate = dtTo;
            _outstandingReportBuilder.LabIds.Clear();

            switch (cboLabGroups.SelectedIndex)
            {
                case 0:
                    _outstandingReportBuilder.LabsFilterMode = FilterMode.ExcludeItems;
                    ReportHeading = "Pathology";
                    _outstandingReportBuilder.LabIds.Add(_labMRI);
                    _outstandingReportBuilder.LabIds.Add(_labCT);
                    _outstandingReportBuilder.LabIds.Add(_labRadMisc);
                    _outstandingReportBuilder.LabIds.Add(_labRadMiscCT);
                    _outstandingReportBuilder.LabIds.Add(_labRadMiscMRI);
                    _outstandingReportBuilder.LabIds.Add(_labRadMiscSpect);
                    _outstandingReportBuilder.LabIds.Add(_labSpect);
                    break;
                case 1:
                    // CT
                    _outstandingReportBuilder.LabsFilterMode = FilterMode.IncludeItems;
                    _outstandingReportBuilder.LabIds.Add(_labCT);
                    _outstandingReportBuilder.LabIds.Add(_labRadMiscCT);
                    ReportHeading = "CT";
                    break;
                case 2:
                    // MRI
                    _outstandingReportBuilder.LabsFilterMode = FilterMode.IncludeItems;
                    _outstandingReportBuilder.LabIds.Add(_labMRI);
                    _outstandingReportBuilder.LabIds.Add(_labRadMiscMRI);
                    ReportHeading = "MRI";
                    break;
                case 3:
                    // SPECT
                    _outstandingReportBuilder.LabsFilterMode = FilterMode.IncludeItems;
                    _outstandingReportBuilder.LabIds.Add(_labSpect);
                    _outstandingReportBuilder.LabIds.Add(_labRadMiscSpect);
                    ReportHeading = "SPECT";
                    break;
                case 4:
                    _outstandingReportBuilder.LabsFilterMode = FilterMode.ExcludeItems;
                    _outstandingReportBuilder.LabIds.Clear();
                    ReportHeading = "All Labs";
                    break;
                default:
                    throw new Exception("No Lab Group selected!");
            }

            _outstandingReportBuilder.ReferrerCategory =
                comboIndexToReferrerCategory(cboReferrerCategory.SelectedIndex);
        }
        catch (Exception exc)
        {
            MessageDlg.Error(exc.Message);
            return false;
        }

        return true;
    }

    private void btnCopyDate_Click(object sender, EventArgs e) => dteTo.DateTime = dteFrom.DateTime;

    private void popMenuItemClick(object sender, ItemClickEventArgs e)
    {
        var tag = (short)e.Item.Tag;

        switch (tag)
        {
            case 0:
                // this week
                dteFrom.DateTime = DateTime.Now.Previous(DayOfWeek.Saturday);
                dteTo.DateTime = DateTime.Now.Date;
                break;
            case 1:
                // this month
                dteFrom.DateTime = DateTime.Now.FirstDayOfMonth();
                dteTo.DateTime = DateTime.Now.Date;
                break;
            case -1:
                // yesterday
                dteFrom.DateTime = DateTime.Now.Date.AddDays(tag);
                dteTo.DateTime = dteFrom.DateTime;
                break;
            default:
                dteFrom.DateTime = DateTime.Now.Date.AddDays(tag);
                dteTo.DateTime = DateTime.Now.Date;
                break;
        }
    }

    private void IncomeStatementReportDialog_KeyUp(object sender, KeyEventArgs e)
    {
        if (e.KeyCode == Keys.Escape)
        {
            e.Handled = true;
            DialogResult = DialogResult.Cancel;
        }
    }

    private void btnClose_Click(object sender, EventArgs e) => DialogResult = DialogResult.Cancel;

    public void InitCheckBoxes(bool generateDynamicReport, bool summateCanceled)
    {
        chkDynamicReport.Checked = generateDynamicReport;
        chkSumCanceledInvoices.Checked = summateCanceled;
    }
}