﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: DuesRegisterReportDialog.cs 1203 2014-03-07 06:41:01Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using LabMaestro.Domain;

namespace LabMaestro.Controls.Win
{
    public partial class DuesRegisterReportDialog : XtraForm
    {
        private readonly Dictionary<int, short> _refCategoryMap;

        public DuesRegisterReportDialog()
        {
            InitializeComponent();
            _refCategoryMap = new Dictionary<int, short>();
            WinUtils.ApplyTheme(this);
            dteFrom.DateTime = DateTime.Now;
            InvoiceDate = DateTime.Now;
            ReferrerCategory = -1;
            updateControls();
        }

        public DateTime InvoiceDate { get; set; }
        public short ReferrerCategory { get; set; }

        private void updateControls()
        {
            var cats = ReferrerCategoryRepository.FetchAllSlices();
            _refCategoryMap.Clear();
            cboReferrerCategory.Properties.Items.Clear();
            foreach (var cat in cats.Where(x => x.IsActive))
            {
                addReferrerCategory(cat);
            }

            addReferrerCategory(null);
        }

        private void addReferrerCategory(ReferrerCategorySlice slice)
        {
            if (slice != null)
            {
                _refCategoryMap.Add(cboReferrerCategory.Properties.Items.Add(slice.Name), slice.Id);
            }
            else
            {
                _refCategoryMap.Add(cboReferrerCategory.Properties.Items.Add("All"), -1);
            }
        }

        private short comboIndexToReferrerCategory(int index)
        {
            if (index < 0) return -1;

            short value;
            _refCategoryMap.TryGetValue(index, out value);
            return value;
        }

        private void DuesRegisterReportDialog_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape)
            {
                e.Handled = true;
                DialogResult = DialogResult.Cancel;
            }
        }

        private void btnOk_Click(object sender, EventArgs e)
        {
            InvoiceDate = dteFrom.DateTime;
            ReferrerCategory = comboIndexToReferrerCategory(cboReferrerCategory.SelectedIndex);
            DialogResult = DialogResult.OK;
        }
    }
}