﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ReportDateRangeSelectorDialog.cs 1157 2014-02-13 13:06:48Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Windows.Forms;
using CuttingEdge.Conditions;
using DevExpress.XtraBars;
using DevExpress.XtraEditors;
using FluentDateTime;
using LabMaestro.Shared;

namespace LabMaestro.Controls.Win
{
    public partial class ReportDateRangeSelectorDialog : XtraForm
    {
        public ReportDateRangeSelectorDialog()
        {
            InitializeComponent();

            WinUtils.SetFormTitle(this, "Select Report Date Range");
            WinUtils.ApplyTheme(this);
        }

        public DateTime DateFrom { get; set; }
        public DateTime DateTo { get; set; }
        public bool SingleDate { get; set; }

        public void UpdateControls()
        {
            if (SingleDate)
            {
                dteTo.Enabled = false;
            }
        }

        private void btnCopyDate_Click(object sender, EventArgs e)
        {
            dteTo.DateTime = dteFrom.DateTime;
        }

        private void popupMenu_Click(object sender, ItemClickEventArgs e)
        {
            var tag = (short) e.Item.Tag;

            if (tag == 0)
            {
                // this week
                dteFrom.DateTime = DateTime.Now.Previous(DayOfWeek.Saturday);
                dteTo.DateTime = DateTime.Now.Date;
            }
            else if (tag == 1)
            {
                // this month
                dteFrom.DateTime = DateTime.Now.FirstDayOfMonth();
                dteTo.DateTime = DateTime.Now.Date;
            }
            else if (tag == -1)
            {
                // yesterday
                dteFrom.DateTime = DateTime.Now.Date.AddDays(tag);
                dteTo.DateTime = dteFrom.DateTime;
            }
            else
            {
                dteFrom.DateTime = DateTime.Now.Date.AddDays(tag);
                dteTo.DateTime = DateTime.Now.Date;
            }
        }

        private void ReportDateRangeSelectorDialog_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape)
            {
                e.Handled = true;
                DialogResult = DialogResult.Cancel;
            }
        }

        private bool validateAndInitialize()
        {
            try
            {
                DateFrom = dteFrom.DateTime.Date;
                if (!SingleDate)
                {
                    DateTo = SharedUtilities.LastMinuteOfDay(dteTo.DateTime);
                    Condition.Requires(DateFrom).IsLessOrEqual(DateTo);
                }
            }
            catch (Exception exc)
            {
                MessageDlg.Error(exc.Message);
                return false;
            }
            return true;
        }

        private void btnOk_Click(object sender, EventArgs e)
        {
            if (validateAndInitialize())
            {
                DialogResult = DialogResult.OK;
            }
        }

        private void ReportDateRangeSelectorDialog_Load(object sender, EventArgs e)
        {
            dteFrom.DateTime = DateTime.Now;
            dteTo.DateTime = DateTime.Now;
        }
    }
}