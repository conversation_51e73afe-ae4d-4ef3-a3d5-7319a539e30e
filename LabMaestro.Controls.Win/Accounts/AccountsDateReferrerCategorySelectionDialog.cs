﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: AccountsDateReferrerCategorySelectionDialog.cs 1027 2013-10-16 07:48:25Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using CuttingEdge.Conditions;
using DevExpress.XtraBars;
using DevExpress.XtraEditors;
using FluentDateTime;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Shared;

namespace LabMaestro.Controls.Win
{
    public enum DateReferrerCategoryDialogOption
    {
        All,
        OnlyDateRange,
        OnlySingleDate,
        SingleDateAndReferrer
    }

    public partial class AccountsDateReferrerCategorySelectionDialog : XtraForm
    {
        private readonly Dictionary<int, short> _refCategoryMap;

        public AccountsDateReferrerCategorySelectionDialog()
        {
            _refCategoryMap = new Dictionary<int, short>();
            InitializeComponent();
            WinUtils.ApplyTheme(this);
            Text = "Select Date";
            ReferrerCategory = -1;
        }

        public short ReferrerCategory { get; set; }

        public void SetOption(DateReferrerCategoryDialogOption option)
        {
            switch (option)
            {
                case DateReferrerCategoryDialogOption.All:
                    break;
                case DateReferrerCategoryDialogOption.OnlyDateRange:
                    cboReferrerCategory.Enabled = false;
                    break;
                case DateReferrerCategoryDialogOption.OnlySingleDate:
                    dteTo.Enabled = false;
                    btnCopyDate.Enabled = false;
                    btnDropDown.Enabled = false;
                    cboReferrerCategory.Enabled = false;
                    break;
                case DateReferrerCategoryDialogOption.SingleDateAndReferrer:
                    dteTo.Enabled = false;
                    btnCopyDate.Enabled = false;
                    btnDropDown.Enabled = false;
                    break;
            }
        }

        private void addReferrerCategory(ReferrerCategorySlice slice)
        {
            if (slice != null)
            {
                _refCategoryMap.Add(cboReferrerCategory.Properties.Items.Add(slice.Name), slice.Id);
            }
            else
            {
                _refCategoryMap.Add(cboReferrerCategory.Properties.Items.Add("All"), -1);
            }
        }

        private short comboIndexToReferrerCategory(int index)
        {
            if (index < 0 || !cboReferrerCategory.Enabled) return -1;

            short value;
            _refCategoryMap.TryGetValue(index, out value);
            return value;
        }


        private void btnCopyDate_Click(object sender, EventArgs e)
        {
            dteTo.DateTime = dteFrom.DateTime;
        }

        public static bool ExecuteDialog(IWin32Window parent,
                                         DateReferrerCategoryDialogOption option,
                                         out DateTime dtFrom,
                                         out DateTime dtTo,
                                         out short refCat)
        {
            using (var frm = new AccountsDateReferrerCategorySelectionDialog())
            {
                frm.SetOption(option);
                if (frm.ShowDialog(parent) == DialogResult.OK)
                {
                    dtFrom = frm.dteFrom.DateTime.Date;
                    dtTo = SharedUtilities.LastMinuteOfDay(frm.dteTo.DateTime);
                    refCat = frm.ReferrerCategory;
                    return true;
                }
            }

            dtFrom = DateTime.MinValue;
            dtTo = DateTime.MinValue;
            refCat = -1;
            return false;
        }

        private void btnOk_Click(object sender, EventArgs e)
        {
            if (dteTo.Enabled)
            {
                try
                {
                    Condition.Requires(dteTo.DateTime).IsGreaterOrEqual(dteFrom.DateTime);
                }
                catch (Exception exc)
                {
                    MessageDlg.Error(exc.Message);
                    return;
                }
            }
            
            ReferrerCategory = comboIndexToReferrerCategory(cboReferrerCategory.SelectedIndex);
            DialogResult = DialogResult.OK;
        }

        private void AccountsDateSelectionDialog_Load(object sender, EventArgs e)
        {
            dteFrom.DateTime = DateTime.Now;
            dteTo.DateTime = DateTime.Now;

            var cats = FaultHandler.Shield(() => ReferrerCategoryRepository.FetchAllSlices());
            _refCategoryMap.Clear();
            cboReferrerCategory.Properties.Items.Clear();
            foreach (var cat in cats.Where(cat => cat.IsActive))
            {
                addReferrerCategory(cat);
            }

            addReferrerCategory(null);
        }

        private void AccountsDateSelectionDialog_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape)
            {
                e.Handled = true;
                DialogResult = DialogResult.Cancel;
            }
        }

        private void popMenuItemClick(object sender, ItemClickEventArgs e)
        {
            var tag = (short) e.Item.Tag;

            if (tag == 0)
            {
                // this week
                dteFrom.DateTime = DateTime.Now.Previous(DayOfWeek.Saturday);
                dteTo.DateTime = DateTime.Now.Date;
            }
            else if (tag == 1)
            {
                // this month
                dteFrom.DateTime = DateTime.Now.FirstDayOfMonth();
                dteTo.DateTime = DateTime.Now.Date;
            }
            else if (tag == -1)
            {
                // yesterday
                dteFrom.DateTime = DateTime.Now.Date.AddDays(tag);
                dteTo.DateTime = dteFrom.DateTime;
            }
            else
            {
                dteFrom.DateTime = DateTime.Now.Date.AddDays(tag);
                dteTo.DateTime = DateTime.Now.Date;
            }
        }
    }
}