﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2015, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id$
// 
// Last modified: 2015.08.21 3:41 PM
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Windows.Forms;
using CuttingEdge.Conditions;
using DevExpress.XtraBars;
using DevExpress.XtraEditors;
using FluentDateTime;
using LabMaestro.Shared;

namespace LabMaestro.Controls.Win.Accounts
{
    public partial class DiscountOverageReportDialog : XtraForm
    {
        public DiscountOverageReportDialog()
        {
            InitializeComponent();
            WinUtils.ApplyTheme(this);
            populateControls();
        }

        public DateTime DateFrom { get; set; }
        public DateTime DateTo { get; set; }
        public int DiscountThreshold { get; set; }
        public bool SortByDiscountPercentage { get; set; }

        private void btnCopyDate_Click(object sender, EventArgs e)
        {
            dteTo.DateTime = dteFrom.DateTime;
        }

        private void btnOk_Click(object sender, EventArgs e)
        {
            if (validateAndInitialize())
            {
                DialogResult = DialogResult.OK;
            }
        }

        private bool validateAndInitialize()
        {
            DateFrom = dteFrom.DateTime.Date;
            DateTo = SharedUtilities.LastMinuteOfDay(dteTo.DateTime);
            Condition.Requires(DateFrom).IsLessOrEqual(DateTo);

            if (cboThresholds.SelectedIndex == -1)
            {
                MessageDlg.Error("Please select the Discount Threshold level!");
                return false;
            }

            var index = cboThresholds.SelectedIndex + 1;
            DiscountThreshold = Math.Min(Math.Max(index*10, 10), 100);
            SortByDiscountPercentage = chkSortByPct.Checked;
            return true;
        }

        private void popMenuItemClick(object sender, ItemClickEventArgs e)
        {
            var tag = (int) e.Item.Tag;

            if (tag == 0)
            {
                // this week
                dteFrom.DateTime = DateTime.Now.Previous(DayOfWeek.Saturday);
                dteTo.DateTime = DateTime.Now.Date;
            }
            else if (tag == 1)
            {
                // this month
                dteFrom.DateTime = DateTime.Now.FirstDayOfMonth();
                dteTo.DateTime = DateTime.Now.Date;
            }
            else if (tag == -1)
            {
                // yesterday
                dteFrom.DateTime = DateTime.Now.Date.AddDays(tag);
                dteTo.DateTime = dteFrom.DateTime;
            }
            else
            {
                dteFrom.DateTime = DateTime.Now.Date.AddDays(tag);
                dteTo.DateTime = DateTime.Now.Date;
            }

            popupMenu.HidePopup();
        }

        private void DiscountOverageReportDialog_Load(object sender, EventArgs e)
        {
        }

        private void populateControls()
        {
            dteFrom.DateTime = DateTime.Now;
            dteTo.DateTime = DateTime.Now;
            popupMenu.BeginUpdate();
            addPopupMenuItem("Yesterday", -1);
            addPopupMenuItem("Last 3 days", -3);
            addPopupMenuItem("Last 7 days", -7);
            addPopupMenuItem("This week", 0);
            addPopupMenuItem("This Month", 1);
            popupMenu.EndUpdate();
        }

        private void addPopupMenuItem(string caption, int tag)
        {
            var si = new BarSubItem(barManager, caption) {Tag = tag};
            si.ItemClick += popMenuItemClick;

            //var link = popupMenu.ItemLinks.Add(si);
            var link = popupMenu.AddItem(si);
            link.ActAsButtonGroup = false;
        }
    }
}