﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ReceivablesSummaryReportDialog.cs 1347 2014-06-01 05:27:07Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using CuttingEdge.Conditions;
using DevExpress.XtraBars;
using DevExpress.XtraEditors;
using FluentDateTime;
using LabMaestro.BusinessLogic;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.Controls.Win;

public partial class ReceivablesSummaryReportDialog : XtraForm, IExecutableDialog
{
    private readonly Dictionary<int, short> _roleMap;

    public ReceivablesSummaryReportDialog()
    {
        _roleMap = new Dictionary<int, short>();
        ReportBuilder = new ReceivablesSummaryReportBuilder();
        InitializeComponent();
        WinUtils.ApplyTheme(this);
        WinUtils.SetFormTitle(this, "Receivables Summary Report");

        dteFrom.DateTime = DateTime.Now;
        dteTo.DateTime = DateTime.Now;
    }

    public ReceivablesSummaryReportBuilder ReportBuilder { get; }

    public bool ExecuteDialog(IWin32Window parent)
    {
        UpdateControls();
        return ShowDialog(parent) == DialogResult.OK;
    }

    public void UpdateControls()
    {
        _roleMap.Clear();
        cboStaffRole.Properties.Items.Clear();

        var roleDict = FaultHandler.Shield(fetchEligibleRoles);
        foreach (var role in roleDict.OrderBy(r => r.Value).ToList()) 
            addRole(role.Key, role.Value);
        
        addRole(-1);
    }

    /// <summary>
    ///     Fetches all active roles which have appropriate financial permissions.
    /// </summary>
    private Dictionary<short, string> fetchEligibleRoles()
    {
        var roleDict = new Dictionary<short, string>();
        var permCodes = new[]
        {
            @"finance.issue_discount",
            @"finance.issue_refund",
            @"finance.receive_payment",
            @"finance.issue_discount_rebate"
        };

        foreach (var permCode in permCodes)
        {
            var roles = RolesRepository.GetActiveRolesWithPermissionCode(permCode);
            foreach (var role in roles.Where(role => !roleDict.ContainsKey(role.Id)))
                roleDict.Add(role.Id, role.Name);
        }

        return roleDict;
    }

    private void addRole(short id, string name = null)
    {
        if (id > 0)
            _roleMap.Add(cboStaffRole.Properties.Items.Add(name), id);
        else
            _roleMap.Add(cboStaffRole.Properties.Items.Add("All"), -1);
    }

    private short comboIndexToRoleId(int index)
    {
        if (index < 0) return -1;

        _roleMap.TryGetValue(index, out var value);
        return value;
    }

    private bool validateAndInitialize()
    {
        try
        {
            var dtFrom = dteFrom.DateTime.Date;
            var dtTo = SharedUtilities.LastMinuteOfDay(dteTo.DateTime);
            Condition.Requires(dtFrom).IsLessOrEqual(dtTo);

            ReportBuilder.BeginDate = dtFrom;
            ReportBuilder.EndDate = dtTo;
            ReportBuilder.RoleId = comboIndexToRoleId(cboStaffRole.SelectedIndex);
        }
        catch (Exception exc)
        {
            MessageDlg.Error(exc.Message);
            return false;
        }

        return true;
    }

    private void btnOk_Click(object sender, EventArgs e)
    {
        if (validateAndInitialize()) 
            DialogResult = DialogResult.OK;
    }

    public ReceivablesSummaryReportPrintDto CompilePrintDto()
    {
        WaitFormControl.WaitOperation(this, () =>
                FaultHandler.Shield(() =>
                {
                    if (ReportBuilder.RoleId > 0)
                        ReportBuilder.CompileRoleTransactionsReport();
                    else
                        ReportBuilder.CompileAllTransactionsReport();
                }),
            "Compiling report..."
        );

        return ReportBuilder.GetReceivablesSummaryReportPrintDto(CurrentUserContext.UserDisplayName);
    }

    private void btnCopyDate_Click(object sender, EventArgs e) => dteTo.DateTime = dteFrom.DateTime;

    private void popMenuItemClick(object sender, ItemClickEventArgs e)
    {
        var tag = (short)e.Item.Tag;

        switch (tag)
        {
            case 0:
                // this week
                dteFrom.DateTime = DateTime.Now.Previous(DayOfWeek.Saturday);
                dteTo.DateTime = DateTime.Now.Date;
                break;
            case 1:
                // this month
                dteFrom.DateTime = DateTime.Now.FirstDayOfMonth();
                dteTo.DateTime = DateTime.Now.Date;
                break;
            case -1:
                // yesterday
                dteFrom.DateTime = DateTime.Now.Date.AddDays(tag);
                dteTo.DateTime = dteFrom.DateTime;
                break;
            default:
                dteFrom.DateTime = DateTime.Now.Date.AddDays(tag);
                dteTo.DateTime = DateTime.Now.Date;
                break;
        }
    }

    private void ReceivablesSummaryReportDialog_KeyUp(object sender, KeyEventArgs e)
    {
        if (e.KeyCode == Keys.Escape)
        {
            e.Handled = true;
            DialogResult = DialogResult.Cancel;
        }
    }
}