﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: StartupSplashScreen.cs 799 2013-07-12 14:40:58Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Configuration;
using DevExpress.XtraSplashScreen;
using IdeaBlade.Core;
using LabMaestro.Shared;

namespace LabMaestro.Controls.Win
{
    public partial class StartupSplashScreen : SplashScreen
    {
        private const string SERVER_IP = @"***********";

        public enum SplashScreenCommand
        {
        }

        public StartupSplashScreen()
        {
            InitializeComponent();
            
            try
            {
                lblCopyright.Text = string.Format("Copyright © 2012-{0} Dr. Masroor Ehsan. All rights reserved.",
                                                  DateTime.Now.ToString("yyyy"));
                lblAppVersion.Text = AppVersion.GetVersionString();
                lblIPAddress.Text = SharedUtilities.GetFirstLocalIpString();
                lblPCName.Text = getMachineName();
                var serverHost = getServerHost();
                lblServerAddress.Text = serverHost;
                lblUpdateServer.Text = serverHost;
            }
            catch
            {
                // swallow
            }
        }

        private string getMachineName()
        {
            try
            {
                return Environment.MachineName;
            }
            catch
            {
                return string.Empty;
            }
        }

        private string getServerHost()
        {
            try
            {
                var endPoint = ConfigurationManager.AppSettings[@"entityServerAddress"];
                return endPoint;
            }
            catch
            {
                return SERVER_IP;
            }
        }

        #region Overrides

        public override void ProcessCommand(Enum cmd, object arg)
        {
            base.ProcessCommand(cmd, arg);
        }

        #endregion

        public void UpdateStatusLabel(string message)
        {
            lblStatus.Text = message;
            Refresh();
        }
    }
}