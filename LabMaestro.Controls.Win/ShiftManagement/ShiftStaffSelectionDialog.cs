﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ShiftStaffSelectionDialog.cs 1394 2014-09-02 12:37:10Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Shared;

namespace LabMaestro.Controls.Win
{
    public partial class ShiftStaffSelectionDialog : XtraForm
    {
        public ShiftStaffSelectionDialog()
        {
            StaffId = -1;
            InitializeComponent();
            WinUtils.ApplyTheme(this);
        }

        public short StaffId { get; private set; }

        private void loadUsers()
        {
            var roleCodes = new[] {UserRoles.Receptionist, UserRoles.AccountsReceivable, UserRoles.Supervisor};
            var users = new List<User>();

            WaitFormControl.WaitOperation(this, () =>
                FaultHandler.Shield(() =>
                {
                    foreach (var role in roleCodes.Select(RolesRepository.FindRoleByCode))
                    {
                        users.AddRange(UsersRepository.FindAllUsersInRole(role.Id));
                    }
                }));

            foreach (var user in users.OrderBy(x => x.UserName).ThenBy(x => x.DisplayName))
            {
                var uname = string.Format("{0} ({1})", user.DisplayName, user.UserName);
                lbxUsers.Items.Add(new StaffInfo(uname, string.Empty, user.Id));
            }
        }

        private void btnSelect_Click(object sender, EventArgs e)
        {
            if (lbxUsers.SelectedIndex < 0)
            {
                MessageDlg.Error("Please select a staff!");
            }

            var user = lbxUsers.SelectedValue as StaffInfo;
            if (user == null)
            {
                MessageDlg.Error("Please select a staff!");
                return;
            }
            StaffId = user.UserId;
            DialogResult = DialogResult.OK;
        }

        private void ShiftStaffSelectionDialog_Load(object sender, EventArgs e)
        {
            loadUsers();
        }
    }

    public sealed class StaffInfo
    {
        public StaffInfo(string userName, string roleName, short userId)
        {
            UserId = userId;
            UserName = userName;
            RoleName = roleName;
        }

        public string UserName { get; private set; }
        public string RoleName { get; private set; }
        public short UserId { get; private set; }

        public override string ToString()
        {
            return UserName;
        }
    }
}