﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ShiftSearchDialog.cs 1314 2014-05-24 19:15:37Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Views.Base;
using FluentDate;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Shared;

namespace LabMaestro.Controls.Win
{
    // grid - start + end datetime, count, final balance
    public partial class ShiftSearchDialog : XtraForm, IExecutableDialog
    {
        private readonly BindingList<ShiftDetailsInfo> _shiftHistory = new BindingList<ShiftDetailsInfo>();
        private readonly short _userId;

        public ShiftSearchDialog(short userId)
        {
            _userId = userId;
            AuthHelper.GuardAccess();
            InitializeComponent();
            WinUtils.ApplyTheme(this);
            WinUtils.SetFormTitle(this, "Shift Search");
        }

        public bool ExecuteDialog(IWin32Window parent)
        {
            UpdateControls();
            ShowDialog(parent);
            return true;
        }

        public void UpdateControls()
        {
        }

        private void btnSearchShiftId_Click(object sender, EventArgs e)
        {
            var shiftCode = txtShiftCode.Text.Trim();
            txtShiftCode.Text = shiftCode;
            if (string.IsNullOrEmpty(shiftCode))
            {
                MessageDlg.Warning("Please enter the desired shift ID number");
                return;
            }

            try
            {
                var shiftId = int.Parse(shiftCode);
                searchById(shiftId);
            }
            catch (FormatException)
            {
                MessageDlg.Error("The shift ID must be a valid, positive number!");
                return;
            }
            catch (Exception exc)
            {
                MessageDlg.Error(exc.Message);
                return;
            }

            updateGridHistory();
        }

        private void updateGridHistory()
        {
            var curOld = Cursor;
            Cursor = Cursors.WaitCursor;
            try
            {
                grdShiftHistory.DataSource = null;
                WinUtils.ClearGridViewRows(gridView);
                grdShiftHistory.DataSource = _shiftHistory;
            }
            finally
            {
                Cursor = curOld;
            }
        }

        private void searchById(int shiftId)
        {
            _shiftHistory.Clear();

            WorkShift shift = null;
            WaitFormControl.WaitOperation(this, () => shift = WorkShiftsRepository.FindShiftById(shiftId));

            if (shift == null)
            {
                MessageDlg.Info("No shifts found.");
                return;
            }

            if (shift.UserId != _userId)
            {
                var msg = string.Format("Shift #{0} does not belong to your user account!", shiftId);
                MessageDlg.Warning(msg);
                return;
            }

            _shiftHistory.Add(ShiftDetailsInfo.AssembleFrom(shift));
        }

        private void searchByDate(DateTime start, DateTime end)
        {
            _shiftHistory.Clear();

            List<WorkShift> shifts = null;
            WaitFormControl.WaitOperation(this, () => shifts = end == DateTime.MinValue
                                                                   ? WorkShiftsRepository.FindByDateRange(_userId, start,
                                                                                                          SharedUtilities
                                                                                                              .LastInstantOfDay
                                                                                                              (DateTime
                                                                                                                   .Now))
                                                                   : WorkShiftsRepository.FindByDateRange(_userId, start,
                                                                                                          end));

            if (shifts == null || shifts.Count == 0)
            {
                MessageDlg.Info("No shifts found.");
                return;
            }

            foreach (var shift in shifts)
            {
                _shiftHistory.Add(ShiftDetailsInfo.AssembleFrom(shift));
            }
        }

        private void resetControls(bool clearControls = false)
        {
            _shiftHistory.Clear();
            if (clearControls)
            {
                txtShiftCode.Text = "";
                cboSearchTimeRange.SelectedIndex = -1;
                dteRangeEnd.DateTime = DateTime.Now.Date;
                dteRangeStart.DateTime = DateTime.Now.Date;
                btnViewShiftDetails.Enabled = false;
            }

            updateGridHistory();
        }

        private void btnSearchQuickDate_Click(object sender, EventArgs e)
        {
            if (cboSearchTimeRange.SelectedIndex == -1)
            {
                MessageDlg.Warning("Please select the appropriate date range");
                return;
            }

            var start = AppSysRepository.GetServerTime().Date;
            var end = SharedUtilities.LastInstantOfDay(start);
            switch (cboSearchTimeRange.SelectedIndex)
            {
                case 0:
                    // yesterday
                    start = start - 1.Days();
                    end = SharedUtilities.LastInstantOfDay(start);
                    break;
                case 1:
                    // last 3 days
                    start = start - 3.Days();
                    break;
                case 2:
                    // this week
                    start = SharedUtilities.StartOfWeek(start);
                    break;
                case 3:
                    // this month
                    start = new DateTime(start.Year, start.Month, 1);
                    break;
                case 4:
                    // this fortnight
                    start = start - 2.Weeks();
                    break;
                case 5:
                    // last 30 days
                    start = start - 1.Months();
                    break;
                case 6:
                    // last 3 months
                    start = start - 3.Months();
                    break;
            }
            resetControls();
            searchByDate(start, end);
        }

        private void btnSearchCustomDate_Click(object sender, EventArgs e)
        {
            var startDate = dteRangeStart.DateTime.Date;
            if ((startDate > DateTime.Now.Date) ||
                (startDate < new DateTime(2013, 1, 1)))
            {
                MessageDlg.Error("Invalid start date!");
                dteRangeStart.SelectAll();
                dteRangeStart.Focus();
                return;
            }

            var endDate = (dteRangeEnd.EditValue == null) ? startDate : dteRangeEnd.DateTime.Date;

            if ((endDate > DateTime.Now.Date) ||
                (startDate > endDate))
            {
                MessageDlg.Warning("Invalid end date!");
                dteRangeEnd.SelectAll();
                dteRangeEnd.Focus();
                return;
            }

            resetControls();
            searchByDate(startDate, SharedUtilities.LastInstantOfDay(endDate));
        }

        private void ShiftSearchDialog_Load(object sender, EventArgs e)
        {
            resetControls(true);
            txtShiftCode.Select();
            txtShiftCode.Focus();
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            _shiftHistory.Clear();
            resetControls(true);
        }

        private ShiftDetailsInfo getCurrentSelectedShift()
        {
            if (gridView.SelectedRowsCount != 1)
                return null;
            var rowIndex = gridView.GetSelectedRows()[0];
            if (rowIndex >= 0)
            {
                var shift = gridView.GetRow(rowIndex) as ShiftDetailsInfo;
                return shift;
            }
            return null;
        }

        private void gridView1_FocusedRowChanged(object sender, FocusedRowChangedEventArgs e)
        {
            toggleShiftDetailsButton();
        }

        private void toggleShiftDetailsButton()
        {
            btnViewShiftDetails.Enabled = getCurrentSelectedShift() != null;
        }

        private void gridView1_GotFocus(object sender, EventArgs e)
        {
            toggleShiftDetailsButton();
        }

        private void btnViewShiftDetails_Click(object sender, EventArgs e)
        {
            var shift = getCurrentSelectedShift();
            if (shift == null)
            {
                MessageDlg.Error("No shift selected!");
                return;
            }

            using (var detForm = new ShiftDetailsDialog(shift.ShiftId, false))
            {
                detForm.UpdateControls();
                detForm.ShowDialog();
            }
        }

        private void ShiftSearchDialog_KeyUp(object sender, KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.F5:
                    txtShiftCode.Select();
                    txtShiftCode.Focus();
                    break;
                case Keys.F6:
                    cboSearchTimeRange.Select();
                    cboSearchTimeRange.Focus();
                    break;
            }
        }

        private void txtShiftCode_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter) btnSearchShiftId_Click(null, null);
        }

        private void cboSearchTimeRange_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter) btnSearchQuickDate_Click(null, null);
        }

        private void gridView_CustomColumnDisplayText(object sender, CustomColumnDisplayTextEventArgs e)
        {
            if (e.Column.FieldName == "EndTime")
            {
                var endTime = (DateTime) e.Value;
                e.DisplayText = endTime == DateTime.MinValue ? "" : endTime.ToString("dd-MM-yy h:mm tt");
            }
        }

        public sealed class ShiftDetailsInfo
        {
            public int ShiftId { get; set; }
            public DateTime StartTime { get; set; }
            public DateTime EndTime { get; set; }
            public int NumOrders { get; set; }
            public decimal FinalBalance { get; set; }

            internal static ShiftDetailsInfo AssembleFrom(WorkShift src)
            {
                return new ShiftDetailsInfo
                    {
                        EndTime = src.EndTime ?? DateTime.MinValue,
                        StartTime = src.StartTime,
                        ShiftId = src.Id,
                        NumOrders = src.NumOrders,
                        FinalBalance = src.FinalBalance
                    };
            }
        }
    }
}