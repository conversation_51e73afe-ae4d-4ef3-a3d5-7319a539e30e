﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the organization nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: AdditionalBalanceEditorDialog.cs 927 2013-08-27 13:31:32Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Windows.Forms;
using DevExpress.XtraEditors;

namespace LabMaestro.Controls.Win
{
    public partial class AdditionalBalanceEditorDialog : XtraForm
    {
        public AdditionalBalanceEditorDialog(decimal additionalBalance)
        {
            AdditionalBalance = additionalBalance;
            InitializeComponent();
            WinUtils.ApplyTheme(this);
        }

        public decimal AdditionalBalance { get; private set; }

        private void AdditionalBalanceEditorDialog_Load(object sender, EventArgs e)
        {
            txtBalance.Value = AdditionalBalance;
            txtBalance.Select();
            txtBalance.Focus();
        }

        private void btnApply_Click(object sender, EventArgs e)
        {
            if (!chkVerify.Checked)
            {
                MessageDlg.Warning("Please confirm the balance amount.");
                return;
            }

            try
            {
                AdditionalBalance = decimal.Parse(txtBalance.Text.Trim());
            }
            catch (Exception exc)
            {
                MessageDlg.Error(exc.Message);
                return;
            }

            DialogResult = DialogResult.OK;
        }

        private void AdditionalBalanceEditorDialog_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode != Keys.Enter) return;
            
            e.Handled = true;
            decimal.TryParse(txtBalance.Text.Trim(), out var tmp);
            if (tmp > 0) btnApply_Click(null, null);
            DialogResult = DialogResult.OK;
        }
    }
}