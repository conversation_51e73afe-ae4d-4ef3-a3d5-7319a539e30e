﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the organization nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ShiftStartDialog.cs 1038 2013-10-26 08:17:55Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using LabMaestro.Infrastructure.Client;

namespace LabMaestro.Controls.Win
{
    public partial class ShiftStartDialog : XtraForm
    {
        public ShiftStartDialog()
        {
            AuthHelper.GuardAccess();
            InitializeComponent();
            WinUtils.ApplyTheme(this);
            WinUtils.SetFormTitle(this, "Start New Shift");
        }

        public decimal BalanceAmount { get; set; }

        public string Note { get; set; }

        private void btnAccept_Click(object sender, EventArgs e)
        {
            if (txtBalance.Value < 0)
            {
                MessageDlg.Error("Negative balance is not allowed!");
                return;
            }

            BalanceAmount = txtBalance.Value;
            Note = txtNote.Text.Trim();

            DialogResult = DialogResult.OK;
        }

        public static bool ExecuteDialog(IWin32Window parent, out decimal balance, out string note)
        {
            balance = 0;
            note = null;

            using (var frm = new ShiftStartDialog())
            {
                if (frm.ShowDialog(parent) == DialogResult.OK)
                {
                    balance = frm.BalanceAmount;
                    note = frm.Note;
                    return true;
                }
            }

            return false;
        }

        private void ShiftStartDialog_Load(object sender, EventArgs e)
        {
            txtBalance.Select();
            txtBalance.Focus();
        }

        private void ShiftStartDialog_KeyUp(object sender, KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.Enter:
                    e.Handled = true;
                    btnAccept_Click(null, null);
                    break;
                case Keys.Escape:
                    e.Handled = true;
                    DialogResult = DialogResult.Cancel;
                    break;
            }
        }
    }
}