﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ShiftDetailsDialog.cs 1314 2014-05-24 19:15:37Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Globalization;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraBars;
using DevExpress.XtraEditors;
using LabMaestro.BusinessLogic;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Printing;
using LabMaestro.Shared;
using Xceed.Grid;

namespace LabMaestro.Controls.Win
{
    public partial class ShiftDetailsDialog : XtraForm, IExecutableDialog
    {
        public delegate void OnButtonClicked(WorkShift shift);

        private readonly bool _autoPreviewShiftReport;

        public ShiftDetailsDialog(int shiftId, bool autoPreviewShiftReport)
            : this(WorkShiftsRepository.FindShiftById(shiftId), autoPreviewShiftReport)
        {
        }

        public ShiftDetailsDialog(WorkShift shift, bool autoPreviewShiftReport)
        {
            _autoPreviewShiftReport = autoPreviewShiftReport;
            //AuthHelper.GuardAccess();
            InitializeComponent();
            WinUtils.ApplyTheme(this);
            WinUtils.SetFormTitle(this, "Shift Details");

            CurrentWorkShift = shift;
            grdShiftDetails.Columns.Clear();
            var i = 0;
            grdShiftDetails.GridAddColumn("orderNum", "Ord #", i++, 40);
            grdShiftDetails.GridAddColumn("invoiceId", "Inv #", i++, 80);
            //grdShiftDetails.GridAddColumn("patientName", "Name", i++, 130);
            grdShiftDetails.GridAddColumn("time", "Time", i++, 110);
            grdShiftDetails.GridAddColumn("referredBy", "Referrer", i++, 150);
            grdShiftDetails.GridAddColumn("netBill", "Net Bill", i++, 80);
            grdShiftDetails.GridAddColumn("cash", "Cash", i++, 70);
            grdShiftDetails.GridAddColumn("cashless", "Cashless", i++, 70);
            grdShiftDetails.GridAddColumn("received", "Rcvd Total", i++, 70);
            grdShiftDetails.GridAddColumn("discount", "Discount", i++, 70);
            grdShiftDetails.GridAddColumn("refund", "Refund", i++, 70);
        }

        public WorkShift CurrentWorkShift { get; private set; }

        public bool ExecuteDialog(IWin32Window parent)
        {
            UpdateControls();
            ShowDialog(parent);
            return true;
        }

        public void UpdateControls() => loadShiftData();

        public event OnButtonClicked OnEndShiftButtonClicked;
        //public event OnButtonClicked OnPrintShiftReportButtonClicked;

        private void loadShiftData()
        {
            if (CurrentWorkShift == null) return;

            WaitFormControl.WaitOperation(this,
                () =>
                {
                    lblUserDisplayName.Text = CurrentWorkShift.User.DisplayName;
                    lblShiftId.Text = CurrentWorkShift.Id.ToString(CultureInfo.InvariantCulture);
                    lblShiftStartTime.Text = SharedUtilities.DateTimeToString(CurrentWorkShift.StartTime);
                    lblShiftEndTime.Text = CurrentWorkShift.EndTime == null
                        ? ""
                        : SharedUtilities.DateTimeToString((DateTime)CurrentWorkShift.EndTime);
                    lblShiftStatus.Text = CurrentWorkShift.IsClosed ? "Closed" : "Open";
                    lblDiscount.Text = SharedUtilities.MoneyToString(CurrentWorkShift.DiscountAmount, false);
                    lblAdditionalBalance.Text =
                        SharedUtilities.MoneyToString(CurrentWorkShift.AdditionalBalance, false);
                    lblFinalBalance.Text = SharedUtilities.MoneyToString(CurrentWorkShift.FinalBalance, false);
                    lblReceive.Text = SharedUtilities.MoneyToString(CurrentWorkShift.ReceiveAmount, false);
                    lblNonCash.Text = SharedUtilities.MoneyToString(CurrentWorkShift.NonCashAmount, false);
                    lblRefund.Text = SharedUtilities.MoneyToString(CurrentWorkShift.RefundAmount, false);
                    lblNumOrders.Text = CurrentWorkShift.NumOrders.ToString(CultureInfo.InvariantCulture);
                    lblShiftNote.Text = CurrentWorkShift.UserNotes;

                    updateGrid();
                    updateButtonControls();
                });
        }

        private void updateGrid()
        {
            grdShiftDetails.BeginInit();
            try
            {
                grdShiftDetails.DataRows.Clear();

                //if (!CurrentUserContext.CheckShiftIsOpen()) return;

                var shiftCompiler = new WorkShiftSummaryCompiler(CurrentWorkShift.Id);
                shiftCompiler.CompileSummary();

                var summaries = shiftCompiler.InvoicesInShiftPrintDto;
                //TODO: to long
                foreach (var summary in summaries)
                {
                    var row = grdShiftDetails.DataRows.AddNew();

                    row.Cells["orderNum"].Value = summary.OrderId;
                    row.Cells["invoiceId"].Value = summary.InvoiceId;
                    //row.Cells["patientName"].Value = summary.PatientName;
                    row.Cells["time"].Value = summary.OrderDateTime;
                    row.Cells["referredBy"].Value = summary.ReferredBy;
                    row.Cells["netBill"].Value = summary.NetBillAmount;
                    row.Cells["discount"].Value = summary.DiscountAmount;
                    row.Cells["refund"].Value = summary.RefundAmount;
                    row.Cells["cash"].Value = summary.CashAmount;
                    row.Cells["cashless"].Value = summary.NonCashAmount;
                    row.Cells["received"].Value = summary.ReceivedAmount;
                    row.Tag = summary.ActualInvoiceId;
                    row.Height = 22;
                    row.VerticalAlignment = VerticalAlignment.Center;

                    row.EndEdit();
                }
            }
            finally
            {
                grdShiftDetails.EndInit();
            }
        }

        private void updateButtonControls()
        {
            btnCloseOpenShift.Enabled = false;
            btnPrintDropDown.Enabled = false;
            btnReconcileShiftFinances.Enabled = false;
            btnSetAddlBalance.Enabled = false;

            if (CurrentWorkShift != null)
            {
                //btnCloseOpenShift.Enabled = !CurrentWorkShift.IsClosed;
                btnReconcileShiftFinances.Enabled = !CurrentWorkShift.IsClosed;
                btnPrintDropDown.Enabled = CurrentWorkShift.IsClosed;
                btnSetAddlBalance.Enabled = !CurrentWorkShift.IsClosed;
            }
        }

        private void ShiftDetailsDialog_Load(object sender, EventArgs e)
        {
            lblCurrentDate.Text = SharedUtilities.DateToString(AppSysRepository.GetServerTime());
            btnCloseOpenShift.Click += handleButtonClick;
            if (_autoPreviewShiftReport) printShiftReport();
        }

        private void handleButtonClick(object sender, EventArgs e)
        {
            if (sender == btnCloseOpenShift && OnEndShiftButtonClicked != null)
            {
                reconcileShiftFinances();
                OnEndShiftButtonClicked(CurrentWorkShift);
                loadShiftData();
            }
            //else if (sender == btnPrintShiftReport)
            //{
            //    if (OnPrintShiftReportButtonClicked != null)
            //        OnPrintShiftReportButtonClicked();
            //}
        }

        private void grdShiftDetails_DoubleClick(object sender, EventArgs e) => showTransactionsViewerDialog();

        private void btnReconcileShiftFinances_Click(object sender, EventArgs e)
        {
            /*
            if (!MessageDlg.Confirm("Really perform shift reconciliation?\nThis operation may take a little while."))
                return;
            */
            reconcileShiftFinances();
        }

        private void reconcileShiftFinances()
        {
            if (!CurrentWorkShift.IsClosed)
            {
                FaultHandler.Shield(() => WaitFormControl.WaitOperation(this, () =>
                {
                    CurrentWorkShift.ReconcileShiftFinances();
                    WorkShiftsRepository.UpdateShiftFinances(CurrentWorkShift);
                }, "Reconciling..."));

                WorkShiftsRepository.Reset();
                CurrentWorkShift = WorkShiftsRepository.FindShiftById(CurrentWorkShift.Id);
                loadShiftData();
            }
        }

        private void grdShiftDetails_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                showTransactionsViewerDialog();
                e.Handled = true;
            }
        }

        private void showTransactionsViewerDialog()
        {
            if (grdShiftDetails.SelectedRows.Count == 1 && grdShiftDetails.SelectedRows[0] != null)
            {
                var invoiceId = (long)grdShiftDetails.SelectedRows[0].Tag;

                using var dlgTrans = new InvoiceTransactionsViewerDialog(invoiceId);
                dlgTrans.UpdateControls();
                dlgTrans.ShowDialog(this);
            }
        }

        private void btnSetAddlBalance_Click(object sender, EventArgs e)
        {
            var amount = CurrentWorkShift.AdditionalBalance;
            using var frm = new AdditionalBalanceEditorDialog(amount);
            if (frm.ShowDialog(this) == DialogResult.OK) amount = frm.AdditionalBalance;

            if (amount != CurrentWorkShift.AdditionalBalance && amount >= 0m)
            {
                WorkShiftsRepository.UpdateShiftAdditionalBalance(CurrentWorkShift.Id, amount);
                CurrentWorkShift.AdditionalBalance = amount;
                reconcileShiftFinances();
            }
        }

        private void miPrintInvoices_ItemClick(object sender, ItemClickEventArgs e) => printShiftReport();

        private void printShiftReport()
        {
            if (CurrentWorkShift != null)
            {
                ShiftReportMasterPrintDto dto = null;
                FaultHandler.Shield(() => WaitFormControl.WaitOperation(this, () =>
                {
                    dto = ShiftReportMasterPrintDto.AssembleFrom(CurrentWorkShift);
                    var compiler = new WorkShiftSummaryCompiler(CurrentWorkShift.Id);
                    compiler.CompileSummary();
                    dto.AssignInvoiceDetails(compiler.InvoicesInShiftPrintDto);
                }));
                PrintHelper.PrintShiftInvoicesReport(true, dto);
            }
        }

        private void miPrintTransactions_ItemClick(object sender, ItemClickEventArgs e)
        {
            if (CurrentWorkShift == null) return;

            ShiftReportMasterPrintDto dto = null;
            FaultHandler.Shield(() => WaitFormControl.WaitOperation(this, () =>
            {
                dto = ShiftReportMasterPrintDto.AssembleFrom(CurrentWorkShift);
                var txs = InvoiceTransactionsRepository.GetTransactionsInShiftDetailed(CurrentWorkShift.Id);
                var txDtos = txs.Select(ShiftTransactionsPrintDto.AssembleFrom).ToList();
                dto.AssignInvoiceTransactions(txDtos);
            }));
            PrintHelper.PrintShiftTransactionsReport(true, dto);
        }

        private void btnCalculator_Click(object sender, EventArgs e) => ExternalProcessHelper.ExecuteCalculator();
    }
}