﻿using System;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using LabMaestro.BusinessLogic.CRM;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Shared;

namespace LabMaestro.Controls.Win.CRM;

public partial class CustomerCreateForm : XtraForm
{
    private readonly string _defaultPassword;

    private enum SearchMode
    {
        Upin,
        Phone,
        Login
    }

    public long InvoiceId { get; private set; }
    public long SubscriptionId { get; private set; }
    public bool PrintInvoice => chkPrintInvoice.Checked;

    public CustomerCreateForm()
    {
        InitializeComponent();
        WinUtils.ApplyTheme(this);
        WinUtils.SetFormTitle(this, "Register Patient");
        resetControls();
        _defaultPassword = GlobalSettingsRepository.GetStringValue("crm.default_password", "ctg123");
    }

    string getPhone() => txtPhone.Text.Trim();
    string getLogin() => txtLogin.Text.Trim().ToLowerInvariant();

    private void populateTextControls()
    {
        resetControls();
#if DEBUG
        txtFirstName.Text = "John";
        txtLastName.Text = "Doe";
        txtAge.Text = "25Y";
        txtPhone.Text = "01733938582";
        txtEmail.Text = "<EMAIL>";
        txtLogin.Text = "johndoe";
        rgSex.SelectedIndex = 0;
#endif
        txtAmountPaid.Text = "0";
    }

    private int getAmountPaid()
    {
        var s = txtAmountPaid.Text.Trim();
        return SharedUtilities.IsDigitsOnly(s) ? int.Parse(s) : 0;
    }

    private void btnRegisterCustomer_Click(object sender, EventArgs e)
    {
        var customer = makeCustomer();
        if (customer == null) return;

        var plan = lbPlans.SelectedItem as PlanInfo;

        SelectedCustomer = null;

        try
        {
            var service = new CustomerRegistrationService
            {
                Customer = customer,
                Password = getPassword(),
                Plan = plan!,
                AmountPaid = getAmountPaid(),
                StaffId = CurrentUserContext.UserId,
            };

            if (!service.PreFlightCheck())
                throw new InvalidOperationException(
                    "One or more customer with same mobile phone or username already exists");

            service.Execute();
            PlainTextPassword = service.Password;
            SelectedCustomer = service.RegisteredCustomer;
            SubscriptionId = (long)service.SubscriptionId!;
            InvoiceId = (long)service.InvoiceId!;
            IsNewRegisteredCustomer = true;
            DialogResult = DialogResult.OK;
        }
        catch (Exception exc)
        {
            XtraMessageBox.Show(exc.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private void populatePlansListBox()
    {
        var plans = FaultHandler.Shield(PlansRepository.FetchActive);

        try
        {
            lbPlans.BeginUpdate();
            // allow only paid plans
            foreach (var plan in plans.Where(p => p.ListPrice > 0).OrderBy(p => p.Name))
                lbPlans.Items.Add(new PlanInfo(plan));
        }
        finally
        {
            lbPlans.EndUpdate();
        }

        lbPlans.SelectedIndex = 0;
    }

    private void CustomerCreateForm_Load(object sender, EventArgs e)
    {
        populateTextControls();
        populatePlansListBox();
        picPhoneOk.BackColor = Appearance.BackColor;
        picLoginOk.BackColor = Appearance.BackColor;
    }

    public CustomerInfo? SelectedCustomer { get; private set; }
    public bool IsNewRegisteredCustomer { get; private set; } = false;
    public string PlainTextPassword { get; private set; }

    private void resetControls()
    {
        picLoginOk.Visible = false;
        picPhoneOk.Visible = false;
        txtPassword.Text = _defaultPassword;
    }

    private void txtPhone_ButtonClick(object sender, ButtonPressedEventArgs e)
    {
        try
        {
            var phone = SharedUtilities.SanitizeMobileNumber(getPhone());

            if (!searchCustomers(new CustomerPhoneSearchProvider(), phone, SearchMode.Phone))
            {
                picPhoneOk.Visible = true;
            }
        }
        catch (Exception ex)
        {
            XtraMessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private bool searchCustomers(ICustomerSearchProvider searcher, string query, SearchMode mode)
    {
        var customers = searcher.Search(query);
        if (!customers.Any()) return false;

        using var form = new CustomerSearchForm();
        switch (mode)
        {
            case SearchMode.Upin:
                form.SetSearchTerms(query, string.Empty, string.Empty);
                break;
            case SearchMode.Phone:
                form.SetSearchTerms(string.Empty, query, string.Empty);
                break;
            case SearchMode.Login:
                form.SetSearchTerms(string.Empty, string.Empty, query);
                break;
        }

        form.SetCustomers(customers);
        var proceed = form.ShowDialog(this) == DialogResult.OK;
        var result = proceed && form.SelectedCustomer != null;
        if (result)
        {
            SelectedCustomer = form.SelectedCustomer;
            IsNewRegisteredCustomer = false;
            DialogResult = DialogResult.OK;
        }

        return result;
    }

    private void txtLogin_ButtonClick(object sender, ButtonPressedEventArgs e)
    {
        var s = getLogin();
        if (!string.IsNullOrEmpty(s) && !searchCustomers(new CustomerLoginSearchProvider(), s, SearchMode.Login))
        {
            picLoginOk.Visible = true;
        }
    }

    private void txtPassword_ButtonClick(object sender, ButtonPressedEventArgs e)
    {
        if (string.IsNullOrEmpty(getPassword())) txtPassword.Text = _defaultPassword;
    }

    private string getFirstName() => txtFirstName.Text.Trim();

    private string getLastName() => txtLastName.Text.Trim();

    private void updatePlanLabels()
    {
        if (lbPlans.SelectedItem is not PlanInfo plan) return;

        lblPlanName.Text = plan.Name;
        lblPlanPrice.Text = plan.ListPrice.ToString("N0");
        lblPlanStart.Text = plan.StartDate.ToString("dd-MMM-yyyy");
        lblPlanExpiry.Text = plan.ExpiryDate.ToString("dd-MMM-yyyy");
    }

    private void titleCaseTextBox(TextEdit txtEdit)
    {
        var text = txtEdit.Text.Trim();
        text = SharedUtilities.TitleCase(text);
        txtEdit.Text = text;
    }

    private void fixCasingAndSanitize()
    {
        titleCaseTextBox(txtFirstName);
        titleCaseTextBox(txtLastName);
        var age = txtAge.Text.Trim().ToUpper();
        if (!string.IsNullOrEmpty(age) && SharedUtilities.IsDigitsOnly(age)) age += "Y";
        txtAge.Text = age;
    }

    private void lbPlans_SelectedIndexChanged(object sender, EventArgs e) => updatePlanLabels();

    private SexType getSexType()
    {
        return rgSex.SelectedIndex switch
        {
            0 => SexType.Male,
            1 => SexType.Female,
            _ => SexType.Unknown
        };
    }

    private string getEmail() => txtEmail.Text.Trim().ToLowerInvariant();

    private string getPassword() => txtPassword.Text.Trim();

    private CustomerInfo validateCustomer(CustomerInfo customer)
    {
        if (string.IsNullOrEmpty(customer.FirstName)) throw new ArgumentException("First Name cannot be blank");
        if (string.IsNullOrEmpty(customer.Phone)) throw new ArgumentException("Phone number cannot be blank");
        if (string.IsNullOrEmpty(customer.Login)) throw new ArgumentException("User name cannot be blank");
        if (!string.IsNullOrEmpty(customer.Email) && !SharedUtilities.IsValidEmail(customer.Email))
            throw new ArgumentException("Invalid email address");
        customer.Phone = SharedUtilities.SanitizeMobileNumber(customer.Phone);
        return customer;
    }

    private CustomerInfo? makeCustomer()
    {
        fixCasingAndSanitize();

        try
        {
            var customer = new CustomerInfo
            {
                FirstName = getFirstName(),
                LastName = getLastName(),
                Sex = getSexType(),
                Phone = getPhone(),
                Email = getEmail(),
                Login = getLogin(),
                DoB = getDoB(),
                Age = getAge(),
                EnrollingUserId = CurrentUserContext.UserId,
                EnrolledOn = DateTime.Today
            };

            return validateCustomer(customer);
        }
        catch (Exception e)
        {
            XtraMessageBox.Show(this, e.Message, "Error");
        }

        return null;
    }

    private string? getAge()
    {
        var s = txtAge.Text.Trim();
        return string.IsNullOrEmpty(s) ? null : s;
    }

    private DateTime? getDoB()
    {
        var dt = new SmartDate(dteDoB.DateTime, true);
        return dt.IsEmpty ? null : dt.Date;
    }
}