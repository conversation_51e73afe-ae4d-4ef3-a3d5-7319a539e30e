﻿using System;
using System.Text.RegularExpressions;
using System.Windows.Forms;
using LabMaestro.Domain;
using LabMaestro.Shared;

namespace LabMaestro.Controls.Win.CRM;

public partial class NewCustomerConfirmationForm : DevExpress.XtraEditors.XtraForm
{
    public CustomerInfo Customer { get; }
    public string Password { get; private set; }
    public bool PrintInvoice { get; private set; }

    private readonly Regex rexUsername = new(@"^[A-Z][A-Z0-9]+$", RegexOptions.IgnoreCase);

    public NewCustomerConfirmationForm(CustomerInfo customer, string password)
    {
        Customer = customer;
        Password = password;
        InitializeComponent();
        WinUtils.ApplyTheme(this);
    }

    void validate()
    {
        if (!rexUsername.IsMatch(txtUsername.Text.Trim()))
            throw new FormatException("Invalid username. Username should only contain alphabets.");

        if (!SharedUtilities.IsValidMobileNumber(txtPhone.Text.Trim()))
            throw new FormatException("Invalid phone number. Please enter a valid 11-digit phone number.");

        var email = txtEmail.Text.Trim();
        if (!string.IsNullOrEmpty(email) && !SharedUtilities.IsValidEmail(txtEmail.Text.Trim()))
            throw new FormatException("Invalid email address.");
    }

    void updateProperties(bool print)
    {
        validate();

        Customer.Title = txtTitle.Text.Trim();
        Customer.FirstName = txtFirstName.Text.Trim();
        Customer.Phone = SharedUtilities.DigitsOnly(txtPhone.Text.Trim());
        Customer.LastName = txtLastName.Text.Trim();
        Customer.Login = txtUsername.Text.Trim().ToUpperInvariant();
        Customer.Email = txtEmail.Text.Trim().ToLowerInvariant();
        Customer.Age = txtAge.Text.Trim().ToUpperInvariant();
        if (string.IsNullOrEmpty(Customer.Age) && dtDoB.DateTime != DateTime.MinValue)
            Customer.DoB = dtDoB.DateOnly.ToDateTime(new TimeOnly(0));

        Password = txtPassword.Text.Trim();
        Customer.Sex = rgSex.SelectedIndex switch
        {
            0 => SexType.Male,
            1 => SexType.Female,
            _ => SexType.Unknown
        };
        PrintInvoice = print;
        DialogResult = DialogResult.OK;
    }

    private void btnCreateOnly_Click(object sender, EventArgs e) => updateProperties(false);

    private void btnCreatePrint_Click(object sender, EventArgs e) => updateProperties(true);

    private void NewCustomerConfirmationForm_Load(object sender, EventArgs e)
    {
        txtPassword.Text = Password;
        txtTitle.Text = Customer.Title;
        txtFirstName.Text = Customer.FirstName;
        txtLastName.Text = Customer.LastName;
        txtPhone.Text = Customer.Phone;
        txtEmail.Text = Customer.Email;
        txtUsername.Text = Customer.Login;
        txtAge.Text = Customer.Age;
        if (Customer.HasDoB())
            dtDoB.DateTime = Customer.DoB.Value;
        else
            dtDoB.EditValue = null;
        rgSex.SelectedIndex = Customer.Sex switch
        {
            SexType.Male => 0,
            SexType.Female => 1,
            _ => -1
        };
    }

    private void NewCustomerConfirmationForm_KeyUp(object sender, KeyEventArgs e)
    {
        if (e.KeyCode == Keys.F9)
        {
            e.Handled = true;
            updateProperties(true);
        }
    }
}