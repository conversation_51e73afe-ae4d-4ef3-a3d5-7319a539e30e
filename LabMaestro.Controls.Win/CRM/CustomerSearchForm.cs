﻿using DevExpress.XtraEditors;
using LabMaestro.BusinessLogic.CRM;
using LabMaestro.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.Controls.Win.CRM;

public partial class CustomerSearchForm : XtraForm
{
    public CustomerInfo? SelectedCustomer { get; private set; }

    public CustomerSearchForm()
    {
        InitializeComponent();
        WinUtils.ApplyTheme(this);
        WinUtils.SetFormTitle(this, "Registered Patients");
    }

    public void SetCustomers(IEnumerable<Customer> customers)
    {
        try
        {
            gridCustomers.BeginUpdate();
            gridCustomers.DataSource = customers;
            lblSearchCount.Text = $"{customers.Count()} record(s) found";
        }
        finally
        {
            gridCustomers.EndUpdate();
        }
    }

    public void SetSearchTerms(string phn, string phone, string login)
    {
        txtPHN.Text = phn;
        txtPhone.Text = phone;
        txtLogin.Text = login;
    }

    private void btnSearchCustomer_Click(object sender, EventArgs e) => performSearch();

    private void performSearch()
    {
        if (chkRegistration.Checked)
        {
            if (dteRegistration.EditValue == null)
            {
                MessageBox.Show("Please select a registration date");
                return;
            }

            SetCustomers(CustomerRepository.FindByRegistrationDate(dteRegistration.DateTime));
            return;
        }

        var upin = txtPHN.Text.Trim().ToUpperInvariant();
        var phone = SharedUtilities.DigitsOnly(txtPhone.Text.Trim());
        var login = txtLogin.Text.Trim().ToLowerInvariant();
        if (string.IsNullOrEmpty(upin) && string.IsNullOrEmpty(phone) && string.IsNullOrEmpty(login))
        {
            MessageDlg.Warning("Please provide a valid PHN, phone or username to perform search.");
            return;
        }

        ICustomerSearchProvider searcher;
        string searchString;
        if (!string.IsNullOrEmpty(upin))
        {
            searcher = new CustomerUPINSearchProvider();
            searchString = upin;
        }
        else if (!string.IsNullOrEmpty(phone))
        {
            searcher = new CustomerPhoneSearchProvider();
            searchString = phone;
        }
        else
        {
            searcher = new CustomerLoginSearchProvider();
            searchString = login;
        }

        SetCustomers(searcher.Search(searchString));
    }

    private void btnSelectCustomer_Click(object sender, EventArgs e) => selectCurrentCustomer();

    void getSelectedCustomer()
    {
        if (gvCustomers.SelectedRowsCount != 1) return;
        if (gvCustomers.FocusedRowObject is Customer customer)
            SelectedCustomer = CustomerInfo.AssembleFrom(customer);
    }

    Customer? getCurrentCustomer()
    {
        if (gvCustomers.SelectedRowsCount != 1) return null;
        if (gvCustomers.FocusedRowObject is Customer customer)
            return customer;
        return null;
    }

    private void selectCurrentCustomer()
    {
        getSelectedCustomer();
        if (SelectedCustomer != null)
        {
            DialogResult = DialogResult.OK;
        }
    }

    private void gridCustomers_DoubleClick(object sender, EventArgs e) => selectCurrentCustomer();

    private void CustomerSearchForm_Load(object sender, EventArgs e)
    {
    }

    private void txtPHN_KeyUp(object sender, KeyEventArgs e)
    {
        if (e.KeyCode != Keys.Enter) return;
        e.Handled = true;
        performSearch();
    }

    private void btnResetPassword_Click(object sender, EventArgs e)
    {
        var customer = getCurrentCustomer();
        if (customer == null) return;
        var password = InputDialog.ExecuteDialogOnlyString(this, "Enter new password", "ctg123");
        password = password.Trim();
        if (string.IsNullOrEmpty(password)) return;

        customer = CustomerRepository.FindById(customer.Id);
        customer.PassHash = AuthUtils.EncodePassword(password);
        customer.LastUpdated = DateTime.Now;
        CustomerRepository.Save();
        MessageDlg.Info("Password reset successful");

        if (!MessageDlg.Confirm("Do you want to print the invoice?")) return;
        printCustomerInvoice(customer, password);
    }

    private void printCustomerInvoice(Customer customer, string password)
    {
        var dto = CustomerSubscriptionPaymentInfo.AssembleFrom(customer, password);
        dto.SiteName =
            GlobalSettingsRepository.GetStringValue("crm.site_name", "Chevron Clinical Lab Pte Ltd, Panchlaish");
        var portal = GlobalSettingsRepository.GetStringValue("crm.portal_url", "MYLABCTG.COM");
        dto.PortalUrl = portal;
        dto.FooterText =
            $"মোবাইল এ্যাপ ও অনলাইন রিপোর্টের জন্য ভিজিট করুনঃ {portal}\nআপনার USERNAME ও PASSWORD দিয়ে login করুন\nDownload the app or access your reports online at: {portal}\nUse the above username and password to log in\n";
        var preview = ModifierKeys == Keys.Shift;
        PrintHelper.PrintCrmInvoice(dto, preview);
    }


    private void btnLinkInvoices_Click(object sender, EventArgs e)
    {
        var customer = getCurrentCustomer();
        if (customer == null) return;
        using var frm = new CustomerInvoicesForm(customer);
        frm.ShowDialog(this);
    }

    private void btnChangePhone_Click(object sender, EventArgs e)
    {
        var customer = getCurrentCustomer();
        if (customer == null) return;
        var phone = InputDialog.ExecuteDialogOnlyString(this, "Enter new phone number");
        try
        {
            phone = SharedUtilities.SanitizeMobileNumber(phone);
        }
        catch (Exception exception)
        {
            MessageDlg.Error(exception.Message);
            return;
        }

        var another = CustomerRepository.FindByPhoneFirst(phone);
        if (another != null)
        {
            MessageDlg.Warning("This phone number is already registered with " + another.Name);
            return;
        }

        customer = CustomerRepository.FindById(customer.Id);
        customer.Phone = phone;
        customer.LastUpdated = DateTime.Now;
        CustomerRepository.Save();
        MessageDlg.Info("Phone update successful");
    }

    private void btnChangeUsername_Click(object sender, EventArgs e)
    {
        var customer = getCurrentCustomer();
        if (customer == null) return;
        var username = InputDialog.ExecuteDialogOnlyString(this, "Enter new username");
        username = username.Trim().ToUpperInvariant();

        if (!SharedUtilities.IsValidUsername(username))
        {
            MessageDlg.Error("Invalid username");
            return;
        }

        var another = CustomerRepository.FindByLoginFirst(username);
        if (another != null)
        {
            MessageDlg.Warning("This username is already registered with " + another.Name);
            return;
        }

        customer = CustomerRepository.FindById(customer.Id);
        customer.Login = username.ToLowerInvariant();
        customer.LastUpdated = DateTime.Now;
        CustomerRepository.Save();
        MessageDlg.Info("Username update successful");
    }

    private void chkRegistration_CheckedChanged(object sender, EventArgs e) =>
        dteRegistration.Enabled = chkRegistration.Checked;
}