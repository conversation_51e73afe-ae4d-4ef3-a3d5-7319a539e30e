﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Linq;
using DevExpress.XtraEditors;
using LabMaestro.BusinessLogic.CRM;
using LabMaestro.Domain;

namespace LabMaestro.Controls.Win.CRM
{
    public partial class CustomerInvoicesForm : XtraForm
    {
        private readonly Customer _customer;
        private List<CustomerInvoiceSlice> _invoices = [];

        public CustomerInvoicesForm(Customer customer)
        {
            _customer = customer;
            InitializeComponent();
            updateLabels();
            WinUtils.ApplyTheme(this);
            //gridControl.DataSource = new BindingList<InvoiceData>();
        }

        private void updateLabels()
        {
            lblCustomerName.Text = _customer.Name;
            lblCustomerAge.Text = _customer.Age;
            lblCustomerPhone.Text = _customer.Phone;
            lblCustomerId.Text = _customer.Id.ToString();
            lblCustomerEnrollDate.Text = _customer.EnrolledOn.ToString("dd MMM, yyyy");
        }

        private void CustomerInvoicesForm_Load(object sender, EventArgs e)
        {
            _invoices = PatientLabOrdersRepository
                .FindAllOrdersByPhone(_customer.Phone)
                .Select(CustomerInvoiceSlice.From).ToList();
            gridControl.DataSource = _invoices;
        }

        private void btnSelectAll_Click(object sender, EventArgs e) => gridView.SelectAll();

        private void btnLinkInvoices_Click(object sender, EventArgs e)
        {
            if (gridView.SelectedRowsCount == 0)
                return;

            if (!MessageDlg.Confirm($"Link {gridView.SelectedRowsCount} lab orders with this patient?"))
                return;

            var selected = new List<CustomerInvoiceSlice>();
            foreach (var rowHandle in gridView.GetSelectedRows())
                if (gridView.GetRow(rowHandle) is CustomerInvoiceSlice inv)
                    selected.Add(inv);

            if (selected.Any())
            {
                PatientLabOrdersRepository.SetCustomerIdForLabOrders(
                    selected.Select(slice => slice.InvoiceId).ToList(),
                    _customer.Id);
                MessageDlg.Info($"Associated {selected.Count} lab orders with the registered patient.");
            }
        }

        private void btnSelectOrphans_Click(object sender, EventArgs e)
        {
            gridView.BeginSelection();
            gridView.ClearSelection();
            for (var index = 0; index < gridView.DataRowCount; index++)
            {
                if (gridView.GetRow(index) is CustomerInvoiceSlice invoice)
                    //if (invoice.PatientId != _customer.Id)
                    if (!invoice.PatientId.HasValue)
                        gridView.SelectRow(index);
            }

            gridView.EndSelection();
        }

        void selectInvoices(List<CustomerInvoiceSlice> list)
        {
            gridView.BeginSelection();
            gridView.ClearSelection();
            for (var index = 0; index < gridView.DataRowCount; index++)
            {
                if (gridView.GetRow(index) is CustomerInvoiceSlice invoice)
                    if (list.Contains(invoice))
                        gridView.SelectRow(index);
            }

            gridView.EndSelection();
        }

        private void btnSmartSelect_Click(object sender, EventArgs e)
        {
            // filter out non-matching sexes 
            var customers = _invoices.Where(c => (byte)c.Sex == _customer.Sex).ToList();
            var searcher = new SimilarCustomerMatcher(customers);
            var found = searcher.Match(_customer.Name);
            if (found.Any()) selectInvoices(found);
        }
    }
}