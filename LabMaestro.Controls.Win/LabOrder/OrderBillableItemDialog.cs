﻿//  * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
//
//  Copyright (c) 2012, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
//  All rights reserved.
//
//  Redistribution and use in source and binary forms, with or without
//  modification, are permitted provided that the following conditions are met:
//     * Redistributions of source code must retain the above copyright
//       notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above copyright
//       notice, this list of conditions and the following disclaimer in the
//       documentation and/or other materials provided with the distribution.
//     * Neither the name of the <organization> nor the
//       names of its contributors may be used to endorse or promote products
//       derived from this software without specific prior written permission.
//
//  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
//  ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
//  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
//  DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
//  DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
//  (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
//  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
//  ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
//  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
//  SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
//
//  $Id: OrderBillableItemDialog.cs 847 2013-07-22 07:17:00Z <EMAIL> $
//
//  * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Shared;
using HorizontalAlignment = Xceed.Grid.HorizontalAlignment;

namespace LabMaestro.Controls.Win
{
    public partial class OrderBillableItemDialog : XtraForm, IExecutableDialog
    {
        public OrderBillableItemDialog()
        {
            InitializeComponent();
            WinUtils.ApplyTheme(this);
            WinUtils.SetFormTitle(this, "Billable Items");

            createGridColumns();
            SelectedBillableItemId = -1;
            SelectedQuantity = 0;
            AllowSelectionOfBillableItem = false;
        }

        public bool AllowSelectionOfBillableItem { get; set; }

        public short SelectedBillableItemId { get; set; }

        public short SelectedQuantity { get; set; }

        private void createGridColumns()
        {
            var i = 0;
            grdBillableItems.BeginInit();
            try
            {
                grdBillableItems.Columns.Clear();
                grdBillableItems.GridAddColumn(@"Name", "Item", i++, 180);
                var col = grdBillableItems.GridAddColumn(@"Price", "Price", i, 80);
                col.HorizontalAlignment = HorizontalAlignment.Right;
            }
            finally
            {
                grdBillableItems.EndInit();
            }
        }

        public bool ExecuteDialog(IWin32Window parent)
        {
            UpdateControls();
            return ShowDialog(parent) == DialogResult.OK;
        }

        public void UpdateControls()
        {
            grdBillableItems.BeginInit();
            try
            {
                grdBillableItems.DataRows.Clear();
                var slices = FaultHandler.Shield(() => OrderableBillableItemsRepository.GetAllItems());
                foreach (var slice in slices)
                {
                    var row = grdBillableItems.DataRows.AddNew();
                    row.Cells["Name"].Value = slice.Name;
                    row.Cells["Price"].Value = SharedUtilities.MoneyToString(slice.UnitPrice, false);
                    row.Cells["Price"].HorizontalAlignment = HorizontalAlignment.Right;
                    row.Tag = slice.Id;
                    row.Height = 23;
                    //row.IsSelected = (slice.Id == SelectedBillableItemId);
                    row.EndEdit();
                }

                grdBillableItems.SelectionMode = !AllowSelectionOfBillableItem ? SelectionMode.None : SelectionMode.One;
            }
            finally
            {
                grdBillableItems.EndInit();
            }

            edQuantity.Value = SelectedQuantity;
        }

        private void btnApply_Click(object sender, EventArgs e)
        {
            if (grdBillableItems.SelectedRows.Count == 1)
            {
                SelectedBillableItemId = (short)grdBillableItems.SelectedRows[0].Tag;
            }

            SelectedQuantity = (short)edQuantity.Value;
            if (SelectedQuantity <= 0)
            {
                MessageDlg.Warning("Quantity must be greater than 0");
                return;
            }

            DialogResult = DialogResult.OK;
        }

        private void edQuantity_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                btnApply.PerformClick();
                e.Handled = true;
            }
        }
    }
}