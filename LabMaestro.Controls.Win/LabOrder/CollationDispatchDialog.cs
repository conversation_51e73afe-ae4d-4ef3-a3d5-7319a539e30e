﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: CollationDispatchDialog.cs 1459 2014-10-12 11:21:09Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Drawing;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
#if COLLATION_RECENT_BUNDLES
using System.Threading;
using System.Threading.Tasks;
#endif
using System.Windows.Forms;
using C1.LiveLinq.Collections;
using DevExpress.XtraBars;
using DevExpress.XtraEditors;
using LabMaestro.BusinessLogic;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Printing;
using LabMaestro.Shared;
using Xceed.Grid;

namespace LabMaestro.Controls.Win;

public partial class CollationDispatchDialog : XtraForm, IExecutableDialog
{
    private readonly CollationDispatchDialogType _dialogType;

#if COLLATION_RECENT_BUNDLES
        private readonly RecentlyUpdatedResultBundlesProvider _recentBundlesProvider;
        private readonly RecentlyUpdatedResultBundlesReceiver _recentBundlesReceiver;
        private bool _recentHideUnarchived;
        private bool _recentSortOldestFirst;
#endif

    private readonly Regex _rexPatientId = new(
        @"^[A-Z]{1,2}[\d]{1,3}$",
        RegexOptions.Compiled | RegexOptions.Singleline);

    private readonly IndexedCollection<SearchedLabOrderInfo> _searchResults;

    private List<ResultBundlesForInvoice> _activeResultBundles;

    private bool _applyFilter;
    private ILabOrderSearchProvider _searchProvider;

    public CollationDispatchDialog(CollationDispatchDialogType dialogType)
    {
        _applyFilter = false;
        _dialogType = dialogType;
        _searchResults = [];
        InitializeComponent();
        WinUtils.ApplyTheme(this);
        WinUtils.SetFormTitle(this, isCollationDialog() ? "Report Collation" : "Report Dispatch");

        updateDialogControlsByType();
        gridCreateColumns();
        SelectedFilterItems = [];
        updateFilterLabel();
        resetSearchControls();

#if COLLATION_RECENT_BUNDLES
            WorkflowStageType wfLwm = WorkflowStageType.Unknown, wfHwm = WorkflowStageType.Unknown;
            switch (_dialogType)
            {
                case CollationDispatchDialogType.CollationDialog:
                    wfLwm = WorkflowStageType.ReportFinalization;
                    wfHwm = WorkflowStageType.ReportCollation;
                    break;
                case CollationDispatchDialogType.DispatchDialog:
                    wfLwm = WorkflowStageType.ReportCollation;
                    wfHwm = WorkflowStageType.ReportDispatch;
                    break;
            }

            _recentBundlesProvider =
                new RecentlyUpdatedResultBundlesProvider(
                    GlobalSettingsHelper.ReportCollation.RecentlyUpdatedBundlesUpdateInterval,
                    CurrentUserContext.Username,
                    CurrentUserContext.Password,
                    wfLwm,
                    wfHwm);

            _recentHideUnarchived = false;
            _recentSortOldestFirst = false;
            _recentBundlesReceiver = new RecentlyUpdatedResultBundlesReceiver();
            _recentBundlesReceiver.Subscribe(_recentBundlesProvider);
            _recentBundlesReceiver.OnUpdatedResultBundlesAvailable += handleUpdatedBundlesAvailable;
#endif
        gridResultBundles.SelectionMode = isCollationDialog() ? SelectionMode.MultiExtended : SelectionMode.One;
    }

    public char[] SelectedFilterItems { get; set; }

    public bool ExecuteDialog(IWin32Window parent)
    {
        UpdateControls();
        ShowDialog(parent);
        return true;
    }

    public void UpdateControls()
    {
    }

    private void CollationDialog_FormClosing(object sender, FormClosingEventArgs e)
    {
#if COLLATION_RECENT_BUNDLES
            FaultHandler.Shield(() => _recentBundlesProvider.EndBroadcast());
#endif
    }

    private void CollationDialog_Load(object sender, EventArgs e)
    {
        txtInvoiceNum.Select();
#if COLLATION_RECENT_BUNDLES
            Task.Factory.StartNew(() =>
                                  {
                                      // give breathing space for the form to initialize
                                      Thread.Sleep(TimeSpan.FromSeconds(5));
                                      collationAlert();
                                      _recentBundlesProvider.StartBroadcast();
                                  });
#endif
    }

#if COLLATION_RECENT_BUNDLES
    private void collationAlert()
    {
        if (InvokeRequired)
        {
            BeginInvoke(new MethodInvoker(collationAlert));
        }
        else
        {
            if (isCollationDialog())
            {
                var count = getPendingBundlesCount(WorkflowStageType.ReportCollation);
                if (count > 0)
                {
                    var msg =
                        "There are still {0} reports pending from previous few days!\n\nPlease take necessary steps for smooth completion of these lab orders.";
                    MessageDlg.Warning(string.Format(msg, count));
                }
            }
        }
    }
#endif

    private void CollationDispatchDialog_KeyUp(object sender, KeyEventArgs e)
    {
        if (e.KeyCode == Keys.F5)
            focusInvoiceNumTextbox();
    }

    private void focusPatientIdTextbox()
    {
        txtPatientId.Select();
        txtPatientId.SelectAll();
        txtPatientId.Focus();
    }

    private void focusInvoiceNumTextbox()
    {
        txtInvoiceNum.Select();
        txtInvoiceNum.SelectAll();
        txtInvoiceNum.Focus();
    }

    private void GridOrders_SelectedRowsChanged(object sender, EventArgs e)
    {
        refreshBundlesGrid();
        updateInvoiceDetails();
    }

    private void btnClear_Click(object sender, EventArgs e)
    {
        resetSearchControls();
    }

    private void btnEditFilter_Click(object sender, EventArgs e)
    {
        using var frm = new CollationFilterDialog { SelectedFilterItems = SelectedFilterItems };
        frm.UpdateControls();
        if (frm.ShowDialog(this) == DialogResult.OK)
        {
            SelectedFilterItems = frm.SelectedFilterItems;
            updateFilterLabel();
        }
    }

    private void btnPreviewSelectedBundle_Click(object sender, EventArgs e)
    {
        if (!isCollationDialog())
            return;

        var order = getSelectedLabOrder();

        if (order == null)
        {
            MessageDlg.Warning("No lab order selected!");
            return;
        }

        var orderSlice = FaultHandler.Shield(() => PatientLabOrdersRepository.GetLabOrderFullDetails(order.InvoiceId));
        var bundleSlice = getSelectedResultBundle();

        if (orderSlice != null && bundleSlice != null && verifyPrintability(bundleSlice))
        {
            var bundles = new List<ResultBundlesForInvoice> { bundleSlice };
            printResultBundles(orderSlice, bundles, true);
        }

        focusInvoiceNumTextbox();
    }

    private int getPendingBundlesCount(WorkflowStageType wf)
    {
        var report = new CollatorsManifestReportBuilder
        {
            InvoiceDateFrom = DateTime.Now.Date.AddDays(-5),
            InvoiceDateTill = DateTime.Now.Date.AddDays(-1),
            BundleCutoffStage = wf
        };

        WaitFormControl.WaitOperation(this, () => FaultHandler.Shield(report.CompileReport));
        return report.Invoices.Count;
    }

    private bool isCollationDialog() => _dialogType == CollationDispatchDialogType.CollationDialog;

    private void btnPrintDispatchAllBundles_Click(object sender, EventArgs e)
    {
        var order = getSelectedLabOrder();
        if (order == null)
        {
            MessageDlg.Warning("No lab order selected!");
            return;
        }

        var orderSlice = FaultHandler.Shield(
            () => PatientLabOrdersRepository.GetLabOrderFullDetails(order.InvoiceId));
        
        var bundles = new List<ResultBundlesForInvoice>();
        var wfStage = isCollationDialog()
            ? (byte)WorkflowStageType.ReportFinalization
            : (byte)WorkflowStageType.ReportCollation;
        if (orderSlice != null && _activeResultBundles != null)
        {
            bundles.AddRange(_activeResultBundles.Where(x => x.WorkflowStage == wfStage));
        }

        /* TODO:
         * Print the bundle(s)
         * Show the collatable bundle list => get collation status
         * Collate
         */
        if (isCollationDialog())
        {
            printResultBundles(orderSlice, bundles, false);
            confirmAndPerformCollation(orderSlice, bundles);
        }
        else
        {
            if (CaptchaDialog.ConfirmCaptcha(2, false, false))
                performDispatch(orderSlice, bundles);
        }

        executeInvoiceIdSearch(order.InvoiceId);

        //FaultHandler.Shield(
        //    () =>
        //        AsyncEsbMessenger.SendMessagesAsync(
        //            EsbMessageChain.CreateNew(AsyncEsbMessenger.DealyedSleepSeconds)
        //                           .AddLabOrderWorkflowHook(order.InvoiceId)));

        focusInvoiceNumTextbox();
    }

    private void btnPrintDispatchSelectedBundle_Click(object sender, EventArgs e)
    {
        var order = getSelectedLabOrder();
        if (order == null)
        {
            MessageDlg.Warning("No lab order selected!");
            return;
        }

        var preventPrinting = ModifierKeys == Keys.Shift;

        var orderSlice =
            FaultHandler.Shield(() => PatientLabOrdersRepository.GetLabOrderFullDetails(order.InvoiceId));
        var bundles = new List<ResultBundlesForInvoice>();
        if (isCollationDialog())
        {
            var slices = getSelectedResultBundles();

            foreach (var slice in slices)
            {
                if (orderSlice != null && verifyPrintability(slice))
                {
                    bundles.Add(slice);
                }
                else
                {
                    MessageDlg.Error($"The result bundle ({slice.DisplayTitle}) cannot be collated!");
                }
            }
        }
        else
        {
            var slice = getSelectedResultBundle();
            if (orderSlice != null && slice != null)
            {
                if (!verifyPrintability(slice))
                {
                    MessageDlg.Error("This result bundle cannot be dispatched!");
                    //TODO: log the error cause
                    return;
                }

                /* TODO:
                 * Print the bundle(s)
                 * Show the collatable bundle list => get collation status
                 * Collate
                 */
                bundles.Add(slice);
            }
        }

        if (bundles.Count > 0)
        {
            if (isCollationDialog())
            {
                if (!preventPrinting)
                    printResultBundles(orderSlice, bundles, false);
                confirmAndPerformCollation(orderSlice, bundles);
            }
            else
            {
                if (orderSlice.DueAmount > 0)
                {
                    var amountDue = SharedUtilities.MoneyToString(orderSlice.DueAmount, true);
                    var msg =
                        $"This invoice has outstanding balance of {amountDue}!\nReports cannot be dispatched for this lab order.";
                    MessageDlg.Warning(msg);
                }
                else if (MessageDlg.Confirm($"Really dispatch {bundles.First().DisplayTitle}?"))
                {
                    performDispatch(orderSlice, bundles);
                }
            }

            executeInvoiceIdSearch(order.InvoiceId);
            //FaultHandler.Shield(
            //    () =>
            //        AsyncEsbMessenger.SendMessagesAsync(
            //            EsbMessageChain.CreateNew(AsyncEsbMessenger.DealyedSleepSeconds)
            //                           .AddLabOrderWorkflowHook(order.InvoiceId)));
            focusInvoiceNumTextbox();
        }
    }

    private void btnSearchInvoiceId_Click(object sender, EventArgs e)
    {
        txtPatientId.Text = "";
        cboReportStatus.SelectedIndex = -1;

        searchByInvoiceId();
    }

    private void btnSearchOrderStatus_Click(object sender, EventArgs e)
    {
        if (cboReportStatus.SelectedIndex == -1)
        {
            MessageDlg.Warning("Please select a valid Order Status!");
            return;
        }

        txtInvoiceNum.Text = "";
        txtPatientId.Text = "";

        searchByWorkflowStageAndDateRange();
    }

    private void btnSearchPatientId_Click(object sender, EventArgs e)
    {
        txtInvoiceNum.Text = "";
        cboReportStatus.SelectedIndex = -1;

        searchByPatientIdAndDateRange();
    }

    private void chkHideCollatedBundles_CheckedChanged(object sender, EventArgs e) => refreshBundlesGrid();

    private string coalesceFilterPrefixes(IEnumerable<char> filters, bool forDisplay)
    {
        var enumerable = filters as IList<char> ?? filters.ToList();
        return string.Join(forDisplay ? " " : ",", enumerable);

        var sb = new StringBuilder();
        foreach (var filter in enumerable)
        {
            sb.Append(string.Format(forDisplay ? @" {0} " : @"{0},", filter));
        }

        var result = sb.ToString();
        return (!forDisplay && result.Length > 0) ? result.Substring(0, result.Length - 1) : result;
    }

    private void confirmAndPerformCollation(LabOrderFullDetailsSlice order, List<ResultBundlesForInvoice> bundles)
    {
        using var frm = new CollationConfirmDialog();
        frm.CollatableResultBundles = bundles;
        frm.UpdateControl();
        if (frm.ShowDialog(this) == DialogResult.OK)
        {
            var collateList = frm.GetSelectedBundles();
            performCollation(order, collateList);
        }
    }

    private SearchedLabOrderInfo? getSelectedLabOrder() => gridOrders.SelectedRows.Count == 1
        ? gridOrders.SelectedRows[0].Tag as SearchedLabOrderInfo
        : null;

    private ResultBundlesForInvoice? getSelectedResultBundle() => gridResultBundles.SelectedRows.Count == 1
        ? gridResultBundles.SelectedRows[0].Tag as ResultBundlesForInvoice
        : null;

    private List<ResultBundlesForInvoice> getSelectedResultBundles()
    {
        var list = new List<ResultBundlesForInvoice>();
        if (gridResultBundles.SelectedRows.Count > 0)
        {
            foreach (Row row in gridResultBundles.SelectedRows)
            {
                var bundle = row.Tag as ResultBundlesForInvoice;
                if (bundle != null)
                {
                    list.Add(bundle);
                }
            }
        }

        return list;
    }

    private void gridCreateColumns()
    {
        var idx = 0;

        gridOrders.Columns.Clear();
        gridOrders.GridAddColumn(@"ptId", "Pt Id", idx++, 45);
        gridOrders.GridAddColumn(@"invId", "Invoice", idx++, 70);
        gridOrders.GridAddColumn(@"ptName", "Name", idx++, 230);
        gridOrders.GridAddColumn(@"ptSex", "Sex", idx++, 40);
        gridOrders.GridAddColumn(@"ordDate", "Ordered On", idx++, 100);
        gridOrders.GridAddColumn(@"refPhy", "Refd. By", idx++, 235);

        idx = 0;
        gridResultBundles.Columns.Clear();
        gridResultBundles.GridAddColumn(@"name", "Bundle", idx++, 120);
        gridResultBundles.GridAddColumn(@"tests", "Test(s)", idx++, 470);
        gridResultBundles.GridAddColumn(@"status", "Status", idx++, 120);
        gridResultBundles.GridAddColumn(@"time", "Last Update", idx++, 100);
        gridResultBundles.GridAddColumn(@"staff", "Performed By", idx++, 190);

        idx = 0;
        gridRecentUpdates.Columns.Clear();
        gridRecentUpdates.GridAddColumn(@"ptId", "Pt Id", idx++, 40);
        gridRecentUpdates.GridAddColumn(@"invId", "Invoice", idx++, 60);
        gridRecentUpdates.GridAddColumn(@"ptName", "Name", idx++, 130);
        gridRecentUpdates.GridAddColumn(@"status", "Status", idx++, 60);
        gridRecentUpdates.GridAddColumn(@"lab", "Lab", idx++, 70);
        gridRecentUpdates.GridAddColumn(@"time", "When", idx++, 60);
    }

    private void gridRecentUpdates_KeyUp(object sender, KeyEventArgs e)
    {
        if (e.KeyCode == Keys.Enter)
            loadRecentlyUpdatedBundle();
    }

    private void gridResultBundles_SelectedRowsChanged(object sender, EventArgs e)
    {
        var order = getSelectedLabOrder();
        if (isCollationDialog())
        {
            var bundles = getSelectedResultBundles();
            updateCollationButtons(order, bundles);
        }
        else
        {
            var bundle = getSelectedResultBundle();
            updateDispatchButtons(order, bundle);
        }
    }

    private void updateDispatchButtons(SearchedLabOrderInfo? order, ResultBundlesForInvoice? bundle)
    {
        toggleDispatchButtons(false);

        if (order == null || bundle == null)
            return;

        if (order != null)
        {
            var stage = order.WorkflowStage;
            btnPrintDispatchAllBundles.Enabled = stage == WorkflowStageType.ReportCollation;
        }

        if (bundle != null)
        {
            var stage = (WorkflowStageType)bundle.WorkflowStage;
            btnPrintDispatchSelectedBundle.Enabled = stage == WorkflowStageType.ReportCollation;
        }

        if (!isCollationDialog() && order.DueAmount > 0)
        {
            // disable buttons for dispatchers if invoice is unpaid
            toggleDispatchButtons(false);
        }
    }

    private void toggleDispatchButtons(bool enable)
    {
        btnPreviewSelectedBundle.Enabled = enable;
        btnPrintDispatchSelectedBundle.Enabled = enable;
        btnPrintDispatchAllBundles.Enabled = enable;
    }

    private void loadRecentlyUpdatedBundle()
    {
        if (gridRecentUpdates.SelectedRows.Count == 1)
        {
            try
            {
                var invoiceId = gridRecentUpdates.SelectedRows[0].Tag is long
                    ? (long)gridRecentUpdates.SelectedRows[0].Tag
                    : 0;

                if (invoiceId > 0)
                {
                    resetSearchControls();
                    txtInvoiceNum.Text = invoiceId.ToString();
                    searchByInvoiceId();
                }
            }
            catch
            {
            }
        }
    }

    private void performCollation(LabOrderFullDetailsSlice order, List<ResultBundlesForInvoice> bundles)
    {
        if (bundles == null || bundles.Count == 0)
        {
            return;
        }

        var sbFailed = new StringBuilder();
        foreach (var bundle in bundles)
        {
            var wfBundle = (WorkflowStageType)bundle.WorkflowStage;

            // Skip if over
            if (wfBundle >= WorkflowStageType.ReportCollation)
            {
                continue;
            }

            var tmpBundle = bundle;
            FaultHandler.Shield(
                () =>
                {
                    if (ResultBundlesRepository.VerifyResultBundleWorkflowStageMatch(tmpBundle.Id, wfBundle))
                    {
                        var wfNewStage = sanitizeWorkflowStage(
                            tmpBundle.PostReportCollationWorkflowStage,
                            WorkflowStageType.ReportCollation);

                        ResultBundlesRepository.UpdateResultBundleWorkflowStage(tmpBundle.Id, wfNewStage);

                        AsyncEsbMessenger.SendMessagesAsync(
                            EsbMessageChain.CreateNew().AddResultBundleWorkflowHook(order.InvoiceId, tmpBundle.Id));

                        AuditTrailRepository.LogOrderWorkflowEvent(
                            AuditEventType.wfReportCollated,
                            order.InvoiceId,
                            tmpBundle.Id,
                            null,
                            wfNewStage);

                        /*
    if (wfNewStage == WorkflowStageType.ReportCollation)
        RecentlyUpdatedResultBundlesRepository.AddResultBundleToRecentUpdatesList(
            bundle.Id,
            order.InvoiceId,
            wfNewStage,
            SortPriorityType.Normal);
    */
                    }
                    else
                    {
                        sbFailed.AppendLine(
                            string.Format("{0} - {1}", tmpBundle.DisplayTitle, tmpBundle.ComponentLabTests));
                    }
                });
        }

        /*
        var estimator = new LabOrderWorkflowStageEstimator(order.InvoiceId);
        estimator.EstimateWorkflowStage();
        */
        //AppSysRepository.SendLabOrderWorkflowStageEstimateRequest(order.InvoiceId);
        FaultHandler.Shield(
            () =>
            {
                AsyncEsbMessenger.SendMessagesAsync(
                    EsbMessageChain.CreateNew()
                        .AddWorkflowEstimator(
                            order
                                .InvoiceId));
                AsyncEsbMessenger.SendMessagesAsync(
                    EsbMessageChain.CreateNew(
                            AsyncEsbMessenger
                                .DealyedSleepSeconds)
                        .AddLabOrderWorkflowHook(
                            order
                                .InvoiceId));
            });

        // send delayed SMS hook request


        if (sbFailed.Length > 0)
        {
            var msg = "The following report bundles could not be collated:\n" + sbFailed;
            msg += "\nSomeone else may have processed these report bundles in the meantime.";
            msg += "\nPlease reload the lab order to fetch latest updates.";
            MessageDlg.Warning(msg);
        }
    }

    private void performDispatch(LabOrderFullDetailsSlice order, List<ResultBundlesForInvoice> bundles)
    {
        if (bundles == null || bundles.Count == 0)
            return;

        var sbFailed = new StringBuilder();
        foreach (var bundle in bundles)
        {
            var wfBundle = (WorkflowStageType)bundle.WorkflowStage;

            // Skip if over
            if (wfBundle >= WorkflowStageType.ReportDispatch)
                continue;

            var tmpBundle = bundle;
            FaultHandler.Shield(
                () =>
                {
                    if (ResultBundlesRepository.VerifyResultBundleWorkflowStageMatch(tmpBundle.Id, wfBundle))
                    {
                        ResultBundlesRepository.UpdateResultBundleWorkflowStage(
                            tmpBundle.Id,
                            WorkflowStageType
                                .ReportDispatch);

                        AsyncEsbMessenger.SendMessagesAsync(
                            EsbMessageChain
                                .CreateNew()
                                .AddResultBundleWorkflowHook(
                                    order
                                        .InvoiceId,
                                    tmpBundle
                                        .Id));

                        AuditTrailRepository.LogOrderWorkflowEvent(
                            AuditEventType.wfReportDispatched,
                            order.InvoiceId,
                            tmpBundle.Id,
                            null,
                            WorkflowStageType.ReportDispatch);

                        // end of the line...
                        RecentlyUpdatedResultBundlesRepository
                            .RemoveResultBundleFromRecentUpdatesList(tmpBundle.Id);
                    }
                    else
                    {
                        sbFailed.AppendLine(
                            string.Format("{0} - {1}", tmpBundle.DisplayTitle, tmpBundle.ComponentLabTests));
                    }
                });
        }

        /*
        var estimator = new LabOrderWorkflowStageEstimator(order.InvoiceId);
        estimator.EstimateWorkflowStage();
        */
        //AppSysRepository.SendLabOrderWorkflowStageEstimateRequest(order.InvoiceId);
        FaultHandler.Shield(
            () =>
            {
                AsyncEsbMessenger.SendMessagesAsync(
                    EsbMessageChain.CreateNew().AddWorkflowEstimator(order.InvoiceId)
                );
                AsyncEsbMessenger.SendMessagesAsync(
                    EsbMessageChain.CreateNew(AsyncEsbMessenger.DealyedSleepSeconds)
                        .AddLabOrderWorkflowHook(order.InvoiceId)
                );
            });

        if (sbFailed.Length > 0)
        {
            var msg = "The following report bundles could not be dispatched:\n" + sbFailed;
            msg += "\nSomeone else may have processed these report bundles in the meantime.";
            msg += "\nPlease reload the lab order to fetch latest updates.";
            MessageDlg.Warning(msg);
        }
    }

    private bool performLabOrderSearchOperation()
    {
        bool success;

        var cur = WaitFormControl.WaitStart(this, description: "Searching lab orders...");
        SuspendLayout();
        _searchResults.BeginUpdate();

        try
        {
            _searchResults.Clear();
            var orders = FaultHandler.Shield(() => _searchProvider.SearchLabOrders());
            success = orders != null;
            if (success)
            {
                _searchResults.AddRange(orders);
            }
        }
        finally
        {
            _searchResults.EndUpdate();
            ResumeLayout();
            WaitFormControl.WaitEnd(this, cur);
        }

        return success;
    }

    private void populateBundlesGrid(SearchedLabOrderInfo slice)
    {
        gridResultBundles.BeginInit();
        try
        {
            gridResultBundles.DataRows.Clear();
            if (slice != null)
            {
                _activeResultBundles = FaultHandler.Shield(
                    () => ResultBundlesRepository.GetActiveResultBundlesForInvoice(
                        slice
                            .InvoiceId));
                if (_activeResultBundles != null)
                {
                    foreach (var bundle in _activeResultBundles)
                    {
                        var wfStage = (WorkflowStageType)bundle.WorkflowStage;
                        if (chkHideCollatedBundles.Checked)
                        {
                            if (isCollationDialog())
                            {
                                // skip if the bundle is already collated
                                if (wfStage >= WorkflowStageType.ReportCollation)
                                {
                                    continue;
                                }
                            }
                            else
                            {
                                // skip if the bundle is already dispatched
                                if (wfStage >= WorkflowStageType.ReportDispatch)
                                {
                                    continue;
                                }
                            }
                        }

                        var row = gridResultBundles.DataRows.AddNew();
                        row.Cells["name"].Value = bundle.DisplayTitle;
                        row.Cells["tests"].Value = bundle.ComponentLabTests;
                        row.Cells["status"].Value = EnumUtils.EnumDescription(wfStage);
                        row.Cells["time"].Value = SharedUtilities.DateTimeToUltraShortString(bundle.LastUpdated);

                        var wf = (WorkflowStageType)bundle.WorkflowStage;
                        if (wf == WorkflowStageType.ReportFinalization)
                        {
                            row.Cells["staff"].Value = bundle.FinalizingConsultantName;
                        }
                        else
                        {
                            row.Cells["staff"].Value = FaultHandler.Shield(
                                () => ResultBundlesRepository.GetResultBundleWorkflowUserName(bundle.Id, wf));
                        }

                        /*if (isCollationDialog())
                        {
                            row.Cells["staff"].Value =
                                ResultBundlesRepository.GetResultBundleWorkflowUserName(bundle.Id,
                                                                                        WorkflowStageType
                                                                                            .ReportCollation);
                        }
                        else
                        {
                            row.Cells["staff"].Value = bundle.FinalizingConsultantName;
                            ResultBundlesRepository.GetResultBundleWorkflowUserName(bundle.Id,
                                                                                    WorkflowStageType
                                                                                        .ReportCollation);
                        }*/
                        row.Tag = bundle;
                        row.Height = 25;
                        row.EndEdit();
                    }
                }
            }
        }
        finally
        {
            gridResultBundles.EndInit();
        }

        gridResultBundles.Refresh();
        //gridResultBundles.Focus();
    }

#if COLLATION_RECENT_BUNDLES
        private List<RecentlyUpdatedResultBundleDetails> sortRecentBundlesList(
            IEnumerable<RecentlyUpdatedResultBundleDetails> src)
        {
            var list = new List<RecentlyUpdatedResultBundleDetails>();
            list.AddRange(!_recentHideUnarchived
                ? src
                : src.Where(b =>
                    ResultBundlesRepository.FindById((long) b.ResultBundleId).TestResultType !=
                    (byte) TestResultType.Unarchived));

            return _recentSortOldestFirst
                ? list.OrderBy(x => x.LastUpdated).ToList()
                : list.OrderByDescending(x => x.LastUpdated).ToList();
        }
        
        private void populateRecentGrid(List<RecentlyUpdatedResultBundleDetails> list)
        {
            
            var cursor = Cursor;
            Cursor = Cursors.WaitCursor;
            SuspendLayout();
            gridRecentUpdates.BeginInit();
            try
            {
                gridRecentUpdates.DataRows.Clear();

                foreach (var item in sortRecentBundlesList(list))
                {
                    var row = gridRecentUpdates.DataRows.AddNew();
                    row.Cells[@"ptId"].Value = item.OrderId;
                    row.Cells[@"invId"].Value = item.InvoiceId.ToString(CultureInfo.InvariantCulture);
                    row.Cells[@"ptName"].Value = item.PatientFullName;
                    row.Cells[@"status"].Value = EnumUtils.EnumDescription((WorkflowStageType) item.WorkflowStage);
                    row.Cells[@"lab"].Value = item.LabName;
                    row.Cells[@"time"].Value = HumanReadableTime.ToHumanReadableTime(item.LastUpdated);
                    row.Tag = item.InvoiceId;
                    row.EndEdit();
                }
            }
            finally
            {
                gridRecentUpdates.EndInit();
                gridRecentUpdates.Refresh();
                ResumeLayout(true);
                Cursor = cursor;
            }
        }

        private void handleUpdatedBundlesAvailable(List<RecentlyUpdatedResultBundleDetails> list)
        {
            if (list == null || list.Count < 1)
                return;

            if (gridRecentUpdates.InvokeRequired)
            {
                var cb = new PopulateRecentGridCallback(populateRecentGrid);
                Invoke(cb, new object[] {list});
            }
            else
            {
                populateRecentGrid(list);
            }

            // list.Clear();
        }
#endif

    private void printResultBundles(
        LabOrderFullDetailsSlice order,
        List<ResultBundlesForInvoice> bundles,
        bool showPreview)
    {
        var dtoInvoice = InvoiceSlicePrintDto.AssembleFrom(order, false);
        foreach (var bundle in bundles)
        {
            var resultType = (TestResultType)bundle.TestResultType;
            if (resultType == TestResultType.Unarchived)
            {
                continue;
            }

            var compiler = new ResultBundlePrintDtoCompiler(bundle.Id);
            var dtoBundle = compiler.CompileBundle();

            switch (resultType)
            {
                case TestResultType.Discrete:
                    var discreteItems = compiler.GetDiscreteResultItems();
                    PrintHelper.PrintDiscreteReport(showPreview, dtoInvoice, dtoBundle, discreteItems);
                    break;
                case TestResultType.UserTemplate:
                case TestResultType.Template:
                    var templateItem = compiler.GetTemplateResultContent();
                    //File.WriteAllText("cbc.rtf", templateItem.ResultContent);
                    PrintHelper.PrintTemplateReport(showPreview, dtoInvoice, dtoBundle, templateItem);
                    break;
            }

            var evType = showPreview ? AuditEventType.wfReportPreviewed : AuditEventType.wfReportPrinted;
            FaultHandler.Shield(
                () => AuditTrailRepository.LogOrderWorkflowEvent(
                    evType,
                    order.InvoiceId,
                    bundle.Id,
                    null,
                    (WorkflowStageType)bundle.WorkflowStage,
                    null));
            // TODO: audit log
        }
    }

    private void refreshBundlesGrid() => populateBundlesGrid(getSelectedLabOrder());

    private void resetLeftGrids()
    {
        gridOrders.DataRows.Clear();
        gridResultBundles.DataRows.Clear();
        updateInvoiceDetails();
    }

    private void resetSearchControls()
    {
        txtInvoiceNum.Text = string.Empty;
        txtPatientId.Text = string.Empty;

        cboReportStatus.SelectedIndex = -1;
        cboBundleStatus.SelectedIndex = -1;

        dtOrderFrom.DateTime = DateTime.Today;
        dtOrderTo.DateTime = DateTime.Today;

        dtPatientFrom.DateTime = DateTime.Today;
        dtPatientTo.DateTime = DateTime.Today;

        dteBundleFrom.DateTime = DateTime.Today;
        dteBundleTo.DateTime = DateTime.Today;

        updateInvoiceDetails();
    }

    private DateTime sanitizeDateTime(DateTime value) =>
        value <= DateTime.MinValue || value >= DateTime.MaxValue ? DateTime.Today : value;

    private WorkflowStageType sanitizeWorkflowStage(byte? postOpStage, WorkflowStageType defaultStage)
    {
        if (postOpStage != null)
        {
            var stage = (byte)postOpStage;
            if (stage <= (byte)WorkflowStageType.OrderFulfillment)
            {
                return (WorkflowStageType)stage;
            }
        }

        return defaultStage;
    }

    private void executeInvoiceIdSearch(long invoiceId)
    {
        FaultHandler.Shield(PatientLabOrdersRepository.Reset);
        _searchProvider = new ActiveLabOrderSearchByInvoiceIdProvider(invoiceId);

        updateLeftGrids(true);
        gridOrders.Focus();
    }

    private void searchByInvoiceId()
    {
        resetLeftGrids();
        long invoiceId = -1;
        var text = txtInvoiceNum.Text.Trim();
        // queitly ignore empty strings - spurious key-up event is generated 
        // when the form is loaded
        if (string.IsNullOrEmpty(text))
            return;

        if (!long.TryParse(text, out invoiceId) || (invoiceId <= 0))
        {
            MessageDlg.Error("Invalid invoice number specified!");
            txtInvoiceNum.Focus();
            return;
        }

        executeInvoiceIdSearch(invoiceId);
    }

    private void searchByWorkflowStageAndDateRange()
    {
        var wfStage = cboReportStatus.SelectedIndex switch
        {
            0 => WorkflowStageType.ReportFinalization,
            1 => WorkflowStageType.ResultValidation,
            2 => WorkflowStageType.ResultEntry,
            3 => WorkflowStageType.ReportCollation,
            4 => WorkflowStageType.ReportDispatch,
            5 => WorkflowStageType.RepeatProcedure,
            6 => WorkflowStageType.OrderFulfillment,
            7 => WorkflowStageType.Canceled,
            8 => WorkflowStageType.OrderEntry,
            9 => WorkflowStageType.Unknown,
            _ => WorkflowStageType.Unknown
        };

        var dtFrom = sanitizeDateTime(dtOrderFrom.DateTime).Date;
        var dtTo = SharedUtilities.LastInstantOfDay(sanitizeDateTime(dtOrderTo.DateTime));

        FaultHandler.Shield(PatientLabOrdersRepository.Reset);
        _searchProvider = null;
        if (_applyFilter)
        {
            if (wfStage == WorkflowStageType.Unknown)
            {
                _searchProvider = new ActiveLabOrderFilterByDateRangeProvider(dtFrom, dtTo, SelectedFilterItems);
            }
            else
            {
                _searchProvider = new ActiveLabOrderFilterByWorkflowAndDateRangeProvider(
                    wfStage,
                    dtFrom,
                    dtTo,
                    SelectedFilterItems);
            }
        }
        else
        {
            if (wfStage == WorkflowStageType.Unknown)
            {
                _searchProvider = new ActiveLabOrderSearchByDateRangeProvider(dtFrom, dtTo);
            }
            else
            {
                _searchProvider = new ActiveLabOrderSearchByWorkflowAndDateRangeProvider(wfStage, dtFrom, dtTo);
            }
        }

        updateLeftGrids(true);
        gridOrders.Focus();
    }

    private void searchByPatientIdAndDateRange()
    {
        var idPatient = txtPatientId.Text.Trim().ToUpperInvariant();
        if (string.IsNullOrEmpty(idPatient) || !_rexPatientId.IsMatch(idPatient))
        {
            MessageDlg.Error("Invalid Patient ID specified!");
            focusPatientIdTextbox();
            return;
        }

        var dtFrom = sanitizeDateTime(dtPatientFrom.DateTime.Date);
        var dtTo = SharedUtilities.LastMinuteOfDay(sanitizeDateTime(dtPatientTo.DateTime));

        FaultHandler.Shield(PatientLabOrdersRepository.Reset);
        _searchProvider = new ActiveLabOrderSearchByPatientIdAndDateRangeProvider(idPatient, dtFrom, dtTo);

        updateLeftGrids(true);
        gridOrders.Focus();
    }

    private void txtInvoiceId_KeyUp(object sender, KeyEventArgs e)
    {
        if (e.KeyCode == Keys.Enter)
        {
            e.Handled = true;
            searchByInvoiceId();
        }
    }

    private void updateCollationButtons(SearchedLabOrderInfo order, IEnumerable<ResultBundlesForInvoice> bundles)
    {
        btnPreviewSelectedBundle.Enabled = false;
        btnPrintDispatchSelectedBundle.Enabled = false;
        btnPrintDispatchAllBundles.Enabled = false;

        if (order != null)
        {
            var stage = order.WorkflowStage;
            btnPrintDispatchAllBundles.Enabled = stage == WorkflowStageType.ReportFinalization;
        }

        var canPrint = false;
        foreach (var bundle in bundles)
        {
            if (bundle != null)
            {
                canPrint = (WorkflowStageType)bundle.WorkflowStage == WorkflowStageType.ReportFinalization;
                if (!canPrint)
                    return;
            }
        }

        btnPreviewSelectedBundle.Enabled = canPrint;
        btnPrintDispatchSelectedBundle.Enabled = canPrint;
    }

    private void updateDialogControlsByType()
    {
        btnPreviewSelectedBundle.Enabled = false;
        btnPrintDispatchAllBundles.Enabled = false;
        btnPrintDispatchSelectedBundle.Enabled = false;

        if (!isCollationDialog())
        {
            btnPrintDispatchAllBundles.Text = @"Dispatch All Bundles";
            btnPrintDispatchAllBundles.Image = Resources.mail_all24;

            btnPrintDispatchSelectedBundle.Text = @"Dispatch Selected Bundle";
            btnPrintDispatchSelectedBundle.Image = Resources.mail24;

            chkHideCollatedBundles.Text = @"Hide Delivered Bundles";
        }
    }

    private void updateFilterLabel() => lblFilterItems.Text = coalesceFilterPrefixes(SelectedFilterItems, false);

    private void updateLeftGrids(bool performDatabaseSearch)
    {
        resetLeftGrids();
        gridOrders.BeginInit();
        try
        {
            if (_searchProvider != null)
            {
                if (performDatabaseSearch)
                {
                    performLabOrderSearchOperation();
                }

                foreach (var slice in getCuratedSearchResults())
                {
                    var row = gridOrders.DataRows.AddNew();
                    row.Cells["ptId"].Value = slice.OrderId;
                    row.Cells["invId"].Value = slice.InvoiceId.ToString();
                    row.Cells["ptName"].Value = slice.PatientName;
                    row.Cells["ptSex"].Value = slice.Sex.ToString();
                    row.Cells["ordDate"].Value = SharedUtilities.DateTimeToUltraShortString(slice.OrderDateTime);
                    row.Cells["refPhy"].Value = slice.ReferrerName;
                    row.Tag = slice;
                    row.Height = 23;
                    row.EndEdit();
                }
            }
        }
        finally
        {
            gridOrders.EndInit();
        }

        gridOrders.Refresh();
    }

    private bool verifyPrintability(ResultBundlesForInvoice bundle)
    {
        if (!FaultHandler.Shield(
                () => ResultBundlesRepository.VerifyResultBundleWorkflowStageMatch(
                    bundle.Id,
                    (WorkflowStageType)bundle.WorkflowStage)))
        {
            MessageDlg.Warning(
                "Workflow status has changed on the server!\nSomeone else may have processed this report bundle meanwhile.\nPlease reload this invoice to get the latest status.");
            return false;
        }

        if (isCollationDialog())
        {
            if (bundle.WorkflowStage != (byte)WorkflowStageType.ReportFinalization)
            {
                MessageDlg.Warning(
                    "This report bundle can not be collated. Only finalized bundles are eligible for collation.");
                return false;
            }
        }
        else
        {
            if (bundle.WorkflowStage != (byte)WorkflowStageType.ReportCollation)
            {
                MessageDlg.Warning(
                    "This report bundle can not be dispatched. Only collated bundles are eligible for dispatch.");
                return false;
            }
        }

        return true;
    }

    private void updateInvoiceDetails()
    {
        lblInvoiceNum.Text = string.Empty;
        lblPatientId.Text = string.Empty;
        lblInvoiceDue.Text = string.Empty;
        imgDueStatus.Image = null;

        var order = getSelectedLabOrder();
        if (order != null)
        {
            lblInvoiceNum.Text = order.InvoiceId.ToString(CultureInfo.InvariantCulture);
            lblPatientId.Text = order.OrderId;
            var slice = FaultHandler.Shield(() => InvoiceMasterRepository.GetInvoiceFinancialDetails(order.InvoiceId));
            if (slice == null) return;

            if (slice.DueAmount > 0m)
            {
                lblInvoiceDue.ForeColor = Color.Firebrick;
                lblInvoiceDue.Text = SharedUtilities.MoneyToStringPlain(slice.DueAmount);
                imgDueStatus.Image = Resources.alert_triangle_red_24;
            }
            else
            {
                lblInvoiceDue.ForeColor = Color.Green;
                lblInvoiceDue.Text = "0";
                imgDueStatus.Image = Resources.ok_24;
            }
        }
    }

    private void chkApplyFilter_CheckedChanged(object sender, EventArgs e) => _applyFilter = chkApplyFilter.Checked;

    /// <summary>
    /// Returns the search results. This method filters the result-set according to the selected alphabetic filter
    /// tab.
    /// </summary>
    private IEnumerable<SearchedLabOrderInfo> getCuratedSearchResults()
    {
        if (tabFilter.SelectedIndex == 0)
            return _searchResults.OrderBy(x => x.InvoiceId).ToList();

        var filterChar = Convert.ToChar(tabFilter.SelectedIndex + 64);
        return _searchResults.Where(x => x.OrderId[0] == filterChar).OrderBy(x => x.InvoiceId).ToList();
    }

    private void tabFilter_SelectedIndexChanged(object sender, EventArgs e)
    {
        updateLeftGrids(false);
        gridOrders.Focus();
    }

    private void btnExit_ItemClick(object sender, ItemClickEventArgs e) => Close();

    private void setQuickDateRange(int dayStart, int dayEnd)
    {
        var dtStart = DateTime.Now.AddDays(dayStart);
        var dtEnd = DateTime.Now.AddDays(dayEnd);

        dtPatientFrom.DateTime = dtStart;
        dtOrderFrom.DateTime = dtStart;
        dteBundleFrom.DateTime = dtStart;

        dtPatientTo.DateTime = dtEnd;
        dtOrderTo.DateTime = dtEnd;
        dteBundleTo.DateTime = dtEnd;
    }

    private void miToday_ItemClick(object sender, ItemClickEventArgs e) => setQuickDateRange(0, 0);

    private void miYesterday_ItemClick(object sender, ItemClickEventArgs e) => setQuickDateRange(-1, -1);

    private void miLast3Days_ItemClick(object sender, ItemClickEventArgs e) => setQuickDateRange(-3, 0);

    private void miLast1Week_ItemClick(object sender, ItemClickEventArgs e) => setQuickDateRange(-7, 0);

    private void gridResultBundles_KeyUp(object sender, KeyEventArgs e)
    {
        if (e.KeyCode == Keys.F12)
        {
            var bundle = getSelectedResultBundle();
            var order = getSelectedLabOrder();
            if (bundle != null && order != null)
            {
                var orderDetails = string.Format(
                    "{0} ({2}) #{1}",
                    order.OrderId,
                    order.InvoiceId,
                    SharedUtilities.DateToStringOnlySlash(order.OrderDateTime));
                ResultBundleAuditTrailViewerDialog.ExecuteDialog(
                    this,
                    bundle.Id,
                    orderDetails,
                    bundle.ComponentLabTests);
            }
        }
    }

    private void btnSearchBundleStatus_Click(object sender, EventArgs e)
    {
        if (cboBundleStatus.SelectedIndex == -1)
        {
            MessageDlg.Warning("Please select a valid Result Bundle Status filter criteria!");
            cboBundleStatus.Focus();
            return;
        }

        txtInvoiceNum.Text = "";
        txtPatientId.Text = "";

        searchByBundleWorkflowStageAndDateRange();
    }

    private void searchByBundleWorkflowStageAndDateRange()
    {
        var wfStart = WorkflowStageType.OrderEntry;
        var wfEnd = WorkflowStageType.OrderEntry;
        var repeatProcedure = false;
        switch (cboBundleStatus.SelectedIndex)
        {
            case 0:
                // Pending collation
                wfEnd = WorkflowStageType.ReportFinalization;
                break;
            case 1:
                // Pending dispatch
                wfEnd = WorkflowStageType.ReportCollation;
                break;
            case 2:
                // Repeat
                wfStart = WorkflowStageType.RepeatProcedure;
                repeatProcedure = true;
                break;
        }

        var dtFrom = sanitizeDateTime(dteBundleFrom.DateTime).Date;
        var dtTo = SharedUtilities.LastInstantOfDay(sanitizeDateTime(dteBundleTo.DateTime));

        PatientLabOrdersRepository.Reset();
        _searchProvider = null;
        if (repeatProcedure)
        {
            _searchProvider = new LabOrderSearchByDateRangeAndResultBundleWorkflowProvider(wfStart, dtFrom, dtTo);
        }
        else
        {
            _searchProvider = new LabOrderSearchByDateRangeAndResultBundleWorkflowBetweenProvider(
                wfStart,
                wfEnd,
                dtFrom,
                dtTo);
        }

        updateLeftGrids(true);
        gridOrders.Focus();
    }

    private void chkHideUnarchivedTests_CheckedChanged(object sender, EventArgs e) => updateRecencyFilterSettings();

    private void updateRecencyFilterSettings()
    {
#if COLLATION_RECENT_BUNDLES
            _recentHideUnarchived = chkHideUnarchivedTests.Checked;
            _recentSortOldestFirst = chkSortOldestFirst.Checked;
#endif
    }

    private void chkSortOldestFirst_CheckedChanged(object sender, EventArgs e) => updateRecencyFilterSettings();

    private delegate void PopulateRecentGridCallback(List<RecentlyUpdatedResultBundleDetails> list);
}