﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: DiscountCalculatorDialog.cs 687 2013-06-27 08:45:35Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Shared;
using Xceed.Grid;

namespace LabMaestro.Controls.Win
{
    public record struct TestDiscountInfo(decimal MaxDiscount, decimal Price);

    public partial class DiscountCalculatorDialog : XtraForm, IExecutableDialog
    {
        private readonly decimal _grossPayable;
        private readonly List<OrderableTestSlice> _orderedTests;
        private decimal _effectiveDiscount;
        public readonly int RoundBy = 5;

        public DiscountCalculatorDialog(List<OrderableTestSlice> orderedTests, decimal grossPayable)
        {
            InitializeComponent();
            WinUtils.ApplyTheme(this);

            _orderedTests = orderedTests;
            _grossPayable = grossPayable;
            _effectiveDiscount = 0m;
        }

        public decimal EffectiveDiscount => _effectiveDiscount;

        public bool ExecuteDialog(IWin32Window parent)
        {
            UpdateControls();
            return ShowDialog(parent) == DialogResult.OK;
        }

        public void UpdateControls() { }

        private void DiscountCalculatorDialog_Load(object sender, EventArgs e)
        {
            createGridColumns();
            populateGrid();

            lblGrossPayable.Text = SharedUtilities.MoneyToStringPlainCulture(_grossPayable);
            // TODO: roundBy should be loaded from global configurable
            var max = SharedUtilities.RoundToNearest(_orderedTests.Sum(slice => slice.MaxApplicableDiscount), RoundBy);
            lblMaxDiscount.Text = SharedUtilities.MoneyToStringPlainCulture(max);
            lblEffectiveDiscount.Text = SharedUtilities.MoneyToStringPlainCulture(0);

            GridControl.Select();
            GridControl.Focus();
        }

        private void createGridColumns()
        {
            var i = 0;

            GridControl.BeginInit();
            GridControl.Columns.Clear();

            var col = GridControl.GridAddColumn(@"name", "Test", i++, 300);
            col.ReadOnly = true;
            col.Fixed = true;

            col = GridControl.GridAddColumn(@"price", "Price", i++, 80);
            col.ReadOnly = true;
            col.Fixed = true;

            col = GridControl.GridAddColumn(@"max", "Max Disc.", i++, 80);
            col.ReadOnly = true;
            col.Fixed = true;

            col = GridControl.GridAddColumn(@"discount", "Discount", i++, 80);
            col.ReadOnly = false;
            col.BackColor = Color.Ivory;
            col.Font = new Font(col.Font, FontStyle.Bold);
            col.ForeColor = Color.Navy;

            col = GridControl.GridAddColumn(@"discount_pct", "%", i++, 80);
            col.ReadOnly = false;
            col.BackColor = Color.Ivory;
            col.Font = new Font(col.Font, FontStyle.Bold);
            col.ForeColor = Color.Firebrick;

            GridControl.EndInit();
        }

        private void btnCancel_Click(object sender, EventArgs e) => DialogResult = DialogResult.Cancel;

        private void populateGrid()
        {
            GridControl.BeginInit();
            try {
                GridControl.DataRows.Clear();
                foreach (var slice in _orderedTests) {
                    var row = GridControl.DataRows.AddNew();
                    row.Cells["name"].Value = slice.ShortName;
                    row.Cells["price"].Value = SharedUtilities.MoneyToStringPlainCulture(slice.ListPrice);
                    // TODO: roundBy should be loaded from global configurable
                    var maxDisc = SharedUtilities.RoundToNearest(slice.MaxApplicableDiscount, RoundBy);
                    row.Cells["max"].Value = SharedUtilities.MoneyToStringPlainCulture(maxDisc);
                    row.Cells["discount"].Value = "0";
                    row.Cells["discount_pct"].Value = "0";
                    row.Tag = new TestDiscountInfo(maxDisc, slice.ListPrice);
                    row.Height = 22;
                    row.EndEdit();
                }
            }
            finally {
                GridControl.EndInit();
            }
        }

        private void updateDiscountCells(DataRow row, decimal amount, decimal price)
        {
            row.Cells["discount"].Value = SharedUtilities.MoneyToStringPlainCulture(amount);
            row.Cells["discount_pct"].Value = SharedUtilities.CalculatePercentage(amount, price).ToString("0.0");
        }

        private void calculateEffectiveDiscount()
        {
            var result = 0m;
            foreach (DataRow row in GridControl.DataRows) {
                var amount = 0m;
                var pct = 0f;
                decimal.TryParse(row.Cells["discount"].Value.ToString().Trim(), out amount);
                float.TryParse(row.Cells["discount_pct"].Value.ToString().Trim(), out pct);
                var info = (TestDiscountInfo)row.Tag;
                if (amount > 0m)
                    updateDiscountCells(row, amount, info.Price);
                else if (pct > 0f) {
                    pct = Math.Max(0f, Math.Min(100f, pct));
                    amount = SharedUtilities.CalculatePercent(pct, info.Price, RoundBy);
                    updateDiscountCells(row, amount, info.Price);
                }
                else {
                    row.Cells["discount"].Value = "0";
                    row.Cells["discount_pct"].Value = "0";
                }

                if (info.MaxDiscount > 0 && amount > info.MaxDiscount) {
                    row.Cells["discount"].BackColor = Color.OrangeRed;
                    row.Cells["discount"].ForeColor = Color.Yellow;
                    row.Cells["discount_pct"].BackColor = Color.OrangeRed;
                    row.Cells["discount_pct"].ForeColor = Color.Yellow;
                }
                else {
                    row.Cells["discount"].BackColor = Color.Ivory;
                    row.Cells["discount"].ForeColor = Color.Navy;
                    row.Cells["discount_pct"].BackColor = Color.Ivory;
                    row.Cells["discount_pct"].ForeColor = Color.Navy;
                }

                result += amount;
            }

            // TODO: roundBy should be loaded from global configurable
            _effectiveDiscount = SharedUtilities.RoundToNearest(result, RoundBy);

            if (_effectiveDiscount < 0m)
                _effectiveDiscount = 0m;
            else if (_effectiveDiscount > _grossPayable)
                _effectiveDiscount = _grossPayable;
        }

        private void updateLabels()
        {
            calculateEffectiveDiscount();
            lblEffectiveDiscount.Text = SharedUtilities.MoneyToStringPlainCulture(_effectiveDiscount);
        }

        private void GridControl_CurrentCellChanged(object sender, EventArgs e) => updateLabels();

        private void btnApply_Click(object sender, EventArgs e)
        {
            calculateEffectiveDiscount();
            DialogResult = DialogResult.OK;
        }

        private void GridControl_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode != Keys.Delete) return;
            e.Handled = true;

            foreach (DataRow row in GridControl.DataRows) {
                if (row.IsSelected) {
                    row.Cells["discount"].Value = "0";
                    row.Cells["discount_pct"].Value = "0";
                    break;
                }
            }
        }
    }
}