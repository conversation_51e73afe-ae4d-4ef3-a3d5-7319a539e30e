﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="barManager.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>16, 6</value>
  </metadata>
  <metadata name="barAndDockingController.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>382, 6</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>46</value>
  </metadata>
  <metadata name="imageList1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>141, 6</value>
  </metadata>
  <metadata name="styleController.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>251, 6</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="btnReimburseSurcharge.ImageOptions.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAAudEVYdFRpdGxlAE1vZGVsO1Jlc2V0O0NsZWFyO1Jl
        c2V0TW9kZWw7Q2xlYXJNb2RlbDuS4pBZAAACgElEQVQ4T43RXUhTYRwG8FOpy9SbvpAoSkGK6IPMCjWX
        tJnNq1JQU0uzyFRUxKi5cPkxMsnERPMmc7TNXChhm2hoVCOUUat0K3WaoGihCH7MU9iHT+95nYcz2EV/
        +F38z3mf53DOYbpTYxgAvJGyTGa49CpDZg3haZGnVVlvpluMmXGRZPewFV5krPI0/jxTp7znUkAvroS9
        zAUpj5YdwwAxoMpmdSkyKVfK3efPFui+uAuL3uYmNP6dGwTb0UA8xPL8EPpv57FFwbujyX0vtwXOsPfL
        jFj1nxkb2Of1WCRYQz1+T7yHPTUGTyWHf+Tv3RnDZ/I1NmF4w7PEqMdL42Y4Wmuw2HqfqMGv0R4MX5Bh
        5LwMA0nRqA0OmuMLcho/rYZ90qq6Nf1jU1jQlGNeWw5HcyWWBl9jKEGKoXgJIYVJForiwG0mvoDMWi6c
        XNGhfTPOosg4hU59O+YbS8Ga2/D5jNjpBExRR1AS4N9/wEcUKCzwjVcZdF1fF2h41diHPlgkIXh3MgQf
        T4eiS3wQiu2bbUEizz0k4014EOuYs8qWBsPArEtYa5mGWGFEa3YO7MosXp8qDr13w10wsWUvHIq2b3y4
        ofc7IgrbcfxGG+xFWcArLfVTW4EnKQHAbDO1YFXRnYm4XC0/V2lavt4yhgemCYTLjZApDEgs6eRdqS7A
        NfU+F5MdqSsFZPwOxd/KEudqJ8OydTiWocapPD3Mi6D01mnEyndgBmqqZ7SY7sIC7i9wH2Uj4U/s4gqU
        3VNUep2ZBjS2CErRtJ/utTJ/3JFsraS/QojM+v8pcD7Qy23B0eR6CEVe2uIiLGlTFQ1z46Zg9ZV8BfwE
        uF1EkHMM8w+xWiuANzMq7gAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="btnDissociateCustomer.ImageOptions.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAACV0RVh0VGl0
        bGUAQ2xlYXI7RXJhc2U7UmVtb3ZlO0JhcnM7UmliYm9uO4eAg3MAAAKVSURBVDhPnZJrSJNRGMdPubnZ
        Gln5wSSKLFuKhJpGWmqgIhtBpaBdrK2QbFqaGeUlrbxSqJRkfsmWbYMKJUzXtBRqRCE48zJT81KKJUaQ
        Ot/CLP+dMzZTK4j+8ON53/Oc33PgPS/5xyyi2FF4lMVsYV4Mx3aTdwUnyNu8BDKQF0/6c5WkL0dJerOP
        szaT+cZURXFHxlFjrTJyJ33nmdKOkI5UBesT0iCXEQC/QcNk+6aUmFsz5l6A0pWbwGljpKF0nW/tE1Ka
        VfQ3WfA8MVr1Y6wbnL6cchMz4z1oz0/iMn0k4bRvzzaSFO3rP8nCxriI298/mcA9LMMkhaspw/RwM97I
        ZbgfsuVLssdaGds8bwANkx30il0V0yOvYK4qwWTVNUoJvg28QO9hKfoOSdF1IBzXfdzGmECS1aa58pIH
        +8LuTA01wXy3kFJkqVPdT9ETHYqeqBBKKAxSf1x0dTEwiZxUtbLCZJGiuEHdPjiKCXUBxjUFlvq1tR6d
        e4KsBMMQ5odL65zbN4sErkxkYXcrOnhZr3k2xCGzdhR193QYV2WDa6pGGz2tTRZAawAag71wfrVTh7vQ
        3o067CYsJy+Nyq3RPumfsMg2Blva0BLiCyOF1cf+njjn7GiSCHgbrTJziXBvVmV5TdfnebLG+BFBGTrk
        x6SgOdgbdX7uOOMk7tzA50lsMl1nPhFH5NSb06s/zMrlL0cQmP6IorOQEanEqeWizvU8u010P7t7i2wb
        IAyMvZq6v9Awc7ZyEDcMw9iepsMOK/6nq+AtL+1eJV7psVC2DWAfUOwddSE+KFHzPiBBi21KNbbGVcA3
        VjXtGX1Fv2yN1+zJSQ58Mhdb2BAHygqKM8XFCnt2pPz67xfG9gf+HyA/AV6Ksk3Y/ofNAAAAAElFTkSu
        QmCC
</value>
  </data>
  <data name="btnFindCustomer.ImageOptions.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAADJ0RVh0VGl0
        bGUAQ3VzdG9tZXI7RW1wbG95ZWU7UGVyc29uO0NvbnRhY3Q7VXNlcjtDbGllbnR+ETboAAADOUlEQVQ4
        T3WSZ0zTQRjGD0vFqi2oERUcoQwHooCLKpFS2n+tgkIQEpCgUUEKuAiWQB0oLgyjlSIYSEANASNRVBwQ
        CeIIYTQEEDA4on5QagQBgxocj3eIWj54yZPLve/7/O69QVJTU//qz5BKpRbrpOt4crmcr5Qp+ZyUs6Qx
        Houz/BiP+SIkJISZx9WcCHJtzgisqDu2vu2mRoaiKElHRqhHTqzCY2pAQICFuWcMQKlUjrulVQYYMzYN
        vKs6AlPNSfQ9ykR32QHcTQtFkty5ViVTWZp7xgBoy5YVib7PXxUG445WBaMuGO3ZG1Gn8cb1eAkSfMTg
        fDm+uWcMQCaT8S9ES4bL4r1h6qrGnSw19OGeOBnoCl3Qgp9xkrnDCoXi/wD5Wjk/M2Tpm9ytXvjW34Fv
        va341GLAi9LdOMQ5Yaen3Rt2seYe0pXi+FeUzkuUu5zRR3h9/fy6Gt/f1mCwIQPPStQ4zIm/hLnOyKVd
        8to1YtJ6gCrR4R+gM9mRvYKFQCAQ6SK8HnZfS8ZQsw49lUlo0ofhqMqpkeVYTfP+eaSJqnHfPELaksSk
        TeNAfnZnkQtqGXtmq5QNi+LKExToKopCZ+F2lMRIEL/abi/NTTjuZ09+1MeQh7FzyAP1bEKMCb9pdLBP
        wrscMcuzKtahufJoEAxhnsgOXoy8cA9UbLM3GgKnr2QbsDpWX7XDjpDHcZRCA9e2zvK+F21/syHZDa2G
        QHQW78KTkgTUpkfiSow3bu9fjto9TigPt63W+0/jRkEjP5N3I3Kmtj7FHW+vbsNwUyIGyjeg8eASVEY5
        40qkGFXxLjAV+2DoVihe5ktRrXZAnv+U09Q7ngHGV2yxNQ3ei8LQ9Y0YvLQKAxdXYqBoBfqLlqG/0AMf
        C9zRl78EvXlu6C1Yhp4CLxT42/RR72QGsCrdPA1GrQueprngZfoCfDi3GB8MC/H+7HyYdE7oyXJET6YY
        XYfnoEVjj/uxM2CQC0G9QgbgpfkIfXUK0dkcTlSTw1m35ylFyFWIYFAIkeMnhN5vMrJlk3Bq7cTOtDWC
        Ou0qwfloNysV9Y4cYeT2RxcTqCZRMbKIynpUNqMzi7G2BVR8QojFLxErpYC5aZnAAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="btnRegisterCustomerFast.ImageOptions.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAACp0RVh0VGl0
        bGUAQ29udGFjdDtDYXJkO0luZm87RGV0YWlsO1BlcnNvbjtFZGl04bGTWAAAAtlJREFUOE+Nk1lIVFEA
        ho8zruPWhhuaCEOUtuiDPVQQiqQlWJIvBuJajU1oqWOSRZpS2KiVWpKKS5baJI6mKZYb2l4oyNhEVlQ+
        iKIzOrmM4/J3zrFQi6AL38Occ77v3nPPHbLqMvoHgj9gYwQAh11G5Up1R0X9B/wPZXUDndQRrAmwiYXF
        pb9ZWGH+F2V175klXB0QlCnVfNHEDwPKFe04l1EKWXoJSqufQqOb4+NaimF+EXdqVMwybrvkTZqSdvGA
        sOiBitYXMTahR1JaMeJTCznJ6cUY1cxAqzNgXKfH9MwcmrsGWcCCYvQw1n05kF/Zx+saurCmvgenU25x
        qpTd0E7OYXxSj+GRSQy05oHeGRWx7jm/IyxgLC95C72BBuhi9eAwYmX5nF7VN/pUsxga1qCvMRefGkKw
        uPQFqqrDyA5yyaWuGQuYZBQ8x6x+AaPjs2hu7+WyJCkPypbXGPw6gncNcnysC+ay/nMculO3Q+7nwLZi
        wwPns7swNTuP9OwqHD97AzGU6PhcREiz0HBbBrUiiMtTKgk6ZFvRId8P6U7bQuqKWMA0IfMJdNPzVLrO
        xai4HIRJrkBx8wwG7gdyWfsmEm3xYrRm7EX0NutS6m2gsA+LmJ26+BgTUwZ6XHPQUEbHtHihyER/mT+X
        RzpD0Sp1Q2PqboSJLcup48jkcCf+CohJRGJtW1RyPSIp4Qm1KChUoEcewOWhpmC0SFxRl+iFY24iJjtR
        zEPtTUmY43KAHYUJhR0Lw85j38mKR0VXMT30Cs0nXFEj9UCIi3klnWN3NqUIjm40ITGbzVf+FIxn1/bQ
        OWLn6Zf4vf9lK+5mpeCyjxhHHM3u0XEHilBd7EvUxT4kaJ2QxIst1wba0rxZwGG9sy9s7LyHLWy3VNtb
        iYLp2CYmB9oICeOQtYAcpFzYYb020CTzZAG2MbZPZwp70+y3UYCVgPhbCsgBCovEiUUkw8uW/ASLygoB
        zKvoygAAAABJRU5ErkJggg==
</value>
  </data>
  <metadata name="styleController.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>251, 6</value>
  </metadata>
  <data name="btnCustomerSearch.ImageOptions.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAACZ0RVh0VGl0
        bGUARmluZDtCYXJzO1JpYmJvbjtTdGFuZGFyZDtTZWFyY2i7ZZwIAAAC7klEQVQ4T6WTe0iTURjGP+1i
        miD0RzdJCkVRA7EbzUuSRowuaGgNW27aalhupTOzpXYxL5kVol3I0UKtxJmbbLWZVJRlYJdptnIrU0uz
        tVIso6kVT+9nGQX+UfTCj+98z3Pe853zfu9hAPwXv4cDMZGYTEwiHImxYL0x/gz5sSvsw3FT0hFPWb7a
        kFagHdmRo7oaJcjwIX1iWn61x76iel1mcf2w/LheJ8ks9yDdQZZfy+YxjOzw6MBJkq26e+fBC5jbe6Gs
        aoDkoGqYdNfM4rqhpkddeNb5FqfPX0NyrrqB9AmS/dVs3mg4xiTkLj9UUgfru0E0NFlQUFIFsfwie0Dv
        3YWX0f1mAJYOG85VXock6yLiUk5yyPt1HCf+zlPnawwt6Ozuh+HWE0h2lyAsOhuLV6YiamsxLF3vYe54
        hwu1d5C4RwF+suIs5bH1ok8ERMyJT6sYMj23wtjWC5XhIQRJhQhak4UNwjysFhbCaH4LU7sNl642Q5px
        Bjypwh4QHDOd0h2YtcLC8grNPfR/tOPL12941mXD3hwlFnPTsWSZGCtic9FssaJvwI7hka8wmjrBEx/B
        0tWppbTAJCZSdGKox/qBtteKaIkKlXoT6m8+REB4Cub6rkHougPoJr+s5jEiE9Uo05qg0Tdi4QqpnRZw
        Zrj8oi+fPg+BL1NDrHkDwX4tFXOAdrCLLSJ3fVIpBsnfkKzFFo0Vsfv0VCsb/EJEI+RPZcJicjoetXVD
        qTaCJ9dAqWvB7aY2+AYldNEE91WCo+bWtldQ1LQgOl2HUm0rGu8/hecCnoV8F2Z+iFCafKAC1xtNePm6
        D4YbzRClFGG2d3g6TXDjcFNEsoNluHHXhB5rP+roeHHb8zDLOyKefLZjGTevwBiZf2ii2S90G7wWbbTM
        9ApLI30aMYGY4s8RbPYP3tLsw0nAvMD1LbN8IkSkOxOjvcD2vAvB/hZ3YgYxlRi7C+wk9n6wmhvh+vP9
        RyONd8P+hXHFvwfMd61HCl7ECOjpAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="editorButtonImageOptions3.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAGLSURBVDhPfZPXbsJAFET56XwJmN4MUr4iEv2ZanoR
        RRG996KB2bCWlXVi6TzZZ+7uXNkBQBD/eRCLxUx0XRdEo1FBJBIRhMPhuPSsARiNRoLH44H7/Y7b7Sa4
        Xq+4XC7o9XrodrsIhUIv5VcAJw6HQ3x89v+l0+n8HTAYDMRH2te3LXzXbrcRDAbVAN6137efaqXZbCIQ
        CKgBLIkBvOv5fMbpdBIcj0ccDgfs93vsdjs0Gg34/X77AJZEOZfLmWImkxHidrtFOp1GvV6Hz+dTA7ge
        Nkw5m82aMpEyqdVq8Hq9asBrt6Jh63Epks1mg/V6jdVqhWq1Co/HowZwNWyYspxKkVMpLpdLpFIpVCoV
        +wCuptVqmceWMiUpJ5NJlMtluN1uNYCrYcPW41JcLBaC+XyO2WyGUqkETdPsA9gwZTmVIqdSJIlEAsVi
        ES6XSw3gbtkwZSJlMp1OhUwKhYJ9AHfLhg3DMI9LKE8mE4zHYyHn83k4nU414LXbONslLInwroQTCcU3
        798ZjifgKz0KQADcQwAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="btnSetDiscountPercent.ImageOptions.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAAYdEVYdFRpdGxlAFBpdm90R3JpZDtGb3JtdWxhO24l
        MusAAAJ4SURBVDhPlZLZT1NREIcrQXZQE0QkJgYI4P+kiRgVqSyGSCpGICBxiUSBqLhEIouiPoCy+iCy
        tEEuGFoRXnwwLjECtgVK6e1ti/78zWmpS+KDTb7MyT0z38yZ1MTfNhJLtpO4/0BqpNYU29U/N9E9vIDu
        oQU8/I0Hg/OEUZ3JwDy6+K2Lsf2pfYK10tQUJxfB0HfyIxL/JCAxuIkAMSLcfzYH1saLIL69761K9Ac2
        FbqxCV8gBJ8RZsMfJBJD8PJsMOdezxsRJIggoa13jgIWsXDmVB6my3PDlOVCK2UkWkkOyca6Lwid8ttP
        7CJIFEFi66NZjhXuoJXmwFF1AI4zBXBYCmC35MNRmQ+7cDoPa96AymvunBFBkgiSGts0jh5S9o/DN6LJ
        i7bu6LO2WFkPwKsHcfHO5C/B5buv1FgeX/jyk0gqcjBbkY0PIx0sMogfbo+hkEYNrTYRJIsg+cKtSbWo
        VY4nvO9rgVa8D6/L9+PzaCdcLHKt+eFU6PAwp+66VQQpIkipv2lT25VO73qaYC3cg6miLHwZ6+CbDVXk
        XNXxbdVPdNWkpnlcBKlKUNtihZdjudYMWA9nYPJoJqbNWdBO7IWtMAPjh9IxJhxMx/KKD25Kz10biwpS
        q5vGOVZQdZBkrShTMVWUganj5NhuRsK46KLA40dV40sRpIkg7ezVUY5lKPsGlyjIMhWcbF2WG4lfnRuc
        VIflihLsUAJL4wjcfL9654q8Ncwyz2F8WHLrWFLRp/IqL72ITpBoru6dKG8YRtn55yirH0Jp3RaDKKkd
        xElirhlAMTHX9KO4uh9HKh9bWav+BzGRg4yz8y92/QO5Y40p5ifB3YESMkP4aAAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="editorButtonImageOptions1.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGOfPtRkwAAACBjSFJNAACH
        DwAAjA8AAP1SAACBQAAAfXkAAOmLAAA85QAAGcxzPIV3AAAKOWlDQ1BQaG90b3Nob3AgSUNDIHByb2Zp
        bGUAAEjHnZZ3VFTXFofPvXd6oc0wAlKG3rvAANJ7k15FYZgZYCgDDjM0sSGiAhFFRJoiSFDEgNFQJFZE
        sRAUVLAHJAgoMRhFVCxvRtaLrqy89/Ly++Osb+2z97n77L3PWhcAkqcvl5cGSwGQyhPwgzyc6RGRUXTs
        AIABHmCAKQBMVka6X7B7CBDJy82FniFyAl8EAfB6WLwCcNPQM4BOB/+fpFnpfIHomAARm7M5GSwRF4g4
        JUuQLrbPipgalyxmGCVmvihBEcuJOWGRDT77LLKjmNmpPLaIxTmns1PZYu4V8bZMIUfEiK+ICzO5nCwR
        3xKxRoowlSviN+LYVA4zAwAUSWwXcFiJIjYRMYkfEuQi4uUA4EgJX3HcVyzgZAvEl3JJS8/hcxMSBXQd
        li7d1NqaQffkZKVwBALDACYrmcln013SUtOZvBwAFu/8WTLi2tJFRbY0tba0NDQzMv2qUP91829K3NtF
        ehn4uWcQrf+L7a/80hoAYMyJarPziy2uCoDOLQDI3fti0zgAgKSobx3Xv7oPTTwviQJBuo2xcVZWlhGX
        wzISF/QP/U+Hv6GvvmckPu6P8tBdOfFMYYqALq4bKy0lTcinZ6QzWRy64Z+H+B8H/nUeBkGceA6fwxNF
        hImmjMtLELWbx+YKuGk8Opf3n5r4D8P+pMW5FonS+BFQY4yA1HUqQH7tBygKESDR+8Vd/6NvvvgwIH55
        4SqTi3P/7zf9Z8Gl4iWDm/A5ziUohM4S8jMX98TPEqABAUgCKpAHykAd6ABDYAasgC1wBG7AG/iDEBAJ
        VgMWSASpgA+yQB7YBApBMdgJ9oBqUAcaQTNoBcdBJzgFzoNL4Bq4AW6D+2AUTIBnYBa8BgsQBGEhMkSB
        5CEVSBPSh8wgBmQPuUG+UBAUCcVCCRAPEkJ50GaoGCqDqqF6qBn6HjoJnYeuQIPQXWgMmoZ+h97BCEyC
        qbASrAUbwwzYCfaBQ+BVcAK8Bs6FC+AdcCXcAB+FO+Dz8DX4NjwKP4PnEIAQERqiihgiDMQF8UeikHiE
        j6xHipAKpAFpRbqRPuQmMorMIG9RGBQFRUcZomxRnqhQFAu1BrUeVYKqRh1GdaB6UTdRY6hZ1Ec0Ga2I
        1kfboL3QEegEdBa6EF2BbkK3oy+ib6Mn0K8xGAwNo42xwnhiIjFJmLWYEsw+TBvmHGYQM46Zw2Kx8lh9
        rB3WH8vECrCF2CrsUexZ7BB2AvsGR8Sp4Mxw7rgoHA+Xj6vAHcGdwQ3hJnELeCm8Jt4G749n43PwpfhG
        fDf+On4Cv0CQJmgT7AghhCTCJkIloZVwkfCA8JJIJKoRrYmBRC5xI7GSeIx4mThGfEuSIemRXEjRJCFp
        B+kQ6RzpLuklmUzWIjuSo8gC8g5yM/kC+RH5jQRFwkjCS4ItsUGiRqJDYkjiuSReUlPSSXK1ZK5kheQJ
        yeuSM1J4KS0pFymm1HqpGqmTUiNSc9IUaVNpf+lU6RLpI9JXpKdksDJaMm4ybJkCmYMyF2TGKQhFneJC
        YVE2UxopFykTVAxVm+pFTaIWU7+jDlBnZWVkl8mGyWbL1sielh2lITQtmhcthVZKO04bpr1borTEaQln
        yfYlrUuGlszLLZVzlOPIFcm1yd2WeydPl3eTT5bfJd8p/1ABpaCnEKiQpbBf4aLCzFLqUtulrKVFS48v
        vacIK+opBimuVTyo2K84p6Ss5KGUrlSldEFpRpmm7KicpFyufEZ5WoWiYq/CVSlXOavylC5Ld6Kn0Cvp
        vfRZVUVVT1Whar3qgOqCmrZaqFq+WpvaQ3WCOkM9Xr1cvUd9VkNFw08jT6NF454mXpOhmai5V7NPc15L
        Wytca6tWp9aUtpy2l3audov2Ax2yjoPOGp0GnVu6GF2GbrLuPt0berCehV6iXo3edX1Y31Kfq79Pf9AA
        bWBtwDNoMBgxJBk6GWYathiOGdGMfI3yjTqNnhtrGEcZ7zLuM/5oYmGSYtJoct9UxtTbNN+02/R3Mz0z
        llmN2S1zsrm7+QbzLvMXy/SXcZbtX3bHgmLhZ7HVosfig6WVJd+y1XLaSsMq1qrWaoRBZQQwShiXrdHW
        ztYbrE9Zv7WxtBHYHLf5zdbQNtn2iO3Ucu3lnOWNy8ft1OyYdvV2o/Z0+1j7A/ajDqoOTIcGh8eO6o5s
        xybHSSddpySno07PnU2c+c7tzvMuNi7rXM65Iq4erkWuA24ybqFu1W6P3NXcE9xb3Gc9LDzWepzzRHv6
        eO7yHPFS8mJ5NXvNelt5r/Pu9SH5BPtU+zz21fPl+3b7wX7efrv9HqzQXMFb0ekP/L38d/s/DNAOWBPw
        YyAmMCCwJvBJkGlQXlBfMCU4JvhI8OsQ55DSkPuhOqHC0J4wybDosOaw+XDX8LLw0QjjiHUR1yIVIrmR
        XVHYqLCopqi5lW4r96yciLaILoweXqW9KnvVldUKq1NWn46RjGHGnIhFx4bHHol9z/RnNjDn4rziauNm
        WS6svaxnbEd2OXuaY8cp40zG28WXxU8l2CXsTphOdEisSJzhunCruS+SPJPqkuaT/ZMPJX9KCU9pS8Wl
        xqae5Mnwknm9acpp2WmD6frphemja2zW7Fkzy/fhN2VAGasyugRU0c9Uv1BHuEU4lmmfWZP5Jiss60S2
        dDYvuz9HL2d7zmSue+63a1FrWWt78lTzNuWNrXNaV78eWh+3vmeD+oaCDRMbPTYe3kTYlLzpp3yT/LL8
        V5vDN3cXKBVsLBjf4rGlpVCikF84stV2a9021DbutoHt5turtn8sYhddLTYprih+X8IqufqN6TeV33za
        Eb9joNSydP9OzE7ezuFdDrsOl0mX5ZaN7/bb3VFOLy8qf7UnZs+VimUVdXsJe4V7Ryt9K7uqNKp2Vr2v
        Tqy+XeNc01arWLu9dn4fe9/Qfsf9rXVKdcV17w5wD9yp96jvaNBqqDiIOZh58EljWGPft4xvm5sUmoqb
        PhziHRo9HHS4t9mqufmI4pHSFrhF2DJ9NProje9cv+tqNWytb6O1FR8Dx4THnn4f+/3wcZ/jPScYJ1p/
        0Pyhtp3SXtQBdeR0zHYmdo52RXYNnvQ+2dNt293+o9GPh06pnqo5LXu69AzhTMGZT2dzz86dSz83cz7h
        /HhPTM/9CxEXbvUG9g5c9Ll4+ZL7pQt9Tn1nL9tdPnXF5srJq4yrndcsr3X0W/S3/2TxU/uA5UDHdavr
        XTesb3QPLh88M+QwdP6m681Lt7xuXbu94vbgcOjwnZHokdE77DtTd1PuvriXeW/h/sYH6AdFD6UeVjxS
        fNTws+7PbaOWo6fHXMf6Hwc/vj/OGn/2S8Yv7ycKnpCfVEyqTDZPmU2dmnafvvF05dOJZ+nPFmYKf5X+
        tfa5zvMffnP8rX82YnbiBf/Fp99LXsq/PPRq2aueuYC5R69TXy/MF72Rf3P4LeNt37vwd5MLWe+x7ys/
        6H7o/ujz8cGn1E+f/gUDmPP8usTo0wAAAAlwSFlzAAALDAAACwwBP0AiyAAAARlJREFUOE+djF1HRFEU
        huc/nf/SxZDEOGZUYro4JMPMxVwVFUlJKUOpiC4imr6/1M0QqXFyTI2j48xHKd2+WYu9zdrti85sHvtd
        7177SQFIqeO6Loj/Zj4kUGU/aMHcZVuXxcN3nfN7rzpnKi86Dyw/8S0E06cxytWIBVMHIbz9JgvGdhrI
        bQUsGFr3kV6t/xWUd+9R2r5DYfMWk5UbeBvXmFi7wvjKBUaWzpFdPENm4QTD88cYnK0iPXMkBaN5LzFC
        EHV+EiMEYfzNOI4jMHs1E0LwFn0xtKSyOZtvQhCEn1boky0TQuA3PzS0qDD73lkInhtdK/TJlgkheAza
        DC2pbM7mmxA8+C2Glnqx9aoTglo9TowQ9IeLX6XfvWSnnOg7AAAAAElFTkSuQmCC
</value>
  </data>
  <metadata name="imageList1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>141, 6</value>
  </metadata>
  <data name="imageList1.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        ********************************************************************************
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAABy
        IAAAAk1TRnQBSQFMAgEBFQEAAdABBQHQAQUBEAEAARABAAT/AQkBAAj/AUIBTQE2AQQGAAE2AQQCAAEo
        AwABQAMAAWADAAEBAQABCAYAARgYAAGAAgABgAMAAoABAAGAAwABgAEAAYABAAKAAgADwAEAAcAB3AHA
        AQAB8AHKAaYBAAEzBQABMwEAATMBAAEzAQACMwIAAxYBAAMcAQADIgEAAykBAANVAQADTQEAA0IBAAM5
        AQABgAF8Af8BAAJQAf8BAAGTAQAB1gEAAf8B7AHMAQABxgHWAe8BAAHWAucBAAGQAakBrQIAAf8BMwMA
        AWYDAAGZAwABzAIAATMDAAIzAgABMwFmAgABMwGZAgABMwHMAgABMwH/AgABZgMAAWYBMwIAAmYCAAFm
        AZkCAAFmAcwCAAFmAf8CAAGZAwABmQEzAgABmQFmAgACmQIAAZkBzAIAAZkB/wIAAcwDAAHMATMCAAHM
        AWYCAAHMAZkCAALMAgABzAH/AgAB/wFmAgAB/wGZAgAB/wHMAQABMwH/AgAB/wEAATMBAAEzAQABZgEA
        ATMBAAGZAQABMwEAAcwBAAEzAQAB/wEAAf8BMwIAAzMBAAIzAWYBAAIzAZkBAAIzAcwBAAIzAf8BAAEz
        AWYCAAEzAWYBMwEAATMCZgEAATMBZgGZAQABMwFmAcwBAAEzAWYB/wEAATMBmQIAATMBmQEzAQABMwGZ
        AWYBAAEzApkBAAEzAZkBzAEAATMBmQH/AQABMwHMAgABMwHMATMBAAEzAcwBZgEAATMBzAGZAQABMwLM
        AQABMwHMAf8BAAEzAf8BMwEAATMB/wFmAQABMwH/AZkBAAEzAf8BzAEAATMC/wEAAWYDAAFmAQABMwEA
        AWYBAAFmAQABZgEAAZkBAAFmAQABzAEAAWYBAAH/AQABZgEzAgABZgIzAQABZgEzAWYBAAFmATMBmQEA
        AWYBMwHMAQABZgEzAf8BAAJmAgACZgEzAQADZgEAAmYBmQEAAmYBzAEAAWYBmQIAAWYBmQEzAQABZgGZ
        AWYBAAFmApkBAAFmAZkBzAEAAWYBmQH/AQABZgHMAgABZgHMATMBAAFmAcwBmQEAAWYCzAEAAWYBzAH/
        AQABZgH/AgABZgH/ATMBAAFmAf8BmQEAAWYB/wHMAQABzAEAAf8BAAH/AQABzAEAApkCAAGZATMBmQEA
        AZkBAAGZAQABmQEAAcwBAAGZAwABmQIzAQABmQEAAWYBAAGZATMBzAEAAZkBAAH/AQABmQFmAgABmQFm
        ATMBAAGZATMBZgEAAZkBZgGZAQABmQFmAcwBAAGZATMB/wEAApkBMwEAApkBZgEAA5kBAAKZAcwBAAKZ
        Af8BAAGZAcwCAAGZAcwBMwEAAWYBzAFmAQABmQHMAZkBAAGZAswBAAGZAcwB/wEAAZkB/wIAAZkB/wEz
        AQABmQHMAWYBAAGZAf8BmQEAAZkB/wHMAQABmQL/AQABzAMAAZkBAAEzAQABzAEAAWYBAAHMAQABmQEA
        AcwBAAHMAQABmQEzAgABzAIzAQABzAEzAWYBAAHMATMBmQEAAcwBMwHMAQABzAEzAf8BAAHMAWYCAAHM
        AWYBMwEAAZkCZgEAAcwBZgGZAQABzAFmAcwBAAGZAWYB/wEAAcwBmQIAAcwBmQEzAQABzAGZAWYBAAHM
        ApkBAAHMAZkBzAEAAcwBmQH/AQACzAIAAswBMwEAAswBZgEAAswBmQEAA8wBAALMAf8BAAHMAf8CAAHM
        Af8BMwEAAZkB/wFmAQABzAH/AZkBAAHMAf8BzAEAAcwC/wEAAcwBAAEzAQAB/wEAAWYBAAH/AQABmQEA
        AcwBMwIAAf8CMwEAAf8BMwFmAQAB/wEzAZkBAAH/ATMBzAEAAf8BMwH/AQAB/wFmAgAB/wFmATMBAAHM
        AmYBAAH/AWYBmQEAAf8BZgHMAQABzAFmAf8BAAH/AZkCAAH/AZkBMwEAAf8BmQFmAQAB/wKZAQAB/wGZ
        AcwBAAH/AZkB/wEAAf8BzAIAAf8BzAEzAQAB/wHMAWYBAAH/AcwBmQEAAf8CzAEAAf8BzAH/AQAC/wEz
        AQABzAH/AWYBAAL/AZkBAAL/AcwBAAJmAf8BAAFmAf8BZgEAAWYC/wEAAf8CZgEAAf8BZgH/AQAC/wFm
        AQABIQEAAaUBAANfAQADdwEAA4YBAAOWAQADywEAA7IBAAPXAQAD3QEAA+MBAAPqAQAD8QEAA/gBAAHw
        AfsB/wEAAaQCoAEAA4ADAAH/AgAB/wMAAv8BAAH/AwAB/wEAAf8BAAL/AgAD//8ARwAB8wGYAXIBmAHz
        OgAB8wFyAZgBVAFVAXIB8zkAApgB8AHxAU4BVQGYOQABlwH/AU4BmAHxAU4BlzkAAZgBlwJVAZgB/wGY
        OQAB8wWXAfM6AAHzAZgBlwGYAfP/AEcAAf8BGwGaAXQBUQFzARwB8AH/DAABCAFWAZcB9AkAAQcKbQEH
        EgABeQN6AVICKgFLAVEBmQF0AUsBcwEcAfAB/wQAAZgBVQEIAZgBlwkAAesBBwjvAQcB6xIAAXkBegTD
        AZkBUgFLAlIBKgEjASoBSwFRAwABmAFVAp4BCAGXAQgIAAHrAQcB7QHrAe8B7QHrAe8B7QHrAQcB6xIA
        AXQCmgF6AXQBUQFzAksBeQN0AXkBUgFLAgABmAFVA5gBngGfAVYB9AcAAesBvAjvAbwB6wgAAfQD8wH0
        BQABmQF0A3oBUgIqAksBTAFKAksBdAFLAQABmAFVApgBeAEIAZ4BnwGYAZcHAAHsAbwB7QHrAQcB7QHr
        AQcB7QHrAbwB7AcAAfQBuwOzAbsB9AQAAf8BeQF6AnQBcwFLAXQBUgFLAVIBKgEjASoCSwGYAVUBmAJ4
        AZgBeAEIAZ4BCAGXAQgGAAHsAbwIBwG8AewHAAG7AbMD2wGzAbsEAAF5A3oBUgIqA0sDdAGZAVIBSwEt
        AZgBlwF4AZgBVQEtAZgCngGfAVUB9AUAAewB8AHtAesBBwHtAesBBwHtAesB8AHsBwABswXbAbMEAAF5
        AXoEwwGZAVIBSwFSAUwCSwFzAXQBSwEtAXgCmAFPAZgB9AFPAZ4BmAGeAZgBlwH/BAAB7AHwCAcB8AHs
        BwABswHbA7MB2wGzBAABdAegAUsCUgMqAksBmAFPAVUBLQGYAgABmAGXAZgBeAGeAZcBCAQAAewB8QHt
        AesBvAHtAesBvAJvAfEB7AcAAbMB3AOzAdwBswQAAZkBeQN6ApoBeQFSAXkCdAHDAZkBUgFLAQAB9AHw
        AfQDAAH/AU8EmAFVAfQDAAHsAfEI8AHxAewHAAG7AbMD3AGzAbsFAAH0BRoBUgFZAlIBUQJzAXQBSwgA
        AfABTwGYAZcBmAF4AZcB/wIAAe0B8QjwAfEB7QgAAbsBswGyAbMBuwwAAVIBUwJ6AVICKgJLCQAClwGY
        AZcBmAGXAQgCAAHtAfIIswHyAe0ZAAF5AXoEwwGZAVIBSwkAAfQBLQGYApcBmAFPAgABkgHyCLQB8gGS
        GQABdAegAUsKAAEIAVUBmAGXAZgBTwIAAZIB8wi0AfMBkhkAAZkBeQN6ApoBeQGZCgAB/wFPAVYBlwFW
        AZcCAAGSAf8I9AH/AZIaAAH0BRoB9AwAAf8BmAGXAZgB/wIAAfAK9wHwJAAB7wvsAe8EAAH/AfQB8wTy
        Av8EAAH0C/MB9AcAAf8BGgFvAkwBbwEaAf8GAAHsC/8B7AUAAf8FUAT/AgAB9wvrAfcGAAHzAW8BTAEW
        ApQBFgFGAUwBGwUAAewB/wnyAf8B7AYAAVADVwFQBgAB6wv/AewFAAHzAm8BlAEWAeMBbwEWAZQBbwFM
        ARsEAAHsAf8B8gPsAQABKwEaAvIB/wHsBgABUAM2AVAGAAHrBf8C8QP/AXgBLgHzAwAB/wJvAZQGFwGU
        Am8B/wMAAewB/wTyASsBAAFKASsBGgH/AewB/wUAAVABVwI2AVAGAAHsA/8BEAHvAW0B6gHrAv8BKAFX
        AS4B8wIAARoBTAGUAuMGFwGUAUwBGgMAAewB/wHyBOwBKwFTASwBKwGaAewB/wUAAVADVwFQBAAB/wEA
        AewD/wERA/8BKAEuASgBNQE2AXkBLgHzAQABkwEWAXUB4wcXAhYBbwMAAewB/wXyASsBOAFTASwBKwFL
        AQAGUANXBlABAAHsAf8B9AH/AREB8wFtARIBLgF+BDYBeQEuAQABbwGUAhYG/wEXAeMBlAFMAwAB7AH/
        AfIF7AErATgBUwEsASsBGg9XAQAB7AH/AfQB8gFDAf8BEgH/AS4DeQI2AZkBLgEAAW8BmgF1ARYG/wEX
        AeMBlAFMAwAB7AH/B/IBKwE4AVMBLAErD1cBAAHsAf8B9AEUAREB/wEVAW0BKAIuAS8BNgGZAS4CAAGT
        ApQBdQYXAeMCFgFvAwAB7AH/AfIH7AErATgBUwErAVcB/wOfAf8DVwH/A58B/wFXAQAB7QH/AfMB9AEH
        A/QD/wEuAZkBLgMAARoBFgG9AZQCdQQWAeMBlAFGARoDAAHsAf8C8wTyAfECBwIrARsGNgNXBjYBAAGS
        Af8B8wj0AXgBLgQAAf8BFgGUAb0BlAF1ARYDdQGaAm8B/wMAAewB/wHzBOwB8gHvAZIB7QLsBgABNgJX
        AVgBNgYAASUB4wMgAyUCIAEmAeMBJgUAAfMBFgGUAb0BmgOUAb0BFgFvAfMEAAHsAf8G8wGSAfMC8gHs
        BgABNgFYAVcBWAE2BgABJgGaAZQBuwGLA5oBuwGLApoBJgYAAfMBFgGUA70BmgJvAfMFAAHsAf8F8wEH
        Ae8B8gH/AewB8wYAATYBeQFXAX4BNgYAAW8CJgHvAZEDJgHvAZECJgFvBwAB/wEaAZMCbwGTARoB/wYA
        AewH/wH0Af8B7AHzBwABNgH/AZ8B/wE2CQAB7wH3AwAB7wH3GAAB8wnsAfMIAAU2EwAB8QHrAfECAAEb
        AUsBIwHwAfEB9AwAAf8BBwFFAgEBRQGTAfQKAAH2AlEBmQH2EQAB8QHtAhIBAAH/AUsBTAEjAfIC8wH0
        Af8JAAGTAR8BJAQlASQBHwGTCAAB/wFRAnkBWAEwAZkB/w4AAfEB7QESAW0B9AEAAXQCTAEjBPQC/wcA
        AZMB6QglAekBkwcAAXkBWAF5AlgBeQFYAVEB9AYAAfQB8gHwAfQB/wEAAfMB7QESAW0B9AEAAUwBSwJM
        ASMB8wL0Av8BTAYAAZMBJAolASQBkwUAAfMBUQF6BDcCeQFRAfQDAAH0AfcB7AH3Au8B9wHwAfcB7AFt
        AfQCAAFMAUsCTAEjAfMC9AL/AS4BAgQAAf8B6QJGAW8B/wFvAkYBbwH/AW8CRgEkAfQEAAFSAXkBWQFY
        BDcBWAF5ATAB/wEAAfIB6wEHAfQC/wH0Ad0B7wHsAQcB9AMAAW4BSwFMAUsBRAHzAvQB/wGYAS0BAgQA
        AQcDRgP/Am8D/wNGAZMDAAH/AVEBoARZAzcBeQFYAZkB/wHsAbwF/wH0ARkB9wHwBAABTAFLAUwBbgHq
        AfMC9AEIAS0BlwUCAW8ERgb/BUYDAAEbAVgBoAPlAlkBWAE3AVgBeQFRAQcB7Qb/AfQBGQEJAe0EAANM
        AXQBIwHzAfQBCAEtAZcBVQSXAQIBRQRMAUYE/wFGBEwB6QIAAf8BTAFFAaAB5QGgAuUBWQJ6AVgBUgFR
        AZIBvAf/ARkB3QHsAfMDAANMAXQBIwHzAfQBLQF4BFUClwECAekETAF0BP8BdARMAekBAAEbAVEBUgJG
        AXoD5QGgAVgBMAGZAfMB/wHrAfAF/wH0AxkBkQHzAwACTAFSAXQBIwHzAfQBCAEtAZgDlwF4AZcBAgJv
        AkwBdQb/AXUCTAJvAQABGwEwAVgBegFMASQBeQF6AVgBUQGZAf8DAAHtAfAB9AL/BPQBGQHdAZEB8wMA
        AkwBUgF0ASMB8wL0AQgBLQGYBS0BBwFvAkwD/wJGA/8CTAFvAQcCAAG8ATABWAF6AUwBSwFSARoB/wUA
        AgcH9AEZAbUB6wH/AwABbgFMAVIBmgEcAfMC9AH/AZgCLQQAAf8BJQF1AUwBRgH/AUYCTAFGAf8BRgFM
        AXUBRgL/AZkBKgErATACUgH0CAAB8wH3AfMG9AEJAZEB9wQAAkwBegGZAfIB8wL0Av8BLgEtBQABBwFG
        AXUBTQHpBE0B6QFNAXUBRgGZAQABGgEqAisBSwF0ATAB9AkAAQcB7wHxBPQB8QGRAewB9AQAAUwBUgF0
        AvIB8wL0Av8BTAcAAZMBRgGUAnUCTQJ1AZQBRgGTAgABSwIrASoBSwH/ARoB/wkAAf8BBwH3AQcCvAEH
        Ae0B7AH0BQALTAgAAQcBJQFvAXQClAF0AW8BRgGZAwABSwMqAfMOAAH0AbwBBwLvAfAB/xoAAf8BGgGT
        AkYBkwEaAf8EAAH0AksB8wwABFIMAAL/AvQB8QHvAfEC9AP/AbUB8QIAAv8C9AHxAe8B8QL0Av8BmAFy
        AfcCAAH/CPMB/wYAAVIBGgLDAlIKAAP/AfMBBwEbAQcB8wT/ArQB8gEAA/8B8wEHARsBBwHzA/8BcgFV
        AXICAAG8CIoBvAEAAf8EAAFSAXoCWQH2AVIFAAH/BgAB9AEHARsBGgEbAQcBuwS0AdoBtAHyAgAB9AEH
        ARsBGgEbAQcB9AGYAnIBVQJyAZgBrQi0AawF8wH/AVIBmgJZAcMDUgcAAf8BAAH0AQcBGwMaARsBtAHc
        BdsBtAEAAfQBBwEbAxoBGwEHAXEFdwGXAa0BCQa0AQkBrQJQAkoBUAG8AQABUgGaAlkBwwH2AVICAAH/
        BQAB9AG8ARsFGgW0AdwBtAHyAfQBvAEbBRoB9AFyAXcBlwHkApcBmAGzAd0C2wH2AfQC2wHdAa0CUQNz
        AVACAAFSAZoCWQHDA1IBMQFSAZkB9gIAAbwBGwYaARsB9AG8AfQCugHzAQABvAEbBhoBGwH0AbwBdwHk
        AZcCAAGzAhkC/wH0AxkBswJRAlABeAFQAwABUgGaB1kBWAFSAfMBAAH0AfABGwQaAxsB9AHwAQkB8wIA
        AfQB8AEbBBoDGwH0ApcBmAIAAf8BtAHcAZkBdAFLAewB3AGzAVAB/wH0AVEBUAGZAVAEAAFSAZoIWQFS
        AfYBAAH0AfABGwIaARsBvAEHARoB9gHwBQAB9AHwARsCGgEbAbwBBwEaAfYB8AYAAf8BHAF0AXMBbQGY
        AZkBGwL/AQgCmQFQBQABUgF6B1kBWAGZAgAB9AHwAfQCGwEcAQABbgEbAfAGAAH0AfAB9AIbARwBAAFu
        ARsB8AYAAfMCeQJ0AXMBeAGZAXQBSwFzAXgBcwH/BQABUgF6CFkBUgMAAfQB8AH0ARsBBwHsAe8BHAMH
        AfMEAAH0AfAB9AEbAQcB7AHvARwDBwHzAwABRAGgAZoCeQFLAf8CdAFzAVEB9AcAATEBmgNZAVIBMQFS
        AlkBMQQAAfQB8AH0BPYB8AIAAbwFAAH0AfAB9AT2AfACAAG8AwABIwF0AXkBmgGZASMB9AF5AZkBeQF0
        AfMGAAH/AVIBmgNZATEBwwExAVkBegFSBQAB9AXwAfMBAAH0AbwGAAH0BfAB8wEAAfQBvAMAASMBSwFS
        AW4BRAEjAUsBoAGaAnkBRAcAAZkBeQF6AlkBUgExAVIBegF5AZkMAAH0AfAB/w0AAfQB8AH/AwABIwFL
        AVIBSwFEAUoBIwF0AXkBmgGZASMHAAH/AVIBGgGgA3oBoAEaAVIB/wsAAfQB8AH/DQAB9AHwAf8EAAH0
        AUQCSwFEAf8BIwFLAVIBbgFKASMIAAHzAVIBeQEaAfYBGgF5AVIB8wwAAfAB/wEAAfAMAAHwAf8BAAHw
        BgAB/wIAAUQBSwFSAUsBRAEjCQAB/wGZAVIBMQFSAZkB/w0AAfQC8AH0DAAB9ALwAfQJAAH/ASMCSwEj
        Af8CAAHzAW0BEQmuAZIB8wH0Af8B8wHvAREIbgJJAewB9AH/AfIN8AHyEQAB/wEVAW0JtAGuAQcB8AL/
        ARUBbQF0B1IBTwFVAU8B8AH/AQcNSwEHAQAB/wHzAvIB/wHzBPIB8wH/AvIB8wH/AQABEgFtCbQBiwHz
        AbwCAAESAW0BdAVSA08BVQJPAZgBSwFzC3QBcwFLAQAB8wL0AfMB7wG8BPMBvAHvAvIC8wEAAeoBbQK0
        AbUCvAG1A7QBiwH0AbwCAAHqAW0BdAFSAXQCvAGTAU8FdwGXAUsBcwtLAXMBSwEAAfIB/wX0BvMB8gH0
        AfIBAAJtAbQBtQHwBrQBiwH/AbwCAAJtAnQBGgNSA08B5AFVAU8BmAFLAXQLSwFzAUsBAAHyAf8B8Ae8
        BAcB9AHyAQACbQG0Ad0CtAHdAbsB3QG1AbQBiwG8AfMCAAJtAXQBGgFSAXQBGgGZARoBkwFPAeQBTwH0
        AQADSwEJB0sBCQNLAQAB8gL/BfQF8wHyAfQB8gEAAm0BtAHxAbQB8QG0AfEBtAHxAbQBiwHzAbwCAAJt
        AXQBGgFSARoBUgEaAVIBGgFQAU8BlwG8AQABSwF0AUsBGQFLBZkBSwEZAUsBdAFLAQAB8gH/AY4EbwGT
        AfQBvAEHA7wB9AHyAQABbQHrAbQBGQG0ARkBtAEZAbQBGQG0AYsB9AG8AgABbQHrAXQBGwF0ARsBdAEb
        AXQBGwF0AVIB9AG8AQABSwF0A0sFdANLAXQBSwEAAfIB/wH3AW8CFgGTAe8D9AHzAfQB8wH0AfIBAALr
        AbQBGQG0AbUCGQG0ARkBtAGtAf8BvAIAAusBdAEbAnQCGwF0ARsBdAFSAf8BvAEAAUsGdAF5BnQBSwEA
        AfIB/wEZAQkCHAEJARkB9AS8AfMB9AHyAQAB6wHsAbQBuwEZA7QCCQG0Ac8BvAH0AgAB6wHsAXQBmQEb
        A3QCGgJ0AbwB9AEAAUsBkwR0AXkBegF5BHQBkwFLAQAB8gH/ARkBuwEaAfEBCQHzAvQBmAFVAS0BVQGY
        AfIBAALsArQBCQQZA7QB8wG8AgAC7AJ0AZkDGwEaA3QB8wG8AQABSwKZAZoIGgKZAUsBAAHyAf8BGQHv
        ARoBGwG7AfMB/wGYAS0BLgH/ATQBLQGYAQAC7AG6B7QBugG0AfQBvAIAAuwBeQd0AXkBdAH0AbwBAAFz
        BHQCeQJ6AXkEdAFzAQAB8gH/AfMB3QIHARkB8wH/AVUBLQEuAf8BNQEuAVUBAALsAdwHugHcAbQB/wHw
        AgAC7AF6B3kBegF0Af8B8AEAAfABcwFLAq4B7ANLAewCrgFLAXMB8AEAAfIB/wXzAfQB/wEtBf8BLQEA
        AewBEgm0AbUB8AHyAgAB7AESCnQB8AHyBQABrgEJAwABCQGRBQAB8wH0B/8BTwJVAf8BVgFVAU8BAALv
        AgcCvAHwAvMB9AH/AfAEAALvAgcCvAHwAvMB9AH/AfAHAAEHBa4B7wUAAf8B8wfyAZgBVQGXAf8BlwFV
        AQgBAAG8Ae8JtQEJBAAB8gHvCXQBmR0AAQgDlwEIAQABQgFNAT4HAAE+AwABKAMAAUADAAFgAwABAQEA
        AQEGAAEDFgAD/wEAAv8GAAL/BgAC/wYAAv8GAAL/BgAB+AE/BgAB8AEfBgAB8AEfBgAB8AEfBgAB8AEf
        BgAB8AEfBgAB+AE/BgAC/wYAAv8GAAL/BgAC/wYAAv8BAAF/AfgBfwHAAQMC/wIAAfABfwHAAQMC/wIA
        AeABPwHAAQMC/wIAAcABHwHAAQMB/AEfAgABgAEfAcABAwH4AQ8DAAEPAcABAwH4AQ8DAAEHAcABAwH4
        AQ8DAAEDAcABAwH4AQ8CAAEGAQMBwAEDAfgBDwIAAY4BAQHAAQMB+AEPAYABAAH/AQABwAEDAfwBHwH+
        AQAB/wGAAcABAwL/Af4BAAH/AYABwAEDAv8B/gEAAf8CwAEDAv8B/gEAAf8CwAEDA/8BAQH/AeABwAED
        BP8BwAEBAeABDwEAAQcB8AEPAcABAQHwAQMBAAEHAeABBwHAAQEB+AE/AQABBwHAAQMBwAEBAfgBPwEA
        AQMBgAEBAcABAAH4AT8BAAEBAYABAQHAAQAB+AE9AgABgAEBAcABAQEAAQECAAGAAQEBwAIAAQECAAGA
        AQEBwAIAAQEBAAEBAYABAQHAAgABAQEAAQMBgAEBAcACAAEBAQABBwGAAQEBwAEBAfgBPwEAAQcBwAED
        AcABAQH4AT8BAAEHAeABBwHAAQEB+AE/AQABBwHwAQ8BwAEDAfgBPwHnAT8C/wHAAQcB+AE/Af8B+AHA
        Af8B8AEPAfwBHwH/AfABgAE/AeABBwH4AQcB/wHgAYABHwHAAQMB+AEDAfABQQEAAR8BgAEBAfABAQHA
        AQMBAAEPAgAB8AEAAYABBwEAAQ8CAAHgAgABDwQAAeACAAEPBAABwAIAAQcEAAGAAgABBwQAAYABBwEA
        AQcEAAHAAR8BAAEHAQABDwMAAf8BAAEPAQABDwGAAQEBAAH/AYABDwEAAR8BwAEDAQAB/wGAAR8BAAEf
        AeACBwH/AeABPwL/AfACDwH/AQ8B/wEAAQMBAAEDAQABPwEDAf8BAAEBAQABAwEAAS8BAwHvAcABAAHA
        BAAB/gGAAQABgAMAAYAB3wYAAcABAwEAAQEBAAEDAgAB4AEBAQABAwEAAQMCAAHwAQABgAEPAYABDwHA
        AQAB+AEAAcABjwHAAY8BwAEAAfgBAAHgAQEB4AEBAcABAwH4AQAB8AENAfABDQHAAQMB8AEAAfgBCQH4
        AQkBwAEDAfgBAAH/AfEB/wHxAcABAwH4AQAB/wHjAf8B4wHAAQMB/AEBAf8B5QH/AeUB+wEDAf4BAwH/
        AeEB/wHhAf8BAwUAAQEC/wUAAQECAAGAAQEBgAIAAQECAAGAAQEBgAIAAQECAAGAAQEBgAIAAQECAAGA
        AQEBgAEBAQABAQIAAYABAQGAAQEBAAEBAgABgAEBAYABAQEAAQECAAGAAQEBgAEBAQABAQIAAYABAQGA
        AQEBAAEBAgABgAEBAYABAQEAAQECAAGAAQEBgAEBAQABAQIAAYABAQGAAQEBAAEBAgABgAEBAYABAQHz
        AZ8CAAGAAQcBgAEHAfABHwIAAYABBwGAAQcD/wHBCw==
</value>
  </data>
  <data name="btnRegisterCustomer.ImageOptions.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAACp0RVh0VGl0
        bGUAQ29udGFjdDtDYXJkO0luZm87RGV0YWlsO1BlcnNvbjtFZGl04bGTWAAAAtlJREFUOE+Nk1lIVFEA
        ho8zruPWhhuaCEOUtuiDPVQQiqQlWJIvBuJajU1oqWOSRZpS2KiVWpKKS5baJI6mKZYb2l4oyNhEVlQ+
        iKIzOrmM4/J3zrFQi6AL38Occ77v3nPPHbLqMvoHgj9gYwQAh11G5Up1R0X9B/wPZXUDndQRrAmwiYXF
        pb9ZWGH+F2V175klXB0QlCnVfNHEDwPKFe04l1EKWXoJSqufQqOb4+NaimF+EXdqVMwybrvkTZqSdvGA
        sOiBitYXMTahR1JaMeJTCznJ6cUY1cxAqzNgXKfH9MwcmrsGWcCCYvQw1n05kF/Zx+saurCmvgenU25x
        qpTd0E7OYXxSj+GRSQy05oHeGRWx7jm/IyxgLC95C72BBuhi9eAwYmX5nF7VN/pUsxga1qCvMRefGkKw
        uPQFqqrDyA5yyaWuGQuYZBQ8x6x+AaPjs2hu7+WyJCkPypbXGPw6gncNcnysC+ay/nMculO3Q+7nwLZi
        wwPns7swNTuP9OwqHD97AzGU6PhcREiz0HBbBrUiiMtTKgk6ZFvRId8P6U7bQuqKWMA0IfMJdNPzVLrO
        xai4HIRJrkBx8wwG7gdyWfsmEm3xYrRm7EX0NutS6m2gsA+LmJ26+BgTUwZ6XHPQUEbHtHihyER/mT+X
        RzpD0Sp1Q2PqboSJLcup48jkcCf+CohJRGJtW1RyPSIp4Qm1KChUoEcewOWhpmC0SFxRl+iFY24iJjtR
        zEPtTUmY43KAHYUJhR0Lw85j38mKR0VXMT30Cs0nXFEj9UCIi3klnWN3NqUIjm40ITGbzVf+FIxn1/bQ
        OWLn6Zf4vf9lK+5mpeCyjxhHHM3u0XEHilBd7EvUxT4kaJ2QxIst1wba0rxZwGG9sy9s7LyHLWy3VNtb
        iYLp2CYmB9oICeOQtYAcpFzYYb020CTzZAG2MbZPZwp70+y3UYCVgPhbCsgBCovEiUUkw8uW/ASLygoB
        zKvoygAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="btnRegisterCustomer.ImageOptions.LargeImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAACp0RVh0VGl0
        bGUAQ29udGFjdDtDYXJkO0luZm87RGV0YWlsO1BlcnNvbjtFZGl04bGTWAAACFtJREFUWEfFlmlQVFcW
        x9slmrjGPQmTxBnHmIGJM05ZMzXmQ0anMhnHZBKTlJhUKJeoRFlkUxEU0WBkC/suCqItm4gsLhg1CEEE
        jHbciOKggHQ3a3dDb2z5zzm3+zVLOVXjfMi8ql+d9+499/zPu/fcd58MwP+VJzb+nDyx8edk5DXqf2T0
        fwn72i5bAvJCxV+yTt+6S0AikymWuCk4PsQKiizIi36AvHCQY4UKHCuwcJQ5pUBG/vWGQzlVS0nOloSU
        wKi0vO9VzWodBgZ+IgasSPdshzLY12+97xcMjuNni7W08XOTUoMEeaWa9MawJgtLCYw+nHtNDOjrG0Bv
        Xz96ewm2RJ/V2toJ0SZ8RvjbxlnbpTaiv38A8ccqWXEsMSyBMamUADvwgJ4hg5geCe4baq0M85Hoke77
        LPcEx489UsGKzxDDamFMSna1cDBbB7I199BgEawPZtFmCTa0b/CefHgs+1rbTFYrPfdR/Kj07ziBccSo
        ep9NMoavscmZVcJBGmAy90OjNSCnsByRyfnw25+GHUGHcfj4eajbtBSc/MwsYiEl93sk0yym5FxDcjZT
        gyTCxD6E0dQrkssuvsEJPEeMqXVbK6t1XSMSeCZRXinWXzhbOZx5Hj6BqcRBeO+RSEHMwQKbj9FkCW40
        M9ZnW3sfDATH7NKb0XAiC9fXOaLc6eP9pMmzYNuW4+KPXhGFw8F4kIFsUGQWtu9NhVdACpEMT2Z3MgLD
        5KJ/KElZNUjMrEaCxPEqxMurqa+HYvbgx6PpuB/oiX71A9Ru24TzK5cHkS7XAichGx975DuxpnpjL9En
        bOyhApGAB4l67ErCVsL/q3REJZ8a5tc9BNuzoQd6ottgwkV5AW77uqBfVYfO9DB0l55AueM/kffXtzgJ
        3hGy8dFp5WLteWC3oRddhOLOQ3jtSsZWv0QiAe47E7CNErp+u170M5JvnLwKcceqEEvEZFQit+QWymvq
        kZZZjuDEC1B1GtGRFoaGbeuhykiCYr8/EuwXcD2M5wSejThUJqq2S99jCW61ASEZcCNhV994uOyIR1DE
        cSGqo0R15DMSbbeZrAnaLj1K8rMREHkajdpenLn6EBWJKWiKPoCchQ5InT8fgXZ2IaTNtSB7LvxgqSge
        HQXQdnMgC9mnykg4Dlu2x2Hz9ljkn7062E+FpSGroTFRRyoRmX4F2WduorSqDqWZwXhUvBY9JhUKyuuQ
        VnQD5yrqELZiNeLn/hIe06eHk+5EQizBhJCkSzBQAtouCthlCdyqMSAz/zK+8ImFs08MnL2jIc/7Fq2d
        eiHayX6CHnToTOjUGdHeqcOdczGoP+WEn8z1MN31Q0VJFIou/4hPXJKxdOVevDnHIYY0pxLie8AJTDyQ
        cAF6quZOnRktJFBYUi2qfxOJbvKKEmz0jMIGj0i4U03kn62Csr2bhM1o15roQFLgYkUtLhwJwoO8TzFg
        fADjDR9oS99FpyIQ/jt8seQfO7Fmcyivux0hvgXO4yawvmzS/thvRAW3U8Bo2ueS4EYS3LA1Ep8LIrDe
        /Wusc7MQFpdHs2FAu0aPljYNFIVf437WKgzo78F4JwSab95Be40/Lu+2x1nP1xEdfAAF565xArMI8fYb
        rQlM3htdIoqujd6Gq32o4KBoONa6WljjEgZnSrKlowsqdTvOpu6hvf4h+rvuQluxBW2Fy9B+ZQdKd7yG
        Ypf5OBO9Ew2P1UhIL+UEphPiPPjD5F+zvmzKnohzVL29tO4mS4WLSpeqfci9FVEDOj2a1W2ozg1Gbdr7
        6NfegubSBrTlvonWMi9c8JyH/E2/EuL3/qWkT7gOPl+elBIQJ+JSewfWl03dHX6GKrsXatqvLRqC7RNQ
        dxigatdDRcGalC24mnUAt1PeRV/nDXSUrEWL/I9QX3JHictc5K2bi+KI7WikN3+k7KB6McJ7b96wBCLe
        msn6sql+ocXirdQdJELrKiyLWS0/q9oNaG7TQ9mqo6AqXJEH4Wb839HbVoO24k/x+NAiqM4748zGV5Dt
        9DJOhfrgYWMzmls0NI4L1gSPwOEJhP55GuvLpvoGF4mKVpGYkoRYjO+FbTdSG013azeJa/GoSYnyI/ug
        iHobZnUlVCdXoSHxt1CeXo/C9XY47miHk195ob7hMR6TeFNLl0icC9w94MSwGti/+HnWl03ZFpSvqrn1
        WCQxiMl2z8XZ2kFJ0LRfPBSOsqjNMDeXQZn5AR5FL0BzgRPynV7CsY9exIkgDzwkcTUl20KzyWNZ/Kqi
        ES7+mfxLxqpiBvYu4s8B7cm1rtGO3vtOar2/zIf3PuYkvCRo3TwCculwuoiMlHTkuq6gPymg+eh7eBgy
        D015q3Bi9Wykvz8HkVvWQJ5bivCkc3DdlQ233TlkLbj4ZRscPw/5hPR4743es3CyLOCNySIBzoY/DJwO
        T88MgqtDgvftS8SCiFVLCirpUGm5dx11QXPRmL0SOR/OwuEVs+DiMDmDfN4gXiVeIOYQswkez3F4wVlc
        vP1uh0myXQRf/Dnkc5k7eG1GwifW89Nm/eJ3vp+tNmqa6tBYloGyBF9kuH2EuJWL4GY/KZV8WJin91mC
        PzQj43B88Ud8x3O2zN9+kmDkxckMhRPjwTMWLfkg0NVjH5TVefg2xgfJTssQ+s5COP/e7iD1v0xwNBaR
        /nSGcdt9poz5wW2GwO83EwXi1/g/YR3Mb/PCn5Z9Vrt1VzqW/+1jLF/oYPJc/GrRe/NmOnIfIZ1so4pW
        z5YJHC2UrSfRzdNlCuaLaTaeJgGegVmvLXwbM160vz9l+ivBY8dNWExtfKhIUy6mdufrE2VPyxOFJTio
        NTi/IRcUFyMXFJcv14Ztyn0XTJA9PRNk/wZO9sxf8+NFVQAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="miAssignAssocLab.ImageOptions.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAARdEVYdFRpdGxlAENvbnRhY3Q7TmV341PvfgAAAKhJ
        REFUOE9j+P//P0WYoaWl5T8lGGwAuYA2Bhy+cfF/+6YlYBoGQGqQMQyA2BgGgDSXLZ8OpgkBog0AqUHG
        MABiE+UFKECPQkwDbjx79H/JkV3/a1fPAdMgPgiA1ABdxQByGRRjN2DOgS0wBWAM4sMASBOSHHYDGtfN
        h2sGYSAfpAkZww2AYYwwQAPImrBi6hiAB8OdCsUwjXAx5GjBipEVAzG1DZjOAABlYQpZ3YZMTQAAAABJ
        RU5ErkJggg==
</value>
  </data>
</root>