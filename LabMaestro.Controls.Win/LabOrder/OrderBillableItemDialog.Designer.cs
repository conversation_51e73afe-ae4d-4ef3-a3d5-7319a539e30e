﻿namespace LabMaestro.Controls.Win
{
    partial class OrderBillableItemDialog
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            Xceed.Grid.GradientMap gradientMap2 = new Xceed.Grid.GradientMap();
            Xceed.Grid.GradientStop gradientStop3 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientStop gradientStop4 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientMap gradientMap1 = new Xceed.Grid.GradientMap();
            Xceed.Grid.GradientStop gradientStop1 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientStop gradientStop2 = new Xceed.Grid.GradientStop();
            this.grdBillableItems = new Xceed.Grid.GridControl();
            this.dataRowTemplate2 = new Xceed.Grid.DataRow();
            this.visualGridElementStyle2 = new Xceed.Grid.VisualGridElementStyle();
            this.dataRowStyle1 = new Xceed.Grid.VisualGridElementStyle();
            this.columnManagerRow2 = new Xceed.Grid.ColumnManagerRow();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.edQuantity = new DevExpress.XtraEditors.SpinEdit();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.btnApply = new DevExpress.XtraEditors.SimpleButton();
            ((System.ComponentModel.ISupportInitialize)(this.grdBillableItems)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataRowTemplate2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.columnManagerRow2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.edQuantity.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // grdBillableItems
            // 
            this.grdBillableItems.ClipCurrentCellSelection = false;
            this.grdBillableItems.DataRowTemplate = this.dataRowTemplate2;
            this.grdBillableItems.DataRowTemplateStyles.Add(this.visualGridElementStyle2);
            this.grdBillableItems.DataRowTemplateStyles.Add(this.dataRowStyle1);
            // 
            // 
            // 
            this.grdBillableItems.ErrorVisualStyle.OverrideUIStyle = false;
            // 
            // 
            // 
            this.grdBillableItems.FixedColumnSplitter.AllowRepositioning = false;
            this.grdBillableItems.FixedColumnSplitter.Visible = false;
            this.grdBillableItems.FixedHeaderRows.Add(this.columnManagerRow2);
            this.grdBillableItems.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.grdBillableItems.GridLineColor = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(239)))), ((int)(((byte)(255)))));
            this.grdBillableItems.GridLineStyle = System.Drawing.Drawing2D.DashStyle.Dot;
            this.grdBillableItems.InactiveSelectionBackColor = System.Drawing.Color.RoyalBlue;
            // 
            // 
            // 
            this.grdBillableItems.InactiveSelectionVisualStyle.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(229)))), ((int)(((byte)(229)))), ((int)(((byte)(229)))));
            this.grdBillableItems.InactiveSelectionVisualStyle.ForeColor = System.Drawing.Color.Black;
            this.grdBillableItems.InactiveSelectionVisualStyle.OverrideUIStyle = true;
            this.grdBillableItems.Location = new System.Drawing.Point(6, 13);
            this.grdBillableItems.Margin = new System.Windows.Forms.Padding(4);
            this.grdBillableItems.Name = "grdBillableItems";
            this.grdBillableItems.OverrideUIStyle = false;
            this.grdBillableItems.ReadOnly = true;
            // 
            // 
            // 
            this.grdBillableItems.RowSelectorPane.AllowRowResize = false;
            this.grdBillableItems.RowSelectorPane.CurrentImageIndex = 16;
            gradientMap2.GradientMode = Xceed.UI.GradientMode.Horizontal;
            gradientStop3.Color = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(239)))), ((int)(((byte)(255)))));
            gradientStop3.Offset = 0D;
            gradientStop4.Color = System.Drawing.Color.FromArgb(((int)(((byte)(175)))), ((int)(((byte)(210)))), ((int)(((byte)(255)))));
            gradientStop4.Offset = 1D;
            gradientMap2.GradientStops.Add(gradientStop3);
            gradientMap2.GradientStops.Add(gradientStop4);
            this.grdBillableItems.RowSelectorPane.GradientMap = gradientMap2;
            // 
            // 
            // 
            this.grdBillableItems.RowSelectorPane.HotVisualStyle.OverrideUIStyle = true;
            this.grdBillableItems.RowSelectorPane.OverrideUIStyle = true;
            this.grdBillableItems.RowSelectorPane.SelectedImageIndex = 20;
            this.grdBillableItems.RowSelectorPane.Width = 17;
            this.grdBillableItems.SelectionBackColor = System.Drawing.Color.Indigo;
            this.grdBillableItems.SelectionMode = System.Windows.Forms.SelectionMode.One;
            // 
            // 
            // 
            this.grdBillableItems.SelectionVisualStyle.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(167)))), ((int)(((byte)(205)))), ((int)(((byte)(240)))));
            this.grdBillableItems.SelectionVisualStyle.ForeColor = System.Drawing.Color.Black;
            this.grdBillableItems.SelectionVisualStyle.OverrideUIStyle = true;
            this.grdBillableItems.ShowFocusRectangle = false;
            // 
            // 
            // 
            this.grdBillableItems.SideMargin.Visible = false;
            this.grdBillableItems.Size = new System.Drawing.Size(303, 262);
            this.grdBillableItems.SynchronizeDetailGrids = false;
            this.grdBillableItems.TabIndex = 0;
            this.grdBillableItems.TabStop = false;
            this.grdBillableItems.TreeLineColor = System.Drawing.Color.Silver;
            this.grdBillableItems.UIStyle = Xceed.UI.UIStyle.System;
            this.grdBillableItems.UIVirtualizationMode = Xceed.Grid.UIVirtualizationMode.None;
            // 
            // visualGridElementStyle2
            // 
            // 
            // 
            // 
            this.visualGridElementStyle2.HotVisualStyle.OverrideUIStyle = true;
            this.visualGridElementStyle2.OverrideUIStyle = false;
            // 
            // dataRowStyle1
            // 
            this.dataRowStyle1.BackColor = System.Drawing.Color.LightCyan;
            // 
            // columnManagerRow2
            // 
            this.columnManagerRow2.AllowColumnReorder = false;
            this.columnManagerRow2.AllowColumnResize = true;
            this.columnManagerRow2.AllowSort = false;
            this.columnManagerRow2.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            gradientStop1.Color = System.Drawing.Color.FromArgb(((int)(((byte)(252)))), ((int)(((byte)(253)))), ((int)(((byte)(255)))));
            gradientStop1.Offset = 0D;
            gradientStop2.Color = System.Drawing.Color.FromArgb(((int)(((byte)(196)))), ((int)(((byte)(221)))), ((int)(((byte)(255)))));
            gradientStop2.Offset = 1D;
            gradientMap1.GradientStops.Add(gradientStop1);
            gradientMap1.GradientStops.Add(gradientStop2);
            this.columnManagerRow2.GradientMap = gradientMap1;
            this.columnManagerRow2.Height = 23;
            // 
            // 
            // 
            this.columnManagerRow2.HotVisualStyle.OverrideUIStyle = false;
            this.columnManagerRow2.OverrideUIStyle = true;
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(11, 284);
            this.labelControl2.Margin = new System.Windows.Forms.Padding(4);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(52, 16);
            this.labelControl2.TabIndex = 29;
            this.labelControl2.Text = "Quantity:";
            // 
            // edQuantity
            // 
            this.edQuantity.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.edQuantity.Location = new System.Drawing.Point(70, 282);
            this.edQuantity.Name = "edQuantity";
            this.edQuantity.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.edQuantity.Properties.Appearance.Options.UseFont = true;
            this.edQuantity.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.edQuantity.Properties.Mask.EditMask = "n0";
            this.edQuantity.Size = new System.Drawing.Size(90, 22);
            this.edQuantity.TabIndex = 1;
            this.edQuantity.KeyUp += new System.Windows.Forms.KeyEventHandler(this.edQuantity_KeyUp);
            // 
            // btnCancel
            // 
            this.btnCancel.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCancel.Appearance.ForeColor = System.Drawing.Color.Black;
            this.btnCancel.Appearance.Options.UseFont = true;
            this.btnCancel.Appearance.Options.UseForeColor = true;
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.cancel_16;
            this.btnCancel.Location = new System.Drawing.Point(9, 311);
            this.btnCancel.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(87, 28);
            this.btnCancel.TabIndex = 3;
            this.btnCancel.Text = "Cancel";
            // 
            // btnApply
            // 
            this.btnApply.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnApply.Appearance.ForeColor = System.Drawing.Color.Black;
            this.btnApply.Appearance.Options.UseFont = true;
            this.btnApply.Appearance.Options.UseForeColor = true;
            this.btnApply.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.tick_16;
            this.btnApply.Location = new System.Drawing.Point(222, 311);
            this.btnApply.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.btnApply.Name = "btnApply";
            this.btnApply.Size = new System.Drawing.Size(87, 28);
            this.btnApply.TabIndex = 2;
            this.btnApply.Text = "Apply";
            this.btnApply.Click += new System.EventHandler(this.btnApply_Click);
            // 
            // OrderBillableItemDialog
            // 
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 16F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(315, 346);
            this.ControlBox = false;
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnApply);
            this.Controls.Add(this.edQuantity);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.grdBillableItems);
            this.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "OrderBillableItemDialog";
            this.SizeGripStyle = System.Windows.Forms.SizeGripStyle.Hide;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "OrderBillableItemDialog";
            this.TopMost = true;
            ((System.ComponentModel.ISupportInitialize)(this.grdBillableItems)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataRowTemplate2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.columnManagerRow2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.edQuantity.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private Xceed.Grid.GridControl grdBillableItems;
        private Xceed.Grid.DataRow dataRowTemplate2;
        private Xceed.Grid.VisualGridElementStyle visualGridElementStyle2;
        private Xceed.Grid.VisualGridElementStyle dataRowStyle1;
        private Xceed.Grid.ColumnManagerRow columnManagerRow2;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.SpinEdit edQuantity;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private DevExpress.XtraEditors.SimpleButton btnApply;
    }
}