﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using LabMaestro.Domain;

namespace LabMaestro.Controls.Win.LabOrder;

public partial class AssociatedLabDialog : DevExpress.XtraEditors.XtraForm
{
    internal class ItemInfo(short id, string name)
    {
        public short Id { get; set; } = id;
        public string Name { get; set; } = name;
        public override string ToString() => Name;
    }

    public short SelectedId { get; private set; } = -1;
    private IEnumerable<ItemInfo> _items = [];

    public AssociatedLabDialog()
    {
        InitializeComponent();
        WinUtils.ApplyTheme(this);
    }

    public void SetAssociateLabs(IEnumerable<AssociateOrganizationSlice> labs) =>
        _items = labs.Select(x => new ItemInfo(x.Id, x.Name));

    public void SetCorporateClients(IEnumerable<CorporateClientSlice> clients) =>
        _items = clients.Select(x => new ItemInfo(x.Id, x.Name));

    private void AssociatedLabDialog_Load(object sender, EventArgs e)
    {
        try
        {
            lbxItems.BeginUpdate();
            lbxItems.Items.AddRange(_items.ToArray());
        }
        finally
        {
            lbxItems.EndUpdate();
        }
    }

    private void btnClear_Click(object sender, EventArgs e) => DialogResult = DialogResult.Ignore;

    private void btnAssign_Click(object sender, EventArgs e)
    {
        var item = getSelectedItem();
        if (item != null)
        {
            SelectedId = item.Id;
            SelectedName = item.Name;
            DialogResult = DialogResult.OK;
        }
    }

    public string SelectedName { get; set; }

    private ItemInfo? getSelectedItem() => lbxItems.SelectedItem as ItemInfo;

    private void lbxLabs_SelectedValueChanged(object sender, EventArgs e) =>
        lblSelectedItemName.Text = getSelectedItem()?.Name ?? string.Empty;

    public void SetLabel(string name)
    {
        lblSelectItem.Text = $"Select {name}";
        Text = $"Select {name}";
        btnAssign.Text = $"Assign {name}";
    }
}