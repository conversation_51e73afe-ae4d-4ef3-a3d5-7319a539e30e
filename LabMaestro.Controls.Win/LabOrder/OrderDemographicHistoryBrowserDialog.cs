﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: OrderDemographicHistoryBrowserDialog.cs 761 2013-07-06 14:53:17Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using LabMaestro.BusinessLogic;

namespace LabMaestro.Controls.Win
{
    public partial class OrderDemographicHistoryBrowserDialog : XtraForm
    {
        public OrderDemographicHistoryBrowserDialog()
        {
            InitializeComponent();

            WinUtils.ApplyTheme(this);
            WinUtils.SetFormTitle(this, "Lab Order Demographic Histories");
        }

        public LabOrderDemographicInfoSlice SelectedSlice { get; private set; }

        private LabOrderDemographicInfoSlice getSelectedDemographicSlice()
        {
            SelectedSlice = null;
            if (gridView.SelectedRowsCount == 1)
            {
                SelectedSlice = gridView.GetFocusedRow() as LabOrderDemographicInfoSlice;
            }

            return SelectedSlice;
        }

        public static LabOrderDemographicInfoSlice ExecuteDialog(IWin32Window parent)
        {
            using (var frm = new OrderDemographicHistoryBrowserDialog())
            {
                if (frm.ShowDialog(parent) == DialogResult.OK)
                {
                    return frm.SelectedSlice;
                }
            }
            return null;
        }

        private void OrderDemographiHistoryBrowserDialog_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape) Close();
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            if (MessageDlg.Confirm("Really remove all demographics?"))
                LabOrderDemographicHistory.Reset();

            populateGridControl();
        }

        private void populateGridControl()
        {
            gridView.BeginUpdate();
            try
            {
                labOrderDemographicInfoSliceBindingSource.DataSource = LabOrderDemographicHistory.Items;
            }
            finally
            {
                gridView.EndUpdate();
            }
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            var item = getSelectedDemographicSlice();
            if (item != null && MessageDlg.Confirm("Really delete the selected item?"))
            {
                LabOrderDemographicHistory.Remove(item);
                populateGridControl();
            }
        }

        private void OrderDemographiHistoryBrowserDialog_Load(object sender, EventArgs e)
        {
            populateGridControl();
        }

        private void btnApply_Click(object sender, EventArgs e)
        {
            var item = getSelectedDemographicSlice();
            if (item == null)
            {
                MessageDlg.Error("Nothing to apply.");
                return;
            }
            
            DialogResult = DialogResult.OK;
        }
    }
}