﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: BatterySelectionDialog.cs 1270 2014-05-20 05:28:47Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Shared;

namespace LabMaestro.Controls.Win.LabOrder;

public partial class BatterySelectionDialog : XtraForm
{
    private IEnumerable<Tuple<int, string>> _batteries = [];

    public BatterySelectionDialog()
    {
        InitializeComponent();
        WinUtils.ApplyTheme(this);
        SelectedBatteryId = -1;
    }

    public int SelectedBatteryId { get; private set; }

    private void BatterySelectionDialog_Load(object sender, EventArgs e)
    {
        SelectedBatteryId = -1;
        lbBatteries.BeginUpdate();
        try {
            lbBatteries.Items.Clear();
            foreach (var tuple in _batteries) lbBatteries.Items.Add(new BatteryInfo(tuple.Item2, tuple.Item1));
        }
        finally {
            lbBatteries.EndUpdate();
        }

        lbBatteries.Focus();
    }

    private void lbBatteries_SelectedIndexChanged(object sender, EventArgs e)
    {
        var battery = lbBatteries.SelectedItem as BatteryInfo;
        if (battery != null) {
            SelectedBatteryId = battery.Id;
            var components = FaultHandler.Shield(() => BatteryComponentRepository
                                                     .GetBatteryComponentsDetailed(SelectedBatteryId, false));

            lblComponentCount.Text = components.Count().ToString();
            lbComponents.BeginUpdate();
            try {
                lbComponents.Items.Clear();
                foreach (var slice in components) lbComponents.Items.Add(new BatteryComponentTestInfo(slice.ShortName, slice.ListPrice));
            }
            finally {
                lbComponents.EndUpdate();
            }
        }
    }

    private void btnSelect_Click(object sender, EventArgs e)
    {
        if (SelectedBatteryId == -1) MessageDlg.Warning("Please select a battery of tests.");

        DialogResult = DialogResult.OK;
    }

    private void lbBatteries_KeyUp(object sender, KeyEventArgs e)
    {
        if (e.KeyCode == Keys.Enter) {
            e.Handled = true;
            btnSelect_Click(null, null);
        }
    }

    public void SetBatteries(IEnumerable<Tuple<int, string>> batteries) => _batteries = batteries;

    public sealed class BatteryComponentTestInfo(string name, decimal price)
    {
        public decimal Price { get; } = price;
        public string Name { get; } = name;
        public override string ToString() => $"{Name}: {SharedUtilities.MoneyToStringPlainCulture(Price)}";
    }

    public sealed class BatteryInfo(string name, int id)
    {
        public string Name { get; } = name;
        public int Id { get; } = id;
        public override string ToString() => Name;
    }
}