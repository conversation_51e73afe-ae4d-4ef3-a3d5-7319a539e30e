﻿namespace LabMaestro.Controls.Win
{
    partial class AuditTrailViewerDialog
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.panelControl1 = new DevExpress.XtraEditors.PanelControl();
            this.lblReferrerName = new DevExpress.XtraEditors.LabelControl();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.lblBookingDate = new DevExpress.XtraEditors.LabelControl();
            this.lblOrderId = new DevExpress.XtraEditors.LabelControl();
            this.lblInvoiceId = new DevExpress.XtraEditors.LabelControl();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.lblPatientName = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.gridControl = new DevExpress.XtraGrid.GridControl();
            this.auditEventSliceBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.gridView = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colEventDate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colEventTime = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colPerformingUser = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colEventTypeDescr = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colResultBundle = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colWorkflowStageDescr = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colEventCategoryDescr = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colUserIpAddress = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colRemarks = new DevExpress.XtraGrid.Columns.GridColumn();
            this.btnClose = new DevExpress.XtraEditors.SimpleButton();
            this.btnPrint = new DevExpress.XtraEditors.SimpleButton();
            this.rgFilter = new DevExpress.XtraEditors.RadioGroup();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            this.gridTests = new DevExpress.XtraGrid.GridControl();
            this.componentLabTestsSliceBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.gvwTests = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colTestName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colLabName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colWorkflowStage = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colIsCancelled = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colDateCreated = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colLastModified = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colResultsETA = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colLabTestId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colBundleId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colBundleIsCancelled = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colBundleDateCreated = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colBundleLastModified = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colBundleWorkflowStage = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colBundleResultType = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).BeginInit();
            this.panelControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.auditEventSliceBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rgFilter.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridTests)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.componentLabTestsSliceBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvwTests)).BeginInit();
            this.SuspendLayout();
            // 
            // panelControl1
            // 
            this.panelControl1.Appearance.BackColor = System.Drawing.Color.Azure;
            this.panelControl1.Appearance.Options.UseBackColor = true;
            this.panelControl1.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.panelControl1.Controls.Add(this.lblReferrerName);
            this.panelControl1.Controls.Add(this.labelControl7);
            this.panelControl1.Controls.Add(this.lblBookingDate);
            this.panelControl1.Controls.Add(this.lblOrderId);
            this.panelControl1.Controls.Add(this.lblInvoiceId);
            this.panelControl1.Controls.Add(this.labelControl5);
            this.panelControl1.Controls.Add(this.lblPatientName);
            this.panelControl1.Controls.Add(this.labelControl3);
            this.panelControl1.Controls.Add(this.labelControl2);
            this.panelControl1.Controls.Add(this.labelControl1);
            this.panelControl1.Location = new System.Drawing.Point(12, 12);
            this.panelControl1.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Flat;
            this.panelControl1.LookAndFeel.UseDefaultLookAndFeel = false;
            this.panelControl1.Name = "panelControl1";
            this.panelControl1.Size = new System.Drawing.Size(1088, 55);
            this.panelControl1.TabIndex = 0;
            // 
            // lblReferrerName
            // 
            this.lblReferrerName.Appearance.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblReferrerName.Appearance.ForeColor = System.Drawing.Color.MediumVioletRed;
            this.lblReferrerName.Appearance.Options.UseFont = true;
            this.lblReferrerName.Appearance.Options.UseForeColor = true;
            this.lblReferrerName.Location = new System.Drawing.Point(433, 28);
            this.lblReferrerName.Name = "lblReferrerName";
            this.lblReferrerName.Size = new System.Drawing.Size(128, 17);
            this.lblReferrerName.TabIndex = 9;
            this.lblReferrerName.Text = "99/99/9999 99:99 PM";
            // 
            // labelControl7
            // 
            this.labelControl7.Appearance.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl7.Appearance.Options.UseFont = true;
            this.labelControl7.Location = new System.Drawing.Point(354, 28);
            this.labelControl7.Name = "labelControl7";
            this.labelControl7.Size = new System.Drawing.Size(71, 17);
            this.labelControl7.TabIndex = 8;
            this.labelControl7.Text = "Referred By:";
            // 
            // lblBookingDate
            // 
            this.lblBookingDate.Appearance.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblBookingDate.Appearance.ForeColor = System.Drawing.Color.MediumVioletRed;
            this.lblBookingDate.Appearance.Options.UseFont = true;
            this.lblBookingDate.Appearance.Options.UseForeColor = true;
            this.lblBookingDate.Location = new System.Drawing.Point(775, 5);
            this.lblBookingDate.Name = "lblBookingDate";
            this.lblBookingDate.Size = new System.Drawing.Size(128, 17);
            this.lblBookingDate.TabIndex = 7;
            this.lblBookingDate.Text = "99/99/9999 99:99 PM";
            // 
            // lblOrderId
            // 
            this.lblOrderId.Appearance.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblOrderId.Appearance.ForeColor = System.Drawing.Color.MediumVioletRed;
            this.lblOrderId.Appearance.Options.UseFont = true;
            this.lblOrderId.Appearance.Options.UseForeColor = true;
            this.lblOrderId.Location = new System.Drawing.Point(433, 5);
            this.lblOrderId.Name = "lblOrderId";
            this.lblOrderId.Size = new System.Drawing.Size(34, 17);
            this.lblOrderId.TabIndex = 6;
            this.lblOrderId.Text = "W999";
            // 
            // lblInvoiceId
            // 
            this.lblInvoiceId.Appearance.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblInvoiceId.Appearance.ForeColor = System.Drawing.Color.MediumVioletRed;
            this.lblInvoiceId.Appearance.Options.UseFont = true;
            this.lblInvoiceId.Appearance.Options.UseForeColor = true;
            this.lblInvoiceId.Location = new System.Drawing.Point(107, 5);
            this.lblInvoiceId.Name = "lblInvoiceId";
            this.lblInvoiceId.Size = new System.Drawing.Size(56, 17);
            this.lblInvoiceId.TabIndex = 5;
            this.lblInvoiceId.Text = "99999999";
            // 
            // labelControl5
            // 
            this.labelControl5.Appearance.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl5.Appearance.Options.UseFont = true;
            this.labelControl5.Location = new System.Drawing.Point(688, 5);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(81, 17);
            this.labelControl5.TabIndex = 4;
            this.labelControl5.Text = "Booking Date:";
            // 
            // lblPatientName
            // 
            this.lblPatientName.Appearance.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblPatientName.Appearance.ForeColor = System.Drawing.Color.MediumVioletRed;
            this.lblPatientName.Appearance.Options.UseFont = true;
            this.lblPatientName.Appearance.Options.UseForeColor = true;
            this.lblPatientName.Location = new System.Drawing.Point(107, 28);
            this.lblPatientName.Name = "lblPatientName";
            this.lblPatientName.Size = new System.Drawing.Size(162, 17);
            this.lblPatientName.TabIndex = 3;
            this.lblPatientName.Text = "Mrs. Ding Dong Blueberry";
            // 
            // labelControl3
            // 
            this.labelControl3.Appearance.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl3.Appearance.Options.UseFont = true;
            this.labelControl3.Location = new System.Drawing.Point(13, 28);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(81, 17);
            this.labelControl3.TabIndex = 2;
            this.labelControl3.Text = "Patient Name:";
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(354, 5);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(54, 17);
            this.labelControl2.TabIndex = 1;
            this.labelControl2.Text = "Order ID:";
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Location = new System.Drawing.Point(13, 5);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(55, 17);
            this.labelControl1.TabIndex = 0;
            this.labelControl1.Text = "Invoice #:";
            // 
            // gridControl
            // 
            this.gridControl.DataSource = this.auditEventSliceBindingSource;
            this.gridControl.Location = new System.Drawing.Point(12, 129);
            this.gridControl.MainView = this.gridView;
            this.gridControl.Name = "gridControl";
            this.gridControl.Size = new System.Drawing.Size(1088, 246);
            this.gridControl.TabIndex = 1;
            this.gridControl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView});
            // 
            // auditEventSliceBindingSource
            // 
            this.auditEventSliceBindingSource.DataSource = typeof(LabMaestro.BusinessLogic.AuditEventSlice);
            // 
            // gridView
            // 
            this.gridView.Appearance.ColumnFilterButton.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(221)))), ((int)(((byte)(236)))), ((int)(((byte)(254)))));
            this.gridView.Appearance.ColumnFilterButton.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(171)))), ((int)(((byte)(228)))));
            this.gridView.Appearance.ColumnFilterButton.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(221)))), ((int)(((byte)(236)))), ((int)(((byte)(254)))));
            this.gridView.Appearance.ColumnFilterButton.ForeColor = System.Drawing.Color.Black;
            this.gridView.Appearance.ColumnFilterButton.GradientMode = System.Drawing.Drawing2D.LinearGradientMode.Vertical;
            this.gridView.Appearance.ColumnFilterButton.Options.UseBackColor = true;
            this.gridView.Appearance.ColumnFilterButton.Options.UseBorderColor = true;
            this.gridView.Appearance.ColumnFilterButton.Options.UseForeColor = true;
            this.gridView.Appearance.ColumnFilterButtonActive.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(247)))), ((int)(((byte)(251)))), ((int)(((byte)(255)))));
            this.gridView.Appearance.ColumnFilterButtonActive.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(154)))), ((int)(((byte)(190)))), ((int)(((byte)(243)))));
            this.gridView.Appearance.ColumnFilterButtonActive.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(247)))), ((int)(((byte)(251)))), ((int)(((byte)(255)))));
            this.gridView.Appearance.ColumnFilterButtonActive.ForeColor = System.Drawing.Color.Black;
            this.gridView.Appearance.ColumnFilterButtonActive.GradientMode = System.Drawing.Drawing2D.LinearGradientMode.Vertical;
            this.gridView.Appearance.ColumnFilterButtonActive.Options.UseBackColor = true;
            this.gridView.Appearance.ColumnFilterButtonActive.Options.UseBorderColor = true;
            this.gridView.Appearance.ColumnFilterButtonActive.Options.UseForeColor = true;
            this.gridView.Appearance.Empty.BackColor = System.Drawing.Color.White;
            this.gridView.Appearance.Empty.Options.UseBackColor = true;
            this.gridView.Appearance.EvenRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(231)))), ((int)(((byte)(242)))), ((int)(((byte)(254)))));
            this.gridView.Appearance.EvenRow.ForeColor = System.Drawing.Color.Black;
            this.gridView.Appearance.EvenRow.Options.UseBackColor = true;
            this.gridView.Appearance.EvenRow.Options.UseForeColor = true;
            this.gridView.Appearance.FilterCloseButton.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(221)))), ((int)(((byte)(236)))), ((int)(((byte)(254)))));
            this.gridView.Appearance.FilterCloseButton.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(171)))), ((int)(((byte)(228)))));
            this.gridView.Appearance.FilterCloseButton.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(221)))), ((int)(((byte)(236)))), ((int)(((byte)(254)))));
            this.gridView.Appearance.FilterCloseButton.ForeColor = System.Drawing.Color.Black;
            this.gridView.Appearance.FilterCloseButton.GradientMode = System.Drawing.Drawing2D.LinearGradientMode.Vertical;
            this.gridView.Appearance.FilterCloseButton.Options.UseBackColor = true;
            this.gridView.Appearance.FilterCloseButton.Options.UseBorderColor = true;
            this.gridView.Appearance.FilterCloseButton.Options.UseForeColor = true;
            this.gridView.Appearance.FilterPanel.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(62)))), ((int)(((byte)(109)))), ((int)(((byte)(185)))));
            this.gridView.Appearance.FilterPanel.ForeColor = System.Drawing.Color.White;
            this.gridView.Appearance.FilterPanel.Options.UseBackColor = true;
            this.gridView.Appearance.FilterPanel.Options.UseForeColor = true;
            this.gridView.Appearance.FixedLine.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(59)))), ((int)(((byte)(97)))), ((int)(((byte)(156)))));
            this.gridView.Appearance.FixedLine.Options.UseBackColor = true;
            this.gridView.Appearance.FocusedCell.BackColor = System.Drawing.Color.White;
            this.gridView.Appearance.FocusedCell.ForeColor = System.Drawing.Color.Black;
            this.gridView.Appearance.FocusedCell.Options.UseBackColor = true;
            this.gridView.Appearance.FocusedCell.Options.UseForeColor = true;
            this.gridView.Appearance.FocusedRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(49)))), ((int)(((byte)(106)))), ((int)(((byte)(197)))));
            this.gridView.Appearance.FocusedRow.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.gridView.Appearance.FocusedRow.ForeColor = System.Drawing.Color.White;
            this.gridView.Appearance.FocusedRow.Options.UseBackColor = true;
            this.gridView.Appearance.FocusedRow.Options.UseFont = true;
            this.gridView.Appearance.FocusedRow.Options.UseForeColor = true;
            this.gridView.Appearance.FooterPanel.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(221)))), ((int)(((byte)(236)))), ((int)(((byte)(254)))));
            this.gridView.Appearance.FooterPanel.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(171)))), ((int)(((byte)(228)))));
            this.gridView.Appearance.FooterPanel.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(221)))), ((int)(((byte)(236)))), ((int)(((byte)(254)))));
            this.gridView.Appearance.FooterPanel.ForeColor = System.Drawing.Color.Black;
            this.gridView.Appearance.FooterPanel.GradientMode = System.Drawing.Drawing2D.LinearGradientMode.Vertical;
            this.gridView.Appearance.FooterPanel.Options.UseBackColor = true;
            this.gridView.Appearance.FooterPanel.Options.UseBorderColor = true;
            this.gridView.Appearance.FooterPanel.Options.UseForeColor = true;
            this.gridView.Appearance.GroupButton.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(193)))), ((int)(((byte)(216)))), ((int)(((byte)(247)))));
            this.gridView.Appearance.GroupButton.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(193)))), ((int)(((byte)(216)))), ((int)(((byte)(247)))));
            this.gridView.Appearance.GroupButton.ForeColor = System.Drawing.Color.Black;
            this.gridView.Appearance.GroupButton.Options.UseBackColor = true;
            this.gridView.Appearance.GroupButton.Options.UseBorderColor = true;
            this.gridView.Appearance.GroupButton.Options.UseForeColor = true;
            this.gridView.Appearance.GroupFooter.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(193)))), ((int)(((byte)(216)))), ((int)(((byte)(247)))));
            this.gridView.Appearance.GroupFooter.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(193)))), ((int)(((byte)(216)))), ((int)(((byte)(247)))));
            this.gridView.Appearance.GroupFooter.ForeColor = System.Drawing.Color.Black;
            this.gridView.Appearance.GroupFooter.Options.UseBackColor = true;
            this.gridView.Appearance.GroupFooter.Options.UseBorderColor = true;
            this.gridView.Appearance.GroupFooter.Options.UseForeColor = true;
            this.gridView.Appearance.GroupPanel.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(62)))), ((int)(((byte)(109)))), ((int)(((byte)(185)))));
            this.gridView.Appearance.GroupPanel.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(221)))), ((int)(((byte)(236)))), ((int)(((byte)(254)))));
            this.gridView.Appearance.GroupPanel.Options.UseBackColor = true;
            this.gridView.Appearance.GroupPanel.Options.UseForeColor = true;
            this.gridView.Appearance.GroupRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(193)))), ((int)(((byte)(216)))), ((int)(((byte)(247)))));
            this.gridView.Appearance.GroupRow.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(193)))), ((int)(((byte)(216)))), ((int)(((byte)(247)))));
            this.gridView.Appearance.GroupRow.Font = new System.Drawing.Font("Tahoma", 8F, System.Drawing.FontStyle.Bold);
            this.gridView.Appearance.GroupRow.ForeColor = System.Drawing.Color.Black;
            this.gridView.Appearance.GroupRow.Options.UseBackColor = true;
            this.gridView.Appearance.GroupRow.Options.UseBorderColor = true;
            this.gridView.Appearance.GroupRow.Options.UseFont = true;
            this.gridView.Appearance.GroupRow.Options.UseForeColor = true;
            this.gridView.Appearance.HeaderPanel.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(221)))), ((int)(((byte)(236)))), ((int)(((byte)(254)))));
            this.gridView.Appearance.HeaderPanel.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(171)))), ((int)(((byte)(228)))));
            this.gridView.Appearance.HeaderPanel.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(221)))), ((int)(((byte)(236)))), ((int)(((byte)(254)))));
            this.gridView.Appearance.HeaderPanel.ForeColor = System.Drawing.Color.Black;
            this.gridView.Appearance.HeaderPanel.GradientMode = System.Drawing.Drawing2D.LinearGradientMode.Vertical;
            this.gridView.Appearance.HeaderPanel.Options.UseBackColor = true;
            this.gridView.Appearance.HeaderPanel.Options.UseBorderColor = true;
            this.gridView.Appearance.HeaderPanel.Options.UseForeColor = true;
            this.gridView.Appearance.HideSelectionRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(106)))), ((int)(((byte)(153)))), ((int)(((byte)(228)))));
            this.gridView.Appearance.HideSelectionRow.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(208)))), ((int)(((byte)(224)))), ((int)(((byte)(251)))));
            this.gridView.Appearance.HideSelectionRow.Options.UseBackColor = true;
            this.gridView.Appearance.HideSelectionRow.Options.UseForeColor = true;
            this.gridView.Appearance.HorzLine.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(99)))), ((int)(((byte)(127)))), ((int)(((byte)(196)))));
            this.gridView.Appearance.HorzLine.Options.UseBackColor = true;
            this.gridView.Appearance.OddRow.BackColor = System.Drawing.Color.White;
            this.gridView.Appearance.OddRow.ForeColor = System.Drawing.Color.Black;
            this.gridView.Appearance.OddRow.Options.UseBackColor = true;
            this.gridView.Appearance.OddRow.Options.UseForeColor = true;
            this.gridView.Appearance.Preview.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(249)))), ((int)(((byte)(252)))), ((int)(((byte)(255)))));
            this.gridView.Appearance.Preview.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(88)))), ((int)(((byte)(129)))), ((int)(((byte)(185)))));
            this.gridView.Appearance.Preview.Options.UseBackColor = true;
            this.gridView.Appearance.Preview.Options.UseForeColor = true;
            this.gridView.Appearance.Row.BackColor = System.Drawing.Color.White;
            this.gridView.Appearance.Row.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.gridView.Appearance.Row.ForeColor = System.Drawing.Color.Black;
            this.gridView.Appearance.Row.Options.UseBackColor = true;
            this.gridView.Appearance.Row.Options.UseFont = true;
            this.gridView.Appearance.Row.Options.UseForeColor = true;
            this.gridView.Appearance.RowSeparator.BackColor = System.Drawing.Color.White;
            this.gridView.Appearance.RowSeparator.Options.UseBackColor = true;
            this.gridView.Appearance.SelectedRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(69)))), ((int)(((byte)(126)))), ((int)(((byte)(217)))));
            this.gridView.Appearance.SelectedRow.ForeColor = System.Drawing.Color.White;
            this.gridView.Appearance.SelectedRow.Options.UseBackColor = true;
            this.gridView.Appearance.SelectedRow.Options.UseForeColor = true;
            this.gridView.Appearance.VertLine.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(99)))), ((int)(((byte)(127)))), ((int)(((byte)(196)))));
            this.gridView.Appearance.VertLine.Options.UseBackColor = true;
            this.gridView.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colEventDate,
            this.colEventTime,
            this.colPerformingUser,
            this.colEventTypeDescr,
            this.colResultBundle,
            this.colWorkflowStageDescr,
            this.colEventCategoryDescr,
            this.colUserIpAddress,
            this.colRemarks});
            this.gridView.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.None;
            this.gridView.GridControl = this.gridControl;
            this.gridView.Name = "gridView";
            this.gridView.OptionsBehavior.Editable = false;
            this.gridView.OptionsBehavior.ReadOnly = true;
            this.gridView.OptionsCustomization.AllowFilter = false;
            this.gridView.OptionsCustomization.AllowGroup = false;
            this.gridView.OptionsCustomization.AllowQuickHideColumns = false;
            this.gridView.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView.OptionsView.AllowHtmlDrawGroups = false;
            this.gridView.OptionsView.AnimationType = DevExpress.XtraGrid.Views.Base.GridAnimationType.NeverAnimate;
            this.gridView.OptionsView.EnableAppearanceEvenRow = true;
            this.gridView.OptionsView.EnableAppearanceOddRow = true;
            this.gridView.OptionsView.GroupFooterShowMode = DevExpress.XtraGrid.Views.Grid.GroupFooterShowMode.Hidden;
            this.gridView.OptionsView.ShowFilterPanelMode = DevExpress.XtraGrid.Views.Base.ShowFilterPanelMode.Never;
            this.gridView.OptionsView.ShowGroupExpandCollapseButtons = false;
            this.gridView.OptionsView.ShowGroupPanel = false;
            this.gridView.OptionsView.ShowIndicator = false;
            // 
            // colEventDate
            // 
            this.colEventDate.Caption = "Date";
            this.colEventDate.DisplayFormat.FormatString = "dd/MM/yyyy";
            this.colEventDate.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.colEventDate.FieldName = "EventTime";
            this.colEventDate.Name = "colEventDate";
            this.colEventDate.OptionsColumn.AllowEdit = false;
            this.colEventDate.OptionsColumn.AllowFocus = false;
            this.colEventDate.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colEventDate.OptionsColumn.ReadOnly = true;
            this.colEventDate.Visible = true;
            this.colEventDate.VisibleIndex = 0;
            this.colEventDate.Width = 74;
            // 
            // colEventTime
            // 
            this.colEventTime.Caption = "Time";
            this.colEventTime.DisplayFormat.FormatString = "HH:mm";
            this.colEventTime.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.colEventTime.FieldName = "EventTime";
            this.colEventTime.Name = "colEventTime";
            this.colEventTime.OptionsColumn.AllowEdit = false;
            this.colEventTime.OptionsColumn.AllowFocus = false;
            this.colEventTime.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colEventTime.OptionsColumn.ReadOnly = true;
            this.colEventTime.Visible = true;
            this.colEventTime.VisibleIndex = 1;
            this.colEventTime.Width = 36;
            // 
            // colPerformingUser
            // 
            this.colPerformingUser.Caption = "Staff";
            this.colPerformingUser.FieldName = "PerformingUser";
            this.colPerformingUser.Name = "colPerformingUser";
            this.colPerformingUser.OptionsColumn.AllowEdit = false;
            this.colPerformingUser.OptionsColumn.AllowFocus = false;
            this.colPerformingUser.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colPerformingUser.OptionsColumn.ReadOnly = true;
            this.colPerformingUser.Visible = true;
            this.colPerformingUser.VisibleIndex = 2;
            this.colPerformingUser.Width = 99;
            // 
            // colEventTypeDescr
            // 
            this.colEventTypeDescr.Caption = "Action";
            this.colEventTypeDescr.FieldName = "EventTypeDescr";
            this.colEventTypeDescr.Name = "colEventTypeDescr";
            this.colEventTypeDescr.OptionsColumn.AllowEdit = false;
            this.colEventTypeDescr.OptionsColumn.AllowFocus = false;
            this.colEventTypeDescr.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colEventTypeDescr.OptionsColumn.ReadOnly = true;
            this.colEventTypeDescr.Visible = true;
            this.colEventTypeDescr.VisibleIndex = 3;
            this.colEventTypeDescr.Width = 105;
            // 
            // colResultBundle
            // 
            this.colResultBundle.Caption = "Bundle";
            this.colResultBundle.FieldName = "ResultBundle";
            this.colResultBundle.Name = "colResultBundle";
            this.colResultBundle.OptionsColumn.AllowEdit = false;
            this.colResultBundle.OptionsColumn.AllowFocus = false;
            this.colResultBundle.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colResultBundle.OptionsColumn.ReadOnly = true;
            this.colResultBundle.Visible = true;
            this.colResultBundle.VisibleIndex = 4;
            this.colResultBundle.Width = 144;
            // 
            // colWorkflowStageDescr
            // 
            this.colWorkflowStageDescr.Caption = "LAW Stage";
            this.colWorkflowStageDescr.FieldName = "WorkflowStageDescr";
            this.colWorkflowStageDescr.Name = "colWorkflowStageDescr";
            this.colWorkflowStageDescr.OptionsColumn.AllowEdit = false;
            this.colWorkflowStageDescr.OptionsColumn.AllowFocus = false;
            this.colWorkflowStageDescr.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colWorkflowStageDescr.OptionsColumn.ReadOnly = true;
            this.colWorkflowStageDescr.Visible = true;
            this.colWorkflowStageDescr.VisibleIndex = 5;
            this.colWorkflowStageDescr.Width = 139;
            // 
            // colEventCategoryDescr
            // 
            this.colEventCategoryDescr.Caption = "Event Category";
            this.colEventCategoryDescr.FieldName = "EventCategoryDescr";
            this.colEventCategoryDescr.Name = "colEventCategoryDescr";
            this.colEventCategoryDescr.OptionsColumn.AllowEdit = false;
            this.colEventCategoryDescr.OptionsColumn.AllowFocus = false;
            this.colEventCategoryDescr.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colEventCategoryDescr.OptionsColumn.ReadOnly = true;
            this.colEventCategoryDescr.Visible = true;
            this.colEventCategoryDescr.VisibleIndex = 6;
            this.colEventCategoryDescr.Width = 108;
            // 
            // colUserIpAddress
            // 
            this.colUserIpAddress.Caption = "IP";
            this.colUserIpAddress.FieldName = "UserIpAddress";
            this.colUserIpAddress.Name = "colUserIpAddress";
            this.colUserIpAddress.OptionsColumn.AllowEdit = false;
            this.colUserIpAddress.OptionsColumn.AllowFocus = false;
            this.colUserIpAddress.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colUserIpAddress.OptionsColumn.ReadOnly = true;
            this.colUserIpAddress.Visible = true;
            this.colUserIpAddress.VisibleIndex = 7;
            this.colUserIpAddress.Width = 46;
            // 
            // colRemarks
            // 
            this.colRemarks.Caption = "Remarks";
            this.colRemarks.FieldName = "Remarks";
            this.colRemarks.Name = "colRemarks";
            this.colRemarks.OptionsColumn.AllowEdit = false;
            this.colRemarks.OptionsColumn.AllowFocus = false;
            this.colRemarks.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colRemarks.OptionsColumn.ReadOnly = true;
            this.colRemarks.Visible = true;
            this.colRemarks.VisibleIndex = 8;
            this.colRemarks.Width = 105;
            // 
            // btnClose
            // 
            this.btnClose.Appearance.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnClose.Appearance.Options.UseFont = true;
            this.btnClose.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.btnClose.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.exit_16;
            this.btnClose.Location = new System.Drawing.Point(1018, 559);
            this.btnClose.Name = "btnClose";
            this.btnClose.Size = new System.Drawing.Size(82, 23);
            this.btnClose.TabIndex = 2;
            this.btnClose.Text = "Close";
            // 
            // btnPrint
            // 
            this.btnPrint.Appearance.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnPrint.Appearance.Options.UseFont = true;
            this.btnPrint.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.print2;
            this.btnPrint.Location = new System.Drawing.Point(13, 559);
            this.btnPrint.Name = "btnPrint";
            this.btnPrint.Size = new System.Drawing.Size(82, 23);
            this.btnPrint.TabIndex = 3;
            this.btnPrint.Text = "Print";
            this.btnPrint.Click += new System.EventHandler(this.btnPrint_Click);
            // 
            // rgFilter
            // 
            this.rgFilter.EditValue = ((byte)(0));
            this.rgFilter.Location = new System.Drawing.Point(105, 73);
            this.rgFilter.Name = "rgFilter";
            this.rgFilter.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.RadioGroupItem[] {
            new DevExpress.XtraEditors.Controls.RadioGroupItem(((byte)(0)), "All Events"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(((byte)(4)), "PR/OE"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(((byte)(5)), "Financial"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(((byte)(5)), "LAW"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(((byte)(6)), "System")});
            this.rgFilter.Properties.LookAndFeel.UseDefaultLookAndFeel = false;
            this.rgFilter.Size = new System.Drawing.Size(606, 26);
            this.rgFilter.TabIndex = 4;
            this.rgFilter.SelectedIndexChanged += new System.EventHandler(this.rgFilter_SelectedIndexChanged);
            // 
            // labelControl4
            // 
            this.labelControl4.Appearance.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl4.Appearance.Options.UseFont = true;
            this.labelControl4.Location = new System.Drawing.Point(13, 76);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(72, 17);
            this.labelControl4.TabIndex = 5;
            this.labelControl4.Text = "Filter Events:";
            // 
            // labelControl6
            // 
            this.labelControl6.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl6.Appearance.ForeColor = System.Drawing.Color.Black;
            this.labelControl6.Appearance.Options.UseFont = true;
            this.labelControl6.Appearance.Options.UseForeColor = true;
            this.labelControl6.Location = new System.Drawing.Point(13, 106);
            this.labelControl6.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(102, 16);
            this.labelControl6.TabIndex = 8;
            this.labelControl6.Text = "Invoice Audit Log:";
            // 
            // labelControl8
            // 
            this.labelControl8.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl8.Appearance.ForeColor = System.Drawing.Color.Black;
            this.labelControl8.Appearance.Options.UseFont = true;
            this.labelControl8.Appearance.Options.UseForeColor = true;
            this.labelControl8.Location = new System.Drawing.Point(13, 382);
            this.labelControl8.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.labelControl8.Name = "labelControl8";
            this.labelControl8.Size = new System.Drawing.Size(139, 16);
            this.labelControl8.TabIndex = 7;
            this.labelControl8.Text = "Component Lab Test(s):";
            // 
            // gridTests
            // 
            this.gridTests.DataSource = this.componentLabTestsSliceBindingSource;
            this.gridTests.Location = new System.Drawing.Point(13, 405);
            this.gridTests.MainView = this.gvwTests;
            this.gridTests.Name = "gridTests";
            this.gridTests.Size = new System.Drawing.Size(1087, 148);
            this.gridTests.TabIndex = 6;
            this.gridTests.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvwTests});
            // 
            // componentLabTestsSliceBindingSource
            // 
            this.componentLabTestsSliceBindingSource.DataSource = typeof(LabMaestro.Controls.Win.ComponentLabTestsSlice);
            // 
            // gvwTests
            // 
            this.gvwTests.Appearance.EvenRow.BackColor = System.Drawing.Color.Lavender;
            this.gvwTests.Appearance.EvenRow.Options.UseBackColor = true;
            this.gvwTests.Appearance.FocusedRow.BackColor = System.Drawing.Color.DarkSlateGray;
            this.gvwTests.Appearance.FocusedRow.ForeColor = System.Drawing.Color.White;
            this.gvwTests.Appearance.FocusedRow.Options.UseBackColor = true;
            this.gvwTests.Appearance.FocusedRow.Options.UseForeColor = true;
            this.gvwTests.Appearance.SelectedRow.BackColor = System.Drawing.Color.DarkMagenta;
            this.gvwTests.Appearance.SelectedRow.ForeColor = System.Drawing.Color.White;
            this.gvwTests.Appearance.SelectedRow.Options.UseBackColor = true;
            this.gvwTests.Appearance.SelectedRow.Options.UseForeColor = true;
            this.gvwTests.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.gvwTests.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colTestName,
            this.colLabName,
            this.colWorkflowStage,
            this.colIsCancelled,
            this.colDateCreated,
            this.colLastModified,
            this.colResultsETA,
            this.colId,
            this.colLabTestId,
            this.colBundleId,
            this.colBundleIsCancelled,
            this.colBundleDateCreated,
            this.colBundleLastModified,
            this.colBundleWorkflowStage,
            this.colBundleResultType});
            this.gvwTests.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.None;
            this.gvwTests.GridControl = this.gridTests;
            this.gvwTests.Name = "gvwTests";
            this.gvwTests.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.gvwTests.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.gvwTests.OptionsBehavior.AllowFixedGroups = DevExpress.Utils.DefaultBoolean.False;
            this.gvwTests.OptionsBehavior.Editable = false;
            this.gvwTests.OptionsBehavior.ReadOnly = true;
            this.gvwTests.OptionsMenu.EnableColumnMenu = false;
            this.gvwTests.OptionsView.EnableAppearanceEvenRow = true;
            this.gvwTests.OptionsView.ShowDetailButtons = false;
            this.gvwTests.OptionsView.ShowFilterPanelMode = DevExpress.XtraGrid.Views.Base.ShowFilterPanelMode.Never;
            this.gvwTests.OptionsView.ShowGroupExpandCollapseButtons = false;
            this.gvwTests.OptionsView.ShowGroupPanel = false;
            this.gvwTests.OptionsView.ShowHorizontalLines = DevExpress.Utils.DefaultBoolean.True;
            this.gvwTests.OptionsView.ShowIndicator = false;
            this.gvwTests.OptionsView.ShowVerticalLines = DevExpress.Utils.DefaultBoolean.True;
            // 
            // colTestName
            // 
            this.colTestName.FieldName = "TestName";
            this.colTestName.Name = "colTestName";
            this.colTestName.OptionsColumn.AllowEdit = false;
            this.colTestName.OptionsColumn.AllowFocus = false;
            this.colTestName.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colTestName.OptionsColumn.AllowIncrementalSearch = false;
            this.colTestName.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colTestName.OptionsColumn.AllowShowHide = false;
            this.colTestName.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colTestName.OptionsColumn.ReadOnly = true;
            this.colTestName.OptionsColumn.ShowInCustomizationForm = false;
            this.colTestName.OptionsColumn.ShowInExpressionEditor = false;
            this.colTestName.OptionsColumn.TabStop = false;
            this.colTestName.OptionsFilter.AllowAutoFilter = false;
            this.colTestName.OptionsFilter.AllowFilter = false;
            this.colTestName.OptionsFilter.AllowFilterModeChanging = DevExpress.Utils.DefaultBoolean.False;
            this.colTestName.OptionsFilter.FilterBySortField = DevExpress.Utils.DefaultBoolean.False;
            this.colTestName.Visible = true;
            this.colTestName.VisibleIndex = 0;
            this.colTestName.Width = 90;
            // 
            // colLabName
            // 
            this.colLabName.Caption = "Lab";
            this.colLabName.FieldName = "LabName";
            this.colLabName.Name = "colLabName";
            this.colLabName.OptionsColumn.AllowEdit = false;
            this.colLabName.OptionsColumn.AllowFocus = false;
            this.colLabName.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colLabName.OptionsColumn.AllowIncrementalSearch = false;
            this.colLabName.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colLabName.OptionsColumn.AllowShowHide = false;
            this.colLabName.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colLabName.OptionsColumn.ReadOnly = true;
            this.colLabName.OptionsColumn.ShowInCustomizationForm = false;
            this.colLabName.OptionsColumn.ShowInExpressionEditor = false;
            this.colLabName.OptionsColumn.TabStop = false;
            this.colLabName.OptionsFilter.AllowAutoFilter = false;
            this.colLabName.OptionsFilter.AllowFilter = false;
            this.colLabName.OptionsFilter.AllowFilterModeChanging = DevExpress.Utils.DefaultBoolean.False;
            this.colLabName.OptionsFilter.FilterBySortField = DevExpress.Utils.DefaultBoolean.False;
            this.colLabName.Visible = true;
            this.colLabName.VisibleIndex = 1;
            this.colLabName.Width = 69;
            // 
            // colWorkflowStage
            // 
            this.colWorkflowStage.Caption = "LAW Stage";
            this.colWorkflowStage.FieldName = "TestWorkflowStage";
            this.colWorkflowStage.Name = "colWorkflowStage";
            this.colWorkflowStage.OptionsColumn.AllowEdit = false;
            this.colWorkflowStage.OptionsColumn.AllowFocus = false;
            this.colWorkflowStage.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colWorkflowStage.OptionsColumn.AllowIncrementalSearch = false;
            this.colWorkflowStage.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colWorkflowStage.OptionsColumn.AllowShowHide = false;
            this.colWorkflowStage.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colWorkflowStage.OptionsColumn.ReadOnly = true;
            this.colWorkflowStage.OptionsColumn.ShowInCustomizationForm = false;
            this.colWorkflowStage.OptionsColumn.ShowInExpressionEditor = false;
            this.colWorkflowStage.OptionsColumn.TabStop = false;
            this.colWorkflowStage.OptionsFilter.AllowAutoFilter = false;
            this.colWorkflowStage.OptionsFilter.AllowFilter = false;
            this.colWorkflowStage.OptionsFilter.AllowFilterModeChanging = DevExpress.Utils.DefaultBoolean.False;
            this.colWorkflowStage.OptionsFilter.FilterBySortField = DevExpress.Utils.DefaultBoolean.False;
            this.colWorkflowStage.Visible = true;
            this.colWorkflowStage.VisibleIndex = 2;
            this.colWorkflowStage.Width = 69;
            // 
            // colIsCancelled
            // 
            this.colIsCancelled.Caption = "X?";
            this.colIsCancelled.FieldName = "IsCancelled";
            this.colIsCancelled.Name = "colIsCancelled";
            this.colIsCancelled.OptionsColumn.AllowEdit = false;
            this.colIsCancelled.OptionsColumn.AllowFocus = false;
            this.colIsCancelled.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colIsCancelled.OptionsColumn.AllowIncrementalSearch = false;
            this.colIsCancelled.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colIsCancelled.OptionsColumn.AllowShowHide = false;
            this.colIsCancelled.OptionsColumn.AllowSize = false;
            this.colIsCancelled.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colIsCancelled.OptionsColumn.FixedWidth = true;
            this.colIsCancelled.OptionsColumn.ReadOnly = true;
            this.colIsCancelled.OptionsColumn.ShowInCustomizationForm = false;
            this.colIsCancelled.OptionsColumn.ShowInExpressionEditor = false;
            this.colIsCancelled.OptionsColumn.TabStop = false;
            this.colIsCancelled.OptionsFilter.AllowAutoFilter = false;
            this.colIsCancelled.OptionsFilter.AllowFilter = false;
            this.colIsCancelled.OptionsFilter.AllowFilterModeChanging = DevExpress.Utils.DefaultBoolean.False;
            this.colIsCancelled.OptionsFilter.FilterBySortField = DevExpress.Utils.DefaultBoolean.False;
            this.colIsCancelled.ToolTip = "Test Is Canceled?";
            this.colIsCancelled.Visible = true;
            this.colIsCancelled.VisibleIndex = 3;
            this.colIsCancelled.Width = 22;
            // 
            // colDateCreated
            // 
            this.colDateCreated.Caption = "Ordered On";
            this.colDateCreated.FieldName = "DateCreated";
            this.colDateCreated.Name = "colDateCreated";
            this.colDateCreated.OptionsColumn.AllowEdit = false;
            this.colDateCreated.OptionsColumn.AllowFocus = false;
            this.colDateCreated.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colDateCreated.OptionsColumn.AllowIncrementalSearch = false;
            this.colDateCreated.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colDateCreated.OptionsColumn.AllowShowHide = false;
            this.colDateCreated.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colDateCreated.OptionsColumn.ReadOnly = true;
            this.colDateCreated.OptionsColumn.ShowInCustomizationForm = false;
            this.colDateCreated.OptionsColumn.ShowInExpressionEditor = false;
            this.colDateCreated.OptionsColumn.TabStop = false;
            this.colDateCreated.OptionsFilter.AllowAutoFilter = false;
            this.colDateCreated.OptionsFilter.AllowFilter = false;
            this.colDateCreated.OptionsFilter.AllowFilterModeChanging = DevExpress.Utils.DefaultBoolean.False;
            this.colDateCreated.OptionsFilter.FilterBySortField = DevExpress.Utils.DefaultBoolean.False;
            this.colDateCreated.Visible = true;
            this.colDateCreated.VisibleIndex = 4;
            this.colDateCreated.Width = 86;
            // 
            // colLastModified
            // 
            this.colLastModified.Caption = "Last Updated";
            this.colLastModified.FieldName = "LastModified";
            this.colLastModified.Name = "colLastModified";
            this.colLastModified.OptionsColumn.AllowEdit = false;
            this.colLastModified.OptionsColumn.AllowFocus = false;
            this.colLastModified.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colLastModified.OptionsColumn.AllowIncrementalSearch = false;
            this.colLastModified.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colLastModified.OptionsColumn.AllowShowHide = false;
            this.colLastModified.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colLastModified.OptionsColumn.ReadOnly = true;
            this.colLastModified.OptionsColumn.ShowInCustomizationForm = false;
            this.colLastModified.OptionsColumn.ShowInExpressionEditor = false;
            this.colLastModified.OptionsColumn.TabStop = false;
            this.colLastModified.OptionsFilter.AllowAutoFilter = false;
            this.colLastModified.OptionsFilter.AllowFilter = false;
            this.colLastModified.OptionsFilter.AllowFilterModeChanging = DevExpress.Utils.DefaultBoolean.False;
            this.colLastModified.OptionsFilter.FilterBySortField = DevExpress.Utils.DefaultBoolean.False;
            this.colLastModified.Visible = true;
            this.colLastModified.VisibleIndex = 5;
            this.colLastModified.Width = 86;
            // 
            // colResultsETA
            // 
            this.colResultsETA.Caption = "Dispatch At";
            this.colResultsETA.FieldName = "ResultsETA";
            this.colResultsETA.Name = "colResultsETA";
            this.colResultsETA.OptionsColumn.AllowEdit = false;
            this.colResultsETA.OptionsColumn.AllowFocus = false;
            this.colResultsETA.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colResultsETA.OptionsColumn.AllowIncrementalSearch = false;
            this.colResultsETA.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colResultsETA.OptionsColumn.AllowShowHide = false;
            this.colResultsETA.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colResultsETA.OptionsColumn.ReadOnly = true;
            this.colResultsETA.OptionsColumn.ShowInCustomizationForm = false;
            this.colResultsETA.OptionsColumn.ShowInExpressionEditor = false;
            this.colResultsETA.OptionsColumn.TabStop = false;
            this.colResultsETA.OptionsFilter.AllowAutoFilter = false;
            this.colResultsETA.OptionsFilter.AllowFilter = false;
            this.colResultsETA.OptionsFilter.AllowFilterModeChanging = DevExpress.Utils.DefaultBoolean.False;
            this.colResultsETA.OptionsFilter.FilterBySortField = DevExpress.Utils.DefaultBoolean.False;
            this.colResultsETA.Visible = true;
            this.colResultsETA.VisibleIndex = 6;
            this.colResultsETA.Width = 86;
            // 
            // colId
            // 
            this.colId.Caption = "OT";
            this.colId.FieldName = "Id";
            this.colId.Name = "colId";
            this.colId.OptionsColumn.AllowEdit = false;
            this.colId.OptionsColumn.AllowFocus = false;
            this.colId.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colId.OptionsColumn.AllowIncrementalSearch = false;
            this.colId.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colId.OptionsColumn.AllowShowHide = false;
            this.colId.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colId.OptionsColumn.FixedWidth = true;
            this.colId.OptionsColumn.ReadOnly = true;
            this.colId.OptionsColumn.ShowInCustomizationForm = false;
            this.colId.OptionsColumn.ShowInExpressionEditor = false;
            this.colId.OptionsColumn.TabStop = false;
            this.colId.OptionsFilter.AllowAutoFilter = false;
            this.colId.OptionsFilter.AllowFilter = false;
            this.colId.OptionsFilter.AllowFilterModeChanging = DevExpress.Utils.DefaultBoolean.False;
            this.colId.OptionsFilter.FilterBySortField = DevExpress.Utils.DefaultBoolean.False;
            this.colId.Visible = true;
            this.colId.VisibleIndex = 7;
            this.colId.Width = 30;
            // 
            // colLabTestId
            // 
            this.colLabTestId.Caption = "LT";
            this.colLabTestId.FieldName = "LabTestId";
            this.colLabTestId.Name = "colLabTestId";
            this.colLabTestId.OptionsColumn.AllowEdit = false;
            this.colLabTestId.OptionsColumn.AllowFocus = false;
            this.colLabTestId.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colLabTestId.OptionsColumn.AllowIncrementalSearch = false;
            this.colLabTestId.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colLabTestId.OptionsColumn.AllowShowHide = false;
            this.colLabTestId.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colLabTestId.OptionsColumn.FixedWidth = true;
            this.colLabTestId.OptionsColumn.ReadOnly = true;
            this.colLabTestId.OptionsColumn.ShowInCustomizationForm = false;
            this.colLabTestId.OptionsColumn.ShowInExpressionEditor = false;
            this.colLabTestId.OptionsColumn.TabStop = false;
            this.colLabTestId.OptionsFilter.AllowAutoFilter = false;
            this.colLabTestId.OptionsFilter.AllowFilter = false;
            this.colLabTestId.OptionsFilter.AllowFilterModeChanging = DevExpress.Utils.DefaultBoolean.False;
            this.colLabTestId.OptionsFilter.FilterBySortField = DevExpress.Utils.DefaultBoolean.False;
            this.colLabTestId.Visible = true;
            this.colLabTestId.VisibleIndex = 8;
            this.colLabTestId.Width = 30;
            // 
            // colBundleId
            // 
            this.colBundleId.Caption = "RB";
            this.colBundleId.FieldName = "BundleId";
            this.colBundleId.Name = "colBundleId";
            this.colBundleId.OptionsColumn.AllowEdit = false;
            this.colBundleId.OptionsColumn.AllowFocus = false;
            this.colBundleId.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleId.OptionsColumn.AllowIncrementalSearch = false;
            this.colBundleId.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleId.OptionsColumn.AllowShowHide = false;
            this.colBundleId.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleId.OptionsColumn.ReadOnly = true;
            this.colBundleId.OptionsColumn.ShowInCustomizationForm = false;
            this.colBundleId.OptionsColumn.ShowInExpressionEditor = false;
            this.colBundleId.OptionsColumn.TabStop = false;
            this.colBundleId.OptionsFilter.AllowAutoFilter = false;
            this.colBundleId.OptionsFilter.AllowFilter = false;
            this.colBundleId.OptionsFilter.AllowFilterModeChanging = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleId.OptionsFilter.FilterBySortField = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleId.Visible = true;
            this.colBundleId.VisibleIndex = 9;
            this.colBundleId.Width = 50;
            // 
            // colBundleIsCancelled
            // 
            this.colBundleIsCancelled.Caption = "X?";
            this.colBundleIsCancelled.FieldName = "BundleIsCancelled";
            this.colBundleIsCancelled.Name = "colBundleIsCancelled";
            this.colBundleIsCancelled.OptionsColumn.AllowEdit = false;
            this.colBundleIsCancelled.OptionsColumn.AllowFocus = false;
            this.colBundleIsCancelled.OptionsColumn.AllowIncrementalSearch = false;
            this.colBundleIsCancelled.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleIsCancelled.OptionsColumn.AllowShowHide = false;
            this.colBundleIsCancelled.OptionsColumn.AllowSize = false;
            this.colBundleIsCancelled.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleIsCancelled.OptionsColumn.FixedWidth = true;
            this.colBundleIsCancelled.OptionsColumn.ReadOnly = true;
            this.colBundleIsCancelled.OptionsColumn.ShowInCustomizationForm = false;
            this.colBundleIsCancelled.OptionsColumn.ShowInExpressionEditor = false;
            this.colBundleIsCancelled.OptionsColumn.TabStop = false;
            this.colBundleIsCancelled.OptionsFilter.AllowAutoFilter = false;
            this.colBundleIsCancelled.OptionsFilter.AllowFilter = false;
            this.colBundleIsCancelled.OptionsFilter.AllowFilterModeChanging = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleIsCancelled.OptionsFilter.FilterBySortField = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleIsCancelled.ToolTip = "Bundle Is Canceled?";
            this.colBundleIsCancelled.Visible = true;
            this.colBundleIsCancelled.VisibleIndex = 10;
            this.colBundleIsCancelled.Width = 22;
            // 
            // colBundleDateCreated
            // 
            this.colBundleDateCreated.Caption = "Bundle Created";
            this.colBundleDateCreated.FieldName = "BundleDateCreated";
            this.colBundleDateCreated.Name = "colBundleDateCreated";
            this.colBundleDateCreated.OptionsColumn.AllowEdit = false;
            this.colBundleDateCreated.OptionsColumn.AllowFocus = false;
            this.colBundleDateCreated.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleDateCreated.OptionsColumn.AllowIncrementalSearch = false;
            this.colBundleDateCreated.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleDateCreated.OptionsColumn.AllowShowHide = false;
            this.colBundleDateCreated.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleDateCreated.OptionsColumn.ReadOnly = true;
            this.colBundleDateCreated.OptionsColumn.ShowInCustomizationForm = false;
            this.colBundleDateCreated.OptionsColumn.ShowInExpressionEditor = false;
            this.colBundleDateCreated.OptionsColumn.TabStop = false;
            this.colBundleDateCreated.OptionsFilter.AllowAutoFilter = false;
            this.colBundleDateCreated.OptionsFilter.AllowFilter = false;
            this.colBundleDateCreated.OptionsFilter.AllowFilterModeChanging = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleDateCreated.OptionsFilter.FilterBySortField = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleDateCreated.Visible = true;
            this.colBundleDateCreated.VisibleIndex = 11;
            this.colBundleDateCreated.Width = 93;
            // 
            // colBundleLastModified
            // 
            this.colBundleLastModified.Caption = "Bun. Last Upd.";
            this.colBundleLastModified.FieldName = "BundleLastModified";
            this.colBundleLastModified.Name = "colBundleLastModified";
            this.colBundleLastModified.OptionsColumn.AllowEdit = false;
            this.colBundleLastModified.OptionsColumn.AllowFocus = false;
            this.colBundleLastModified.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleLastModified.OptionsColumn.AllowIncrementalSearch = false;
            this.colBundleLastModified.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleLastModified.OptionsColumn.AllowShowHide = false;
            this.colBundleLastModified.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleLastModified.OptionsColumn.ReadOnly = true;
            this.colBundleLastModified.OptionsColumn.ShowInCustomizationForm = false;
            this.colBundleLastModified.OptionsColumn.ShowInExpressionEditor = false;
            this.colBundleLastModified.OptionsColumn.TabStop = false;
            this.colBundleLastModified.OptionsFilter.AllowAutoFilter = false;
            this.colBundleLastModified.OptionsFilter.AllowFilter = false;
            this.colBundleLastModified.OptionsFilter.AllowFilterModeChanging = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleLastModified.OptionsFilter.FilterBySortField = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleLastModified.Visible = true;
            this.colBundleLastModified.VisibleIndex = 12;
            this.colBundleLastModified.Width = 93;
            // 
            // colBundleWorkflowStage
            // 
            this.colBundleWorkflowStage.Caption = "Bundle Stage";
            this.colBundleWorkflowStage.FieldName = "BundleWorkflowStage";
            this.colBundleWorkflowStage.Name = "colBundleWorkflowStage";
            this.colBundleWorkflowStage.OptionsColumn.AllowEdit = false;
            this.colBundleWorkflowStage.OptionsColumn.AllowFocus = false;
            this.colBundleWorkflowStage.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleWorkflowStage.OptionsColumn.AllowIncrementalSearch = false;
            this.colBundleWorkflowStage.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleWorkflowStage.OptionsColumn.AllowShowHide = false;
            this.colBundleWorkflowStage.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleWorkflowStage.OptionsColumn.ReadOnly = true;
            this.colBundleWorkflowStage.OptionsColumn.ShowInCustomizationForm = false;
            this.colBundleWorkflowStage.OptionsColumn.ShowInExpressionEditor = false;
            this.colBundleWorkflowStage.OptionsColumn.TabStop = false;
            this.colBundleWorkflowStage.OptionsFilter.AllowAutoFilter = false;
            this.colBundleWorkflowStage.OptionsFilter.AllowFilter = false;
            this.colBundleWorkflowStage.OptionsFilter.AllowFilterModeChanging = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleWorkflowStage.OptionsFilter.FilterBySortField = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleWorkflowStage.Visible = true;
            this.colBundleWorkflowStage.VisibleIndex = 13;
            this.colBundleWorkflowStage.Width = 108;
            // 
            // colBundleResultType
            // 
            this.colBundleResultType.Caption = "Bundle Type";
            this.colBundleResultType.FieldName = "BundleResultType";
            this.colBundleResultType.Name = "colBundleResultType";
            this.colBundleResultType.OptionsColumn.AllowEdit = false;
            this.colBundleResultType.OptionsColumn.AllowFocus = false;
            this.colBundleResultType.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleResultType.OptionsColumn.AllowIncrementalSearch = false;
            this.colBundleResultType.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleResultType.OptionsColumn.AllowShowHide = false;
            this.colBundleResultType.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleResultType.OptionsColumn.ReadOnly = true;
            this.colBundleResultType.OptionsColumn.ShowInCustomizationForm = false;
            this.colBundleResultType.OptionsColumn.ShowInExpressionEditor = false;
            this.colBundleResultType.OptionsColumn.TabStop = false;
            this.colBundleResultType.OptionsFilter.AllowAutoFilter = false;
            this.colBundleResultType.OptionsFilter.AllowFilter = false;
            this.colBundleResultType.OptionsFilter.AllowFilterModeChanging = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleResultType.OptionsFilter.FilterBySortField = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleResultType.Visible = true;
            this.colBundleResultType.VisibleIndex = 14;
            this.colBundleResultType.Width = 87;
            // 
            // AuditTrailViewerDialog
            // 
            this.Appearance.Options.UseFont = true;
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.ClientSize = new System.Drawing.Size(1115, 595);
            this.Controls.Add(this.labelControl6);
            this.Controls.Add(this.labelControl8);
            this.Controls.Add(this.gridTests);
            this.Controls.Add(this.labelControl4);
            this.Controls.Add(this.rgFilter);
            this.Controls.Add(this.btnPrint);
            this.Controls.Add(this.btnClose);
            this.Controls.Add(this.gridControl);
            this.Controls.Add(this.panelControl1);
            this.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.KeyPreview = true;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "AuditTrailViewerDialog";
            this.SizeGripStyle = System.Windows.Forms.SizeGripStyle.Hide;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "AuditTrailViewerDialog";
            this.Load += new System.EventHandler(this.AuditTrailViewerDialog_Load);
            this.KeyUp += new System.Windows.Forms.KeyEventHandler(this.AuditTrailViewerDialog_KeyUp);
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).EndInit();
            this.panelControl1.ResumeLayout(false);
            this.panelControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.auditEventSliceBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rgFilter.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridTests)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.componentLabTestsSliceBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvwTests)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.PanelControl panelControl1;
        private DevExpress.XtraEditors.LabelControl lblBookingDate;
        private DevExpress.XtraEditors.LabelControl lblOrderId;
        private DevExpress.XtraEditors.LabelControl lblInvoiceId;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.LabelControl lblPatientName;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraGrid.GridControl gridControl;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView;
        private System.Windows.Forms.BindingSource auditEventSliceBindingSource;
        private DevExpress.XtraGrid.Columns.GridColumn colEventDate;
        private DevExpress.XtraGrid.Columns.GridColumn colEventTime;
        private DevExpress.XtraGrid.Columns.GridColumn colResultBundle;
        private DevExpress.XtraGrid.Columns.GridColumn colPerformingUser;
        private DevExpress.XtraGrid.Columns.GridColumn colWorkflowStageDescr;
        private DevExpress.XtraGrid.Columns.GridColumn colEventCategoryDescr;
        private DevExpress.XtraGrid.Columns.GridColumn colEventTypeDescr;
        private DevExpress.XtraGrid.Columns.GridColumn colUserIpAddress;
        private DevExpress.XtraGrid.Columns.GridColumn colRemarks;
        private DevExpress.XtraEditors.SimpleButton btnClose;
        private DevExpress.XtraEditors.SimpleButton btnPrint;
        private DevExpress.XtraEditors.RadioGroup rgFilter;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.LabelControl lblReferrerName;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.LabelControl labelControl8;
        private DevExpress.XtraGrid.GridControl gridTests;
        private DevExpress.XtraGrid.Views.Grid.GridView gvwTests;
        private DevExpress.XtraGrid.Columns.GridColumn colTestName;
        private DevExpress.XtraGrid.Columns.GridColumn colLabName;
        private DevExpress.XtraGrid.Columns.GridColumn colWorkflowStage;
        private DevExpress.XtraGrid.Columns.GridColumn colIsCancelled;
        private DevExpress.XtraGrid.Columns.GridColumn colDateCreated;
        private DevExpress.XtraGrid.Columns.GridColumn colLastModified;
        private DevExpress.XtraGrid.Columns.GridColumn colResultsETA;
        private DevExpress.XtraGrid.Columns.GridColumn colId;
        private DevExpress.XtraGrid.Columns.GridColumn colLabTestId;
        private DevExpress.XtraGrid.Columns.GridColumn colBundleId;
        private DevExpress.XtraGrid.Columns.GridColumn colBundleIsCancelled;
        private DevExpress.XtraGrid.Columns.GridColumn colBundleDateCreated;
        private DevExpress.XtraGrid.Columns.GridColumn colBundleLastModified;
        private DevExpress.XtraGrid.Columns.GridColumn colBundleWorkflowStage;
        private DevExpress.XtraGrid.Columns.GridColumn colBundleResultType;
        private System.Windows.Forms.BindingSource componentLabTestsSliceBindingSource;
    }
}