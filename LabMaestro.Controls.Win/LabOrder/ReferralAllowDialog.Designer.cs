﻿namespace LabMaestro.Controls.Win
{
    partial class ReferralAllowDialog
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.btnAllowReferral = new DevExpress.XtraEditors.SimpleButton();
            this.btnDisallowReferral = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.SuspendLayout();
            // 
            // btnAllowReferral
            // 
            this.btnAllowReferral.Appearance.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnAllowReferral.Appearance.ForeColor = System.Drawing.Color.Indigo;
            this.btnAllowReferral.Appearance.Options.UseFont = true;
            this.btnAllowReferral.Appearance.Options.UseForeColor = true;
            this.btnAllowReferral.ImageOptions.Image = global::LabMaestro.Controls.Win.ResourceIcons.female_24;
            this.btnAllowReferral.Location = new System.Drawing.Point(12, 76);
            this.btnAllowReferral.Name = "btnAllowReferral";
            this.btnAllowReferral.Size = new System.Drawing.Size(149, 36);
            this.btnAllowReferral.TabIndex = 1;
            this.btnAllowReferral.Text = "&Walk-in";
            this.btnAllowReferral.ToolTip = "Patient was admitted without referral";
            this.btnAllowReferral.Click += new System.EventHandler(this.btnAllowReferral_Click);
            this.btnAllowReferral.KeyDown += new System.Windows.Forms.KeyEventHandler(this.btnDisallowReferral_KeyDown);
            // 
            // btnDisallowReferral
            // 
            this.btnDisallowReferral.Appearance.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnDisallowReferral.Appearance.ForeColor = System.Drawing.Color.DarkBlue;
            this.btnDisallowReferral.Appearance.Options.UseFont = true;
            this.btnDisallowReferral.Appearance.Options.UseForeColor = true;
            this.btnDisallowReferral.ImageOptions.Image = global::LabMaestro.Controls.Win.ResourceIcons.doctor_happy24;
            this.btnDisallowReferral.Location = new System.Drawing.Point(12, 34);
            this.btnDisallowReferral.Name = "btnDisallowReferral";
            this.btnDisallowReferral.Size = new System.Drawing.Size(149, 36);
            this.btnDisallowReferral.TabIndex = 0;
            this.btnDisallowReferral.Text = "&Referral";
            this.btnDisallowReferral.ToolTip = "Patient was referred by a physician";
            this.btnDisallowReferral.Click += new System.EventHandler(this.btnDisallowReferral_Click);
            this.btnDisallowReferral.KeyDown += new System.Windows.Forms.KeyEventHandler(this.btnDisallowReferral_KeyDown);
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Location = new System.Drawing.Point(12, 12);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(140, 16);
            this.labelControl1.TabIndex = 2;
            this.labelControl1.Text = "Select referral eligibility:";
            // 
            // ReferralAllowDialog
            // 
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 16F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(176, 124);
            this.ControlBox = false;
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.btnDisallowReferral);
            this.Controls.Add(this.btnAllowReferral);
            this.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow;
            this.IconOptions.ShowIcon = false;
            this.KeyPreview = true;
            this.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "ReferralAllowDialog";
            this.ShowInTaskbar = false;
            this.SizeGripStyle = System.Windows.Forms.SizeGripStyle.Hide;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.KeyUp += new System.Windows.Forms.KeyEventHandler(this.ReferralAllowDialog_KeyUp);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.SimpleButton btnAllowReferral;
        private DevExpress.XtraEditors.SimpleButton btnDisallowReferral;
        private DevExpress.XtraEditors.LabelControl labelControl1;
    }
}