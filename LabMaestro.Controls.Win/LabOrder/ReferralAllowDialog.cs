﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the organization nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ReferralAllowDialog.cs 933 2013-09-07 12:15:37Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Windows.Forms;
using DevExpress.XtraEditors;

namespace LabMaestro.Controls.Win;

public partial class ReferralAllowDialog : XtraForm
{
    public ReferralAllowDialog()
    {
        InitializeComponent();
        WinUtils.ApplyTheme(this);
    }

    public bool DisallowReferral { get; set; }

    private void ReferralAllowDialog_KeyUp(object sender, KeyEventArgs e)
    {
        switch (e.KeyCode)
        {
            case Keys.D:
                e.Handled = true;
                DisallowReferral = true;
                DialogResult = DialogResult.OK;
                break;
            case Keys.R:
                e.Handled = true;
                DisallowReferral = false;
                DialogResult = DialogResult.OK;
                break;
            case Keys.Escape:
                e.Handled = true;
                DialogResult = DialogResult.Cancel;
                break;
        }
    }

    private void btnAllowReferral_Click(object sender, EventArgs e)
    {
        DisallowReferral = true;
        DialogResult = DialogResult.OK;
    }

    private void btnDisallowReferral_Click(object sender, EventArgs e)
    {
        DisallowReferral = false;
        DialogResult = DialogResult.OK;
    }

    public static bool Execute(IWin32Window parent)
    {
        using (var frm = new ReferralAllowDialog())
        {
            frm.ShowDialog(parent);
            return frm.DisallowReferral;
        }
    }

    public static bool Execute(IWin32Window parent, out bool cancel)
    {
        using var frm = new ReferralAllowDialog();
        cancel = frm.ShowDialog(parent) == DialogResult.Cancel;
        return frm.DisallowReferral;
    }

    private void btnDisallowReferral_KeyDown(object sender, KeyEventArgs e)
    {
        if (e.KeyCode == Keys.Enter)
        {
            e.SuppressKeyPress = true;
            e.Handled = true;
            (sender as SimpleButton)!.PerformClick();
        }
    }
}