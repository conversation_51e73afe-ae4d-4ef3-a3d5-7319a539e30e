﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: TransactionDialog.cs 847 2013-07-22 07:17:00Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using CuttingEdge.Conditions;
using DevExpress.XtraBars;
using DevExpress.XtraEditors;
using LabMaestro.BusinessLogic;
using LabMaestro.Controls.Win.Utils;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Shared;
using Xceed.Grid;

namespace LabMaestro.Controls.Win;

public partial class TransactionDialog : XtraForm
{
    private DataCell _oldCell;

    public TransactionDialog()
    {
        //AuthHelper.GuardAccess(true, UserRoles.GetTransactionRoles());
        InitializeComponent();
        WinUtils.ApplyTheme(this);
        WinUtils.SetFormTitle(this, "Transactions");

        TransactionsHistory = [];
        TransactionsHelper.CreateGridColumns(grdTransactionHistory);
        AllowedTransactionOperations = TransactionOperationTypes.None;
        SelectedTransactionType = InvoiceTransactionType.Payment;
        TransactionAmount = 0m;
        EnablePreviewPopupMenu = true;
        ShowPreviewDialog = false;
        IsSubcontractOrder = false;

        toggleNonCashPayment(false);
    }

    public bool IsNewInvoice { get; set; }
    public bool IsSubcontractOrder { get; set; }

    public TransactionPerformHook OnTransactionPerform { get; set; }

    public bool AllowManualInvoicePrinting { get; set; }

    public bool EnablePreviewPopupMenu { get; set; }

    public string PaymentReference { get; set; }
    public string PaymentSource { get; set; }
    public decimal NonCashAmount { get; private set; }
    public PaymentMethod SelectedPaymentMethod { get; private set; } = PaymentMethod.Cash;

    public TransactionOperationTypes AllowedTransactionOperations { get; set; }

    public LabOrderContextInfo CurrentLabOrder { get; set; }

    public InvoiceInfo InvoiceInfo { get; set; }

    public bool ShowPreviewDialog { get; set; }

    public InvoiceTransactionType SelectedTransactionType { get; private set; }

    public string TransactionRemarks { get; set; }

    public List<InvoiceTransactionDetailedExSlice> TransactionsHistory { get; }

    public decimal TransactionAmount { get; private set; }

    private void toggleTransactionRelatedControls(bool enable)
    {
        rgTransactionType.Enabled = enable;
        rgTransactionType.SelectedIndex = -1;

        btnPerform.Enabled = enable;

        txtTransactionAmount.Text = string.Empty;
        txtTransactionAmount.Enabled = enable;

        txtUserRemarks.Text = string.Empty;
        txtUserRemarks.Enabled = enable;
    }

    private bool transactionIsAllowed(TransactionOperationTypes tx) => (AllowedTransactionOperations & tx) == tx;

    private void updateTransactionRelatedControls()
    {
        toggleTransactionRelatedControls(false);
        if (!hasActiveLabOrder()) return;

        if ((AllowedTransactionOperations & TransactionOperationTypes.None) == TransactionOperationTypes.None)
        {
            rgTransactionType.Properties.Items[(int)TransactionItem.Payment].Enabled = false;
            rgTransactionType.Properties.Items[(int)TransactionItem.Refund].Enabled = false;
            rgTransactionType.Properties.Items[(int)TransactionItem.Discount].Enabled = false;
            rgTransactionType.Properties.Items[(int)TransactionItem.DiscountRebate].Enabled = false;
            return;
        }

        toggleTransactionRelatedControls(true);
        rgTransactionType.Properties.Items[(int)TransactionItem.Payment].Enabled =
            transactionIsAllowed(TransactionOperationTypes.Payment);
        rgTransactionType.Properties.Items[(int)TransactionItem.Discount].Enabled =
            transactionIsAllowed(TransactionOperationTypes.Discount);
        rgTransactionType.Properties.Items[(int)TransactionItem.DiscountRebate].Enabled =
            transactionIsAllowed(TransactionOperationTypes.DiscountRebate);
        rgTransactionType.Properties.Items[(int)TransactionItem.Refund].Enabled =
            transactionIsAllowed(TransactionOperationTypes.Refund);
    }

    public void UpdateControls()
    {
        updateTransactionRelatedControls();
        updateOrderDetails();
        updateTransactionsGrid();
        updateBalances();
        updateOtherControls();
    }

    private void updateOtherControls()
    {
        btnPrintInvoice.Enabled = AllowManualInvoicePrinting && hasActiveLabOrder();
        miPreviewOnly.Enabled = EnablePreviewPopupMenu;
    }

    private void updateTransactionType(int value)
    {
        if (!hasActiveLabOrder()) return;
        SelectedTransactionType = value switch
        {
            (int)TransactionItem.Payment => InvoiceTransactionType.Payment,
            (int)TransactionItem.Refund => InvoiceTransactionType.Refund,
            (int)TransactionItem.Discount => InvoiceTransactionType.CashDiscount,
            _ => SelectedTransactionType
        };
    }

    private string getReferrerName() =>
        CurrentLabOrder.IsReferrerUnknown || CurrentLabOrder.ReferrerId == null
            ? CurrentLabOrder.ReferrerCustomName
            : FaultHandler.Shield(() => ReferrersRepository.GetReferrerFullName((int)CurrentLabOrder.ReferrerId));

    private string getUserName() =>
        string.IsNullOrEmpty(CurrentLabOrder.OrderingUserName)
            ? CurrentUserContext.UserDisplayName
            : CurrentLabOrder.OrderingUserName;

    private void updateOrderDetails()
    {
        Condition.Requires(CurrentLabOrder).IsNotNull();
        Condition.Requires(InvoiceInfo).IsNotNull();

        lblInvoiceNum.Text = CurrentLabOrder.InvoiceId > 0 ? CurrentLabOrder.InvoiceId.ToString() : "(pending)";
        lblOrderId.Text = !string.IsNullOrEmpty(CurrentLabOrder.OrderId) ? CurrentLabOrder.OrderId : "(pending)";
        lblPatientName.Text = $"{CurrentLabOrder.FirstName} {CurrentLabOrder.LastName}".Trim();
        lblReferrerName.Text = getReferrerName();
        lblOrderingUserName.Text = getUserName();
        lblOrderTime.Text = CurrentLabOrder.OrderDateTime == DateTime.MinValue
            ? "Just now"
            : SharedUtilities.DateTimeToString(CurrentLabOrder.OrderDateTime);
        lblNetPayable.Text = SharedUtilities.MoneyToString(InvoiceInfo.NetPayable);
        lblAmountPaid.Text = SharedUtilities.MoneyToString(InvoiceInfo.PaidAmount);
        lblCurrentDue.Text = SharedUtilities.MoneyToString(InvoiceInfo.DueAmount);
    }

    private void updateTransactionsGrid() => TransactionsHelper.UpdateTransactionsGrid(grdTransactionHistory,
        TransactionsHistory, hasActiveLabOrder(), showToolTip);

    private void showToolTip(object sender, EventArgs e)
    {
        var cell = (DataCell)sender;
        if (_oldCell == cell) return;

        _oldCell = cell;
        var content = _oldCell.Value;
        toolTip.SetToolTip(grdTransactionHistory, content != null ? content.ToString() : string.Empty);
    }

    private decimal getEnteredAmount()
    {
        var enteredAmount = 0m;
        var text = txtTransactionAmount.Text.Trim();

        if (string.IsNullOrEmpty(text)) return enteredAmount;

        if (!decimal.TryParse(txtTransactionAmount.Text, out enteredAmount))
            throw new Exception("Invalid amount entered!");

        return enteredAmount;
    }

    private bool validateUserInput()
    {
        if (!hasActiveLabOrder())
            throw new Exception("Lab order was not selected or has been cancelled");

        if (rgTransactionType.SelectedIndex == -1)
            throw new Exception("Please select the appropriate Transaction Type!");

        updateTransactionType(rgTransactionType.SelectedIndex);

        if (chkNonCashPayment.Checked)
        {
            if (rgPaymentMethod.SelectedIndex == -1)
                throw new Exception("Please select the appropriate Payment Method.");

            updatePaymentMethod(rgPaymentMethod.SelectedIndex);
            if (SelectedPaymentMethod != PaymentMethod.Cash)
            {
                PaymentSource = txtPaymentSource.Text.Trim();
                PaymentReference = txtPaymentRef.Text.Trim();
            }
        }


        if (string.IsNullOrEmpty(txtTransactionAmount.Text.Trim()))
        {
            if (IsSubcontractOrder)
                txtTransactionAmount.Text = "0";
            else
                throw new Exception("Transaction Amount cannot be empty!\r\nPlease enter the desired amount.");
        }

        var txAmount = getEnteredAmount();
        // TODO: more stringent validation
        Condition.Requires(txAmount, @"Transaction Amount")
            //.IsGreaterThan(0m)
            .IsLessOrEqual(InvoiceInfo.NetPayable);

        // allow zero payment for subcontracted orders only. for direct orders, confirm
        if (!IsSubcontractOrder
            && txAmount == 0
            && !MessageDlg.Confirm("Payment amount is Zero.\r\nDo you wish to continue without payment?"))
            return false;

        if (SelectedPaymentMethod == PaymentMethod.Cash)
            TransactionAmount = txAmount;
        else
            NonCashAmount = txAmount;

        TransactionRemarks = txtUserRemarks.Text.Trim();

        switch (SelectedTransactionType)
        {
            case InvoiceTransactionType.Payment:
                var totalPaid = InvoiceInfo.PaidAmount + txAmount;
                if (totalPaid > InvoiceInfo.NetPayable) throw new Exception("Transaction Amount + Paid > NetPayable");
                break;
            case InvoiceTransactionType.Refund:
                if (txAmount > InvoiceInfo.PaidAmount) throw new Exception("Transaction Amount > Paid");
                break;
            case InvoiceTransactionType.CashDiscount:
                if (txAmount > InvoiceInfo.DueAmount) throw new Exception("Transaction Amount > Due");
                break;
        }

        return true;
    }

    private void updatePaymentMethod(int value)
    {
        SelectedPaymentMethod = value switch
        {
            1 => PaymentMethod.Card,
            2 => PaymentMethod.Mobile,
            3 => PaymentMethod.Cheque,
            4 => PaymentMethod.BankTransfer,
            _ => PaymentMethod.Cash
        };
    }

    private void btnCancel_Click(object sender, EventArgs e) => DialogResult = DialogResult.Cancel;

    private void btnPerform_Click(object sender, EventArgs e) => performTransactionAndCloseDialogOnSuccess();

    private void performTransactionAndCloseDialogOnSuccess()
    {
        bool success;
        var cursor = Cursor;
        try
        {
            btnPerform.Enabled = false;
            Cursor = Cursors.WaitCursor;
            success = performTransaction();
        }
        finally
        {
            btnPerform.Enabled = true;
            Cursor = cursor;
        }

        if (success)
        {
            // TODO: Print directly - Invoice, Req foils
            ShowPreviewDialog = false;
            DialogResult = DialogResult.OK;
        }
    }

    private bool performTransaction()
    {
        try
        {
            if (!validateUserInput()) return false;
        }
        catch (Exception exc)
        {
            MessageDlg.Error(exc.Message);
            return false;
        }

        try
        {
            if (OnTransactionPerform != null)
            {
                var flag = IsNewInvoice ? TransactionFlag.InitialOperation : TransactionFlag.None;
                return OnTransactionPerform(
                    SelectedTransactionType,
                    flag,
                    TransactionAmount,
                    TransactionRemarks,
                    NonCashAmount,
                    SelectedPaymentMethod,
                    PaymentSource,
                    PaymentReference
                );
            }
        }
        catch (Exception exc)
        {
            FaultHandler.HandleNonFatalException(exc);
            return false;
        }

        return false;
    }

    private void txtTransactionAmount_EditValueChanged(object sender, EventArgs e) => updateBalances();

    private bool hasActiveLabOrder() => CurrentLabOrder is { IsCancelled: false } && InvoiceInfo != null;

    private void updateBalances()
    {
        if (!hasActiveLabOrder())
        {
            txtTransactionAmount.Text = string.Empty;
            return;
        }

        decimal enteredAmount;
        try
        {
            enteredAmount = getEnteredAmount();
        }
        catch (Exception e)
        {
            MessageDlg.Error(e.Message);
            return;
        }

        var balanceAmount = SelectedTransactionType switch
        {
            InvoiceTransactionType.Payment => InvoiceInfo.DueAmount - enteredAmount,
            InvoiceTransactionType.Refund => InvoiceInfo.DueAmount + enteredAmount,
            InvoiceTransactionType.CashDiscount => InvoiceInfo.DueAmount - enteredAmount,
            _ => 0m
        };

        lblBalanceDue.ForeColor = balanceAmount < 0 ? Color.Crimson : Color.Green;
        lblBalanceDue.Text = SharedUtilities.MoneyToString(balanceAmount);
    }

    private void rgTransactionType_SelectedIndexChanged(object sender, EventArgs e)
    {
        updateTransactionType(rgTransactionType.SelectedIndex);
        updateBalances();
    }

    private void miPreviewOnly_ItemClick(object sender, ItemClickEventArgs e)
    {
        if (performTransaction())
        {
            ShowPreviewDialog = true;
            DialogResult = DialogResult.OK;
        }
    }

    private void TransactionDialog_Load(object sender, EventArgs e)
    {
        if (AllowedTransactionOperations == TransactionOperationTypes.Payment)
        {
            // if only Payment is allowed
            rgTransactionType.SelectedIndex = 0;
            txtTransactionAmount.Select();
            txtTransactionAmount.Focus();
            return;
        }

        rgTransactionType.Select();
        rgTransactionType.Focus();
    }

    private void txtTransactionAmount_KeyUp(object sender, KeyEventArgs e)
    {
        if (e.KeyCode == Keys.Enter) performTransactionAndCloseDialogOnSuccess();
    }

    private void chkNonCashPayment_CheckedChanged(object sender, EventArgs e) =>
        toggleNonCashPayment(chkNonCashPayment.Checked);

    private void toggleNonCashPayment(bool enabled)
    {
        rgPaymentMethod.Enabled = enabled;
        txtPaymentSource.Enabled = enabled;
        txtPaymentRef.Enabled = enabled;
    }
}