﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using LabMaestro.BusinessLogic.CRM;
using LabMaestro.Domain;
using LabMaestro.Shared;

namespace LabMaestro.Controls.Win.LabOrder;

public partial class PatientSelectForm : XtraForm
{
    private readonly string _phone;
    public PatientDemographicInfo SelectedPatient { get; private set; }
    private List<PatientDemographicInfo> _patients = [];

    public PatientSelectForm(string phone, List<PatientDemographicInfo> patients)
    {
        _phone = phone;
        _patients = patients;
        InitializeComponent();
        WinUtils.ApplyTheme(this);
        Text = $"Past encounters for {phone}";
    }

    void selectPatient()
    {
        if (gridView.SelectedRowsCount == 0)
            return;

        var rows = gridView.GetSelectedRows();
        if (rows.Any() && gridView.GetRow(rows.First()) is PatientDemographicInfo info)
        {
            SelectedPatient = info;
            DialogResult = DialogResult.OK;
        }
    }

    private void btnSelectPatient_Click(object sender, EventArgs e) => selectPatient();

    private void PatientSelectForm_Load(object sender, EventArgs e)
    {
        gridControl.DataSource = _patients;
        gridControl.Focus();
    }

    private void gridView_KeyUp(object sender, KeyEventArgs e)
    {
        switch (e.KeyCode)
        {
            case Keys.Enter:
                e.Handled = true;
                selectPatient();
                break;
            case Keys.Escape:
                e.Handled = true;
                DialogResult = DialogResult.Cancel;
                break;
        }
    }

    public static PatientDemographicInfo? SelectPatient(string phone)
    {
        phone = SharedUtilities.GetPhoneLocalPart(phone);
        if (string.IsNullOrEmpty(phone)) return null;

        var patients = PatientLabOrdersRepository
            .FindAllOrdersByPhone(phone)
            .Select(PatientDemographicInfo.From)
            .Distinct()
            .ToList();

        if (!patients.Any()) return null;
        if (patients.Count == 1) return patients[0];

        using var frm = new PatientSelectForm(phone, patients);
        return frm.ShowDialog() != DialogResult.OK ? null : frm.SelectedPatient;
    }
}