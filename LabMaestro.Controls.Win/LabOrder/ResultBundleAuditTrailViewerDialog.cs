﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ResultBundleAuditTrailViewerDialog.cs 1404 2014-09-20 14:11:20Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using DevExpress.Utils;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraEditors.Repository;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.Controls.Win
{
    public partial class ResultBundleAuditTrailViewerDialog : XtraForm
    {
        private readonly List<ComponentLabTestsSlice> _componentTests;
        private readonly List<ResultBundleAuditTrailInfo> _trailInfos;

        public ResultBundleAuditTrailViewerDialog()
        {
            _trailInfos = new List<ResultBundleAuditTrailInfo>();
            _componentTests = new List<ComponentLabTestsSlice>();

            InitializeComponent();
            WinUtils.ApplyTheme(this);
            WinUtils.SetFormTitle(this, "Audit Trail");
        }

        public string LabOrderDetails { get; set; }
        public string BundleTitle { get; set; }

        private void ResultBundleAuditTrailViewerDialog_Load(object sender, EventArgs e)
        {
        }

        private void loadAuditTrailsIntoGrid(long bundleId)
        {
            gridControl.BeginUpdate();
            try
            {
                var trails = AuditTrailRepository.GetAuditRecordsForResultBundle(bundleId,
                                                                                 AuditEventCategory.Workflow);

                _trailInfos.AddRange(trails.OrderBy(x => x.Id).Select(x => new ResultBundleAuditTrailInfo
                                                                           {
                                                                               EventDate =
                                                                                   SharedUtilities.DateToString(
                                                                                                                x
                                                                                                                    .EventTime),
                                                                               EventTime =
                                                                                   SharedUtilities.TimeToStringShort(
                                                                                                                     x
                                                                                                                         .EventTime),
                                                                               EventType =
                                                                                   EnumUtils.EnumDescription(
                                                                                                             (
                                                                                                             AuditEventType
                                                                                                             )
                                                                                                             x.EventType),
                                                                               UserName = x.PerformingUserName,
                                                                               IpAddress =
                                                                                   SharedUtilities.IpAddressToString(
                                                                                                                     x
                                                                                                                         .UserIpAddress),
                                                                               Note = x.Note
                                                                           }));

                resultBundleAuditTrailInfoBindingSource.DataSource = _trailInfos;
            }
            finally
            {
                gridControl.EndUpdate();
            }
        }

        private void updateControls()
        {
            var cancelImages = new ImageCollection();
            cancelImages.AddImage(Resources.cancel2_16);
            var cancelImageCombo = gridTests.RepositoryItems.Add("ImageComboBoxEdit") as RepositoryItemImageComboBox;
            cancelImageCombo.SmallImages = cancelImages;
            cancelImageCombo.Items.Add(new ImageComboBoxItem(true, 0));
            cancelImageCombo.GlyphAlignment = HorzAlignment.Center;
            gvwTests.Columns[@"IsCancelled"].ColumnEdit = cancelImageCombo;
            gvwTests.Columns[@"BundleIsCancelled"].ColumnEdit = cancelImageCombo;
        }

        private void loadComponentLabTestsIntoGrid(long bundleId)
        {
            gridTests.BeginUpdate();

            try
            {
                var slices =
                    FaultHandler.Shield(() => OrderedTestsRepository.GetAllOrderedTestDetailsInResultBundle(bundleId));
                if (slices != null && slices.Count > 0)
                {
                    foreach (var slice in slices)
                    {
                        _componentTests.Add(ComponentLabTestsSlice.AssembleFrom(slice));
                    }
                }

                componentLabTestsSliceBindingSource.DataSource = _componentTests;
            }
            finally
            {
                gridTests.EndUpdate();
            }
        }

        public static void ExecuteDialog(IWin32Window parent, long bundleId, string orderDetails, string bundleTitle)
        {
            using (var frm = new ResultBundleAuditTrailViewerDialog())
            {
                frm.LabOrderDetails = orderDetails;
                frm.BundleTitle = bundleTitle;
                frm.updateControls();
                frm.loadComponentLabTestsIntoGrid(bundleId);
                frm.loadAuditTrailsIntoGrid(bundleId);
                frm.ShowDialog(parent);
            }
        }

        private void btnExit_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        private void ResultBundleAuditTrailViewerDialog_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape) DialogResult = DialogResult.OK;
        }

        private void btnPrint_Click(object sender, EventArgs e)
        {
            var dto = new DynamicTabulatedReportPrintDto
                      {
                          PrintedOn = SharedUtilities.DateTimeToString24(AppSysRepository.GetServerTime()),
                          PrintedBy = CurrentUserContext.UserDisplayName,
                          ReportHeader = "Result Bundle Audit Trail",
                          Label1 = "Invoice",
                          Value1 = LabOrderDetails,
                          Label2 = "Bundle",
                          Value2 = BundleTitle
                      };
            dto.SetHeadings("Date", "Time", "Action", "Performed By", "Note", "IP");
            dto.Rows.AddRange(_trailInfos.Select(x => x.ToPrintDto()));
            PrintHelper.PrintDynamicTabulatedReport(dto, true);
        }
    }

    public sealed class ResultBundleAuditTrailInfo
    {
        public string EventDate { get; set; }
        public string EventTime { get; set; }
        public string EventType { get; set; }
        public string IpAddress { get; set; }
        public string UserName { get; set; }
        public string Note { get; set; }

        public DynamicTabulatedReportRow ToPrintDto()
        {
            return new DynamicTabulatedReportRow
                   {
                       Field1 = EventDate,
                       Field2 = EventTime,
                       Field3 = EventType,
                       Field4 = UserName,
                       Field5 = Note,
                       Field6 = IpAddress
                   };
        }
    }
}