﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: BulkDispatchDialog.cs 1283 2014-05-22 06:21:17Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Data;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Views.Grid;
using LabMaestro.BusinessLogic;

namespace LabMaestro.Controls.Win
{
    public partial class BulkDispatchDialog : XtraForm
    {
        private readonly ResultBundlesBulkDispatchManager _manager;

        public BulkDispatchDialog()
        {
            InitializeComponent();
            _manager = new ResultBundlesBulkDispatchManager();
            WinUtils.ApplyTheme(this);
            WinUtils.SetFormTitle(this, "Bulk Dispatch");
        }

        private void BulkDispatchDialog_Load(object sender, EventArgs e)
        {
            focusSearchTextbox();
        }

        private void chkConfirmDispatch_CheckedChanged(object sender, EventArgs e)
        {
            btnBulkDispatch.Enabled = chkConfirmDispatch.Checked;
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            performResultBundleSearch();
        }

        private void performResultBundleSearch()
        {
            var txt = txtBundleSearch.Text.Trim();
            if (string.IsNullOrEmpty(txt)) return;

            long bundleId;
            if (!long.TryParse(txt, out bundleId))
            {
                MessageDlg.Error("Invalid report id specified!");
                focusSearchTextbox(true);
                return;
            }

            if (bundleId <= 0)
            {
                MessageDlg.Error("Invalid report id specified!");
                focusSearchTextbox(true);
                return;
            }

            try
            {
                if (!_manager.AlreadyAdded(bundleId))
                    _manager.SearchAndAddEligibleBundle(bundleId);
            }
            catch (Exception e)
            {
                MessageDlg.Error(e.Message);
                focusSearchTextbox(true);
                return;
            }

            grdUpdateDataRows();
            focusSearchTextbox(true);
        }

        private void grdUpdateDataRows()
        {
            grdBundles.BeginUpdate();
            try
            {
                grdBundles.DataSource = _manager.EligibleBundles;
            }
            finally
            {
                grdBundles.EndUpdate();
            }

            chkConfirmDispatch.Checked = false;
            chkConfirmDispatch.Enabled = _manager.EligibleBundlesCount > 0;
            btnBulkDispatch.Enabled = false;
        }

        private void txtBundleSearch_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                e.Handled = true;
                performResultBundleSearch();
            }
        }

        private void BulkDispatchDialog_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.F5)
            {
                e.Handled = true;
                focusSearchTextbox();
            }
        }

        private void focusSearchTextbox(bool clearContents = false)
        {
            txtBundleSearch.Focus();
            if (!clearContents)
            {
                txtBundleSearch.SelectAll();
            }
            else
            {
                txtBundleSearch.Text = string.Empty;
            }
        }

        private void btnBulkDispatch_Click(object sender, EventArgs e)
        {
            if (_manager.EligibleBundlesCount == 0)
                return;

            WaitFormControl.WaitOperation(this,
                () => _manager.PerformBulkDispatch(),
                "Dispatching reports...");

            if (!string.IsNullOrEmpty(_manager.ErrorMessage))
            {
                MessageDlg.Error(_manager.ErrorMessage);
            }
            else
            {
                MessageDlg.Info(string.Format("{0} reports have been dispatched successfully.",
                    _manager.DispatchedBundlesCount));
            }

            _manager.Reset();
            grdUpdateDataRows();
            btnBulkDispatch.Enabled = false;
            focusSearchTextbox(true);
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            if (_manager.EligibleBundlesCount > 0 && MessageDlg.Confirm("Really clear all items?"))
            {
                _manager.Reset();
                grdUpdateDataRows();
                focusSearchTextbox(true);
            }
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            try
            {
                var rowIds = ((GridView) grdBundles.MainView).GetSelectedRows();
                var row = (DataRowView) (grdBundles.MainView.GetRow(rowIds[0]));
                var bundleId = (long) row[0];
                if (bundleId > 0)
                {
                    if (MessageDlg.Confirm("Really delete the selected report?"))
                    {
                        //_manager.RemoveBundle();
                        grdUpdateDataRows();
                        focusSearchTextbox(true);
                    }
                }
            }
            catch
            {
            }
        }
    }
}