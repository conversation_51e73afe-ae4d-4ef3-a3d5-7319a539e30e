﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: DateTimeDialog.cs 1071 2013-11-03 07:57:52Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Windows.Forms;
using DevExpress.XtraEditors.Controls;
using LabMaestro.Infrastructure.Client;

namespace LabMaestro.Controls.Win;

public partial class DateTimeDialog : Form, IExecutableDialog
{
    public DateTimeDialog()
    {
        SelectedDateTime = DateTime.MinValue;
        InitializeComponent();

        for (var i = 1; i <= 12; i++)
            rgHours.Properties.Items.Add(createItem(i));
        chkSingleDate.Checked = SpecifyOnce;
    }

    public DateTime SelectedDateTime { get; set; }

    public bool ExecuteDialog(IWin32Window parent)
    {
        UpdateControls();
        return ShowDialog(parent) == DialogResult.OK;
    }

    public void UpdateControls()
    {
    }

    private void setTimeControls(DateTime time)
    {
        if (time.Hour <= 12)
        {
            rgHours.SelectedIndex = time.Hour - 1;
            rgAMPM.SelectedIndex = 0;
        }
        else
        {
            rgHours.SelectedIndex = time.Hour - 13;
            rgAMPM.SelectedIndex = 1;
        }

        //rgMinutes.SelectedIndex = (time.Minute < 30) ? 0 : 1;
    }

    private RadioGroupItem createItem(int val) => new(val, val.ToString());

    private void DeliveryTimeDialog_Load(object sender, EventArgs e)
    {
        if (SelectedDateTime != DateTime.MinValue)
        {
            dateEdit.DateTime = SelectedDateTime;
            setTimeControls(SelectedDateTime);
        }
        else
        {
            dateEdit.DateTime = DateTime.Today.AddDays(1);
            setTimeControls(DateTime.Now);
        }

        dateEdit.Focus();
    }

    private int getHour()
    {
        var iAdd = rgAMPM.SelectedIndex == 0 ? 1 : 13;
        return rgHours.SelectedIndex + iAdd;

        //return rgHours.SelectedIndex + 1 + (rgAMPM.SelectedIndex * 12);
    }

    private void btnOk_Click(object sender, EventArgs e)
    {
        var yr = dateEdit.DateTime.Year;
        var mo = dateEdit.DateTime.Month;
        var dy = dateEdit.DateTime.Day;

        var hr = getHour();

        if (hr > 23)
            hr = 23;

        SelectedDateTime = new DateTime(yr, mo, dy, hr, 0, 0);
        if (SelectedDateTime < DateTime.Now)
        {
            if (
                !MessageDlg.Confirm(
                    "The selected date/time is earlier than this instant.\r\nDo you still want to apply the time?"))
            {
                return;
            }
        }

        SpecifyOnce = chkSingleDate.Checked;
        DialogResult = DialogResult.OK;
    }

    public bool SpecifyOnce { get; set; }

    private void btnClear_Click(object sender, EventArgs e) => DialogResult = DialogResult.Abort;

    private void DateTimeDialog_KeyUp(object sender, KeyEventArgs e)
    {
        switch (e.KeyCode)
        {
            case Keys.Escape:
                e.Handled = true;
                DialogResult = DialogResult.Cancel;
                break;
            case Keys.Enter:
                e.Handled = true;
                btnOk_Click(null, null);
                break;
        }
    }
}