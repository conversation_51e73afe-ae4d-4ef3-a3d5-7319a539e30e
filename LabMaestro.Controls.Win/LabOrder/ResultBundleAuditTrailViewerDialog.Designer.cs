﻿namespace LabMaestro.Controls.Win
{
    partial class ResultBundleAuditTrailViewerDialog
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.btnExit = new DevExpress.XtraEditors.SimpleButton();
            this.gridControl = new DevExpress.XtraGrid.GridControl();
            this.resultBundleAuditTrailInfoBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.gvwTrail = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colEventDate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colEventTime = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colEventType = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colUserName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colNote = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colIpAddress = new DevExpress.XtraGrid.Columns.GridColumn();
            this.btnPrint = new DevExpress.XtraEditors.SimpleButton();
            this.gridTests = new DevExpress.XtraGrid.GridControl();
            this.componentLabTestsSliceBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.gvwTests = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colTestName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colLabName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colWorkflowStage = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colIsCancelled = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colDateCreated = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colLastModified = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colResultsETA = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colLabTestId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colBundleId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colBundleIsCancelled = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colBundleDateCreated = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colBundleLastModified = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colBundleWorkflowStage = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colBundleResultType = new DevExpress.XtraGrid.Columns.GridColumn();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.resultBundleAuditTrailInfoBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvwTrail)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridTests)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.componentLabTestsSliceBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvwTests)).BeginInit();
            this.SuspendLayout();
            // 
            // btnExit
            // 
            this.btnExit.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnExit.Appearance.Options.UseFont = true;
            this.btnExit.Image = global::LabMaestro.Controls.Win.Resources.exit_16;
            this.btnExit.Location = new System.Drawing.Point(919, 425);
            this.btnExit.Name = "btnExit";
            this.btnExit.Size = new System.Drawing.Size(71, 23);
            this.btnExit.TabIndex = 0;
            this.btnExit.Text = "Close";
            this.btnExit.Click += new System.EventHandler(this.btnExit_Click);
            // 
            // gridControl
            // 
            this.gridControl.DataSource = this.resultBundleAuditTrailInfoBindingSource;
            this.gridControl.Location = new System.Drawing.Point(12, 36);
            this.gridControl.MainView = this.gvwTrail;
            this.gridControl.Name = "gridControl";
            this.gridControl.Size = new System.Drawing.Size(978, 205);
            this.gridControl.TabIndex = 1;
            this.gridControl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvwTrail});
            // 
            // gvwTrail
            // 
            this.gvwTrail.Appearance.EvenRow.BackColor = System.Drawing.Color.Lavender;
            this.gvwTrail.Appearance.EvenRow.Options.UseBackColor = true;
            this.gvwTrail.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.gvwTrail.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colEventDate,
            this.colEventTime,
            this.colEventType,
            this.colUserName,
            this.colNote,
            this.colIpAddress});
            this.gvwTrail.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.None;
            this.gvwTrail.GridControl = this.gridControl;
            this.gvwTrail.Name = "gvwTrail";
            this.gvwTrail.OptionsView.EnableAppearanceEvenRow = true;
            this.gvwTrail.OptionsView.ShowDetailButtons = false;
            this.gvwTrail.OptionsView.ShowGroupExpandCollapseButtons = false;
            this.gvwTrail.OptionsView.ShowGroupPanel = false;
            this.gvwTrail.OptionsView.ShowIndicator = false;
            // 
            // colEventDate
            // 
            this.colEventDate.Caption = "Date";
            this.colEventDate.FieldName = "EventDate";
            this.colEventDate.Name = "colEventDate";
            this.colEventDate.OptionsColumn.AllowEdit = false;
            this.colEventDate.OptionsColumn.AllowFocus = false;
            this.colEventDate.OptionsColumn.AllowShowHide = false;
            this.colEventDate.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colEventDate.OptionsColumn.ReadOnly = true;
            this.colEventDate.Visible = true;
            this.colEventDate.VisibleIndex = 0;
            // 
            // colEventTime
            // 
            this.colEventTime.Caption = "Time";
            this.colEventTime.FieldName = "EventTime";
            this.colEventTime.Name = "colEventTime";
            this.colEventTime.OptionsColumn.AllowEdit = false;
            this.colEventTime.OptionsColumn.AllowFocus = false;
            this.colEventTime.OptionsColumn.AllowShowHide = false;
            this.colEventTime.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colEventTime.OptionsColumn.ReadOnly = true;
            this.colEventTime.Visible = true;
            this.colEventTime.VisibleIndex = 1;
            // 
            // colEventType
            // 
            this.colEventType.Caption = "Event";
            this.colEventType.FieldName = "EventType";
            this.colEventType.Name = "colEventType";
            this.colEventType.OptionsColumn.AllowEdit = false;
            this.colEventType.OptionsColumn.AllowFocus = false;
            this.colEventType.OptionsColumn.AllowShowHide = false;
            this.colEventType.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colEventType.OptionsColumn.ReadOnly = true;
            this.colEventType.Visible = true;
            this.colEventType.VisibleIndex = 2;
            // 
            // colUserName
            // 
            this.colUserName.Caption = "Staff";
            this.colUserName.FieldName = "UserName";
            this.colUserName.Name = "colUserName";
            this.colUserName.OptionsColumn.AllowEdit = false;
            this.colUserName.OptionsColumn.AllowFocus = false;
            this.colUserName.OptionsColumn.AllowShowHide = false;
            this.colUserName.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colUserName.OptionsColumn.ReadOnly = true;
            this.colUserName.Visible = true;
            this.colUserName.VisibleIndex = 3;
            // 
            // colNote
            // 
            this.colNote.Caption = "Remarks";
            this.colNote.FieldName = "Note";
            this.colNote.Name = "colNote";
            this.colNote.OptionsColumn.AllowEdit = false;
            this.colNote.OptionsColumn.AllowFocus = false;
            this.colNote.OptionsColumn.AllowShowHide = false;
            this.colNote.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colNote.OptionsColumn.ReadOnly = true;
            this.colNote.Visible = true;
            this.colNote.VisibleIndex = 4;
            // 
            // colIpAddress
            // 
            this.colIpAddress.Caption = "IP";
            this.colIpAddress.FieldName = "IpAddress";
            this.colIpAddress.Name = "colIpAddress";
            this.colIpAddress.OptionsColumn.AllowEdit = false;
            this.colIpAddress.OptionsColumn.AllowFocus = false;
            this.colIpAddress.OptionsColumn.AllowShowHide = false;
            this.colIpAddress.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colIpAddress.OptionsColumn.ReadOnly = true;
            this.colIpAddress.Visible = true;
            this.colIpAddress.VisibleIndex = 5;
            // 
            // btnPrint
            // 
            this.btnPrint.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnPrint.Appearance.Options.UseFont = true;
            this.btnPrint.Image = global::LabMaestro.Controls.Win.Resources.print2;
            this.btnPrint.Location = new System.Drawing.Point(12, 425);
            this.btnPrint.Name = "btnPrint";
            this.btnPrint.Size = new System.Drawing.Size(71, 23);
            this.btnPrint.TabIndex = 2;
            this.btnPrint.Text = "Print";
            this.btnPrint.Click += new System.EventHandler(this.btnPrint_Click);
            // 
            // gridTests
            // 
            this.gridTests.DataSource = this.componentLabTestsSliceBindingSource;
            this.gridTests.Location = new System.Drawing.Point(12, 271);
            this.gridTests.MainView = this.gvwTests;
            this.gridTests.Name = "gridTests";
            this.gridTests.Size = new System.Drawing.Size(978, 148);
            this.gridTests.TabIndex = 3;
            this.gridTests.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvwTests});
            // 
            // componentLabTestsSliceBindingSource
            // 
            this.componentLabTestsSliceBindingSource.DataSource = typeof(LabMaestro.Controls.Win.ComponentLabTestsSlice);
            // 
            // gvwTests
            // 
            this.gvwTests.Appearance.EvenRow.BackColor = System.Drawing.Color.AliceBlue;
            this.gvwTests.Appearance.EvenRow.Options.UseBackColor = true;
            this.gvwTests.Appearance.FixedLine.BackColor = System.Drawing.Color.SteelBlue;
            this.gvwTests.Appearance.FixedLine.Options.UseBackColor = true;
            this.gvwTests.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.gvwTests.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colTestName,
            this.colLabName,
            this.colWorkflowStage,
            this.colIsCancelled,
            this.colDateCreated,
            this.colLastModified,
            this.colResultsETA,
            this.colId,
            this.colLabTestId,
            this.colBundleId,
            this.colBundleIsCancelled,
            this.colBundleDateCreated,
            this.colBundleLastModified,
            this.colBundleWorkflowStage,
            this.colBundleResultType});
            this.gvwTests.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.None;
            this.gvwTests.GridControl = this.gridTests;
            this.gvwTests.Name = "gvwTests";
            this.gvwTests.OptionsBehavior.Editable = false;
            this.gvwTests.OptionsBehavior.ReadOnly = true;
            this.gvwTests.OptionsMenu.EnableColumnMenu = false;
            this.gvwTests.OptionsView.EnableAppearanceEvenRow = true;
            this.gvwTests.OptionsView.ShowDetailButtons = false;
            this.gvwTests.OptionsView.ShowFilterPanelMode = DevExpress.XtraGrid.Views.Base.ShowFilterPanelMode.Never;
            this.gvwTests.OptionsView.ShowGroupExpandCollapseButtons = false;
            this.gvwTests.OptionsView.ShowGroupPanel = false;
            this.gvwTests.OptionsView.ShowHorizontalLines = DevExpress.Utils.DefaultBoolean.True;
            this.gvwTests.OptionsView.ShowIndicator = false;
            this.gvwTests.OptionsView.ShowVerticalLines = DevExpress.Utils.DefaultBoolean.True;
            this.gvwTests.ShowButtonMode = DevExpress.XtraGrid.Views.Base.ShowButtonModeEnum.Default;
            // 
            // colTestName
            // 
            this.colTestName.FieldName = "TestName";
            this.colTestName.Name = "colTestName";
            this.colTestName.OptionsColumn.AllowEdit = false;
            this.colTestName.OptionsColumn.AllowFocus = false;
            this.colTestName.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colTestName.OptionsColumn.AllowIncrementalSearch = false;
            this.colTestName.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colTestName.OptionsColumn.AllowShowHide = false;
            this.colTestName.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colTestName.OptionsColumn.ReadOnly = true;
            this.colTestName.OptionsColumn.ShowInCustomizationForm = false;
            this.colTestName.OptionsColumn.ShowInExpressionEditor = false;
            this.colTestName.OptionsColumn.TabStop = false;
            this.colTestName.OptionsFilter.AllowAutoFilter = false;
            this.colTestName.OptionsFilter.AllowFilter = false;
            this.colTestName.OptionsFilter.AllowFilterModeChanging = DevExpress.Utils.DefaultBoolean.False;
            this.colTestName.OptionsFilter.FilterBySortField = DevExpress.Utils.DefaultBoolean.False;
            this.colTestName.Visible = true;
            this.colTestName.VisibleIndex = 0;
            this.colTestName.Width = 90;
            // 
            // colLabName
            // 
            this.colLabName.Caption = "Lab";
            this.colLabName.FieldName = "LabName";
            this.colLabName.Name = "colLabName";
            this.colLabName.OptionsColumn.AllowEdit = false;
            this.colLabName.OptionsColumn.AllowFocus = false;
            this.colLabName.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colLabName.OptionsColumn.AllowIncrementalSearch = false;
            this.colLabName.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colLabName.OptionsColumn.AllowShowHide = false;
            this.colLabName.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colLabName.OptionsColumn.ReadOnly = true;
            this.colLabName.OptionsColumn.ShowInCustomizationForm = false;
            this.colLabName.OptionsColumn.ShowInExpressionEditor = false;
            this.colLabName.OptionsColumn.TabStop = false;
            this.colLabName.OptionsFilter.AllowAutoFilter = false;
            this.colLabName.OptionsFilter.AllowFilter = false;
            this.colLabName.OptionsFilter.AllowFilterModeChanging = DevExpress.Utils.DefaultBoolean.False;
            this.colLabName.OptionsFilter.FilterBySortField = DevExpress.Utils.DefaultBoolean.False;
            this.colLabName.Visible = true;
            this.colLabName.VisibleIndex = 1;
            this.colLabName.Width = 69;
            // 
            // colWorkflowStage
            // 
            this.colWorkflowStage.Caption = "LAW Stage";
            this.colWorkflowStage.FieldName = "TestWorkflowStage";
            this.colWorkflowStage.Name = "colWorkflowStage";
            this.colWorkflowStage.OptionsColumn.AllowEdit = false;
            this.colWorkflowStage.OptionsColumn.AllowFocus = false;
            this.colWorkflowStage.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colWorkflowStage.OptionsColumn.AllowIncrementalSearch = false;
            this.colWorkflowStage.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colWorkflowStage.OptionsColumn.AllowShowHide = false;
            this.colWorkflowStage.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colWorkflowStage.OptionsColumn.ReadOnly = true;
            this.colWorkflowStage.OptionsColumn.ShowInCustomizationForm = false;
            this.colWorkflowStage.OptionsColumn.ShowInExpressionEditor = false;
            this.colWorkflowStage.OptionsColumn.TabStop = false;
            this.colWorkflowStage.OptionsFilter.AllowAutoFilter = false;
            this.colWorkflowStage.OptionsFilter.AllowFilter = false;
            this.colWorkflowStage.OptionsFilter.AllowFilterModeChanging = DevExpress.Utils.DefaultBoolean.False;
            this.colWorkflowStage.OptionsFilter.FilterBySortField = DevExpress.Utils.DefaultBoolean.False;
            this.colWorkflowStage.Visible = true;
            this.colWorkflowStage.VisibleIndex = 2;
            this.colWorkflowStage.Width = 69;
            // 
            // colIsCancelled
            // 
            this.colIsCancelled.Caption = "X?";
            this.colIsCancelled.FieldName = "IsCancelled";
            this.colIsCancelled.Name = "colIsCancelled";
            this.colIsCancelled.OptionsColumn.AllowEdit = false;
            this.colIsCancelled.OptionsColumn.AllowFocus = false;
            this.colIsCancelled.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colIsCancelled.OptionsColumn.AllowIncrementalSearch = false;
            this.colIsCancelled.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colIsCancelled.OptionsColumn.AllowShowHide = false;
            this.colIsCancelled.OptionsColumn.AllowSize = false;
            this.colIsCancelled.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colIsCancelled.OptionsColumn.FixedWidth = true;
            this.colIsCancelled.OptionsColumn.ReadOnly = true;
            this.colIsCancelled.OptionsColumn.ShowInCustomizationForm = false;
            this.colIsCancelled.OptionsColumn.ShowInExpressionEditor = false;
            this.colIsCancelled.OptionsColumn.TabStop = false;
            this.colIsCancelled.OptionsFilter.AllowAutoFilter = false;
            this.colIsCancelled.OptionsFilter.AllowFilter = false;
            this.colIsCancelled.OptionsFilter.AllowFilterModeChanging = DevExpress.Utils.DefaultBoolean.False;
            this.colIsCancelled.OptionsFilter.FilterBySortField = DevExpress.Utils.DefaultBoolean.False;
            this.colIsCancelled.ToolTip = "Test Is Canceled?";
            this.colIsCancelled.Visible = true;
            this.colIsCancelled.VisibleIndex = 3;
            this.colIsCancelled.Width = 22;
            // 
            // colDateCreated
            // 
            this.colDateCreated.Caption = "Ordered On";
            this.colDateCreated.FieldName = "DateCreated";
            this.colDateCreated.Name = "colDateCreated";
            this.colDateCreated.OptionsColumn.AllowEdit = false;
            this.colDateCreated.OptionsColumn.AllowFocus = false;
            this.colDateCreated.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colDateCreated.OptionsColumn.AllowIncrementalSearch = false;
            this.colDateCreated.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colDateCreated.OptionsColumn.AllowShowHide = false;
            this.colDateCreated.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colDateCreated.OptionsColumn.ReadOnly = true;
            this.colDateCreated.OptionsColumn.ShowInCustomizationForm = false;
            this.colDateCreated.OptionsColumn.ShowInExpressionEditor = false;
            this.colDateCreated.OptionsColumn.TabStop = false;
            this.colDateCreated.OptionsFilter.AllowAutoFilter = false;
            this.colDateCreated.OptionsFilter.AllowFilter = false;
            this.colDateCreated.OptionsFilter.AllowFilterModeChanging = DevExpress.Utils.DefaultBoolean.False;
            this.colDateCreated.OptionsFilter.FilterBySortField = DevExpress.Utils.DefaultBoolean.False;
            this.colDateCreated.Visible = true;
            this.colDateCreated.VisibleIndex = 4;
            this.colDateCreated.Width = 86;
            // 
            // colLastModified
            // 
            this.colLastModified.Caption = "Last Updated";
            this.colLastModified.FieldName = "LastModified";
            this.colLastModified.Name = "colLastModified";
            this.colLastModified.OptionsColumn.AllowEdit = false;
            this.colLastModified.OptionsColumn.AllowFocus = false;
            this.colLastModified.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colLastModified.OptionsColumn.AllowIncrementalSearch = false;
            this.colLastModified.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colLastModified.OptionsColumn.AllowShowHide = false;
            this.colLastModified.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colLastModified.OptionsColumn.ReadOnly = true;
            this.colLastModified.OptionsColumn.ShowInCustomizationForm = false;
            this.colLastModified.OptionsColumn.ShowInExpressionEditor = false;
            this.colLastModified.OptionsColumn.TabStop = false;
            this.colLastModified.OptionsFilter.AllowAutoFilter = false;
            this.colLastModified.OptionsFilter.AllowFilter = false;
            this.colLastModified.OptionsFilter.AllowFilterModeChanging = DevExpress.Utils.DefaultBoolean.False;
            this.colLastModified.OptionsFilter.FilterBySortField = DevExpress.Utils.DefaultBoolean.False;
            this.colLastModified.Visible = true;
            this.colLastModified.VisibleIndex = 5;
            this.colLastModified.Width = 86;
            // 
            // colResultsETA
            // 
            this.colResultsETA.Caption = "Dispatch At";
            this.colResultsETA.FieldName = "ResultsETA";
            this.colResultsETA.Name = "colResultsETA";
            this.colResultsETA.OptionsColumn.AllowEdit = false;
            this.colResultsETA.OptionsColumn.AllowFocus = false;
            this.colResultsETA.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colResultsETA.OptionsColumn.AllowIncrementalSearch = false;
            this.colResultsETA.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colResultsETA.OptionsColumn.AllowShowHide = false;
            this.colResultsETA.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colResultsETA.OptionsColumn.ReadOnly = true;
            this.colResultsETA.OptionsColumn.ShowInCustomizationForm = false;
            this.colResultsETA.OptionsColumn.ShowInExpressionEditor = false;
            this.colResultsETA.OptionsColumn.TabStop = false;
            this.colResultsETA.OptionsFilter.AllowAutoFilter = false;
            this.colResultsETA.OptionsFilter.AllowFilter = false;
            this.colResultsETA.OptionsFilter.AllowFilterModeChanging = DevExpress.Utils.DefaultBoolean.False;
            this.colResultsETA.OptionsFilter.FilterBySortField = DevExpress.Utils.DefaultBoolean.False;
            this.colResultsETA.Visible = true;
            this.colResultsETA.VisibleIndex = 6;
            this.colResultsETA.Width = 86;
            // 
            // colId
            // 
            this.colId.Caption = "OT";
            this.colId.FieldName = "Id";
            this.colId.Name = "colId";
            this.colId.OptionsColumn.AllowEdit = false;
            this.colId.OptionsColumn.AllowFocus = false;
            this.colId.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colId.OptionsColumn.AllowIncrementalSearch = false;
            this.colId.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colId.OptionsColumn.AllowShowHide = false;
            this.colId.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colId.OptionsColumn.FixedWidth = true;
            this.colId.OptionsColumn.ReadOnly = true;
            this.colId.OptionsColumn.ShowInCustomizationForm = false;
            this.colId.OptionsColumn.ShowInExpressionEditor = false;
            this.colId.OptionsColumn.TabStop = false;
            this.colId.OptionsFilter.AllowAutoFilter = false;
            this.colId.OptionsFilter.AllowFilter = false;
            this.colId.OptionsFilter.AllowFilterModeChanging = DevExpress.Utils.DefaultBoolean.False;
            this.colId.OptionsFilter.FilterBySortField = DevExpress.Utils.DefaultBoolean.False;
            this.colId.Visible = true;
            this.colId.VisibleIndex = 7;
            this.colId.Width = 30;
            // 
            // colLabTestId
            // 
            this.colLabTestId.Caption = "LT";
            this.colLabTestId.FieldName = "LabTestId";
            this.colLabTestId.Name = "colLabTestId";
            this.colLabTestId.OptionsColumn.AllowEdit = false;
            this.colLabTestId.OptionsColumn.AllowFocus = false;
            this.colLabTestId.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colLabTestId.OptionsColumn.AllowIncrementalSearch = false;
            this.colLabTestId.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colLabTestId.OptionsColumn.AllowShowHide = false;
            this.colLabTestId.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colLabTestId.OptionsColumn.FixedWidth = true;
            this.colLabTestId.OptionsColumn.ReadOnly = true;
            this.colLabTestId.OptionsColumn.ShowInCustomizationForm = false;
            this.colLabTestId.OptionsColumn.ShowInExpressionEditor = false;
            this.colLabTestId.OptionsColumn.TabStop = false;
            this.colLabTestId.OptionsFilter.AllowAutoFilter = false;
            this.colLabTestId.OptionsFilter.AllowFilter = false;
            this.colLabTestId.OptionsFilter.AllowFilterModeChanging = DevExpress.Utils.DefaultBoolean.False;
            this.colLabTestId.OptionsFilter.FilterBySortField = DevExpress.Utils.DefaultBoolean.False;
            this.colLabTestId.Visible = true;
            this.colLabTestId.VisibleIndex = 8;
            this.colLabTestId.Width = 30;
            // 
            // colBundleId
            // 
            this.colBundleId.Caption = "RB";
            this.colBundleId.FieldName = "BundleId";
            this.colBundleId.Name = "colBundleId";
            this.colBundleId.OptionsColumn.AllowEdit = false;
            this.colBundleId.OptionsColumn.AllowFocus = false;
            this.colBundleId.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleId.OptionsColumn.AllowIncrementalSearch = false;
            this.colBundleId.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleId.OptionsColumn.AllowShowHide = false;
            this.colBundleId.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleId.OptionsColumn.ReadOnly = true;
            this.colBundleId.OptionsColumn.ShowInCustomizationForm = false;
            this.colBundleId.OptionsColumn.ShowInExpressionEditor = false;
            this.colBundleId.OptionsColumn.TabStop = false;
            this.colBundleId.OptionsFilter.AllowAutoFilter = false;
            this.colBundleId.OptionsFilter.AllowFilter = false;
            this.colBundleId.OptionsFilter.AllowFilterModeChanging = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleId.OptionsFilter.FilterBySortField = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleId.Visible = true;
            this.colBundleId.VisibleIndex = 9;
            this.colBundleId.Width = 50;
            // 
            // colBundleIsCancelled
            // 
            this.colBundleIsCancelled.Caption = "X?";
            this.colBundleIsCancelled.FieldName = "BundleIsCancelled";
            this.colBundleIsCancelled.Name = "colBundleIsCancelled";
            this.colBundleIsCancelled.OptionsColumn.AllowEdit = false;
            this.colBundleIsCancelled.OptionsColumn.AllowFocus = false;
            this.colBundleIsCancelled.OptionsColumn.AllowIncrementalSearch = false;
            this.colBundleIsCancelled.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleIsCancelled.OptionsColumn.AllowShowHide = false;
            this.colBundleIsCancelled.OptionsColumn.AllowSize = false;
            this.colBundleIsCancelled.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleIsCancelled.OptionsColumn.FixedWidth = true;
            this.colBundleIsCancelled.OptionsColumn.ReadOnly = true;
            this.colBundleIsCancelled.OptionsColumn.ShowInCustomizationForm = false;
            this.colBundleIsCancelled.OptionsColumn.ShowInExpressionEditor = false;
            this.colBundleIsCancelled.OptionsColumn.TabStop = false;
            this.colBundleIsCancelled.OptionsFilter.AllowAutoFilter = false;
            this.colBundleIsCancelled.OptionsFilter.AllowFilter = false;
            this.colBundleIsCancelled.OptionsFilter.AllowFilterModeChanging = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleIsCancelled.OptionsFilter.FilterBySortField = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleIsCancelled.ToolTip = "Bundle Is Canceled?";
            this.colBundleIsCancelled.Visible = true;
            this.colBundleIsCancelled.VisibleIndex = 10;
            this.colBundleIsCancelled.Width = 22;
            // 
            // colBundleDateCreated
            // 
            this.colBundleDateCreated.Caption = "Bundle Created";
            this.colBundleDateCreated.FieldName = "BundleDateCreated";
            this.colBundleDateCreated.Name = "colBundleDateCreated";
            this.colBundleDateCreated.OptionsColumn.AllowEdit = false;
            this.colBundleDateCreated.OptionsColumn.AllowFocus = false;
            this.colBundleDateCreated.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleDateCreated.OptionsColumn.AllowIncrementalSearch = false;
            this.colBundleDateCreated.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleDateCreated.OptionsColumn.AllowShowHide = false;
            this.colBundleDateCreated.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleDateCreated.OptionsColumn.ReadOnly = true;
            this.colBundleDateCreated.OptionsColumn.ShowInCustomizationForm = false;
            this.colBundleDateCreated.OptionsColumn.ShowInExpressionEditor = false;
            this.colBundleDateCreated.OptionsColumn.TabStop = false;
            this.colBundleDateCreated.OptionsFilter.AllowAutoFilter = false;
            this.colBundleDateCreated.OptionsFilter.AllowFilter = false;
            this.colBundleDateCreated.OptionsFilter.AllowFilterModeChanging = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleDateCreated.OptionsFilter.FilterBySortField = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleDateCreated.Visible = true;
            this.colBundleDateCreated.VisibleIndex = 11;
            this.colBundleDateCreated.Width = 93;
            // 
            // colBundleLastModified
            // 
            this.colBundleLastModified.Caption = "Bun. Last Upd.";
            this.colBundleLastModified.FieldName = "BundleLastModified";
            this.colBundleLastModified.Name = "colBundleLastModified";
            this.colBundleLastModified.OptionsColumn.AllowEdit = false;
            this.colBundleLastModified.OptionsColumn.AllowFocus = false;
            this.colBundleLastModified.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleLastModified.OptionsColumn.AllowIncrementalSearch = false;
            this.colBundleLastModified.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleLastModified.OptionsColumn.AllowShowHide = false;
            this.colBundleLastModified.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleLastModified.OptionsColumn.ReadOnly = true;
            this.colBundleLastModified.OptionsColumn.ShowInCustomizationForm = false;
            this.colBundleLastModified.OptionsColumn.ShowInExpressionEditor = false;
            this.colBundleLastModified.OptionsColumn.TabStop = false;
            this.colBundleLastModified.OptionsFilter.AllowAutoFilter = false;
            this.colBundleLastModified.OptionsFilter.AllowFilter = false;
            this.colBundleLastModified.OptionsFilter.AllowFilterModeChanging = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleLastModified.OptionsFilter.FilterBySortField = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleLastModified.Visible = true;
            this.colBundleLastModified.VisibleIndex = 12;
            this.colBundleLastModified.Width = 93;
            // 
            // colBundleWorkflowStage
            // 
            this.colBundleWorkflowStage.Caption = "Bundle Stage";
            this.colBundleWorkflowStage.FieldName = "BundleWorkflowStage";
            this.colBundleWorkflowStage.Name = "colBundleWorkflowStage";
            this.colBundleWorkflowStage.OptionsColumn.AllowEdit = false;
            this.colBundleWorkflowStage.OptionsColumn.AllowFocus = false;
            this.colBundleWorkflowStage.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleWorkflowStage.OptionsColumn.AllowIncrementalSearch = false;
            this.colBundleWorkflowStage.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleWorkflowStage.OptionsColumn.AllowShowHide = false;
            this.colBundleWorkflowStage.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleWorkflowStage.OptionsColumn.ReadOnly = true;
            this.colBundleWorkflowStage.OptionsColumn.ShowInCustomizationForm = false;
            this.colBundleWorkflowStage.OptionsColumn.ShowInExpressionEditor = false;
            this.colBundleWorkflowStage.OptionsColumn.TabStop = false;
            this.colBundleWorkflowStage.OptionsFilter.AllowAutoFilter = false;
            this.colBundleWorkflowStage.OptionsFilter.AllowFilter = false;
            this.colBundleWorkflowStage.OptionsFilter.AllowFilterModeChanging = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleWorkflowStage.OptionsFilter.FilterBySortField = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleWorkflowStage.Visible = true;
            this.colBundleWorkflowStage.VisibleIndex = 13;
            this.colBundleWorkflowStage.Width = 108;
            // 
            // colBundleResultType
            // 
            this.colBundleResultType.Caption = "Bundle Type";
            this.colBundleResultType.FieldName = "BundleResultType";
            this.colBundleResultType.Name = "colBundleResultType";
            this.colBundleResultType.OptionsColumn.AllowEdit = false;
            this.colBundleResultType.OptionsColumn.AllowFocus = false;
            this.colBundleResultType.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleResultType.OptionsColumn.AllowIncrementalSearch = false;
            this.colBundleResultType.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleResultType.OptionsColumn.AllowShowHide = false;
            this.colBundleResultType.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleResultType.OptionsColumn.ReadOnly = true;
            this.colBundleResultType.OptionsColumn.ShowInCustomizationForm = false;
            this.colBundleResultType.OptionsColumn.ShowInExpressionEditor = false;
            this.colBundleResultType.OptionsColumn.TabStop = false;
            this.colBundleResultType.OptionsFilter.AllowAutoFilter = false;
            this.colBundleResultType.OptionsFilter.AllowFilter = false;
            this.colBundleResultType.OptionsFilter.AllowFilterModeChanging = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleResultType.OptionsFilter.FilterBySortField = DevExpress.Utils.DefaultBoolean.False;
            this.colBundleResultType.Visible = true;
            this.colBundleResultType.VisibleIndex = 14;
            this.colBundleResultType.Width = 87;
            // 
            // labelControl7
            // 
            this.labelControl7.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl7.Appearance.ForeColor = System.Drawing.Color.Black;
            this.labelControl7.Location = new System.Drawing.Point(12, 248);
            this.labelControl7.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.labelControl7.Name = "labelControl7";
            this.labelControl7.Size = new System.Drawing.Size(139, 16);
            this.labelControl7.TabIndex = 4;
            this.labelControl7.Text = "Component Lab Test(s):";
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Appearance.ForeColor = System.Drawing.Color.Black;
            this.labelControl1.Location = new System.Drawing.Point(12, 13);
            this.labelControl1.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(139, 16);
            this.labelControl1.TabIndex = 5;
            this.labelControl1.Text = "Result Bundle Audit Log:";
            // 
            // ResultBundleAuditTrailViewerDialog
            // 
            this.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.ClientSize = new System.Drawing.Size(1002, 458);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.labelControl7);
            this.Controls.Add(this.gridTests);
            this.Controls.Add(this.btnPrint);
            this.Controls.Add(this.gridControl);
            this.Controls.Add(this.btnExit);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.KeyPreview = true;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "ResultBundleAuditTrailViewerDialog";
            this.SizeGripStyle = System.Windows.Forms.SizeGripStyle.Hide;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "ResultBundleAuditTrailViewerDialog";
            this.Load += new System.EventHandler(this.ResultBundleAuditTrailViewerDialog_Load);
            this.KeyUp += new System.Windows.Forms.KeyEventHandler(this.ResultBundleAuditTrailViewerDialog_KeyUp);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.resultBundleAuditTrailInfoBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvwTrail)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridTests)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.componentLabTestsSliceBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvwTests)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.SimpleButton btnExit;
        private DevExpress.XtraGrid.GridControl gridControl;
        private DevExpress.XtraGrid.Views.Grid.GridView gvwTrail;
        private System.Windows.Forms.BindingSource resultBundleAuditTrailInfoBindingSource;
        private DevExpress.XtraGrid.Columns.GridColumn colEventTime;
        private DevExpress.XtraGrid.Columns.GridColumn colEventType;
        private DevExpress.XtraGrid.Columns.GridColumn colIpAddress;
        private DevExpress.XtraGrid.Columns.GridColumn colUserName;
        private DevExpress.XtraGrid.Columns.GridColumn colNote;
        private DevExpress.XtraGrid.Columns.GridColumn colEventDate;
        private DevExpress.XtraEditors.SimpleButton btnPrint;
        private DevExpress.XtraGrid.GridControl gridTests;
        private DevExpress.XtraGrid.Views.Grid.GridView gvwTests;
        private DevExpress.XtraGrid.Columns.GridColumn colId;
        private DevExpress.XtraGrid.Columns.GridColumn colLabTestId;
        private DevExpress.XtraGrid.Columns.GridColumn colTestName;
        private DevExpress.XtraGrid.Columns.GridColumn colLabName;
        private DevExpress.XtraGrid.Columns.GridColumn colIsCancelled;
        private DevExpress.XtraGrid.Columns.GridColumn colDateCreated;
        private DevExpress.XtraGrid.Columns.GridColumn colLastModified;
        private DevExpress.XtraGrid.Columns.GridColumn colResultsETA;
        private DevExpress.XtraGrid.Columns.GridColumn colWorkflowStage;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private System.Windows.Forms.BindingSource componentLabTestsSliceBindingSource;
        private DevExpress.XtraGrid.Columns.GridColumn colBundleId;
        private DevExpress.XtraGrid.Columns.GridColumn colBundleIsCancelled;
        private DevExpress.XtraGrid.Columns.GridColumn colBundleDateCreated;
        private DevExpress.XtraGrid.Columns.GridColumn colBundleLastModified;
        private DevExpress.XtraGrid.Columns.GridColumn colBundleWorkflowStage;
        private DevExpress.XtraGrid.Columns.GridColumn colBundleResultType;
    }
}