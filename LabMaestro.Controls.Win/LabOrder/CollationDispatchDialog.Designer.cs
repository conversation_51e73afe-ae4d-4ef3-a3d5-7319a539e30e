﻿namespace LabMaestro.Controls.Win
{
    partial class CollationDispatchDialog
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(CollationDispatchDialog));
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject1 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject2 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject3 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject4 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject5 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject6 = new DevExpress.Utils.SerializableAppearanceObject();
            Xceed.Grid.GradientMap gradientMap6 = new Xceed.Grid.GradientMap();
            Xceed.Grid.GradientStop gradientStop11 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientStop gradientStop12 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientMap gradientMap5 = new Xceed.Grid.GradientMap();
            Xceed.Grid.GradientStop gradientStop9 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientStop gradientStop10 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientMap gradientMap2 = new Xceed.Grid.GradientMap();
            Xceed.Grid.GradientStop gradientStop3 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientStop gradientStop4 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientMap gradientMap1 = new Xceed.Grid.GradientMap();
            Xceed.Grid.GradientStop gradientStop1 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientStop gradientStop2 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientMap gradientMap4 = new Xceed.Grid.GradientMap();
            Xceed.Grid.GradientStop gradientStop7 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientStop gradientStop8 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientMap gradientMap3 = new Xceed.Grid.GradientMap();
            Xceed.Grid.GradientStop gradientStop5 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientStop gradientStop6 = new Xceed.Grid.GradientStop();
            this.barManager = new DevExpress.XtraBars.BarManager(this.components);
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.btnExit = new DevExpress.XtraBars.BarButtonItem();
            this.barAndDockingController = new DevExpress.XtraBars.BarAndDockingController(this.components);
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.miToday = new DevExpress.XtraBars.BarButtonItem();
            this.miYesterday = new DevExpress.XtraBars.BarButtonItem();
            this.miLast3Days = new DevExpress.XtraBars.BarButtonItem();
            this.miLast1Week = new DevExpress.XtraBars.BarButtonItem();
            this.btnQuickDateRangePatientId = new DevExpress.XtraEditors.DropDownButton();
            this.popupMenu = new DevExpress.XtraBars.PopupMenu(this.components);
            this.btnQuickDateRangeOrderStatus = new DevExpress.XtraEditors.DropDownButton();
            this.btnQuickDateRangeBundleStatus = new DevExpress.XtraEditors.DropDownButton();
            this.panelControl1 = new DevExpress.XtraEditors.PanelControl();
            this.labelControl15 = new DevExpress.XtraEditors.LabelControl();
            this.btnSearchBundleStatus = new DevExpress.XtraEditors.SimpleButton();
            this.cboBundleStatus = new DevExpress.XtraEditors.ComboBoxEdit();
            this.dteBundleTo = new DevExpress.XtraEditors.DateEdit();
            this.dteBundleFrom = new DevExpress.XtraEditors.DateEdit();
            this.labelControl12 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl13 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl14 = new DevExpress.XtraEditors.LabelControl();
            this.chkApplyFilter = new DevExpress.XtraEditors.CheckEdit();
            this.btnEditFilter = new DevExpress.XtraEditors.SimpleButton();
            this.btnSearchOrderStatus = new DevExpress.XtraEditors.SimpleButton();
            this.btnClear = new DevExpress.XtraEditors.SimpleButton();
            this.btnSearchPatientId = new DevExpress.XtraEditors.SimpleButton();
            this.btnSearchInvoiceId = new DevExpress.XtraEditors.SimpleButton();
            this.cboReportStatus = new DevExpress.XtraEditors.ComboBoxEdit();
            this.dtOrderTo = new DevExpress.XtraEditors.DateEdit();
            this.dtOrderFrom = new DevExpress.XtraEditors.DateEdit();
            this.dtPatientTo = new DevExpress.XtraEditors.DateEdit();
            this.dtPatientFrom = new DevExpress.XtraEditors.DateEdit();
            this.txtPatientId = new DevExpress.XtraEditors.TextEdit();
            this.txtInvoiceNum = new DevExpress.XtraEditors.TextEdit();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.lblFilterItems = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.btnPreviewSelectedBundle = new DevExpress.XtraEditors.SimpleButton();
            this.gridOrders = new Xceed.Grid.GridControl();
            this.dataRowTemplate2 = new Xceed.Grid.DataRow();
            this.visualGridElementStyle2 = new Xceed.Grid.VisualGridElementStyle();
            this.dataRowStyle1 = new Xceed.Grid.VisualGridElementStyle();
            this.columnManagerRow2 = new Xceed.Grid.ColumnManagerRow();
            this.tabFilter = new Syncfusion.Windows.Forms.Tools.TabControlAdv();
            this.tabPageAdv28 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv29 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv30 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv31 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv32 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv33 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv34 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv35 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv36 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv37 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv38 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv39 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv40 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv41 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv42 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv43 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv44 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv45 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv46 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv47 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv48 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv49 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv50 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv51 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv52 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv53 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.tabPageAdv54 = new Syncfusion.Windows.Forms.Tools.TabPageAdv();
            this.designTimeTabTypeLoader = new Syncfusion.Reflection.TypeLoader(this.components);
            this.gridResultBundles = new Xceed.Grid.GridControl();
            this.dataRow1 = new Xceed.Grid.DataRow();
            this.visualGridElementStyle1 = new Xceed.Grid.VisualGridElementStyle();
            this.visualGridElementStyle3 = new Xceed.Grid.VisualGridElementStyle();
            this.columnManagerRow1 = new Xceed.Grid.ColumnManagerRow();
            this.gridRecentUpdates = new Xceed.Grid.GridControl();
            this.dataRow2 = new Xceed.Grid.DataRow();
            this.visualGridElementStyle4 = new Xceed.Grid.VisualGridElementStyle();
            this.visualGridElementStyle5 = new Xceed.Grid.VisualGridElementStyle();
            this.columnManagerRow3 = new Xceed.Grid.ColumnManagerRow();
            this.btnPrintDispatchSelectedBundle = new DevExpress.XtraEditors.SimpleButton();
            this.btnPrintDispatchAllBundles = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl9 = new DevExpress.XtraEditors.LabelControl();
            this.chkHideCollatedBundles = new DevExpress.XtraEditors.CheckEdit();
            this.panelControl2 = new DevExpress.XtraEditors.PanelControl();
            this.labelControl10 = new DevExpress.XtraEditors.LabelControl();
            this.imgDueStatus = new System.Windows.Forms.PictureBox();
            this.lblInvoiceDue = new DevExpress.XtraEditors.LabelControl();
            this.lblPatientId = new DevExpress.XtraEditors.LabelControl();
            this.labelControl11 = new DevExpress.XtraEditors.LabelControl();
            this.lblInvoiceNum = new DevExpress.XtraEditors.LabelControl();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            this.chkHideUnarchivedTests = new DevExpress.XtraEditors.CheckEdit();
            this.chkSortOldestFirst = new DevExpress.XtraEditors.CheckEdit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.popupMenu)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).BeginInit();
            this.panelControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cboBundleStatus.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dteBundleTo.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dteBundleTo.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dteBundleFrom.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dteBundleFrom.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkApplyFilter.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cboReportStatus.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtOrderTo.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtOrderTo.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtOrderFrom.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtOrderFrom.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtPatientTo.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtPatientTo.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtPatientFrom.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtPatientFrom.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPatientId.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtInvoiceNum.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridOrders)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataRowTemplate2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.columnManagerRow2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabFilter)).BeginInit();
            this.tabFilter.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridResultBundles)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataRow1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.columnManagerRow1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridRecentUpdates)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataRow2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.columnManagerRow3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkHideCollatedBundles.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl2)).BeginInit();
            this.panelControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.imgDueStatus)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkHideUnarchivedTests.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSortOldestFirst.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // barManager
            // 
            this.barManager.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barManager.Controller = this.barAndDockingController;
            this.barManager.DockControls.Add(this.barDockControlTop);
            this.barManager.DockControls.Add(this.barDockControlBottom);
            this.barManager.DockControls.Add(this.barDockControlLeft);
            this.barManager.DockControls.Add(this.barDockControlRight);
            this.barManager.Form = this;
            this.barManager.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.btnExit,
            this.miToday,
            this.miYesterday,
            this.miLast3Days,
            this.miLast1Week});
            this.barManager.MaxItemId = 5;
            // 
            // bar1
            // 
            this.bar1.BarName = "Tools";
            this.bar1.CanDockStyle = DevExpress.XtraBars.BarCanDockStyle.Top;
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.btnExit, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph)});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DrawDragBorder = false;
            this.bar1.OptionsBar.UseWholeRow = true;
            this.bar1.Text = "Tools";
            // 
            // btnExit
            // 
            this.btnExit.Caption = "Exit";
            this.btnExit.Glyph = global::LabMaestro.Controls.Win.Resources.exit_16;
            this.btnExit.Hint = "Exit";
            this.btnExit.Id = 0;
            this.btnExit.Name = "btnExit";
            this.btnExit.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnExit_ItemClick);
            // 
            // barAndDockingController
            // 
            this.barAndDockingController.LookAndFeel.SkinName = "Office 2010 Blue";
            this.barAndDockingController.LookAndFeel.UseDefaultLookAndFeel = false;
            this.barAndDockingController.PropertiesBar.AllowLinkLighting = false;
            this.barAndDockingController.PropertiesBar.DefaultGlyphSize = new System.Drawing.Size(16, 16);
            this.barAndDockingController.PropertiesBar.DefaultLargeGlyphSize = new System.Drawing.Size(32, 32);
            // 
            // barDockControlTop
            // 
            this.barDockControlTop.CausesValidation = false;
            this.barDockControlTop.Dock = System.Windows.Forms.DockStyle.Top;
            this.barDockControlTop.Location = new System.Drawing.Point(0, 0);
            this.barDockControlTop.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.barDockControlTop.Size = new System.Drawing.Size(1238, 29);
            // 
            // barDockControlBottom
            // 
            this.barDockControlBottom.CausesValidation = false;
            this.barDockControlBottom.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.barDockControlBottom.Location = new System.Drawing.Point(0, 722);
            this.barDockControlBottom.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.barDockControlBottom.Size = new System.Drawing.Size(1238, 0);
            // 
            // barDockControlLeft
            // 
            this.barDockControlLeft.CausesValidation = false;
            this.barDockControlLeft.Dock = System.Windows.Forms.DockStyle.Left;
            this.barDockControlLeft.Location = new System.Drawing.Point(0, 29);
            this.barDockControlLeft.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.barDockControlLeft.Size = new System.Drawing.Size(0, 693);
            // 
            // barDockControlRight
            // 
            this.barDockControlRight.CausesValidation = false;
            this.barDockControlRight.Dock = System.Windows.Forms.DockStyle.Right;
            this.barDockControlRight.Location = new System.Drawing.Point(1238, 29);
            this.barDockControlRight.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.barDockControlRight.Size = new System.Drawing.Size(0, 693);
            // 
            // miToday
            // 
            this.miToday.Caption = "Today";
            this.miToday.Id = 1;
            this.miToday.Name = "miToday";
            this.miToday.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.miToday_ItemClick);
            // 
            // miYesterday
            // 
            this.miYesterday.Caption = "Yesterday";
            this.miYesterday.Id = 2;
            this.miYesterday.Name = "miYesterday";
            this.miYesterday.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.miYesterday_ItemClick);
            // 
            // miLast3Days
            // 
            this.miLast3Days.Caption = "Last 3 days";
            this.miLast3Days.Id = 3;
            this.miLast3Days.Name = "miLast3Days";
            this.miLast3Days.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.miLast3Days_ItemClick);
            // 
            // miLast1Week
            // 
            this.miLast1Week.Caption = "Last 1 week";
            this.miLast1Week.Id = 4;
            this.miLast1Week.Name = "miLast1Week";
            this.miLast1Week.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.miLast1Week_ItemClick);
            // 
            // btnQuickDateRangePatientId
            // 
            this.btnQuickDateRangePatientId.Appearance.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnQuickDateRangePatientId.Appearance.Options.UseFont = true;
            this.btnQuickDateRangePatientId.DropDownArrowStyle = DevExpress.XtraEditors.DropDownArrowStyle.Show;
            this.btnQuickDateRangePatientId.DropDownControl = this.popupMenu;
            this.btnQuickDateRangePatientId.Image = global::LabMaestro.Controls.Win.Resources.calendar_export_16;
            this.btnQuickDateRangePatientId.Location = new System.Drawing.Point(624, 32);
            this.btnQuickDateRangePatientId.MenuManager = this.barManager;
            this.btnQuickDateRangePatientId.Name = "btnQuickDateRangePatientId";
            this.barManager.SetPopupContextMenu(this.btnQuickDateRangePatientId, this.popupMenu);
            this.btnQuickDateRangePatientId.Size = new System.Drawing.Size(41, 23);
            this.btnQuickDateRangePatientId.TabIndex = 6;
            this.btnQuickDateRangePatientId.ToolTip = "Set Quick Date-range";
            // 
            // popupMenu
            // 
            this.popupMenu.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.miToday),
            new DevExpress.XtraBars.LinkPersistInfo(this.miYesterday),
            new DevExpress.XtraBars.LinkPersistInfo(this.miLast3Days),
            new DevExpress.XtraBars.LinkPersistInfo(this.miLast1Week)});
            this.popupMenu.Manager = this.barManager;
            this.popupMenu.Name = "popupMenu";
            // 
            // btnQuickDateRangeOrderStatus
            // 
            this.btnQuickDateRangeOrderStatus.Appearance.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnQuickDateRangeOrderStatus.Appearance.Options.UseFont = true;
            this.btnQuickDateRangeOrderStatus.DropDownArrowStyle = DevExpress.XtraEditors.DropDownArrowStyle.Show;
            this.btnQuickDateRangeOrderStatus.DropDownControl = this.popupMenu;
            this.btnQuickDateRangeOrderStatus.Image = global::LabMaestro.Controls.Win.Resources.calendar_export_16;
            this.btnQuickDateRangeOrderStatus.Location = new System.Drawing.Point(624, 63);
            this.btnQuickDateRangeOrderStatus.MenuManager = this.barManager;
            this.btnQuickDateRangeOrderStatus.Name = "btnQuickDateRangeOrderStatus";
            this.barManager.SetPopupContextMenu(this.btnQuickDateRangeOrderStatus, this.popupMenu);
            this.btnQuickDateRangeOrderStatus.Size = new System.Drawing.Size(41, 23);
            this.btnQuickDateRangeOrderStatus.TabIndex = 7;
            this.btnQuickDateRangeOrderStatus.ToolTip = "Set Quick Date-range";
            // 
            // btnQuickDateRangeBundleStatus
            // 
            this.btnQuickDateRangeBundleStatus.Appearance.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnQuickDateRangeBundleStatus.Appearance.Options.UseFont = true;
            this.btnQuickDateRangeBundleStatus.DropDownArrowStyle = DevExpress.XtraEditors.DropDownArrowStyle.Show;
            this.btnQuickDateRangeBundleStatus.DropDownControl = this.popupMenu;
            this.btnQuickDateRangeBundleStatus.Image = global::LabMaestro.Controls.Win.Resources.calendar_export_16;
            this.btnQuickDateRangeBundleStatus.Location = new System.Drawing.Point(624, 91);
            this.btnQuickDateRangeBundleStatus.MenuManager = this.barManager;
            this.btnQuickDateRangeBundleStatus.Name = "btnQuickDateRangeBundleStatus";
            this.barManager.SetPopupContextMenu(this.btnQuickDateRangeBundleStatus, this.popupMenu);
            this.btnQuickDateRangeBundleStatus.Size = new System.Drawing.Size(41, 23);
            this.btnQuickDateRangeBundleStatus.TabIndex = 15;
            this.btnQuickDateRangeBundleStatus.ToolTip = "Set Quick Date-range";
            // 
            // panelControl1
            // 
            this.panelControl1.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.panelControl1.Controls.Add(this.labelControl15);
            this.panelControl1.Controls.Add(this.btnQuickDateRangeBundleStatus);
            this.panelControl1.Controls.Add(this.btnSearchBundleStatus);
            this.panelControl1.Controls.Add(this.cboBundleStatus);
            this.panelControl1.Controls.Add(this.dteBundleTo);
            this.panelControl1.Controls.Add(this.dteBundleFrom);
            this.panelControl1.Controls.Add(this.labelControl12);
            this.panelControl1.Controls.Add(this.labelControl13);
            this.panelControl1.Controls.Add(this.labelControl14);
            this.panelControl1.Controls.Add(this.btnQuickDateRangeOrderStatus);
            this.panelControl1.Controls.Add(this.btnQuickDateRangePatientId);
            this.panelControl1.Controls.Add(this.chkApplyFilter);
            this.panelControl1.Controls.Add(this.btnEditFilter);
            this.panelControl1.Controls.Add(this.btnSearchOrderStatus);
            this.panelControl1.Controls.Add(this.btnClear);
            this.panelControl1.Controls.Add(this.btnSearchPatientId);
            this.panelControl1.Controls.Add(this.btnSearchInvoiceId);
            this.panelControl1.Controls.Add(this.cboReportStatus);
            this.panelControl1.Controls.Add(this.dtOrderTo);
            this.panelControl1.Controls.Add(this.dtOrderFrom);
            this.panelControl1.Controls.Add(this.dtPatientTo);
            this.panelControl1.Controls.Add(this.dtPatientFrom);
            this.panelControl1.Controls.Add(this.txtPatientId);
            this.panelControl1.Controls.Add(this.txtInvoiceNum);
            this.panelControl1.Controls.Add(this.labelControl7);
            this.panelControl1.Controls.Add(this.labelControl2);
            this.panelControl1.Controls.Add(this.lblFilterItems);
            this.panelControl1.Controls.Add(this.labelControl3);
            this.panelControl1.Controls.Add(this.labelControl4);
            this.panelControl1.Controls.Add(this.labelControl6);
            this.panelControl1.Controls.Add(this.labelControl5);
            this.panelControl1.Controls.Add(this.labelControl1);
            this.panelControl1.Location = new System.Drawing.Point(0, 37);
            this.panelControl1.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.panelControl1.Name = "panelControl1";
            this.panelControl1.Size = new System.Drawing.Size(770, 147);
            this.panelControl1.TabIndex = 4;
            // 
            // labelControl15
            // 
            this.labelControl15.AllowHtmlString = true;
            this.labelControl15.Appearance.BackColor = System.Drawing.Color.Transparent;
            this.labelControl15.Appearance.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl15.Appearance.ForeColor = System.Drawing.Color.DimGray;
            this.labelControl15.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.labelControl15.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.labelControl15.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.labelControl15.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Horizontal;
            this.labelControl15.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.labelControl15.Location = new System.Drawing.Point(462, 8);
            this.labelControl15.LookAndFeel.UseDefaultLookAndFeel = false;
            this.labelControl15.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.labelControl15.Name = "labelControl15";
            this.labelControl15.Padding = new System.Windows.Forms.Padding(2);
            this.labelControl15.Size = new System.Drawing.Size(295, 18);
            this.labelControl15.TabIndex = 16;
            this.labelControl15.Text = "<b>F5</b> - Focus invoice box | <b>F12</b> - View Bundle audit trail";
            // 
            // btnSearchBundleStatus
            // 
            this.btnSearchBundleStatus.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnSearchBundleStatus.Appearance.Options.UseFont = true;
            this.btnSearchBundleStatus.Image = global::LabMaestro.Controls.Win.Resources.search_16;
            this.btnSearchBundleStatus.Location = new System.Drawing.Point(681, 92);
            this.btnSearchBundleStatus.Name = "btnSearchBundleStatus";
            this.btnSearchBundleStatus.Size = new System.Drawing.Size(75, 23);
            this.btnSearchBundleStatus.TabIndex = 14;
            this.btnSearchBundleStatus.Text = "Search";
            this.btnSearchBundleStatus.Click += new System.EventHandler(this.btnSearchBundleStatus_Click);
            // 
            // cboBundleStatus
            // 
            this.cboBundleStatus.Location = new System.Drawing.Point(111, 89);
            this.cboBundleStatus.MenuManager = this.barManager;
            this.cboBundleStatus.Name = "cboBundleStatus";
            this.cboBundleStatus.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cboBundleStatus.Properties.Appearance.Options.UseFont = true;
            this.cboBundleStatus.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cboBundleStatus.Properties.Items.AddRange(new object[] {
            "Not Collated",
            "Not Dispatched",
            "Repeat Procedure"});
            this.cboBundleStatus.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cboBundleStatus.Size = new System.Drawing.Size(140, 22);
            this.cboBundleStatus.TabIndex = 13;
            // 
            // dteBundleTo
            // 
            this.dteBundleTo.EditValue = null;
            this.dteBundleTo.Location = new System.Drawing.Point(483, 92);
            this.dteBundleTo.Name = "dteBundleTo";
            this.dteBundleTo.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.dteBundleTo.Properties.Appearance.Options.UseFont = true;
            this.dteBundleTo.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph, "", -1, true, true, false, DevExpress.XtraEditors.ImageLocation.MiddleCenter, ((System.Drawing.Image)(resources.GetObject("dteBundleTo.Properties.Buttons"))), new DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.None), serializableAppearanceObject1, "", null, null, true)});
            this.dteBundleTo.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.dteBundleTo.Size = new System.Drawing.Size(135, 22);
            this.dteBundleTo.TabIndex = 11;
            // 
            // dteBundleFrom
            // 
            this.dteBundleFrom.EditValue = null;
            this.dteBundleFrom.Location = new System.Drawing.Point(310, 89);
            this.dteBundleFrom.Name = "dteBundleFrom";
            this.dteBundleFrom.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.dteBundleFrom.Properties.Appearance.Options.UseFont = true;
            this.dteBundleFrom.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph, "", -1, true, true, false, DevExpress.XtraEditors.ImageLocation.MiddleCenter, ((System.Drawing.Image)(resources.GetObject("dteBundleFrom.Properties.Buttons"))), new DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.None), serializableAppearanceObject2, "", null, null, true)});
            this.dteBundleFrom.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.dteBundleFrom.Size = new System.Drawing.Size(135, 22);
            this.dteBundleFrom.TabIndex = 12;
            // 
            // labelControl12
            // 
            this.labelControl12.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl12.Appearance.ForeColor = System.Drawing.Color.Black;
            this.labelControl12.Location = new System.Drawing.Point(12, 92);
            this.labelControl12.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.labelControl12.Name = "labelControl12";
            this.labelControl12.Size = new System.Drawing.Size(90, 16);
            this.labelControl12.TabIndex = 8;
            this.labelControl12.Text = "Bundle Status";
            // 
            // labelControl13
            // 
            this.labelControl13.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl13.Appearance.ForeColor = System.Drawing.Color.Black;
            this.labelControl13.Location = new System.Drawing.Point(273, 92);
            this.labelControl13.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.labelControl13.Name = "labelControl13";
            this.labelControl13.Size = new System.Drawing.Size(31, 16);
            this.labelControl13.TabIndex = 9;
            this.labelControl13.Text = "From";
            // 
            // labelControl14
            // 
            this.labelControl14.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl14.Appearance.ForeColor = System.Drawing.Color.Black;
            this.labelControl14.Location = new System.Drawing.Point(462, 95);
            this.labelControl14.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.labelControl14.Name = "labelControl14";
            this.labelControl14.Size = new System.Drawing.Size(15, 16);
            this.labelControl14.TabIndex = 10;
            this.labelControl14.Text = "To";
            // 
            // chkApplyFilter
            // 
            this.chkApplyFilter.Location = new System.Drawing.Point(109, 123);
            this.chkApplyFilter.MenuManager = this.barManager;
            this.chkApplyFilter.Name = "chkApplyFilter";
            this.chkApplyFilter.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkApplyFilter.Properties.Appearance.ForeColor = System.Drawing.Color.Black;
            this.chkApplyFilter.Properties.Appearance.Options.UseFont = true;
            this.chkApplyFilter.Properties.Appearance.Options.UseForeColor = true;
            this.chkApplyFilter.Properties.Caption = "Apply Filter?";
            this.chkApplyFilter.Size = new System.Drawing.Size(112, 20);
            this.chkApplyFilter.TabIndex = 5;
            this.chkApplyFilter.CheckedChanged += new System.EventHandler(this.chkApplyFilter_CheckedChanged);
            // 
            // btnEditFilter
            // 
            this.btnEditFilter.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnEditFilter.Appearance.Options.UseFont = true;
            this.btnEditFilter.Image = global::LabMaestro.Controls.Win.Resources.edit16;
            this.btnEditFilter.Location = new System.Drawing.Point(255, 121);
            this.btnEditFilter.Name = "btnEditFilter";
            this.btnEditFilter.Size = new System.Drawing.Size(88, 23);
            this.btnEditFilter.TabIndex = 4;
            this.btnEditFilter.Text = "Edit Filter";
            this.btnEditFilter.Click += new System.EventHandler(this.btnEditFilter_Click);
            // 
            // btnSearchOrderStatus
            // 
            this.btnSearchOrderStatus.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnSearchOrderStatus.Appearance.Options.UseFont = true;
            this.btnSearchOrderStatus.Image = global::LabMaestro.Controls.Win.Resources.search_16;
            this.btnSearchOrderStatus.Location = new System.Drawing.Point(681, 64);
            this.btnSearchOrderStatus.Name = "btnSearchOrderStatus";
            this.btnSearchOrderStatus.Size = new System.Drawing.Size(75, 23);
            this.btnSearchOrderStatus.TabIndex = 4;
            this.btnSearchOrderStatus.Text = "Search";
            this.btnSearchOrderStatus.Click += new System.EventHandler(this.btnSearchOrderStatus_Click);
            // 
            // btnClear
            // 
            this.btnClear.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnClear.Appearance.Options.UseFont = true;
            this.btnClear.Image = global::LabMaestro.Controls.Win.Resources.clear;
            this.btnClear.Location = new System.Drawing.Point(681, 121);
            this.btnClear.Name = "btnClear";
            this.btnClear.Size = new System.Drawing.Size(75, 23);
            this.btnClear.TabIndex = 4;
            this.btnClear.Text = "Clear";
            this.btnClear.Click += new System.EventHandler(this.btnClear_Click);
            // 
            // btnSearchPatientId
            // 
            this.btnSearchPatientId.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnSearchPatientId.Appearance.Options.UseFont = true;
            this.btnSearchPatientId.Image = global::LabMaestro.Controls.Win.Resources.search_16;
            this.btnSearchPatientId.Location = new System.Drawing.Point(681, 32);
            this.btnSearchPatientId.Name = "btnSearchPatientId";
            this.btnSearchPatientId.Size = new System.Drawing.Size(75, 23);
            this.btnSearchPatientId.TabIndex = 4;
            this.btnSearchPatientId.Text = "Search";
            this.btnSearchPatientId.Click += new System.EventHandler(this.btnSearchPatientId_Click);
            // 
            // btnSearchInvoiceId
            // 
            this.btnSearchInvoiceId.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnSearchInvoiceId.Appearance.Options.UseFont = true;
            this.btnSearchInvoiceId.Image = global::LabMaestro.Controls.Win.Resources.search_16;
            this.btnSearchInvoiceId.Location = new System.Drawing.Point(257, 4);
            this.btnSearchInvoiceId.Name = "btnSearchInvoiceId";
            this.btnSearchInvoiceId.Size = new System.Drawing.Size(75, 23);
            this.btnSearchInvoiceId.TabIndex = 4;
            this.btnSearchInvoiceId.Text = "Search";
            this.btnSearchInvoiceId.Click += new System.EventHandler(this.btnSearchInvoiceId_Click);
            // 
            // cboReportStatus
            // 
            this.cboReportStatus.Location = new System.Drawing.Point(111, 61);
            this.cboReportStatus.MenuManager = this.barManager;
            this.cboReportStatus.Name = "cboReportStatus";
            this.cboReportStatus.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cboReportStatus.Properties.Appearance.Options.UseFont = true;
            this.cboReportStatus.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cboReportStatus.Properties.Items.AddRange(new object[] {
            "Finalized",
            "Verified",
            "Resulted",
            "Collated",
            "Dispatched",
            "Repeat Procedure",
            "Fulfilled",
            "Canceled",
            "Ordered",
            "<ALL>"});
            this.cboReportStatus.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cboReportStatus.Size = new System.Drawing.Size(140, 22);
            this.cboReportStatus.TabIndex = 3;
            // 
            // dtOrderTo
            // 
            this.dtOrderTo.EditValue = null;
            this.dtOrderTo.Location = new System.Drawing.Point(483, 64);
            this.dtOrderTo.Name = "dtOrderTo";
            this.dtOrderTo.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.dtOrderTo.Properties.Appearance.Options.UseFont = true;
            this.dtOrderTo.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph, "", -1, true, true, false, DevExpress.XtraEditors.ImageLocation.MiddleCenter, ((System.Drawing.Image)(resources.GetObject("dtOrderTo.Properties.Buttons"))), new DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.None), serializableAppearanceObject3, "", null, null, true)});
            this.dtOrderTo.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.dtOrderTo.Size = new System.Drawing.Size(135, 22);
            this.dtOrderTo.TabIndex = 2;
            // 
            // dtOrderFrom
            // 
            this.dtOrderFrom.EditValue = null;
            this.dtOrderFrom.Location = new System.Drawing.Point(310, 61);
            this.dtOrderFrom.Name = "dtOrderFrom";
            this.dtOrderFrom.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.dtOrderFrom.Properties.Appearance.Options.UseFont = true;
            this.dtOrderFrom.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph, "", -1, true, true, false, DevExpress.XtraEditors.ImageLocation.MiddleCenter, ((System.Drawing.Image)(resources.GetObject("dtOrderFrom.Properties.Buttons"))), new DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.None), serializableAppearanceObject4, "", null, null, true)});
            this.dtOrderFrom.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.dtOrderFrom.Size = new System.Drawing.Size(135, 22);
            this.dtOrderFrom.TabIndex = 2;
            // 
            // dtPatientTo
            // 
            this.dtPatientTo.EditValue = null;
            this.dtPatientTo.Location = new System.Drawing.Point(483, 33);
            this.dtPatientTo.Name = "dtPatientTo";
            this.dtPatientTo.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.dtPatientTo.Properties.Appearance.Options.UseFont = true;
            this.dtPatientTo.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph, "", -1, true, true, false, DevExpress.XtraEditors.ImageLocation.MiddleCenter, ((System.Drawing.Image)(resources.GetObject("dtPatientTo.Properties.Buttons"))), new DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.None), serializableAppearanceObject5, "", null, null, true)});
            this.dtPatientTo.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.dtPatientTo.Size = new System.Drawing.Size(135, 22);
            this.dtPatientTo.TabIndex = 2;
            // 
            // dtPatientFrom
            // 
            this.dtPatientFrom.EditValue = null;
            this.dtPatientFrom.Location = new System.Drawing.Point(310, 33);
            this.dtPatientFrom.MenuManager = this.barManager;
            this.dtPatientFrom.Name = "dtPatientFrom";
            this.dtPatientFrom.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.dtPatientFrom.Properties.Appearance.Options.UseFont = true;
            this.dtPatientFrom.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph, "", -1, true, true, false, DevExpress.XtraEditors.ImageLocation.MiddleCenter, ((System.Drawing.Image)(resources.GetObject("dtPatientFrom.Properties.Buttons"))), new DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.None), serializableAppearanceObject6, "", null, null, true)});
            this.dtPatientFrom.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.dtPatientFrom.Size = new System.Drawing.Size(135, 22);
            this.dtPatientFrom.TabIndex = 2;
            // 
            // txtPatientId
            // 
            this.txtPatientId.Location = new System.Drawing.Point(111, 33);
            this.txtPatientId.Name = "txtPatientId";
            this.txtPatientId.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtPatientId.Properties.Appearance.ForeColor = System.Drawing.Color.DarkRed;
            this.txtPatientId.Properties.Appearance.Options.UseFont = true;
            this.txtPatientId.Properties.Appearance.Options.UseForeColor = true;
            this.txtPatientId.Size = new System.Drawing.Size(140, 22);
            this.txtPatientId.TabIndex = 1;
            // 
            // txtInvoiceNum
            // 
            this.txtInvoiceNum.Location = new System.Drawing.Point(111, 5);
            this.txtInvoiceNum.MenuManager = this.barManager;
            this.txtInvoiceNum.Name = "txtInvoiceNum";
            this.txtInvoiceNum.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtInvoiceNum.Properties.Appearance.ForeColor = System.Drawing.Color.DarkRed;
            this.txtInvoiceNum.Properties.Appearance.Options.UseFont = true;
            this.txtInvoiceNum.Properties.Appearance.Options.UseForeColor = true;
            this.txtInvoiceNum.Size = new System.Drawing.Size(140, 22);
            this.txtInvoiceNum.TabIndex = 1;
            this.txtInvoiceNum.KeyUp += new System.Windows.Forms.KeyEventHandler(this.txtInvoiceId_KeyUp);
            // 
            // labelControl7
            // 
            this.labelControl7.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl7.Appearance.ForeColor = System.Drawing.Color.Black;
            this.labelControl7.Location = new System.Drawing.Point(12, 8);
            this.labelControl7.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.labelControl7.Name = "labelControl7";
            this.labelControl7.Size = new System.Drawing.Size(61, 16);
            this.labelControl7.TabIndex = 0;
            this.labelControl7.Text = "Invoice #";
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Appearance.ForeColor = System.Drawing.Color.Black;
            this.labelControl2.Location = new System.Drawing.Point(12, 36);
            this.labelControl2.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(64, 16);
            this.labelControl2.TabIndex = 0;
            this.labelControl2.Text = "Patient Id";
            // 
            // lblFilterItems
            // 
            this.lblFilterItems.Appearance.BackColor = System.Drawing.Color.LemonChiffon;
            this.lblFilterItems.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblFilterItems.Appearance.ForeColor = System.Drawing.Color.Maroon;
            this.lblFilterItems.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lblFilterItems.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.lblFilterItems.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.lblFilterItems.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Horizontal;
            this.lblFilterItems.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.lblFilterItems.Location = new System.Drawing.Point(349, 123);
            this.lblFilterItems.LookAndFeel.UseDefaultLookAndFeel = false;
            this.lblFilterItems.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.lblFilterItems.Name = "lblFilterItems";
            this.lblFilterItems.Padding = new System.Windows.Forms.Padding(2);
            this.lblFilterItems.Size = new System.Drawing.Size(130, 20);
            this.lblFilterItems.TabIndex = 0;
            this.lblFilterItems.Text = " [A] [B] [C] [D] [E] ";
            // 
            // labelControl3
            // 
            this.labelControl3.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl3.Appearance.ForeColor = System.Drawing.Color.Black;
            this.labelControl3.Location = new System.Drawing.Point(12, 64);
            this.labelControl3.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(84, 16);
            this.labelControl3.TabIndex = 0;
            this.labelControl3.Text = "Order Status";
            // 
            // labelControl4
            // 
            this.labelControl4.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl4.Appearance.ForeColor = System.Drawing.Color.Black;
            this.labelControl4.Location = new System.Drawing.Point(273, 64);
            this.labelControl4.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(31, 16);
            this.labelControl4.TabIndex = 0;
            this.labelControl4.Text = "From";
            // 
            // labelControl6
            // 
            this.labelControl6.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl6.Appearance.ForeColor = System.Drawing.Color.Black;
            this.labelControl6.Location = new System.Drawing.Point(462, 67);
            this.labelControl6.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(15, 16);
            this.labelControl6.TabIndex = 0;
            this.labelControl6.Text = "To";
            // 
            // labelControl5
            // 
            this.labelControl5.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl5.Appearance.ForeColor = System.Drawing.Color.Black;
            this.labelControl5.Location = new System.Drawing.Point(462, 36);
            this.labelControl5.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(15, 16);
            this.labelControl5.TabIndex = 0;
            this.labelControl5.Text = "To";
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Appearance.ForeColor = System.Drawing.Color.Black;
            this.labelControl1.Location = new System.Drawing.Point(273, 36);
            this.labelControl1.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(31, 16);
            this.labelControl1.TabIndex = 0;
            this.labelControl1.Text = "From";
            // 
            // btnPreviewSelectedBundle
            // 
            this.btnPreviewSelectedBundle.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnPreviewSelectedBundle.Appearance.Options.UseFont = true;
            this.btnPreviewSelectedBundle.Image = global::LabMaestro.Controls.Win.Resources.preview24;
            this.btnPreviewSelectedBundle.Location = new System.Drawing.Point(1045, 579);
            this.btnPreviewSelectedBundle.Name = "btnPreviewSelectedBundle";
            this.btnPreviewSelectedBundle.Size = new System.Drawing.Size(181, 40);
            this.btnPreviewSelectedBundle.TabIndex = 4;
            this.btnPreviewSelectedBundle.Text = "Preview Selected Bundle";
            this.btnPreviewSelectedBundle.Click += new System.EventHandler(this.btnPreviewSelectedBundle_Click);
            // 
            // gridOrders
            // 
            this.gridOrders.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.gridOrders.ClipCurrentCellSelection = false;
            this.gridOrders.DataRowTemplate = this.dataRowTemplate2;
            this.gridOrders.DataRowTemplateStyles.Add(this.visualGridElementStyle2);
            this.gridOrders.DataRowTemplateStyles.Add(this.dataRowStyle1);
            // 
            // 
            // 
            this.gridOrders.ErrorVisualStyle.OverrideUIStyle = false;
            // 
            // 
            // 
            this.gridOrders.FixedColumnSplitter.AllowRepositioning = false;
            this.gridOrders.FixedColumnSplitter.Visible = false;
            this.gridOrders.FixedHeaderRows.Add(this.columnManagerRow2);
            this.gridOrders.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.gridOrders.GridLineColor = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(239)))), ((int)(((byte)(255)))));
            this.gridOrders.GridLineStyle = System.Drawing.Drawing2D.DashStyle.Dot;
            this.gridOrders.InactiveSelectionBackColor = System.Drawing.Color.RoyalBlue;
            // 
            // 
            // 
            this.gridOrders.InactiveSelectionVisualStyle.BackColor = System.Drawing.Color.Olive;
            this.gridOrders.InactiveSelectionVisualStyle.ForeColor = System.Drawing.Color.White;
            this.gridOrders.InactiveSelectionVisualStyle.OverrideUIStyle = true;
            this.gridOrders.Location = new System.Drawing.Point(12, 216);
            this.gridOrders.Margin = new System.Windows.Forms.Padding(6);
            this.gridOrders.Name = "gridOrders";
            this.gridOrders.OverrideUIStyle = false;
            this.gridOrders.ReadOnly = true;
            // 
            // 
            // 
            this.gridOrders.RowSelectorPane.AllowRowResize = false;
            this.gridOrders.RowSelectorPane.CurrentImageIndex = 16;
            gradientMap6.GradientMode = Xceed.UI.GradientMode.Horizontal;
            gradientStop11.Color = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(239)))), ((int)(((byte)(255)))));
            gradientStop11.Offset = 0D;
            gradientStop12.Color = System.Drawing.Color.FromArgb(((int)(((byte)(175)))), ((int)(((byte)(210)))), ((int)(((byte)(255)))));
            gradientStop12.Offset = 1D;
            gradientMap6.GradientStops.Add(gradientStop11);
            gradientMap6.GradientStops.Add(gradientStop12);
            this.gridOrders.RowSelectorPane.GradientMap = gradientMap6;
            // 
            // 
            // 
            this.gridOrders.RowSelectorPane.HotVisualStyle.OverrideUIStyle = true;
            this.gridOrders.RowSelectorPane.OverrideUIStyle = true;
            this.gridOrders.RowSelectorPane.SelectedImageIndex = 0;
            this.gridOrders.RowSelectorPane.Visible = false;
            this.gridOrders.RowSelectorPane.Width = 17;
            this.gridOrders.SelectionBackColor = System.Drawing.Color.Indigo;
            this.gridOrders.SelectionForeColor = System.Drawing.Color.White;
            this.gridOrders.SelectionMode = System.Windows.Forms.SelectionMode.One;
            // 
            // 
            // 
            this.gridOrders.SelectionVisualStyle.BackColor = System.Drawing.Color.Indigo;
            this.gridOrders.SelectionVisualStyle.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.gridOrders.SelectionVisualStyle.ForeColor = System.Drawing.Color.White;
            this.gridOrders.SelectionVisualStyle.OverrideUIStyle = true;
            this.gridOrders.ShowFocusRectangle = false;
            // 
            // 
            // 
            this.gridOrders.SideMargin.Visible = false;
            this.gridOrders.Size = new System.Drawing.Size(758, 325);
            this.gridOrders.SynchronizeDetailGrids = false;
            this.gridOrders.TabIndex = 31;
            this.gridOrders.TabStop = false;
            this.gridOrders.TreeLineColor = System.Drawing.Color.Silver;
            this.gridOrders.UIStyle = Xceed.UI.UIStyle.System;
            this.gridOrders.VerticalAlignment = Xceed.Grid.VerticalAlignment.Center;
            this.gridOrders.SelectedRowsChanged += new System.EventHandler(this.GridOrders_SelectedRowsChanged);
            // 
            // visualGridElementStyle2
            // 
            // 
            // 
            // 
            this.visualGridElementStyle2.HotVisualStyle.OverrideUIStyle = true;
            this.visualGridElementStyle2.OverrideUIStyle = false;
            // 
            // dataRowStyle1
            // 
            this.dataRowStyle1.BackColor = System.Drawing.Color.AliceBlue;
            // 
            // columnManagerRow2
            // 
            this.columnManagerRow2.AllowColumnReorder = false;
            this.columnManagerRow2.AllowColumnResize = true;
            this.columnManagerRow2.AllowSort = false;
            this.columnManagerRow2.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            gradientStop9.Color = System.Drawing.Color.FromArgb(((int)(((byte)(252)))), ((int)(((byte)(253)))), ((int)(((byte)(255)))));
            gradientStop9.Offset = 0D;
            gradientStop10.Color = System.Drawing.Color.FromArgb(((int)(((byte)(196)))), ((int)(((byte)(221)))), ((int)(((byte)(255)))));
            gradientStop10.Offset = 1D;
            gradientMap5.GradientStops.Add(gradientStop9);
            gradientMap5.GradientStops.Add(gradientStop10);
            this.columnManagerRow2.GradientMap = gradientMap5;
            this.columnManagerRow2.Height = 23;
            // 
            // 
            // 
            this.columnManagerRow2.HotVisualStyle.OverrideUIStyle = false;
            this.columnManagerRow2.OverrideUIStyle = true;
            // 
            // tabFilter
            // 
            this.tabFilter.Controls.Add(this.tabPageAdv28);
            this.tabFilter.Controls.Add(this.tabPageAdv29);
            this.tabFilter.Controls.Add(this.tabPageAdv30);
            this.tabFilter.Controls.Add(this.tabPageAdv31);
            this.tabFilter.Controls.Add(this.tabPageAdv32);
            this.tabFilter.Controls.Add(this.tabPageAdv33);
            this.tabFilter.Controls.Add(this.tabPageAdv34);
            this.tabFilter.Controls.Add(this.tabPageAdv35);
            this.tabFilter.Controls.Add(this.tabPageAdv36);
            this.tabFilter.Controls.Add(this.tabPageAdv37);
            this.tabFilter.Controls.Add(this.tabPageAdv38);
            this.tabFilter.Controls.Add(this.tabPageAdv39);
            this.tabFilter.Controls.Add(this.tabPageAdv40);
            this.tabFilter.Controls.Add(this.tabPageAdv41);
            this.tabFilter.Controls.Add(this.tabPageAdv42);
            this.tabFilter.Controls.Add(this.tabPageAdv43);
            this.tabFilter.Controls.Add(this.tabPageAdv44);
            this.tabFilter.Controls.Add(this.tabPageAdv45);
            this.tabFilter.Controls.Add(this.tabPageAdv46);
            this.tabFilter.Controls.Add(this.tabPageAdv47);
            this.tabFilter.Controls.Add(this.tabPageAdv48);
            this.tabFilter.Controls.Add(this.tabPageAdv49);
            this.tabFilter.Controls.Add(this.tabPageAdv50);
            this.tabFilter.Controls.Add(this.tabPageAdv51);
            this.tabFilter.Controls.Add(this.tabPageAdv52);
            this.tabFilter.Controls.Add(this.tabPageAdv53);
            this.tabFilter.Controls.Add(this.tabPageAdv54);
            this.tabFilter.Location = new System.Drawing.Point(12, 189);
            this.tabFilter.Name = "tabFilter";
            this.tabFilter.ShowScroll = false;
            this.tabFilter.Size = new System.Drawing.Size(758, 27);
            this.tabFilter.SizeMode = Syncfusion.Windows.Forms.Tools.TabSizeMode.ShrinkToFit;
            this.tabFilter.SwitchPagesForDialogKeys = false;
            this.tabFilter.TabGap = 4;
            this.tabFilter.TabIndex = 33;
            this.tabFilter.TabStop = false;
            this.tabFilter.TabStyle = typeof(Syncfusion.Windows.Forms.Tools.TabRendererVS2010);
            this.tabFilter.ThemesEnabled = true;
            this.tabFilter.SelectedIndexChanged += new System.EventHandler(this.tabFilter_SelectedIndexChanged);
            // 
            // tabPageAdv28
            // 
            this.tabPageAdv28.Image = null;
            this.tabPageAdv28.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv28.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv28.Name = "tabPageAdv28";
            this.tabPageAdv28.ShowCloseButton = true;
            this.tabPageAdv28.Size = new System.Drawing.Size(751, 0);
            this.tabPageAdv28.TabIndex = 28;
            this.tabPageAdv28.Text = "*";
            this.tabPageAdv28.ThemesEnabled = true;
            // 
            // tabPageAdv29
            // 
            this.tabPageAdv29.Image = null;
            this.tabPageAdv29.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv29.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv29.Name = "tabPageAdv29";
            this.tabPageAdv29.ShowCloseButton = true;
            this.tabPageAdv29.Size = new System.Drawing.Size(751, 0);
            this.tabPageAdv29.TabIndex = 29;
            this.tabPageAdv29.Text = "A";
            this.tabPageAdv29.ThemesEnabled = true;
            // 
            // tabPageAdv30
            // 
            this.tabPageAdv30.Image = null;
            this.tabPageAdv30.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv30.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv30.Name = "tabPageAdv30";
            this.tabPageAdv30.ShowCloseButton = true;
            this.tabPageAdv30.Size = new System.Drawing.Size(751, 0);
            this.tabPageAdv30.TabIndex = 30;
            this.tabPageAdv30.Text = "B";
            this.tabPageAdv30.ThemesEnabled = true;
            // 
            // tabPageAdv31
            // 
            this.tabPageAdv31.Image = null;
            this.tabPageAdv31.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv31.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv31.Name = "tabPageAdv31";
            this.tabPageAdv31.ShowCloseButton = true;
            this.tabPageAdv31.Size = new System.Drawing.Size(751, 0);
            this.tabPageAdv31.TabIndex = 31;
            this.tabPageAdv31.Text = "C";
            this.tabPageAdv31.ThemesEnabled = true;
            // 
            // tabPageAdv32
            // 
            this.tabPageAdv32.Image = null;
            this.tabPageAdv32.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv32.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv32.Name = "tabPageAdv32";
            this.tabPageAdv32.ShowCloseButton = true;
            this.tabPageAdv32.Size = new System.Drawing.Size(751, 0);
            this.tabPageAdv32.TabIndex = 32;
            this.tabPageAdv32.Text = "D";
            this.tabPageAdv32.ThemesEnabled = true;
            // 
            // tabPageAdv33
            // 
            this.tabPageAdv33.Image = null;
            this.tabPageAdv33.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv33.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv33.Name = "tabPageAdv33";
            this.tabPageAdv33.ShowCloseButton = true;
            this.tabPageAdv33.Size = new System.Drawing.Size(751, 0);
            this.tabPageAdv33.TabIndex = 33;
            this.tabPageAdv33.Text = "E";
            this.tabPageAdv33.ThemesEnabled = true;
            // 
            // tabPageAdv34
            // 
            this.tabPageAdv34.Image = null;
            this.tabPageAdv34.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv34.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv34.Name = "tabPageAdv34";
            this.tabPageAdv34.ShowCloseButton = true;
            this.tabPageAdv34.Size = new System.Drawing.Size(751, 0);
            this.tabPageAdv34.TabIndex = 34;
            this.tabPageAdv34.Text = "F";
            this.tabPageAdv34.ThemesEnabled = true;
            // 
            // tabPageAdv35
            // 
            this.tabPageAdv35.Image = null;
            this.tabPageAdv35.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv35.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv35.Name = "tabPageAdv35";
            this.tabPageAdv35.ShowCloseButton = true;
            this.tabPageAdv35.Size = new System.Drawing.Size(751, 0);
            this.tabPageAdv35.TabIndex = 35;
            this.tabPageAdv35.Text = "G";
            this.tabPageAdv35.ThemesEnabled = true;
            // 
            // tabPageAdv36
            // 
            this.tabPageAdv36.Image = null;
            this.tabPageAdv36.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv36.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv36.Name = "tabPageAdv36";
            this.tabPageAdv36.ShowCloseButton = true;
            this.tabPageAdv36.Size = new System.Drawing.Size(751, 0);
            this.tabPageAdv36.TabIndex = 36;
            this.tabPageAdv36.Text = "H";
            this.tabPageAdv36.ThemesEnabled = true;
            // 
            // tabPageAdv37
            // 
            this.tabPageAdv37.Image = null;
            this.tabPageAdv37.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv37.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv37.Name = "tabPageAdv37";
            this.tabPageAdv37.ShowCloseButton = true;
            this.tabPageAdv37.Size = new System.Drawing.Size(751, 0);
            this.tabPageAdv37.TabIndex = 37;
            this.tabPageAdv37.Text = "I";
            this.tabPageAdv37.ThemesEnabled = true;
            // 
            // tabPageAdv38
            // 
            this.tabPageAdv38.Image = null;
            this.tabPageAdv38.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv38.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv38.Name = "tabPageAdv38";
            this.tabPageAdv38.ShowCloseButton = true;
            this.tabPageAdv38.Size = new System.Drawing.Size(751, 0);
            this.tabPageAdv38.TabIndex = 38;
            this.tabPageAdv38.Text = "J";
            this.tabPageAdv38.ThemesEnabled = true;
            // 
            // tabPageAdv39
            // 
            this.tabPageAdv39.Image = null;
            this.tabPageAdv39.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv39.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv39.Name = "tabPageAdv39";
            this.tabPageAdv39.ShowCloseButton = true;
            this.tabPageAdv39.Size = new System.Drawing.Size(751, 0);
            this.tabPageAdv39.TabIndex = 39;
            this.tabPageAdv39.Text = "K";
            this.tabPageAdv39.ThemesEnabled = true;
            // 
            // tabPageAdv40
            // 
            this.tabPageAdv40.Image = null;
            this.tabPageAdv40.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv40.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv40.Name = "tabPageAdv40";
            this.tabPageAdv40.ShowCloseButton = true;
            this.tabPageAdv40.Size = new System.Drawing.Size(751, 0);
            this.tabPageAdv40.TabIndex = 40;
            this.tabPageAdv40.Text = "L";
            this.tabPageAdv40.ThemesEnabled = true;
            // 
            // tabPageAdv41
            // 
            this.tabPageAdv41.Image = null;
            this.tabPageAdv41.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv41.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv41.Name = "tabPageAdv41";
            this.tabPageAdv41.ShowCloseButton = true;
            this.tabPageAdv41.Size = new System.Drawing.Size(751, 0);
            this.tabPageAdv41.TabIndex = 41;
            this.tabPageAdv41.Text = "M";
            this.tabPageAdv41.ThemesEnabled = true;
            // 
            // tabPageAdv42
            // 
            this.tabPageAdv42.Image = null;
            this.tabPageAdv42.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv42.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv42.Name = "tabPageAdv42";
            this.tabPageAdv42.ShowCloseButton = true;
            this.tabPageAdv42.Size = new System.Drawing.Size(751, 0);
            this.tabPageAdv42.TabIndex = 42;
            this.tabPageAdv42.Text = "N";
            this.tabPageAdv42.ThemesEnabled = true;
            // 
            // tabPageAdv43
            // 
            this.tabPageAdv43.Image = null;
            this.tabPageAdv43.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv43.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv43.Name = "tabPageAdv43";
            this.tabPageAdv43.ShowCloseButton = true;
            this.tabPageAdv43.Size = new System.Drawing.Size(751, 0);
            this.tabPageAdv43.TabIndex = 43;
            this.tabPageAdv43.Text = "O";
            this.tabPageAdv43.ThemesEnabled = true;
            // 
            // tabPageAdv44
            // 
            this.tabPageAdv44.Image = null;
            this.tabPageAdv44.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv44.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv44.Name = "tabPageAdv44";
            this.tabPageAdv44.ShowCloseButton = true;
            this.tabPageAdv44.Size = new System.Drawing.Size(751, 0);
            this.tabPageAdv44.TabIndex = 44;
            this.tabPageAdv44.Text = "P";
            this.tabPageAdv44.ThemesEnabled = true;
            // 
            // tabPageAdv45
            // 
            this.tabPageAdv45.Image = null;
            this.tabPageAdv45.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv45.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv45.Name = "tabPageAdv45";
            this.tabPageAdv45.ShowCloseButton = true;
            this.tabPageAdv45.Size = new System.Drawing.Size(751, 0);
            this.tabPageAdv45.TabIndex = 45;
            this.tabPageAdv45.Text = "Q";
            this.tabPageAdv45.ThemesEnabled = true;
            // 
            // tabPageAdv46
            // 
            this.tabPageAdv46.Image = null;
            this.tabPageAdv46.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv46.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv46.Name = "tabPageAdv46";
            this.tabPageAdv46.ShowCloseButton = true;
            this.tabPageAdv46.Size = new System.Drawing.Size(751, 0);
            this.tabPageAdv46.TabIndex = 46;
            this.tabPageAdv46.Text = "R";
            this.tabPageAdv46.ThemesEnabled = true;
            // 
            // tabPageAdv47
            // 
            this.tabPageAdv47.Image = null;
            this.tabPageAdv47.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv47.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv47.Name = "tabPageAdv47";
            this.tabPageAdv47.ShowCloseButton = true;
            this.tabPageAdv47.Size = new System.Drawing.Size(751, 0);
            this.tabPageAdv47.TabIndex = 47;
            this.tabPageAdv47.Text = "S";
            this.tabPageAdv47.ThemesEnabled = true;
            // 
            // tabPageAdv48
            // 
            this.tabPageAdv48.Image = null;
            this.tabPageAdv48.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv48.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv48.Name = "tabPageAdv48";
            this.tabPageAdv48.ShowCloseButton = true;
            this.tabPageAdv48.Size = new System.Drawing.Size(751, 0);
            this.tabPageAdv48.TabIndex = 48;
            this.tabPageAdv48.Text = "T";
            this.tabPageAdv48.ThemesEnabled = true;
            // 
            // tabPageAdv49
            // 
            this.tabPageAdv49.Image = null;
            this.tabPageAdv49.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv49.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv49.Name = "tabPageAdv49";
            this.tabPageAdv49.ShowCloseButton = true;
            this.tabPageAdv49.Size = new System.Drawing.Size(751, 0);
            this.tabPageAdv49.TabIndex = 49;
            this.tabPageAdv49.Text = "U";
            this.tabPageAdv49.ThemesEnabled = true;
            // 
            // tabPageAdv50
            // 
            this.tabPageAdv50.Image = null;
            this.tabPageAdv50.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv50.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv50.Name = "tabPageAdv50";
            this.tabPageAdv50.ShowCloseButton = true;
            this.tabPageAdv50.Size = new System.Drawing.Size(751, 0);
            this.tabPageAdv50.TabIndex = 50;
            this.tabPageAdv50.Text = "V";
            this.tabPageAdv50.ThemesEnabled = true;
            // 
            // tabPageAdv51
            // 
            this.tabPageAdv51.Image = null;
            this.tabPageAdv51.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv51.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv51.Name = "tabPageAdv51";
            this.tabPageAdv51.ShowCloseButton = true;
            this.tabPageAdv51.Size = new System.Drawing.Size(751, 0);
            this.tabPageAdv51.TabIndex = 51;
            this.tabPageAdv51.Text = "W";
            this.tabPageAdv51.ThemesEnabled = true;
            // 
            // tabPageAdv52
            // 
            this.tabPageAdv52.Image = null;
            this.tabPageAdv52.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv52.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv52.Name = "tabPageAdv52";
            this.tabPageAdv52.ShowCloseButton = true;
            this.tabPageAdv52.Size = new System.Drawing.Size(751, 0);
            this.tabPageAdv52.TabIndex = 52;
            this.tabPageAdv52.Text = "X";
            this.tabPageAdv52.ThemesEnabled = true;
            // 
            // tabPageAdv53
            // 
            this.tabPageAdv53.Image = null;
            this.tabPageAdv53.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv53.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv53.Name = "tabPageAdv53";
            this.tabPageAdv53.ShowCloseButton = true;
            this.tabPageAdv53.Size = new System.Drawing.Size(751, 0);
            this.tabPageAdv53.TabIndex = 53;
            this.tabPageAdv53.Text = "Y";
            this.tabPageAdv53.ThemesEnabled = true;
            // 
            // tabPageAdv54
            // 
            this.tabPageAdv54.Image = null;
            this.tabPageAdv54.ImageSize = new System.Drawing.Size(16, 16);
            this.tabPageAdv54.Location = new System.Drawing.Point(3, 32);
            this.tabPageAdv54.Name = "tabPageAdv54";
            this.tabPageAdv54.ShowCloseButton = true;
            this.tabPageAdv54.Size = new System.Drawing.Size(751, 0);
            this.tabPageAdv54.TabIndex = 54;
            this.tabPageAdv54.Text = "Z";
            this.tabPageAdv54.ThemesEnabled = true;
            // 
            // designTimeTabTypeLoader
            // 
            this.designTimeTabTypeLoader.InvokeMemberName = "TabStyleName";
            // 
            // gridResultBundles
            // 
            this.gridResultBundles.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.gridResultBundles.ClipCurrentCellSelection = false;
            this.gridResultBundles.DataRowTemplate = this.dataRow1;
            this.gridResultBundles.DataRowTemplateStyles.Add(this.visualGridElementStyle1);
            this.gridResultBundles.DataRowTemplateStyles.Add(this.visualGridElementStyle3);
            // 
            // 
            // 
            this.gridResultBundles.ErrorVisualStyle.OverrideUIStyle = false;
            // 
            // 
            // 
            this.gridResultBundles.FixedColumnSplitter.AllowRepositioning = false;
            this.gridResultBundles.FixedColumnSplitter.Visible = false;
            this.gridResultBundles.FixedHeaderRows.Add(this.columnManagerRow1);
            this.gridResultBundles.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.gridResultBundles.GridLineColor = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(239)))), ((int)(((byte)(255)))));
            this.gridResultBundles.GridLineStyle = System.Drawing.Drawing2D.DashStyle.Dot;
            this.gridResultBundles.InactiveSelectionBackColor = System.Drawing.Color.RoyalBlue;
            // 
            // 
            // 
            this.gridResultBundles.InactiveSelectionVisualStyle.BackColor = System.Drawing.Color.Olive;
            this.gridResultBundles.InactiveSelectionVisualStyle.ForeColor = System.Drawing.Color.White;
            this.gridResultBundles.InactiveSelectionVisualStyle.OverrideUIStyle = true;
            this.gridResultBundles.Location = new System.Drawing.Point(12, 553);
            this.gridResultBundles.Margin = new System.Windows.Forms.Padding(6);
            this.gridResultBundles.Name = "gridResultBundles";
            this.gridResultBundles.OverrideUIStyle = false;
            this.gridResultBundles.ReadOnly = true;
            // 
            // 
            // 
            this.gridResultBundles.RowSelectorPane.AllowRowResize = false;
            this.gridResultBundles.RowSelectorPane.CurrentImageIndex = 16;
            gradientMap2.GradientMode = Xceed.UI.GradientMode.Horizontal;
            gradientStop3.Color = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(239)))), ((int)(((byte)(255)))));
            gradientStop3.Offset = 0D;
            gradientStop4.Color = System.Drawing.Color.FromArgb(((int)(((byte)(175)))), ((int)(((byte)(210)))), ((int)(((byte)(255)))));
            gradientStop4.Offset = 1D;
            gradientMap2.GradientStops.Add(gradientStop3);
            gradientMap2.GradientStops.Add(gradientStop4);
            this.gridResultBundles.RowSelectorPane.GradientMap = gradientMap2;
            // 
            // 
            // 
            this.gridResultBundles.RowSelectorPane.HotVisualStyle.OverrideUIStyle = true;
            this.gridResultBundles.RowSelectorPane.OverrideUIStyle = true;
            this.gridResultBundles.RowSelectorPane.SelectedImageIndex = 20;
            this.gridResultBundles.RowSelectorPane.Visible = false;
            this.gridResultBundles.RowSelectorPane.Width = 17;
            this.gridResultBundles.SelectionBackColor = System.Drawing.Color.Indigo;
            this.gridResultBundles.SelectionMode = System.Windows.Forms.SelectionMode.MultiSimple;
            // 
            // 
            // 
            this.gridResultBundles.SelectionVisualStyle.BackColor = System.Drawing.Color.Red;
            this.gridResultBundles.SelectionVisualStyle.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.gridResultBundles.SelectionVisualStyle.ForeColor = System.Drawing.Color.Yellow;
            this.gridResultBundles.SelectionVisualStyle.OverrideUIStyle = true;
            this.gridResultBundles.SelectionVisualStyle.VerticalAlignment = Xceed.Grid.VerticalAlignment.Center;
            this.gridResultBundles.SelectionVisualStyle.WordWrap = false;
            this.gridResultBundles.ShowFocusRectangle = false;
            // 
            // 
            // 
            this.gridResultBundles.SideMargin.Visible = false;
            this.gridResultBundles.Size = new System.Drawing.Size(1024, 162);
            this.gridResultBundles.SynchronizeDetailGrids = false;
            this.gridResultBundles.TabIndex = 31;
            this.gridResultBundles.TabStop = false;
            this.gridResultBundles.TreeLineColor = System.Drawing.Color.Silver;
            this.gridResultBundles.UIStyle = Xceed.UI.UIStyle.System;
            this.gridResultBundles.VerticalAlignment = Xceed.Grid.VerticalAlignment.Center;
            this.gridResultBundles.SelectedRowsChanged += new System.EventHandler(this.gridResultBundles_SelectedRowsChanged);
            this.gridResultBundles.KeyUp += new System.Windows.Forms.KeyEventHandler(this.gridResultBundles_KeyUp);
            // 
            // visualGridElementStyle1
            // 
            // 
            // 
            // 
            this.visualGridElementStyle1.HotVisualStyle.OverrideUIStyle = true;
            this.visualGridElementStyle1.OverrideUIStyle = false;
            // 
            // visualGridElementStyle3
            // 
            this.visualGridElementStyle3.BackColor = System.Drawing.Color.AliceBlue;
            // 
            // columnManagerRow1
            // 
            this.columnManagerRow1.AllowColumnReorder = false;
            this.columnManagerRow1.AllowColumnResize = true;
            this.columnManagerRow1.AllowSort = false;
            this.columnManagerRow1.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            gradientStop1.Color = System.Drawing.Color.FromArgb(((int)(((byte)(252)))), ((int)(((byte)(253)))), ((int)(((byte)(255)))));
            gradientStop1.Offset = 0D;
            gradientStop2.Color = System.Drawing.Color.FromArgb(((int)(((byte)(196)))), ((int)(((byte)(221)))), ((int)(((byte)(255)))));
            gradientStop2.Offset = 1D;
            gradientMap1.GradientStops.Add(gradientStop1);
            gradientMap1.GradientStops.Add(gradientStop2);
            this.columnManagerRow1.GradientMap = gradientMap1;
            this.columnManagerRow1.Height = 23;
            // 
            // 
            // 
            this.columnManagerRow1.HotVisualStyle.OverrideUIStyle = false;
            this.columnManagerRow1.OverrideUIStyle = true;
            // 
            // gridRecentUpdates
            // 
            this.gridRecentUpdates.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.gridRecentUpdates.ClipCurrentCellSelection = false;
            this.gridRecentUpdates.DataRowTemplate = this.dataRow2;
            this.gridRecentUpdates.DataRowTemplateStyles.Add(this.visualGridElementStyle4);
            this.gridRecentUpdates.DataRowTemplateStyles.Add(this.visualGridElementStyle5);
            // 
            // 
            // 
            this.gridRecentUpdates.ErrorVisualStyle.OverrideUIStyle = false;
            // 
            // 
            // 
            this.gridRecentUpdates.FixedColumnSplitter.AllowRepositioning = false;
            this.gridRecentUpdates.FixedColumnSplitter.Visible = false;
            this.gridRecentUpdates.FixedHeaderRows.Add(this.columnManagerRow3);
            this.gridRecentUpdates.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.gridRecentUpdates.GridLineColor = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(239)))), ((int)(((byte)(255)))));
            this.gridRecentUpdates.GridLineStyle = System.Drawing.Drawing2D.DashStyle.Dot;
            this.gridRecentUpdates.InactiveSelectionBackColor = System.Drawing.Color.RoyalBlue;
            // 
            // 
            // 
            this.gridRecentUpdates.InactiveSelectionVisualStyle.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(229)))), ((int)(((byte)(229)))), ((int)(((byte)(229)))));
            this.gridRecentUpdates.InactiveSelectionVisualStyle.ForeColor = System.Drawing.Color.Black;
            this.gridRecentUpdates.InactiveSelectionVisualStyle.OverrideUIStyle = true;
            this.gridRecentUpdates.Location = new System.Drawing.Point(782, 100);
            this.gridRecentUpdates.Margin = new System.Windows.Forms.Padding(6);
            this.gridRecentUpdates.Name = "gridRecentUpdates";
            this.gridRecentUpdates.OverrideUIStyle = false;
            this.gridRecentUpdates.ReadOnly = true;
            // 
            // 
            // 
            this.gridRecentUpdates.RowSelectorPane.AllowRowResize = false;
            this.gridRecentUpdates.RowSelectorPane.CurrentImageIndex = 16;
            gradientMap4.GradientMode = Xceed.UI.GradientMode.Horizontal;
            gradientStop7.Color = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(239)))), ((int)(((byte)(255)))));
            gradientStop7.Offset = 0D;
            gradientStop8.Color = System.Drawing.Color.FromArgb(((int)(((byte)(175)))), ((int)(((byte)(210)))), ((int)(((byte)(255)))));
            gradientStop8.Offset = 1D;
            gradientMap4.GradientStops.Add(gradientStop7);
            gradientMap4.GradientStops.Add(gradientStop8);
            this.gridRecentUpdates.RowSelectorPane.GradientMap = gradientMap4;
            // 
            // 
            // 
            this.gridRecentUpdates.RowSelectorPane.HotVisualStyle.OverrideUIStyle = true;
            this.gridRecentUpdates.RowSelectorPane.OverrideUIStyle = true;
            this.gridRecentUpdates.RowSelectorPane.SelectedImageIndex = 20;
            this.gridRecentUpdates.RowSelectorPane.Visible = false;
            this.gridRecentUpdates.RowSelectorPane.Width = 17;
            this.gridRecentUpdates.SelectionBackColor = System.Drawing.Color.Indigo;
            this.gridRecentUpdates.SelectionMode = System.Windows.Forms.SelectionMode.One;
            // 
            // 
            // 
            this.gridRecentUpdates.SelectionVisualStyle.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(167)))), ((int)(((byte)(205)))), ((int)(((byte)(240)))));
            this.gridRecentUpdates.SelectionVisualStyle.ForeColor = System.Drawing.Color.Black;
            this.gridRecentUpdates.SelectionVisualStyle.OverrideUIStyle = true;
            this.gridRecentUpdates.ShowFocusRectangle = false;
            // 
            // 
            // 
            this.gridRecentUpdates.SideMargin.Visible = false;
            this.gridRecentUpdates.Size = new System.Drawing.Size(447, 441);
            this.gridRecentUpdates.SynchronizeDetailGrids = false;
            this.gridRecentUpdates.TabIndex = 31;
            this.gridRecentUpdates.TabStop = false;
            this.gridRecentUpdates.TreeLineColor = System.Drawing.Color.Silver;
            this.gridRecentUpdates.UIStyle = Xceed.UI.UIStyle.System;
            this.gridRecentUpdates.KeyUp += new System.Windows.Forms.KeyEventHandler(this.gridRecentUpdates_KeyUp);
            // 
            // visualGridElementStyle4
            // 
            // 
            // 
            // 
            this.visualGridElementStyle4.HotVisualStyle.OverrideUIStyle = true;
            this.visualGridElementStyle4.OverrideUIStyle = false;
            // 
            // visualGridElementStyle5
            // 
            this.visualGridElementStyle5.BackColor = System.Drawing.Color.AliceBlue;
            // 
            // columnManagerRow3
            // 
            this.columnManagerRow3.AllowColumnReorder = false;
            this.columnManagerRow3.AllowColumnResize = true;
            this.columnManagerRow3.AllowSort = false;
            this.columnManagerRow3.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            gradientStop5.Color = System.Drawing.Color.FromArgb(((int)(((byte)(252)))), ((int)(((byte)(253)))), ((int)(((byte)(255)))));
            gradientStop5.Offset = 0D;
            gradientStop6.Color = System.Drawing.Color.FromArgb(((int)(((byte)(196)))), ((int)(((byte)(221)))), ((int)(((byte)(255)))));
            gradientStop6.Offset = 1D;
            gradientMap3.GradientStops.Add(gradientStop5);
            gradientMap3.GradientStops.Add(gradientStop6);
            this.columnManagerRow3.GradientMap = gradientMap3;
            this.columnManagerRow3.Height = 23;
            // 
            // 
            // 
            this.columnManagerRow3.HotVisualStyle.OverrideUIStyle = false;
            this.columnManagerRow3.OverrideUIStyle = true;
            // 
            // btnPrintDispatchSelectedBundle
            // 
            this.btnPrintDispatchSelectedBundle.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnPrintDispatchSelectedBundle.Appearance.Options.UseFont = true;
            this.btnPrintDispatchSelectedBundle.Image = global::LabMaestro.Controls.Win.Resources.print24;
            this.btnPrintDispatchSelectedBundle.Location = new System.Drawing.Point(1045, 625);
            this.btnPrintDispatchSelectedBundle.Name = "btnPrintDispatchSelectedBundle";
            this.btnPrintDispatchSelectedBundle.Size = new System.Drawing.Size(181, 40);
            this.btnPrintDispatchSelectedBundle.TabIndex = 4;
            this.btnPrintDispatchSelectedBundle.Text = "Print Selected Bundle(s)";
            this.btnPrintDispatchSelectedBundle.Click += new System.EventHandler(this.btnPrintDispatchSelectedBundle_Click);
            // 
            // btnPrintDispatchAllBundles
            // 
            this.btnPrintDispatchAllBundles.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnPrintDispatchAllBundles.Appearance.Options.UseFont = true;
            this.btnPrintDispatchAllBundles.Image = global::LabMaestro.Controls.Win.Resources.print_all24;
            this.btnPrintDispatchAllBundles.Location = new System.Drawing.Point(1045, 671);
            this.btnPrintDispatchAllBundles.Name = "btnPrintDispatchAllBundles";
            this.btnPrintDispatchAllBundles.Size = new System.Drawing.Size(181, 40);
            this.btnPrintDispatchAllBundles.TabIndex = 4;
            this.btnPrintDispatchAllBundles.Text = "Print All Bundles";
            this.btnPrintDispatchAllBundles.Click += new System.EventHandler(this.btnPrintDispatchAllBundles_Click);
            // 
            // labelControl9
            // 
            this.labelControl9.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl9.Appearance.ForeColor = System.Drawing.Color.Black;
            this.labelControl9.Location = new System.Drawing.Point(782, 82);
            this.labelControl9.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.labelControl9.Name = "labelControl9";
            this.labelControl9.Size = new System.Drawing.Size(120, 16);
            this.labelControl9.TabIndex = 38;
            this.labelControl9.Text = "Recently Updated:";
            // 
            // chkHideCollatedBundles
            // 
            this.chkHideCollatedBundles.AutoSizeInLayoutControl = true;
            this.chkHideCollatedBundles.Location = new System.Drawing.Point(1043, 552);
            this.chkHideCollatedBundles.MenuManager = this.barManager;
            this.chkHideCollatedBundles.Name = "chkHideCollatedBundles";
            this.chkHideCollatedBundles.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkHideCollatedBundles.Properties.Appearance.ForeColor = System.Drawing.Color.Black;
            this.chkHideCollatedBundles.Properties.Appearance.Options.UseFont = true;
            this.chkHideCollatedBundles.Properties.Appearance.Options.UseForeColor = true;
            this.chkHideCollatedBundles.Properties.Caption = "Hide collated bundles";
            this.chkHideCollatedBundles.Size = new System.Drawing.Size(183, 20);
            this.chkHideCollatedBundles.TabIndex = 43;
            this.chkHideCollatedBundles.CheckedChanged += new System.EventHandler(this.chkHideCollatedBundles_CheckedChanged);
            // 
            // panelControl2
            // 
            this.panelControl2.Appearance.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.panelControl2.Appearance.Options.UseBackColor = true;
            this.panelControl2.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.panelControl2.Controls.Add(this.labelControl10);
            this.panelControl2.Controls.Add(this.imgDueStatus);
            this.panelControl2.Controls.Add(this.lblInvoiceDue);
            this.panelControl2.Controls.Add(this.lblPatientId);
            this.panelControl2.Controls.Add(this.labelControl11);
            this.panelControl2.Controls.Add(this.lblInvoiceNum);
            this.panelControl2.Controls.Add(this.labelControl8);
            this.panelControl2.Location = new System.Drawing.Point(782, 41);
            this.panelControl2.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.UltraFlat;
            this.panelControl2.LookAndFeel.UseDefaultLookAndFeel = false;
            this.panelControl2.Name = "panelControl2";
            this.panelControl2.Size = new System.Drawing.Size(447, 34);
            this.panelControl2.TabIndex = 48;
            // 
            // labelControl10
            // 
            this.labelControl10.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl10.Appearance.ForeColor = System.Drawing.Color.DimGray;
            this.labelControl10.Location = new System.Drawing.Point(314, 7);
            this.labelControl10.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.labelControl10.Name = "labelControl10";
            this.labelControl10.Size = new System.Drawing.Size(22, 16);
            this.labelControl10.TabIndex = 8;
            this.labelControl10.Text = "Due";
            // 
            // imgDueStatus
            // 
            this.imgDueStatus.InitialImage = null;
            this.imgDueStatus.Location = new System.Drawing.Point(418, 4);
            this.imgDueStatus.Name = "imgDueStatus";
            this.imgDueStatus.Size = new System.Drawing.Size(24, 24);
            this.imgDueStatus.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.imgDueStatus.TabIndex = 7;
            this.imgDueStatus.TabStop = false;
            // 
            // lblInvoiceDue
            // 
            this.lblInvoiceDue.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblInvoiceDue.Appearance.ForeColor = System.Drawing.Color.Crimson;
            this.lblInvoiceDue.Location = new System.Drawing.Point(342, 7);
            this.lblInvoiceDue.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.lblInvoiceDue.Name = "lblInvoiceDue";
            this.lblInvoiceDue.Size = new System.Drawing.Size(50, 16);
            this.lblInvoiceDue.TabIndex = 6;
            this.lblInvoiceDue.Text = "9,99,999";
            // 
            // lblPatientId
            // 
            this.lblPatientId.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblPatientId.Appearance.ForeColor = System.Drawing.Color.RoyalBlue;
            this.lblPatientId.Location = new System.Drawing.Point(86, 7);
            this.lblPatientId.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.lblPatientId.Name = "lblPatientId";
            this.lblPatientId.Size = new System.Drawing.Size(26, 16);
            this.lblPatientId.TabIndex = 4;
            this.lblPatientId.Text = "W99";
            // 
            // labelControl11
            // 
            this.labelControl11.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl11.Appearance.ForeColor = System.Drawing.Color.DimGray;
            this.labelControl11.Location = new System.Drawing.Point(26, 7);
            this.labelControl11.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.labelControl11.Name = "labelControl11";
            this.labelControl11.Size = new System.Drawing.Size(54, 16);
            this.labelControl11.TabIndex = 3;
            this.labelControl11.Text = "Patient Id";
            // 
            // lblInvoiceNum
            // 
            this.lblInvoiceNum.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblInvoiceNum.Appearance.ForeColor = System.Drawing.Color.Indigo;
            this.lblInvoiceNum.Location = new System.Drawing.Point(217, 7);
            this.lblInvoiceNum.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.lblInvoiceNum.Name = "lblInvoiceNum";
            this.lblInvoiceNum.Size = new System.Drawing.Size(56, 16);
            this.lblInvoiceNum.TabIndex = 2;
            this.lblInvoiceNum.Text = "99999999";
            // 
            // labelControl8
            // 
            this.labelControl8.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl8.Appearance.ForeColor = System.Drawing.Color.DimGray;
            this.labelControl8.Location = new System.Drawing.Point(158, 7);
            this.labelControl8.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.labelControl8.Name = "labelControl8";
            this.labelControl8.Size = new System.Drawing.Size(53, 16);
            this.labelControl8.TabIndex = 1;
            this.labelControl8.Text = "Invoice #";
            // 
            // chkHideUnarchivedTests
            // 
            this.chkHideUnarchivedTests.Location = new System.Drawing.Point(922, 80);
            this.chkHideUnarchivedTests.MenuManager = this.barManager;
            this.chkHideUnarchivedTests.Name = "chkHideUnarchivedTests";
            this.chkHideUnarchivedTests.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkHideUnarchivedTests.Properties.Appearance.Options.UseFont = true;
            this.chkHideUnarchivedTests.Properties.Caption = "Hide unarchived tests?";
            this.chkHideUnarchivedTests.Size = new System.Drawing.Size(164, 20);
            this.chkHideUnarchivedTests.TabIndex = 53;
            this.chkHideUnarchivedTests.CheckedChanged += new System.EventHandler(this.chkHideUnarchivedTests_CheckedChanged);
            // 
            // chkSortOldestFirst
            // 
            this.chkSortOldestFirst.Location = new System.Drawing.Point(1092, 80);
            this.chkSortOldestFirst.MenuManager = this.barManager;
            this.chkSortOldestFirst.Name = "chkSortOldestFirst";
            this.chkSortOldestFirst.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkSortOldestFirst.Properties.Appearance.Options.UseFont = true;
            this.chkSortOldestFirst.Properties.Caption = "Sort by oldest first?";
            this.chkSortOldestFirst.Size = new System.Drawing.Size(146, 20);
            this.chkSortOldestFirst.TabIndex = 54;
            this.chkSortOldestFirst.CheckedChanged += new System.EventHandler(this.chkSortOldestFirst_CheckedChanged);
            // 
            // CollationDispatchDialog
            // 
            this.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 16F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1238, 722);
            this.Controls.Add(this.chkSortOldestFirst);
            this.Controls.Add(this.chkHideUnarchivedTests);
            this.Controls.Add(this.panelControl2);
            this.Controls.Add(this.chkHideCollatedBundles);
            this.Controls.Add(this.labelControl9);
            this.Controls.Add(this.tabFilter);
            this.Controls.Add(this.btnPrintDispatchAllBundles);
            this.Controls.Add(this.btnPrintDispatchSelectedBundle);
            this.Controls.Add(this.btnPreviewSelectedBundle);
            this.Controls.Add(this.gridResultBundles);
            this.Controls.Add(this.gridRecentUpdates);
            this.Controls.Add(this.gridOrders);
            this.Controls.Add(this.panelControl1);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.KeyPreview = true;
            this.LookAndFeel.SkinName = "Seven Classic";
            this.LookAndFeel.UseDefaultLookAndFeel = false;
            this.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "CollationDispatchDialog";
            this.SizeGripStyle = System.Windows.Forms.SizeGripStyle.Hide;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "Report Collation";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.CollationDialog_FormClosing);
            this.Load += new System.EventHandler(this.CollationDialog_Load);
            this.KeyUp += new System.Windows.Forms.KeyEventHandler(this.CollationDispatchDialog_KeyUp);
            ((System.ComponentModel.ISupportInitialize)(this.barManager)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.popupMenu)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).EndInit();
            this.panelControl1.ResumeLayout(false);
            this.panelControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cboBundleStatus.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dteBundleTo.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dteBundleTo.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dteBundleFrom.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dteBundleFrom.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkApplyFilter.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cboReportStatus.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtOrderTo.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtOrderTo.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtOrderFrom.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtOrderFrom.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtPatientTo.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtPatientTo.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtPatientFrom.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtPatientFrom.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPatientId.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtInvoiceNum.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridOrders)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataRowTemplate2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.columnManagerRow2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabFilter)).EndInit();
            this.tabFilter.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridResultBundles)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataRow1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.columnManagerRow1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridRecentUpdates)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataRow2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.columnManagerRow3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkHideCollatedBundles.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl2)).EndInit();
            this.panelControl2.ResumeLayout(false);
            this.panelControl2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.imgDueStatus)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkHideUnarchivedTests.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSortOldestFirst.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraBars.BarManager barManager;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraEditors.PanelControl panelControl1;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.DateEdit dtPatientFrom;
        private DevExpress.XtraEditors.TextEdit txtPatientId;
        private DevExpress.XtraEditors.TextEdit txtInvoiceNum;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.ComboBoxEdit cboReportStatus;
        private DevExpress.XtraEditors.DateEdit dtOrderTo;
        private DevExpress.XtraEditors.DateEdit dtOrderFrom;
        private DevExpress.XtraEditors.DateEdit dtPatientTo;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.SimpleButton btnSearchInvoiceId;
        private DevExpress.XtraEditors.CheckEdit chkApplyFilter;
        private DevExpress.XtraEditors.SimpleButton btnPreviewSelectedBundle;
        private DevExpress.XtraEditors.LabelControl lblFilterItems;
        private DevExpress.XtraEditors.SimpleButton btnSearchOrderStatus;
        private DevExpress.XtraEditors.SimpleButton btnSearchPatientId;
        private Xceed.Grid.GridControl gridOrders;
        private Xceed.Grid.DataRow dataRowTemplate2;
        private Xceed.Grid.VisualGridElementStyle visualGridElementStyle2;
        private Xceed.Grid.VisualGridElementStyle dataRowStyle1;
        private Xceed.Grid.ColumnManagerRow columnManagerRow2;
        private Syncfusion.Windows.Forms.Tools.TabControlAdv tabFilter;
        private Syncfusion.Reflection.TypeLoader designTimeTabTypeLoader;
        private Xceed.Grid.GridControl gridResultBundles;
        private Xceed.Grid.DataRow dataRow1;
        private Xceed.Grid.VisualGridElementStyle visualGridElementStyle1;
        private Xceed.Grid.VisualGridElementStyle visualGridElementStyle3;
        private Xceed.Grid.ColumnManagerRow columnManagerRow1;
        private Xceed.Grid.GridControl gridRecentUpdates;
        private Xceed.Grid.DataRow dataRow2;
        private Xceed.Grid.VisualGridElementStyle visualGridElementStyle4;
        private Xceed.Grid.VisualGridElementStyle visualGridElementStyle5;
        private Xceed.Grid.ColumnManagerRow columnManagerRow3;
        private DevExpress.XtraEditors.SimpleButton btnPrintDispatchAllBundles;
        private DevExpress.XtraEditors.SimpleButton btnPrintDispatchSelectedBundle;
        private DevExpress.XtraEditors.SimpleButton btnEditFilter;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv28;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv29;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv30;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv31;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv32;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv33;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv34;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv35;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv36;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv37;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv38;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv39;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv40;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv41;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv42;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv43;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv44;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv45;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv46;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv47;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv48;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv49;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv50;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv51;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv52;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv53;
        private Syncfusion.Windows.Forms.Tools.TabPageAdv tabPageAdv54;
        private DevExpress.XtraEditors.LabelControl labelControl9;
        private DevExpress.XtraEditors.SimpleButton btnClear;
        private DevExpress.XtraEditors.CheckEdit chkHideCollatedBundles;
        private DevExpress.XtraEditors.PanelControl panelControl2;
        private DevExpress.XtraEditors.LabelControl lblInvoiceNum;
        private DevExpress.XtraEditors.LabelControl labelControl8;
        private DevExpress.XtraEditors.LabelControl lblInvoiceDue;
        private DevExpress.XtraEditors.LabelControl lblPatientId;
        private DevExpress.XtraEditors.LabelControl labelControl11;
        private System.Windows.Forms.PictureBox imgDueStatus;
        private DevExpress.XtraEditors.LabelControl labelControl10;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController;
        private DevExpress.XtraBars.BarButtonItem btnExit;
        private DevExpress.XtraEditors.DropDownButton btnQuickDateRangePatientId;
        private DevExpress.XtraBars.PopupMenu popupMenu;
        private DevExpress.XtraBars.BarButtonItem miToday;
        private DevExpress.XtraBars.BarButtonItem miYesterday;
        private DevExpress.XtraBars.BarButtonItem miLast3Days;
        private DevExpress.XtraBars.BarButtonItem miLast1Week;
        private DevExpress.XtraEditors.DropDownButton btnQuickDateRangeOrderStatus;
        private DevExpress.XtraEditors.DropDownButton btnQuickDateRangeBundleStatus;
        private DevExpress.XtraEditors.SimpleButton btnSearchBundleStatus;
        private DevExpress.XtraEditors.ComboBoxEdit cboBundleStatus;
        private DevExpress.XtraEditors.DateEdit dteBundleTo;
        private DevExpress.XtraEditors.DateEdit dteBundleFrom;
        private DevExpress.XtraEditors.LabelControl labelControl12;
        private DevExpress.XtraEditors.LabelControl labelControl13;
        private DevExpress.XtraEditors.LabelControl labelControl14;
        private DevExpress.XtraEditors.LabelControl labelControl15;
        private DevExpress.XtraEditors.CheckEdit chkHideUnarchivedTests;
        private DevExpress.XtraEditors.CheckEdit chkSortOldestFirst;
    }
}