﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: AccountsReceivableDialog.cs 1363 2014-06-11 04:04:58Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using CuttingEdge.Conditions;
using DevExpress.XtraBars;
using DevExpress.XtraEditors;
using LabMaestro.BusinessLogic;
using LabMaestro.Controls.Win.Utils;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Infrastructure.Client.BackgroundJobs;
using LabMaestro.Printing;
using LabMaestro.Shared;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using Xceed.Grid;

namespace LabMaestro.Controls.Win;

public partial class AccountsReceivableDialog : XtraForm, IExecutableDialog
{
    private LabOrderFinancialContext _currentLabOrderContext;
    private DataCell _oldCell;
    private ILabOrderSearchProvider _searchProvider;
    private OrderLifecycleEventType _submitEvent = OrderLifecycleEventType.None;

    /// <summary>
    ///     Initializes a new instance of the AccountsReceivableDialog class.
    /// </summary>
    /// <param name="allowedTransactionOperations"></param>
    public AccountsReceivableDialog(TransactionOperationTypes allowedTransactionOperations,
        bool allowManualInvoicePrinting,
        bool enablePreviewPopupMenu) : this()
    {
        AllowedTransactionOperations = allowedTransactionOperations;
        AllowManualInvoicePrinting = allowManualInvoicePrinting;
        EnablePreviewPopupMenu = enablePreviewPopupMenu;
    }

    public AccountsReceivableDialog()
    {
        InitializeComponent();
        WinUtils.ApplyTheme(this);
        WinUtils.SetFormTitle(this, "Accounts Receivable");

        TransactionsHelper.CreateGridColumns(grdTransactionHistory);
        AllowedTransactionOperations = TransactionOperationTypes.None;
        SelectedTransactionType = InvoiceTransactionType.Payment;
        TransactionAmount = 0m;
        EnablePreviewPopupMenu = true;

        toggleNonCashPayment(false);
    }

    public bool AllowManualInvoicePrinting { get; set; }
    public bool EnablePreviewPopupMenu { get; set; }
    public TransactionOperationTypes AllowedTransactionOperations { get; set; }
    public InvoiceTransactionType SelectedTransactionType { get; private set; }
    public string TransactionRemarks { get; set; }
    public string PaymentReference { get; set; }
    public string PaymentSource { get; set; }
    public decimal TransactionAmount { get; private set; }
    public decimal NonCashAmount { get; private set; }
    public PaymentMethod SelectedPaymentMethod { get; private set; } = PaymentMethod.Cash;

    public bool ExecuteDialog(IWin32Window parent)
    {
        UpdateControls();
        ShowDialog(parent);
        return true;
    }

    public void UpdateControls()
    {
    }

    private bool hasActiveLabOrder() => _currentLabOrderContext is { InvoiceMaster: not null };

    private decimal getEnteredAmount()
    {
        var enteredAmount = 0m;
        var text = txtTransactionAmount.Text.Trim();

        if (string.IsNullOrEmpty(text))
        {
            return enteredAmount;
        }

        if (!decimal.TryParse(txtTransactionAmount.Text, out enteredAmount))
        {
            throw new Exception("Invalid amount entered!");
        }

        return enteredAmount;
    }

    private void updateBalances()
    {
        if (!hasActiveLabOrder())
        {
            txtTransactionAmount.Text = string.Empty;
            return;
        }

        decimal enteredAmount;
        try
        {
            enteredAmount = getEnteredAmount();
        }
        catch (Exception e)
        {
            MessageDlg.Error(e.Message);
            return;
        }

        var balanceAmount = SelectedTransactionType switch
        {
            InvoiceTransactionType.Payment => _currentLabOrderContext.InvoiceMaster.DueAmount - enteredAmount,
            InvoiceTransactionType.Refund => _currentLabOrderContext.InvoiceMaster.DueAmount + enteredAmount,
            InvoiceTransactionType.CashDiscount => _currentLabOrderContext.InvoiceMaster.DueAmount - enteredAmount,
            _ => 0m
        };

        lblBalanceDue.ForeColor = balanceAmount < 0 ? Color.Crimson : Color.Green;
        lblBalanceDue.Text = SharedUtilities.MoneyToString(balanceAmount);
    }

    private void rgTransactionType_SelectedIndexChanged(object sender, EventArgs e)
    {
        updateTransactionType(rgTransactionType.SelectedIndex);
        updateBalances();
    }

    private void toggleTransactionRelatedControls(bool enable)
    {
        rgTransactionType.Enabled = enable;
        rgTransactionType.SelectedIndex = -1;

        btnPerform.Enabled = enable;

        txtTransactionAmount.Text = string.Empty;
        txtTransactionAmount.Enabled = enable;

        txtUserRemarks.Text = string.Empty;
        txtUserRemarks.Enabled = enable;
    }

    /// <summary>
    ///     Determines if the invoice will allow further transactions. Returns
    ///     FALSE if no valid order is found, or the order workflow status is
    ///     fulfilled or the invoice has been paid in full.
    /// </summary>
    private bool invoiceWillAllowFurtherTransactions() =>
        hasActiveLabOrder() &&
        _currentLabOrderContext.WorkflowStage < WorkflowStageType.OrderFulfillment &&
        !_currentLabOrderContext.InvoiceMaster.IsInvoicePaidInFull();

    private bool transactionIsAllowed(TransactionOperationTypes tx) => (AllowedTransactionOperations & tx) == tx;

    private void updateTransactionRelatedControls()
    {
        toggleTransactionRelatedControls(false);
        if (!invoiceWillAllowFurtherTransactions()) return;

        if ((AllowedTransactionOperations & TransactionOperationTypes.None) == TransactionOperationTypes.None)
        {
            rgTransactionType.Properties.Items[(int)TransactionItem.Payment].Enabled = false;
            rgTransactionType.Properties.Items[(int)TransactionItem.Refund].Enabled = false;
            rgTransactionType.Properties.Items[(int)TransactionItem.Discount].Enabled = false;
            rgTransactionType.Properties.Items[(int)TransactionItem.DiscountRebate].Enabled = false;
            return;
        }

        toggleTransactionRelatedControls(true);
        rgTransactionType.Properties.Items[(int)TransactionItem.Payment].Enabled =
            transactionIsAllowed(TransactionOperationTypes.Payment);
        rgTransactionType.Properties.Items[(int)TransactionItem.Discount].Enabled =
            transactionIsAllowed(TransactionOperationTypes.Discount);
        rgTransactionType.Properties.Items[(int)TransactionItem.DiscountRebate].Enabled =
            transactionIsAllowed(TransactionOperationTypes.DiscountRebate);
        rgTransactionType.Properties.Items[(int)TransactionItem.Refund].Enabled =
            transactionIsAllowed(TransactionOperationTypes.Refund);
    }

    private void updateAllControls()
    {
        resetTransactionRelatedControls();
        updateTransactionRelatedControls();
        updateOrderDetails();
        updateTransactionsGrid();
        updateBalances();
        updateOtherControls();
    }

    private void updateOtherControls()
    {
        btnPrintInvoice.Enabled = AllowManualInvoicePrinting && hasActiveLabOrder();
        miPreviewOnly.Enabled = EnablePreviewPopupMenu;
    }

    private void updateTransactionType(int value)
    {
        if (!hasActiveLabOrder()) return;

        SelectedTransactionType = value switch
        {
            (int)TransactionItem.Payment => InvoiceTransactionType.Payment,
            (int)TransactionItem.Refund => InvoiceTransactionType.Refund,
            (int)TransactionItem.Discount => InvoiceTransactionType.CashDiscount,
            _ => SelectedTransactionType
        };
    }

    private void updateOrderDetails()
    {
        if (!hasActiveLabOrder()) return;

        lblInvoiceNum.Text = _currentLabOrderContext.InvoiceId > 0
            ? _currentLabOrderContext.InvoiceId.ToString()
            : "(pending)";
        lblOrderId.Text = _currentLabOrderContext.OrderId;
        lblPatientName.Text = _currentLabOrderContext.PatientName;
        lblReferrerName.Text = _currentLabOrderContext.PhysicianName;
        //lblOrderingUserName.Text = _currentLabOrderContext.OrderingUserName;
        lblOrderTime.Text = _currentLabOrderContext.OrderDateTime == DateTime.MinValue
            ? "Just now"
            : SharedUtilities.DateTimeToString(_currentLabOrderContext.OrderDateTime);
        lblNetPayable.Text = SharedUtilities.MoneyToString(_currentLabOrderContext.InvoiceMaster.NetPayable, false);
        lblAmountPaid.Text = SharedUtilities.MoneyToString(_currentLabOrderContext.InvoiceMaster.PaidAmount, false);
        lblCurrentDue.Text = SharedUtilities.MoneyToString(_currentLabOrderContext.InvoiceMaster.DueAmount, false);
    }

    private void resetTransactionRelatedControls()
    {
        lblAmountPaid.Text = string.Empty;
        lblBalanceDue.Text = string.Empty;
        lblCurrentDue.Text = string.Empty;
        lblInvoiceNum.Text = string.Empty;
        lblNetPayable.Text = string.Empty;
        lblOrderId.Text = string.Empty;
        lblOrderTime.Text = string.Empty;
        lblOrderingUserName.Text = string.Empty;
        lblPatientName.Text = string.Empty;
        lblReferrerName.Text = string.Empty;
        txtTransactionAmount.Text = string.Empty;
        txtUserRemarks.Text = string.Empty;

        grdTransactionHistory.DataRows.Clear();
    }

    private void updateTransactionsGrid()
    {
        if (hasActiveLabOrder())
            TransactionsHelper.UpdateTransactionsGrid(
                grdTransactionHistory,
                _currentLabOrderContext.Transactions,
                hasActiveLabOrder(),
                showToolTip
            );
    }

    private void showToolTip(object sender, EventArgs e)
    {
        var cell = (DataCell)sender;
        if (_oldCell == cell) return;

        _oldCell = cell;
        var content = _oldCell.Value;
        toolTip.SetToolTip(grdTransactionHistory, (content != null) ? content.ToString() : string.Empty);
    }

    private void resetTransactionAmounts()
    {
        TransactionAmount = 0m;
        NonCashAmount = 0m;
    }

    private void validateUserInput()
    {
        if (!hasActiveLabOrder())
            throw new Exception("Lab order was not selected or has been canceled");

        if (rgTransactionType.SelectedIndex == -1)
            throw new Exception("Please select the appropriate Transaction Type.");

        updateTransactionType(rgTransactionType.SelectedIndex);

        if (chkNonCashPayment.Checked)
        {
            if (rgPaymentMethod.SelectedIndex == -1)
                throw new Exception("Please select the appropriate Payment Method.");

            updatePaymentMethod(rgPaymentMethod.SelectedIndex);
            if (SelectedPaymentMethod != PaymentMethod.Cash)
            {
                PaymentSource = txtPaymentSource.Text.Trim();
                PaymentReference = txtPaymentRef.Text.Trim();
            }
        }

        if (string.IsNullOrEmpty(txtTransactionAmount.Text.Trim()))
            throw new Exception("Transaction Amount cannot be empty!\r\nPlease enter the desired amount.");

        var txAmount = getEnteredAmount();

        // TODO: more stringent validation
        Condition.Requires(txAmount, @"Transaction Amount")
            .IsGreaterThan(0m)
            .IsLessOrEqual(_currentLabOrderContext.InvoiceMaster.NetPayable);

        resetTransactionAmounts();

        switch (SelectedPaymentMethod)
        {
            case PaymentMethod.Cash:
                TransactionAmount = txAmount;
                break;
            default:
                NonCashAmount = txAmount;
                break;
        }

        TransactionRemarks = txtUserRemarks.Text.Trim();

        switch (SelectedTransactionType)
        {
            case InvoiceTransactionType.Payment:
                var totalPaid = _currentLabOrderContext.InvoiceMaster.PaidAmount + txAmount;
                if (totalPaid > _currentLabOrderContext.InvoiceMaster.NetPayable)
                {
                    throw new Exception("Transaction Amount + Paid-up Amount > Net Payable");
                }
                _submitEvent = OrderLifecycleEventType.FinancePayment;

                break;
            case InvoiceTransactionType.Refund:
                if (txAmount > _currentLabOrderContext.InvoiceMaster.PaidAmount)
                {
                    throw new Exception("Transaction Amount > Paid-up Amount");
                }
                _submitEvent = OrderLifecycleEventType.FinanceRefund;

                break;
            case InvoiceTransactionType.CashDiscount:
                if (txAmount > _currentLabOrderContext.InvoiceMaster.DueAmount)
                {
                    throw new Exception("Transaction Amount > Due");
                }
                _submitEvent = OrderLifecycleEventType.FinanceDiscount;

                break;
        }
    }

    private void updatePaymentMethod(int value)
    {
        SelectedPaymentMethod = value switch
        {
            1 => PaymentMethod.Card,
            2 => PaymentMethod.Mobile,
            3 => PaymentMethod.Cheque,
            4 => PaymentMethod.BankTransfer,
            _ => PaymentMethod.Cash
        };
    }

    private void txtTransactionAmount_EditValueChanged(object sender, EventArgs e) => updateBalances();

    private bool performTransaction()
    {
        var success = false;

        try
        {
            validateUserInput();
        }
        catch (Exception exc)
        {
            MessageDlg.Error(exc.Message);
            return false;
        }

        try
        {
            WaitFormControl.WaitOperation(this,
                () =>
                {
                    success = createTransaction();
                    if (_submitEvent != OrderLifecycleEventType.None)
                    {
                        var evt = OrderLifecycleEvent.On(_submitEvent).ForInvoice(_currentLabOrderContext.InvoiceId);
                        ApiClient.For<IBackgroundJobs>().ProcessLifecycleEvent(evt).Wait();
                    }
                },
                "Performing transaction...");
        }
        catch (Exception exc)
        {
            FaultHandler.HandleNonFatalException(exc);
            return false;
        }

        return success;
    }

    private bool createTransaction()
    {
        var manager = new InvoiceTransactionsManager(
            PatientLabOrdersRepository.FindById(_currentLabOrderContext.InvoiceId)
        );

        var success = manager.RecordTransaction(
            SelectedTransactionType,
            TransactionFlag.None,
            TransactionAmount,
            TransactionRemarks,
            NonCashAmount,
            SelectedPaymentMethod,
            PaymentSource,
            PaymentReference
        );
        manager.ApplyChanges();
        return success;
    }

    private void printCurrentInvoice(bool showPreview, string reason)
    {
        if (!hasActiveLabOrder())
        {
            MessageDlg.Error("No lab order loaded!");
            return;
        }

        var evType = AuditEventType.ordInvoicePrinted;
        if (showPreview)
        {
            // show the preview / print dialog and let the user manually print
            new PreviewPrintDialog(_currentLabOrderContext.InvoiceId, false, reason).ExecuteDialog(this);
            evType = AuditEventType.ordInvoicePreviewed;
        }
        else
        {
            // TODO: batch print the items: invoice and other req. foils
            Tuple<InvoicePrintDto, List<InvoiceRequisitionFoilsPackage>> packages = null;
            WaitFormControl.WaitOperation(this,
                () => packages =
                    ReqSlipBundleCompiler.BuildInvoiceReqBundlesChain(_currentLabOrderContext.InvoiceId, true));
            var invoice = packages.Item1;
            if (!string.IsNullOrEmpty(reason))
                invoice.ReprintReason = reason;
            PrintHelper.PrintInvoice(false, invoice);
        }

        FaultHandler.Shield(() => AuditTrailRepository.LogLabOrderEvent(_currentLabOrderContext.InvoiceId, evType));
    }

    private void performAndPrint(bool showPreview)
    {
        var success = performTransaction();

        if (!success) return;

        // TODO: Print directly - Invoice, Req foils
        printCurrentInvoice(showPreview, null);

        resetCashlessControls(false);
        fetchAndUpdateInvoiceData();

        // if the invoice is fully paid, focus the search text box
        if (!invoiceWillAllowFurtherTransactions())
        {
            focusInvoiceSearchTextBox();
        }
    }

    private void resetCashlessControls(bool enable)
    {
        txtPaymentRef.Text = string.Empty;
        txtPaymentSource.Text = string.Empty;
        rgPaymentMethod.SelectedIndex = -1;
        chkNonCashPayment.Checked = enable;
        updatePaymentMethod(-1);
    }

    private void btnPerform_Click(object sender, EventArgs e) => performAndPrint(false);

    private void miPreviewOnly_ItemClick(object sender, ItemClickEventArgs e) => performAndPrint(true);

    private void AccountsReceivableDialog_Load(object sender, EventArgs e)
    {
        updateAllControls();
        txtInvoiceId.Select();
    }

    private void btnSearchInvoiceId_Click(object sender, EventArgs e)
    {
        txtPatientId.Text = string.Empty;
        searchByInvoiceId();
    }

    private void searchByInvoiceId()
    {
        var text = txtInvoiceId.Text.Trim();
        if (string.IsNullOrEmpty(text)) return;

        long invoiceId = -1;
        if (!long.TryParse(text, out invoiceId) || (invoiceId <= 0))
        {
            MessageDlg.Error("Invalid invoice number specified!");
            txtInvoiceId.Select();
            return;
        }

        FaultHandler.Shield(PatientLabOrdersRepository.Reset);
        _searchProvider = new ActiveLabOrderSearchByInvoiceIdProvider(invoiceId);
        fetchAndUpdateInvoiceData();
    }

    private void searchByPatientIdDateRange()
    {
        var oid = txtPatientId.Text.Trim().ToUpper();
        if (string.IsNullOrEmpty(oid) || dteSearchDate.EditValue == null)
        {
            MessageDlg.Error("Invalid order ID or search date provided!");
            return;
        }

        var dtFrom = dteSearchDate.DateTime.Date;
        var dtTo = SharedUtilities.LastInstantOfDay(dtFrom);

        FaultHandler.Shield(PatientLabOrdersRepository.Reset);
        _searchProvider = new ActiveLabOrderSearchByPatientIdAndDateRangeProvider(oid, dtFrom, dtTo);
        fetchAndUpdateInvoiceData();
    }

    private List<SearchedLabOrderInfo> performLabOrderSearchOperation()
    {
        List<SearchedLabOrderInfo> orders = null;

        SuspendLayout();
        FaultHandler.Shield(() => WaitFormControl.WaitOperation(this,
            () => { orders = (List<SearchedLabOrderInfo>)_searchProvider.SearchLabOrders(); },
            "Searching lab orders..."));
        ResumeLayout();

        return orders;
    }

    private void fetchAndUpdateInvoiceData()
    {
        _currentLabOrderContext = null;
        _submitEvent = OrderLifecycleEventType.None;

        if (_searchProvider != null)
        {
            var orders = performLabOrderSearchOperation();
            if (orders is { Count: 1 }) _currentLabOrderContext = LabOrderFinancialContext.AssembleFrom(orders[0]);
        }

        updateAllControls();

        if (invoiceWillAllowFurtherTransactions())
        {
            selectFirstAvailableTransactionType();

            txtTransactionAmount.Select();
            txtTransactionAmount.Focus();
        }
    }

    private void selectFirstAvailableTransactionType()
    {
        if (rgTransactionType.Properties.Items[(int)TransactionItem.Payment].Enabled)
        {
            rgTransactionType.SelectedIndex = 0;
        }
        else if (rgTransactionType.Properties.Items[(int)TransactionItem.Discount].Enabled)
        {
            rgTransactionType.SelectedIndex = 1;
        }
        else if (rgTransactionType.Properties.Items[(int)TransactionItem.Refund].Enabled)
        {
            rgTransactionType.SelectedIndex = 2;
        }
    }

    private void txtInvoiceId_KeyUp(object sender, KeyEventArgs e)
    {
        if (e.KeyCode == Keys.Enter)
        {
            e.Handled = true;
            searchByInvoiceId();
        }
    }

    private void AccountsReceivableDialog_KeyUp(object sender, KeyEventArgs e)
    {
        if (e.KeyCode == Keys.F5)
        {
            e.Handled = true;
            focusInvoiceSearchTextBox();
        }
    }

    private void focusInvoiceSearchTextBox()
    {
        txtInvoiceId.Focus();
        txtInvoiceId.Select();
        txtInvoiceId.SelectAll();
    }

    private void btnPrintInvoice_Click(object sender, EventArgs e)
    {
        var reason = InputDialog.ExecuteDialog(this, "Specify reason for reprint:");
        if (string.IsNullOrEmpty(reason))
        {
            MessageDlg.Warning("A valid reason must be specified to perform reprint.\nOperation aborted.");
            return;
        }

        printCurrentInvoice(true, reason);
    }

    private void btnExit_ItemClick(object sender, ItemClickEventArgs e) => Close();

    private void txtTransactionAmount_KeyUp(object sender, KeyEventArgs e)
    {
        if (e.KeyCode == Keys.Enter)
        {
            e.Handled = true;
            performAndPrint(false);
        }
    }

    private void txtUserRemarks_KeyUp(object sender, KeyEventArgs e)
    {
        if (e.KeyCode == Keys.Enter)
        {
            e.Handled = true;
            performAndPrint(false);
        }
    }

    private void btnClear_Click(object sender, EventArgs e)
    {
        txtInvoiceId.Text = "";
        txtPatientId.Text = "";
        dteSearchDate.DateTime = DateTime.Now;

        focusInvoiceSearchTextBox();
    }

    private void btnSearchPatientId_Click(object sender, EventArgs e)
    {
        txtInvoiceId.Text = "";
        searchByPatientIdDateRange();
    }

    private void btnShiftReport_ItemClick(object sender, ItemClickEventArgs e)
    {
        if (!UserWorkshiftHelper.UserHasOpenWorkShift())
        {
            MessageDlg.Warning("You do not have an active work shift!");
            return;
        }

        new ShiftDetailsDialog(CurrentUserContext.WorkShiftId, false).ExecuteDialog(this);
    }

    private void chkNonCashPayment_CheckedChanged(object sender, EventArgs e) =>
        toggleNonCashPayment(chkNonCashPayment.Checked);

    private void toggleNonCashPayment(bool enabled)
    {
        rgPaymentMethod.Enabled = enabled;
        txtPaymentSource.Enabled = enabled;
        txtPaymentRef.Enabled = enabled;
    }
}