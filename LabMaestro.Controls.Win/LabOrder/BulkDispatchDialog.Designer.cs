﻿namespace LabMaestro.Controls.Win
{
    partial class BulkDispatchDialog
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.txtBundleSearch = new DevExpress.XtraEditors.TextEdit();
            this.btnSearch = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.grdBundles = new DevExpress.XtraGrid.GridControl();
            this.bulkDispatchableResultBundleInfoBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colResultBundleId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colResultBundleTitle = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colInvoiceId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colOrderId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colOrderDateTime = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colPatientName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colResultBundlesCount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.chkConfirmDispatch = new DevExpress.XtraEditors.CheckEdit();
            this.btnBulkDispatch = new DevExpress.XtraEditors.SimpleButton();
            this.btnExit = new DevExpress.XtraEditors.SimpleButton();
            this.btnClear = new DevExpress.XtraEditors.SimpleButton();
            this.btnDelete = new DevExpress.XtraEditors.SimpleButton();
            ((System.ComponentModel.ISupportInitialize)(this.txtBundleSearch.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdBundles)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.bulkDispatchableResultBundleInfoBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkConfirmDispatch.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // txtBundleSearch
            // 
            this.txtBundleSearch.EditValue = "";
            this.txtBundleSearch.Location = new System.Drawing.Point(77, 9);
            this.txtBundleSearch.Name = "txtBundleSearch";
            this.txtBundleSearch.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtBundleSearch.Properties.Appearance.Options.UseFont = true;
            this.txtBundleSearch.Size = new System.Drawing.Size(184, 30);
            this.txtBundleSearch.TabIndex = 0;
            this.txtBundleSearch.KeyUp += new System.Windows.Forms.KeyEventHandler(this.txtBundleSearch_KeyUp);
            // 
            // btnSearch
            // 
            this.btnSearch.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnSearch.Appearance.Options.UseFont = true;
            this.btnSearch.Image = global::LabMaestro.Controls.Win.Resources.search_16;
            this.btnSearch.Location = new System.Drawing.Point(267, 8);
            this.btnSearch.Name = "btnSearch";
            this.btnSearch.Size = new System.Drawing.Size(80, 31);
            this.btnSearch.TabIndex = 1;
            this.btnSearch.Text = "Search";
            this.btnSearch.Click += new System.EventHandler(this.btnSearch_Click);
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Location = new System.Drawing.Point(12, 16);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(59, 16);
            this.labelControl1.TabIndex = 2;
            this.labelControl1.Text = "Report ID:";
            // 
            // grdBundles
            // 
            this.grdBundles.DataSource = this.bulkDispatchableResultBundleInfoBindingSource;
            this.grdBundles.Location = new System.Drawing.Point(12, 52);
            this.grdBundles.MainView = this.gridView1;
            this.grdBundles.Name = "grdBundles";
            this.grdBundles.Size = new System.Drawing.Size(708, 381);
            this.grdBundles.TabIndex = 3;
            this.grdBundles.TabStop = false;
            this.grdBundles.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            // 
            // bulkDispatchableResultBundleInfoBindingSource
            // 
            this.bulkDispatchableResultBundleInfoBindingSource.DataSource = typeof(LabMaestro.BusinessLogic.BulkDispatchableResultBundleInfo);
            // 
            // gridView1
            // 
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colResultBundleId,
            this.colResultBundleTitle,
            this.colInvoiceId,
            this.colOrderId,
            this.colOrderDateTime,
            this.colPatientName,
            this.colResultBundlesCount});
            this.gridView1.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFullFocus;
            this.gridView1.GridControl = this.grdBundles;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsView.EnableAppearanceEvenRow = true;
            this.gridView1.OptionsView.ShowFilterPanelMode = DevExpress.XtraGrid.Views.Base.ShowFilterPanelMode.Never;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            this.gridView1.PaintStyleName = "Skin";
            // 
            // colResultBundleId
            // 
            this.colResultBundleId.Caption = "Id";
            this.colResultBundleId.FieldName = "ResultBundleId";
            this.colResultBundleId.Name = "colResultBundleId";
            this.colResultBundleId.OptionsColumn.AllowEdit = false;
            this.colResultBundleId.OptionsColumn.AllowFocus = false;
            this.colResultBundleId.OptionsColumn.AllowIncrementalSearch = false;
            this.colResultBundleId.OptionsColumn.AllowMove = false;
            this.colResultBundleId.OptionsColumn.AllowShowHide = false;
            this.colResultBundleId.OptionsColumn.ReadOnly = true;
            this.colResultBundleId.Visible = true;
            this.colResultBundleId.VisibleIndex = 0;
            // 
            // colResultBundleTitle
            // 
            this.colResultBundleTitle.Caption = "Report";
            this.colResultBundleTitle.FieldName = "ResultBundleTitle";
            this.colResultBundleTitle.Name = "colResultBundleTitle";
            this.colResultBundleTitle.OptionsColumn.AllowEdit = false;
            this.colResultBundleTitle.OptionsColumn.AllowFocus = false;
            this.colResultBundleTitle.OptionsColumn.AllowIncrementalSearch = false;
            this.colResultBundleTitle.OptionsColumn.AllowMove = false;
            this.colResultBundleTitle.OptionsColumn.AllowShowHide = false;
            this.colResultBundleTitle.OptionsColumn.ReadOnly = true;
            this.colResultBundleTitle.Visible = true;
            this.colResultBundleTitle.VisibleIndex = 1;
            // 
            // colInvoiceId
            // 
            this.colInvoiceId.FieldName = "InvoiceId";
            this.colInvoiceId.Name = "colInvoiceId";
            this.colInvoiceId.OptionsColumn.AllowEdit = false;
            this.colInvoiceId.OptionsColumn.AllowFocus = false;
            this.colInvoiceId.OptionsColumn.AllowIncrementalSearch = false;
            this.colInvoiceId.OptionsColumn.AllowMove = false;
            this.colInvoiceId.OptionsColumn.AllowShowHide = false;
            this.colInvoiceId.OptionsColumn.ReadOnly = true;
            this.colInvoiceId.Visible = true;
            this.colInvoiceId.VisibleIndex = 3;
            // 
            // colOrderId
            // 
            this.colOrderId.FieldName = "OrderId";
            this.colOrderId.Name = "colOrderId";
            this.colOrderId.OptionsColumn.AllowEdit = false;
            this.colOrderId.OptionsColumn.AllowFocus = false;
            this.colOrderId.OptionsColumn.AllowIncrementalSearch = false;
            this.colOrderId.OptionsColumn.AllowMove = false;
            this.colOrderId.OptionsColumn.AllowShowHide = false;
            this.colOrderId.OptionsColumn.ReadOnly = true;
            this.colOrderId.Visible = true;
            this.colOrderId.VisibleIndex = 4;
            // 
            // colOrderDateTime
            // 
            this.colOrderDateTime.Caption = "Order Date";
            this.colOrderDateTime.FieldName = "OrderDateTime";
            this.colOrderDateTime.Name = "colOrderDateTime";
            this.colOrderDateTime.OptionsColumn.AllowEdit = false;
            this.colOrderDateTime.OptionsColumn.AllowFocus = false;
            this.colOrderDateTime.OptionsColumn.AllowIncrementalSearch = false;
            this.colOrderDateTime.OptionsColumn.AllowMove = false;
            this.colOrderDateTime.OptionsColumn.AllowShowHide = false;
            this.colOrderDateTime.OptionsColumn.ReadOnly = true;
            this.colOrderDateTime.Visible = true;
            this.colOrderDateTime.VisibleIndex = 5;
            // 
            // colPatientName
            // 
            this.colPatientName.FieldName = "PatientName";
            this.colPatientName.Name = "colPatientName";
            this.colPatientName.OptionsColumn.AllowEdit = false;
            this.colPatientName.OptionsColumn.AllowFocus = false;
            this.colPatientName.OptionsColumn.AllowIncrementalSearch = false;
            this.colPatientName.OptionsColumn.AllowMove = false;
            this.colPatientName.OptionsColumn.AllowShowHide = false;
            this.colPatientName.OptionsColumn.ReadOnly = true;
            this.colPatientName.Visible = true;
            this.colPatientName.VisibleIndex = 6;
            // 
            // colResultBundlesCount
            // 
            this.colResultBundlesCount.Caption = "Total # of Reports";
            this.colResultBundlesCount.FieldName = "ResultBundlesCount";
            this.colResultBundlesCount.Name = "colResultBundlesCount";
            this.colResultBundlesCount.OptionsColumn.AllowEdit = false;
            this.colResultBundlesCount.OptionsColumn.AllowFocus = false;
            this.colResultBundlesCount.OptionsColumn.AllowIncrementalSearch = false;
            this.colResultBundlesCount.OptionsColumn.AllowMove = false;
            this.colResultBundlesCount.OptionsColumn.AllowShowHide = false;
            this.colResultBundlesCount.OptionsColumn.ReadOnly = true;
            this.colResultBundlesCount.Visible = true;
            this.colResultBundlesCount.VisibleIndex = 2;
            // 
            // chkConfirmDispatch
            // 
            this.chkConfirmDispatch.Enabled = false;
            this.chkConfirmDispatch.Location = new System.Drawing.Point(522, 439);
            this.chkConfirmDispatch.Name = "chkConfirmDispatch";
            this.chkConfirmDispatch.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkConfirmDispatch.Properties.Appearance.Options.UseFont = true;
            this.chkConfirmDispatch.Properties.Caption = "Dispatch the selected reports";
            this.chkConfirmDispatch.Size = new System.Drawing.Size(198, 20);
            this.chkConfirmDispatch.TabIndex = 5;
            this.chkConfirmDispatch.CheckedChanged += new System.EventHandler(this.chkConfirmDispatch_CheckedChanged);
            // 
            // btnBulkDispatch
            // 
            this.btnBulkDispatch.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnBulkDispatch.Appearance.Options.UseFont = true;
            this.btnBulkDispatch.Enabled = false;
            this.btnBulkDispatch.Image = global::LabMaestro.Controls.Win.Resources.mail24;
            this.btnBulkDispatch.Location = new System.Drawing.Point(524, 465);
            this.btnBulkDispatch.Name = "btnBulkDispatch";
            this.btnBulkDispatch.Size = new System.Drawing.Size(196, 45);
            this.btnBulkDispatch.TabIndex = 6;
            this.btnBulkDispatch.Text = "Bulk Dispatch";
            this.btnBulkDispatch.Click += new System.EventHandler(this.btnBulkDispatch_Click);
            // 
            // btnExit
            // 
            this.btnExit.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnExit.Appearance.Options.UseFont = true;
            this.btnExit.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnExit.Image = global::LabMaestro.Controls.Win.Resources.exit_16;
            this.btnExit.Location = new System.Drawing.Point(12, 487);
            this.btnExit.Name = "btnExit";
            this.btnExit.Size = new System.Drawing.Size(80, 23);
            this.btnExit.TabIndex = 7;
            this.btnExit.Text = "Exit";
            // 
            // btnClear
            // 
            this.btnClear.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnClear.Appearance.Options.UseFont = true;
            this.btnClear.Image = global::LabMaestro.Controls.Win.Resources.clear;
            this.btnClear.Location = new System.Drawing.Point(12, 436);
            this.btnClear.Name = "btnClear";
            this.btnClear.Size = new System.Drawing.Size(80, 23);
            this.btnClear.TabIndex = 3;
            this.btnClear.Text = "Clear";
            this.btnClear.ToolTip = "Clear all reports";
            this.btnClear.Click += new System.EventHandler(this.btnClear_Click);
            // 
            // btnDelete
            // 
            this.btnDelete.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnDelete.Appearance.Options.UseFont = true;
            this.btnDelete.Enabled = false;
            this.btnDelete.Image = global::LabMaestro.Controls.Win.Resources.delete_16;
            this.btnDelete.Location = new System.Drawing.Point(98, 436);
            this.btnDelete.Name = "btnDelete";
            this.btnDelete.Size = new System.Drawing.Size(80, 23);
            this.btnDelete.TabIndex = 4;
            this.btnDelete.Text = "Delete";
            this.btnDelete.ToolTip = "Delete selected report";
            this.btnDelete.Click += new System.EventHandler(this.btnDelete_Click);
            // 
            // BulkDispatchDialog
            // 
            this.Appearance.Options.UseFont = true;
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.ClientSize = new System.Drawing.Size(732, 522);
            this.Controls.Add(this.btnDelete);
            this.Controls.Add(this.btnClear);
            this.Controls.Add(this.btnExit);
            this.Controls.Add(this.btnBulkDispatch);
            this.Controls.Add(this.chkConfirmDispatch);
            this.Controls.Add(this.grdBundles);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.btnSearch);
            this.Controls.Add(this.txtBundleSearch);
            this.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.KeyPreview = true;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "BulkDispatchDialog";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "BulkDispatchDialog";
            this.Load += new System.EventHandler(this.BulkDispatchDialog_Load);
            this.KeyUp += new System.Windows.Forms.KeyEventHandler(this.BulkDispatchDialog_KeyUp);
            ((System.ComponentModel.ISupportInitialize)(this.txtBundleSearch.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdBundles)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.bulkDispatchableResultBundleInfoBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkConfirmDispatch.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.TextEdit txtBundleSearch;
        private DevExpress.XtraEditors.SimpleButton btnSearch;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraGrid.GridControl grdBundles;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraEditors.CheckEdit chkConfirmDispatch;
        private DevExpress.XtraEditors.SimpleButton btnBulkDispatch;
        private System.Windows.Forms.BindingSource bulkDispatchableResultBundleInfoBindingSource;
        private DevExpress.XtraGrid.Columns.GridColumn colResultBundleId;
        private DevExpress.XtraGrid.Columns.GridColumn colInvoiceId;
        private DevExpress.XtraGrid.Columns.GridColumn colOrderId;
        private DevExpress.XtraGrid.Columns.GridColumn colOrderDateTime;
        private DevExpress.XtraGrid.Columns.GridColumn colPatientName;
        private DevExpress.XtraGrid.Columns.GridColumn colResultBundleTitle;
        private DevExpress.XtraGrid.Columns.GridColumn colResultBundlesCount;
        private DevExpress.XtraEditors.SimpleButton btnExit;
        private DevExpress.XtraEditors.SimpleButton btnClear;
        private DevExpress.XtraEditors.SimpleButton btnDelete;
    }
}