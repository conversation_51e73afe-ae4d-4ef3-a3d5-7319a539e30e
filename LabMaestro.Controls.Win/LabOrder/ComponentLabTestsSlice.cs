﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the organization nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ComponentLabTestsSlice.cs 1053 2013-10-30 17:19:53Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using LabMaestro.Domain;
using LabMaestro.Shared;

namespace LabMaestro.Controls.Win
{
    public sealed class ComponentLabTestsSlice
    {
        public string Id { get; set; }
        public string LabTestId { get; set; }
        public string TestName { get; set; }
        public string LabName { get; set; }
        public string UnitPrice { get; set; }
        public bool IsCancelled { get; set; }
        public string DateCreated { get; set; }
        public string LastModified { get; set; }
        public string ResultsETA { get; set; }
        public string TestWorkflowStage { get; set; }
        public string BundleId { get; set; }
        public bool BundleIsCancelled { get; set; }
        public string BundleResultType { get; set; }
        public string BundleWorkflowStage { get; set; }
        public string BundleLastModified { get; set; }
        public string BundleDateCreated { get; set; }

        internal static ComponentLabTestsSlice AssembleFrom(OrderedTestDetailSlice slice)
        {
            return new ComponentLabTestsSlice
            {
                Id = SharedUtilities.IntToStringPositive((int) slice.Id),
                LabTestId = SharedUtilities.IntToStringPositive(slice.LabTestId),
                DateCreated = SharedUtilities.DateTimeToStringDMHM(slice.DateCreated),
                LastModified = SharedUtilities.DateTimeToStringDMHM(slice.LastModified),
                ResultsETA = slice.ResultsETA != null
                    ? SharedUtilities.DateTimeToStringDMHM((DateTime)slice.ResultsETA)
                    : "",
                IsCancelled = slice.IsCancelled,
                UnitPrice = SharedUtilities.MoneyToStringPlain(slice.UnitPrice),
                TestName = slice.TestName,
                LabName = slice.LabName,
                TestWorkflowStage = EnumUtils.EnumDescription((WorkflowStageType) slice.TestWorkflowStage),
                BundleId = slice.BundleId != null ? SharedUtilities.IntToStringPositive((int) slice.BundleId) : "",
                BundleIsCancelled = (bool) (slice.BundleIsActive != null ? !slice.BundleIsActive : false),
                BundleWorkflowStage =
                    slice.BundleWorkflowStage != null
                        ? EnumUtils.EnumDescription((WorkflowStageType) slice.BundleWorkflowStage)
                        : "",
                BundleResultType =
                    slice.BundleResultType != null
                        ? EnumUtils.EnumDescription((TestResultType) slice.BundleResultType)
                        : "",
                BundleDateCreated =
                    slice.BundleDateCreated != null
                        ? SharedUtilities.DateTimeToStringDMHM((DateTime)slice.BundleDateCreated)
                        : "",
                BundleLastModified =
                    slice.BundleLastModified != null
                        ? SharedUtilities.DateTimeToStringDMHM((DateTime)slice.BundleLastModified)
                        : "",
            };
        }
    }
}