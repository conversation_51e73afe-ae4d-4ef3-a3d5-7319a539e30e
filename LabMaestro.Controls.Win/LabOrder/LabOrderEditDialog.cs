﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2015, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id$
// 
// Last modified: 2015-08-10 9:06 AM
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraBars;
using DevExpress.XtraEditors;
using LabMaestro.BusinessLogic;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Shared;
using Xceed.Grid;
using Xceed.Grid.Viewers;
using HorizontalAlignment = Xceed.Grid.HorizontalAlignment;

namespace LabMaestro.Controls.Win;

public partial class LabOrderEditDialog : XtraForm, IExecutableDialog
{
    private readonly long _initInvoiceNum;
    private bool _freezeBillableItems;
    private InvoiceCalculator _invoiceCalc;
    private LabOrderEditor? _orderEditor;
    private bool _orderIsDirty;
    private ILabOrderSearchProvider _searchProvider;

    public LabOrderEditDialog(long invoiceId) : this()
    {
        _initInvoiceNum = invoiceId;
        _freezeBillableItems = false;
    }

    public LabOrderEditDialog()
    {
        InitializeComponent();
        WinUtils.ApplyTheme(this);
        WinUtils.SetFormTitle(this, "Edit Lab Order");
    }

    public bool ExecuteDialog(IWin32Window parent)
    {
        UpdateControls();
        ShowDialog(parent);
        return true;
    }

    public void UpdateControls() => PatientLabOrdersRepository.Reset(); // BUG FIX #96: Purge stale data

    private void gridCreateColumns()
    {
        var i = 0;
        grdOrderedTests.BeginInit();
        grdOrderedTests.Columns.Clear();

        var imageViewer = new Column(@"mod", typeof(Image))
        {
            CellViewerManager = new ImageViewer(),
            ReadOnly = true,
            CanBeSorted = false,
            VisibleIndex = i++,
            Width = 20,
            Visible = true,
            Title = "",
            HorizontalAlignment = HorizontalAlignment.Center,
            VerticalAlignment = VerticalAlignment.Center
        };
        grdOrderedTests.Columns.Add(imageViewer);
        grdOrderedTests.GridAddColumn(@"name", "Test", i++, 260);
        grdOrderedTests.GridAddColumn(@"canceled", "Cancld?", i++, 70);
        grdOrderedTests.GridAddColumn(@"price", "Price", i++, 70);
        grdOrderedTests.GridAddColumn(@"delivery", "Delivery", i++, 130);
        grdOrderedTests.GridAddColumn(@"maxDisc", "Disc.", i++, 60);
        grdOrderedTests.GridAddColumn(@"lastMod", "Last Modified", i, 100);
        grdOrderedTests.GridAddColumn(@"workflow", "Workflow", i, 140);
        grdOrderedTests.EndInit();

        i = 0;
        grdBillableItems.BeginInit();
        grdBillableItems.Columns.Clear();
        grdBillableItems.GridAddColumn(@"name", "Item", i++, 140);
        grdBillableItems.GridAddColumn(@"qty", "Qty", i++, 55);
        var col = grdBillableItems.GridAddColumn(@"price", "Price", i, 65);
        col.HorizontalAlignment = HorizontalAlignment.Right;
        grdBillableItems.EndInit();
    }

    private void LabOrderEditDialog_Load(object sender, EventArgs e)
    {
        _orderIsDirty = false;
        gridCreateColumns();
        resetAllControls();
        updateOrderModifications();
        txtInvoiceNum.Select();
        txtInvoiceNum.Focus();
        if (_initInvoiceNum > 0) {
            txtInvoiceNum.Text = _initInvoiceNum.ToString();
            performSearchInvoiceId();
        }
    }

    private void resetAllControls()
    {
        resetSearchControls();
        resetDemographicLabels();
        updateAfterFinancialLabels();
        updateBeforeFinancialLabels();
        gridResetRows();
        chkConfirm.Checked = false;
    }

    private void resetSearchControls()
    {
        txtInvoiceNum.Text = string.Empty;
        txtOrderId.Text = string.Empty;
        dteOrderDate.DateTime = DateTime.Now;
    }

    private void btnSearchInvoiceId_Click(object sender, EventArgs e) => performSearchInvoiceId();

    private void performSearchInvoiceId()
    {
        long invoiceId;
        var sId = txtInvoiceNum.Text.Trim();
        if (string.IsNullOrEmpty(sId)) return;
        try {
            invoiceId = long.Parse(sId);
            if (invoiceId <= 0)
                throw new Exception("Invalid Invoice number entered.");
        }
        catch (Exception exc) {
            MessageDlg.Error(exc.Message);
            return;
        }

        resetAllControls();
        _searchProvider = new ActiveLabOrderSearchByInvoiceIdProvider(invoiceId);
        populateControlsFromSearchResult();
    }

    private SearchedLabOrderInfo performLabOrderSearchOperation()
    {
        IEnumerable<SearchedLabOrderInfo> orders = null;

        FaultHandler.Shield(() => WaitFormControl.WaitOperation(this,
                                                                () =>
                                                                {
                                                                    SuspendLayout();
                                                                    _orderIsDirty = false;
                                                                    _freezeBillableItems = false;
                                                                    orders = _searchProvider.SearchLabOrders();
                                                                    ResumeLayout();
                                                                },
                                                                "Searching lab order..."));

        return orders != null && orders.Any() ? orders.First() : null;
    }

    private void populateControlsFromSearchResult()
    {
        resetDemographicLabels();
        gridResetRows();
        _invoiceCalc = null;
        _orderEditor = null;
        updateAllControls(null);

        var foundInvoice = performLabOrderSearchOperation();
        if (foundInvoice == null) {
            MessageDlg.Error("No invoice found!");
            return;
        }

        FaultHandler.Shield(() => WaitFormControl.WaitOperation(this, () => _orderEditor = new LabOrderEditor(foundInvoice.InvoiceId)));
        _invoiceCalc = new InvoiceCalculator();
        updateInvoiceCalculator(_orderEditor.OriginalInvoiceSummary);
        updateAllControls(foundInvoice);
    }

    private void updateAllControls(SearchedLabOrderInfo? foundInvoice)
    {
        updateDemographicLabels(foundInvoice);
        updateBeforeFinancialLabels();
        updateAfterFinancialLabels();
        gridPopulateDataRows();
        updateButtonControls();
    }

    private void updateInvoiceCalculator(InvoiceSummary invSum)
    {
        _invoiceCalc.Reset();
        _invoiceCalc.SetDiscountAmount(invSum.DiscountAmount);
        _invoiceCalc.SetPaidAmount(invSum.PaidAmount);
        _invoiceCalc.CalculateInvoice(_orderEditor.EffectiveOrderedTests, _orderEditor.OrderedBillableItems);
    }

    private void updateButtonControls()
    {
        btnApplyChanges.Enabled = _orderIsDirty && (_orderEditor != null);
        chkConfirm.Enabled = btnApplyChanges.Enabled;

        var enableTool = _orderEditor is { IsCancelled: false };
        btnDiscount.Enabled = enableTool;
        btnDiscountRebate.Enabled = enableTool;
        btnRefund.Enabled = enableTool;
        btnCancelOrder.Enabled = enableTool;
    }

    private static Bitmap? modificationToBitmap(PendingModificationType modType) =>
        modType switch
        {
            PendingModificationType.Addition     => new Bitmap(Resources.add_16),
            PendingModificationType.Cancellation => new Bitmap(Resources.cancel2_16),
            _                                    => null
        };

    //return new Bitmap(Resources.tick_16);
    private void gridPopulateDataRows()
    {
        grdOrderedTests.BeginInit();
        try {
            grdOrderedTests.DataRows.Clear();
            if (_orderEditor != null) {
                foreach (var slice in _orderEditor.OrderedTests) {
                    var row = grdOrderedTests.DataRows.AddNew();
                    row.Cells["mod"].Value = modificationToBitmap(slice.PendingModification);
                    row.Cells["name"].Value = slice.ShortName;
                    row.Cells["canceled"].Value = SharedUtilities.BooleanToStringYN(slice.OrderedTestIsCancelled);
                    row.Cells["price"].Value = SharedUtilities.MoneyToStringPlain(slice.GetPrice());
                    row.Cells["delivery"].Value = SharedUtilities.DateToStringShort(slice.OrderedTestETA) + " " +
                                                  SharedUtilities.TimeToStringShort(slice.OrderedTestETA);
                    row.Cells["maxDisc"].Value = SharedUtilities.MoneyToStringPlain(slice.MaxApplicableDiscount);
                    row.Cells["lastMod"].Value = SharedUtilities.DateTimeToUltraShortString(slice.OrderedTestLastModified);
                    row.Cells["workflow"].Value = EnumUtils.EnumDescription(slice.OrderedTestWorkflowStage);

                    if (slice.OrderedTestIsCancelled) row.ForeColor = Color.Gray;

                    row.Tag = slice;
                    row.Height = 22;
                    row.EndEdit();
                }
            }
        }
        finally {
            grdOrderedTests.EndInit();
        }

        grdBillableItems.BeginInit();
        try {
            grdBillableItems.DataRows.Clear();
            if (_orderEditor == null) return;
            foreach (var slice in _orderEditor.OrderedBillableItems) {
                var row = grdBillableItems.DataRows.AddNew();
                row.Cells["name"].Value = slice.Name;
                row.Cells["price"].Value = SharedUtilities.MoneyToStringPlain(slice.UnitPrice);
                row.Cells["qty"].Value = slice.Quantity.ToString();

                row.Tag = slice;
                row.Height = 22;
                row.EndEdit();
            }
        }
        finally {
            grdBillableItems.EndInit();
        }
    }

    private void gridResetRows()
    {
        grdOrderedTests.DataRows.Clear();
        grdBillableItems.DataRows.Clear();
    }

    private void updateBeforeFinancialLabels()
    {
        lblGrossPayableB.Text = string.Empty;
        lblDiscount.Text = string.Empty;
        lblRefund.Text = string.Empty;
        lblNetPayableB.Text = string.Empty;
        lblDueB.Text = string.Empty;
        lblNumTestsB.Text = string.Empty;
        lblPaidB.Text = string.Empty;

        if (_orderEditor == null) return;

        lblNumTestsB.Text = _orderEditor.NumberOfActiveOrderedTests.ToString();
        lblGrossPayableB.Text = SharedUtilities.MoneyToStringPlain(_orderEditor.OriginalInvoiceSummary.GrossPayable);
        lblDiscount.Text = SharedUtilities.MoneyToStringPlain(_orderEditor.OriginalInvoiceSummary.DiscountAmount);
        lblRefund.Text = SharedUtilities.MoneyToStringPlain(_orderEditor.OriginalInvoiceSummary.RefundAmount);
        lblNetPayableB.Text = SharedUtilities.MoneyToStringPlain(_orderEditor.OriginalInvoiceSummary.NetPayable);
        lblDueB.Text = SharedUtilities.MoneyToStringPlain(_orderEditor.OriginalInvoiceSummary.DueAmount);
        lblPaidB.Text = SharedUtilities.MoneyToStringPlain(_orderEditor.OriginalInvoiceSummary.PaidAmount);
    }

    private void updateAfterFinancialLabels()
    {
        lblGrossPayableA.Text = string.Empty;
        lblNetPayableA.Text = string.Empty;
        lblDueA.Text = string.Empty;
        lblNumTestsA.Text = string.Empty;
        lblPaidA.Text = string.Empty;

        if (_orderEditor == null) return;

        lblNumTestsA.Text = _orderEditor.NumberOfActiveOrderedTests.ToString();
        lblGrossPayableA.Text = SharedUtilities.MoneyToStringPlain(_invoiceCalc.GrossPayable);
        lblNetPayableA.Text = SharedUtilities.MoneyToStringPlain(_invoiceCalc.NetPayable);
        lblDueA.Text = SharedUtilities.MoneyToStringPlain(_invoiceCalc.DueAmount);
        lblPaidA.Text = SharedUtilities.MoneyToStringPlain(_invoiceCalc.PaidAmount);
    }

    private void resetDemographicLabels()
    {
        lblRefdBy.Text = string.Empty;
        lblPatientName.Text = string.Empty;
        lblSex.Text = string.Empty;
        lblInvoiceNum.Text = string.Empty;
        lblOrderId.Text = string.Empty;
        lblAge.Text = string.Empty;
        lblBooking.Text = string.Empty;
        lblStaff.Text = string.Empty;
    }

    private void updateDemographicLabels(SearchedLabOrderInfo? foundInvoice)
    {
        resetDemographicLabels();

        if (foundInvoice != null) {
            lblPatientName.Text = SharedUtilities.Chomp(foundInvoice.PatientName, 40);
            lblRefdBy.Text = SharedUtilities.Chomp(foundInvoice.ReferrerName, 40);
            lblSex.Text = foundInvoice.Sex == SexType.Male ? "M" : "F";
            lblInvoiceNum.Text = foundInvoice.InvoiceId.ToString();
            lblOrderId.Text = foundInvoice.OrderId;
            lblAge.Text = foundInvoice.Age;
            lblBooking.Text = SharedUtilities.DateTimeToShortString(foundInvoice.OrderDateTime);
        }

        FaultHandler.Shield(() =>
        {
            var det = PatientLabOrdersRepository.FetchLabOrderDetails(_initInvoiceNum);
            lblStaff.Text = det.OrderingUserName;
        });
    }

    private void btnSearchOrderId_Click(object sender, EventArgs e)
    {
        var patientId = txtOrderId.Text.Trim().ToUpperInvariant();
        var date = dteOrderDate.DateTime;
        _searchProvider = new ActiveLabOrderSearchByPatientIdAndDateRangeProvider(patientId, date, date);
        populateControlsFromSearchResult();
    }

    private OrderedTestWithWorkflowSlice? getSelectedTest() =>
        grdOrderedTests.SelectedRows.Count == 1
            ? grdOrderedTests.SelectedRows[0].Tag as OrderedTestWithWorkflowSlice
            : null;

    private List<OrderedTestWithWorkflowSlice> getSelectedTests()
    {
        var list = new List<OrderedTestWithWorkflowSlice>();
        if (grdOrderedTests.SelectedRows.Count > 0)
            list.AddRange((from Row row in grdOrderedTests.SelectedRows select row.Tag).OfType<OrderedTestWithWorkflowSlice>());

        return list;
    }

    private void btnTestAdd_Click(object sender, EventArgs e)
    {
        string reason = null;
        //reason = InputDialog.ExecuteDialog(this, "Specify reason for test addition");
        //if (string.IsNullOrEmpty(reason))
        //{
        //    MessageDlg.Warning("No reason specified!\nOperation aborted.");
        //    return;
        //}

        var testIds = LabTestQuickSelectionDialog.ExecuteDialog(this, true);

        if (testIds == null) return;

        foreach (var testId in testIds.Where(tid => !_orderEditor.TestAlreadyOrdered(tid)))
            _orderEditor.AddTest(testId, reason);

        _freezeBillableItems = false;
        updateOrderModifications();
    }

    private void updateOrderModifications()
    {
        if (_orderEditor != null) {
            performOrderCompilation();
            _orderIsDirty = true;
        }

        gridPopulateDataRows();
        updateAfterFinancialLabels();
        updateButtonControls();
    }

    private void performOrderCompilation()
    {
        if (!_freezeBillableItems) _orderEditor.CompileBillableItemsList();

        updateInvoiceCalculator(_orderEditor.OriginalInvoiceSummary);
    }

    private void btnTestCost_Click(object sender, EventArgs e)
    {
        var selectedTest = getSelectedTest();
        if (selectedTest != null) {
            var msg = string.Format("Base Price: {0}\nEffective Price: {1}",
                                    SharedUtilities.MoneyToStringPlain(selectedTest.ListPrice),
                                    SharedUtilities.MoneyToStringPlain(selectedTest.CalculateEffectivePrice()));
            MessageDlg.Info(msg);
        }
    }

    private void btnTestConfirm_Click(object sender, EventArgs e)
    {
        var selectedTest = getSelectedTest();
        if (selectedTest != null && _orderEditor != null) {
            checkTestCancelability(selectedTest, true);
        }
        else {
            MessageDlg.Warning("No test selected.\nPlease select a single lab test.");
        }
    }

    private bool checkTestCancelability(OrderedTestWithWorkflowSlice selectedTest, bool showSuccess)
    {
        string message;
        var result = _orderEditor.CheckTestCancelability(selectedTest, true, out message);
        if (!result)
            MessageDlg.Warning(message);
        else if (showSuccess) MessageDlg.Info("The selected Lab Test can be canceled");

        return result;
    }

    private void btnTestETA_Click(object sender, EventArgs e)
    {
        var selectedTest = getSelectedTest();
        if (selectedTest != null && _orderEditor != null) {
            using (var dlgDelivery = new DateTimeDialog { SelectedDateTime = DateTime.Now }) {
                switch (dlgDelivery.ShowDialog()) {
                    case DialogResult.OK:
                        selectedTest.DeliveryTime = dlgDelivery.SelectedDateTime;
                        break;
                    case DialogResult.Abort:
                        selectedTest.DeliveryTime = null;
                        break;
                    default:
                        return;
                }
            }

            gridPopulateDataRows();
        }
        else {
            MessageDlg.Warning("No test selected.\nPlease select a single lab test.");
        }
    }

    private void btnApplyChanges_Click(object sender, EventArgs e)
    {
        if (!chkConfirm.Checked) {
            MessageDlg.Warning("Please confirm the changes by checking the confirmation box!");
            return;
        }

        if (!_orderIsDirty) {
            MessageDlg.Info("No changes have been made. Nothing to do...");
            return;
        }

        if (!CaptchaDialog.ConfirmCaptcha(3, false, false))
            return;

        /*  ToDo - cycle through all changesets:
         *
         *  Test add ->
         *      create new result bundle [LabId, OrderedTestId]
         *  test cancel - find result bundle. remove parameters.
         *  Result bundle ->
         *      Discrete ->
         *          is this the only test? ->
         *              remove/cancel the bundle [IsActive = false]
         *          shares with other tests? ->
         *              remove/cancel the parameters    [DELETE FROM DiscreteResultLineItems WHERE
         *                                               ResultBundleId, OrderedTestId]
         *      Template ->
         *          Remove/Cancel the bundle    [IsActive = false]
         *  Billable Items:
         *      Method #1->
         *          Remove all PROE.OrderedBillableItems {InvoiceId} (?? IsCancelled ??)
         *          Add all Billable Items
         *      Method #2 ->
         *          Cycle Through all PROE.OrderedBillableItems ->
         *              If OBI is in Working set and quantity is different:
         *                  Update quantity and line total
         *              If OBI is not in Working set:
         *                  Set IsActive = false / remove the item
         *
         */

        // prepare the order invoice finances and billable items
        performOrderCompilation();

        _orderEditor.ApplyChanges(_invoiceCalc);

        if (_orderEditor.LabOrderCanBeCanceled(false))
            MessageDlg.Info("The invoice contains no tests and may be voided.\nYou should cancel this order.");

        populateControlsFromSearchResult();
    }

    private void btnTestCancel_Click(object sender, EventArgs e)
    {
        var testsToCancel = getSelectedTests();
        if (testsToCancel.Count <= 0) return;

        if (!MessageDlg.Confirm($"Do you want to cancel the selected {testsToCancel.Count} tests?"))
            return;

        var reason = InputDialog.ExecuteDialog(this, "Specify the reason for test cancellation");
        if (string.IsNullOrEmpty(reason)) {
            MessageDlg.Warning("No cancellation reason specified!\nOperation aborted.");
            return;
        }

        //if (!CaptchaDialog.ConfirmCaptcha(5, false, false)) return;
        foreach (var test in testsToCancel) {
            if (!cancelLabTest(test, reason))
                break;
        }
    }

    private bool cancelLabTest(OrderedTestWithWorkflowSlice selectedTest, string reason)
    {
        if (selectedTest == null || _orderEditor == null) {
            MessageDlg.Warning("No test selected.\nOperation aborted.");
            return false;
        }

        if (selectedTest.OrderedTestIsCancelled ||
            selectedTest.PendingModification == PendingModificationType.Cancellation) {
            MessageDlg.Warning("Selected Lab Test had already been canceled!");
            return false;
        }

        var removeUncommittedTest = false;
        if (selectedTest.PendingModification == PendingModificationType.Addition)
            removeUncommittedTest = true; // if the test was recently added in this current session, simply remove it
        else if (!checkTestCancelability(selectedTest, false)) return false;

        //if (!MessageDlg.Confirm(string.Format("Do you want to cancel the following test?\n{0}",selectedTest.ShortName))) return false;

        //if (!CaptchaDialog.ConfirmCaptcha(3, false, false)) return false;

        if (removeUncommittedTest)
            _orderEditor.RemoveTest(selectedTest);
        else {
            selectedTest.PendingOperationReason = reason;
            _orderEditor.CancelTest(selectedTest);
        }

        _freezeBillableItems = false;
        updateOrderModifications();

        return true;
    }

    private void showRefundDiscountDialog(RefundDiscountOperationMode operationMode)
    {
        var isCancelled =
            FaultHandler.Shield(() => PatientLabOrdersRepository.CheckLabOrderIsCanceled(_orderEditor.InvoiceId));
        if (_orderEditor == null || isCancelled) return;

        var order = FaultHandler.Shield(() => PatientLabOrdersRepository.FindById(_orderEditor.InvoiceId));

        if (order == null || order.IsCancelled) {
            MessageDlg.Error("Non-existent or canceled order.\nOperation aborted.");
            return;
        }

        using var frm = new RefundDiscountDialog(order, operationMode);
        frm.ShowDialog();

        _freezeBillableItems = false;
        _searchProvider = new ActiveLabOrderSearchByInvoiceIdProvider(_orderEditor.InvoiceId);
        populateControlsFromSearchResult();
    }

    private void btnDiscount_ItemClick(object sender, ItemClickEventArgs e)
    {
        if (_orderEditor != null && _orderEditor.OriginalInvoiceSummary.DueAmount <= 0) {
            MessageDlg.Warning("This invoice is paid in full.\nDiscount operation cannot be performed.");
            return;
        }

        showRefundDiscountDialog(RefundDiscountOperationMode.Discount);
    }

    private void btnDiscountRebate_ItemClick(object sender, ItemClickEventArgs e)
    {
        if (_orderEditor != null && _orderEditor.OriginalInvoiceSummary.DiscountAmount <= 0) {
            MessageDlg.Warning("This invoice has no discounts issued.\nDiscount Rebate operation cannot be performed.");
            return;
        }

        showRefundDiscountDialog(RefundDiscountOperationMode.DiscountRebate);
    }

    private void btnRefund_ItemClick(object sender, ItemClickEventArgs e)
    {
        if (_orderEditor != null && _orderEditor.OriginalInvoiceSummary.PaidAmount <= 0) {
            MessageDlg.Warning(
                "No payments have been made against this invoice!\nRefund operation cannot be performed.\nOperation aborted.");
            return;
        }

        showRefundDiscountDialog(RefundDiscountOperationMode.Refund);
    }

    private void btnCancelOrder_ItemClick(object sender, ItemClickEventArgs e)
    {
        if (_orderEditor == null) return;

        if (FaultHandler.Shield(() => PatientLabOrdersRepository.CheckLabOrderIsCanceled(_orderEditor.InvoiceId))) {
            MessageDlg.Warning("This order has already been canceled!");
            return;
        }

        var invoice = FaultHandler.Shield(() => InvoiceMasterRepository.GetInvoiceFinancialDetails(_orderEditor.InvoiceId));

        if (invoice.PaidAmount > 0) {
            var msg =
                $"Payment of Tk. {SharedUtilities.MoneyToString(invoice.PaidAmount)} had been made against this invoice.\n";
            msg += "You must refund the above amount should you wish to cancel this lab order.\nOperation aborted.";
            MessageDlg.Warning(msg);
            return;
        }

        var reason = InputDialog.ExecuteDialog(this, "Order cancellation reason:");
        if (string.IsNullOrEmpty(reason)) {
            MessageDlg.Warning("No valid reason for order cancellation was provided!\nOperation aborted.");
            return;
        }

        if (!CaptchaDialog.ConfirmCaptcha(4))
            return;

        FaultHandler.Shield(() => WaitFormControl.WaitOperation(this, () =>
        {
            PatientLabOrdersRepository.CancelLabOrder(_orderEditor.InvoiceId, WorkflowStageType.Canceled);
            AuditTrailRepository.LogGenericEvent(
                AuditEventCategory.OrderEntry,
                AuditEventType.ordOrderCanceled,
                WorkflowStageType.Canceled,
                CurrentUserContext.UserId,
                CurrentUserContext.WorkShiftId,
                _orderEditor.InvoiceId,
                null,
                null,
                reason);
        }, "Saving..."));
        _orderEditor = null;
        _orderIsDirty = false;
        _freezeBillableItems = false;
        updateOrderModifications();
    }

    private BillableItemSlice? getSelectedBillableItem() =>
        grdBillableItems.SelectedRows.Count != 1
            ? null
            : grdBillableItems.SelectedRows[0].Tag as BillableItemSlice;

    private void btnBIDelete_Click(object sender, EventArgs e)
    {
        var slice = getSelectedBillableItem();
        if (slice == null ||
            !MessageDlg.Confirm($"Really delete {slice.Quantity} unit(s) of {slice.Name}?") ||
            !checkUnderflow(slice.LineTotal)) return;

        _freezeBillableItems = true;
        _orderEditor.RemoveBillableItem(slice);
        updateOrderModifications();
    }

    private void txtInvoiceNum_KeyUp(object sender, KeyEventArgs e)
    {
        if (e.KeyCode == Keys.Enter) performSearchInvoiceId();
    }

    private void btnCalc_ItemClick(object sender, ItemClickEventArgs e) => Process.Start("calc.exe");

    private void btnExit_ItemClick(object sender, ItemClickEventArgs e) => exitForm();

    private void exitForm()
    {
        if (_orderIsDirty) {
            if (!MessageDlg.Confirm("All changes will be lost if you exit. Do you want to close this window?"))
                return;
        }

        Close();
    }

    private void LabOrderEditDialog_KeyUp(object sender, KeyEventArgs e)
    {
        if (e.KeyCode == Keys.Escape) exitForm();
    }

    private void btnBIModify_Click(object sender, EventArgs e)
    {
        var slice = getSelectedBillableItem();
        if (slice == null) return;

        using var frm = new OrderBillableItemDialog();
        frm.SelectedBillableItemId = slice.Id;
        frm.SelectedQuantity = slice.Quantity;
        frm.UpdateControls();
        if (frm.ShowDialog() != DialogResult.OK) return;
        _orderEditor.ModifyBillableItemQuantity(slice.Id, slice.Quantity, frm.SelectedQuantity);
        _freezeBillableItems = true;
        updateOrderModifications();
    }

    private void btnBIAdd_Click(object sender, EventArgs e)
    {
        using var frm = new OrderBillableItemDialog { AllowSelectionOfBillableItem = true };
        frm.UpdateControls();
        if (frm.ShowDialog() != DialogResult.OK) return;

        if (_orderEditor.HasBillableItem(frm.SelectedBillableItemId)) {
            MessageDlg.Warning("The selected billable item is already in the list.");
            return;
        }

        _orderEditor.AddBillableItem(frm.SelectedBillableItemId, frm.SelectedQuantity);
        _freezeBillableItems = true;
        updateOrderModifications();
    }

    private bool checkUnderflow(decimal deductAmount)
    {
        var netBill = _invoiceCalc.NetPayable - deductAmount;
        if (_invoiceCalc.PaidAmount <= netBill) return true;

        MessageDlg.Warning(
            $"Deduction of Tk {deductAmount} will result in negative balance.\nPlease issue adequate refund before attempting this operation.");
        return false;
    }

    private void btnBIClear_Click(object sender, EventArgs e)
    {
        if (!MessageDlg.Confirm("Really remove all billable items?")) return;
        var biPrice = _orderEditor.OrderedBillableItemsTotal;
        if (!checkUnderflow(biPrice)) return;
        _orderEditor.ClearBillableItems();
        _freezeBillableItems = true;
        updateOrderModifications();
    }
}