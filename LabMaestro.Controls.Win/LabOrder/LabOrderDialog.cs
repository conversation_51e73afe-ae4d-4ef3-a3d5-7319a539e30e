﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: LabOrderDialog.cs 1532 2014-11-29 08:57:12Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Drawing;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using C1.LiveLinq;
using C1.LiveLinq.Collections;
using DevExpress.Skins;
using DevExpress.XtraBars;
using DevExpress.XtraEditors;
using LabMaestro.BusinessLogic;
using LabMaestro.BusinessLogic.CRM;
using LabMaestro.Controls.Win.CRM;
using LabMaestro.Controls.Win.LabOrder;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Infrastructure.Search;
using LabMaestro.Printing;
using LabMaestro.Shared;
using Syncfusion.Windows.Forms.Grid;
using Syncfusion.Windows.Forms.Tools;
using Xceed.Grid;
using HorizontalAlignment = Xceed.Grid.HorizontalAlignment;
using PrintHelper = LabMaestro.Printing.PrintHelper;
#if TRACE
using Gurock.SmartInspect;
#endif

namespace LabMaestro.Controls.Win;

public partial class LabOrderDialog : XtraForm, IExecutableDialog
{
    private const int MAX_SEARCH_RESULTS = 100;
    private const int GRIDLISTCONTROL_ROWHEIGHT28 = 28;
    private const int GRIDLISTCONTROL_ROWHEIGHT24 = 24;
    private const Keys KEY_SEARCH_POPUP = Keys.PageDown;
    private const Keys KEY_COPY_CUSTOM_REFERRER = Keys.F6;
    private const Keys KEY_SEARCH_ACCEPT = Keys.Enter;
    private const Keys KEY_SEARCH_CANCEL = Keys.Escape;
    private const Keys KEY_FIX_CASING = Keys.F5;
    private const Keys KEY_SEARCH_QUICKSELECT = Keys.F8;
    private const Keys KEY_CRM_REGISTER = Keys.F9;
    private const Keys KEY_DISCOUNT_CALC = Keys.F11;
    private const Keys KEY_BATTERY_SELECT = Keys.F2;
    private readonly InvoiceCalculator _invoiceCalc = new();
    private readonly IndexedCollection<LabTestInfo> _labTestsSearchCollection = [];
    private IEnumerable<AffiliateSlice> _affiliates = [];
    private IEnumerable<Tuple<int, string>> _batteries = [];
    private IEnumerable<AssociateOrganizationSlice> _associateLabs = [];
    private LabOrderContextInfo _copyOfOrderDemographics;
    private IEnumerable<CorporateClientSlice> _corporateClients = [];
    private LabOrderContextInfo _currentLabOrder = new();
    private short _globalAssociateLabId = -1;
    private short _orderAssociateLabId = -1;
    private string _physicianSearchString;
    private string CustomerDefaultPassword;
    private SearchMode _searchMode = SearchMode.Any;
    private bool _suppressReferrerComboTextChange;
    private string _testSearchString;
    private BillableItemSlice? AppSurchargeBillableItem = null;
    private DateTime _catalogLastUpdateTime = DateTime.Now;
    private bool _reimburseSurcharge = false;

    public LabOrderDialog(LabOrderDialogAccessType accessType)
    {
        AccessType = accessType;
        InitializeComponent();
        FaultHandler.Shield(PatientLabOrdersRepository.Reset);
        WinUtils.SetFormTitle(this, "New Lab Order");
        setGlobalAssociateLab(null);
        applySurchargeParameters();
#if DEBUG
        SiAuto.Si.Connections = "tcp()";
        SiAuto.Si.Enabled = true;
#endif
    }

    public LabOrderDialog(short associateLabId, string labName)
    {
        AccessType = LabOrderDialogAccessType.CreateNew;
        InitializeComponent();
        FaultHandler.Shield(PatientLabOrdersRepository.Reset);
        WinUtils.SetFormTitle(this, $"New Lab Order - {labName}");
        setGlobalAssociateLab(associateLabId);
        applySurchargeParameters();
#if DEBUG
        SiAuto.Si.Connections = "tcp()";
        SiAuto.Si.Enabled = true;
#endif
    }

    void applySurchargeParameters()
    {
        CustomerDefaultPassword = GlobalSettingsHelper.Crm.DefaultPassword;
        _invoiceCalc.SurchargeMinAmount = GlobalSettingsHelper.System.Surcharge.MinAmount;
        _invoiceCalc.SurchargeMaxAmount = GlobalSettingsHelper.System.Surcharge.MaxAmount;
        _invoiceCalc.SetSurchargePercent(GlobalSettingsHelper.System.Surcharge.RatePaisa);
        var plans = FaultHandler.Shield(PlansRepository.FetchActive);
        CustomerDefaultSubscriptionPlan =
            new PlanInfo(plans.Single(p => p.Id == GlobalSettingsHelper.System.Surcharge.DefaultPlanId));
        var billables = FaultHandler.Shield(BillableItemsRepository.FindActiveItems);
        AppSurchargeBillableItem = BillableItemSlice.AssembleFrom(
            billables.Single(x => x.Id == GlobalSettingsHelper.System.Surcharge.BillableItemId)
        );
        AppSurchargeBillableItem.OptimizationLevel = BillableItemOptimizationLevelType.DoNotOptimize;
        AppSurchargeBillableItem.Quantity = 1;
        AppSurchargeBillableItem.IsActive = true;
        AppSurchargeBillableItem.IsCancelled = false;
    }

    public PlanInfo CustomerDefaultSubscriptionPlan { get; set; }

    public LabOrderDialogAccessType AccessType { get; }

    public bool ExecuteDialog(IWin32Window parent)
    {
        UpdateControls();
        ShowDialog(parent);
        return true;
    }

    public void UpdateControls()
    {
        if (hasNoAssociateLab()) return;

        // hide the BI grids and controls
        disableControl(grdBillableItems);
        disableControl(btnBIAdd);
        disableControl(btnBIClear);
        disableControl(btnBIDelete);
        disableControl(btnBIModify);

        grdOrderedTests.Width = ClientSize.Width - 16;

        lblExternalId.Visible = true;
        /*
        txtExtTrackingId.Left = rgSex.Left;
        txtExtTrackingId.Width = ClientSize.Width - txtExtTrackingId.Left - 12;
        txtExtTrackingId.TabIndex = cboPhysician.TabIndex + 1;
        */

        chkDirectPatient.Checked = false;
        disableControl(chkDirectPatient);
        chkOutsideOrder.Checked = false;
        disableControl(chkOutsideOrder);
        disableControl(chkUnknownPhysician);
        disableControl(txtPhysicianCustomName);

        cboLabTest.Width = lblNote.Left - cboLabTest.Left - 8;
        disableControl(lblTelephone);
        disableControl(txtTelephone);
        disableControl(lblEmail);
        disableControl(txtEmail);

        resetSelectedItemLabels();
    }

    private void disableControl(Control control)
    {
        control.Enabled = false;
        control.Visible = false;
    }

    private static void GridListControl_QueryRowHeight(object sender, GridRowColSizeEventArgs e)
    {
        /*
        e.Size = sender switch
        {
            GridListControlModel model => (string)model.ListControl.Tag == "100"
                ? GRIDLISTCONTROL_ROWHEIGHT26
                : GRIDLISTCONTROL_ROWHEIGHT36,
            _ => GRIDLISTCONTROL_ROWHEIGHT36
        };
        */
        e.Size = GRIDLISTCONTROL_ROWHEIGHT28;
        e.Handled = true;
    }

    private void createGridColumns()
    {
        var i = 0;
        grdOrderedTests.BeginInit();
        grdOrderedTests.Columns.Clear();
        grdOrderedTests.GridAddColumn(@"TestName", "Test", i++, 315);
        grdOrderedTests.GridAddColumn(@"TestPrice", "Price", i++, 80);
        grdOrderedTests.GridAddColumn(@"DeliveryETA", "Delivery ETA", i++, 165);
        var col = grdOrderedTests.GridAddColumn(@"MaxDiscount", "Max Disc.", i, 75);
        col.HorizontalAlignment = HorizontalAlignment.Right;
        grdOrderedTests.EndInit();

        i = 0;
        grdBillableItems.BeginInit();
        grdBillableItems.Columns.Clear();
        grdBillableItems.GridAddColumn(@"Name", "Item", i++, 140);
        grdBillableItems.GridAddColumn(@"Qty", "Qty", i++, 55);
        col = grdBillableItems.GridAddColumn(@"Price", "Price", i, 65);
        col.HorizontalAlignment = HorizontalAlignment.Right;
        grdBillableItems.EndInit();
    }

    private void LabOrderDialog_Load(object sender, EventArgs e)
    {
        //WinUtils.ApplyTheme(this);

        createGridColumns();
        updateControlsAccordingToAccessLevel();
        updateOrderChanges();
        refreshAccessoryCatalogs(true);
        //refreshAccessoryCatalogsAsync(true);
        initializeComboBoxes();

        initGridListControl(grdListLabTest, "Segoe UI", 11);
        initGridListControl(grdListPhysician, "Segoe UI", 11);
    }

    private static void initGridListControl(GridListControl gridControl,
                                            string fontName = "Segoe UI",
                                            int fontSize = 11,
                                            bool bold = false)
    {
        var style = bold ? FontStyle.Bold : FontStyle.Regular;
        gridControl.Grid.BaseStylesMap["Standard"].StyleInfo.Font =
            new GridFontInfo(new Font(fontName, fontSize, style));
        //gridControl.Grid.DefaultRowHeight = GRIDLISTCONTROL_ROWHEIGHT36;
        gridControl.Grid.Model.QueryRowHeight += GridListControl_QueryRowHeight;
    }

    public void CheckUserPrivileges()
    {
        switch (AccessType) {
            case LabOrderDialogAccessType.CreateNew:
                if (!CurrentUserContext.CheckShiftIsOpen())
                    throw new UnauthorizedAccessException("You do not have an open shift");
                break;
            case LabOrderDialogAccessType.Modify:
                if (!CurrentUserContext.CheckShiftIsOpen())
                    throw new UnauthorizedAccessException("You do not have an open shift");
                break;
            case LabOrderDialogAccessType.ReadOnly:
                if (!CurrentUserContext.CheckUserIsAuthenticated())
                    throw new UnauthorizedAccessException("You are not logged in");
                break;
        }
    }

    private void updateControlsAccordingToAccessLevel()
    {
        switch (AccessType) {
            case LabOrderDialogAccessType.CreateNew:
                //btnExit.Enabled = false;
                break;
            case LabOrderDialogAccessType.Modify:
                btnNewLabOrder.Enabled = false;

                //btnSetDeliveryTime.Enabled = false;
                btnCopyDemographics.Enabled = false;
                btnPasteDemographics.Enabled = false;

                btnPutOnHold.Enabled = false;
                btnOrdersOnHold.Enabled = false;

                OrdersMenu.Enabled = false;
                UserMenu.Enabled = false;
                break;
            case LabOrderDialogAccessType.ReadOnly:
                btnNewLabOrder.Enabled = false;
                btnCancelOrder.Enabled = false;
                btnClearAllTests.Enabled = false;
                btnDeleteTest.Enabled = false;
                btnSetDeliveryTime.Enabled = false;

                btnSetDiscountPercent.Enabled = false;
                btnApplyDiscount.Enabled = false;
                btnPayment.Enabled = false;

                btnCopyDemographics.Enabled = false;
                btnPasteDemographics.Enabled = false;

                btnPutOnHold.Enabled = false;
                btnOrdersOnHold.Enabled = false;

                btnBIAdd.Enabled = false;
                btnBIDelete.Enabled = false;
                btnBIModify.Enabled = false;

                OrdersMenu.Enabled = false;
                UserMenu.Enabled = false;
                break;
        }
    }

    private void btnSetDeliveryTime_ItemClick(object sender, ItemClickEventArgs e) =>
        setDeliveryTimeForSelectedTests(false);

    private void setDeliveryTimeForSelectedTests(bool onceOnly)
    {
        var selTests = new List<OrderableTestSlice>();
        if (grdOrderedTests.SelectedRows.Count > 0)
            foreach (Row row in grdOrderedTests.SelectedRows)
                if (row.Tag is OrderableTestSlice test)
                    selTests.Add(test);

        using var dlgDelivery = new DateTimeDialog { SelectedDateTime = DateTime.Now, SpecifyOnce = onceOnly };
        switch (dlgDelivery.ShowDialog()) {
            case DialogResult.OK:
                var first = true;
                foreach (var slice in selTests) {
                    if (dlgDelivery.SpecifyOnce) {
                        if (first) {
                            slice.DeliveryTime = dlgDelivery.SelectedDateTime;
                            first = false;
                        }
                        else
                            slice.DeliveryTime = null;
                    }
                    else
                        slice.DeliveryTime = dlgDelivery.SelectedDateTime;
                }

                break;
            case DialogResult.Abort:
                foreach (var slice in selTests) slice.DeliveryTime = null;

                break;
            default:
                return;
        }

        updateOrderedTestsGrid();
    }

    private void refreshAccessoryCatalogs(bool fullRefresh)
    {
        FaultHandler.Shield(
            () =>
                WaitFormControl.WaitOperation(
                    this,
                    () =>
                    {
                        WaitFormControl.WaitStart(this);
                        ReferrerSearchEngine.ResetCatalog();
                        PatientLabOrdersRepository.Reset();

                        List<Task> tasks = [];
                        var physicianCatalog = ActiveReferringPhysiciansRepository.GetCatalog(true);
                        tasks.Add(Task.Factory.StartNew(() => ReferrerSearchEngine.AssignCatalog(physicianCatalog)));

                        var testCatalog = OrderableTestSliceCatalogRepository.GetCatalog(true);
                        tasks.Add(Task.Factory.StartNew(() => LabTestSearchEngine.AssignCatalog(testCatalog)));

                        Task.WaitAll(tasks.ToArray());

                        if (fullRefresh) {
                            _corporateClients = CorporateClientRepository.FetchActive();
                            _affiliates = AffiliateRepository.FetchActive();
                            _associateLabs = AssociateOrganizationRepository.FetchActive();
                            _batteries = BatteryMasterRepository.GetActiveBatteries();
                        }

                        _labTestsSearchCollection.BeginUpdate();
                        try {
                            _labTestsSearchCollection.Clear();
                            foreach (var slice in testCatalog)
                                _labTestsSearchCollection.Add(
                                    new LabTestInfo { TestId = slice.TestId, TestName = slice.ShortName.ToLower() }
                                );
                        }
                        finally {
                            _labTestsSearchCollection.EndUpdate();
                        }
                    }
                )
        );
        _catalogLastUpdateTime = DateTime.Now;
    }

    private void refreshAccessoryCatalogsIfExpired(bool fullRefresh, int timeoutMinutes)
    {
        if (DateTime.Now > _catalogLastUpdateTime.AddMinutes(timeoutMinutes))
            refreshAccessoryCatalogs(fullRefresh);
    }

    private void btnRefreshSearchIndices_ItemClick(object sender, ItemClickEventArgs e)
    {
        // check if we have any pending orders - in that case refuse to update
        refreshAccessoryCatalogs(true);
        //refreshAccessoryCatalogsAsync(true);
    }

    private void updatePhysicianDropDownGrid(List<ReferrerSearchRec> items)
    {
        grdListPhysician.BeginUpdate();
        try {
            grdListPhysician.TopIndex = 0;
            grdListPhysician.SelectedIndex = -1;
            grdListPhysician.MultiColumn = true;
            grdListPhysician.SelectionMode = SelectionMode.One;
            grdListPhysician.DisplayMember = "Name";
            grdListPhysician.ValueMember = "Id";

            grdListPhysician.DataSource = items;
        }
        finally {
            grdListPhysician.EndUpdate();
        }

        grdListPhysician.Focus();
    }

    private void searchPhysicians()
    {
        if (string.IsNullOrEmpty(_physicianSearchString))
            _physicianSearchString = cboPhysician.Text.Trim().ToLowerInvariant();

        var searchRecs = string.IsNullOrEmpty(_physicianSearchString)
            ? ReferrerSearchEngine.GetCatalog()
            : ReferrerSearchEngine.Search($"*{_physicianSearchString}*", MAX_SEARCH_RESULTS);
        updatePhysicianDropDownGrid(searchRecs.Any() ? searchRecs : ReferrerSearchEngine.GetCatalog());
    }

    private void cboPhysician_TextChanged(object sender, EventArgs e)
    {
        if (!_suppressReferrerComboTextChange)
            _physicianSearchString = cboPhysician.Text.Trim().ToLowerInvariant();
    }

    private void applySelectedTest()
    {
        if (grdListLabTest.SelectedValue != null) {
            var id = (short)grdListLabTest.SelectedValue;
            addTestToCurrentLabOrder(id);
        }
        else {
            var testName = cboLabTest.Text.Trim().ToLower();
            if (!string.IsNullOrEmpty(testName)) {
                var slice =
                    _labTestsSearchCollection.AsIndexed().SingleOrDefault(x => x.TestName == testName);
                if (slice != null) {
                    addTestToCurrentLabOrder(slice.TestId);
                }
                else {
                    //MessageDlg.Warning("No test found.\nPlease provide a valid test name.");
                    cboLabTest.Focus();
                    comboDoPopup(cboLabTest, true);
                    return;
                }
            }
        }

        grdListLabTest.SelectedItem = null;
        cboLabTest.Text = null;
        _testSearchString = null;
    }

    private void applySelectedTestId(short testId)
    {
        if (testId > 0) addTestToCurrentLabOrder(testId);

        grdListLabTest.SelectedItem = null;
        cboLabTest.Text = null;
        _testSearchString = null;
    }

    private bool applySelectedReferrer()
    {
        var refId = -1;
        if (grdListPhysician.SelectedValue != null) refId = (int)grdListPhysician.SelectedValue;

        if (refId < 0) {
            //cboPhysician.Focus();
            comboDoPopup(cboPhysician, false);
        }
        else {
            setCurrentReferrerId(refId);
            return true;
        }

        return false;
    }

    private void applySelectedReferrerId(int id)
    {
        _suppressReferrerComboTextChange = true;
        cboPhysician.Text = FaultHandler.Shield(() => ReferrersRepository.GetReferrerName(id));
        _suppressReferrerComboTextChange = false;

        setCurrentReferrerId(id);
    }

    private void setCurrentReferrerId(int refId)
    {
        if (refId > 0) {
            grdListPhysician.SelectedValue = refId;
            _currentLabOrder.ReferrerId = refId;
            _currentLabOrder.IsReferrerUnknown = false;
        }
        else {
            grdListPhysician.ClearSelected();
            _currentLabOrder.ReferrerId = null;
            _currentLabOrder.IsReferrerUnknown = true;
        }
    }

    private void setCustomerId(long? id)
    {
        _currentLabOrder.CustomerId = id is > 0 ? id : null;
        //_currentLabOrder.RegisteredMemberId = id;
        //_currentLabOrder.RegisteredMemberId = null;
    }

    private void resetSearchComboControls(ComboBoxBase combo, GridListControl grid)
    {
        combo.Text = null;
        grid.SelectedIndex = -1;
    }

    void catalogUpdateReferrers()
    {
        var catalog = ActiveReferringPhysiciansRepository.GetCatalog(true);
        ReferrerSearchEngine.AssignCatalog(catalog);
    }

    void catalogUpdateCorporate() => _corporateClients = CorporateClientRepository.FetchActive();

    void catalogUpdateAffiliates() => _affiliates = AffiliateRepository.FetchActive();

    void catalogUpdateAssocLabs() => _associateLabs = AssociateOrganizationRepository.FetchActive();

    void catalogUpdateBatteries() => _batteries = BatteryMasterRepository.GetActiveBatteries();

    void catalogUpdateLabTests()
    {
        var catalog = OrderableTestSliceCatalogRepository.GetCatalog(true);
        LabTestSearchEngine.AssignCatalog(catalog);

        _labTestsSearchCollection.BeginUpdate();
        try {
            _labTestsSearchCollection.Clear();
            foreach (var slice in catalog)
                _labTestsSearchCollection.Add(
                    new LabTestInfo { TestId = slice.TestId, TestName = slice.ShortName.ToLower() }
                );
        }
        finally {
            _labTestsSearchCollection.EndUpdate();
        }
    }

    void refreshAccessoryCatalogsAsync(bool fullRefresh)
    {
        throw new NotSupportedException("Entity manager does not support multi-threaded access.");

        ReferrerSearchEngine.ResetCatalog();

        FaultHandler.Shield(
            () =>
                WaitFormControl.WaitOperation(
                    this,
                    () =>
                    {
                        WaitFormControl.WaitStart(this);
                        PatientLabOrdersRepository.Reset();

                        List<Task> tasks =
                        [
                            Task.Factory.StartNew(catalogUpdateReferrers),
                            Task.Factory.StartNew(catalogUpdateLabTests)
                        ];

                        if (fullRefresh) {
                            tasks.Add(Task.Factory.StartNew(catalogUpdateCorporate));
                            tasks.Add(Task.Factory.StartNew(catalogUpdateAffiliates));
                            tasks.Add(Task.Factory.StartNew(catalogUpdateAssocLabs));
                            tasks.Add(Task.Factory.StartNew(catalogUpdateBatteries));
                        }

                        Task.WaitAll(tasks.ToArray());
                    }
                )
        );
    }

    private void handleSearchComboBoxKeyUp(ComboBoxBase combo, GridListControl grid, KeyEventArgs e)
    {
        var isTestComboBox = combo == cboLabTest;

        switch (e.KeyCode) {
            case KEY_SEARCH_QUICKSELECT:
                e.Handled = true;
                if (isTestComboBox) {
                    var tests = LabTestQuickSelectionDialog.ExecuteDialog(this, false);
                    if (tests is { Count: > 0 })
                        foreach (var id in tests)
                            applySelectedTestId(id);
                }
                else {
                    var refId = ReferrerQuickSelectionDialog.ExecuteDialog(this, false, false,
                                                                           SearchEntityType.Physician);
                    if (refId > 0) {
                        applySelectedReferrerId(refId);
                        cboLabTest.Focus();
                    }
                }

                refreshAccessoryCatalogsIfExpired(false, 20);
                //refreshAccessoryCatalogsAsync(false);
                break;

            case KEY_SEARCH_ACCEPT:
                e.Handled = true;
                if (grid.SelectedIndex != -1) {
                    combo.SuppressDropDownEvent = true;
                    combo.DroppedDown = false;
                    combo.SuppressDropDownEvent = false;
                }

                if (isTestComboBox)
                    applySelectedTest();
                else if (applySelectedReferrer()) cboLabTest.Focus();

                break;

            case KEY_SEARCH_POPUP:
                e.Handled = true;
                _searchMode = SearchMode.Any;

                if ((ModifierKeys & Keys.Shift) != 0) _searchMode = SearchMode.BeginsWith;

                if ((ModifierKeys & Keys.Alt) != 0) _searchMode = SearchMode.EndsWith;

                comboDoPopup(combo, isTestComboBox);
                break;

            case KEY_SEARCH_CANCEL:
                e.Handled = true;
                combo.Text = null;
                grid.SelectedIndex = -1;

                if (isTestComboBox)
                    _testSearchString = null;
                else
                    _physicianSearchString = null;

                break;

            case KEY_BATTERY_SELECT:
                e.Handled = true;
                if (isTestComboBox) {
                    _testSearchString = null;
                    showBatterySelectionDialog();
                }

                break;
        }
    }

    private void showBatterySelectionDialog()
    {
        using var frmBattery = new BatterySelectionDialog();
        frmBattery.SetBatteries(_batteries);
        if (frmBattery.ShowDialog(this) != DialogResult.OK) return;

        var batteryId = frmBattery.SelectedBatteryId;
        var components =
            FaultHandler.Shield(() => BatteryComponentRepository.GetBatteryComponentsDetailed(batteryId, true));
        foreach (var component in components) addTestToCurrentLabOrderSilent(component.LabTestId);
    }

    private void comboDoPopup(ComboBoxBase combo, bool isTestComboBox)
    {
        try {
            combo.SuppressDropDownEvent = true;

            if (isTestComboBox)
                searchLabTest(_searchMode);
            else
                searchPhysicians();

            combo.DroppedDown = true;
            combo.SuppressDropDownEvent = false;
        }
        catch {
            combo.Text = null;
        }
    }

    private void cboPhysician_KeyUp(object sender, KeyEventArgs e) =>
        handleSearchComboBoxKeyUp(cboPhysician, grdListPhysician, e);

    private void cboPhysician_DropDown(object sender, EventArgs e) => searchPhysicians();

    private void txtAge_TextChanged(object sender, EventArgs e)
    {
        var text = txtAge.Text.Trim();

        if (!string.IsNullOrEmpty(text)) {
            if (dteDateOfBirth.Enabled) resetDoBTextBox(false);
        }
        else {
            if (!dteDateOfBirth.Enabled) resetDoBTextBox(true);
        }
    }

    private void dtDateOfBirth_TextChanged(object sender, EventArgs e)
    {
        var enable = string.IsNullOrEmpty(dteDateOfBirth.Text.Trim());
        resetAgeTextBoxes(enable);
    }

    private void resetAgeTextBoxes(bool enable)
    {
        txtAge.Text = null;
        txtAge.Enabled = enable;
    }

    private void resetDoBTextBox(bool enable)
    {
        //dteDateOfBirth.Text = null;
        dteDateOfBirth.EditValue = null;
        dteDateOfBirth.Enabled = enable;
    }

    private bool containsData()
    {
        initializeOrderInfoFromControls();
        if (!string.IsNullOrEmpty(_currentLabOrder.FirstName)) return true;
        return _currentLabOrder.OrderedTests.Count > 0;
    }

    private void btnNewLabOrder_ItemClick(object sender, ItemClickEventArgs e)
    {
        if (containsData() && !MessageDlg.Confirm("Do you want to create a new order?")) return;

        prepareForNewOrder();

        focusControl(txtTelephone);
    }

    void focusControl(Control ctrl)
    {
        ctrl.Focus();
        ctrl.Select();
    }

    private void prepareForNewOrder()
    {
        resetSearchComboControls(cboLabTest, grdListLabTest);
        resetSearchComboControls(cboPhysician, grdListPhysician);
        makeTextEditMandatory(txtFirstName, false);
        makeTextEditMandatory(txtPhysicianCustomName, false);
        rgSex.SelectedIndex = 0;
        chkUnknownPhysician.Checked = false;

        resetAgeTextBoxes(true);
        resetDoBTextBox(true);

        txtCustomerPHN.Text = null;
        txtCustomerPhone.Text = null;
        txtExtTrackingId.Text = null;
        txtTelephone.Text = null;
        txtEmail.Text = null;

        cboCorporateClients.SelectedIndex = -1;
        cboHealthPackages.SelectedIndex = -1;
        cboAffiliates.SelectedIndex = -1;
        cboAffiliateLabs.SelectedIndex = -1;

        _currentLabOrder.Reset();
        _currentLabOrder.OrderingUserName = CurrentUserContext.UserDisplayName;
        populateControlsFromOrderInfo(_currentLabOrder);
        _invoiceCalc.Reset();

        resetDiscountControls();
        updateOrderChanges();
        initializeComboBoxes();

        resetSelectedItemLabels();
        updateCustomerLabels(null);

        // assign global associate lab 
        if (hasGlobalAssociateLab())
            setGlobalAssociateLab(_globalAssociateLabId);
        else
            setGlobalAssociateLab(null);
    }


    private void searchLabTest(SearchMode mode)
    {
        if (string.IsNullOrEmpty(_testSearchString)) _testSearchString = cboLabTest.Text.Trim().ToLowerInvariant();

        _testSearchString = sanitizeSearchString(_testSearchString);
        List<LabTestSearchRec> searchRecs;
        if (string.IsNullOrEmpty(_testSearchString))
            searchRecs = LabTestSearchEngine.GetCatalog();
        else
            searchRecs = mode switch
            {
                SearchMode.BeginsWith => LabTestSearchEngine.SearchBeginsWith(_testSearchString, MAX_SEARCH_RESULTS),
                SearchMode.EndsWith   => LabTestSearchEngine.SearchEndsWith(_testSearchString, MAX_SEARCH_RESULTS),
                _                     => LabTestSearchEngine.Search(_testSearchString, MAX_SEARCH_RESULTS)
            };

        updateLabTestDropDownGrid(searchRecs.Any() ? searchRecs : LabTestSearchEngine.GetCatalog());
    }

    private string buildSearchQuery(SearchMode mode) =>
        mode switch
        {
            SearchMode.BeginsWith => $"{_testSearchString}*",
            SearchMode.EndsWith   => $"*{_testSearchString}",
            _                     => $"*{_testSearchString}*"
        };

    private string sanitizeSearchString(string s)
    {
        s = s.Trim();
        return string.IsNullOrEmpty(s) ? s : s.Replace("-", " ").Replace("/", " ").Replace("\"", " ");
    }

    private void updateLabTestDropDownGrid(List<LabTestSearchRec> items)
    {
        grdListLabTest.BeginUpdate();
        try {
            grdListLabTest.TopIndex = 0;
            grdListLabTest.SelectedIndex = -1;
            grdListLabTest.MultiColumn = true;
            grdListLabTest.SelectionMode = SelectionMode.One;
            grdListLabTest.DisplayMember = "Name";
            grdListLabTest.ValueMember = "Id";

            grdListLabTest.DataSource = items;
        }
        finally {
            grdListLabTest.EndUpdate();
        }

        grdListLabTest.Focus();
    }

    private void cboLabTest_DropDown(object sender, EventArgs e) => searchLabTest(_searchMode);

    private void cboLabTest_KeyUp(object sender, KeyEventArgs e) =>
        handleSearchComboBoxKeyUp(cboLabTest, grdListLabTest, e);

    private void cboLabTest_TextChanged(object sender, EventArgs e) =>
        _testSearchString = cboLabTest.Text.Trim().ToLowerInvariant();

    private void txtAge_KeyUp(object sender, KeyEventArgs e)
    {
        var control = sender as TextEdit;
        switch (e.KeyCode) {
            case Keys.Escape:
                e.Handled = true;
                control.Text = null;
                break;

            case Keys.Delete:
                e.Handled = true;
                resetAgeTextBoxes(true);
                resetDoBTextBox(true);
                break;

            case KEY_FIX_CASING:
                e.Handled = true;
                fixCasingForPatientNameAndAge();
                if (string.IsNullOrEmpty(getTelephone()))
                    focusSelectControl(txtTelephone);
                else
                    focusSelectControl(cboPhysician);
                break;
        }
    }

    private void focusSelectControl(Control ctrl)
    {
        if (ctrl.CanFocus) ctrl.Focus();
        if (ctrl.CanSelect) ctrl.Select();
    }

    private string getAge() => txtAge.Text.Trim().ToUpper();

    private void fixCasingForPatientNameAndAge()
    {
        titleCaseTextBox(txtFirstName, true);
        titleCaseTextBox(txtLastName, false);
        var age = getAge();
        if (!string.IsNullOrEmpty(age) && SharedUtilities.IsDigitsOnly(age)) age += "Y";
        txtAge.Text = age;
    }

    private void dtDateOfBirth_KeyUp(object sender, KeyEventArgs e)
    {
        switch (e.KeyCode) {
            case KEY_SEARCH_CANCEL:
                e.Handled = true;
                resetDoBTextBox(true);
                resetAgeTextBoxes(true);
                break;
        }
    }

    private void txtPhysicianCustomName_KeyUp(object sender, KeyEventArgs e)
    {
        switch (e.KeyCode) {
            case KEY_SEARCH_CANCEL:
                e.Handled = true;
                txtPhysicianCustomName.Text = null;
                break;
            case KEY_FIX_CASING:
                e.Handled = true;
                titleCaseTextBox(txtPhysicianCustomName, false);
                _currentLabOrder.ReferrerCustomName = txtPhysicianCustomName.Text;
                break;
            case KEY_COPY_CUSTOM_REFERRER:
                e.Handled = true;
                var text = cboPhysician.Text.Trim();
                if (!string.IsNullOrEmpty(text)) txtPhysicianCustomName.Text = text;
                _currentLabOrder.ReferrerCustomName = text;
                break;
        }
    }

    /// <summary>
    ///     Determines if age or DoB has been specified
    /// </summary>
    /// <returns> True if age was specified, false if DoB was specified. </returns>
    private bool determineAgeOrDoB()
    {
        if (txtAge.Enabled == false && dteDateOfBirth.Enabled && dteDateOfBirth.DateTime > DateTime.MinValue)
            return false;

        return txtAge.Enabled && !string.IsNullOrEmpty(txtAge.Text.Trim());
    }

    private void addTestToCurrentLabOrder(short testId)
    {
        if (_currentLabOrder.TestAlreadyOrdered(testId)) {
            MessageDlg.Warning(@"Duplicate tests cannot be ordered!");
            return;
        }

        var slice = FaultHandler.Shield(() => OrderableTestSliceCatalogRepository.FindById(testId));
        _currentLabOrder.AddTest(slice, DateTime.Now);
        _invoiceCalc.Reset();
        if (_orderAssociateLabId > 0)
            _currentLabOrder.ClearBillableItems();
        else
            _currentLabOrder.CompileBillableItemsList();

        _invoiceCalc.CalculateInvoice(_currentLabOrder);
        applyAppSurcharge(false);
        updateOrderChanges();
    }

    private void addTestToCurrentLabOrderSilent(short testId)
    {
        if (_currentLabOrder.TestAlreadyOrdered(testId)) return;

        var slice = FaultHandler.Shield(() => OrderableTestSliceCatalogRepository.FindById(testId));
        _currentLabOrder.AddTest(slice, DateTime.Now);
        _invoiceCalc.Reset();
        if (_orderAssociateLabId > 0)
            _currentLabOrder.ClearBillableItems();
        else
            _currentLabOrder.CompileBillableItemsList();

        _invoiceCalc.CalculateInvoice(_currentLabOrder);
        applyAppSurcharge(false);
        updateOrderChanges();
    }

    private void updateOrderChanges()
    {
        logSi(nameof(updateOrderChanges));
        updateOrderedTestsGrid();
        updateOrderedBillableItemsGrid();
        updateFinanceLabels();
        updateDiscountControls();
    }

    private void updateDiscountControls()
    {
        logSi(nameof(updateDiscountControls));

        txtDiscountAmount.Value = 0m;
        if (_invoiceCalc.DiscountAmount > 0)
            txtDiscountAmount.Value = _invoiceCalc.DiscountAmount;

        updateDiscountNoteTextbox(true);
        /*
        else
            txtDiscountAmount.Text = null;

        txtDiscountPercent.Text = null;
        txtDiscountNote.Text = !string.IsNullOrEmpty(_currentLabOrder.DiscountNotes)
            ? _currentLabOrder.DiscountNotes
            : null;
        */
    }

    private void updateDiscountNoteTextbox(bool applyDefault)
    {
        var note = _currentLabOrder.DiscountNotes;
        if (string.IsNullOrEmpty(note) && applyDefault) {
            if (_currentLabOrder.CorporateClientId != null)
                note = "Corp.";
            else if (chkOutsideOrder.Checked || _currentLabOrder.AssociateLabId != null || _globalAssociateLabId > 0)
                note = "Subcontr.";
            else
                note = "Dr.";
        }

        setDiscountNoteText(note);
    }

    private void setDiscountNoteText(string? text) => txtDiscountNote.Text = text;

    private string getDiscountNoteText(bool applyCasing)
    {
        var note = txtDiscountNote.Text.Trim();
        return applyCasing ? CultureInfo.CurrentCulture.TextInfo.ToTitleCase(note) : note;
    }

    private void resetDiscountControls()
    {
        logSi(nameof(resetDiscountControls));
        txtDiscountAmount.Text = null;
        txtDiscountPercent.Text = null;
        setDiscountNoteText(null);

        updateDiscountNoteTextbox(true);
        updateEffectiveDiscountPctLabelColor(0);
    }

    private float getDiscountPercent() => float.TryParse(txtDiscountPercent.Text.Trim(), out var amt) ? amt : -1;

    private void updateOrderedTestsGrid()
    {
        grdOrderedTests.BeginInit();
        try {
            grdOrderedTests.DataRows.Clear();
            foreach (var slice in _currentLabOrder.OrderedTests) {
                var row = grdOrderedTests.DataRows.AddNew();
                row.Cells["TestName"].Value = slice.ShortName;
                row.Cells["TestPrice"].Value = SharedUtilities.MoneyToString(slice.ListPrice);
                if (slice.DeliveryTime != null) {
                    var dt = (DateTime)slice.DeliveryTime;
                    row.Cells["DeliveryETA"].Value =
                        $"{SharedUtilities.DateToStringShort(dt)} {SharedUtilities.TimeToStringShort(dt)}";
                }

                var cellDiscount = row.Cells["MaxDiscount"];
                cellDiscount.Value = SharedUtilities.MoneyToString(slice.MaxApplicableDiscount);
                cellDiscount.ForeColor = Color.LightSlateGray;
                cellDiscount.HorizontalAlignment = HorizontalAlignment.Right;

                row.Height = 28;
                row.VerticalAlignment = VerticalAlignment.Center;
                row.Tag = slice;
                row.EndEdit();
            }
        }
        finally {
            grdOrderedTests.EndInit();
        }
    }

    private void updateOrderedBillableItemsGrid()
    {
        grdBillableItems.BeginInit();
        try {
            grdBillableItems.DataRows.Clear();
            foreach (var slice in _currentLabOrder.OrderedBillableItems) {
                var row = grdBillableItems.DataRows.AddNew();
                row.Cells["Name"].Value = slice.Name;
                row.Cells["Qty"].Value = slice.Quantity.ToString();
                row.Cells["Price"].Value = SharedUtilities.MoneyToString(slice.UnitPrice);
                row.Cells["Price"].HorizontalAlignment = HorizontalAlignment.Right;

                row.Height = 28;
                row.VerticalAlignment = VerticalAlignment.Center;
                row.Tag = slice;
                row.EndEdit();
            }
        }
        finally {
            grdBillableItems.EndInit();
        }
    }

    private void updateFinanceLabels()
    {
        lblGrossPayable.Text = SharedUtilities.MoneyToString(_invoiceCalc.GrossPayable);
        lblNumTests.Text = _currentLabOrder.OrderedTests.Count.ToString();
        lblNetPayable.Text = SharedUtilities.MoneyToString(_invoiceCalc.NetPayable);
        lblMaxDiscountPercent.Text = $"( {_invoiceCalc.MaxApplicableDiscountPercent}% )";
        lblMaxDiscountAmount.Text = SharedUtilities.MoneyToString(_invoiceCalc.MaxApplicableDiscountAmount);
        updateEffectiveDiscountPct(_invoiceCalc.DiscountAmount);
        //lblSurcharge.Text = "0";
    }

    private void updateEffectiveDiscountPctLabelColor(decimal percent)
    {
        percent = Math.Round(percent, 0);
        lblEffectiveDiscountPct.ForeColor = Color.White;
        if (percent >= 100)
            pnlEffectiveDiscountPct.BackColor = Color.Tomato;
        else if (percent >= (decimal)_invoiceCalc.MaxApplicableDiscountPercent)
            pnlEffectiveDiscountPct.BackColor = Color.Orange;
        else if (percent > 0)
            pnlEffectiveDiscountPct.BackColor = Color.YellowGreen;
        else {
            pnlEffectiveDiscountPct.BackColor = Color.WhiteSmoke;
            lblEffectiveDiscountPct.ForeColor = Color.DimGray;
        }
    }

    private void updateEffectiveDiscountPct(decimal discount)
    {
        var pct = getRoundedDiscountPercent(_invoiceCalc.GrossPayable, discount);
        updateEffectiveDiscountPctLabelColor(pct);
        lblEffectiveDiscountPct.Text = pct > 0 ? $"{pct.ToString(CultureInfo.InvariantCulture)}%" : null;
    }

    private void updateSexRadioGroup(SexType value)
    {
        rgSex.SelectedIndex = value switch
        {
            SexType.Male   => 0,
            SexType.Female => 1,
            _              => 2
        };
    }

    private SexType getSexTypeFromRadioGroup() =>
        rgSex.SelectedIndex switch
        {
            0 => SexType.Male,
            1 => SexType.Female,
            _ => SexType.Unknown
        };

    private void populateControlsFromDemographicInfo(LabOrderDemographicInfoSlice orderInfo)
    {
        txtFirstName.Text = orderInfo.FirstName;
        txtLastName.Text = orderInfo.LastName;
        cboPatientTitle.Text = orderInfo.Title;
        chkUnknownPhysician.Checked = orderInfo.IsReferrerUnknown;
        txtPhysicianCustomName.Text = orderInfo.ReferrerCustomName;
        if (orderInfo.ReferrerId != null) {
            var refId = (int)orderInfo.ReferrerId;
            cboPhysician.Text = FaultHandler.Shield(() => ActiveReferringPhysiciansRepository.GetPhysicianName(refId));
            setCurrentReferrerId(refId);
        }
        else {
            cboPhysician.Text = null;
            setCurrentReferrerId(-1);
        }

        txtTelephone.Text = orderInfo.PhoneNumber;
        txtOrderNotes.Text = orderInfo.OrderNotes;
        updateSexRadioGroup(orderInfo.Sex);
        chkDirectPatient.Checked = orderInfo.DisallowReferral;

        // todo: apply assoc labs, corporate, affiliate, PHN...
        chkOutsideOrder.Checked = orderInfo.IsExternalSubOrder;
        txtExtTrackingId.Text = orderInfo.AssociateLabAccessionId;
        if (orderInfo.AssociateLabId != null) { }

        if (orderInfo.CorporateClientId != null) { }

        if (orderInfo.AffiliateId != null) { }

        if (orderInfo.CustomerId != null) { }

        if (orderInfo.DoB != null)
            dteDateOfBirth.EditValue = orderInfo.DoB;
        else
            txtAge.Text = orderInfo.Age;

        resetSearchComboControls(cboLabTest, grdListLabTest);
    }

    private void logSi(string? methodName = null)
    {
#if DEBUG
        if (!string.IsNullOrEmpty(methodName))
            SiAuto.Main.EnterMethod(methodName);

        //SiAuto.Main.AddCheckpoint(methodName);
        SiAuto.Main.LogString("_currentLabOrder.DiscountNotes", _currentLabOrder.DiscountNotes);
        SiAuto.Main.LogString("txtDiscountNote", getDiscountNoteText(true));
        SiAuto.Main.LogString("txtDiscountPercent", txtDiscountPercent.Text);
        SiAuto.Main.LogString("txtDiscountAmount", txtDiscountAmount.Text);

        if (!string.IsNullOrEmpty(methodName))
            SiAuto.Main.LeaveMethod(methodName);
#endif
    }

    private void populateControlsFromOrderInfo(LabOrderContextInfo orderInfo)
    {
        txtFirstName.Text = orderInfo.FirstName;
        txtLastName.Text = orderInfo.LastName;
        cboPatientTitle.Text = orderInfo.Title;
        chkUnknownPhysician.Checked = orderInfo.IsReferrerUnknown;
        txtPhysicianCustomName.Text = orderInfo.ReferrerCustomName;
        cboPhysician.Text = orderInfo.ReferrerId != null
            ? FaultHandler.Shield(() => ActiveReferringPhysiciansRepository.GetPhysicianName((int)orderInfo.ReferrerId))
            : null;
        txtTelephone.Text = orderInfo.PhoneNumber;
        txtOrderNotes.Text = orderInfo.OrderNotes;
        updateSexRadioGroup(orderInfo.Sex);
        chkDirectPatient.Checked = orderInfo.DisallowReferral;
        chkOutsideOrder.Checked = orderInfo.OrderDemoInfo.IsExternalSubOrder;
        txtExtTrackingId.Text = orderInfo.OrderDemoInfo.AssociateLabAccessionId;

        if (orderInfo.DoB != null)
            dteDateOfBirth.EditValue = orderInfo.DoB;
        else
            txtAge.Text = orderInfo.Age;

        resetSearchComboControls(cboLabTest, grdListLabTest);
    }

    private void initializeOrderInfoFromControls()
    {
        _currentLabOrder.FirstName = txtFirstName.Text.Trim();
        _currentLabOrder.LastName = txtLastName.Text.Trim();
        _currentLabOrder.Title = cboPatientTitle.Text.Trim();
        _currentLabOrder.IsCancelled = false;
        if (orderHasAssociateLab()) {
            updateOrderAssociateLab(_orderAssociateLabId, false, false);
        }
        else {
            _currentLabOrder.IsExternalSubOrder = chkOutsideOrder.Checked;
            if (_currentLabOrder.IsExternalSubOrder) updateAffiliateLab();
        }

        // check if the referrer was not set already
        if (_currentLabOrder.ReferrerId == null) {
            _currentLabOrder.ReferrerCustomName = txtPhysicianCustomName.Text.Trim();
            _currentLabOrder.IsReferrerUnknown = chkUnknownPhysician.Checked ||
                                                 grdListPhysician.SelectedValue == null;

            if (!_currentLabOrder.IsReferrerUnknown && grdListPhysician.SelectedValue != null)
                _currentLabOrder.ReferrerId = (int)grdListPhysician.SelectedValue;
            else
                _currentLabOrder.ReferrerId = null;
        }

        _currentLabOrder.OrderNotes = txtOrderNotes.Text.Trim();
        _currentLabOrder.PhoneNumber = SharedUtilities.DigitsOnly(getTelephone());
        _currentLabOrder.Sex = getSexTypeFromRadioGroup();

        logSi(nameof(initializeOrderInfoFromControls));

        _currentLabOrder.DiscountNotes = getDiscountNoteText(true);
        if (!_currentLabOrder.IsExternalSubOrder)
            _currentLabOrder.DisallowReferral = chkDirectPatient.Checked;

        if (determineAgeOrDoB()) {
            _currentLabOrder.DoB = null;
            _currentLabOrder.Age = txtAge.Text.Trim();
        }
        else {
            _currentLabOrder.Age = null;
            _currentLabOrder.DoB = dteDateOfBirth.DateTime.Date;
        }

        _currentLabOrder.ResultsReleaseFlag = getResultReleaseFlag();
    }

    private bool orderHasAssociateLab() => _orderAssociateLabId > 0;
    private bool hasGlobalAssociateLab() => _globalAssociateLabId > 0;

    private void deleteTestFromCurrentLabOrder()
    {
        if (grdOrderedTests.SelectedRows.Count > 0) {
            var sb = new StringBuilder();
            sb.AppendLine($"Really delete the following {grdOrderedTests.SelectedRows.Count} test(s)?\n");
            foreach (var slice in from Row row in grdOrderedTests.SelectedRows select (OrderableTestSlice)row.Tag)
                sb.AppendLine(slice.ShortName);

            if (!MessageDlg.Confirm(sb.ToString()))
                return;

            foreach (Row row in grdOrderedTests.SelectedRows) {
                var slice = (OrderableTestSlice)row.Tag;
                _currentLabOrder.RemoveTest(slice.TestId);
            }

            _invoiceCalc.Reset();
            if (hasNoAssociateLab())
                _currentLabOrder.CompileBillableItemsList();
            else
                _currentLabOrder.ClearBillableItems();

            _invoiceCalc.CalculateInvoice(_currentLabOrder);

            applyAppSurcharge(false);
            updateOrderChanges();
            return;
        }

        MessageDlg.Warning("No test(s) selected!");
    }

    private void clearAllTestFromCurrentLabOrder()
    {
        if (_currentLabOrder.OrderedTests.Count > 0 && MessageDlg.Confirm("Clear all tests?")) {
            _currentLabOrder.ClearOrderedTests();
            _invoiceCalc.Reset();
            applyAppSurcharge(false);
            updateOrderChanges();
        }
    }

    private void btnDeleteTest_ItemClick(object sender, ItemClickEventArgs e) => deleteTestFromCurrentLabOrder();

    private void btnClearAllTests_ItemClick(object sender, ItemClickEventArgs e) => clearAllTestFromCurrentLabOrder();

    private void btnCopyDemographics_ItemClick(object sender, ItemClickEventArgs e)
    {
        initializeOrderInfoFromControls();
        //_copyOfOrderDemographics = _currentLabOrder.ShallowClone();
        var item = new LabOrderDemographicInfoSlice();
        item.AssignFrom(_currentLabOrder.OrderDemoInfo);
        LabOrderDemographicHistory.Add(item);
    }

    private void btnPasteDemographics_ItemClick(object sender, ItemClickEventArgs e)
    {
        var info = OrderDemographicHistoryBrowserDialog.ExecuteDialog(this);
        if (info != null) {
            populateControlsFromDemographicInfo(info);
            initializeOrderInfoFromControls();
            _invoiceCalc.Reset();
            updateOrderChanges();
        }
    }

    private void btnCancelOrder_ItemClick(object sender, ItemClickEventArgs e)
    {
        if (!MessageDlg.Confirm("Really cancel this order?")) return;
        if (!CaptchaDialog.ConfirmCaptcha(3, false, false)) return;
        _currentLabOrder.Reset();
        populateControlsFromOrderInfo(_currentLabOrder);
        _invoiceCalc.Reset();
        resetDiscountControls();
        updateOrderChanges();
    }

    private bool validateOrderContext()
    {
        initializeOrderInfoFromControls();

        if (string.IsNullOrEmpty(_currentLabOrder.FirstName)) {
            makeTextEditMandatory(txtFirstName, true);
            MessageDlg.Warning("Patient's first name is required!");
            txtFirstName.Focus();
            return false;
        }

        if (rgSex.SelectedIndex == -1) {
            MessageDlg.Warning("Patient's sex is required!");
            rgSex.Focus();
            return false;
        }

        if (_currentLabOrder.OrderedTests.Count == 0) {
            MessageDlg.Error("This order doesn't contain any tests!");
            return false;
        }

        if (_invoiceCalc.DiscountAmount > _invoiceCalc.GrossPayable) {
            MessageDlg.Error("Discount amount cannot be greater than the gross payable amount!");
            return false;
        }

        if (_invoiceCalc.DiscountAmount > 0 && string.IsNullOrEmpty(_currentLabOrder.DiscountNotes)) {
            MessageDlg.Warning("Discount note is required!");
            txtDiscountNote.Focus();
            return false;
        }

        _currentLabOrder.PhoneNumber = null;
        _currentLabOrder.EmailAddress = null;

        if (orderHasAssociateLab()) updateOrderAssociateLab(_orderAssociateLabId, false, false);

        if (orderHasAssociateLab() &&
            string.IsNullOrEmpty(getExternaleOrderId()) &&
            MessageDlg.Confirm(
                "This is an external subcontract order, and no tracking ID was provided.\nWould you like to enter a tracking ID now?")) {
            txtExtTrackingId.Focus();
            return false;
        }

        if (txtTelephone.Enabled) {
            var phone = SharedUtilities.GetPhoneLocalPart(getTelephone());
            txtTelephone.Text = phone;

            if (hasNoAssociateLab() &&
                string.IsNullOrEmpty(phone) &&
                MessageDlg.Confirm("No telephone number entered.\nDo you want to enter phone number now?")) {
                txtTelephone.Focus();
                return false;
            }

            if (!string.IsNullOrEmpty(phone)) {
                if (!SharedUtilities.IsValidMobileNumber(phone)) {
                    MessageDlg.Error(
                        "The phone number is incorrect. Please provide a valid, 11 digit mobile phone number.\nA valid Bangladesh mobile number format looks like this: 01XXX XXX XXX");
                    txtTelephone.Focus();
                    return false;
                }

                _currentLabOrder.PhoneNumber = phone;
            }
        }

        if (txtEmail.Enabled) {
            var email = getEmailAddress();
            if (!string.IsNullOrEmpty(email) && hasNoAssociateLab()) {
                if (!SharedUtilities.IsValidEmail(email)) {
                    MessageDlg.Error(
                        "The e-mail address seems incorrect.\nPlease enter a valid e-mail address or leave the form blank.");
                    txtEmail.Focus();
                    return false;
                }

                _currentLabOrder.EmailAddress = email;
            }
        }

        if (orderHasAssociateLab()) {
            _currentLabOrder.DisallowReferral = true;
        }
        else if (!chkDirectPatient.Checked) {
            var direct = ReferralAllowDialog.Execute(this, out var cancel);
            if (cancel)
                return false;

            chkDirectPatient.Checked = direct;
            _currentLabOrder.DisallowReferral = direct;
        }

        return true;
    }

    private string getEmailAddress() => txtEmail.Text.Trim().ToLowerInvariant();

    private string getTelephone() => txtTelephone.Text.Trim();

    private void chkUnknownPhysician_CheckedChanged(object sender, EventArgs e) =>
        updatePhysicianControls(chkUnknownPhysician.Checked);

    private void updatePhysicianControls(bool isUnknownReferrer)
    {
        cboPhysician.Enabled = !isUnknownReferrer;
        cboPhysician.Text = null;
        grdListPhysician.SelectedIndex = -1;
        makeTextEditMandatory(txtPhysicianCustomName, isUnknownReferrer);
        if (isUnknownReferrer)
            txtPhysicianCustomName.Focus();
        else
            cboPhysician.Focus();
    }

    private void makeTextEditMandatory(TextEdit control, bool enable) =>
        control.BackColor = enable ? Color.Khaki : Color.White;

    private void btnSetDiscountPercent_Click(object sender, EventArgs e)
    {
        logSi(nameof(btnSetDiscountPercent_Click));

        if (!calculateDiscountAmountFromPercent(out var discAmt, false))
            return;

        txtDiscountAmount.Text = SharedUtilities.MoneyToString(discAmt);
        txtDiscountNote.Focus();
    }

    private bool calculateDiscountAmountFromPercent(out decimal discAmt, bool suppressError)
    {
        discAmt = 0m;
        var pct = getDiscountPercent();
        if (pct < 0) {
            if (!suppressError)
                MessageDlg.Error($"Invalid discount percent: \"{txtDiscountPercent.Text}\"");
            return false;
        }

        if (pct is < 0 or > 100) {
            if (!suppressError)
                MessageDlg.Error($"Invalid discount percent: \"{pct}%\"");
            return false;
        }

        if (hasNoAssociateLab() && pct > _invoiceCalc.MaxApplicableDiscountPercent)
            if (
                !MessageDlg.Confirm(
                    $"The discount percentage you entered ({pct}%) exceeds the allowed maximum discount rate of {_invoiceCalc.MaxApplicableDiscountPercent}%!\nDo you wish to apply this discount?"))
                return false;

        _invoiceCalc.Reset();
        if (orderHasAssociateLab())
            _currentLabOrder.ClearBillableItems();
        else
            _currentLabOrder.CompileBillableItemsList();

        _invoiceCalc.CalculateInvoice(_currentLabOrder);
        discAmt = _invoiceCalc.CalculateDiscountPercent(pct, orderHasAssociateLab());
        return true;
    }

    private decimal getEnteredDiscountAmount(bool calculateFromPercent)
    {
        var txt = txtDiscountAmount.Text.Trim();

        var amount = 0m;
        if (!string.IsNullOrEmpty(txt))
            return decimal.TryParse(txt, out amount) ? amount : 0m;

        if (calculateFromPercent) {
            // calculate percentage if amount not set already
            if (!calculateDiscountAmountFromPercent(out amount, true))
                return 0m;

            txtDiscountAmount.Text = amount.ToString();
        }

        return amount;
    }

    private void btnApplyDiscount_Click(object sender, EventArgs e) => applyDiscount();

    private void applyDiscount()
    {
        logSi(nameof(btnApplyDiscount_Click));

        var discountAmount = getEnteredDiscountAmount(true);

        if (discountAmount == 0m) {
            applyAppSurcharge(true);
            return;
        }

        if (discountAmount < 0m || discountAmount > _invoiceCalc.GrossPayable) {
            MessageDlg.Error(
                $"Invalid discount amount: \"{discountAmount:N0}\"\nDiscount amount must be greater than 0 and less than the Gross Bill amount.");
            return;
        }

        var note = getDiscountNoteText(true);
        if (string.IsNullOrEmpty(note)) {
            MessageDlg.Warning("Please enter a valid discount note!");
            return;
        }

        // check discount barrier if individual patient
        if (hasNoAssociateLab() && discountAmount > _invoiceCalc.MaxApplicableDiscountAmount)
            if (
                !MessageDlg.Confirm(
                    $"The discount amount you entered ({discountAmount:N0}) exceeds the maximum allowable discount of {_invoiceCalc.MaxApplicableDiscountAmount:N0}.\nDo you want to apply this discount?"))
                return;

        // add surcharge to discount 
        if (_reimburseSurcharge && surchargeApplies()) {
            var net = _invoiceCalc.GrossPayable - discountAmount;
            _invoiceCalc.CalculateSurchargeCustom(net, discountAmount);
            discountAmount += _invoiceCalc.SurchargeAmount;
            txtDiscountAmount.Text = discountAmount.ToString();
        }

        _invoiceCalc.SetDiscountAmount(discountAmount);

        applyAppSurcharge(false);
        updateOrderedBillableItemsGrid();
        updateFinanceLabels();
    }

    private static decimal getRoundedDiscountPercent(decimal grossPayable, decimal discount)
    {
        if (discount <= 0 || grossPayable <= 0) return 0;
        var pct = discount * 100m / grossPayable;
        return Math.Round(pct, 1);
    }

    private void txtDiscountAmount_KeyUp(object sender, KeyEventArgs e)
    {
        e.Handled = true;
        switch (e.KeyCode) {
            case KEY_SEARCH_CANCEL:
                txtDiscountAmount.Value = 0m;
                break;
            case Keys.Enter:
                focusSelectControl(txtDiscountNote);
                return;
        }

        var amount = getEnteredDiscountAmount(false);
        updateEffectiveDiscountPct(amount);
    }

    private void txtDiscountPercent_KeyUp(object sender, KeyEventArgs e)
    {
        switch (e.KeyCode) {
            case Keys.Escape:
                txtDiscountPercent.Text = null;
                break;
            case KEY_SEARCH_ACCEPT:
                btnSetDiscountPercent.PerformClick();
                e.Handled = true;
                break;
        }
    }

    private bool hasNoAssociateLab() => _orderAssociateLabId <= 0;

    private void btnPayment_Click(object sender, EventArgs e)
    {
        logSi(nameof(btnPayment_Click));

        if (!validateOrderContext())
            return;

        if (orderHasAssociateLab()) {
            // subcontract jobs do not have referrers, copy the referrer to custom name 
            _currentLabOrder.ReferrerId = null;
            var referrer = txtPhysicianCustomName.Text.Trim();
            if (!string.IsNullOrEmpty(referrer))
                _currentLabOrder.ReferrerCustomName = referrer;
        }

        var invoiceInfo = InvoiceCalculator.AssembleTo(_invoiceCalc);
        var orderCreator = new NewLabOrderCreator(_currentLabOrder, invoiceInfo);
        using var txForm = new TransactionDialog();
        txForm.AllowedTransactionOperations = TransactionOperationTypes.Payment;
        /* | TransactionOperationTypes.Refund | TransactionOperationTypes.Discount*/
        txForm.CurrentLabOrder = _currentLabOrder;
        txForm.InvoiceInfo = invoiceInfo;
        txForm.IsNewInvoice = true;
        txForm.OnTransactionPerform += orderCreator.RecordTransaction;
        txForm.IsSubcontractOrder = orderHasAssociateLab();

        // TODO: disable the preview popup menu for new orders. enable for order edit.
        //frm.EnablePreviewPopupMenu = false;

        if (_invoiceCalc.DiscountAmount > 0) {
            var note = getDiscountNoteText(true);
            txForm.TransactionsHistory.Add(new InvoiceTransactionDetailedExSlice
            {
                StaffName = CurrentUserContext.UserDisplayName,
                TxAmount = _invoiceCalc.DiscountAmount,
                TxTime = DateTime.Now,
                TxType = (byte)InvoiceTransactionType.CashDiscount,
                TxFlag = (byte)TransactionFlag.InitialOperation,
                UserRemarks = note
            });

            orderCreator.RecordTransaction(
                InvoiceTransactionType.CashDiscount,
                TransactionFlag.InitialOperation,
                _invoiceCalc.DiscountAmount,
                note,
                0m,
                PaymentMethod.Cash
            );
        }

        txForm.UpdateControls();
        if (txForm.ShowDialog() != DialogResult.OK) return;

        var showPreview = txForm.ShowPreviewDialog;

        long invoiceId = -1;
        invoiceId = orderCreator.CreateOrder();
        /*
        FaultHandler.Shield(() =>
            WaitFormControl.WaitOperation(this,
                () => { invoiceId = orderCreator.CreateOrder(); },
                "Creating Lab Order..."));
        */
        if (showPreview) {
            // show the preview / print dialog and let the user manually print
            new PreviewPrintDialog(invoiceId, true, null).ExecuteDialog(this);
            FaultHandler.Shield(() =>
            {
                AuditTrailRepository.LogLabOrderEvent(invoiceId, AuditEventType.ordInvoicePreviewed);
                AuditTrailRepository.LogLabOrderEvent(invoiceId, AuditEventType.ordReqSlipPreviewed);
            });
        }
        else {
            // TODO: batch print the items: invoice and other req. foils
            Tuple<InvoicePrintDto, List<InvoiceRequisitionFoilsPackage>> packages = null;
            FaultHandler.Shield(() => WaitFormControl.WaitOperation(this,
                                                                    () => packages = ReqSlipBundleCompiler.BuildInvoiceReqBundlesChain(invoiceId, true),
                                                                    "Compiling Printable Documents..."));

            // the following snippet can be off-loaded to async
            // -------X-------X-------X-------X-------X-------X-------X-------X-------
            //var printTask = Task.Factory.StartNew(() => performPrinting(packages.Item1, packages.Item2));
            performPrinting(packages.Item1, packages.Item2);
            // -------X-------X-------X-------X-------X-------X-------X-------X-------

            FaultHandler.Shield(() =>
            {
                AuditTrailRepository.LogLabOrderEvent(invoiceId, AuditEventType.ordInvoicePrinted);
                AuditTrailRepository.LogLabOrderEvent(invoiceId, AuditEventType.ordReqSlipPrinted);
            });

            //printTask.Wait();
        }

        prepareForNewOrder();

        focusControl(txtTelephone);
    }

    private void performPrinting(InvoicePrintDto invoice, List<InvoiceRequisitionFoilsPackage> foils)
    {
        PrintHelper.PrintInvoice(false, invoice);
        foreach (var foil in foils) PrintHelper.PrintRequisitionSlip(false, invoice, foil.ReqBundle);
    }

    internal bool PerformTransaction(InvoiceTransactionType txType, decimal txAmount, string txRemarks) => false;

    private void btnPutOnHold_ItemClick(object sender, ItemClickEventArgs e)
    {
        initializeOrderInfoFromControls();
        if (!containsData()) {
            MessageDlg.Warning("Nothing to stash...");
            return;
        }

        FaultHandler.Shield(() =>
        {
            HeldLabOrdersRepository.PutOrderOnHold(CurrentUserContext.UserId,
                                                   CurrentUserContext.WorkShiftId,
                                                   _currentLabOrder);
            HeldLabOrdersRepository.Save();
            MessageDlg.Info("Order details stashed in memory.");
        });

        prepareForNewOrder();

        focusControl(txtTelephone);
    }

    private void btnOrdersOnHold_ItemClick(object sender, ItemClickEventArgs e)
    {
        using var frm = new OrdersOnHoldDialog();
        if (frm.ShowDialog() != DialogResult.OK) return;
        if (frm.PerformHeldOrderId == -1) return;

        prepareForNewOrder();
        var ctxOrder = FaultHandler.Shield(() =>
                                               LabOrderContextInfo.GetHeldLabOrderContextInfoFromRepository(frm.PerformHeldOrderId));
        FaultHandler.Shield(() => HeldLabOrdersRepository.RemoveHeldOrder(frm.PerformHeldOrderId));

        if (ctxOrder == null) return;

        _currentLabOrder = ctxOrder;
        _currentLabOrder.OrderingUserName = CurrentUserContext.UserDisplayName;

        _invoiceCalc.Reset();
        if (_orderAssociateLabId > 0)
            _currentLabOrder.ClearBillableItems();
        else
            _currentLabOrder.CompileBillableItemsList();

        _invoiceCalc.CalculateInvoice(_currentLabOrder);

        populateControlsFromOrderInfo(_currentLabOrder);

        updateOrderChanges();
    }

    private BillableItemSlice getSelectedBillableItem()
    {
        if (grdBillableItems.SelectedRows.Count != 1) return null;

        return grdBillableItems.SelectedRows[0].Tag as BillableItemSlice;
    }

    private void btnBIAdd_Click(object sender, EventArgs e)
    {
        using var frm = new OrderBillableItemDialog { AllowSelectionOfBillableItem = true };
        frm.UpdateControls();
        if (frm.ShowDialog() != DialogResult.OK) return;
        if (_currentLabOrder.HasBillableItem(frm.SelectedBillableItemId)) {
            MessageDlg.Warning("The selected billable item is already in the list.");
            return;
        }

        _currentLabOrder.AddBillableItem(frm.SelectedBillableItemId, frm.SelectedQuantity);
        updateAfterBillableItemsChange(true);
    }

    private void btnBIModify_Click(object sender, EventArgs e) => modifyBillableItems();

    private void modifyBillableItems()
    {
        var slice = getSelectedBillableItem();
        if (slice == null) return;

        using var frm = new OrderBillableItemDialog();
        frm.SelectedBillableItemId = slice.Id;
        frm.SelectedQuantity = slice.Quantity;
        frm.UpdateControls();
        if (frm.ShowDialog() != DialogResult.OK) return;

        _currentLabOrder.ModifyBillableItemQuantity(slice.Id, slice.Quantity, frm.SelectedQuantity);
        updateAfterBillableItemsChange(false);
    }

    private void btnBIDelete_Click(object sender, EventArgs e)
    {
        var slice = getSelectedBillableItem();
        if (slice == null || !MessageDlg.Confirm($"Really delete {slice.Quantity} unit(s) of {slice.Name}?")) return;
        _currentLabOrder.RemoveBillableItem(slice);
        updateAfterBillableItemsChange(false);
    }

    private void updateAfterBillableItemsChange(bool recompile)
    {
        if (recompile) {
            if (_orderAssociateLabId > 0)
                _currentLabOrder.ClearBillableItems();
            else
                _currentLabOrder.CompileBillableItemsList();
        }

        _invoiceCalc.Reset();
        _invoiceCalc.CalculateInvoice(_currentLabOrder);
        updateOrderChanges();
    }

    private void btnDiscountCalculator_Click(object sender, EventArgs e) => showDiscountCalculatorDialog();

    private void showDiscountCalculatorDialog()
    {
        using var frm = new DiscountCalculatorDialog(_currentLabOrder.OrderedTests, _invoiceCalc.GrossPayable);
        if (frm.ShowDialog() != DialogResult.OK) return;
        if (frm.EffectiveDiscount > _invoiceCalc.MaxApplicableDiscountAmount)
            if (!MessageDlg.Confirm("The amount you've entered is greater than the maximum applicable discount." +
                                    "\nDo you still want to apply this discount?"))
                return;

        txtDiscountAmount.Value = frm.EffectiveDiscount;
        txtDiscountAmount.Select();
        txtDiscountAmount.Focus();
    }

    private void grdOrderedTests_KeyUp(object sender, KeyEventArgs e)
    {
        if (e.KeyCode == Keys.F2) {
            setDeliveryTimeForSelectedTests(true);
            e.Handled = true;
        }
        else if (e is { KeyCode: Keys.A, Control: true }) {
            grdOrderedTests.BeginInit();
            try {
                grdOrderedTests.SelectedRows.Clear();
                foreach (DataRow row in grdOrderedTests.DataRows)
                    grdOrderedTests.SelectedRows.Add(row);
            }
            finally {
                grdOrderedTests.EndInit();
            }

            e.Handled = true;
        }
    }

    private void LabOrderDialog_KeyUp(object sender, KeyEventArgs e)
    {
        switch (e.KeyCode) {
            case KEY_DISCOUNT_CALC:
                showDiscountCalculatorDialog();
                e.Handled = true;
                break;
        }
    }

    private void PatientName_KeyUp(object sender, KeyEventArgs e)
    {
        switch (e.KeyCode)
        {
            case KEY_FIX_CASING:
                e.Handled = true;
                normalizeName();
                break;
            
            case KEY_CRM_REGISTER:
                e.Handled = true;
                registerCustomerFast();
                break;
            
            case KEY_SEARCH_QUICKSELECT:
                e.Handled = true;
                var phone = SharedUtilities.GetPhoneLocalPart(getTelephone());
                if (string.IsNullOrEmpty(phone)) return;
                if (findPreviousPatientsByPhone(phone)) 
                    focusControl(cboPhysician);
                break;
        }
    }

    private void titleCaseTextBox(TextEdit txtEdit, bool normalizeTitle)
    {
        if (normalizeTitle) {
            txtEdit.Text = normalizeFirstNameTitle(txtEdit.Text);
        }
        else {
            var fname = txtEdit.Text.Trim();
            fname = SharedUtilities.TitleCase(fname);
            txtEdit.Text = fname;
        }
    }

    private void miShiftSearch_ItemClick(object sender, ItemClickEventArgs e)
    {
        using var frm = new ShiftSearchDialog(CurrentUserContext.UserId);
        frm.ShowDialog();
    }

    private void miUserShiftManagement_ItemClick(object sender, ItemClickEventArgs e)
    {
        // TODO: implement shift closure
        var shift = FaultHandler.Shield(() => WorkShiftsRepository.FindShiftById(CurrentUserContext.WorkShiftId));
        using var frm = new ShiftDetailsDialog(shift, false);
        frm.UpdateControls();
        frm.ShowDialog();
    }

    private void btnExit_ItemClick(object sender, ItemClickEventArgs e) => Close();

    private void miInvoiceSearch_ItemClick(object sender, ItemClickEventArgs e) =>
        new InvoiceSearchForm().ExecuteDialog(this);

    private void miExit_ItemClick(object sender, ItemClickEventArgs e) => Close();

    private static void focusEditControl(BaseEdit edit)
    {
        if (edit != null) edit.BackColor = Color.Bisque;
    }

    private static void defocusEditControl(BaseEdit edit)
    {
        if (edit != null) {
            var currentSkin = CommonSkins.GetSkin(edit.LookAndFeel);
            var color = currentSkin.TranslateColor(SystemColors.Window);
            edit.BackColor = color;
        }
    }

    private void txtFirstName_Enter(object sender, EventArgs e) => focusEditControl(sender as TextEdit);

    private void txtFirstName_Leave(object sender, EventArgs e) => defocusEditControl(sender as TextEdit);

    private void cboPatientTitle_Enter(object sender, EventArgs e) => focusEditControl(sender as ComboBoxEdit);

    private void cboPatientTitle_Leave(object sender, EventArgs e) => defocusEditControl(sender as ComboBoxEdit);

    private void dteDateOfBirth_Leave(object sender, EventArgs e) => defocusEditControl(sender as DateEdit);

    private void dteDateOfBirth_Enter(object sender, EventArgs e) => focusEditControl(sender as DateEdit);

    private void txtDiscountAmount_Enter(object sender, EventArgs e) => focusEditControl(sender as CalcEdit);

    private void txtDiscountAmount_Leave(object sender, EventArgs e)
    {
        defocusEditControl(sender as CalcEdit);
        updateEffectiveDiscountPct(getEnteredDiscountAmount(false));
    }

    private void cboPhysician_Enter(object sender, EventArgs e)
    {
        if (sender is ComboBoxBase edit) edit.BackColor = Color.Bisque;
    }

    private void cboPhysician_Leave(object sender, EventArgs e)
    {
        if (sender is ComboBoxBase edit) edit.BackColor = Color.White;
    }

    private void btnBIClear_Click(object sender, EventArgs e)
    {
        if (MessageDlg.Confirm("Really remove all billable items?")) {
            _currentLabOrder.ClearBillableItems();
            updateAfterBillableItemsChange(false);
        }
    }

    private void btnCalculator_ItemClick(object sender, ItemClickEventArgs e) =>
        ExternalProcessHelper.ExecuteCalculator();

    private void chkOutsideOrder_CheckedChanged(object sender, EventArgs e) => toggleAffiliateLabControls();

    private void toggleAffiliateLabControls()
    {
        var toggle = chkOutsideOrder.Checked;
        cboAffiliateLabs.Enabled = toggle;
        txtExtTrackingId.Enabled = toggle;
        updateDiscountNoteTextbox(toggle);
    }

    private void comboBoxSetItems(ComboBoxEdit combo, object[] items)
    {
        combo.Clear();
        combo.SelectedIndex = -1;
        combo.Properties.Items.Clear();

        if (!items.Any()) return;

        try {
            combo.Properties.Items.BeginUpdate();
            combo.Properties.Items.AddRange(items);
        }
        finally {
            combo.Properties.Items.EndUpdate();
        }
    }

    private void initializeComboBoxes()
    {
        comboBoxSetItems(cboCorporateClients, _corporateClients.ToArray());
        comboBoxSetItems(cboAffiliateLabs, _associateLabs.ToArray());
        comboBoxSetItems(cboAffiliates, _affiliates.ToArray());
    }

    private void cboCorporateClients_SelectedValueChanged(object sender, EventArgs e)
    {
        switch (cboCorporateClients.SelectedItem) {
            case CorporateClientSlice corp:
                setCorporateClientId(corp.Id);
                break;
            default:
                setCorporateClientId(null);
                break;
        }

        updateSelectedItemLabels();
    }

    private void updateSelectedItemLabels()
    {
        resetSelectedItemLabels();

        if (cboCorporateClients.SelectedItem is CorporateClientSlice corp) lblSelectedCorp.Text = corp.Name;

        if (cboAffiliates.SelectedItem is AffiliateSlice affiliate) lblSelectedAffiliate.Text = affiliate.Name;

        if (cboAffiliateLabs.SelectedItem is AssociateOrganizationSlice lab) lblSelectedAffiliateLab.Text = lab.Name;

        //if (cboHealthPackages.SelectedItem is HealthPackage pack) lblSelectedHealthPackage.Text = pack.Name;
    }

    private void setCorporateClientId(short? id) => _currentLabOrder.CorporateClientId = id is > 0 ? id : null;

    private void setAffiliateId(short? id) => _currentLabOrder.AffiliateId = id is > 0 ? id : null;

    private void cboAffiliates_SelectedValueChanged(object sender, EventArgs e)
    {
        switch (cboAffiliates.SelectedItem) {
            case AffiliateSlice aff:
                setAffiliateId(aff.Id);
                break;
            default:
                setAffiliateId(null);
                break;
        }

        updateSelectedItemLabels();
    }

    private void cboAffiliateLabs_SelectedValueChanged(object sender, EventArgs e)
    {
        updateAffiliateLab();
        updateSelectedItemLabels();
    }

    private void updateAffiliateLab()
    {
        switch (cboAffiliateLabs.SelectedItem) {
            case AssociateOrganizationSlice lab:
                updateOrderAssociateLab(lab.Id, false, false);
                break;
            default:
                updateOrderAssociateLab(null, true, true);
                break;
        }
    }

    void searchCustomerLoginPhone(string phone, string login)
    {
        if (string.IsNullOrEmpty(phone) && string.IsNullOrEmpty(login)) {
            MessageDlg.Warning("Please provide a valid username or mobile number.");
            return;
        }

        ICustomerSearchProvider searcher;
        string searchString;
        if (!string.IsNullOrEmpty(login)) {
            searcher = new CustomerLoginSearchProvider();
            searchString = login;
        }
        else {
            searcher = new CustomerPhoneSearchProvider();
            searchString = phone;
        }

        var customers = searcher.Search(searchString);

        using var form = new CustomerSearchForm();
        form.SetSearchTerms(string.Empty, phone, login);
        form.SetCustomers(customers);
        var proceed = form.ShowDialog(this) == DialogResult.OK;
        // todo: provide mechanism to unselect customer
        if (!proceed || form.SelectedCustomer == null) return;
        populateCustomerFields(form.SelectedCustomer);
    }

    void searchCustomer()
    {
        var phn = txtCustomerPHN.Text.Trim().ToUpperInvariant();
        var phone = SharedUtilities.GetPhoneLocalPart(txtCustomerPhone.Text);
        if (string.IsNullOrEmpty(phn) && string.IsNullOrEmpty(phone)) {
            MessageDlg.Warning("Please provide a valid PHN or mobile number.");
            return;
        }

        ICustomerSearchProvider searcher;
        string searchString;
        if (!string.IsNullOrEmpty(phn)) {
            searcher = new CustomerUPINSearchProvider();
            searchString = phn;
        }
        else {
            searcher = new CustomerPhoneSearchProvider();
            searchString = phone;
        }

        var customers = searcher.Search(searchString);

        using var form = new CustomerSearchForm();
        form.SetSearchTerms(phn, phone, string.Empty);
        form.SetCustomers(customers);
        var proceed = form.ShowDialog(this) == DialogResult.OK;
        // todo: provide mechanism to unselect customer
        if (!proceed || form.SelectedCustomer == null) return;
        populateCustomerFields(form.SelectedCustomer);
    }

    private void btnCustomerSearch_Click(object sender, EventArgs e) => searchCustomer();

    private void populateCustomerFields(CustomerInfo customer)
    {
        setCustomerId(customer.Id);
        updateCustomerLabels(customer);
        cboPatientTitle.Text = customer.Title;
        txtFirstName.Text = customer.FirstName;
        txtLastName.Text = customer.LastName;
        if (customer.HasDoB())
            dteDateOfBirth.EditValue = customer.DoB;
        else if (!string.IsNullOrEmpty(customer.Age))
            txtAge.Text = customer.Age;
        txtTelephone.Text = customer.Phone;
        txtEmail.Text = customer.Email;
        if (customer.HasCorporateClient())
            attachCorporateClient((short)customer.CorporateClientId);

        updateSexRadioGroup(customer.Sex);

        applyAppSurcharge(true);
    }

    private void updateCustomerLabels(CustomerInfo? customer)
    {
        void applyStyle(Font prototype, FontStyle style, Color color)
        {
            var font = new Font(prototype, style);
            lblCustomerId.Font = font;
            lblCustomerPHN.Font = font;
            lblCustomerId.ForeColor = color;
            lblCustomerPHN.ForeColor = color;
        }

        if (customer != null) {
            lblCustomerId.Text = "Pt. #: " + customer.Id;
            lblCustomerPHN.Text = "PHN: " + customer.UPIN;
            applyStyle(lblCustomerId.Font, FontStyle.Bold, Color.Firebrick);
        }
        else {
            lblCustomerId.Text = "Pt. #:";
            lblCustomerPHN.Text = "PHN:";
            applyStyle(lblCustomerId.Font, FontStyle.Regular, Color.FromArgb(30, 57, 91));
        }
    }

    private void attachCorporateClient(short corpId)
    {
        setCorporateClientId(corpId);
        var index = 0;
        foreach (CorporateClientSlice corp in cboCorporateClients.Properties.Items) {
            if (corp.Id == corpId) {
                cboCorporateClients.SelectedIndex = index;
                return;
            }

            index++;
        }
    }

    private ResultsReleaseFlagType getResultReleaseFlag() =>
        rgResultReleaseMode.SelectedIndex switch
        {
            1 => ResultsReleaseFlagType.OverrideBalanceVerification,
            2 => ResultsReleaseFlagType.ImmediateRelease,
            _ => ResultsReleaseFlagType.Normal
        };

    private void btnNormalizeName_Click(object sender, EventArgs e) => normalizeName();

    void normalizeCustomerDetails(bool processNamesOnly)
    {
        var parts = new List<string>
        {
            cboPatientTitle.Text.Trim(),
            txtFirstName.Text.Trim(),
            txtLastName.Text.Trim()
        };

        var fullName = string.Join(" ", parts.Where(s => !string.IsNullOrEmpty(s)));
        var norm = new PatientDetailsParser(fullName);
        if (!norm.Parse()) return;

        cboPatientTitle.Text = norm.Title;
        txtFirstName.Text = norm.FirstName;
        txtLastName.Text = norm.LastName;

        if (processNamesOnly) return;

        if (!string.IsNullOrEmpty(norm.Age) && string.IsNullOrEmpty(getAge()))
            txtAge.Text = norm.Age;

        var sex = norm.DeduceSexFromTitle();
        if (sex != SexType.Unknown) {
            updateSexRadioGroup(sex);
        }
    }

    string normalizeFirstNameTitle(string text)
    {
        text = text.Trim().ToLowerInvariant();
        if (text.StartsWith("mst")) {
            text = text.Substring(3);
            text = text.Trim('.', ' ');
            text = $"master {text}";
        }

        text = SharedUtilities.TitleCase(text);
        return text;
    }

    private void normalizeName()
    {
        var parts = new List<string>
        {
            cboPatientTitle.Text.Trim(),
            normalizeFirstNameTitle(txtFirstName.Text),
            txtLastName.Text.Trim()
        };

        var fullName = string.Join(" ", parts.Where(s => !string.IsNullOrEmpty(s)));
        var norm = new PatientDetailsParser(fullName);
        if (!norm.Parse()) return;

        if (string.IsNullOrEmpty(getExternaleOrderId()))
            txtExtTrackingId.Text = norm.TrackingId;

        var lastName = norm.LastName;
        if (orderHasAssociateLab() && !string.IsNullOrEmpty(getExternaleOrderId()) )
            lastName += $" [ID: {getExternaleOrderId()}]";

        cboPatientTitle.Text = norm.Title;
        txtFirstName.Text = norm.FirstName;
        txtLastName.Text = lastName.Trim();
        
        if (!string.IsNullOrEmpty(norm.Age) && string.IsNullOrEmpty(getAge()))
            txtAge.Text = norm.Age;

        var sex = norm.DeduceSexFromTitle();
        if (sex != SexType.Unknown) {
            updateSexRadioGroup(sex);
            if (string.IsNullOrEmpty(getAge()))
                txtAge.Focus();
            else
                cboPhysician.Focus();
        }
        else {
            rgSex.Focus();
        }
    }

    private void resetSelectedItemLabels()
    {
        lblSelectedCorp.Text = null;
        lblSelectedAffiliate.Text = null;
        lblSelectedAffiliateLab.Text = null;
        lblSelectedHealthPackage.Text = null;
    }

    private void btnRegisterCustomer_ItemClick(object sender, ItemClickEventArgs e)
    {
        using var form = new CustomerCreateForm();
        var proceed = form.ShowDialog(this) == DialogResult.OK;
        if (!proceed || form.SelectedCustomer == null) return;

        if (form is { PrintInvoice: true, InvoiceId: > 0 }) {
            printCustomerInvoice(form.InvoiceId, form.SelectedCustomer.Login, form.PlainTextPassword);
        }

        populateCustomerFields(form.SelectedCustomer);
    }

    private void printCustomerInvoice(long invoiceId, string username, string password)
    {
        var invoice = FaultHandler.Shield(() => SubscriptionInvoicesRepository.GetCustomerInvoiceDetails(invoiceId));
        var dto = CustomerSubscriptionPaymentInfo.AssembleFrom(invoice, password);
        dto.SiteName =
            GlobalSettingsRepository.GetStringValue("crm.site_name", "Chevron Clinical Lab Pte Ltd, Panchlaish");
        var portal = GlobalSettingsRepository.GetStringValue("crm.portal_url", "MYLABCTG.COM");
        dto.PortalUrl = portal;
        dto.FooterText =
            $"মোবাইল এ্যাপ ও অনলাইন রিপোর্টের জন্য ভিজিট করুনঃ {portal}\nআপনার USERNAME ও PASSWORD দিয়ে login করুন\nDownload the app or access your reports online at: {portal}\nUse the above username and password to log in\n";
        var preview = ModifierKeys == Keys.Shift;
        PrintHelper.PrintCrmInvoice(dto, preview);
    }

    private void miAssignAssocLab_ItemClick(object sender, ItemClickEventArgs e)
    {
        using var form = new AssociatedLabDialog();
        form.SetAssociateLabs(_associateLabs);
        var res = form.ShowDialog(this);
        switch (res) {
            case DialogResult.OK:
                // assign
                setGlobalAssociateLab(form.SelectedId);
                break;
            case DialogResult.Ignore:
                // clear assignment
                setGlobalAssociateLab(null);
                return;
            default:
                return;
        }
    }

    private void toggleCheckBox(CheckEdit control, bool value)
    {
        if (control.Checked != value) control.Checked = value;
    }

    private void updateAssociateLabControls()
    {
        logSi(nameof(updateAssociateLabControls));

        if (hasNoAssociateLab()) {
            txtDiscountPercent.Text = null;
            updateDiscountNoteTextbox(true);
            rgResultReleaseMode.SelectedIndex = 0;
            logSi();

            return;
        }

        var lab = findAssociateLab(_orderAssociateLabId);
        if (lab == null) return;

        rgResultReleaseMode.SelectedIndex = lab.OverrideDuesCheck switch
        {
            true  => 1,
            false => 0
        };

        if (lab.DiscountLevel > 0)
            txtDiscountPercent.Text = lab.DiscountLevel.ToString("N0");
        setDiscountNoteText("Subcontr.");
        txtDiscountAmount.Text = null;

        if (!string.IsNullOrEmpty(lab.Phone) && string.IsNullOrEmpty(getTelephone())) txtTelephone.Text = lab.Phone;
        if (!string.IsNullOrEmpty(lab.Email) && string.IsNullOrEmpty(getEmailAddress())) txtEmail.Text = lab.Email;

        logSi();
    }

    private AssociateOrganizationSlice? findAssociateLab(short labId) =>
        _associateLabs.FirstOrDefault(lab => lab.Id == _orderAssociateLabId);

    private string getExternaleOrderId() => txtExtTrackingId.Text.Trim();

    private void updateOrderAssociateLab(short? labId, bool updateControls, bool suppressCheckboxUpdate)
    {
        _orderAssociateLabId = labId ?? -1;

        if (orderHasAssociateLab()) {
            toggleCheckBox(chkOutsideOrder, true);
            _currentLabOrder.AssociateLabId = _orderAssociateLabId;
            _currentLabOrder.AssociateLabAccessionId = getExternaleOrderId();
            _currentLabOrder.IsExternalSubOrder = true;
            _currentLabOrder.DisallowReferral = true;
        }
        else {
            if (!suppressCheckboxUpdate)
                toggleCheckBox(chkOutsideOrder, false);
            _currentLabOrder.AssociateLabId = null;
            _currentLabOrder.AssociateLabAccessionId = null;
            _currentLabOrder.IsExternalSubOrder = false;
            _currentLabOrder.DisallowReferral = chkDirectPatient.Checked;
            cboAffiliateLabs.SelectedIndex = -1;
        }

        if (updateControls)
            updateAssociateLabControls();
    }

    private void setGlobalAssociateLab(short? labId)
    {
        _globalAssociateLabId = labId ?? -1;
        updateOrderAssociateLab(labId, true, false);

        if (hasGlobalAssociateLab()) {
            lblAssociatedLab.BackColor = Color.Gold;
            foreach (var item in cboAffiliateLabs.Properties.Items)
                if (item is AssociateOrganizationSlice obj && obj.Id == labId!) {
                    cboAffiliateLabs.SelectedItem = item;
                    break;
                }
        }
        else
            lblAssociatedLab.BackColor = Color.Snow;
    }

    internal sealed class LabTestInfo
    {
        internal string TestName { get; set; }
        internal short TestId { get; set; }
    }

    internal enum SearchMode
    {
        Any,
        BeginsWith,
        EndsWith
    }

    string getFirstName() => txtFirstName.Text.Trim();
    string getLastName() => txtLastName.Text.Trim();

    private void btnRegisterCustomerFast_Click(object sender, EventArgs e) => registerCustomerFast();

    bool checkIfOrderAlreadyHasACustomer()
    {
        if (orderHasCustomerId()) {
            MessageDlg.Warning("A registered patient has already been associated with this order.");
            return false;
        }

        return true;
    }

    private void registerCustomerFast()
    {
        /*
         * verify input: name, age/dob, sex, phone + email
         * generate username
         * check phone / username -> show customer search form
         * register customer
         * print / confirm dialog
         * set customer id
         */
        if (!checkIfOrderAlreadyHasACustomer())
            return;

        var customerData = makeCustomer();
        if (customerData == null) return;

        var form = new NewCustomerConfirmationForm(customerData, CustomerDefaultPassword);
        if (form.ShowDialog(this) != DialogResult.OK) {
            focusControl(cboPhysician);
            return;
        }

        var printInvoice = form.PrintInvoice;
        customerData = form.Customer;
        var password = form.Password;

        long invoiceId = -1;
        CustomerInfo? newCustomer = null;

        try {
            var service = new CustomerRegistrationService
            {
                Customer = customerData!,
                Password = password,
                Plan = CustomerDefaultSubscriptionPlan,
                AmountPaid = 0,
                StaffId = CurrentUserContext.UserId,
            };

            if (!service.PreFlightCheck()) {
                MessageDlg.Error("A customer with same mobile phone or username already exists");
                searchCustomerLoginPhone(customerData.Phone, customerData.Login);
                return;
            }

            service.Execute();
            newCustomer = service.RegisteredCustomer;
            invoiceId = (long)service.InvoiceId!;
        }
        catch (Exception exc) {
            XtraMessageBox.Show(exc.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        if (invoiceId > 0) {
            if (printInvoice)
                printCustomerInvoice(invoiceId, newCustomer!.Login, CustomerDefaultPassword);

            populateCustomerFields(newCustomer!);
        }

        focusControl(cboPhysician);
    }

    private SexType getSexType() =>
        rgSex.SelectedIndex switch
        {
            0 => SexType.Male,
            1 => SexType.Female,
            _ => SexType.Unknown
        };

    private string getEmail() => txtEmail.Text.Trim().ToLowerInvariant();

    string getValidPhone()
    {
        var phone = SharedUtilities.DigitsOnly(txtTelephone.Text.Trim());
        if (!SharedUtilities.IsValidMobileNumber(phone))
            throw new ApplicationException("Invalid mobile number");
        return phone;
    }

    private string? getAgeForCustomer()
    {
        var s = getAge();
        return string.IsNullOrEmpty(s) ? null : s;
    }

    private DateTime? getDoB()
    {
        var dt = new SmartDate(dteDateOfBirth.DateTime, true);
        return dt.IsEmpty ? null : dt.Date;
    }

    private string? getPatientTitle()
    {
        var s = cboPatientTitle.Text.Trim();
        return string.IsNullOrEmpty(s) ? null : s;
    }

    private CustomerInfo? makeCustomer()
    {
        normalizeCustomerDetails(false);
        fixCasingForPatientNameAndAge();

        try {
            var customer = new CustomerInfo
            {
                Title = getPatientTitle(),
                FirstName = getFirstName(),
                LastName = getLastName(),
                Sex = getSexType(),
                Phone = getValidPhone(),
                Email = getEmail(),
                DoB = getDoB(),
                Age = getAgeForCustomer(),
                EnrollingUserId = CurrentUserContext.UserId,
                EnrolledOn = DateTime.Now
            };
            customer.AutoGenerateLoginName();
            customer.Login = CustomerRegistrationService.GenerateUniqueLoginName(customer.Login);
            return validateCustomer(customer);
        }
        catch (Exception e) {
            XtraMessageBox.Show(this, e.Message, "Error");
        }

        return null;
    }

    private CustomerInfo validateCustomer(CustomerInfo customer)
    {
        if (string.IsNullOrEmpty(customer.FirstName)) throw new ArgumentException("First Name cannot be blank");
        if (string.IsNullOrEmpty(customer.Phone)) throw new ArgumentException("Phone number cannot be blank");
        if (string.IsNullOrEmpty(customer.Login)) throw new ArgumentException("User name cannot be blank");
        if (!string.IsNullOrEmpty(customer.Email) && !SharedUtilities.IsValidEmail(customer.Email))
            throw new ArgumentException("Invalid email address");
        customer.Phone = SharedUtilities.SanitizeMobileNumber(customer.Phone);
        return customer;
    }

    bool orderHasCustomerId() => _currentLabOrder.CustomerId is > 0;
    bool surchargeApplies() => orderHasCustomerId() && !orderHasAssociateLab();

    private void applyAppSurcharge(bool updateControls)
    {
        if (!surchargeApplies()) {
            _invoiceCalc.CalculateSurcharge(false);
            _currentLabOrder.ClearStaticBillableItems();
            if (updateControls)
                updateOrderChanges();
            return;
        }

        _invoiceCalc.CalculateSurcharge(true);
        if (_invoiceCalc.SurchargeAmount > 0m) {
            AppSurchargeBillableItem.Quantity = 1;
            AppSurchargeBillableItem.UnitPrice = _invoiceCalc.SurchargeAmount;
            AppSurchargeBillableItem.LineTotal = _invoiceCalc.SurchargeAmount;
            AppSurchargeBillableItem.OptimizationLevel = BillableItemOptimizationLevelType.DoNotOptimize;
            _currentLabOrder.UpsertStaticBillableItem(AppSurchargeBillableItem);
            // _currentLabOrder.CompileBillableItemsList();
            _invoiceCalc.CalculateInvoice(_currentLabOrder);

            if (updateControls)
                updateOrderChanges();
        }
    }

    bool findCustomerByPhone()
    {
        var phone = SharedUtilities.GetPhoneLocalPart(getTelephone());
        var customer = CustomerRepository.FindByPhoneFirst(phone);
        var success = customer != null;
        if (success)
            populateCustomerFields(CustomerInfo.AssembleFrom(customer));
        return success;
    }

    private void txtTelephone_KeyUp(object sender, KeyEventArgs e)
    {
        if (e.KeyCode != KEY_SEARCH_ACCEPT && e.KeyCode != KEY_CRM_REGISTER && e.KeyCode != KEY_SEARCH_QUICKSELECT)
            return;

        e.Handled = true;

        if (e.KeyCode == KEY_CRM_REGISTER)
        {
            registerCustomerFast();
            return;
        }

        var phone = SharedUtilities.GetPhoneLocalPart(getTelephone());

        if (string.IsNullOrEmpty(phone))
        {
            MessageDlg.Error("Please enter the mobile phone number.");
            return;
        }

        if (!SharedUtilities.IsValidMobileNumber(phone))
        {
            MessageDlg.Error("Please enter a valid, 11 digit Bangladeshi mobile phone number.");
            focusSelectControl(txtTelephone);
            txtTelephone.SelectAll();
            return;
        }
        txtTelephone.Text = phone;

        if (e.KeyCode == KEY_SEARCH_QUICKSELECT)
        {
            if (findPreviousPatientsByPhone(phone)) 
                focusControl(cboPhysician);
            
            return;
        }

        if (!findCustomerByPhone())
        {
            if (!findPreviousPatientsByPhone(phone))
                focusControl(txtFirstName);
            else
            {
                focusControl(cboPhysician);
            }
        }
        else
            focusControl(cboPhysician);
    }

    private bool findPreviousPatientsByPhone(string phone)
    {
        var patient = PatientSelectForm.SelectPatient(phone);
        if (patient == null)
            return false;

        cboPatientTitle.Text = patient.Title;
        txtFirstName.Text = patient.FirstName;
        txtLastName.Text = patient.LastName;
        normalizeCustomerDetails(true); // force normalization

        txtAge.Text = patient.Age;
        updateSexRadioGroup(patient.Sex);
        return true;
    }

    private void btnFindCustomer_Click(object sender, EventArgs e)
    {
        if (!findCustomerByPhone())
            focusControl(txtFirstName);
        else
            focusControl(cboPhysician);
    }

    private void txtCustomerPHN_KeyUp(object sender, KeyEventArgs e)
    {
        if (e.KeyCode != KEY_SEARCH_ACCEPT) return;

        e.Handled = true;
        searchCustomer();
    }

    private void txtDiscountNote_KeyUp(object sender, KeyEventArgs e)
    {
        if (e.KeyCode != KEY_SEARCH_ACCEPT) return;

        e.Handled = true;
        applyDiscount();
        focusControl(btnPayment);
    }

    private void cboAffiliateLabs_KeyUp(object sender, KeyEventArgs e)
    {
        if (e.KeyCode != KEY_SEARCH_QUICKSELECT) return;
        e.Handled = true;

        var selectedId = ReferrerQuickSelectionDialog.ExecuteDialog(this, false, false, SearchEntityType.AssociateLab);

        if (selectedId > 0) {
            updateOrderAssociateLab((short)selectedId, false, false);
            lblAssociatedLab.BackColor = Color.Gold;
            foreach (var item in cboAffiliateLabs.Properties.Items)
                if (item is AssociateOrganizationSlice obj && obj.Id == selectedId) {
                    cboAffiliateLabs.SelectedItem = item;
                    break;
                }
        }
        else {
            lblAssociatedLab.BackColor = Color.Snow;
            updateOrderAssociateLab(null, false, false);
        }

        revertLuceneCatalogToReferrer();
    }

    private void cboCorporateClients_KeyUp(object sender, KeyEventArgs e)
    {
        if (e.KeyCode != KEY_SEARCH_QUICKSELECT) return;
        e.Handled = true;

        var selectedId = ReferrerQuickSelectionDialog.ExecuteDialog(this, false, false, SearchEntityType.Corporate);
        if (selectedId > 0) {
            setCorporateClientId((short)selectedId);
            foreach (var item in cboCorporateClients.Properties.Items)
                if (item is CorporateClientSlice obj && obj.Id == selectedId) {
                    cboCorporateClients.SelectedItem = item;
                    break;
                }
        }
        else {
            setCorporateClientId(null);
        }

        revertLuceneCatalogToReferrer();
    }

    private void cboAffiliates_KeyUp(object sender, KeyEventArgs e)
    {
        if (e.KeyCode != KEY_SEARCH_QUICKSELECT) return;
        e.Handled = true;

        var selectedId = ReferrerQuickSelectionDialog.ExecuteDialog(this, false, false, SearchEntityType.Affiliate);

        if (selectedId > 0) {
            setAffiliateId((short)selectedId);
            foreach (var item in cboAffiliates.Properties.Items)
                if (item is AffiliateSlice obj && obj.Id == selectedId) {
                    cboAffiliates.SelectedItem = item;
                    break;
                }
        }
        else {
            setAffiliateId(null);
        }

        revertLuceneCatalogToReferrer();
    }

    void revertLuceneCatalogToReferrer()
    {
        var physicians = ActiveReferringPhysiciansRepository.GetCatalog(false);
        ReferrerSearchEngine.AssignCatalog(physicians);
    }

    private void btnPayment_KeyDown(object sender, KeyEventArgs e)
    {
        if (e.KeyCode == Keys.Enter) {
            e.SuppressKeyPress = true;
            e.Handled = true;
            btnPayment.PerformClick();
        }
    }

    private void btnDissociateCustomer_Click(object sender, EventArgs e)
    {
        setCustomerId(null);
        updateCustomerLabels(null);
    }

    private void btnReimburseSurcharge_Click(object sender, EventArgs e)
    {
        if (!surchargeApplies()) return;

        //var discountAmount = _invoiceCalc.DiscountAmount;
        var discountAmount = getEnteredDiscountAmount(false);

        // add surcharge to discount
        var newNet = _invoiceCalc.GrossPayable - discountAmount;
        _invoiceCalc.CalculateSurchargeCustom(newNet, discountAmount);
        discountAmount += _invoiceCalc.SurchargeAmount;

        if (discountAmount <= 0m) return;

        txtDiscountAmount.Value = discountAmount;
        _invoiceCalc.SetDiscountAmount(discountAmount);
        applyAppSurcharge(false);
        updateOrderedBillableItemsGrid();
        updateFinanceLabels();
        focusControl(btnPayment);
    }

    private void grdBillableItems_KeyUp(object sender, KeyEventArgs e)
    {
        if (e.KeyCode == Keys.F2) {
            modifyBillableItems();
            e.Handled = true;
        }
    }
}