﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: PreviewPrintDialog.cs 984 2013-10-11 06:37:45Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using LabMaestro.BusinessLogic;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Printing;

namespace LabMaestro.Controls.Win;

public partial class PreviewPrintDialog : XtraForm, IExecutableDialog
{
    private List<InvoiceRequisitionFoilsPackage> _packages;

    public PreviewPrintDialog(long invoiceId, bool showReqSlips, string reprintReason)
    {
        InvoiceId = invoiceId;
        ShowReqSlips = showReqSlips;
        ReprintReason = reprintReason;

        InitializeComponent();
        WinUtils.ApplyTheme(this);
    }

    public long InvoiceId { get; }

    public bool ShowReqSlips { get; }

    public string ReprintReason { get; }

    public bool ExecuteDialog(IWin32Window parent)
    {
        UpdateControls();
        ShowDialog(parent);
        return true;
    }

    public void UpdateControls()
    {
        Tuple<InvoicePrintDto, List<InvoiceRequisitionFoilsPackage>> chain = null;
        WaitFormControl.WaitOperation(this,
                                      () => chain = ReqSlipBundleCompiler.BuildInvoiceReqBundlesChain(InvoiceId, false));

        _packages = chain.Item2;
        var invoice = chain.Item1;
        if (invoice != null) {
            lblInvoiceId.Text = invoice.InvoiceId;
            lblPatientId.Text = invoice.OrderId;
            lblPatientName.Text = invoice.PatientName;
            lblOrderedDate.Text = invoice.BookingDateTime;

            if (!string.IsNullOrEmpty(ReprintReason)) invoice.ReprintReason = ReprintReason;
        }
        else {
            lblInvoiceId.Text = string.Empty;
            lblOrderedDate.Text = string.Empty;
            lblPatientId.Text = string.Empty;
            lblPatientName.Text = string.Empty;
        }

        lbReports.BeginUpdate();
        try {
            lbReports.Items.Clear();
            if (ShowReqSlips)
                foreach (var bundle in _packages)
                    lbReports.Items.Add(bundle);
            else
                lbReports.Items.Add(_packages.First());
        }
        finally {
            lbReports.EndUpdate();
        }
    }

    private void PreviewPrintDialog_Load(object sender, EventArgs e) { }

    private void btnPreview_Click(object sender, EventArgs e) => performPrintPreview(true);

    private void btnPrint_Click(object sender, EventArgs e) => performPrintPreview(false);

    private void performPrintPreview(bool showPreview)
    {
        if (lbReports.SelectedIndex == -1) return;

        if (lbReports.SelectedValue is not InvoiceRequisitionFoilsPackage item) return;

        if (item.IsInvoiceHeader)
            PrintHelper.PrintInvoice(showPreview, item.InvoiceDetails);
        else {
            var invoice = _packages.SingleOrDefault(x => x.IsInvoiceHeader);
            if (invoice != null) PrintHelper.PrintRequisitionSlip(showPreview, invoice.InvoiceDetails, item.ReqBundle);
        }
    }

    private void lbReports_DrawItem(object sender, ListBoxDrawItemEventArgs e)
    {
        if (e.State.HasFlag(DrawItemState.Focus) || e.State.HasFlag(DrawItemState.Selected)) {
            e.Appearance.BackColor = Color.Purple;
            e.Appearance.ForeColor = Color.Yellow;
            e.Appearance.Font = new Font(e.Appearance.Font, FontStyle.Bold);
        }
        else
            e.Appearance.Font = new Font(e.Appearance.Font, FontStyle.Regular);
    }
}