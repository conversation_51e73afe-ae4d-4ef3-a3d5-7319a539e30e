﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: OrdersOnHoldDialog.cs 847 2013-07-22 07:17:00Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Shared;

namespace LabMaestro.Controls.Win
{
    public partial class OrdersOnHoldDialog : XtraForm, IExecutableDialog
    {
        public OrdersOnHoldDialog()
        {
            AuthHelper.GuardAccess();
            InitializeComponent();
            WinUtils.ApplyTheme(this);

            grdHeldOrders.Columns.Clear();
            grdHeldOrders.GridAddColumn(@"Time", "Time", 0, 100);
            grdHeldOrders.GridAddColumn(@"Name", "Name", 1, 250);
            grdHeldOrders.GridAddColumn(@"Age", "Age", 2, 50);
            grdHeldOrders.GridAddColumn(@"PhysicianName", "Referred by", 3, 240);

            PerformHeldOrderId = -1;
        }

        public long PerformHeldOrderId { get; private set; }

        public bool ExecuteDialog(IWin32Window parent)
        {
            UpdateControls();
            return ShowDialog(parent) == DialogResult.OK;
        }

        public void UpdateControls()
        {
        }

        private void btnDeleteOrder_Click(object sender, EventArgs e)
        {
            if (grdHeldOrders.DataRows.Count == 0)
            {
                MessageDlg.Info("Nothing to remove! You must select an order to remove.");
                return;
            }

            long orderId = -1;
            if ((grdHeldOrders.SelectedRows.Count == 1) && (grdHeldOrders.SelectedRows[0] != null))
            {
                orderId = (long) grdHeldOrders.SelectedRows[0].Tag;
            }

            if (orderId > 0 && MessageDlg.Confirm("Really delete?"))
            {
                FaultHandler.Shield(() =>
                {
                    HeldLabOrdersRepository.RemoveHeldOrder(orderId);
                    HeldLabOrdersRepository.Save();
                });
                updateGridControl();
            }
        }

        private void updateGridControl()
        {
            // TODO: convert to long or int?
            var orders = FaultHandler.Shield(() =>
                HeldLabOrdersRepository.GetHeldOrdersInShift(CurrentUserContext.WorkShiftId));

            grdHeldOrders.DataRows.Clear();
            grdHeldOrders.BeginInit();
            foreach (var slice in orders)
            {
                var row = grdHeldOrders.DataRows.AddNew();

                //TODO: fetch time
                row.Cells[@"Time"].Value = SharedUtilities.DateTimeToShortString(slice.DateCreated);
                row.Cells[@"Name"].Value = slice.PatientName;
                if (slice.PatientAge != null) row.Cells[@"Age"].Value = slice.PatientAge;
                if (!string.IsNullOrEmpty(slice.ReferrerName))
                {
                    row.Cells[@"PhysicianName"].Value = slice.ReferrerName;
                }
                row.Tag = slice.Id;
                row.Height = 23;
                row.EndEdit();
            }
            grdHeldOrders.EndInit();
        }

        private void OrdersOnHoldDialog_Load(object sender, EventArgs e)
        {
            updateGridControl();
        }

        private void btnClearAllOrders_Click(object sender, EventArgs e)
        {
            if (grdHeldOrders.DataRows.Count == 0)
            {
                MessageDlg.Info("Nothing to remove.");
                return;
            }

            if (!CurrentUserContext.CheckShiftIsOpen())
            {
                MessageDlg.Error("You do not have any shifts open!");
                return;
            }

            if (MessageDlg.Confirm("Really delete?") && CaptchaDialog.ConfirmCaptcha())
            {
                FaultHandler.Shield(() => HeldLabOrdersRepository.ClearHeldOrdersInShift(CurrentUserContext.WorkShiftId));
            }
            updateGridControl();
        }

        private void btnPerformOrder_Click(object sender, EventArgs e)
        {
            if (grdHeldOrders.DataRows.Count == 0)
            {
                MessageDlg.Info("Nothing to perform.");
                return;
            }

            if ((grdHeldOrders.SelectedRows.Count == 1) && (grdHeldOrders.SelectedRows[0] != null))
            {
                var orderId = (long) grdHeldOrders.SelectedRows[0].Tag;
                if (orderId > 0)
                {
                    PerformHeldOrderId = orderId;
                    DialogResult = DialogResult.OK;
                    return;
                }
            }

            MessageDlg.Warning("Please select the order to perform.");
        }
    }
}