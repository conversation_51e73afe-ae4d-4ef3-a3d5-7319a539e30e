﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2015, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id$
// 
// Last modified: 2015-08-10 9:33 AM
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

#define DISALLOW_NEGATIVE_SHIFT_BALANCE

using System;
using System.Collections.Generic;
using System.Drawing;
using System.Globalization;
using System.Linq;
using System.Windows.Forms;
using CuttingEdge.Conditions;
using DevExpress.XtraEditors;
using LabMaestro.BusinessLogic;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Shared;
using HorizontalAlignment = Xceed.Grid.HorizontalAlignment;

namespace LabMaestro.Controls.Win
{
    public enum RefundDiscountOperationMode
    {
        Discount,
        DiscountRebate,
        Refund
    }

    public partial class RefundDiscountDialog : XtraForm, IExecutableDialog
    {
        private readonly RefundDiscountOperationMode _operationMode;
        private PatientLabOrder _currentLabOrder;

        public RefundDiscountDialog(PatientLabOrder order, RefundDiscountOperationMode operationMode)
        {
            Condition.Requires(order).IsNotNull();
            Condition.Requires(order.InvoiceId).IsGreaterThan(0);

            _currentLabOrder = order;
            _operationMode = operationMode;

            InitializeComponent();

            switch (operationMode)
            {
                case RefundDiscountOperationMode.Discount:
                    WinUtils.SetFormTitle(this, "Issue Discount");
                    lblDialogType.Text = " Discount ";
                    lblDialogType.ForeColor = Color.DodgerBlue;
                    break;
                case RefundDiscountOperationMode.DiscountRebate:
                    WinUtils.SetFormTitle(this, "Issue Discount Rebate");
                    lblDialogType.Text = " Discount Rebate ";
                    lblDialogType.ForeColor = Color.Indigo;
                    break;
                case RefundDiscountOperationMode.Refund:
                    WinUtils.SetFormTitle(this, "Issue Refund");
                    lblDialogType.Text = " Refund ";
                    lblDialogType.ForeColor = Color.Crimson;
                    break;
            }

            WinUtils.ApplyTheme(this);

            if (opIsRefund())
                createGridColumns();
        }

        public bool ExecuteDialog(IWin32Window parent)
        {
            UpdateControls();
            ShowDialog(parent);
            return true;
        }

        public void UpdateControls()
        {
            cboNotes.Properties.Items.Clear();
            var reasons = SharedUtilities.DelimitedStringToList(GlobalSettingsHelper.System.DiscountRefundReasons);
            foreach (var reason in reasons)
            {
                cboNotes.Properties.Items.Add(reason);
            }
        }

        private void createGridColumns()
        {
            var i = 0;
            grdOpenShifts.Columns.Clear();
            grdOpenShifts.GridAddColumn("staff", "Staff", i++, 165);
            grdOpenShifts.GridAddColumn("group", "Group", i++, 160);
            var column = grdOpenShifts.GridAddColumn("balance", "Balance", i++, 105);
            column.HorizontalAlignment = HorizontalAlignment.Right;
            grdOpenShifts.GridAddColumn("open", "Shift Opened On", i++, 170);
            column = grdOpenShifts.GridAddColumn("id", "", i++, 70);
            column.HorizontalAlignment = HorizontalAlignment.Right;
            column.Font = new Font("Tahoma", 9);
        }

        private bool opIsRefund()
        {
            return _operationMode == RefundDiscountOperationMode.Refund;
        }

        private bool opIsDiscount()
        {
            return _operationMode == RefundDiscountOperationMode.Discount;
        }

        private bool opIsDiscountRebate()
        {
            return _operationMode == RefundDiscountOperationMode.DiscountRebate;
        }

        private void populateControls(bool resetInputs = true)
        {
            lblInvoiceNum.Text = _currentLabOrder.InvoiceId.ToString(CultureInfo.InvariantCulture);
            lblOrderId.Text = _currentLabOrder.OrderId;
            lblPatientName.Text = _currentLabOrder.FullName;
            lblReferrerName.Text = _currentLabOrder.GetReferringPhysicianName();

            lblNetPayable.Text = SharedUtilities.MoneyToStringPlain(_currentLabOrder.InvoiceMaster.NetPayable);
            lblRefund.Text = SharedUtilities.MoneyToStringPlain(_currentLabOrder.InvoiceMaster.RefundAmount);
            lblDiscount.Text = SharedUtilities.MoneyToStringPlain(_currentLabOrder.InvoiceMaster.DiscountAmount);
            lblAmountPaid.Text = SharedUtilities.MoneyToStringPlain(_currentLabOrder.InvoiceMaster.PaidAmount);
            lblCurrentDue.Text = SharedUtilities.MoneyToStringPlain(_currentLabOrder.InvoiceMaster.DueAmount);

            if (resetInputs)
            {
                txtAmount.Text = string.Empty;
                cboNotes.Text = string.Empty;

                if (opIsRefund())
                {
                    var now = AppSysRepository.GetServerTime();
                    var shifts = fetchOpenShifts(now, -3, 1);
                    reconcileShifts(shifts);
                    populateShiftsGrid(shifts);
                }
            }
        }

        private List<WorkShift> filterAndSortShifts(List<WorkShift> shifts)
        {
            return shifts.Where(x => x.FinalBalance > 0)
                         .OrderByDescending(x => x.FinalBalance)
                         .ToList();
        }

        private void populateShiftsGrid(List<WorkShift> shifts)
        {
            grdOpenShifts.BeginInit();
            try
            {
                grdOpenShifts.DataRows.Clear();
                foreach (var workShift in filterAndSortShifts(shifts))
                {
                    var row = grdOpenShifts.DataRows.AddNew();
                    row.Cells["id"].Value = workShift.Id.ToString();
                    row.Cells["staff"].Value = workShift.User.DisplayName;
                    row.Cells["group"].Value = workShift.User.Role.Name;
                    row.Cells["balance"].Value = SharedUtilities.MoneyToStringPlainCultureNonZero(workShift.FinalBalance);
                    row.Cells["open"].Value = workShift.StartTime.ToString("dd/MM (ddd) h:mm tt");
                    row.Tag = workShift;
                    row.Height = 24;
                    row.EndEdit();
                }
            }
            finally
            {
                grdOpenShifts.EndInit();
            }
        }

        private static List<WorkShift> fetchOpenShifts(DateTime from, DateTime to)
        {
            return FaultHandler.Shield(() => WorkShiftsRepository.FindAllOpenShiftsByDateRange(from.Date, to.Date));
        }

        private static List<WorkShift> fetchOpenShifts(DateTime now, int startOffset, int endOffset)
        {
            return FaultHandler.Shield(
                                       () =>
                                       WorkShiftsRepository.FindAllOpenShiftsByDateRange(now.AddDays(startOffset).Date,
                                                                                         now.AddDays(endOffset + 1).Date));
        }

        private void RefundDialog_Load(object sender, EventArgs e)
        {
            if (opIsDiscount())
            {
                // This is a Discount dialog
                grdOpenShifts.Enabled = false;
                labelAmt.Text = "Discount Amount:";
                labelNts.Text = "Discount Notes:";
                btnIssueTransaction.Text = "Issue Discount";
                //Text = "Issue Discount";
            }
            else if (opIsDiscountRebate())
            {
                // This is a Discount dialog
                grdOpenShifts.Enabled = false;
                labelAmt.Text = "Rebate Amount:";
                labelNts.Text = "Rebate Notes:";
                btnIssueTransaction.Text = "Issue Rebate";
                //Text = "Issue Discount Rebate";
            }

            populateControls();
        }

        private void btnExit_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        private WorkShift getSelectedShift()
        {
            return grdOpenShifts.SelectedRows != null && grdOpenShifts.SelectedRows.Count == 1
                       ? grdOpenShifts.SelectedRows[0].Tag as WorkShift
                       : null;
        }

        private bool validateInputControls()
        {
            if (opIsRefund() && getSelectedShift() == null)
            {
                MessageDlg.Warning("Please enter a valid work shift.");
                return false;
            }

            decimal amount;
            if (!decimal.TryParse(txtAmount.Text.Trim(), out amount) || amount <= 0m)
            {
                MessageDlg.Warning("Please enter a valid transaction amount.");
                txtAmount.Select();
                return false;
            }

            if (string.IsNullOrEmpty(cboNotes.Text.Trim()))
            {
                MessageDlg.Warning("Please enter transaction notes.");
                cboNotes.Select();
                return false;
            }

            return true;
        }

        private void focusAmountTextbox(string message)
        {
            MessageDlg.Warning(message);
            txtAmount.Select();
            txtAmount.Focus();
        }

        private void btnVerifyShift_Click(object sender, EventArgs e)
        {
            refreshCurrentLabOrder();
            if (!validateInputControls()) return;

            var inputAmount = decimal.Parse(txtAmount.Text.Trim());

            var paid = _currentLabOrder.InvoiceMaster.PaidAmount;
            var disc = _currentLabOrder.InvoiceMaster.DiscountAmount;
            var due = _currentLabOrder.InvoiceMaster.DueAmount;

            if (opIsRefund())
            {
                if (inputAmount > paid)
                {
                    focusAmountTextbox(string.Format("Refund Amount ({0}) cannot be greater than the Paid Amount ({1})",
                                                     SharedUtilities.MoneyToStringPlain(inputAmount),
                                                     SharedUtilities.MoneyToStringPlain(paid)));
                    return;
                }

                var shift = getSelectedShift();
                if (shift == null)
                {
                    MessageDlg.Error("Please select an open work shift.");
                    grdOpenShifts.Select();
                    return;
                }

                FaultHandler.Shield(() =>
                                    {
                                        shift.ReconcileShiftFinances();
                                        WorkShiftsRepository.Save();
                                    });

                if (inputAmount > shift.FinalBalance)
                {
                    focusAmountTextbox(string.Format(
                                                     "Refund Amount ({0}) cannot be greater than the users Cash-At-Hand Amount ({1})",
                                                     SharedUtilities.MoneyToStringPlain(inputAmount),
                                                     SharedUtilities.MoneyToStringPlain(shift.FinalBalance)));
                    return;
                }
            }
            else
            {
                if (opIsDiscount())
                {
                    if (inputAmount > due)
                    {
                        focusAmountTextbox(string.Format(
                                                         "Discount Amount ({0}) cannot be greater than the Due Amount ({1})",
                                                         SharedUtilities.MoneyToStringPlain(inputAmount),
                                                         SharedUtilities.MoneyToStringPlain(due)));
                        return;
                    }
                }
                else if (opIsDiscountRebate())
                {
                    if (inputAmount > disc)
                    {
                        focusAmountTextbox(string.Format(
                                                         "Discount Rebate Amount ({0}) cannot be greater than the Discount Amount ({1})",
                                                         SharedUtilities.MoneyToStringPlain(inputAmount),
                                                         SharedUtilities.MoneyToStringPlain(disc)));
                        return;
                    }
                }
            }

            MessageDlg.Info("Ok to perform transaction!");
        }

        private bool shiftCanIssueRefund(WorkShift shift, decimal amount)
        {
            if (shift == null)
            {
                MessageDlg.Error("Please select an open work shift.");
                grdOpenShifts.Select();
                return false;
            }

            if (shift.IsClosed)
            {
                MessageDlg.Error("The work shift was closed by the user.\nSelect another open shift.");
                grdOpenShifts.Select();
                return false;
            }

            FaultHandler.Shield(() =>
                                {
                                    shift.ReconcileShiftFinances();
                                    WorkShiftsRepository.Save();
                                });

#if DISALLOW_NEGATIVE_SHIFT_BALANCE
            if (amount > shift.FinalBalance)
            {
                focusAmountTextbox(
                                   string.Format(
                                                 "Refund Amount ({0}) cannot be greater than the users Cash-At-Hand Amount ({1})",
                                                 SharedUtilities.MoneyToStringPlain(amount),
                                                 SharedUtilities.MoneyToStringPlain(shift.FinalBalance)));
                return false;
            }
#endif

            return true;
        }

        private void btnIssueTransaction_Click(object sender, EventArgs e)
        {
            refreshCurrentLabOrder();
            if (!validateInputControls()) return;
            var inputAmount = decimal.Parse(txtAmount.Text.Trim());
            var notes = cboNotes.Text.Trim();

            var paid = _currentLabOrder.InvoiceMaster.PaidAmount;
            var due = _currentLabOrder.InvoiceMaster.DueAmount;
            var disc = _currentLabOrder.InvoiceMaster.DiscountAmount;

            var username = string.Empty;

            if (opIsRefund())
            {
                if (inputAmount > paid)
                {
                    focusAmountTextbox(string.Format("Refund Amount ({0}) cannot be greater than the Paid Amount ({1})",
                                                     SharedUtilities.MoneyToStringPlain(inputAmount),
                                                     SharedUtilities.MoneyToStringPlain(paid)));
                    return;
                }

                var shift = getSelectedShift();
                if (!shiftCanIssueRefund(shift, inputAmount)) return;

                username = FaultHandler.Shield(() => UsersRepository.GetUserDisplayName(shift.UserId));
            }
            else
            {
                if (opIsDiscount())
                {
                    if (inputAmount > due)
                    {
                        focusAmountTextbox(string.Format(
                                                         "Discount Amount ({0}) cannot be greater than the Due Amount ({1})",
                                                         SharedUtilities.MoneyToStringPlain(inputAmount),
                                                         SharedUtilities.MoneyToStringPlain(due)));
                        return;
                    }
                }
                else if (opIsDiscountRebate())
                {
                    if (inputAmount > disc)
                    {
                        focusAmountTextbox(string.Format(
                                                         "Discount Rebate Amount ({0}) cannot be greater than the Discount Amount ({1})",
                                                         SharedUtilities.MoneyToStringPlain(inputAmount),
                                                         SharedUtilities.MoneyToStringPlain(disc)));
                        return;
                    }
                }
            }

            // final confirmation
            var msg = "Please confirm the following:\n\nRefund Amount: {0}\nFrom: {1}";
            switch (_operationMode)
            {
                case RefundDiscountOperationMode.Discount:
                    msg = "Please confirm the following:\n\nDiscount Amount: {0}";
                    break;
                case RefundDiscountOperationMode.DiscountRebate:
                    msg = "Please confirm the following:\n\nDiscount Rebate Amount: {0}";
                    break;
            }
            msg = string.Format(msg, SharedUtilities.MoneyToString(inputAmount, true), username);

            var theTime = DateTime.Now;
            if (MessageDlg.Confirm(msg) && CaptchaDialog.ConfirmCaptcha(3, false, false))
            {
                if (DateTime.Now.Subtract(theTime).Minutes > 1)
                {
                    MessageDlg.Warning(
                                       "More than a minute had elapsed.\nTo ensure the integrity of data, you must perform this operation swiftly.");
                    refreshCurrentLabOrder();
                    return;
                }

                // reload the lab order and re-verify
                refreshCurrentLabOrder();
                var wfStage = (WorkflowStageType) _currentLabOrder.WorkflowStage;

                short userId = -1;
                var shiftId = -1;
                WorkShift shift = null;

                if (opIsRefund())
                {
                    shift = getSelectedShift();
                    userId = shift.UserId;
                    shiftId = shift.Id;

                    // reload the shift record from database... in case something changed during the UI wait
                    //FaultHandler.Shield(WorkShiftsRepository.Reset); // << bug!!
                    shift = FaultHandler.Shield(() => WorkShiftsRepository.FindUserShiftById(userId, shiftId));

                    if (!shiftCanIssueRefund(shift, inputAmount)) return;
                }

                if (opIsRefund())
                {
                    if (inputAmount > _currentLabOrder.InvoiceMaster.PaidAmount)
                    {
                        focusAmountTextbox(
                                           string.Format(
                                                         "Refund Amount ({0}) cannot be greater than the Paid Amount ({1})",
                                                         SharedUtilities.MoneyToStringPlain(inputAmount),
                                                         SharedUtilities.MoneyToStringPlain(
                                                                                            _currentLabOrder
                                                                                                .InvoiceMaster
                                                                                                .PaidAmount)));
                        return;
                    }

                    FaultHandler.Shield(() =>
                                        {
                                            _currentLabOrder.InvoiceMaster.IssueRefund(shift,
                                                                                       inputAmount,
                                                                                       TransactionFlag.None,
                                                                                       notes,
                                                                                       CurrentUserContext.UserId,
                                                                                       wfStage);

                                            //InvoiceMasterRepository.Save();
                                            _currentLabOrder.InvoiceMaster.ReconcileInvoiceFinances(true);
                                            shift.ReconcileShiftFinances();
                                            //InvoiceMasterRepository.UpdateInvoiceMaster(_currentLabOrder.InvoiceMaster);
                                            InvoiceMasterRepository.Save();

                                            // log the target users event
                                            var logMessage = string.Format("Refund {0} auth by {1}",
                                                                           SharedUtilities.MoneyToStringPlain(
                                                                                                              inputAmount),
                                                                           UsersRepository.GetUserDisplayName(
                                                                                                              CurrentUserContext
                                                                                                                  .UserId));
                                            AuditTrailRepository.LogGenericEvent(AuditEventCategory.Financial,
                                                                                 AuditEventType.finRefundIssued,
                                                                                 null,
                                                                                 userId,
                                                                                 shiftId,
                                                                                 _currentLabOrder.InvoiceId,
                                                                                 null,
                                                                                 null,
                                                                                 logMessage);
                                        });
                }
                else
                {
                    if (opIsDiscount())
                    {
                        if (inputAmount > _currentLabOrder.InvoiceMaster.DueAmount)
                        {
                            focusAmountTextbox(string.Format(
                                                             "Discount Amount ({0}) cannot be greater than the Due Amount ({1})",
                                                             SharedUtilities.MoneyToStringPlain(inputAmount),
                                                             SharedUtilities.MoneyToStringPlain(
                                                                                                _currentLabOrder
                                                                                                    .InvoiceMaster
                                                                                                    .DueAmount)));
                            return;
                        }
                    }
                    else if (opIsDiscountRebate())
                    {
                        if (inputAmount > _currentLabOrder.InvoiceMaster.DiscountAmount)
                        {
                            focusAmountTextbox(string.Format(
                                                             "Discount Rebate Amount ({0}) cannot be greater than the Discount Amount ({1})",
                                                             SharedUtilities.MoneyToStringPlain(inputAmount),
                                                             SharedUtilities.MoneyToStringPlain(
                                                                                                _currentLabOrder
                                                                                                    .InvoiceMaster
                                                                                                    .DiscountAmount)));
                            return;
                        }
                    }

                    FaultHandler.Shield(() =>
                                        {
                                            var myShift =
                                                WorkShiftsRepository.FindUserShiftById(CurrentUserContext.UserId,
                                                                                       CurrentUserContext.WorkShiftId);
                                            if (opIsDiscount())
                                            {
                                                _currentLabOrder.InvoiceMaster.IssueDiscount(myShift, inputAmount,
                                                                                             TransactionFlag.None,
                                                                                             notes, wfStage);
                                            }
                                            else if (opIsDiscountRebate())
                                            {
                                                _currentLabOrder.InvoiceMaster.IssueDiscountRebate(myShift, inputAmount,
                                                                                                   TransactionFlag.None,
                                                                                                   notes, wfStage);
                                            }

                                            InvoiceMasterRepository.Save();

                                            _currentLabOrder.InvoiceMaster.ReconcileInvoiceFinances(true);
                                            InvoiceMasterRepository.Save();
                                        });
                }

                // FIX #134: If the order workflow stage is set to "Fulfilled" (all reports dispatched), then check 
                // if the invoice contains any unpaid dues. In that case, we downgrade the workflow stage to "Dispatched"
                // to allow receiving of the due amount.
                refreshCurrentLabOrder(false);
                if (hasActiveLabOrder())
                {
                    var invoiceId = _currentLabOrder.InvoiceId;

                    FaultHandler.Shield(() =>
                                        {
                                            var details = PatientLabOrdersRepository.FetchLabOrderDetails(invoiceId);

                                            if (details.DueAmount > 0 &&
                                                details.WorkflowStage == (byte) WorkflowStageType.OrderFulfillment)
                                            {
                                                var newStage = WorkflowStageType.ReportDispatch;
                                                PatientLabOrdersRepository.UpdateLabOrderWorkflowStage(invoiceId,
                                                                                                       newStage);
                                                var remark = string.Format(
                                                                           @"Invoice workflow stage downgraded to '{0}' because of unpaid dues",
                                                                           EnumUtils.EnumDescription(newStage));
                                                AuditTrailRepository.LogSystemEvent(AuditEventCategory.System,
                                                                                    AuditEventType.wfAutoEstimated,
                                                                                    newStage, invoiceId, null, null,
                                                                                    remark);
                                            }
                                        });
                }
                MessageDlg.Info("Operation successful.");
                populateControls();
            }
        }

        private bool hasActiveLabOrder()
        {
            return _currentLabOrder != null
                   && _currentLabOrder.InvoiceMaster != null
                   && !_currentLabOrder.IsCancelled;
        }

        private void refreshCurrentLabOrder(bool updateControls = true)
        {
            FaultHandler.Shield(() =>
                                {
                                    PatientLabOrdersRepository.Reset();
                                    _currentLabOrder = PatientLabOrdersRepository.FindById(_currentLabOrder.InvoiceId);
                                });
            if (updateControls)
                populateControls(false);
        }

        private void txtAmount_EditValueChanged(object sender, EventArgs e)
        {
            var amount = 0m;
            if (decimal.TryParse(txtAmount.Text.Trim(), out amount))
            {
                if (opIsRefund())
                {
                    var refund = _currentLabOrder.InvoiceMaster.RefundAmount + amount;
                    var paid = _currentLabOrder.InvoiceMaster.PaidAmount - amount;
                    var due = _currentLabOrder.InvoiceMaster.DueAmount + amount;

                    lblAmountPaid.Text = SharedUtilities.MoneyToStringPlain(paid);
                    lblRefund.Text = SharedUtilities.MoneyToStringPlain(refund);
                    lblCurrentDue.Text = SharedUtilities.MoneyToStringPlain(due);
                }
                else if (opIsDiscount())
                {
                    var disc = _currentLabOrder.InvoiceMaster.DiscountAmount + amount;
                    var net = _currentLabOrder.InvoiceMaster.NetPayable - amount;
                    var due = _currentLabOrder.InvoiceMaster.DueAmount - amount;

                    lblNetPayable.Text = SharedUtilities.MoneyToStringPlain(net);
                    lblDiscount.Text = SharedUtilities.MoneyToStringPlain(disc);
                    lblCurrentDue.Text = SharedUtilities.MoneyToStringPlain(due);
                }
                else if (opIsDiscountRebate())
                {
                    var disc = _currentLabOrder.InvoiceMaster.DiscountAmount - amount;
                    var net = _currentLabOrder.InvoiceMaster.NetPayable + amount;
                    var due = _currentLabOrder.InvoiceMaster.DueAmount + amount;

                    lblNetPayable.Text = SharedUtilities.MoneyToStringPlain(net);
                    lblDiscount.Text = SharedUtilities.MoneyToStringPlain(disc);
                    lblCurrentDue.Text = SharedUtilities.MoneyToStringPlain(due);
                }
            }
        }

        private void txtAmount_KeyUp(object sender, KeyEventArgs e)
        {
        }

        private void btnTransactionsHistory_Click(object sender, EventArgs e)
        {
            using (var frm = new InvoiceTransactionsViewerDialog(_currentLabOrder.InvoiceId))
            {
                frm.UpdateControls();
                frm.ShowDialog();
            }
        }

        private void RefundDiscountDialog_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.F5 && opIsRefund())
            {
                var start = FaultHandler.Shield(() => AppSysRepository.GetServerTime());
                var end = start.AddDays(1);
                using (var frm = new DateRangeSelectDialog())
                {
                    if (frm.ShowDialog(this) == DialogResult.OK)
                    {
                        start = frm.DateFrom;
                        end = frm.DateTo;
                    }
                }
                var shifts = fetchOpenShifts(start, end);
                reconcileShifts(shifts);
                populateShiftsGrid(shifts);
            }
        }

        private void reconcileShifts(List<WorkShift> shifts)
        {
            if (shifts != null && shifts.Any())
            {
                WaitFormControl.WaitOperation(this, () => FaultHandler.Shield(
                                                                              () =>
                                                                              {
                                                                                  shifts.ForEach(
                                                                                                 x =>
                                                                                                 x
                                                                                                     .ReconcileShiftFinances
                                                                                                     ());
                                                                                  WorkShiftsRepository.Save();
                                                                              }), "Reconciling workshifts...");
            }
        }
    }
}