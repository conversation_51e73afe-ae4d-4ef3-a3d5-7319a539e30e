﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: InvoiceTransactionsViewerDialog.cs 1404 2014-09-20 14:11:20Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Windows.Forms;
using CuttingEdge.Conditions;
using DevExpress.XtraEditors;
using LabMaestro.BusinessLogic;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Printing;
using LabMaestro.Shared;
using Xceed.Grid;

namespace LabMaestro.Controls.Win;

public partial class InvoiceTransactionsViewerDialog : XtraForm, IExecutableDialog
{
    private readonly long _invoiceId;

    private LabOrderFinancialContext _currentLabOrderContext;
    private DataCell _oldCell;

    public InvoiceTransactionsViewerDialog(long invoiceId)
    {
        InitializeComponent();
        WinUtils.ApplyTheme(this);
        WinUtils.SetFormTitle(this, "Invoice Transactions");
        createGridColumns();

        Condition.Requires(invoiceId).IsGreaterThan(0);
        _invoiceId = invoiceId;
    }

    public bool ExecuteDialog(IWin32Window parent)
    {
        UpdateControls();
        ShowDialog(parent);
        return true;
    }

    public void UpdateControls()
    {
        var order = FaultHandler.Shield(() => PatientLabOrdersRepository.FindById(_invoiceId));
        if (order != null)
        {
            _currentLabOrderContext = LabOrderFinancialContext.AssembleFrom(order);
        }

        lblInvoiceNum.Text = _currentLabOrderContext.InvoiceId.ToString();
        lblOrderId.Text = _currentLabOrderContext.OrderId;
        lblPatientName.Text = _currentLabOrderContext.PatientName;
        lblReferrerName.Text = _currentLabOrderContext.PhysicianName;
        lblOrderingUserName.Text = _currentLabOrderContext.OrderingUserId > 0
            ? FaultHandler.Shield(() => UsersRepository.GetUserDisplayName(_currentLabOrderContext.OrderingUserId))
            : string.Empty;
        lblOrderTime.Text = SharedUtilities.DateTimeToString(_currentLabOrderContext.OrderDateTime);
        lblNetPayable.Text = SharedUtilities.MoneyToStringPlain(_currentLabOrderContext.InvoiceMaster.NetPayable);
        lblCurrentDue.Text = SharedUtilities.MoneyToStringPlain(_currentLabOrderContext.InvoiceMaster.DueAmount);
        lblAmountPaid.Text = SharedUtilities.MoneyToStringPlain(_currentLabOrderContext.InvoiceMaster.PaidAmount);

        updateTransactionsGrid();
    }

    private void updateTransactionsGrid()
    {
        grdTransactionHistory.BeginInit();
        try
        {
            grdTransactionHistory.DataRows.Clear();
            foreach (var transaction in _currentLabOrderContext.Transactions)
            {
                var row = grdTransactionHistory.DataRows.AddNew();
                row.Cells[@"type"].Value = EnumUtils.EnumDescription(transaction.TxType);
                row.Cells[@"method"].Value = EnumUtils.EnumDescription(transaction.PaymentMethod);
                row.Cells[@"type"].BackColor = WinUtils.GetTransactionColor(transaction.TxType);
                row.Cells[@"cash"].Value = SharedUtilities.MoneyToString(transaction.TxAmount, false);
                row.Cells[@"noncash"].Value = SharedUtilities.MoneyToString(transaction.NonCashAmount, false);
                //row.Cells[@"date"].Value = SharedUtilities.DateToStringShort(transaction.TxTime);
                //row.Cells[@"time"].Value = SharedUtilities.TimeToString(transaction.TxTime);
                row.Cells[@"time"].Value = SharedUtilities.DateTimeToString24(transaction.TxTime);
                row.Cells[@"user"].Value = transaction.StaffName;
                row.Cells[@"auth"].Value = transaction.AuthorizerName;
                row.Cells[@"notes"].Value = transaction.UserRemarks;

                grdTransactionHistory.GridWireCellToolTips(row.Cells, showToolTip);

                row.Height = 21;
                row.VerticalAlignment = VerticalAlignment.Center;
                row.Tag = transaction;
                row.EndEdit();
            }
        }
        finally
        {
            grdTransactionHistory.EndInit();
        }
    }

    private void showToolTip(object sender, EventArgs e)
    {
        var cell = (DataCell)sender;
        if (_oldCell == cell) return;

        _oldCell = cell;
        var content = _oldCell.Value;
        toolTip.SetToolTip(grdTransactionHistory, (content != null) ? content.ToString() : string.Empty);
    }

    private void createGridColumns()
    {
        grdTransactionHistory.BeginInit();
        try
        {
            grdTransactionHistory.Columns.Clear();
            var i = 0;
            //grdTransactionHistory.GridAddColumn(@"date", "Date", i++, 80);
            grdTransactionHistory.GridAddColumn(@"time", "Time", i++, 150);
            grdTransactionHistory.GridAddColumn(@"type", "Type", i++, 105);
            grdTransactionHistory.GridAddColumn(@"cash", "Cash", i++, 70);
            grdTransactionHistory.GridAddColumn(@"noncash", "Cashless", i++, 70);
            grdTransactionHistory.GridAddColumn(@"method", "Method", i++, 50);
            grdTransactionHistory.GridAddColumn(@"user", "Performed By", i++, 90);
            grdTransactionHistory.GridAddColumn(@"auth", "Auth. By", i++, 90);
            grdTransactionHistory.GridAddColumn(@"notes", "Notes", i, 125);
        }
        finally
        {
            grdTransactionHistory.EndInit();
        }
    }

    private void btnClose_Click(object sender, EventArgs e)
    {
        DialogResult = DialogResult.OK;
    }

    private void printTransactionsReport(bool showPreview)
    {
        var dto = new DynamicTabulatedReportPrintDto
        {
            PrintedOn = SharedUtilities.DateTimeToString24(AppSysRepository.GetServerTime()),
            PrintedBy = CurrentUserContext.UserDisplayName,
            ReportHeader = "Invoice Transactions",
            Label1 = "Invoice Id",
            Value1 = _currentLabOrderContext.InvoiceId.ToString(),
            Label2 = "Booked On",
            Value2 = SharedUtilities.DateTimeToString(_currentLabOrderContext.OrderDateTime)
        };
        dto.SetHeadings("Time", "Action", "Cash", "Cashless", "Rcvd Total", "Performed By", "Authorized By", "Note");
        foreach (var tx in _currentLabOrderContext.Transactions)
        {
            dto.AddRow(SharedUtilities.DateTimeToStringWithSep24(tx.TxTime),
                EnumUtils.EnumDescription(tx.TxType),
                SharedUtilities.MoneyToStringPlainCultureNonZero(tx.TxAmount),
                SharedUtilities.MoneyToStringPlainCultureNonZero(tx.NonCashAmount),
                SharedUtilities.MoneyToStringPlainCultureNonZero(tx.TxAmount + tx.NonCashAmount),
                tx.StaffName,
                tx.AuthorizerName,
                tx.UserRemarks
            );
        }

        PrintHelper.PrintDynamicTabulatedReport(dto, showPreview);
    }

    private void btnPrintPreview_Click(object sender, EventArgs e) => printTransactionsReport(true);

    private void btnPrint_Click(object sender, EventArgs e) => printTransactionsReport(false);
}