﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the organization nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: AuditTrailViewerDialog.cs 1065 2013-10-31 11:39:48Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Windows.Forms;
using C1.LiveLinq;
using C1.LiveLinq.Collections;
using DevExpress.Utils;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraEditors.Repository;
using LabMaestro.BusinessLogic;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Shared;

namespace LabMaestro.Controls.Win
{
    public partial class AuditTrailViewerDialog : XtraForm, IExecutableDialog
    {
        private readonly List<ComponentLabTestsSlice> _componentTests;
        private readonly LabOrderContextInfo _contextInfo;
        private readonly IndexedCollection<AuditEventSlice> _events;

        public AuditTrailViewerDialog(LabOrderContextInfo context, List<AuditEventSlice> events)
        {
            _contextInfo = context;
            _events = new IndexedCollection<AuditEventSlice>(events);
            _componentTests = [];

            InitializeComponent();
        }


        public bool ExecuteDialog(IWin32Window parent)
        {
            UpdateControls();
            ShowDialog(parent);
            return true;
        }

        public void UpdateControls()
        {
        }

        private void loadComponentLabTestsIntoGrid(long invoiceId)
        {
            gridTests.BeginUpdate();

            try
            {
                var slices =
                    FaultHandler.Shield(() => OrderedTestsRepository.GetAllOrderedTestDetailsInInvoice(invoiceId));
                if (slices is { Count: > 0 })
                {
                    foreach (var slice in slices)
                    {
                        _componentTests.Add(ComponentLabTestsSlice.AssembleFrom(slice));
                    }
                }

                componentLabTestsSliceBindingSource.DataSource = _componentTests;
            }
            finally
            {
                gridTests.EndUpdate();
            }
        }

        private void setDataSource(List<AuditEventSlice> list)
        {
            gridControl.BeginUpdate();
            try
            {
                gridControl.DataSource = new BindingList<AuditEventSlice>(list);
            }
            finally
            {
                gridControl.EndUpdate();
            }
        }

        private void AuditTrailViewerDialog_Load(object sender, EventArgs e)
        {
            WinUtils.ApplyTheme(this);
            WinUtils.SetFormTitle(this, $"Audit Trails");
            updateControls();
        }

        private void updateControls()
        {
            //lblBookingStaff.Text = _contextInfo.OrderingUserName;
            lblPatientName.Text = _contextInfo.OrderDemoInfo.FullName;
            lblReferrerName.Text = _contextInfo.ReferrerCustomName;
            lblInvoiceId.Text = _contextInfo.InvoiceId.ToString();
            lblOrderId.Text = _contextInfo.OrderId;
            lblBookingDate.Text = SharedUtilities.DateTimeToString24(_contextInfo.OrderDateTime);
            setDataSource(_events.ToList());
            loadComponentLabTestsIntoGrid(_contextInfo.InvoiceId);

            var cancelImages = new ImageCollection();
            cancelImages.AddImage(Resources.cancel2_16);
            var cancelImageCombo = gridTests.RepositoryItems.Add("ImageComboBoxEdit") as RepositoryItemImageComboBox;
            cancelImageCombo.SmallImages = cancelImages;
            cancelImageCombo.Items.Add(new ImageComboBoxItem(true, 0));
            cancelImageCombo.GlyphAlignment = HorzAlignment.Center;
            gvwTests.Columns[@"IsCancelled"].ColumnEdit = cancelImageCombo;
            gvwTests.Columns[@"BundleIsCancelled"].ColumnEdit = cancelImageCombo;
        }

        private void rgFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            var category = rgFilter.SelectedIndex switch
            {
                1 => AuditEventCategory.OrderEntry,
                2 => AuditEventCategory.Financial,
                3 => AuditEventCategory.Workflow,
                4 => AuditEventCategory.System,
                _ => AuditEventCategory.Unknown
            };

            setDataSource(category == AuditEventCategory.Unknown
                ? _events.OrderByDescending(x => x.Id).ToList()
                : _events.AsIndexed()
                    .Where(x => x.EventCategory == category)
                    .OrderByDescending(x => x.Id)
                    .ToList());
        }

        private void btnPrint_Click(object sender, EventArgs e)
        {
            //TODO: implement
            MessageDlg.Info("Feature not implemented.");
        }

        private void AuditTrailViewerDialog_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape)
            {
                e.Handled = true;
                DialogResult = DialogResult.OK;
            }
        }
    }
}