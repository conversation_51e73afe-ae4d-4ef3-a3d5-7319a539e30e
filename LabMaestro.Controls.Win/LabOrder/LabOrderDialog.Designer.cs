﻿namespace LabMaestro.Controls.Win
{
    partial class LabOrderDialog
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            Xceed.Grid.GradientMap gradientMap1 = new Xceed.Grid.GradientMap();
            Xceed.Grid.GradientStop gradientStop1 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientStop gradientStop2 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientMap gradientMap5 = new Xceed.Grid.GradientMap();
            Xceed.Grid.GradientStop gradientStop9 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientStop gradientStop10 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientMap gradientMap3 = new Xceed.Grid.GradientMap();
            Xceed.Grid.GradientStop gradientStop5 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientStop gradientStop6 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientMap gradientMap2 = new Xceed.Grid.GradientMap();
            Xceed.Grid.GradientStop gradientStop3 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientStop gradientStop4 = new Xceed.Grid.GradientStop();
            DevExpress.XtraEditors.Controls.EditorButtonImageOptions editorButtonImageOptions1 = new DevExpress.XtraEditors.Controls.EditorButtonImageOptions();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject1 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject2 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject3 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject4 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.XtraEditors.Controls.EditorButtonImageOptions editorButtonImageOptions3 = new DevExpress.XtraEditors.Controls.EditorButtonImageOptions();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(LabOrderDialog));
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject9 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject10 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject11 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject12 = new DevExpress.Utils.SerializableAppearanceObject();
            this.barManager = new DevExpress.XtraBars.BarManager(this.components);
            this.toolBar = new DevExpress.XtraBars.Bar();
            this.btnNewLabOrder = new DevExpress.XtraBars.BarButtonItem();
            this.btnPutOnHold = new DevExpress.XtraBars.BarButtonItem();
            this.btnOrdersOnHold = new DevExpress.XtraBars.BarButtonItem();
            this.btnCopyDemographics = new DevExpress.XtraBars.BarButtonItem();
            this.btnPasteDemographics = new DevExpress.XtraBars.BarButtonItem();
            this.btnDeleteTest = new DevExpress.XtraBars.BarButtonItem();
            this.btnClearAllTests = new DevExpress.XtraBars.BarButtonItem();
            this.btnCancelOrder = new DevExpress.XtraBars.BarButtonItem();
            this.btnRegisterCustomer = new DevExpress.XtraBars.BarButtonItem();
            this.btnSetDeliveryTime = new DevExpress.XtraBars.BarButtonItem();
            this.btnCalculator = new DevExpress.XtraBars.BarButtonItem();
            this.btnExit = new DevExpress.XtraBars.BarButtonItem();
            this.menuBar = new DevExpress.XtraBars.Bar();
            this.OrdersMenu = new DevExpress.XtraBars.BarSubItem();
            this.miNewLabOrder = new DevExpress.XtraBars.BarButtonItem();
            this.miOrderPutOnHold = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem3 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem1 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem2 = new DevExpress.XtraBars.BarButtonItem();
            this.miAssignAssocLab = new DevExpress.XtraBars.BarButtonItem();
            this.miInvoiceSearch = new DevExpress.XtraBars.BarButtonItem();
            this.UserMenu = new DevExpress.XtraBars.BarSubItem();
            this.miUserShiftManagement = new DevExpress.XtraBars.BarButtonItem();
            this.miShiftSearch = new DevExpress.XtraBars.BarButtonItem();
            this.miUserChangePassword = new DevExpress.XtraBars.BarButtonItem();
            this.miExit = new DevExpress.XtraBars.BarButtonItem();
            this.CatalogsMenu = new DevExpress.XtraBars.BarSubItem();
            this.btnRefreshSearchIndices = new DevExpress.XtraBars.BarButtonItem();
            this.browseTestCatalog = new DevExpress.XtraBars.BarButtonItem();
            this.miBrowsePhysicianCatalog = new DevExpress.XtraBars.BarButtonItem();
            this.barAndDockingController = new DevExpress.XtraBars.BarAndDockingController(this.components);
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.imageList1 = new System.Windows.Forms.ImageList(this.components);
            this.barSubItem2 = new DevExpress.XtraBars.BarSubItem();
            this.barSubItem4 = new DevExpress.XtraBars.BarSubItem();
            this.grdOrderedTests = new Xceed.Grid.GridControl();
            this.dataRowTemplate2 = new Xceed.Grid.DataRow();
            this.visualGridElementStyle2 = new Xceed.Grid.VisualGridElementStyle();
            this.dataRowStyle1 = new Xceed.Grid.VisualGridElementStyle();
            this.columnManagerRow2 = new Xceed.Grid.ColumnManagerRow();
            this.grdBillableItems = new Xceed.Grid.GridControl();
            this.dataRowTemplate1 = new Xceed.Grid.DataRow();
            this.visualGridElementStyle1 = new Xceed.Grid.VisualGridElementStyle();
            this.dataRowStyle2 = new Xceed.Grid.VisualGridElementStyle();
            this.columnManagerRow1 = new Xceed.Grid.ColumnManagerRow();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.rgSex = new DevExpress.XtraEditors.RadioGroup();
            this.styleController = new DevExpress.XtraEditors.StyleController(this.components);
            this.txtLastName = new DevExpress.XtraEditors.TextEdit();
            this.txtFirstName = new DevExpress.XtraEditors.TextEdit();
            this.cboPatientTitle = new DevExpress.XtraEditors.ComboBoxEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.dteDateOfBirth = new DevExpress.XtraEditors.DateEdit();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            this.cboPhysician = new Syncfusion.Windows.Forms.Tools.ComboBoxBase();
            this.grdListPhysician = new Syncfusion.Windows.Forms.Grid.GridListControl();
            this.cboLabTest = new Syncfusion.Windows.Forms.Tools.ComboBoxBase();
            this.grdListLabTest = new Syncfusion.Windows.Forms.Grid.GridListControl();
            this.txtPhysicianCustomName = new DevExpress.XtraEditors.TextEdit();
            this.txtOrderNotes = new DevExpress.XtraEditors.TextEdit();
            this.labelControl9 = new DevExpress.XtraEditors.LabelControl();
            this.lblNote = new DevExpress.XtraEditors.LabelControl();
            this.lblTelephone = new DevExpress.XtraEditors.LabelControl();
            this.panelControl1 = new DevExpress.XtraEditors.PanelControl();
            this.lblGrossPayable = new DevExpress.XtraEditors.LabelControl();
            this.labelControl13 = new DevExpress.XtraEditors.LabelControl();
            this.panelControl2 = new DevExpress.XtraEditors.PanelControl();
            this.lblNumTests = new DevExpress.XtraEditors.LabelControl();
            this.labelControl18 = new DevExpress.XtraEditors.LabelControl();
            this.btnPayment = new DevExpress.XtraEditors.SimpleButton();
            this.txtDiscountPercent = new DevExpress.XtraEditors.TextEdit();
            this.labelControl14 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl15 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl16 = new DevExpress.XtraEditors.LabelControl();
            this.panelControl3 = new DevExpress.XtraEditors.PanelControl();
            this.lblNetPayable = new DevExpress.XtraEditors.LabelControl();
            this.labelControl19 = new DevExpress.XtraEditors.LabelControl();
            this.btnSetDiscountPercent = new DevExpress.XtraEditors.SimpleButton();
            this.btnApplyDiscount = new DevExpress.XtraEditors.SimpleButton();
            this.btnBIAdd = new DevExpress.XtraEditors.SimpleButton();
            this.btnBIModify = new DevExpress.XtraEditors.SimpleButton();
            this.btnBIDelete = new DevExpress.XtraEditors.SimpleButton();
            this.txtDiscountAmount = new DevExpress.XtraEditors.CalcEdit();
            this.chkUnknownPhysician = new DevExpress.XtraEditors.CheckEdit();
            this.panelControl4 = new DevExpress.XtraEditors.PanelControl();
            this.lblMaxDiscountAmount = new DevExpress.XtraEditors.LabelControl();
            this.lblMaxDiscountPercent = new DevExpress.XtraEditors.LabelControl();
            this.txtAge = new DevExpress.XtraEditors.TextEdit();
            this.chkDirectPatient = new DevExpress.XtraEditors.CheckEdit();
            this.btnDiscountCalculator = new DevExpress.XtraEditors.SimpleButton();
            this.txtExtTrackingId = new DevExpress.XtraEditors.TextEdit();
            this.chkOutsideOrder = new DevExpress.XtraEditors.CheckEdit();
            this.btnBIClear = new DevExpress.XtraEditors.SimpleButton();
            this.txtDiscountNote = new DevExpress.XtraEditors.TextEdit();
            this.lblEmail = new DevExpress.XtraEditors.LabelControl();
            this.txtEmail = new DevExpress.XtraEditors.TextEdit();
            this.lblExternalId = new DevExpress.XtraEditors.LabelControl();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.txtCustomerPHN = new DevExpress.XtraEditors.TextEdit();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.txtCustomerPhone = new DevExpress.XtraEditors.TextEdit();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl11 = new DevExpress.XtraEditors.LabelControl();
            this.cboAffiliateLabs = new DevExpress.XtraEditors.ComboBoxEdit();
            this.cboCorporateClients = new DevExpress.XtraEditors.ComboBoxEdit();
            this.cboAffiliates = new DevExpress.XtraEditors.ComboBoxEdit();
            this.labelControl12 = new DevExpress.XtraEditors.LabelControl();
            this.btnCustomerSearch = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl21 = new DevExpress.XtraEditors.LabelControl();
            this.cboHealthPackages = new DevExpress.XtraEditors.ComboBoxEdit();
            this.btnNormalizeName = new DevExpress.XtraEditors.SimpleButton();
            this.lblSelectedCorp = new DevExpress.XtraEditors.LabelControl();
            this.rgResultReleaseMode = new DevExpress.XtraEditors.RadioGroup();
            this.labelControl22 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl23 = new DevExpress.XtraEditors.LabelControl();
            this.lblSelectedAffiliate = new DevExpress.XtraEditors.LabelControl();
            this.lblAssociatedLab = new DevExpress.XtraEditors.LabelControl();
            this.labelControl26 = new DevExpress.XtraEditors.LabelControl();
            this.lblSelectedHealthPackage = new DevExpress.XtraEditors.LabelControl();
            this.labelControl28 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl20 = new DevExpress.XtraEditors.LabelControl();
            this.lblSelectedAffiliateLab = new DevExpress.XtraEditors.LabelControl();
            this.lblCustomerId = new DevExpress.XtraEditors.LabelControl();
            this.lblCustomerPHN = new DevExpress.XtraEditors.LabelControl();
            this.labelControl10 = new DevExpress.XtraEditors.LabelControl();
            this.txtTelephone = new DevExpress.XtraEditors.TextEdit();
            this.pnlEffectiveDiscountPct = new DevExpress.XtraEditors.PanelControl();
            this.lblEffectiveDiscountPct = new DevExpress.XtraEditors.LabelControl();
            this.btnRegisterCustomerFast = new DevExpress.XtraEditors.SimpleButton();
            this.btnFindCustomer = new DevExpress.XtraEditors.SimpleButton();
            this.btnDissociateCustomer = new DevExpress.XtraEditors.SimpleButton();
            this.btnReimburseSurcharge = new DevExpress.XtraEditors.SimpleButton();
            ((System.ComponentModel.ISupportInitialize)(this.barManager)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdOrderedTests)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataRowTemplate2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.columnManagerRow2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdBillableItems)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataRowTemplate1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.columnManagerRow1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rgSex.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.styleController)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtLastName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFirstName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cboPatientTitle.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dteDateOfBirth.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dteDateOfBirth.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cboPhysician)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdListPhysician)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cboLabTest)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdListLabTest)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPhysicianCustomName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtOrderNotes.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).BeginInit();
            this.panelControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl2)).BeginInit();
            this.panelControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtDiscountPercent.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl3)).BeginInit();
            this.panelControl3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtDiscountAmount.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkUnknownPhysician.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl4)).BeginInit();
            this.panelControl4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtAge.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkDirectPatient.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtExtTrackingId.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkOutsideOrder.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDiscountNote.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtEmail.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCustomerPHN.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCustomerPhone.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cboAffiliateLabs.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cboCorporateClients.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cboAffiliates.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cboHealthPackages.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rgResultReleaseMode.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTelephone.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pnlEffectiveDiscountPct)).BeginInit();
            this.pnlEffectiveDiscountPct.SuspendLayout();
            this.SuspendLayout();
            // 
            // barManager
            // 
            this.barManager.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.toolBar,
            this.menuBar});
            this.barManager.Controller = this.barAndDockingController;
            this.barManager.DockControls.Add(this.barDockControlTop);
            this.barManager.DockControls.Add(this.barDockControlBottom);
            this.barManager.DockControls.Add(this.barDockControlLeft);
            this.barManager.DockControls.Add(this.barDockControlRight);
            this.barManager.DockWindowTabFont = new System.Drawing.Font("Microsoft Sans Serif", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.barManager.Form = this;
            this.barManager.Images = this.imageList1;
            this.barManager.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.OrdersMenu,
            this.barSubItem2,
            this.miNewLabOrder,
            this.miInvoiceSearch,
            this.UserMenu,
            this.miUserShiftManagement,
            this.miUserChangePassword,
            this.miOrderPutOnHold,
            this.barButtonItem1,
            this.barButtonItem2,
            this.barButtonItem3,
            this.barSubItem4,
            this.miExit,
            this.btnNewLabOrder,
            this.btnPutOnHold,
            this.btnOrdersOnHold,
            this.btnCopyDemographics,
            this.btnPasteDemographics,
            this.btnClearAllTests,
            this.btnCancelOrder,
            this.btnSetDeliveryTime,
            this.btnDeleteTest,
            this.CatalogsMenu,
            this.browseTestCatalog,
            this.miBrowsePhysicianCatalog,
            this.btnExit,
            this.btnRefreshSearchIndices,
            this.miShiftSearch,
            this.btnCalculator,
            this.btnRegisterCustomer,
            this.miAssignAssocLab});
            this.barManager.MainMenu = this.menuBar;
            this.barManager.MaxItemId = 32;
            // 
            // toolBar
            // 
            this.toolBar.BarAppearance.Hovered.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.toolBar.BarAppearance.Hovered.Options.UseFont = true;
            this.toolBar.BarAppearance.Normal.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.toolBar.BarAppearance.Normal.Options.UseFont = true;
            this.toolBar.BarAppearance.Pressed.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.toolBar.BarAppearance.Pressed.Options.UseFont = true;
            this.toolBar.BarName = "Tools";
            this.toolBar.DockCol = 0;
            this.toolBar.DockRow = 1;
            this.toolBar.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.toolBar.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.btnNewLabOrder),
            new DevExpress.XtraBars.LinkPersistInfo(this.btnPutOnHold, true),
            new DevExpress.XtraBars.LinkPersistInfo(this.btnOrdersOnHold),
            new DevExpress.XtraBars.LinkPersistInfo(this.btnCopyDemographics, true),
            new DevExpress.XtraBars.LinkPersistInfo(this.btnPasteDemographics),
            new DevExpress.XtraBars.LinkPersistInfo(this.btnDeleteTest, true),
            new DevExpress.XtraBars.LinkPersistInfo(this.btnClearAllTests),
            new DevExpress.XtraBars.LinkPersistInfo(this.btnCancelOrder),
            new DevExpress.XtraBars.LinkPersistInfo(((DevExpress.XtraBars.BarLinkUserDefines)((DevExpress.XtraBars.BarLinkUserDefines.PaintStyle | DevExpress.XtraBars.BarLinkUserDefines.KeyTip))), this.btnRegisterCustomer, "", true, true, true, 0, null, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph, "", ""),
            new DevExpress.XtraBars.LinkPersistInfo(this.btnSetDeliveryTime, true),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.btnCalculator, "", true, true, true, 0, null, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(this.btnExit, true)});
            this.toolBar.OptionsBar.AllowQuickCustomization = false;
            this.toolBar.OptionsBar.DrawDragBorder = false;
            this.toolBar.OptionsBar.UseWholeRow = true;
            this.toolBar.Text = "Tools";
            // 
            // btnNewLabOrder
            // 
            this.btnNewLabOrder.Caption = "New Order";
            this.btnNewLabOrder.Hint = "Create New Lab Order";
            this.btnNewLabOrder.Id = 13;
            this.btnNewLabOrder.ImageOptions.ImageIndex = 3;
            this.btnNewLabOrder.Name = "btnNewLabOrder";
            this.btnNewLabOrder.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.btnNewLabOrder.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnNewLabOrder_ItemClick);
            // 
            // btnPutOnHold
            // 
            this.btnPutOnHold.Caption = "Put On Hold";
            this.btnPutOnHold.Hint = "Put Current Order On Hold";
            this.btnPutOnHold.Id = 14;
            this.btnPutOnHold.ImageOptions.ImageIndex = 1;
            this.btnPutOnHold.Name = "btnPutOnHold";
            this.btnPutOnHold.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.btnPutOnHold.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnPutOnHold_ItemClick);
            // 
            // btnOrdersOnHold
            // 
            this.btnOrdersOnHold.Caption = "Held Orders";
            this.btnOrdersOnHold.Hint = "View All Orders On Hold";
            this.btnOrdersOnHold.Id = 15;
            this.btnOrdersOnHold.ImageOptions.ImageIndex = 0;
            this.btnOrdersOnHold.Name = "btnOrdersOnHold";
            this.btnOrdersOnHold.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.btnOrdersOnHold.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnOrdersOnHold_ItemClick);
            // 
            // btnCopyDemographics
            // 
            this.btnCopyDemographics.Caption = "Copy";
            this.btnCopyDemographics.Hint = "Copy Order Demographic";
            this.btnCopyDemographics.Id = 16;
            this.btnCopyDemographics.ImageOptions.Image = global::LabMaestro.Controls.Win.ResourceIcons.demo_paste_16;
            this.btnCopyDemographics.ImageOptions.ImageIndex = 14;
            this.btnCopyDemographics.Name = "btnCopyDemographics";
            this.btnCopyDemographics.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.btnCopyDemographics.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnCopyDemographics_ItemClick);
            // 
            // btnPasteDemographics
            // 
            this.btnPasteDemographics.Caption = "Paste";
            this.btnPasteDemographics.Hint = "Paste Order Demographic into Current Order";
            this.btnPasteDemographics.Id = 17;
            this.btnPasteDemographics.ImageOptions.Image = global::LabMaestro.Controls.Win.ResourceIcons.demo_copy_16_png;
            this.btnPasteDemographics.ImageOptions.ImageIndex = 3;
            this.btnPasteDemographics.Name = "btnPasteDemographics";
            this.btnPasteDemographics.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.btnPasteDemographics.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnPasteDemographics_ItemClick);
            // 
            // btnDeleteTest
            // 
            this.btnDeleteTest.Caption = "Delete";
            this.btnDeleteTest.Hint = "Delete Selected Test";
            this.btnDeleteTest.Id = 21;
            this.btnDeleteTest.ImageOptions.ImageIndex = 13;
            this.btnDeleteTest.Name = "btnDeleteTest";
            this.btnDeleteTest.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.btnDeleteTest.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnDeleteTest_ItemClick);
            // 
            // btnClearAllTests
            // 
            this.btnClearAllTests.Caption = "Clear All";
            this.btnClearAllTests.Hint = "Remove All Tests";
            this.btnClearAllTests.Id = 18;
            this.btnClearAllTests.ImageOptions.ImageIndex = 11;
            this.btnClearAllTests.Name = "btnClearAllTests";
            this.btnClearAllTests.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.btnClearAllTests.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnClearAllTests_ItemClick);
            // 
            // btnCancelOrder
            // 
            this.btnCancelOrder.Caption = "Cancel Order";
            this.btnCancelOrder.Enabled = false;
            this.btnCancelOrder.Hint = "Cancel Current Lab Order";
            this.btnCancelOrder.Id = 19;
            this.btnCancelOrder.ImageOptions.ImageIndex = 10;
            this.btnCancelOrder.Name = "btnCancelOrder";
            this.btnCancelOrder.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.btnCancelOrder.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnCancelOrder_ItemClick);
            // 
            // btnRegisterCustomer
            // 
            this.btnRegisterCustomer.Caption = "Register Patient";
            this.btnRegisterCustomer.Hint = "Create new Patient Account for App";
            this.btnRegisterCustomer.Id = 30;
            this.btnRegisterCustomer.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnRegisterCustomer.ImageOptions.Image")));
            this.btnRegisterCustomer.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("btnRegisterCustomer.ImageOptions.LargeImage")));
            this.btnRegisterCustomer.Name = "btnRegisterCustomer";
            this.btnRegisterCustomer.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnRegisterCustomer_ItemClick);
            // 
            // btnSetDeliveryTime
            // 
            this.btnSetDeliveryTime.Caption = "Delivery Time";
            this.btnSetDeliveryTime.Hint = "Set Delivery Date && Time for Selected Lab Tests";
            this.btnSetDeliveryTime.Id = 20;
            this.btnSetDeliveryTime.ImageOptions.ImageIndex = 12;
            this.btnSetDeliveryTime.Name = "btnSetDeliveryTime";
            this.btnSetDeliveryTime.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.btnSetDeliveryTime.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnSetDeliveryTime_ItemClick);
            // 
            // btnCalculator
            // 
            this.btnCalculator.Caption = "Calc";
            this.btnCalculator.Hint = "Calculator";
            this.btnCalculator.Id = 28;
            this.btnCalculator.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.calculator_16x16;
            this.btnCalculator.Name = "btnCalculator";
            this.btnCalculator.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnCalculator_ItemClick);
            // 
            // btnExit
            // 
            this.btnExit.Caption = "Close";
            this.btnExit.Id = 25;
            this.btnExit.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.exit_16;
            this.btnExit.Name = "btnExit";
            this.btnExit.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.btnExit.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnExit_ItemClick);
            // 
            // menuBar
            // 
            this.menuBar.BarAppearance.Hovered.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.menuBar.BarAppearance.Hovered.Options.UseFont = true;
            this.menuBar.BarAppearance.Normal.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.menuBar.BarAppearance.Normal.Options.UseFont = true;
            this.menuBar.BarAppearance.Pressed.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.menuBar.BarAppearance.Pressed.Options.UseFont = true;
            this.menuBar.BarName = "Main menu";
            this.menuBar.CanDockStyle = DevExpress.XtraBars.BarCanDockStyle.Top;
            this.menuBar.DockCol = 0;
            this.menuBar.DockRow = 0;
            this.menuBar.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.menuBar.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.OrdersMenu),
            new DevExpress.XtraBars.LinkPersistInfo(this.UserMenu),
            new DevExpress.XtraBars.LinkPersistInfo(this.CatalogsMenu)});
            this.menuBar.OptionsBar.AllowQuickCustomization = false;
            this.menuBar.OptionsBar.DrawDragBorder = false;
            this.menuBar.OptionsBar.UseWholeRow = true;
            this.menuBar.Text = "Main menu";
            // 
            // OrdersMenu
            // 
            this.OrdersMenu.Caption = "&Orders";
            this.OrdersMenu.Id = 0;
            this.OrdersMenu.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.miNewLabOrder),
            new DevExpress.XtraBars.LinkPersistInfo(this.miOrderPutOnHold, true),
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItem3),
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItem1, true),
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItem2),
            new DevExpress.XtraBars.LinkPersistInfo(this.miAssignAssocLab, true),
            new DevExpress.XtraBars.LinkPersistInfo(this.miInvoiceSearch, true)});
            this.OrdersMenu.Name = "OrdersMenu";
            // 
            // miNewLabOrder
            // 
            this.miNewLabOrder.Caption = "&New Lab Order";
            this.miNewLabOrder.Id = 2;
            this.miNewLabOrder.ImageOptions.ImageIndex = 3;
            this.miNewLabOrder.Name = "miNewLabOrder";
            this.miNewLabOrder.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnNewLabOrder_ItemClick);
            // 
            // miOrderPutOnHold
            // 
            this.miOrderPutOnHold.Caption = "&Put On Hold";
            this.miOrderPutOnHold.Id = 7;
            this.miOrderPutOnHold.ImageOptions.ImageIndex = 1;
            this.miOrderPutOnHold.Name = "miOrderPutOnHold";
            this.miOrderPutOnHold.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnPutOnHold_ItemClick);
            // 
            // barButtonItem3
            // 
            this.barButtonItem3.Caption = "Orders On &Hold";
            this.barButtonItem3.Id = 10;
            this.barButtonItem3.ImageOptions.ImageIndex = 0;
            this.barButtonItem3.Name = "barButtonItem3";
            this.barButtonItem3.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnOrdersOnHold_ItemClick);
            // 
            // barButtonItem1
            // 
            this.barButtonItem1.Caption = "&Copy Demographics";
            this.barButtonItem1.Id = 8;
            this.barButtonItem1.ImageOptions.Image = global::LabMaestro.Controls.Win.ResourceIcons.demo_paste_16;
            this.barButtonItem1.ImageOptions.ImageIndex = 6;
            this.barButtonItem1.Name = "barButtonItem1";
            this.barButtonItem1.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnCopyDemographics_ItemClick);
            // 
            // barButtonItem2
            // 
            this.barButtonItem2.Caption = "&Paste Demographics";
            this.barButtonItem2.Id = 9;
            this.barButtonItem2.ImageOptions.Image = global::LabMaestro.Controls.Win.ResourceIcons.demo_copy_16_png;
            this.barButtonItem2.ImageOptions.ImageIndex = 5;
            this.barButtonItem2.Name = "barButtonItem2";
            this.barButtonItem2.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnPasteDemographics_ItemClick);
            // 
            // miAssignAssocLab
            // 
            this.miAssignAssocLab.Caption = "Assign External Lab";
            this.miAssignAssocLab.Id = 31;
            this.miAssignAssocLab.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("miAssignAssocLab.ImageOptions.Image")));
            this.miAssignAssocLab.Name = "miAssignAssocLab";
            this.miAssignAssocLab.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.miAssignAssocLab_ItemClick);
            // 
            // miInvoiceSearch
            // 
            this.miInvoiceSearch.Caption = "&Invoice Search";
            this.miInvoiceSearch.Id = 3;
            this.miInvoiceSearch.ImageOptions.ImageIndex = 8;
            this.miInvoiceSearch.Name = "miInvoiceSearch";
            this.miInvoiceSearch.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.miInvoiceSearch_ItemClick);
            // 
            // UserMenu
            // 
            this.UserMenu.Caption = "&User";
            this.UserMenu.Id = 4;
            this.UserMenu.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.miUserShiftManagement),
            new DevExpress.XtraBars.LinkPersistInfo(this.miShiftSearch),
            new DevExpress.XtraBars.LinkPersistInfo(this.miUserChangePassword, true),
            new DevExpress.XtraBars.LinkPersistInfo(this.miExit, true)});
            this.UserMenu.Name = "UserMenu";
            // 
            // miUserShiftManagement
            // 
            this.miUserShiftManagement.Caption = "&Shift Management";
            this.miUserShiftManagement.Id = 5;
            this.miUserShiftManagement.ImageOptions.ImageIndex = 7;
            this.miUserShiftManagement.Name = "miUserShiftManagement";
            this.miUserShiftManagement.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.miUserShiftManagement_ItemClick);
            // 
            // miShiftSearch
            // 
            this.miShiftSearch.Caption = "Shift History";
            this.miShiftSearch.Id = 27;
            this.miShiftSearch.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.account;
            this.miShiftSearch.Name = "miShiftSearch";
            this.miShiftSearch.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.miShiftSearch_ItemClick);
            // 
            // miUserChangePassword
            // 
            this.miUserChangePassword.Caption = "Change Password...";
            this.miUserChangePassword.Id = 6;
            this.miUserChangePassword.ImageOptions.ImageIndex = 4;
            this.miUserChangePassword.Name = "miUserChangePassword";
            // 
            // miExit
            // 
            this.miExit.Caption = "&Close";
            this.miExit.Id = 12;
            this.miExit.ImageOptions.ImageIndex = 9;
            this.miExit.Name = "miExit";
            this.miExit.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.miExit_ItemClick);
            // 
            // CatalogsMenu
            // 
            this.CatalogsMenu.Caption = "&Catalogs";
            this.CatalogsMenu.Id = 22;
            this.CatalogsMenu.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.btnRefreshSearchIndices),
            new DevExpress.XtraBars.LinkPersistInfo(this.browseTestCatalog, true),
            new DevExpress.XtraBars.LinkPersistInfo(this.miBrowsePhysicianCatalog)});
            this.CatalogsMenu.Name = "CatalogsMenu";
            // 
            // btnRefreshSearchIndices
            // 
            this.btnRefreshSearchIndices.Caption = "&Refresh Catalogs";
            this.btnRefreshSearchIndices.Id = 26;
            this.btnRefreshSearchIndices.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.reload16;
            this.btnRefreshSearchIndices.Name = "btnRefreshSearchIndices";
            this.btnRefreshSearchIndices.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnRefreshSearchIndices_ItemClick);
            // 
            // browseTestCatalog
            // 
            this.browseTestCatalog.Caption = "Browse &Test Catalog";
            this.browseTestCatalog.Id = 23;
            this.browseTestCatalog.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.catalog16;
            this.browseTestCatalog.Name = "browseTestCatalog";
            // 
            // miBrowsePhysicianCatalog
            // 
            this.miBrowsePhysicianCatalog.Caption = "Browse &Physicians Catalog";
            this.miBrowsePhysicianCatalog.Id = 24;
            this.miBrowsePhysicianCatalog.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.physician16;
            this.miBrowsePhysicianCatalog.Name = "miBrowsePhysicianCatalog";
            // 
            // barAndDockingController
            // 
            this.barAndDockingController.LookAndFeel.SkinName = "Office 2010 Blue";
            this.barAndDockingController.LookAndFeel.UseDefaultLookAndFeel = false;
            this.barAndDockingController.PropertiesBar.AllowLinkLighting = false;
            // 
            // barDockControlTop
            // 
            this.barDockControlTop.CausesValidation = false;
            this.barDockControlTop.Dock = System.Windows.Forms.DockStyle.Top;
            this.barDockControlTop.Location = new System.Drawing.Point(0, 0);
            this.barDockControlTop.Manager = this.barManager;
            this.barDockControlTop.Margin = new System.Windows.Forms.Padding(4);
            this.barDockControlTop.Size = new System.Drawing.Size(1264, 54);
            // 
            // barDockControlBottom
            // 
            this.barDockControlBottom.CausesValidation = false;
            this.barDockControlBottom.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.barDockControlBottom.Location = new System.Drawing.Point(0, 730);
            this.barDockControlBottom.Manager = this.barManager;
            this.barDockControlBottom.Margin = new System.Windows.Forms.Padding(4);
            this.barDockControlBottom.Size = new System.Drawing.Size(1264, 0);
            // 
            // barDockControlLeft
            // 
            this.barDockControlLeft.CausesValidation = false;
            this.barDockControlLeft.Dock = System.Windows.Forms.DockStyle.Left;
            this.barDockControlLeft.Location = new System.Drawing.Point(0, 54);
            this.barDockControlLeft.Manager = this.barManager;
            this.barDockControlLeft.Margin = new System.Windows.Forms.Padding(4);
            this.barDockControlLeft.Size = new System.Drawing.Size(0, 676);
            // 
            // barDockControlRight
            // 
            this.barDockControlRight.CausesValidation = false;
            this.barDockControlRight.Dock = System.Windows.Forms.DockStyle.Right;
            this.barDockControlRight.Location = new System.Drawing.Point(1264, 54);
            this.barDockControlRight.Manager = this.barManager;
            this.barDockControlRight.Margin = new System.Windows.Forms.Padding(4);
            this.barDockControlRight.Size = new System.Drawing.Size(0, 676);
            // 
            // imageList1
            // 
            this.imageList1.ImageStream = ((System.Windows.Forms.ImageListStreamer)(resources.GetObject("imageList1.ImageStream")));
            this.imageList1.TransparentColor = System.Drawing.Color.Transparent;
            this.imageList1.Images.SetKeyName(0, "address_book_blue16.png");
            this.imageList1.Images.SetKeyName(1, "address-book-plus16.png");
            this.imageList1.Images.SetKeyName(2, "briefcase16.png");
            this.imageList1.Images.SetKeyName(3, "new16.png");
            this.imageList1.Images.SetKeyName(4, "password16.png");
            this.imageList1.Images.SetKeyName(5, "tag-arrow16.png");
            this.imageList1.Images.SetKeyName(6, "tag-plus16.png");
            this.imageList1.Images.SetKeyName(7, "users16.png");
            this.imageList1.Images.SetKeyName(8, "search16.png");
            this.imageList1.Images.SetKeyName(9, "logout16.png");
            this.imageList1.Images.SetKeyName(10, "cancel16.png");
            this.imageList1.Images.SetKeyName(11, "clear16.png");
            this.imageList1.Images.SetKeyName(12, "date_next.png");
            this.imageList1.Images.SetKeyName(13, "delete16.png");
            this.imageList1.Images.SetKeyName(14, "edit16.png");
            this.imageList1.Images.SetKeyName(15, "plus16.png");
            this.imageList1.Images.SetKeyName(16, "bullet_blue16.png");
            this.imageList1.Images.SetKeyName(17, "coins.png");
            this.imageList1.Images.SetKeyName(18, "tick_16.png");
            this.imageList1.Images.SetKeyName(19, "calculator16.png");
            this.imageList1.Images.SetKeyName(20, "tick_small.png");
            // 
            // barSubItem2
            // 
            this.barSubItem2.Caption = "&New Lab Order";
            this.barSubItem2.Id = 1;
            this.barSubItem2.Name = "barSubItem2";
            // 
            // barSubItem4
            // 
            this.barSubItem4.Caption = "A&bout";
            this.barSubItem4.Id = 11;
            this.barSubItem4.Name = "barSubItem4";
            // 
            // grdOrderedTests
            // 
            this.grdOrderedTests.ClipCurrentCellSelection = false;
            this.grdOrderedTests.DataRowTemplate = this.dataRowTemplate2;
            this.grdOrderedTests.DataRowTemplateStyles.Add(this.visualGridElementStyle2);
            this.grdOrderedTests.DataRowTemplateStyles.Add(this.dataRowStyle1);
            // 
            // 
            // 
            this.grdOrderedTests.ErrorVisualStyle.OverrideUIStyle = false;
            // 
            // 
            // 
            this.grdOrderedTests.FixedColumnSplitter.AllowRepositioning = false;
            this.grdOrderedTests.FixedColumnSplitter.Visible = false;
            this.grdOrderedTests.FixedHeaderRows.Add(this.columnManagerRow2);
            this.grdOrderedTests.Font = new System.Drawing.Font("Segoe UI", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.grdOrderedTests.GridLineColor = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(239)))), ((int)(((byte)(255)))));
            this.grdOrderedTests.GridLineStyle = System.Drawing.Drawing2D.DashStyle.Dash;
            this.grdOrderedTests.InactiveSelectionBackColor = System.Drawing.Color.RoyalBlue;
            // 
            // 
            // 
            this.grdOrderedTests.InactiveSelectionVisualStyle.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(229)))), ((int)(((byte)(229)))), ((int)(((byte)(229)))));
            this.grdOrderedTests.InactiveSelectionVisualStyle.ForeColor = System.Drawing.Color.Black;
            this.grdOrderedTests.InactiveSelectionVisualStyle.OverrideUIStyle = true;
            this.grdOrderedTests.Location = new System.Drawing.Point(7, 192);
            this.grdOrderedTests.Margin = new System.Windows.Forms.Padding(4);
            this.grdOrderedTests.Name = "grdOrderedTests";
            this.grdOrderedTests.OverrideUIStyle = false;
            this.grdOrderedTests.ReadOnly = true;
            // 
            // 
            // 
            this.grdOrderedTests.RowSelectorPane.AllowRowResize = false;
            this.grdOrderedTests.RowSelectorPane.CurrentImageIndex = 16;
            gradientMap1.GradientMode = Xceed.UI.GradientMode.Horizontal;
            gradientStop1.Color = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(239)))), ((int)(((byte)(255)))));
            gradientStop1.Offset = 0D;
            gradientStop2.Color = System.Drawing.Color.FromArgb(((int)(((byte)(175)))), ((int)(((byte)(210)))), ((int)(((byte)(255)))));
            gradientStop2.Offset = 1D;
            gradientMap1.GradientStops.Add(gradientStop1);
            gradientMap1.GradientStops.Add(gradientStop2);
            this.grdOrderedTests.RowSelectorPane.GradientMap = gradientMap1;
            // 
            // 
            // 
            this.grdOrderedTests.RowSelectorPane.HotVisualStyle.OverrideUIStyle = true;
            this.grdOrderedTests.RowSelectorPane.ImageList = this.imageList1;
            this.grdOrderedTests.RowSelectorPane.OverrideUIStyle = true;
            this.grdOrderedTests.RowSelectorPane.SelectedImageIndex = 20;
            this.grdOrderedTests.RowSelectorPane.Width = 17;
            this.grdOrderedTests.SelectionBackColor = System.Drawing.Color.Indigo;
            this.grdOrderedTests.SelectionMode = System.Windows.Forms.SelectionMode.MultiExtended;
            // 
            // 
            // 
            this.grdOrderedTests.SelectionVisualStyle.BackColor = System.Drawing.Color.Indigo;
            this.grdOrderedTests.SelectionVisualStyle.Font = new System.Drawing.Font("Segoe UI", 11.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.grdOrderedTests.SelectionVisualStyle.ForeColor = System.Drawing.Color.Yellow;
            this.grdOrderedTests.SelectionVisualStyle.OverrideUIStyle = true;
            this.grdOrderedTests.ShowFocusRectangle = false;
            // 
            // 
            // 
            this.grdOrderedTests.SideMargin.Visible = false;
            this.grdOrderedTests.Size = new System.Drawing.Size(659, 451);
            this.grdOrderedTests.SynchronizeDetailGrids = false;
            this.grdOrderedTests.TabIndex = 14;
            this.grdOrderedTests.TabStop = false;
            this.grdOrderedTests.TreeLineColor = System.Drawing.Color.Silver;
            this.grdOrderedTests.UIStyle = Xceed.UI.UIStyle.System;
            this.grdOrderedTests.KeyUp += new System.Windows.Forms.KeyEventHandler(this.grdOrderedTests_KeyUp);
            // 
            // dataRowTemplate2
            // 
            this.dataRowTemplate2.Height = 28;
            this.dataRowTemplate2.MaxHeight = 40;
            this.dataRowTemplate2.OverrideUIStyle = true;
            // 
            // visualGridElementStyle2
            // 
            // 
            // 
            // 
            this.visualGridElementStyle2.HotVisualStyle.OverrideUIStyle = true;
            this.visualGridElementStyle2.OverrideUIStyle = false;
            // 
            // dataRowStyle1
            // 
            this.dataRowStyle1.BackColor = System.Drawing.Color.LightCyan;
            // 
            // columnManagerRow2
            // 
            this.columnManagerRow2.AllowColumnReorder = false;
            this.columnManagerRow2.AllowColumnResize = true;
            this.columnManagerRow2.AllowSort = false;
            this.columnManagerRow2.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            gradientStop9.Color = System.Drawing.Color.FromArgb(((int)(((byte)(252)))), ((int)(((byte)(253)))), ((int)(((byte)(255)))));
            gradientStop9.Offset = 0D;
            gradientStop10.Color = System.Drawing.Color.FromArgb(((int)(((byte)(196)))), ((int)(((byte)(221)))), ((int)(((byte)(255)))));
            gradientStop10.Offset = 1D;
            gradientMap5.GradientStops.Add(gradientStop9);
            gradientMap5.GradientStops.Add(gradientStop10);
            this.columnManagerRow2.GradientMap = gradientMap5;
            this.columnManagerRow2.Height = 25;
            // 
            // 
            // 
            this.columnManagerRow2.HotVisualStyle.OverrideUIStyle = false;
            this.columnManagerRow2.OverrideUIStyle = true;
            // 
            // grdBillableItems
            // 
            this.grdBillableItems.ClipCurrentCellSelection = false;
            this.grdBillableItems.DataRowTemplate = this.dataRowTemplate1;
            this.grdBillableItems.DataRowTemplateStyles.Add(this.visualGridElementStyle1);
            this.grdBillableItems.DataRowTemplateStyles.Add(this.dataRowStyle2);
            // 
            // 
            // 
            this.grdBillableItems.ErrorVisualStyle.OverrideUIStyle = false;
            this.grdBillableItems.FixedHeaderRows.Add(this.columnManagerRow1);
            this.grdBillableItems.Font = new System.Drawing.Font("Segoe UI", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.grdBillableItems.GridLineColor = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(239)))), ((int)(((byte)(255)))));
            // 
            // 
            // 
            this.grdBillableItems.InactiveSelectionVisualStyle.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(229)))), ((int)(((byte)(229)))), ((int)(((byte)(229)))));
            this.grdBillableItems.InactiveSelectionVisualStyle.ForeColor = System.Drawing.Color.Black;
            this.grdBillableItems.InactiveSelectionVisualStyle.OverrideUIStyle = true;
            this.grdBillableItems.Location = new System.Drawing.Point(674, 222);
            this.grdBillableItems.Margin = new System.Windows.Forms.Padding(4);
            this.grdBillableItems.Name = "grdBillableItems";
            this.grdBillableItems.OverrideUIStyle = false;
            this.grdBillableItems.ReadOnly = true;
            // 
            // 
            // 
            this.grdBillableItems.RowSelectorPane.CurrentImageIndex = 16;
            gradientMap3.GradientMode = Xceed.UI.GradientMode.Horizontal;
            gradientStop5.Color = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(239)))), ((int)(((byte)(255)))));
            gradientStop5.Offset = 0D;
            gradientStop6.Color = System.Drawing.Color.FromArgb(((int)(((byte)(175)))), ((int)(((byte)(210)))), ((int)(((byte)(255)))));
            gradientStop6.Offset = 1D;
            gradientMap3.GradientStops.Add(gradientStop5);
            gradientMap3.GradientStops.Add(gradientStop6);
            this.grdBillableItems.RowSelectorPane.GradientMap = gradientMap3;
            // 
            // 
            // 
            this.grdBillableItems.RowSelectorPane.HotVisualStyle.OverrideUIStyle = true;
            this.grdBillableItems.RowSelectorPane.ImageList = this.imageList1;
            this.grdBillableItems.RowSelectorPane.OverrideUIStyle = true;
            this.grdBillableItems.SelectionMode = System.Windows.Forms.SelectionMode.One;
            // 
            // 
            // 
            this.grdBillableItems.SelectionVisualStyle.BackColor = System.Drawing.Color.Maroon;
            this.grdBillableItems.SelectionVisualStyle.ForeColor = System.Drawing.Color.White;
            this.grdBillableItems.SelectionVisualStyle.OverrideUIStyle = true;
            this.grdBillableItems.ShowFocusRectangle = false;
            this.grdBillableItems.Size = new System.Drawing.Size(296, 421);
            this.grdBillableItems.SynchronizeDetailGrids = false;
            this.grdBillableItems.TabIndex = 999;
            this.grdBillableItems.TabStop = false;
            this.grdBillableItems.KeyUp += new System.Windows.Forms.KeyEventHandler(this.grdBillableItems_KeyUp);
            // 
            // dataRowTemplate1
            // 
            this.dataRowTemplate1.Height = 28;
            this.dataRowTemplate1.MaxHeight = 40;
            // 
            // visualGridElementStyle1
            // 
            // 
            // 
            // 
            this.visualGridElementStyle1.HotVisualStyle.OverrideUIStyle = true;
            this.visualGridElementStyle1.OverrideUIStyle = false;
            // 
            // dataRowStyle2
            // 
            this.dataRowStyle2.BackColor = System.Drawing.Color.OldLace;
            // 
            // columnManagerRow1
            // 
            this.columnManagerRow1.AllowColumnReorder = false;
            this.columnManagerRow1.AllowSort = false;
            this.columnManagerRow1.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            gradientStop3.Color = System.Drawing.Color.FromArgb(((int)(((byte)(252)))), ((int)(((byte)(253)))), ((int)(((byte)(255)))));
            gradientStop3.Offset = 0D;
            gradientStop4.Color = System.Drawing.Color.FromArgb(((int)(((byte)(196)))), ((int)(((byte)(221)))), ((int)(((byte)(255)))));
            gradientStop4.Offset = 1D;
            gradientMap2.GradientStops.Add(gradientStop3);
            gradientMap2.GradientStops.Add(gradientStop4);
            this.columnManagerRow1.GradientMap = gradientMap2;
            this.columnManagerRow1.Height = 23;
            // 
            // 
            // 
            this.columnManagerRow1.HotVisualStyle.OverrideUIStyle = false;
            this.columnManagerRow1.OverrideUIStyle = true;
            // 
            // labelControl5
            // 
            this.labelControl5.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl5.Appearance.Options.UseFont = true;
            this.labelControl5.Location = new System.Drawing.Point(546, 93);
            this.labelControl5.Margin = new System.Windows.Forms.Padding(4);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(26, 16);
            this.labelControl5.TabIndex = 24;
            this.labelControl5.Text = "Sex:";
            // 
            // rgSex
            // 
            this.rgSex.EditValue = ((byte)(10));
            this.rgSex.Location = new System.Drawing.Point(579, 90);
            this.rgSex.Margin = new System.Windows.Forms.Padding(4);
            this.rgSex.MenuManager = this.barManager;
            this.rgSex.Name = "rgSex";
            this.rgSex.Properties.Appearance.BackColor = System.Drawing.Color.Transparent;
            this.rgSex.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rgSex.Properties.Appearance.Options.UseBackColor = true;
            this.rgSex.Properties.Appearance.Options.UseFont = true;
            this.rgSex.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.rgSex.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.RadioGroupItem[] {
            new DevExpress.XtraEditors.Controls.RadioGroupItem(((byte)(10)), "&M"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(((byte)(20)), "&F"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(((byte)(0)), "&U")});
            this.rgSex.Size = new System.Drawing.Size(107, 22);
            this.rgSex.StyleController = this.styleController;
            this.rgSex.TabIndex = 4;
            // 
            // styleController
            // 
            this.styleController.LookAndFeel.SkinName = "DevExpress Style";
            this.styleController.LookAndFeel.UseDefaultLookAndFeel = false;
            // 
            // txtLastName
            // 
            this.txtLastName.Location = new System.Drawing.Point(344, 90);
            this.txtLastName.Margin = new System.Windows.Forms.Padding(4);
            this.txtLastName.MenuManager = this.barManager;
            this.txtLastName.Name = "txtLastName";
            this.txtLastName.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtLastName.Properties.Appearance.ForeColor = System.Drawing.Color.Black;
            this.txtLastName.Properties.Appearance.Options.UseFont = true;
            this.txtLastName.Properties.Appearance.Options.UseForeColor = true;
            this.txtLastName.Size = new System.Drawing.Size(152, 22);
            this.txtLastName.StyleController = this.styleController;
            this.txtLastName.TabIndex = 3;
            this.txtLastName.Enter += new System.EventHandler(this.txtFirstName_Enter);
            this.txtLastName.KeyUp += new System.Windows.Forms.KeyEventHandler(this.PatientName_KeyUp);
            this.txtLastName.Leave += new System.EventHandler(this.txtFirstName_Leave);
            // 
            // txtFirstName
            // 
            this.txtFirstName.Location = new System.Drawing.Point(129, 90);
            this.txtFirstName.Margin = new System.Windows.Forms.Padding(4);
            this.txtFirstName.MenuManager = this.barManager;
            this.txtFirstName.Name = "txtFirstName";
            this.txtFirstName.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtFirstName.Properties.Appearance.ForeColor = System.Drawing.Color.Black;
            this.txtFirstName.Properties.Appearance.Options.UseFont = true;
            this.txtFirstName.Properties.Appearance.Options.UseForeColor = true;
            this.txtFirstName.Size = new System.Drawing.Size(207, 22);
            this.txtFirstName.StyleController = this.styleController;
            this.txtFirstName.TabIndex = 2;
            this.txtFirstName.ToolTip = "F5 - নাম, লিঙ্গ, বয়স অটো-ফরম্যাট\r\nF9 - নতুন রোগীর মোবাইল এ্যাপ নিবন্ধন\r\nF8 - পূর্" +
    "বের রোগীর লিস্ট";
            this.txtFirstName.Enter += new System.EventHandler(this.txtFirstName_Enter);
            this.txtFirstName.KeyUp += new System.Windows.Forms.KeyEventHandler(this.PatientName_KeyUp);
            this.txtFirstName.Leave += new System.EventHandler(this.txtFirstName_Leave);
            // 
            // cboPatientTitle
            // 
            this.cboPatientTitle.Location = new System.Drawing.Point(75, 90);
            this.cboPatientTitle.Margin = new System.Windows.Forms.Padding(4);
            this.cboPatientTitle.MenuManager = this.barManager;
            this.cboPatientTitle.Name = "cboPatientTitle";
            this.cboPatientTitle.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cboPatientTitle.Properties.Appearance.ForeColor = System.Drawing.Color.Black;
            this.cboPatientTitle.Properties.Appearance.Options.UseFont = true;
            this.cboPatientTitle.Properties.Appearance.Options.UseForeColor = true;
            this.cboPatientTitle.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cboPatientTitle.Properties.Items.AddRange(new object[] {
            "Mr.",
            "Mrs.",
            "Ms.",
            "Master",
            "Dr."});
            this.cboPatientTitle.Size = new System.Drawing.Size(46, 22);
            this.cboPatientTitle.StyleController = this.styleController;
            this.cboPatientTitle.TabIndex = 1;
            this.cboPatientTitle.ToolTip = "Title";
            this.cboPatientTitle.Enter += new System.EventHandler(this.cboPatientTitle_Enter);
            this.cboPatientTitle.Leave += new System.EventHandler(this.cboPatientTitle_Leave);
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Location = new System.Drawing.Point(7, 93);
            this.labelControl1.Margin = new System.Windows.Forms.Padding(4);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(38, 16);
            this.labelControl1.TabIndex = 19;
            this.labelControl1.Text = "Name:";
            // 
            // labelControl3
            // 
            this.labelControl3.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl3.Appearance.Options.UseFont = true;
            this.labelControl3.Location = new System.Drawing.Point(810, 92);
            this.labelControl3.Margin = new System.Windows.Forms.Padding(4);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(27, 16);
            this.labelControl3.TabIndex = 28;
            this.labelControl3.Text = "DoB:";
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(694, 92);
            this.labelControl2.Margin = new System.Windows.Forms.Padding(4);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(27, 16);
            this.labelControl2.TabIndex = 27;
            this.labelControl2.Text = "Age:";
            // 
            // dteDateOfBirth
            // 
            this.dteDateOfBirth.EditValue = null;
            this.dteDateOfBirth.Location = new System.Drawing.Point(856, 90);
            this.dteDateOfBirth.Margin = new System.Windows.Forms.Padding(4);
            this.dteDateOfBirth.MenuManager = this.barManager;
            this.dteDateOfBirth.Name = "dteDateOfBirth";
            this.dteDateOfBirth.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.dteDateOfBirth.Properties.Appearance.ForeColor = System.Drawing.Color.Black;
            this.dteDateOfBirth.Properties.Appearance.Options.UseFont = true;
            this.dteDateOfBirth.Properties.Appearance.Options.UseForeColor = true;
            editorButtonImageOptions1.Image = ((System.Drawing.Image)(resources.GetObject("editorButtonImageOptions1.Image")));
            this.dteDateOfBirth.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph, "", -1, true, true, false, editorButtonImageOptions1, new DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.None), serializableAppearanceObject1, serializableAppearanceObject2, serializableAppearanceObject3, serializableAppearanceObject4, "", null, null, DevExpress.Utils.ToolTipAnchor.Default)});
            this.dteDateOfBirth.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.dteDateOfBirth.Properties.DisplayFormat.FormatString = "d/M/yyyy";
            this.dteDateOfBirth.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.dteDateOfBirth.Properties.EditFormat.FormatString = "d/M/yyyy";
            this.dteDateOfBirth.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.dteDateOfBirth.Properties.MaskSettings.Set("mask", "d/M/yyyy");
            this.dteDateOfBirth.Size = new System.Drawing.Size(114, 22);
            this.dteDateOfBirth.StyleController = this.styleController;
            this.dteDateOfBirth.TabIndex = 6;
            this.dteDateOfBirth.TextChanged += new System.EventHandler(this.dtDateOfBirth_TextChanged);
            this.dteDateOfBirth.Enter += new System.EventHandler(this.dteDateOfBirth_Enter);
            this.dteDateOfBirth.KeyUp += new System.Windows.Forms.KeyEventHandler(this.dtDateOfBirth_KeyUp);
            this.dteDateOfBirth.Leave += new System.EventHandler(this.dteDateOfBirth_Leave);
            // 
            // labelControl8
            // 
            this.labelControl8.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl8.Appearance.Options.UseFont = true;
            this.labelControl8.Location = new System.Drawing.Point(7, 125);
            this.labelControl8.Margin = new System.Windows.Forms.Padding(4);
            this.labelControl8.Name = "labelControl8";
            this.labelControl8.Size = new System.Drawing.Size(57, 16);
            this.labelControl8.TabIndex = 35;
            this.labelControl8.Text = "Physician:";
            // 
            // cboPhysician
            // 
            this.cboPhysician.AutoComplete = false;
            this.cboPhysician.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(234)))), ((int)(((byte)(242)))), ((int)(((byte)(251)))));
            this.cboPhysician.BeforeTouchSize = new System.Drawing.Size(382, 24);
            this.cboPhysician.DropDownWidth = 680;
            this.cboPhysician.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cboPhysician.ListControl = this.grdListPhysician;
            this.cboPhysician.Location = new System.Drawing.Point(75, 124);
            this.cboPhysician.MaxLength = 128;
            this.cboPhysician.Name = "cboPhysician";
            this.cboPhysician.Size = new System.Drawing.Size(382, 24);
            this.cboPhysician.Style = Syncfusion.Windows.Forms.VisualStyle.Office2007;
            this.cboPhysician.TabIndex = 7;
            this.cboPhysician.Tag = "0";
            this.cboPhysician.UseOffice2007ColorsInActiveMode = true;
            this.cboPhysician.DropDown += new System.EventHandler(this.cboPhysician_DropDown);
            this.cboPhysician.TextChanged += new System.EventHandler(this.cboPhysician_TextChanged);
            this.cboPhysician.Enter += new System.EventHandler(this.cboPhysician_Enter);
            this.cboPhysician.KeyUp += new System.Windows.Forms.KeyEventHandler(this.cboPhysician_KeyUp);
            this.cboPhysician.Leave += new System.EventHandler(this.cboPhysician_Leave);
            // 
            // grdListPhysician
            // 
            this.grdListPhysician.AllowResizeColumns = false;
            this.grdListPhysician.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(234)))), ((int)(((byte)(242)))), ((int)(((byte)(251)))));
            this.grdListPhysician.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            this.grdListPhysician.GridVisualStyles = Syncfusion.Windows.Forms.GridVisualStyles.Office2010Blue;
            this.grdListPhysician.ItemHeight = 26;
            this.grdListPhysician.Location = new System.Drawing.Point(120, 353);
            this.grdListPhysician.MultiColumn = true;
            this.grdListPhysician.Name = "grdListPhysician";
            this.grdListPhysician.Properties.BackgroundColor = System.Drawing.SystemColors.Window;
            this.grdListPhysician.Properties.DisplayVertLines = false;
            this.grdListPhysician.SelectedIndex = -1;
            this.grdListPhysician.Size = new System.Drawing.Size(380, 255);
            this.grdListPhysician.SupportsTransparentBackColor = true;
            this.grdListPhysician.TabIndex = 44;
            this.grdListPhysician.TabStop = false;
            this.grdListPhysician.Tag = "200";
            this.grdListPhysician.ThemeName = "Office2010Blue";
            this.grdListPhysician.ThemesEnabled = true;
            this.grdListPhysician.TopIndex = 0;
            // 
            // cboLabTest
            // 
            this.cboLabTest.AutoComplete = false;
            this.cboLabTest.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(237)))), ((int)(((byte)(245)))), ((int)(((byte)(253)))));
            this.cboLabTest.BeforeTouchSize = new System.Drawing.Size(510, 24);
            this.cboLabTest.DropDownWidth = 400;
            this.cboLabTest.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cboLabTest.ListControl = this.grdListLabTest;
            this.cboLabTest.Location = new System.Drawing.Point(75, 155);
            this.cboLabTest.MaxLength = 128;
            this.cboLabTest.Name = "cboLabTest";
            this.cboLabTest.Size = new System.Drawing.Size(510, 24);
            this.cboLabTest.Style = Syncfusion.Windows.Forms.VisualStyle.Office2010;
            this.cboLabTest.TabIndex = 11;
            this.cboLabTest.Tag = "1";
            this.cboLabTest.UseOffice2007ColorsInActiveMode = true;
            this.cboLabTest.DropDown += new System.EventHandler(this.cboLabTest_DropDown);
            this.cboLabTest.TextChanged += new System.EventHandler(this.cboLabTest_TextChanged);
            this.cboLabTest.Enter += new System.EventHandler(this.cboPhysician_Enter);
            this.cboLabTest.KeyUp += new System.Windows.Forms.KeyEventHandler(this.cboLabTest_KeyUp);
            this.cboLabTest.Leave += new System.EventHandler(this.cboPhysician_Leave);
            // 
            // grdListLabTest
            // 
            this.grdListLabTest.AllowResizeColumns = false;
            this.grdListLabTest.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(234)))), ((int)(((byte)(242)))), ((int)(((byte)(251)))));
            this.grdListLabTest.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            this.grdListLabTest.GridVisualStyles = Syncfusion.Windows.Forms.GridVisualStyles.Office2010Blue;
            this.grdListLabTest.ItemHeight = 26;
            this.grdListLabTest.Location = new System.Drawing.Point(12, 305);
            this.grdListLabTest.MultiColumn = true;
            this.grdListLabTest.Name = "grdListLabTest";
            this.grdListLabTest.Properties.BackgroundColor = System.Drawing.SystemColors.Window;
            this.grdListLabTest.Properties.DisplayVertLines = false;
            this.grdListLabTest.SelectedIndex = -1;
            this.grdListLabTest.Size = new System.Drawing.Size(380, 255);
            this.grdListLabTest.SupportsTransparentBackColor = true;
            this.grdListLabTest.TabIndex = 45;
            this.grdListLabTest.TabStop = false;
            this.grdListLabTest.Tag = "100";
            this.grdListLabTest.ThemeName = "Office2010Blue";
            this.grdListLabTest.ThemesEnabled = true;
            this.grdListLabTest.TopIndex = 0;
            // 
            // txtPhysicianCustomName
            // 
            this.txtPhysicianCustomName.Location = new System.Drawing.Point(686, 124);
            this.txtPhysicianCustomName.Margin = new System.Windows.Forms.Padding(4);
            this.txtPhysicianCustomName.MenuManager = this.barManager;
            this.txtPhysicianCustomName.Name = "txtPhysicianCustomName";
            this.txtPhysicianCustomName.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtPhysicianCustomName.Properties.Appearance.ForeColor = System.Drawing.Color.Black;
            this.txtPhysicianCustomName.Properties.Appearance.Options.UseFont = true;
            this.txtPhysicianCustomName.Properties.Appearance.Options.UseForeColor = true;
            this.txtPhysicianCustomName.Size = new System.Drawing.Size(284, 22);
            this.txtPhysicianCustomName.StyleController = this.styleController;
            this.txtPhysicianCustomName.TabIndex = 10;
            this.txtPhysicianCustomName.ToolTip = "Enter the name for the unknown physician";
            this.txtPhysicianCustomName.Enter += new System.EventHandler(this.txtFirstName_Enter);
            this.txtPhysicianCustomName.KeyUp += new System.Windows.Forms.KeyEventHandler(this.txtPhysicianCustomName_KeyUp);
            this.txtPhysicianCustomName.Leave += new System.EventHandler(this.txtFirstName_Leave);
            // 
            // txtOrderNotes
            // 
            this.txtOrderNotes.Location = new System.Drawing.Point(686, 157);
            this.txtOrderNotes.Margin = new System.Windows.Forms.Padding(4);
            this.txtOrderNotes.MenuManager = this.barManager;
            this.txtOrderNotes.Name = "txtOrderNotes";
            this.txtOrderNotes.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtOrderNotes.Properties.Appearance.ForeColor = System.Drawing.Color.Black;
            this.txtOrderNotes.Properties.Appearance.Options.UseFont = true;
            this.txtOrderNotes.Properties.Appearance.Options.UseForeColor = true;
            this.txtOrderNotes.Size = new System.Drawing.Size(284, 22);
            this.txtOrderNotes.StyleController = this.styleController;
            this.txtOrderNotes.TabIndex = 12;
            this.txtOrderNotes.ToolTip = "Order Notes";
            this.txtOrderNotes.Enter += new System.EventHandler(this.txtFirstName_Enter);
            this.txtOrderNotes.Leave += new System.EventHandler(this.txtFirstName_Leave);
            // 
            // labelControl9
            // 
            this.labelControl9.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl9.Appearance.Options.UseFont = true;
            this.labelControl9.Location = new System.Drawing.Point(7, 159);
            this.labelControl9.Name = "labelControl9";
            this.labelControl9.Size = new System.Drawing.Size(54, 16);
            this.labelControl9.TabIndex = 40;
            this.labelControl9.Text = "Lab Test:";
            // 
            // lblNote
            // 
            this.lblNote.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblNote.Appearance.Options.UseFont = true;
            this.lblNote.Location = new System.Drawing.Point(604, 159);
            this.lblNote.Margin = new System.Windows.Forms.Padding(4);
            this.lblNote.Name = "lblNote";
            this.lblNote.Size = new System.Drawing.Size(74, 16);
            this.lblNote.TabIndex = 42;
            this.lblNote.Text = "Order Notes:";
            // 
            // lblTelephone
            // 
            this.lblTelephone.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblTelephone.Appearance.Options.UseFont = true;
            this.lblTelephone.Location = new System.Drawing.Point(7, 63);
            this.lblTelephone.Margin = new System.Windows.Forms.Padding(4);
            this.lblTelephone.Name = "lblTelephone";
            this.lblTelephone.Size = new System.Drawing.Size(40, 16);
            this.lblTelephone.TabIndex = 43;
            this.lblTelephone.Text = "Phone:";
            // 
            // panelControl1
            // 
            this.panelControl1.Appearance.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(224)))), ((int)(((byte)(192)))));
            this.panelControl1.Appearance.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.panelControl1.Appearance.Options.UseBackColor = true;
            this.panelControl1.Appearance.Options.UseFont = true;
            this.panelControl1.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.panelControl1.Controls.Add(this.lblGrossPayable);
            this.panelControl1.Controls.Add(this.labelControl13);
            this.panelControl1.Location = new System.Drawing.Point(72, 650);
            this.panelControl1.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Flat;
            this.panelControl1.LookAndFeel.UseDefaultLookAndFeel = false;
            this.panelControl1.Name = "panelControl1";
            this.panelControl1.Size = new System.Drawing.Size(135, 72);
            this.panelControl1.TabIndex = 46;
            // 
            // lblGrossPayable
            // 
            this.lblGrossPayable.Appearance.Font = new System.Drawing.Font("Verdana", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblGrossPayable.Appearance.ForeColor = System.Drawing.Color.RoyalBlue;
            this.lblGrossPayable.Appearance.Options.UseFont = true;
            this.lblGrossPayable.Appearance.Options.UseForeColor = true;
            this.lblGrossPayable.Appearance.Options.UseTextOptions = true;
            this.lblGrossPayable.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lblGrossPayable.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None;
            this.lblGrossPayable.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.lblGrossPayable.LineOrientation = DevExpress.XtraEditors.LabelLineOrientation.Horizontal;
            this.lblGrossPayable.Location = new System.Drawing.Point(5, 33);
            this.lblGrossPayable.Name = "lblGrossPayable";
            this.lblGrossPayable.Size = new System.Drawing.Size(125, 24);
            this.lblGrossPayable.TabIndex = 1;
            this.lblGrossPayable.Text = "99,999/-";
            this.lblGrossPayable.ToolTip = "Gross bill (before deductions)";
            // 
            // labelControl13
            // 
            this.labelControl13.Appearance.Font = new System.Drawing.Font("Tahoma", 11.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl13.Appearance.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(64)))), ((int)(((byte)(0)))));
            this.labelControl13.Appearance.Options.UseFont = true;
            this.labelControl13.Appearance.Options.UseForeColor = true;
            this.labelControl13.Appearance.Options.UseTextOptions = true;
            this.labelControl13.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.labelControl13.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None;
            this.labelControl13.Location = new System.Drawing.Point(5, 4);
            this.labelControl13.Name = "labelControl13";
            this.labelControl13.Size = new System.Drawing.Size(125, 23);
            this.labelControl13.TabIndex = 0;
            this.labelControl13.Text = "Gross Payable";
            this.labelControl13.ToolTip = "Gross bill (before deductions)";
            // 
            // panelControl2
            // 
            this.panelControl2.Appearance.BackColor = System.Drawing.Color.PaleGreen;
            this.panelControl2.Appearance.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.panelControl2.Appearance.Options.UseBackColor = true;
            this.panelControl2.Appearance.Options.UseFont = true;
            this.panelControl2.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.panelControl2.Controls.Add(this.lblNumTests);
            this.panelControl2.Controls.Add(this.labelControl18);
            this.panelControl2.Location = new System.Drawing.Point(9, 650);
            this.panelControl2.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Flat;
            this.panelControl2.LookAndFeel.UseDefaultLookAndFeel = false;
            this.panelControl2.Name = "panelControl2";
            this.panelControl2.Size = new System.Drawing.Size(53, 72);
            this.panelControl2.TabIndex = 49;
            // 
            // lblNumTests
            // 
            this.lblNumTests.Appearance.Font = new System.Drawing.Font("Verdana", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblNumTests.Appearance.ForeColor = System.Drawing.Color.Firebrick;
            this.lblNumTests.Appearance.Options.UseFont = true;
            this.lblNumTests.Appearance.Options.UseForeColor = true;
            this.lblNumTests.Appearance.Options.UseTextOptions = true;
            this.lblNumTests.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lblNumTests.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None;
            this.lblNumTests.Location = new System.Drawing.Point(0, 33);
            this.lblNumTests.Name = "lblNumTests";
            this.lblNumTests.Size = new System.Drawing.Size(54, 24);
            this.lblNumTests.TabIndex = 1;
            this.lblNumTests.Text = "99";
            this.lblNumTests.ToolTip = "Number of service(s)";
            // 
            // labelControl18
            // 
            this.labelControl18.Appearance.Font = new System.Drawing.Font("Tahoma", 11.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl18.Appearance.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(64)))), ((int)(((byte)(0)))));
            this.labelControl18.Appearance.Options.UseFont = true;
            this.labelControl18.Appearance.Options.UseForeColor = true;
            this.labelControl18.Appearance.Options.UseTextOptions = true;
            this.labelControl18.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.labelControl18.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None;
            this.labelControl18.LineLocation = DevExpress.XtraEditors.LineLocation.Center;
            this.labelControl18.Location = new System.Drawing.Point(5, 3);
            this.labelControl18.Name = "labelControl18";
            this.labelControl18.Size = new System.Drawing.Size(43, 18);
            this.labelControl18.TabIndex = 0;
            this.labelControl18.Text = "#";
            this.labelControl18.ToolTip = "Number of service(s)";
            // 
            // btnPayment
            // 
            this.btnPayment.Appearance.Font = new System.Drawing.Font("Tahoma", 18F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnPayment.Appearance.ForeColor = System.Drawing.Color.Crimson;
            this.btnPayment.Appearance.Options.UseFont = true;
            this.btnPayment.Appearance.Options.UseForeColor = true;
            this.btnPayment.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.cash;
            this.btnPayment.ImageOptions.ImageIndex = 17;
            this.btnPayment.Location = new System.Drawing.Point(980, 650);
            this.btnPayment.Name = "btnPayment";
            this.btnPayment.Size = new System.Drawing.Size(273, 72);
            this.btnPayment.StyleController = this.styleController;
            this.btnPayment.TabIndex = 19;
            this.btnPayment.Text = "Payment";
            this.btnPayment.Click += new System.EventHandler(this.btnPayment_Click);
            this.btnPayment.KeyDown += new System.Windows.Forms.KeyEventHandler(this.btnPayment_KeyDown);
            // 
            // txtDiscountPercent
            // 
            this.txtDiscountPercent.Location = new System.Drawing.Point(400, 672);
            this.txtDiscountPercent.Margin = new System.Windows.Forms.Padding(4);
            this.txtDiscountPercent.MenuManager = this.barManager;
            this.txtDiscountPercent.Name = "txtDiscountPercent";
            this.txtDiscountPercent.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtDiscountPercent.Properties.Appearance.Options.UseFont = true;
            this.txtDiscountPercent.Properties.MaskSettings.Set("MaskManagerType", typeof(DevExpress.Data.Mask.NumericMaskManager));
            this.txtDiscountPercent.Properties.MaskSettings.Set("mask", "N0");
            this.txtDiscountPercent.Size = new System.Drawing.Size(41, 22);
            this.txtDiscountPercent.StyleController = this.styleController;
            this.txtDiscountPercent.TabIndex = 14;
            this.txtDiscountPercent.Enter += new System.EventHandler(this.txtFirstName_Enter);
            this.txtDiscountPercent.KeyUp += new System.Windows.Forms.KeyEventHandler(this.txtDiscountPercent_KeyUp);
            this.txtDiscountPercent.Leave += new System.EventHandler(this.txtFirstName_Leave);
            // 
            // labelControl14
            // 
            this.labelControl14.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl14.Appearance.Options.UseFont = true;
            this.labelControl14.Location = new System.Drawing.Point(323, 675);
            this.labelControl14.Margin = new System.Windows.Forms.Padding(4);
            this.labelControl14.Name = "labelControl14";
            this.labelControl14.Size = new System.Drawing.Size(69, 16);
            this.labelControl14.TabIndex = 60;
            this.labelControl14.Text = "Percentage:";
            // 
            // labelControl15
            // 
            this.labelControl15.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl15.Appearance.Options.UseFont = true;
            this.labelControl15.Location = new System.Drawing.Point(581, 674);
            this.labelControl15.Margin = new System.Windows.Forms.Padding(4);
            this.labelControl15.Name = "labelControl15";
            this.labelControl15.Size = new System.Drawing.Size(71, 16);
            this.labelControl15.TabIndex = 66;
            this.labelControl15.Text = "Amount in ৳:";
            // 
            // labelControl16
            // 
            this.labelControl16.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl16.Appearance.Options.UseFont = true;
            this.labelControl16.Location = new System.Drawing.Point(323, 704);
            this.labelControl16.Margin = new System.Windows.Forms.Padding(4);
            this.labelControl16.Name = "labelControl16";
            this.labelControl16.Size = new System.Drawing.Size(31, 16);
            this.labelControl16.TabIndex = 68;
            this.labelControl16.Text = "Note:";
            // 
            // panelControl3
            // 
            this.panelControl3.Appearance.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.panelControl3.Appearance.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.panelControl3.Appearance.Options.UseBackColor = true;
            this.panelControl3.Appearance.Options.UseFont = true;
            this.panelControl3.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.panelControl3.Controls.Add(this.lblNetPayable);
            this.panelControl3.Controls.Add(this.labelControl19);
            this.panelControl3.Location = new System.Drawing.Point(830, 650);
            this.panelControl3.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Flat;
            this.panelControl3.LookAndFeel.UseDefaultLookAndFeel = false;
            this.panelControl3.Name = "panelControl3";
            this.panelControl3.Size = new System.Drawing.Size(142, 72);
            this.panelControl3.TabIndex = 69;
            // 
            // lblNetPayable
            // 
            this.lblNetPayable.Appearance.Font = new System.Drawing.Font("Verdana", 14.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblNetPayable.Appearance.ForeColor = System.Drawing.Color.Crimson;
            this.lblNetPayable.Appearance.Options.UseFont = true;
            this.lblNetPayable.Appearance.Options.UseForeColor = true;
            this.lblNetPayable.Appearance.Options.UseTextOptions = true;
            this.lblNetPayable.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lblNetPayable.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None;
            this.lblNetPayable.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.lblNetPayable.LineOrientation = DevExpress.XtraEditors.LabelLineOrientation.Horizontal;
            this.lblNetPayable.Location = new System.Drawing.Point(3, 33);
            this.lblNetPayable.Name = "lblNetPayable";
            this.lblNetPayable.Size = new System.Drawing.Size(137, 24);
            this.lblNetPayable.TabIndex = 1;
            this.lblNetPayable.Text = "29,999/-";
            this.lblNetPayable.ToolTip = "Net Bill (after deductions)";
            // 
            // labelControl19
            // 
            this.labelControl19.Appearance.Font = new System.Drawing.Font("Tahoma", 11.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl19.Appearance.ForeColor = System.Drawing.Color.Navy;
            this.labelControl19.Appearance.Options.UseFont = true;
            this.labelControl19.Appearance.Options.UseForeColor = true;
            this.labelControl19.Appearance.Options.UseTextOptions = true;
            this.labelControl19.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.labelControl19.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None;
            this.labelControl19.Location = new System.Drawing.Point(1, 5);
            this.labelControl19.Name = "labelControl19";
            this.labelControl19.Size = new System.Drawing.Size(139, 16);
            this.labelControl19.TabIndex = 0;
            this.labelControl19.Text = "Net Payable";
            this.labelControl19.ToolTip = "Net Bill (after deductions)";
            // 
            // btnSetDiscountPercent
            // 
            this.btnSetDiscountPercent.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnSetDiscountPercent.Appearance.Options.UseFont = true;
            this.btnSetDiscountPercent.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnSetDiscountPercent.ImageOptions.Image")));
            this.btnSetDiscountPercent.ImageOptions.ImageIndex = 18;
            this.btnSetDiscountPercent.Location = new System.Drawing.Point(448, 672);
            this.btnSetDiscountPercent.Name = "btnSetDiscountPercent";
            this.btnSetDiscountPercent.Size = new System.Drawing.Size(60, 23);
            this.btnSetDiscountPercent.StyleController = this.styleController;
            this.btnSetDiscountPercent.TabIndex = 15;
            this.btnSetDiscountPercent.Text = "Set";
            this.btnSetDiscountPercent.ToolTip = "Apply Discount Percentage";
            this.btnSetDiscountPercent.Click += new System.EventHandler(this.btnSetDiscountPercent_Click);
            // 
            // btnApplyDiscount
            // 
            this.btnApplyDiscount.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnApplyDiscount.Appearance.Options.UseFont = true;
            this.btnApplyDiscount.ImageOptions.ImageIndex = 18;
            this.btnApplyDiscount.ImageOptions.ImageList = this.imageList1;
            this.btnApplyDiscount.Location = new System.Drawing.Point(632, 700);
            this.btnApplyDiscount.Name = "btnApplyDiscount";
            this.btnApplyDiscount.Size = new System.Drawing.Size(122, 23);
            this.btnApplyDiscount.StyleController = this.styleController;
            this.btnApplyDiscount.TabIndex = 18;
            this.btnApplyDiscount.Text = "Apply Discount";
            this.btnApplyDiscount.Click += new System.EventHandler(this.btnApplyDiscount_Click);
            // 
            // btnBIAdd
            // 
            this.btnBIAdd.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnBIAdd.Appearance.Options.UseFont = true;
            this.btnBIAdd.ImageOptions.ImageIndex = 15;
            this.btnBIAdd.ImageOptions.ImageList = this.imageList1;
            this.btnBIAdd.Location = new System.Drawing.Point(674, 192);
            this.btnBIAdd.Name = "btnBIAdd";
            this.btnBIAdd.Size = new System.Drawing.Size(76, 23);
            this.btnBIAdd.StyleController = this.styleController;
            this.btnBIAdd.TabIndex = 74;
            this.btnBIAdd.TabStop = false;
            this.btnBIAdd.Text = "Add";
            this.btnBIAdd.ToolTip = "Add new billable item";
            this.btnBIAdd.Click += new System.EventHandler(this.btnBIAdd_Click);
            // 
            // btnBIModify
            // 
            this.btnBIModify.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnBIModify.Appearance.Options.UseFont = true;
            this.btnBIModify.ImageOptions.ImageIndex = 14;
            this.btnBIModify.ImageOptions.ImageList = this.imageList1;
            this.btnBIModify.Location = new System.Drawing.Point(751, 192);
            this.btnBIModify.Name = "btnBIModify";
            this.btnBIModify.Size = new System.Drawing.Size(76, 23);
            this.btnBIModify.StyleController = this.styleController;
            this.btnBIModify.TabIndex = 75;
            this.btnBIModify.TabStop = false;
            this.btnBIModify.Text = "Modify";
            this.btnBIModify.ToolTip = "Change quantity of selected billable item";
            this.btnBIModify.Click += new System.EventHandler(this.btnBIModify_Click);
            // 
            // btnBIDelete
            // 
            this.btnBIDelete.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnBIDelete.Appearance.Options.UseFont = true;
            this.btnBIDelete.ImageOptions.ImageIndex = 13;
            this.btnBIDelete.ImageOptions.ImageList = this.imageList1;
            this.btnBIDelete.Location = new System.Drawing.Point(828, 192);
            this.btnBIDelete.Name = "btnBIDelete";
            this.btnBIDelete.Size = new System.Drawing.Size(71, 23);
            this.btnBIDelete.StyleController = this.styleController;
            this.btnBIDelete.TabIndex = 76;
            this.btnBIDelete.TabStop = false;
            this.btnBIDelete.Text = "Delete";
            this.btnBIDelete.ToolTip = "Delete selected billable item";
            this.btnBIDelete.Click += new System.EventHandler(this.btnBIDelete_Click);
            // 
            // txtDiscountAmount
            // 
            this.txtDiscountAmount.Location = new System.Drawing.Point(659, 672);
            this.txtDiscountAmount.MenuManager = this.barManager;
            this.txtDiscountAmount.Name = "txtDiscountAmount";
            this.txtDiscountAmount.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtDiscountAmount.Properties.Appearance.Options.UseFont = true;
            editorButtonImageOptions3.Image = ((System.Drawing.Image)(resources.GetObject("editorButtonImageOptions3.Image")));
            this.txtDiscountAmount.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph, "", -1, true, true, false, editorButtonImageOptions3, new DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.None), serializableAppearanceObject9, serializableAppearanceObject10, serializableAppearanceObject11, serializableAppearanceObject12, "", null, null, DevExpress.Utils.ToolTipAnchor.Default)});
            this.txtDiscountAmount.Properties.MaskSettings.Set("mask", "n0");
            this.txtDiscountAmount.Size = new System.Drawing.Size(95, 22);
            this.txtDiscountAmount.StyleController = this.styleController;
            this.txtDiscountAmount.TabIndex = 16;
            this.txtDiscountAmount.Enter += new System.EventHandler(this.txtDiscountAmount_Enter);
            this.txtDiscountAmount.KeyUp += new System.Windows.Forms.KeyEventHandler(this.txtDiscountAmount_KeyUp);
            this.txtDiscountAmount.Leave += new System.EventHandler(this.txtDiscountAmount_Leave);
            // 
            // chkUnknownPhysician
            // 
            this.chkUnknownPhysician.Location = new System.Drawing.Point(553, 126);
            this.chkUnknownPhysician.MenuManager = this.barManager;
            this.chkUnknownPhysician.Name = "chkUnknownPhysician";
            this.chkUnknownPhysician.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkUnknownPhysician.Properties.Appearance.Options.UseFont = true;
            this.chkUnknownPhysician.Properties.AutoWidth = true;
            this.chkUnknownPhysician.Properties.Caption = "&Unknown Referrer";
            this.chkUnknownPhysician.Size = new System.Drawing.Size(126, 20);
            this.chkUnknownPhysician.StyleController = this.styleController;
            this.chkUnknownPhysician.TabIndex = 9;
            this.chkUnknownPhysician.ToolTip = "Physician is not found in the database";
            this.chkUnknownPhysician.CheckedChanged += new System.EventHandler(this.chkUnknownPhysician_CheckedChanged);
            // 
            // panelControl4
            // 
            this.panelControl4.Appearance.BackColor = System.Drawing.Color.Gainsboro;
            this.panelControl4.Appearance.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.panelControl4.Appearance.Options.UseBackColor = true;
            this.panelControl4.Appearance.Options.UseFont = true;
            this.panelControl4.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.panelControl4.Controls.Add(this.lblMaxDiscountAmount);
            this.panelControl4.Controls.Add(this.lblMaxDiscountPercent);
            this.panelControl4.Location = new System.Drawing.Point(213, 650);
            this.panelControl4.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Flat;
            this.panelControl4.LookAndFeel.UseDefaultLookAndFeel = false;
            this.panelControl4.Name = "panelControl4";
            this.panelControl4.Size = new System.Drawing.Size(104, 72);
            this.panelControl4.TabIndex = 97;
            // 
            // lblMaxDiscountAmount
            // 
            this.lblMaxDiscountAmount.Appearance.Font = new System.Drawing.Font("Verdana", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblMaxDiscountAmount.Appearance.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblMaxDiscountAmount.Appearance.Options.UseFont = true;
            this.lblMaxDiscountAmount.Appearance.Options.UseForeColor = true;
            this.lblMaxDiscountAmount.Appearance.Options.UseTextOptions = true;
            this.lblMaxDiscountAmount.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lblMaxDiscountAmount.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None;
            this.lblMaxDiscountAmount.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.lblMaxDiscountAmount.LineOrientation = DevExpress.XtraEditors.LabelLineOrientation.Horizontal;
            this.lblMaxDiscountAmount.Location = new System.Drawing.Point(5, 33);
            this.lblMaxDiscountAmount.Name = "lblMaxDiscountAmount";
            this.lblMaxDiscountAmount.Size = new System.Drawing.Size(94, 24);
            this.lblMaxDiscountAmount.TabIndex = 1;
            this.lblMaxDiscountAmount.Text = "29,999/-";
            this.lblMaxDiscountAmount.ToolTip = "Maximum discount amount";
            // 
            // lblMaxDiscountPercent
            // 
            this.lblMaxDiscountPercent.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblMaxDiscountPercent.Appearance.ForeColor = System.Drawing.Color.Gray;
            this.lblMaxDiscountPercent.Appearance.Options.UseFont = true;
            this.lblMaxDiscountPercent.Appearance.Options.UseForeColor = true;
            this.lblMaxDiscountPercent.Appearance.Options.UseTextOptions = true;
            this.lblMaxDiscountPercent.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lblMaxDiscountPercent.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None;
            this.lblMaxDiscountPercent.Location = new System.Drawing.Point(2, 5);
            this.lblMaxDiscountPercent.Name = "lblMaxDiscountPercent";
            this.lblMaxDiscountPercent.Size = new System.Drawing.Size(97, 22);
            this.lblMaxDiscountPercent.TabIndex = 0;
            this.lblMaxDiscountPercent.Text = "Max Discount";
            this.lblMaxDiscountPercent.ToolTip = "Maximum discount percentage";
            // 
            // txtAge
            // 
            this.txtAge.Location = new System.Drawing.Point(728, 90);
            this.txtAge.MenuManager = this.barManager;
            this.txtAge.Name = "txtAge";
            this.txtAge.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtAge.Properties.Appearance.ForeColor = System.Drawing.Color.Black;
            this.txtAge.Properties.Appearance.Options.UseFont = true;
            this.txtAge.Properties.Appearance.Options.UseForeColor = true;
            this.txtAge.Size = new System.Drawing.Size(75, 22);
            this.txtAge.StyleController = this.styleController;
            this.txtAge.TabIndex = 5;
            this.txtAge.TextChanged += new System.EventHandler(this.txtAge_TextChanged);
            this.txtAge.Enter += new System.EventHandler(this.txtFirstName_Enter);
            this.txtAge.KeyUp += new System.Windows.Forms.KeyEventHandler(this.txtAge_KeyUp);
            this.txtAge.Leave += new System.EventHandler(this.txtFirstName_Leave);
            // 
            // chkDirectPatient
            // 
            this.chkDirectPatient.Location = new System.Drawing.Point(463, 126);
            this.chkDirectPatient.MenuManager = this.barManager;
            this.chkDirectPatient.Name = "chkDirectPatient";
            this.chkDirectPatient.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkDirectPatient.Properties.Appearance.Options.UseFont = true;
            this.chkDirectPatient.Properties.AutoWidth = true;
            this.chkDirectPatient.Properties.Caption = "&No Referral";
            this.chkDirectPatient.Size = new System.Drawing.Size(87, 20);
            this.chkDirectPatient.StyleController = this.styleController;
            this.chkDirectPatient.TabIndex = 8;
            this.chkDirectPatient.ToolTip = "Selecting this option will disable referrals for this order";
            // 
            // btnDiscountCalculator
            // 
            this.btnDiscountCalculator.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnDiscountCalculator.Appearance.Options.UseFont = true;
            this.btnDiscountCalculator.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.calculator_arrow_16;
            this.btnDiscountCalculator.ImageOptions.ImageIndex = 18;
            this.btnDiscountCalculator.Location = new System.Drawing.Point(514, 672);
            this.btnDiscountCalculator.Name = "btnDiscountCalculator";
            this.btnDiscountCalculator.Size = new System.Drawing.Size(60, 23);
            this.btnDiscountCalculator.StyleController = this.styleController;
            this.btnDiscountCalculator.TabIndex = 9999;
            this.btnDiscountCalculator.TabStop = false;
            this.btnDiscountCalculator.Text = "Calc";
            this.btnDiscountCalculator.ToolTip = "Calculate Discount";
            this.btnDiscountCalculator.Click += new System.EventHandler(this.btnDiscountCalculator_Click);
            // 
            // txtExtTrackingId
            // 
            this.txtExtTrackingId.Enabled = false;
            this.txtExtTrackingId.Location = new System.Drawing.Point(1039, 252);
            this.txtExtTrackingId.Margin = new System.Windows.Forms.Padding(4);
            this.txtExtTrackingId.MenuManager = this.barManager;
            this.txtExtTrackingId.Name = "txtExtTrackingId";
            this.txtExtTrackingId.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtExtTrackingId.Properties.Appearance.ForeColor = System.Drawing.Color.Black;
            this.txtExtTrackingId.Properties.Appearance.Options.UseFont = true;
            this.txtExtTrackingId.Properties.Appearance.Options.UseForeColor = true;
            this.txtExtTrackingId.Size = new System.Drawing.Size(88, 22);
            this.txtExtTrackingId.StyleController = this.styleController;
            this.txtExtTrackingId.TabIndex = 25;
            this.txtExtTrackingId.TabStop = false;
            this.txtExtTrackingId.ToolTip = "Tracking ID of External Lab";
            this.txtExtTrackingId.Enter += new System.EventHandler(this.txtFirstName_Enter);
            this.txtExtTrackingId.Leave += new System.EventHandler(this.txtFirstName_Leave);
            // 
            // chkOutsideOrder
            // 
            this.chkOutsideOrder.Location = new System.Drawing.Point(981, 189);
            this.chkOutsideOrder.MenuManager = this.barManager;
            this.chkOutsideOrder.Name = "chkOutsideOrder";
            this.chkOutsideOrder.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkOutsideOrder.Properties.Appearance.Options.UseFont = true;
            this.chkOutsideOrder.Properties.Caption = "Is Outside Order?";
            this.chkOutsideOrder.Properties.ContentAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.chkOutsideOrder.Size = new System.Drawing.Size(127, 20);
            this.chkOutsideOrder.StyleController = this.styleController;
            this.chkOutsideOrder.TabIndex = 23;
            this.chkOutsideOrder.TabStop = false;
            this.chkOutsideOrder.ToolTip = "Is External Sub-order?";
            this.chkOutsideOrder.CheckedChanged += new System.EventHandler(this.chkOutsideOrder_CheckedChanged);
            // 
            // btnBIClear
            // 
            this.btnBIClear.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnBIClear.Appearance.Options.UseFont = true;
            this.btnBIClear.ImageOptions.ImageIndex = 11;
            this.btnBIClear.ImageOptions.ImageList = this.imageList1;
            this.btnBIClear.Location = new System.Drawing.Point(900, 192);
            this.btnBIClear.Name = "btnBIClear";
            this.btnBIClear.Size = new System.Drawing.Size(71, 23);
            this.btnBIClear.StyleController = this.styleController;
            this.btnBIClear.TabIndex = 1020;
            this.btnBIClear.TabStop = false;
            this.btnBIClear.Text = "Clear";
            this.btnBIClear.ToolTip = "Clear all billable items";
            this.btnBIClear.Click += new System.EventHandler(this.btnBIClear_Click);
            // 
            // txtDiscountNote
            // 
            this.txtDiscountNote.Location = new System.Drawing.Point(400, 701);
            this.txtDiscountNote.MenuManager = this.barManager;
            this.txtDiscountNote.Name = "txtDiscountNote";
            this.txtDiscountNote.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtDiscountNote.Properties.Appearance.Options.UseFont = true;
            this.txtDiscountNote.Properties.LookAndFeel.SkinName = "Office 2010 Blue";
            this.txtDiscountNote.Properties.LookAndFeel.UseDefaultLookAndFeel = false;
            this.txtDiscountNote.Size = new System.Drawing.Size(226, 22);
            this.txtDiscountNote.TabIndex = 17;
            this.txtDiscountNote.ToolTip = "Reason for Cash Discount";
            this.txtDiscountNote.Enter += new System.EventHandler(this.cboPatientTitle_Enter);
            this.txtDiscountNote.KeyUp += new System.Windows.Forms.KeyEventHandler(this.txtDiscountNote_KeyUp);
            this.txtDiscountNote.Leave += new System.EventHandler(this.cboPatientTitle_Leave);
            // 
            // lblEmail
            // 
            this.lblEmail.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblEmail.Appearance.Options.UseFont = true;
            this.lblEmail.Location = new System.Drawing.Point(546, 63);
            this.lblEmail.Margin = new System.Windows.Forms.Padding(4);
            this.lblEmail.Name = "lblEmail";
            this.lblEmail.Size = new System.Drawing.Size(36, 16);
            this.lblEmail.TabIndex = 1026;
            this.lblEmail.Text = "Email:";
            // 
            // txtEmail
            // 
            this.txtEmail.EditValue = "";
            this.txtEmail.Location = new System.Drawing.Point(590, 60);
            this.txtEmail.Margin = new System.Windows.Forms.Padding(4);
            this.txtEmail.MenuManager = this.barManager;
            this.txtEmail.Name = "txtEmail";
            this.txtEmail.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtEmail.Properties.Appearance.ForeColor = System.Drawing.Color.Black;
            this.txtEmail.Properties.Appearance.Options.UseFont = true;
            this.txtEmail.Properties.Appearance.Options.UseForeColor = true;
            this.txtEmail.Size = new System.Drawing.Size(146, 22);
            this.txtEmail.StyleController = this.styleController;
            this.txtEmail.TabIndex = 999;
            this.txtEmail.TabStop = false;
            this.txtEmail.ToolTip = "Email address";
            // 
            // lblExternalId
            // 
            this.lblExternalId.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblExternalId.Appearance.Options.UseFont = true;
            this.lblExternalId.Location = new System.Drawing.Point(982, 255);
            this.lblExternalId.Margin = new System.Windows.Forms.Padding(4);
            this.lblExternalId.Name = "lblExternalId";
            this.lblExternalId.Size = new System.Drawing.Size(41, 16);
            this.lblExternalId.TabIndex = 1031;
            this.lblExternalId.Text = "Ext. Id:";
            // 
            // labelControl4
            // 
            this.labelControl4.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl4.Appearance.Options.UseFont = true;
            this.labelControl4.Location = new System.Drawing.Point(981, 213);
            this.labelControl4.Margin = new System.Windows.Forms.Padding(4);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(25, 16);
            this.labelControl4.TabIndex = 1038;
            this.labelControl4.Text = "Lab:";
            // 
            // txtCustomerPHN
            // 
            this.txtCustomerPHN.Location = new System.Drawing.Point(1031, 95);
            this.txtCustomerPHN.Margin = new System.Windows.Forms.Padding(4);
            this.txtCustomerPHN.MenuManager = this.barManager;
            this.txtCustomerPHN.Name = "txtCustomerPHN";
            this.txtCustomerPHN.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtCustomerPHN.Properties.Appearance.ForeColor = System.Drawing.Color.Black;
            this.txtCustomerPHN.Properties.Appearance.Options.UseFont = true;
            this.txtCustomerPHN.Properties.Appearance.Options.UseForeColor = true;
            this.txtCustomerPHN.Size = new System.Drawing.Size(129, 22);
            this.txtCustomerPHN.StyleController = this.styleController;
            this.txtCustomerPHN.TabIndex = 20;
            this.txtCustomerPHN.TabStop = false;
            this.txtCustomerPHN.ToolTip = "Personal Health Number of Patient";
            this.txtCustomerPHN.KeyUp += new System.Windows.Forms.KeyEventHandler(this.txtCustomerPHN_KeyUp);
            // 
            // labelControl6
            // 
            this.labelControl6.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl6.Appearance.Options.UseFont = true;
            this.labelControl6.Location = new System.Drawing.Point(982, 97);
            this.labelControl6.Margin = new System.Windows.Forms.Padding(4);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(28, 16);
            this.labelControl6.TabIndex = 1040;
            this.labelControl6.Text = "PHN:";
            // 
            // txtCustomerPhone
            // 
            this.txtCustomerPhone.Location = new System.Drawing.Point(1031, 125);
            this.txtCustomerPhone.Margin = new System.Windows.Forms.Padding(4);
            this.txtCustomerPhone.MenuManager = this.barManager;
            this.txtCustomerPhone.Name = "txtCustomerPhone";
            this.txtCustomerPhone.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtCustomerPhone.Properties.Appearance.ForeColor = System.Drawing.Color.Black;
            this.txtCustomerPhone.Properties.Appearance.Options.UseFont = true;
            this.txtCustomerPhone.Properties.Appearance.Options.UseForeColor = true;
            this.txtCustomerPhone.Properties.MaskSettings.Set("MaskManagerType", typeof(DevExpress.Data.Mask.RegExpMaskManager));
            this.txtCustomerPhone.Properties.MaskSettings.Set("allowBlankInput", true);
            this.txtCustomerPhone.Properties.MaskSettings.Set("mask", "\\+?\\d{6,16}");
            this.txtCustomerPhone.Properties.MaskSettings.Set("showPlaceholders", false);
            this.txtCustomerPhone.Size = new System.Drawing.Size(129, 22);
            this.txtCustomerPhone.StyleController = this.styleController;
            this.txtCustomerPhone.TabIndex = 21;
            this.txtCustomerPhone.TabStop = false;
            this.txtCustomerPhone.ToolTip = "Patient\'s Telephone Number";
            this.txtCustomerPhone.KeyUp += new System.Windows.Forms.KeyEventHandler(this.txtCustomerPHN_KeyUp);
            // 
            // labelControl7
            // 
            this.labelControl7.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl7.Appearance.Options.UseFont = true;
            this.labelControl7.Location = new System.Drawing.Point(982, 128);
            this.labelControl7.Margin = new System.Windows.Forms.Padding(4);
            this.labelControl7.Name = "labelControl7";
            this.labelControl7.Size = new System.Drawing.Size(40, 16);
            this.labelControl7.TabIndex = 1042;
            this.labelControl7.Text = "Phone:";
            // 
            // labelControl11
            // 
            this.labelControl11.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl11.Appearance.Options.UseFont = true;
            this.labelControl11.Location = new System.Drawing.Point(981, 324);
            this.labelControl11.Margin = new System.Windows.Forms.Padding(4);
            this.labelControl11.Name = "labelControl11";
            this.labelControl11.Size = new System.Drawing.Size(37, 16);
            this.labelControl11.TabIndex = 1047;
            this.labelControl11.Text = "Client:";
            // 
            // cboAffiliateLabs
            // 
            this.cboAffiliateLabs.Enabled = false;
            this.cboAffiliateLabs.Location = new System.Drawing.Point(1039, 210);
            this.cboAffiliateLabs.Margin = new System.Windows.Forms.Padding(4);
            this.cboAffiliateLabs.MenuManager = this.barManager;
            this.cboAffiliateLabs.Name = "cboAffiliateLabs";
            this.cboAffiliateLabs.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cboAffiliateLabs.Properties.Appearance.ForeColor = System.Drawing.Color.Black;
            this.cboAffiliateLabs.Properties.Appearance.Options.UseFont = true;
            this.cboAffiliateLabs.Properties.Appearance.Options.UseForeColor = true;
            this.cboAffiliateLabs.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cboAffiliateLabs.Size = new System.Drawing.Size(214, 22);
            this.cboAffiliateLabs.StyleController = this.styleController;
            this.cboAffiliateLabs.TabIndex = 24;
            this.cboAffiliateLabs.TabStop = false;
            this.cboAffiliateLabs.ToolTip = "Lab-to-Lab";
            this.cboAffiliateLabs.SelectedValueChanged += new System.EventHandler(this.cboAffiliateLabs_SelectedValueChanged);
            this.cboAffiliateLabs.KeyUp += new System.Windows.Forms.KeyEventHandler(this.cboAffiliateLabs_KeyUp);
            // 
            // cboCorporateClients
            // 
            this.cboCorporateClients.Location = new System.Drawing.Point(1039, 321);
            this.cboCorporateClients.Margin = new System.Windows.Forms.Padding(4);
            this.cboCorporateClients.Name = "cboCorporateClients";
            this.cboCorporateClients.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cboCorporateClients.Properties.Appearance.ForeColor = System.Drawing.Color.Black;
            this.cboCorporateClients.Properties.Appearance.Options.UseFont = true;
            this.cboCorporateClients.Properties.Appearance.Options.UseForeColor = true;
            this.cboCorporateClients.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cboCorporateClients.Size = new System.Drawing.Size(215, 22);
            this.cboCorporateClients.TabIndex = 26;
            this.cboCorporateClients.TabStop = false;
            this.cboCorporateClients.ToolTip = "Corporate Client";
            this.cboCorporateClients.SelectedValueChanged += new System.EventHandler(this.cboCorporateClients_SelectedValueChanged);
            this.cboCorporateClients.KeyUp += new System.Windows.Forms.KeyEventHandler(this.cboCorporateClients_KeyUp);
            // 
            // cboAffiliates
            // 
            this.cboAffiliates.Location = new System.Drawing.Point(1039, 400);
            this.cboAffiliates.Margin = new System.Windows.Forms.Padding(4);
            this.cboAffiliates.Name = "cboAffiliates";
            this.cboAffiliates.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cboAffiliates.Properties.Appearance.ForeColor = System.Drawing.Color.Black;
            this.cboAffiliates.Properties.Appearance.Options.UseFont = true;
            this.cboAffiliates.Properties.Appearance.Options.UseForeColor = true;
            this.cboAffiliates.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cboAffiliates.Size = new System.Drawing.Size(213, 22);
            this.cboAffiliates.TabIndex = 27;
            this.cboAffiliates.TabStop = false;
            this.cboAffiliates.ToolTip = "Affiliate";
            this.cboAffiliates.SelectedValueChanged += new System.EventHandler(this.cboAffiliates_SelectedValueChanged);
            this.cboAffiliates.KeyUp += new System.Windows.Forms.KeyEventHandler(this.cboAffiliates_KeyUp);
            // 
            // labelControl12
            // 
            this.labelControl12.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl12.Appearance.Options.UseFont = true;
            this.labelControl12.Location = new System.Drawing.Point(979, 403);
            this.labelControl12.Margin = new System.Windows.Forms.Padding(4);
            this.labelControl12.Name = "labelControl12";
            this.labelControl12.Size = new System.Drawing.Size(38, 16);
            this.labelControl12.TabIndex = 1054;
            this.labelControl12.Text = "Agent:";
            // 
            // btnCustomerSearch
            // 
            this.btnCustomerSearch.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCustomerSearch.Appearance.Options.UseFont = true;
            this.btnCustomerSearch.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnCustomerSearch.ImageOptions.Image")));
            this.btnCustomerSearch.ImageOptions.ImageIndex = 18;
            this.btnCustomerSearch.ImageOptions.ImageList = this.imageList1;
            this.btnCustomerSearch.Location = new System.Drawing.Point(1168, 94);
            this.btnCustomerSearch.Name = "btnCustomerSearch";
            this.btnCustomerSearch.Size = new System.Drawing.Size(83, 25);
            this.btnCustomerSearch.StyleController = this.styleController;
            this.btnCustomerSearch.TabIndex = 22;
            this.btnCustomerSearch.TabStop = false;
            this.btnCustomerSearch.Text = "Find";
            this.btnCustomerSearch.ToolTip = "Find Customer by Personal Health Number or Phone";
            this.btnCustomerSearch.Click += new System.EventHandler(this.btnCustomerSearch_Click);
            // 
            // labelControl21
            // 
            this.labelControl21.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl21.Appearance.Options.UseFont = true;
            this.labelControl21.Enabled = false;
            this.labelControl21.Location = new System.Drawing.Point(979, 484);
            this.labelControl21.Margin = new System.Windows.Forms.Padding(4);
            this.labelControl21.Name = "labelControl21";
            this.labelControl21.Size = new System.Drawing.Size(52, 16);
            this.labelControl21.TabIndex = 1066;
            this.labelControl21.Text = "Package:";
            // 
            // cboHealthPackages
            // 
            this.cboHealthPackages.Enabled = false;
            this.cboHealthPackages.Location = new System.Drawing.Point(1039, 481);
            this.cboHealthPackages.Margin = new System.Windows.Forms.Padding(4);
            this.cboHealthPackages.Name = "cboHealthPackages";
            this.cboHealthPackages.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cboHealthPackages.Properties.Appearance.ForeColor = System.Drawing.Color.Black;
            this.cboHealthPackages.Properties.Appearance.Options.UseFont = true;
            this.cboHealthPackages.Properties.Appearance.Options.UseForeColor = true;
            this.cboHealthPackages.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cboHealthPackages.Size = new System.Drawing.Size(213, 22);
            this.cboHealthPackages.TabIndex = 28;
            this.cboHealthPackages.TabStop = false;
            this.cboHealthPackages.ToolTip = "Health Package";
            // 
            // btnNormalizeName
            // 
            this.btnNormalizeName.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnNormalizeName.Appearance.Options.UseFont = true;
            this.btnNormalizeName.ImageOptions.Image = global::LabMaestro.Controls.Win.Resources.tick_16;
            this.btnNormalizeName.ImageOptions.ImageIndex = 18;
            this.btnNormalizeName.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleCenter;
            this.btnNormalizeName.Location = new System.Drawing.Point(503, 90);
            this.btnNormalizeName.Name = "btnNormalizeName";
            this.btnNormalizeName.Size = new System.Drawing.Size(32, 23);
            this.btnNormalizeName.StyleController = this.styleController;
            this.btnNormalizeName.TabIndex = 1073;
            this.btnNormalizeName.TabStop = false;
            this.btnNormalizeName.ToolTip = "রোগীর নাম, লিঙ্গ, বয়স অটো-ফরম্যাট করুন";
            this.btnNormalizeName.Click += new System.EventHandler(this.btnNormalizeName_Click);
            // 
            // lblSelectedCorp
            // 
            this.lblSelectedCorp.Appearance.BackColor = System.Drawing.Color.Transparent;
            this.lblSelectedCorp.Appearance.Font = new System.Drawing.Font("Segoe UI", 6.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblSelectedCorp.Appearance.ForeColor = System.Drawing.Color.DarkRed;
            this.lblSelectedCorp.Appearance.Options.UseBackColor = true;
            this.lblSelectedCorp.Appearance.Options.UseFont = true;
            this.lblSelectedCorp.Appearance.Options.UseForeColor = true;
            this.lblSelectedCorp.Location = new System.Drawing.Point(1039, 347);
            this.lblSelectedCorp.Margin = new System.Windows.Forms.Padding(4);
            this.lblSelectedCorp.Name = "lblSelectedCorp";
            this.lblSelectedCorp.Size = new System.Drawing.Size(22, 12);
            this.lblSelectedCorp.TabIndex = 1079;
            this.lblSelectedCorp.Text = "None";
            // 
            // rgResultReleaseMode
            // 
            this.rgResultReleaseMode.EditValue = ((byte)(0));
            this.rgResultReleaseMode.Location = new System.Drawing.Point(981, 552);
            this.rgResultReleaseMode.Margin = new System.Windows.Forms.Padding(4);
            this.rgResultReleaseMode.MenuManager = this.barManager;
            this.rgResultReleaseMode.Name = "rgResultReleaseMode";
            this.rgResultReleaseMode.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rgResultReleaseMode.Properties.Appearance.Options.UseFont = true;
            this.rgResultReleaseMode.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.RadioGroupItem[] {
            new DevExpress.XtraEditors.Controls.RadioGroupItem(((byte)(0)), "Regular"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(((byte)(10)), "Override Dues Check"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(((byte)(20)), "Immediate Release")});
            this.rgResultReleaseMode.Size = new System.Drawing.Size(271, 91);
            this.rgResultReleaseMode.StyleController = this.styleController;
            this.rgResultReleaseMode.TabIndex = 29;
            this.rgResultReleaseMode.TabStop = false;
            // 
            // labelControl22
            // 
            this.labelControl22.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl22.Appearance.Options.UseFont = true;
            this.labelControl22.Location = new System.Drawing.Point(981, 528);
            this.labelControl22.Margin = new System.Windows.Forms.Padding(4);
            this.labelControl22.Name = "labelControl22";
            this.labelControl22.Size = new System.Drawing.Size(133, 16);
            this.labelControl22.TabIndex = 1081;
            this.labelControl22.Text = "Results Release Check:";
            // 
            // labelControl23
            // 
            this.labelControl23.Appearance.BackColor = System.Drawing.Color.Snow;
            this.labelControl23.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl23.Appearance.ForeColor = System.Drawing.Color.MidnightBlue;
            this.labelControl23.Appearance.Options.UseBackColor = true;
            this.labelControl23.Appearance.Options.UseFont = true;
            this.labelControl23.Appearance.Options.UseForeColor = true;
            this.labelControl23.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None;
            this.labelControl23.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.labelControl23.LineLocation = DevExpress.XtraEditors.LineLocation.Center;
            this.labelControl23.LineOrientation = DevExpress.XtraEditors.LabelLineOrientation.Horizontal;
            this.labelControl23.LineStyle = System.Drawing.Drawing2D.DashStyle.Dot;
            this.labelControl23.LineVisible = true;
            this.labelControl23.Location = new System.Drawing.Point(981, 285);
            this.labelControl23.Margin = new System.Windows.Forms.Padding(4);
            this.labelControl23.Name = "labelControl23";
            this.labelControl23.Padding = new System.Windows.Forms.Padding(4);
            this.labelControl23.Size = new System.Drawing.Size(273, 28);
            this.labelControl23.TabIndex = 1082;
            this.labelControl23.Text = "Corporate Client";
            this.labelControl23.UseMnemonic = false;
            // 
            // lblSelectedAffiliate
            // 
            this.lblSelectedAffiliate.Appearance.BackColor = System.Drawing.Color.Transparent;
            this.lblSelectedAffiliate.Appearance.Font = new System.Drawing.Font("Segoe UI", 6.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblSelectedAffiliate.Appearance.ForeColor = System.Drawing.Color.DarkRed;
            this.lblSelectedAffiliate.Appearance.Options.UseBackColor = true;
            this.lblSelectedAffiliate.Appearance.Options.UseFont = true;
            this.lblSelectedAffiliate.Appearance.Options.UseForeColor = true;
            this.lblSelectedAffiliate.Location = new System.Drawing.Point(1039, 425);
            this.lblSelectedAffiliate.Margin = new System.Windows.Forms.Padding(4);
            this.lblSelectedAffiliate.Name = "lblSelectedAffiliate";
            this.lblSelectedAffiliate.Size = new System.Drawing.Size(22, 12);
            this.lblSelectedAffiliate.TabIndex = 1083;
            this.lblSelectedAffiliate.Text = "None";
            // 
            // lblAssociatedLab
            // 
            this.lblAssociatedLab.Appearance.BackColor = System.Drawing.Color.Snow;
            this.lblAssociatedLab.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblAssociatedLab.Appearance.ForeColor = System.Drawing.Color.MidnightBlue;
            this.lblAssociatedLab.Appearance.Options.UseBackColor = true;
            this.lblAssociatedLab.Appearance.Options.UseFont = true;
            this.lblAssociatedLab.Appearance.Options.UseForeColor = true;
            this.lblAssociatedLab.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None;
            this.lblAssociatedLab.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.lblAssociatedLab.LineLocation = DevExpress.XtraEditors.LineLocation.Center;
            this.lblAssociatedLab.LineOrientation = DevExpress.XtraEditors.LabelLineOrientation.Horizontal;
            this.lblAssociatedLab.LineStyle = System.Drawing.Drawing2D.DashStyle.Dot;
            this.lblAssociatedLab.LineVisible = true;
            this.lblAssociatedLab.Location = new System.Drawing.Point(982, 155);
            this.lblAssociatedLab.Margin = new System.Windows.Forms.Padding(4);
            this.lblAssociatedLab.Name = "lblAssociatedLab";
            this.lblAssociatedLab.Padding = new System.Windows.Forms.Padding(4);
            this.lblAssociatedLab.Size = new System.Drawing.Size(271, 28);
            this.lblAssociatedLab.TabIndex = 1084;
            this.lblAssociatedLab.Text = "External Sub-Order";
            this.lblAssociatedLab.UseMnemonic = false;
            // 
            // labelControl26
            // 
            this.labelControl26.Appearance.BackColor = System.Drawing.Color.Snow;
            this.labelControl26.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl26.Appearance.ForeColor = System.Drawing.Color.MidnightBlue;
            this.labelControl26.Appearance.Options.UseBackColor = true;
            this.labelControl26.Appearance.Options.UseFont = true;
            this.labelControl26.Appearance.Options.UseForeColor = true;
            this.labelControl26.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None;
            this.labelControl26.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.labelControl26.LineLocation = DevExpress.XtraEditors.LineLocation.Center;
            this.labelControl26.LineOrientation = DevExpress.XtraEditors.LabelLineOrientation.Horizontal;
            this.labelControl26.LineStyle = System.Drawing.Drawing2D.DashStyle.Dot;
            this.labelControl26.LineVisible = true;
            this.labelControl26.Location = new System.Drawing.Point(982, 59);
            this.labelControl26.Margin = new System.Windows.Forms.Padding(4);
            this.labelControl26.Name = "labelControl26";
            this.labelControl26.Padding = new System.Windows.Forms.Padding(4);
            this.labelControl26.Size = new System.Drawing.Size(273, 28);
            this.labelControl26.TabIndex = 1085;
            this.labelControl26.Text = "Registered Patient Details";
            this.labelControl26.UseMnemonic = false;
            // 
            // lblSelectedHealthPackage
            // 
            this.lblSelectedHealthPackage.Appearance.BackColor = System.Drawing.Color.Transparent;
            this.lblSelectedHealthPackage.Appearance.Font = new System.Drawing.Font("Segoe UI", 6.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblSelectedHealthPackage.Appearance.ForeColor = System.Drawing.Color.DarkRed;
            this.lblSelectedHealthPackage.Appearance.Options.UseBackColor = true;
            this.lblSelectedHealthPackage.Appearance.Options.UseFont = true;
            this.lblSelectedHealthPackage.Appearance.Options.UseForeColor = true;
            this.lblSelectedHealthPackage.Location = new System.Drawing.Point(1039, 508);
            this.lblSelectedHealthPackage.Margin = new System.Windows.Forms.Padding(4);
            this.lblSelectedHealthPackage.Name = "lblSelectedHealthPackage";
            this.lblSelectedHealthPackage.Size = new System.Drawing.Size(22, 12);
            this.lblSelectedHealthPackage.TabIndex = 1086;
            this.lblSelectedHealthPackage.Text = "None";
            // 
            // labelControl28
            // 
            this.labelControl28.Appearance.BackColor = System.Drawing.Color.Snow;
            this.labelControl28.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl28.Appearance.ForeColor = System.Drawing.Color.MidnightBlue;
            this.labelControl28.Appearance.Options.UseBackColor = true;
            this.labelControl28.Appearance.Options.UseFont = true;
            this.labelControl28.Appearance.Options.UseForeColor = true;
            this.labelControl28.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None;
            this.labelControl28.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.labelControl28.LineLocation = DevExpress.XtraEditors.LineLocation.Center;
            this.labelControl28.LineOrientation = DevExpress.XtraEditors.LabelLineOrientation.Horizontal;
            this.labelControl28.LineStyle = System.Drawing.Drawing2D.DashStyle.Dot;
            this.labelControl28.LineVisible = true;
            this.labelControl28.Location = new System.Drawing.Point(981, 445);
            this.labelControl28.Margin = new System.Windows.Forms.Padding(4);
            this.labelControl28.Name = "labelControl28";
            this.labelControl28.Padding = new System.Windows.Forms.Padding(4);
            this.labelControl28.Size = new System.Drawing.Size(271, 28);
            this.labelControl28.TabIndex = 1087;
            this.labelControl28.Text = "Health Package";
            this.labelControl28.UseMnemonic = false;
            // 
            // labelControl20
            // 
            this.labelControl20.Appearance.BackColor = System.Drawing.Color.Snow;
            this.labelControl20.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl20.Appearance.ForeColor = System.Drawing.Color.MidnightBlue;
            this.labelControl20.Appearance.Options.UseBackColor = true;
            this.labelControl20.Appearance.Options.UseFont = true;
            this.labelControl20.Appearance.Options.UseForeColor = true;
            this.labelControl20.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None;
            this.labelControl20.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.labelControl20.LineLocation = DevExpress.XtraEditors.LineLocation.Center;
            this.labelControl20.LineOrientation = DevExpress.XtraEditors.LabelLineOrientation.Horizontal;
            this.labelControl20.LineStyle = System.Drawing.Drawing2D.DashStyle.Dot;
            this.labelControl20.LineVisible = true;
            this.labelControl20.Location = new System.Drawing.Point(981, 365);
            this.labelControl20.Margin = new System.Windows.Forms.Padding(4);
            this.labelControl20.Name = "labelControl20";
            this.labelControl20.Padding = new System.Windows.Forms.Padding(4);
            this.labelControl20.Size = new System.Drawing.Size(271, 28);
            this.labelControl20.TabIndex = 1088;
            this.labelControl20.Text = "Affiliate";
            this.labelControl20.UseMnemonic = false;
            // 
            // lblSelectedAffiliateLab
            // 
            this.lblSelectedAffiliateLab.Appearance.BackColor = System.Drawing.Color.Transparent;
            this.lblSelectedAffiliateLab.Appearance.Font = new System.Drawing.Font("Segoe UI", 6.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblSelectedAffiliateLab.Appearance.ForeColor = System.Drawing.Color.DarkRed;
            this.lblSelectedAffiliateLab.Appearance.Options.UseBackColor = true;
            this.lblSelectedAffiliateLab.Appearance.Options.UseFont = true;
            this.lblSelectedAffiliateLab.Appearance.Options.UseForeColor = true;
            this.lblSelectedAffiliateLab.Location = new System.Drawing.Point(1039, 235);
            this.lblSelectedAffiliateLab.Margin = new System.Windows.Forms.Padding(4);
            this.lblSelectedAffiliateLab.Name = "lblSelectedAffiliateLab";
            this.lblSelectedAffiliateLab.Size = new System.Drawing.Size(22, 12);
            this.lblSelectedAffiliateLab.TabIndex = 1089;
            this.lblSelectedAffiliateLab.Text = "None";
            // 
            // lblCustomerId
            // 
            this.lblCustomerId.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblCustomerId.Appearance.Options.UseFont = true;
            this.lblCustomerId.Location = new System.Drawing.Point(856, 63);
            this.lblCustomerId.Margin = new System.Windows.Forms.Padding(4);
            this.lblCustomerId.Name = "lblCustomerId";
            this.lblCustomerId.Size = new System.Drawing.Size(29, 16);
            this.lblCustomerId.TabIndex = 1097;
            this.lblCustomerId.Text = "Pt #:";
            this.lblCustomerId.ToolTip = "নিবন্ধন করা রোগীর আইডি নং";
            // 
            // lblCustomerPHN
            // 
            this.lblCustomerPHN.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblCustomerPHN.Appearance.Options.UseFont = true;
            this.lblCustomerPHN.Location = new System.Drawing.Point(751, 63);
            this.lblCustomerPHN.Margin = new System.Windows.Forms.Padding(4);
            this.lblCustomerPHN.Name = "lblCustomerPHN";
            this.lblCustomerPHN.Size = new System.Drawing.Size(28, 16);
            this.lblCustomerPHN.TabIndex = 1096;
            this.lblCustomerPHN.Text = "PHN:";
            this.lblCustomerPHN.ToolTip = "নিবন্ধন করা রোগীর পার্সোনাল হেলথ নাম্বার";
            // 
            // labelControl10
            // 
            this.labelControl10.Appearance.BackColor = System.Drawing.Color.DimGray;
            this.labelControl10.Appearance.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl10.Appearance.ForeColor = System.Drawing.Color.White;
            this.labelControl10.Appearance.Options.UseBackColor = true;
            this.labelControl10.Appearance.Options.UseFont = true;
            this.labelControl10.Appearance.Options.UseForeColor = true;
            this.labelControl10.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None;
            this.labelControl10.Location = new System.Drawing.Point(323, 651);
            this.labelControl10.Name = "labelControl10";
            this.labelControl10.Size = new System.Drawing.Size(431, 14);
            this.labelControl10.TabIndex = 1102;
            this.labelControl10.Text = " Discount Parameters";
            // 
            // txtTelephone
            // 
            this.txtTelephone.Location = new System.Drawing.Point(75, 60);
            this.txtTelephone.MenuManager = this.barManager;
            this.txtTelephone.Name = "txtTelephone";
            this.txtTelephone.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtTelephone.Properties.Appearance.Options.UseFont = true;
            this.txtTelephone.Size = new System.Drawing.Size(102, 22);
            this.txtTelephone.TabIndex = 0;
            this.txtTelephone.ToolTip = "<ENTER> সার্চ\r\nF9 - রোগীর নতুন মোবাইল এ্যাপ এ্যাকাউন্ট নিবন্ধন\r\nF8 - পূর্বের রোগী" +
    "র লিস্ট";
            this.txtTelephone.Enter += new System.EventHandler(this.txtFirstName_Enter);
            this.txtTelephone.KeyUp += new System.Windows.Forms.KeyEventHandler(this.txtTelephone_KeyUp);
            this.txtTelephone.Leave += new System.EventHandler(this.txtFirstName_Leave);
            // 
            // pnlEffectiveDiscountPct
            // 
            this.pnlEffectiveDiscountPct.Appearance.BackColor = System.Drawing.Color.YellowGreen;
            this.pnlEffectiveDiscountPct.Appearance.BorderColor = System.Drawing.Color.DimGray;
            this.pnlEffectiveDiscountPct.Appearance.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.pnlEffectiveDiscountPct.Appearance.Options.UseBackColor = true;
            this.pnlEffectiveDiscountPct.Appearance.Options.UseBorderColor = true;
            this.pnlEffectiveDiscountPct.Appearance.Options.UseFont = true;
            this.pnlEffectiveDiscountPct.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.pnlEffectiveDiscountPct.Controls.Add(this.lblEffectiveDiscountPct);
            this.pnlEffectiveDiscountPct.Location = new System.Drawing.Point(760, 651);
            this.pnlEffectiveDiscountPct.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Flat;
            this.pnlEffectiveDiscountPct.LookAndFeel.UseDefaultLookAndFeel = false;
            this.pnlEffectiveDiscountPct.Name = "pnlEffectiveDiscountPct";
            this.pnlEffectiveDiscountPct.Size = new System.Drawing.Size(64, 72);
            this.pnlEffectiveDiscountPct.TabIndex = 10004;
            // 
            // lblEffectiveDiscountPct
            // 
            this.lblEffectiveDiscountPct.Appearance.Font = new System.Drawing.Font("Segoe UI", 11.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblEffectiveDiscountPct.Appearance.ForeColor = System.Drawing.Color.White;
            this.lblEffectiveDiscountPct.Appearance.Options.UseFont = true;
            this.lblEffectiveDiscountPct.Appearance.Options.UseForeColor = true;
            this.lblEffectiveDiscountPct.Appearance.Options.UseTextOptions = true;
            this.lblEffectiveDiscountPct.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lblEffectiveDiscountPct.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None;
            this.lblEffectiveDiscountPct.Location = new System.Drawing.Point(0, 24);
            this.lblEffectiveDiscountPct.Name = "lblEffectiveDiscountPct";
            this.lblEffectiveDiscountPct.Size = new System.Drawing.Size(64, 24);
            this.lblEffectiveDiscountPct.TabIndex = 1;
            this.lblEffectiveDiscountPct.Text = "999 %";
            this.lblEffectiveDiscountPct.ToolTip = "Effective discount percentage";
            // 
            // btnRegisterCustomerFast
            // 
            this.btnRegisterCustomerFast.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnRegisterCustomerFast.Appearance.Options.UseFont = true;
            this.btnRegisterCustomerFast.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnRegisterCustomerFast.ImageOptions.Image")));
            this.btnRegisterCustomerFast.ImageOptions.ImageIndex = 15;
            this.btnRegisterCustomerFast.ImageOptions.ImageList = this.imageList1;
            this.btnRegisterCustomerFast.Location = new System.Drawing.Point(256, 59);
            this.btnRegisterCustomerFast.Name = "btnRegisterCustomerFast";
            this.btnRegisterCustomerFast.Size = new System.Drawing.Size(87, 23);
            this.btnRegisterCustomerFast.StyleController = this.styleController;
            this.btnRegisterCustomerFast.TabIndex = 10009;
            this.btnRegisterCustomerFast.TabStop = false;
            this.btnRegisterCustomerFast.Text = "Register";
            this.btnRegisterCustomerFast.ToolTip = "Register New Patient";
            this.btnRegisterCustomerFast.Click += new System.EventHandler(this.btnRegisterCustomerFast_Click);
            // 
            // btnFindCustomer
            // 
            this.btnFindCustomer.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnFindCustomer.Appearance.Options.UseFont = true;
            this.btnFindCustomer.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnFindCustomer.ImageOptions.Image")));
            this.btnFindCustomer.ImageOptions.ImageIndex = 15;
            this.btnFindCustomer.ImageOptions.ImageList = this.imageList1;
            this.btnFindCustomer.Location = new System.Drawing.Point(183, 59);
            this.btnFindCustomer.Name = "btnFindCustomer";
            this.btnFindCustomer.Size = new System.Drawing.Size(67, 23);
            this.btnFindCustomer.StyleController = this.styleController;
            this.btnFindCustomer.TabIndex = 1;
            this.btnFindCustomer.TabStop = false;
            this.btnFindCustomer.Text = "Find";
            this.btnFindCustomer.ToolTip = "Find registered patient";
            this.btnFindCustomer.Click += new System.EventHandler(this.btnFindCustomer_Click);
            // 
            // btnDissociateCustomer
            // 
            this.btnDissociateCustomer.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnDissociateCustomer.Appearance.Options.UseFont = true;
            this.btnDissociateCustomer.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnDissociateCustomer.ImageOptions.Image")));
            this.btnDissociateCustomer.ImageOptions.ImageIndex = 15;
            this.btnDissociateCustomer.ImageOptions.ImageList = this.imageList1;
            this.btnDissociateCustomer.Location = new System.Drawing.Point(349, 59);
            this.btnDissociateCustomer.Name = "btnDissociateCustomer";
            this.btnDissociateCustomer.Size = new System.Drawing.Size(87, 23);
            this.btnDissociateCustomer.StyleController = this.styleController;
            this.btnDissociateCustomer.TabIndex = 10014;
            this.btnDissociateCustomer.TabStop = false;
            this.btnDissociateCustomer.Text = "Dissociate";
            this.btnDissociateCustomer.ToolTip = "এই ল্যাব অর্ডার থেকে রেজিস্টার করা রোগীর লিংক সরান";
            this.btnDissociateCustomer.Click += new System.EventHandler(this.btnDissociateCustomer_Click);
            // 
            // btnReimburseSurcharge
            // 
            this.btnReimburseSurcharge.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnReimburseSurcharge.Appearance.Options.UseFont = true;
            this.btnReimburseSurcharge.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnReimburseSurcharge.ImageOptions.Image")));
            this.btnReimburseSurcharge.ImageOptions.ImageIndex = 15;
            this.btnReimburseSurcharge.ImageOptions.ImageList = this.imageList1;
            this.btnReimburseSurcharge.Location = new System.Drawing.Point(442, 59);
            this.btnReimburseSurcharge.Name = "btnReimburseSurcharge";
            this.btnReimburseSurcharge.Size = new System.Drawing.Size(93, 23);
            this.btnReimburseSurcharge.StyleController = this.styleController;
            this.btnReimburseSurcharge.TabIndex = 10024;
            this.btnReimburseSurcharge.TabStop = false;
            this.btnReimburseSurcharge.Text = "Concession";
            this.btnReimburseSurcharge.ToolTip = "মোবাইল অ্যাপ সারচার্জ থেকে ডিসকাউন্ট সমন্বয় করুন";
            this.btnReimburseSurcharge.Click += new System.EventHandler(this.btnReimburseSurcharge_Click);
            // 
            // LabOrderDialog
            // 
            this.Appearance.Options.UseFont = true;
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.ClientSize = new System.Drawing.Size(1264, 730);
            this.ControlBox = false;
            this.Controls.Add(this.btnReimburseSurcharge);
            this.Controls.Add(this.btnDissociateCustomer);
            this.Controls.Add(this.btnFindCustomer);
            this.Controls.Add(this.btnRegisterCustomerFast);
            this.Controls.Add(this.pnlEffectiveDiscountPct);
            this.Controls.Add(this.txtTelephone);
            this.Controls.Add(this.labelControl10);
            this.Controls.Add(this.lblCustomerId);
            this.Controls.Add(this.lblCustomerPHN);
            this.Controls.Add(this.lblSelectedAffiliateLab);
            this.Controls.Add(this.labelControl20);
            this.Controls.Add(this.labelControl28);
            this.Controls.Add(this.lblSelectedHealthPackage);
            this.Controls.Add(this.labelControl26);
            this.Controls.Add(this.lblAssociatedLab);
            this.Controls.Add(this.lblSelectedAffiliate);
            this.Controls.Add(this.labelControl23);
            this.Controls.Add(this.labelControl22);
            this.Controls.Add(this.rgResultReleaseMode);
            this.Controls.Add(this.lblSelectedCorp);
            this.Controls.Add(this.btnNormalizeName);
            this.Controls.Add(this.labelControl21);
            this.Controls.Add(this.cboHealthPackages);
            this.Controls.Add(this.btnCustomerSearch);
            this.Controls.Add(this.labelControl12);
            this.Controls.Add(this.labelControl11);
            this.Controls.Add(this.labelControl7);
            this.Controls.Add(this.labelControl6);
            this.Controls.Add(this.labelControl4);
            this.Controls.Add(this.lblExternalId);
            this.Controls.Add(this.chkOutsideOrder);
            this.Controls.Add(this.cboAffiliates);
            this.Controls.Add(this.cboCorporateClients);
            this.Controls.Add(this.cboAffiliateLabs);
            this.Controls.Add(this.txtCustomerPhone);
            this.Controls.Add(this.txtCustomerPHN);
            this.Controls.Add(this.lblEmail);
            this.Controls.Add(this.txtEmail);
            this.Controls.Add(this.grdListPhysician);
            this.Controls.Add(this.btnBIClear);
            this.Controls.Add(this.txtExtTrackingId);
            this.Controls.Add(this.btnDiscountCalculator);
            this.Controls.Add(this.chkDirectPatient);
            this.Controls.Add(this.txtAge);
            this.Controls.Add(this.panelControl4);
            this.Controls.Add(this.chkUnknownPhysician);
            this.Controls.Add(this.txtDiscountAmount);
            this.Controls.Add(this.btnBIDelete);
            this.Controls.Add(this.btnBIModify);
            this.Controls.Add(this.btnBIAdd);
            this.Controls.Add(this.btnApplyDiscount);
            this.Controls.Add(this.btnSetDiscountPercent);
            this.Controls.Add(this.panelControl3);
            this.Controls.Add(this.labelControl16);
            this.Controls.Add(this.labelControl15);
            this.Controls.Add(this.labelControl14);
            this.Controls.Add(this.txtDiscountPercent);
            this.Controls.Add(this.btnPayment);
            this.Controls.Add(this.panelControl2);
            this.Controls.Add(this.panelControl1);
            this.Controls.Add(this.grdListLabTest);
            this.Controls.Add(this.lblTelephone);
            this.Controls.Add(this.lblNote);
            this.Controls.Add(this.labelControl9);
            this.Controls.Add(this.txtOrderNotes);
            this.Controls.Add(this.txtPhysicianCustomName);
            this.Controls.Add(this.cboLabTest);
            this.Controls.Add(this.cboPhysician);
            this.Controls.Add(this.labelControl8);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.dteDateOfBirth);
            this.Controls.Add(this.labelControl5);
            this.Controls.Add(this.rgSex);
            this.Controls.Add(this.txtLastName);
            this.Controls.Add(this.txtFirstName);
            this.Controls.Add(this.cboPatientTitle);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.grdOrderedTests);
            this.Controls.Add(this.grdBillableItems);
            this.Controls.Add(this.txtDiscountNote);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.KeyPreview = true;
            this.LookAndFeel.SkinName = "Office 2010 Blue";
            this.LookAndFeel.UseDefaultLookAndFeel = false;
            this.Margin = new System.Windows.Forms.Padding(4);
            this.MaximizeBox = false;
            this.Name = "LabOrderDialog";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "NewLabOrderForm";
            this.Load += new System.EventHandler(this.LabOrderDialog_Load);
            this.KeyUp += new System.Windows.Forms.KeyEventHandler(this.LabOrderDialog_KeyUp);
            ((System.ComponentModel.ISupportInitialize)(this.barManager)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdOrderedTests)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataRowTemplate2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.columnManagerRow2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdBillableItems)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataRowTemplate1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.columnManagerRow1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rgSex.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.styleController)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtLastName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFirstName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cboPatientTitle.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dteDateOfBirth.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dteDateOfBirth.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cboPhysician)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdListPhysician)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cboLabTest)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdListLabTest)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPhysicianCustomName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtOrderNotes.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).EndInit();
            this.panelControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.panelControl2)).EndInit();
            this.panelControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.txtDiscountPercent.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl3)).EndInit();
            this.panelControl3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.txtDiscountAmount.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkUnknownPhysician.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl4)).EndInit();
            this.panelControl4.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.txtAge.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkDirectPatient.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtExtTrackingId.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkOutsideOrder.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDiscountNote.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtEmail.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCustomerPHN.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCustomerPhone.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cboAffiliateLabs.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cboCorporateClients.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cboAffiliates.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cboHealthPackages.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rgResultReleaseMode.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTelephone.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pnlEffectiveDiscountPct)).EndInit();
            this.pnlEffectiveDiscountPct.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraBars.BarManager barManager;
        private DevExpress.XtraBars.Bar toolBar;
        private DevExpress.XtraBars.Bar menuBar;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private Xceed.Grid.GridControl grdOrderedTests;
        private Xceed.Grid.DataRow dataRowTemplate2;
        private Xceed.Grid.ColumnManagerRow columnManagerRow2;
        private Xceed.Grid.GridControl grdBillableItems;
        private Xceed.Grid.DataRow dataRowTemplate1;
        private Xceed.Grid.ColumnManagerRow columnManagerRow1;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.RadioGroup rgSex;
        private DevExpress.XtraEditors.TextEdit txtLastName;
        private DevExpress.XtraEditors.TextEdit txtFirstName;
        private DevExpress.XtraEditors.ComboBoxEdit cboPatientTitle;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.DateEdit dteDateOfBirth;
        private DevExpress.XtraEditors.LabelControl labelControl8;
        private Syncfusion.Windows.Forms.Tools.ComboBoxBase cboLabTest;
        private Syncfusion.Windows.Forms.Tools.ComboBoxBase cboPhysician;
        private DevExpress.XtraEditors.TextEdit txtOrderNotes;
        private DevExpress.XtraEditors.TextEdit txtPhysicianCustomName;
        private DevExpress.XtraEditors.LabelControl labelControl9;
        private DevExpress.XtraEditors.LabelControl lblNote;
        private DevExpress.XtraEditors.LabelControl lblTelephone;
        private Syncfusion.Windows.Forms.Grid.GridListControl grdListLabTest;
        private DevExpress.XtraEditors.PanelControl panelControl1;
        private DevExpress.XtraEditors.LabelControl lblGrossPayable;
        private DevExpress.XtraEditors.LabelControl labelControl13;
        private DevExpress.XtraEditors.PanelControl panelControl2;
        private DevExpress.XtraEditors.LabelControl lblNumTests;
        private DevExpress.XtraEditors.LabelControl labelControl18;
        private DevExpress.XtraEditors.SimpleButton btnPayment;
        private DevExpress.XtraEditors.TextEdit txtDiscountPercent;
        private DevExpress.XtraEditors.LabelControl labelControl14;
        private DevExpress.XtraEditors.LabelControl labelControl15;
        private DevExpress.XtraEditors.PanelControl panelControl3;
        private DevExpress.XtraEditors.LabelControl lblNetPayable;
        private DevExpress.XtraEditors.LabelControl labelControl19;
        private DevExpress.XtraEditors.LabelControl labelControl16;
        private DevExpress.XtraEditors.SimpleButton btnSetDiscountPercent;
        private DevExpress.XtraEditors.SimpleButton btnApplyDiscount;
        private DevExpress.XtraEditors.SimpleButton btnBIDelete;
        private DevExpress.XtraEditors.SimpleButton btnBIModify;
        private DevExpress.XtraEditors.SimpleButton btnBIAdd;
        private DevExpress.XtraBars.BarSubItem OrdersMenu;
        private DevExpress.XtraBars.BarButtonItem miNewLabOrder;
        private DevExpress.XtraBars.BarButtonItem miInvoiceSearch;
        private DevExpress.XtraBars.BarSubItem barSubItem2;
        private DevExpress.XtraBars.BarSubItem UserMenu;
        private DevExpress.XtraBars.BarButtonItem miUserShiftManagement;
        private DevExpress.XtraBars.BarButtonItem miUserChangePassword;
        private DevExpress.XtraBars.BarButtonItem miOrderPutOnHold;
        private DevExpress.XtraBars.BarButtonItem barButtonItem1;
        private DevExpress.XtraBars.BarButtonItem barButtonItem2;
        private DevExpress.XtraBars.BarButtonItem barButtonItem3;
        private DevExpress.XtraBars.BarSubItem barSubItem4;
        private DevExpress.XtraBars.BarButtonItem miExit;
        private DevExpress.XtraBars.BarButtonItem btnNewLabOrder;
        private DevExpress.XtraBars.BarButtonItem btnPutOnHold;
        private System.Windows.Forms.ImageList imageList1;
        private DevExpress.XtraBars.BarButtonItem btnOrdersOnHold;
        private DevExpress.XtraBars.BarButtonItem btnCopyDemographics;
        private DevExpress.XtraBars.BarButtonItem btnPasteDemographics;
        private DevExpress.XtraBars.BarButtonItem btnClearAllTests;
        private DevExpress.XtraBars.BarButtonItem btnCancelOrder;
        private DevExpress.XtraBars.BarButtonItem btnSetDeliveryTime;
        private DevExpress.XtraBars.BarButtonItem btnDeleteTest;
        private DevExpress.XtraEditors.CalcEdit txtDiscountAmount;
        private DevExpress.XtraEditors.StyleController styleController;
        private DevExpress.XtraBars.BarSubItem CatalogsMenu;
        private DevExpress.XtraBars.BarButtonItem browseTestCatalog;
        private DevExpress.XtraBars.BarButtonItem miBrowsePhysicianCatalog;
        private DevExpress.XtraBars.BarButtonItem btnExit;
        private DevExpress.XtraBars.BarButtonItem btnRefreshSearchIndices;
        private DevExpress.XtraEditors.CheckEdit chkUnknownPhysician;
        private DevExpress.XtraEditors.PanelControl panelControl4;
        private DevExpress.XtraEditors.LabelControl lblMaxDiscountAmount;
        private DevExpress.XtraEditors.LabelControl lblMaxDiscountPercent;
        private DevExpress.XtraEditors.TextEdit txtAge;
        private Xceed.Grid.VisualGridElementStyle visualGridElementStyle2;
        private Xceed.Grid.VisualGridElementStyle dataRowStyle1;
        private Xceed.Grid.VisualGridElementStyle visualGridElementStyle1;
        private Xceed.Grid.VisualGridElementStyle dataRowStyle2;
        private DevExpress.XtraEditors.CheckEdit chkDirectPatient;
        private DevExpress.XtraEditors.SimpleButton btnDiscountCalculator;
        private DevExpress.XtraEditors.TextEdit txtExtTrackingId;
        private DevExpress.XtraBars.BarButtonItem miShiftSearch;
        private DevExpress.XtraEditors.CheckEdit chkOutsideOrder;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController;
        private DevExpress.XtraEditors.SimpleButton btnBIClear;
        private Syncfusion.Windows.Forms.Grid.GridListControl grdListPhysician;
        private DevExpress.XtraBars.BarButtonItem btnCalculator;
        private DevExpress.XtraEditors.TextEdit txtDiscountNote;
        private DevExpress.XtraEditors.LabelControl lblEmail;
        private DevExpress.XtraEditors.TextEdit txtEmail;
        private DevExpress.XtraEditors.LabelControl lblExternalId;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.TextEdit txtCustomerPhone;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.TextEdit txtCustomerPHN;
        private DevExpress.XtraEditors.LabelControl labelControl11;
        private DevExpress.XtraEditors.ComboBoxEdit cboCorporateClients;
        private DevExpress.XtraEditors.ComboBoxEdit cboAffiliateLabs;
        private DevExpress.XtraEditors.ComboBoxEdit cboAffiliates;
        private DevExpress.XtraEditors.LabelControl labelControl12;
        private DevExpress.XtraEditors.SimpleButton btnCustomerSearch;
        private DevExpress.XtraEditors.LabelControl labelControl21;
        private DevExpress.XtraEditors.ComboBoxEdit cboHealthPackages;
        private DevExpress.XtraEditors.SimpleButton btnNormalizeName;
        private DevExpress.XtraEditors.LabelControl labelControl23;
        private DevExpress.XtraEditors.LabelControl labelControl22;
        private DevExpress.XtraEditors.RadioGroup rgResultReleaseMode;
        private DevExpress.XtraEditors.LabelControl lblSelectedCorp;
        private DevExpress.XtraEditors.LabelControl lblAssociatedLab;
        private DevExpress.XtraEditors.LabelControl lblSelectedAffiliate;
        private DevExpress.XtraEditors.LabelControl labelControl26;
        private DevExpress.XtraEditors.LabelControl labelControl20;
        private DevExpress.XtraEditors.LabelControl labelControl28;
        private DevExpress.XtraEditors.LabelControl lblSelectedHealthPackage;
        private DevExpress.XtraEditors.LabelControl lblSelectedAffiliateLab;
        private DevExpress.XtraEditors.LabelControl lblCustomerId;
        private DevExpress.XtraEditors.LabelControl lblCustomerPHN;
        private DevExpress.XtraBars.BarButtonItem btnRegisterCustomer;
        private DevExpress.XtraBars.BarButtonItem miAssignAssocLab;
        private DevExpress.XtraEditors.LabelControl labelControl10;
        private DevExpress.XtraEditors.TextEdit txtTelephone;
        private DevExpress.XtraEditors.PanelControl pnlEffectiveDiscountPct;
        private DevExpress.XtraEditors.LabelControl lblEffectiveDiscountPct;
        private DevExpress.XtraEditors.SimpleButton btnRegisterCustomerFast;
        private DevExpress.XtraEditors.SimpleButton btnFindCustomer;
        private DevExpress.XtraEditors.SimpleButton btnDissociateCustomer;
        private DevExpress.XtraEditors.SimpleButton btnReimburseSurcharge;
    }
}