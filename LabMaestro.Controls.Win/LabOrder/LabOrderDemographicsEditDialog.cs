﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Windows.Forms;
using CuttingEdge.Conditions;
using DevExpress.XtraEditors;
using LabMaestro.Controls.Win.CRM;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Infrastructure.Search;
using LabMaestro.Shared;
using Syncfusion.Windows.Forms.Grid;
using Syncfusion.Windows.Forms.Tools;

namespace LabMaestro.Controls.Win;

public partial class LabOrderDemographicsEditDialog : XtraForm, IExecutableDialog
{
    private const Keys KEY_SEARCH_POPUP = Keys.PageDown;
    private const Keys KEY_SEARCH_ACCEPT = Keys.Enter;
    private const Keys KEY_SEARCH_CANCEL = Keys.Escape;
    private const Keys KEY_SEARCH_QUICKSELECT = Keys.F8;
    private const int MAX_SEARCH_RESULTS = 100;

    private readonly bool _allowReferrerEdit;
    private readonly PatientLabOrder _currentLabOrder;
    private IEnumerable<AffiliateSlice> _affiliates = [];
    private IEnumerable<AssociateOrganizationSlice> _associateLabs = [];
    private IEnumerable<CorporateClientSlice> _corporateClients = [];
    private string _physicianSearchString;

    public LabOrderDemographicsEditDialog(long invoiceId, bool allowReferrerEdit)
        : this(FaultHandler.Shield(() => PatientLabOrdersRepository.FindById(invoiceId)), allowReferrerEdit) { }

    public LabOrderDemographicsEditDialog(PatientLabOrder currentLabOrder, bool allowReferrerEdit)
    {
        InitializeComponent();

        FaultHandler.Shield(() =>
        {
            _corporateClients = CorporateClientRepository.FetchActive();
            _associateLabs = AssociateOrganizationRepository.FetchActive();
            _affiliates = AffiliateRepository.FetchActive();
        });

        Condition.Requires(currentLabOrder).IsNotNull();
        Condition.Requires(currentLabOrder.IsCancelled).IsFalse();

        _currentLabOrder = currentLabOrder;
        _allowReferrerEdit = allowReferrerEdit;

        WinUtils.ApplyTheme(this);
        WinUtils.SetFormTitle(this, "Edit Order Demographic");
    }

    public bool ExecuteDialog(IWin32Window parent)
    {
        UpdateControls();
        ShowDialog(parent);
        return true;
    }

    public void UpdateControls()
    {
        lblInvoiceId.Text = _currentLabOrder.InvoiceId.ToString(CultureInfo.InvariantCulture);
        lblPatientId.Text = _currentLabOrder.OrderId;
        lblBookedBy.Text = _currentLabOrder.OrderingUserId != null
            ? FaultHandler.Shield(
                () => UsersRepository.GetUserDisplayName((short)_currentLabOrder.OrderingUserId))
            : string.Empty;
        lblBookedOn.Text = SharedUtilities.DateTimeToString(_currentLabOrder.OrderDateTime);

        cboPatientTitle.Text = _currentLabOrder.Title;
        txtFirstName.Text = _currentLabOrder.FirstName;
        txtLastName.Text = _currentLabOrder.LastName;
        txtTelephone.Text = _currentLabOrder.PhoneNumber;
        txtOrderNotes.Text = _currentLabOrder.OrderNotes;
        txtExtTracking.Text = _currentLabOrder.SubOrderTrackingId;
        txtEmail.Text = _currentLabOrder.EmailAddress;
        //chkEmailReport.Checked = _currentLabOrder.EmailTestResults;
        rgSex.SelectedIndex = (SexType)_currentLabOrder.Sex switch
        {
            SexType.Male   => 0,
            SexType.Female => 1,
            _              => 2
        };

        if (_currentLabOrder.DoB != null)
            dteDateOfBirth.EditValue = (DateTime)_currentLabOrder.DoB;
        else
            txtAge.Text = _currentLabOrder.Age;

        chkExtOrd.Checked = _currentLabOrder.IsExternalSubOrder;

        if (_allowReferrerEdit) {
            chkDisallowReferral.Checked = _currentLabOrder.DisallowReferral;
            chkUnknownPhysician.Checked = _currentLabOrder.IsReferrerUnknown;
            txtPhysicianCustomName.Text = _currentLabOrder.ReferrerCustomName;

            resetSearchComboControls(cboPhysician, grdListPhysician);
            refreshPhysiciansCatalog();
            if (_currentLabOrder.ReferrerId != null) {
                cboPhysician.Text =
                    ActiveReferringPhysiciansRepository.GetPhysicianName((int)_currentLabOrder.ReferrerId);
                grdListPhysician.SelectedValue = (int)_currentLabOrder.ReferrerId;
            }
        }
        else {
            chkUnknownPhysician.Enabled = false;
            txtPhysicianCustomName.Enabled = false;
            chkDisallowReferral.Enabled = false;
            cboPhysician.Enabled = false;
        }

        if (_currentLabOrder.CustomerId != null)
            txtUPIN.Text = CustomerRepository.FindById((long)_currentLabOrder.CustomerId).UPIN;

        comboBoxSetItems(cboCorporate, _corporateClients.ToArray());
        if (_currentLabOrder.CorporateClientId != null)
            cboCorporate.SelectedItem = _corporateClients.First(x => x.Id == _currentLabOrder.CorporateClientId);

        comboBoxSetItems(cboAssociateLab, _associateLabs.ToArray());
        chkExtOrd.Checked = _currentLabOrder.IsExternalSubOrder;
        txtExtTracking.Text = _currentLabOrder.AssociateLabAccessionId;
        if (_currentLabOrder.AssociateLabId != null)
            cboAssociateLab.SelectedItem = _associateLabs.First(x => x.Id == _currentLabOrder.AssociateLabId);

        comboBoxSetItems(cboAffiliate, _affiliates.ToArray());
        if (_currentLabOrder.AffiliateId != null)
            cboAffiliate.SelectedItem = _affiliates.First(x => x.Id == _currentLabOrder.AffiliateId);
    }

    private void comboBoxSetItems(ComboBoxEdit combo, object[] items)
    {
        combo.Clear();
        combo.SelectedIndex = -1;
        combo.Properties.Items.Clear();

        if (!items.Any()) return;

        try {
            combo.Properties.Items.BeginUpdate();
            combo.Properties.Items.AddRange(items);
        }
        finally {
            combo.Properties.Items.EndUpdate();
        }
    }

    private void btnClose_Click(object sender, EventArgs e) => DialogResult = DialogResult.Cancel;

    private void refreshPhysiciansCatalog()
    {
        FaultHandler.Shield(() => WaitFormControl.WaitOperation(this, () =>
        {
            var physicianCatalog = ActiveReferringPhysiciansRepository.GetCatalog(true);
            ReferrerSearchEngine.AssignCatalog(physicianCatalog);
        }));
    }

    private void updatePhysicianDropDownGrid(List<ReferrerSearchRec> items)
    {
        grdListPhysician.BeginUpdate();
        try {
            grdListPhysician.TopIndex = 0;
            grdListPhysician.SelectedIndex = -1;
            grdListPhysician.MultiColumn = true;
            grdListPhysician.SelectionMode = SelectionMode.One;
            grdListPhysician.DisplayMember = "Name";
            grdListPhysician.ValueMember = "Id";

            grdListPhysician.DataSource = items;
        }
        finally {
            grdListPhysician.EndUpdate();
        }
    }

    private void searchPhysicians()
    {
        if (string.IsNullOrEmpty(_physicianSearchString))
            _physicianSearchString = cboPhysician.Text.Trim().ToLowerInvariant();

        var searchRecs = string.IsNullOrEmpty(_physicianSearchString)
            ? ReferrerSearchEngine.GetCatalog()
            : ReferrerSearchEngine.Search($"*{_physicianSearchString}*", MAX_SEARCH_RESULTS);
        updatePhysicianDropDownGrid(searchRecs.Any() ? searchRecs : ReferrerSearchEngine.GetCatalog());
    }

    private void cboPhysician_TextChanged(object sender, EventArgs e) =>
        _physicianSearchString = cboPhysician.Text.Trim().ToLowerInvariant();

    private void applySelectedPhysician()
    {
        if (grdListPhysician.SelectedValue != null) {
            var id = (int)grdListPhysician.SelectedValue;
            _currentLabOrder.ReferrerId = id;
        }
        else {
            _currentLabOrder.ReferrerId = null;
        }
    }

    private void applySelectedReferrerId(int refId)
    {
        _currentLabOrder.ReferrerId = refId;
        grdListPhysician.SelectedValue = refId;
        cboPhysician.Text = _currentLabOrder.ReferrerId != null
            ? ActiveReferringPhysiciansRepository.GetPhysicianName((int)_currentLabOrder.ReferrerId)
            : string.Empty;
    }

    private void resetSearchComboControls(ComboBoxBase combo, GridListControl grid)
    {
        combo.Text = string.Empty;
        grid.SelectedIndex = -1;
    }

    private void handleSearchComboBoxKeyUp(ComboBoxBase combo, GridListControl grid, KeyEventArgs e,
                                           SearchEntityType entityType)
    {
        switch (e.KeyCode) {
            case KEY_SEARCH_QUICKSELECT:
                e.Handled = true;
                var refId = ReferrerQuickSelectionDialog.ExecuteDialog(this, false, true, entityType);
                if (refId > 0) applySelectedReferrerId(refId);

                break;

            case KEY_SEARCH_ACCEPT:
                if (grid.SelectedIndex != -1) {
                    combo.SuppressDropDownEvent = true;
                    combo.DroppedDown = false;
                    combo.SuppressDropDownEvent = false;
                }

                applySelectedPhysician();
                break;

            case KEY_SEARCH_POPUP:
                e.Handled = true;
                try {
                    combo.SuppressDropDownEvent = true;
                    searchPhysicians();
                    combo.DroppedDown = true;
                    combo.SuppressDropDownEvent = false;
                }
                catch {
                    combo.Text = string.Empty;
                }

                break;

            case KEY_SEARCH_CANCEL:
                e.Handled = true;
                combo.Text = string.Empty;
                grid.SelectedIndex = -1;
                _physicianSearchString = string.Empty;
                break;
        }
    }

    private void cboPhysician_KeyUp(object sender, KeyEventArgs e) =>
        handleSearchComboBoxKeyUp(cboPhysician, grdListPhysician, e, SearchEntityType.Physician);

    private void cboPhysician_DropDown(object sender, EventArgs e)
    {
        var text = cboPhysician.Text.Trim().ToLowerInvariant();
        if (string.IsNullOrEmpty(text))
            updatePhysicianDropDownGrid(ReferrerSearchEngine.GetCatalog());
        else
            searchPhysicians();
    }

    private int getSelectedReferrerId() =>
        grdListPhysician.SelectedValue != null ? (int)grdListPhysician.SelectedValue : -1;

    private void btnSave_Click(object sender, EventArgs e)
    {
        if (!validateInputControls()) return;

        _currentLabOrder.Title = cboPatientTitle.Text.Trim();
        _currentLabOrder.FirstName = txtFirstName.Text.Trim();
        _currentLabOrder.LastName = txtLastName.Text.Trim();
        _currentLabOrder.OrderNotes = txtOrderNotes.Text.Trim();
        _currentLabOrder.PhoneNumber = txtTelephone.Text.Trim();
        _currentLabOrder.SubOrderTrackingId = txtExtTracking.Text.Trim();
        _currentLabOrder.IsExternalSubOrder = chkExtOrd.Checked;

        var email = txtEmail.Text.Trim().ToLowerInvariant();
        if (!string.IsNullOrEmpty(_currentLabOrder.EmailAddress)) {
            _currentLabOrder.EmailAddress = email;
            //_currentLabOrder.EmailTestResults = chkEmailReport.Checked;
        }
        else {
            _currentLabOrder.EmailAddress = string.Empty;
            _currentLabOrder.EmailTestResults = false;
        }

        if (_allowReferrerEdit) {
            var refId = getSelectedReferrerId();

            _currentLabOrder.ReferrerCustomName = txtPhysicianCustomName.Text.Trim();
            _currentLabOrder.IsReferrerUnknown = chkUnknownPhysician.Checked /*|| refId <= 0*/;
            _currentLabOrder.DisallowReferral = chkDisallowReferral.Checked;

            if (_currentLabOrder.IsReferrerUnknown) {
                _currentLabOrder.ReferrerId = null;
            }
            else {
                if (_currentLabOrder.ReferrerId != refId)
                    if (refId > 0)
                        _currentLabOrder.ReferrerId = refId;
            }
        }

        switch (rgSex.SelectedIndex) {
            case 0:
                _currentLabOrder.Sex = (byte)SexType.Male;
                break;
            case 1:
                _currentLabOrder.Sex = (byte)SexType.Female;
                break;
            default:
                _currentLabOrder.Sex = (byte)SexType.Unknown;
                break;
        }

        if (!string.IsNullOrEmpty(txtAge.Text.Trim())) {
            _currentLabOrder.Age = txtAge.Text.Trim().ToUpperInvariant();
            _currentLabOrder.DoB = null;
        }
        else if (!string.IsNullOrEmpty(dteDateOfBirth.Text.Trim())) {
            _currentLabOrder.DoB = dteDateOfBirth.DateTime.Date;
            _currentLabOrder.Age = null;
        }

        FaultHandler.Shield(() => WaitFormControl.WaitOperation(this, () =>
        {
            //PatientLabOrdersRepository.Save();
            PatientLabOrdersRepository.UpdateLabOrderDemographic(_currentLabOrder);

            // fetch the latest version from database
            var details = PatientLabOrdersRepository.FetchLabOrderDetails(_currentLabOrder.InvoiceId);

            // this will update the last modified field as well the order-lever workflow
            PatientLabOrdersRepository.UpdateLabOrderWorkflowStage(details.InvoiceId,
                                                                   (WorkflowStageType)details.WorkflowStage);

            AuditTrailRepository.LogLabOrderEvent(details.InvoiceId,
                                                  AuditEventType.ordDemographicUpdated);
        }, "Saving data..."));

        MessageDlg.Info("Order changes saved to database");
        DialogResult = DialogResult.OK;
    }

    private bool validateInputControls()
    {
        if (string.IsNullOrEmpty(txtFirstName.Text.Trim())) {
            MessageDlg.Warning("Patients First Name cannot be empty!");
            txtFirstName.Select();
            return false;
        }

        if (rgSex.SelectedIndex == -1) {
            MessageDlg.Warning("Please select the Patients Sex!");
            rgSex.Focus();
            return false;
        }

        if (dteDateOfBirth.EditValue == null && string.IsNullOrEmpty(txtAge.Text.Trim())) {
            MessageDlg.Warning("Please enter Patients Age or DoB!");
            return false;
        }

        if (_allowReferrerEdit && _currentLabOrder.ReferrerId == null)
            if (chkUnknownPhysician.Checked)
                if (string.IsNullOrEmpty(txtPhysicianCustomName.Text.Trim())) {
                    MessageDlg.Warning("Referrer name cannot be empty!");
                    txtFirstName.Select();
                    return false;
                }

        var email = txtEmail.Text.Trim();
        if (!string.IsNullOrEmpty(email) && !SharedUtilities.IsValidEmail(email)) {
            MessageDlg.Warning("The provided e-mail is invalid!");
            return false;
        }

        return true;
    }

    private void LabOrderDemographicsEditDialog_KeyUp(object sender, KeyEventArgs e)
    {
        switch (e.KeyCode) {
            case Keys.Escape:
                //e.Handled = true;
                //DialogResult = DialogResult.OK;
                break;
            case Keys.F5:
                e.Handled = true;
                titleCaseTextBox(txtFirstName);
                titleCaseTextBox(txtLastName);
                break;
        }
    }

    private void titleCaseTextBox(TextEdit txtEdit)
    {
        var fname = txtEdit.Text.Trim();
        fname = SharedUtilities.TitleCase(fname);
        txtEdit.Text = fname;
    }

    private void txtLastName_KeyUp(object sender, KeyEventArgs e)
    {
        if (e.KeyCode == Keys.F5) {
            e.Handled = true;
            titleCaseTextBox(txtFirstName);
            titleCaseTextBox(txtLastName);
        }
    }

    private void btnFindCustomer_Click(object sender, EventArgs e)
    {
        var upin = txtUPIN.Text.Trim().ToUpperInvariant();
        var phone = txtTelephone.Text.Trim().ToLowerInvariant();
        using var form = new CustomerSearchForm();
        form.SetSearchTerms(upin, phone, string.Empty);
        if (form.ShowDialog(this) != DialogResult.OK) return;
        setCustomerId(form.SelectedCustomer);
        updateLabels();
    }

    private void setCustomerId(CustomerInfo? customer)
    {
        if (customer != null) {
            _currentLabOrder.CustomerId = customer.Id;
            txtUPIN.Text = customer.UPIN;
        }
        else {
            _currentLabOrder.CustomerId = null;
            txtUPIN.Text = string.Empty;
        }
    }

    private void cboAssociateLab_SelectedIndexChanged(object sender, EventArgs e)
    {
        switch (cboAssociateLab.SelectedItem) {
            case AssociateOrganizationSlice lab:
                setAssociateLabId(lab.Id);
                break;
            default:
                setAssociateLabId(null);
                break;
        }

        updateLabels();
    }

    private void setCorporateClientId(short? id) => _currentLabOrder.CorporateClientId = id is > 0 ? id : null;
    private void setAffiliatetId(short? id) => _currentLabOrder.AffiliateId = id is > 0 ? id : null;

    private void setAssociateLabId(short? id)
    {
        if (id is > 0) {
            _currentLabOrder.AssociateLabId = id;
            _currentLabOrder.AssociateLabAccessionId = txtExtTracking.Text.Trim();
            _currentLabOrder.IsExternalSubOrder = true;
            /*
            _currentLabOrder.RequestingLabId = id;
            _currentLabOrder.SubOrderTrackingId = txtExtTrackingId.Text.Trim();
            */
        }
        else {
            _currentLabOrder.AssociateLabId = null;
            _currentLabOrder.AssociateLabAccessionId = null;
            _currentLabOrder.IsExternalSubOrder = false;
            /*
            _currentLabOrder.RequestingLabId = null;
            _currentLabOrder.SubOrderTrackingId = null;
            */
        }
    }

    private void cboCorporate_SelectedIndexChanged(object sender, EventArgs e)
    {
        switch (cboCorporate.SelectedItem) {
            case CorporateClientSlice corp:
                setCorporateClientId(corp.Id);
                break;
            default:
                setCorporateClientId(null);
                break;
        }

        updateLabels();
    }

    private void LabOrderDemographicsEditDialog_Load(object sender, EventArgs e) { }

    private void updateLabels()
    {
        lblCustomer.Text = _currentLabOrder.CustomerId != null ? _currentLabOrder.CustomerId.ToString() : string.Empty;
        lblCorporate.Text = _currentLabOrder.CorporateClientId != null
            ? _currentLabOrder.CorporateClientId.ToString()
            : string.Empty;
        lblAssocLab.Text = _currentLabOrder.AssociateLabId != null
            ? _currentLabOrder.AssociateLabId.ToString()
            : string.Empty;
    }

    private void btnRemoveCustomer_Click(object sender, EventArgs e)
    {
        _currentLabOrder.CustomerId = null;
        txtUPIN.Text = null;
        updateLabels();
    }

    private void cboAffiliate_SelectedIndexChanged(object sender, EventArgs e)
    {
        switch (cboAffiliate.SelectedItem) {
            case AffiliateSlice aff:
                setAffiliatetId(aff.Id);
                break;
            default:
                setAffiliatetId(null);
                break;
        }

        updateLabels();
    }

    private void btnRemoveCorporate_Click(object sender, EventArgs e)
    {
        cboCorporate.SelectedIndex = -1;
        setCorporateClientId(null);
        updateLabels();
    }

    private void btnRemoveAssocLab_Click(object sender, EventArgs e)
    {
        cboAssociateLab.SelectedIndex = -1;
        setAssociateLabId(null);
        updateLabels();
    }

    private void btnRemoveAffiliate_Click(object sender, EventArgs e)
    {
        cboAffiliate.SelectedIndex = -1;
        setAffiliatetId(null);
        updateLabels();
    }

    private void handleComboQuickSelect(ComboBoxEdit combo, KeyEventArgs e, SearchEntityType entityType)
    {
        if (e.KeyCode != KEY_SEARCH_QUICKSELECT) return;

        e.Handled = true;
        var id = (short)ReferrerQuickSelectionDialog.ExecuteDialog(this, false, true, entityType);
        if (id <= 0) return;

        switch (entityType) {
            case SearchEntityType.AssociateLab:
                setAssociateLabId(id);
                cboAssociateLab.SelectedItem = _associateLabs.First(x => x.Id == id);
                break;
            case SearchEntityType.Corporate:
                setCorporateClientId(id);
                cboCorporate.SelectedItem = _corporateClients.First(x => x.Id == id);
                break;
            case SearchEntityType.Affiliate:
                setAffiliatetId(id);
                cboAffiliate.SelectedItem = _affiliates.First(x => x.Id == id);
                break;
        }
    }

    private void cboAssociateLab_KeyUp(object sender, KeyEventArgs e) =>
        handleComboQuickSelect(cboAssociateLab, e, SearchEntityType.AssociateLab);

    private void cboCorporate_KeyUp(object sender, KeyEventArgs e) =>
        handleComboQuickSelect(cboCorporate, e, SearchEntityType.Corporate);

    private void cboAffiliate_KeyUp(object sender, KeyEventArgs e) =>
        handleComboQuickSelect(cboAffiliate, e, SearchEntityType.Affiliate);
}