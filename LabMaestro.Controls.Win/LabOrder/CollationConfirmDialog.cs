﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2012, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: CollationConfirmDialog.cs 1097 2013-11-24 13:34:15Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System.Collections.Generic;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using LabMaestro.Domain;

namespace LabMaestro.Controls.Win
{
    public partial class CollationConfirmDialog : XtraForm
    {
        public CollationConfirmDialog()
        {
            InitializeComponent();
            WinUtils.ApplyTheme(this);
        }

        public List<ResultBundlesForInvoice> CollatableResultBundles { get; set; }

        public void UpdateControl()
        {
            clbBundlesList.Items.Clear();
            foreach (var bundleSlice in CollatableResultBundles)
            {
                var item = new CheckedListBoxItem(bundleSlice,
                                                  string.Format("[{0}] {1}",
                                                                bundleSlice.DisplayTitle,
                                                                bundleSlice.ComponentLabTests),
                                                  CheckState.Unchecked);
                clbBundlesList.Items.Add(item);
            }
        }

        public List<ResultBundlesForInvoice> GetSelectedBundles()
        {
            var list = new List<ResultBundlesForInvoice>();
            foreach (CheckedListBoxItem item in clbBundlesList.Items)
            {
                if (item.CheckState == CheckState.Checked)
                {
                    var bundle = item.Value as ResultBundlesForInvoice;
                    if (bundle != null)
                    {
                        list.Add(bundle);
                    }
                }
            }

            return list;
        }

        private void btnCollate_Click(object sender, System.EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        private void btnSelectAll_Click(object sender, System.EventArgs e)
        {
            foreach (CheckedListBoxItem item in clbBundlesList.Items)
            {
                item.CheckState = CheckState.Checked;
            }
        }
    }
}