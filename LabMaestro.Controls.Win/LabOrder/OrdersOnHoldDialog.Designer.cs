﻿namespace LabMaestro.Controls.Win
{
    partial class OrdersOnHoldDialog
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            Xceed.Grid.GradientMap gradientMap2 = new Xceed.Grid.GradientMap();
            Xceed.Grid.GradientStop gradientStop3 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientStop gradientStop4 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientMap gradientMap1 = new Xceed.Grid.GradientMap();
            Xceed.Grid.GradientStop gradientStop1 = new Xceed.Grid.GradientStop();
            Xceed.Grid.GradientStop gradientStop2 = new Xceed.Grid.GradientStop();
            this.btnDeleteOrder = new DevExpress.XtraEditors.SimpleButton();
            this.btnPerformOrder = new DevExpress.XtraEditors.SimpleButton();
            this.btnExit = new DevExpress.XtraEditors.SimpleButton();
            this.btnClearAllOrders = new DevExpress.XtraEditors.SimpleButton();
            this.grdHeldOrders = new Xceed.Grid.GridControl();
            this.dataRowTemplate1 = new Xceed.Grid.DataRow();
            this.visualGridElementStyle1 = new Xceed.Grid.VisualGridElementStyle();
            this.dataRowStyle1 = new Xceed.Grid.VisualGridElementStyle();
            this.columnManagerRow1 = new Xceed.Grid.ColumnManagerRow();
            ((System.ComponentModel.ISupportInitialize)(this.grdHeldOrders)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataRowTemplate1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.columnManagerRow1)).BeginInit();
            this.SuspendLayout();
            // 
            // btnDeleteOrder
            // 
            this.btnDeleteOrder.Appearance.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnDeleteOrder.Appearance.Options.UseFont = true;
            this.btnDeleteOrder.Image = global::LabMaestro.Controls.Win.Resources.cancel_16;
            this.btnDeleteOrder.Location = new System.Drawing.Point(159, 425);
            this.btnDeleteOrder.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.btnDeleteOrder.Name = "btnDeleteOrder";
            this.btnDeleteOrder.Size = new System.Drawing.Size(87, 28);
            this.btnDeleteOrder.TabIndex = 3;
            this.btnDeleteOrder.Text = "Delete";
            this.btnDeleteOrder.ToolTip = "Delete Currently Selected Order";
            this.btnDeleteOrder.Click += new System.EventHandler(this.btnDeleteOrder_Click);
            // 
            // btnPerformOrder
            // 
            this.btnPerformOrder.Appearance.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnPerformOrder.Appearance.Options.UseFont = true;
            this.btnPerformOrder.Image = global::LabMaestro.Controls.Win.Resources.list_accept;
            this.btnPerformOrder.Location = new System.Drawing.Point(14, 425);
            this.btnPerformOrder.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.btnPerformOrder.Name = "btnPerformOrder";
            this.btnPerformOrder.Size = new System.Drawing.Size(139, 28);
            this.btnPerformOrder.TabIndex = 2;
            this.btnPerformOrder.Text = "Perform Order";
            this.btnPerformOrder.ToolTip = "Perform Currently Selected Order";
            this.btnPerformOrder.Click += new System.EventHandler(this.btnPerformOrder_Click);
            // 
            // btnExit
            // 
            this.btnExit.Appearance.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnExit.Appearance.Options.UseFont = true;
            this.btnExit.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnExit.Image = global::LabMaestro.Controls.Win.Resources.exit_16;
            this.btnExit.Location = new System.Drawing.Point(603, 425);
            this.btnExit.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.btnExit.Name = "btnExit";
            this.btnExit.Size = new System.Drawing.Size(87, 28);
            this.btnExit.TabIndex = 1;
            this.btnExit.Text = "Close";
            this.btnExit.ToolTip = "Exit this Window";
            // 
            // btnClearAllOrders
            // 
            this.btnClearAllOrders.Appearance.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnClearAllOrders.Appearance.Options.UseFont = true;
            this.btnClearAllOrders.Image = global::LabMaestro.Controls.Win.Resources.clear;
            this.btnClearAllOrders.Location = new System.Drawing.Point(252, 425);
            this.btnClearAllOrders.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.btnClearAllOrders.Name = "btnClearAllOrders";
            this.btnClearAllOrders.Size = new System.Drawing.Size(87, 28);
            this.btnClearAllOrders.TabIndex = 4;
            this.btnClearAllOrders.Text = "Clear All";
            this.btnClearAllOrders.ToolTip = "Remove All Pending Orders";
            this.btnClearAllOrders.Click += new System.EventHandler(this.btnClearAllOrders_Click);
            // 
            // grdHeldOrders
            // 
            this.grdHeldOrders.ClipCurrentCellSelection = false;
            this.grdHeldOrders.DataRowTemplate = this.dataRowTemplate1;
            this.grdHeldOrders.DataRowTemplateStyles.Add(this.visualGridElementStyle1);
            this.grdHeldOrders.DataRowTemplateStyles.Add(this.dataRowStyle1);
            // 
            // 
            // 
            this.grdHeldOrders.ErrorVisualStyle.OverrideUIStyle = false;
            this.grdHeldOrders.FixedHeaderRows.Add(this.columnManagerRow1);
            this.grdHeldOrders.GridLineColor = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(239)))), ((int)(((byte)(255)))));
            // 
            // 
            // 
            this.grdHeldOrders.InactiveSelectionVisualStyle.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(229)))), ((int)(((byte)(229)))), ((int)(((byte)(229)))));
            this.grdHeldOrders.InactiveSelectionVisualStyle.ForeColor = System.Drawing.Color.Black;
            this.grdHeldOrders.InactiveSelectionVisualStyle.OverrideUIStyle = true;
            this.grdHeldOrders.Location = new System.Drawing.Point(12, 12);
            this.grdHeldOrders.Name = "grdHeldOrders";
            this.grdHeldOrders.OverrideUIStyle = false;
            this.grdHeldOrders.ReadOnly = true;
            // 
            // 
            // 
            this.grdHeldOrders.RowSelectorPane.AllowRowResize = false;
            gradientMap2.GradientMode = Xceed.UI.GradientMode.Horizontal;
            gradientStop3.Color = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(239)))), ((int)(((byte)(255)))));
            gradientStop3.Offset = 0D;
            gradientStop4.Color = System.Drawing.Color.FromArgb(((int)(((byte)(175)))), ((int)(((byte)(210)))), ((int)(((byte)(255)))));
            gradientStop4.Offset = 1D;
            gradientMap2.GradientStops.Add(gradientStop3);
            gradientMap2.GradientStops.Add(gradientStop4);
            this.grdHeldOrders.RowSelectorPane.GradientMap = gradientMap2;
            // 
            // 
            // 
            this.grdHeldOrders.RowSelectorPane.HotVisualStyle.OverrideUIStyle = true;
            this.grdHeldOrders.RowSelectorPane.OverrideUIStyle = true;
            this.grdHeldOrders.RowSelectorPane.Visible = true;
            this.grdHeldOrders.SelectionMode = System.Windows.Forms.SelectionMode.One;
            // 
            // 
            // 
            this.grdHeldOrders.SelectionVisualStyle.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(167)))), ((int)(((byte)(205)))), ((int)(((byte)(240)))));
            this.grdHeldOrders.SelectionVisualStyle.ForeColor = System.Drawing.Color.Black;
            this.grdHeldOrders.SelectionVisualStyle.OverrideUIStyle = true;
            this.grdHeldOrders.ShowFocusRectangle = false;
            this.grdHeldOrders.Size = new System.Drawing.Size(678, 406);
            this.grdHeldOrders.SynchronizeDetailGrids = false;
            this.grdHeldOrders.TabIndex = 0;
            this.grdHeldOrders.UIStyle = Xceed.UI.UIStyle.System;
            // 
            // visualGridElementStyle1
            // 
            // 
            // 
            // 
            this.visualGridElementStyle1.HotVisualStyle.OverrideUIStyle = true;
            this.visualGridElementStyle1.OverrideUIStyle = false;
            // 
            // dataRowStyle1
            // 
            this.dataRowStyle1.BackColor = System.Drawing.Color.Azure;
            // 
            // columnManagerRow1
            // 
            this.columnManagerRow1.AllowCellNavigation = false;
            this.columnManagerRow1.AllowColumnReorder = false;
            this.columnManagerRow1.AllowSort = false;
            this.columnManagerRow1.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            gradientStop1.Color = System.Drawing.Color.FromArgb(((int)(((byte)(252)))), ((int)(((byte)(253)))), ((int)(((byte)(255)))));
            gradientStop1.Offset = 0D;
            gradientStop2.Color = System.Drawing.Color.FromArgb(((int)(((byte)(196)))), ((int)(((byte)(221)))), ((int)(((byte)(255)))));
            gradientStop2.Offset = 1D;
            gradientMap1.GradientStops.Add(gradientStop1);
            gradientMap1.GradientStops.Add(gradientStop2);
            this.columnManagerRow1.GradientMap = gradientMap1;
            // 
            // 
            // 
            this.columnManagerRow1.HotVisualStyle.OverrideUIStyle = false;
            this.columnManagerRow1.OverrideUIStyle = true;
            // 
            // OrdersOnHoldDialog
            // 
            this.Appearance.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 16F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(702, 463);
            this.ControlBox = false;
            this.Controls.Add(this.grdHeldOrders);
            this.Controls.Add(this.btnClearAllOrders);
            this.Controls.Add(this.btnExit);
            this.Controls.Add(this.btnPerformOrder);
            this.Controls.Add(this.btnDeleteOrder);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "OrdersOnHoldDialog";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "Orders On Hold";
            this.Load += new System.EventHandler(this.OrdersOnHoldDialog_Load);
            ((System.ComponentModel.ISupportInitialize)(this.grdHeldOrders)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataRowTemplate1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.columnManagerRow1)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.SimpleButton btnDeleteOrder;
        private DevExpress.XtraEditors.SimpleButton btnPerformOrder;
        private DevExpress.XtraEditors.SimpleButton btnExit;
        private DevExpress.XtraEditors.SimpleButton btnClearAllOrders;
        private Xceed.Grid.GridControl grdHeldOrders;
        private Xceed.Grid.DataRow dataRowTemplate1;
        private Xceed.Grid.ColumnManagerRow columnManagerRow1;
        private Xceed.Grid.VisualGridElementStyle visualGridElementStyle1;
        private Xceed.Grid.VisualGridElementStyle dataRowStyle1;
    }
}