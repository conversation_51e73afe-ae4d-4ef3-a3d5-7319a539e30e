﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2012, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: CollationFilterDialog.cs 516 2013-04-17 08:03:02Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;

namespace LabMaestro.Controls.Win
{
    public partial class CollationFilterDialog : XtraForm
    {
        public CollationFilterDialog()
        {
            InitializeComponent();
            WinUtils.ApplyTheme(this);
        }

        public char[] SelectedFilterItems { get; set; }

        public void UpdateControls()
        {
            clearCheckedItems();
            setCheckedItems(SelectedFilterItems);
        }

        private void btnApply_Click(object sender, EventArgs e)
        {
            SelectedFilterItems = getSelectedFilters();
            DialogResult = DialogResult.OK;
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            clearCheckedItems();
        }

        private void clearCheckedItems()
        {
            for (int i = 0; i < lbPrefixes.ItemCount; i++)
            {
                lbPrefixes.SetItemCheckState(i, CheckState.Unchecked);
            }
        }

        private CheckedListBoxItem getListItem(char alphabet)
        {
            var count = lbPrefixes.ItemCount;
            for (int i = 0; i < count; i++)
            {
                if (lbPrefixes.Items[i].Description.ToCharArray()[0].CompareTo(alphabet) == 0)
                    return lbPrefixes.Items[i];
            }
            return null;
        }

        private char[] getSelectedFilters()
        {
            var result = new List<char>();
            var count = lbPrefixes.ItemCount;
            for (int i = 0; i < count; i++)
            {
                var item = lbPrefixes.Items[i];
                if (item.CheckState == CheckState.Checked)
                {
                    result.Add(item.Description.ToCharArray()[0]);
                }
            }
            return result.ToArray();
        }

        private void setCheckedItems(char[] filters)
        {
            foreach (var alpha in filters)
            {
                var lbItem = getListItem(alpha);
                if (lbItem != null)
                {
                    lbItem.CheckState = CheckState.Checked;
                }
            }
        }
    }
}