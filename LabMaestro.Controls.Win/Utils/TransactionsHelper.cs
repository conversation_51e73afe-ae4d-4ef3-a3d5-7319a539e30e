﻿using System.Collections.Generic;
using System.Windows.Forms;
using LabMaestro.BusinessLogic;
using LabMaestro.Domain;
using LabMaestro.Shared;
using Xceed.Grid;

namespace LabMaestro.Controls.Win.Utils;

public sealed class TransactionsHelper
{
    public static void UpdateTransactionsGrid(GridControl grid, List<TransactionInfoSlice> transactions,
        bool hasLabOrder,
        MouseEventHandler showToolTip)
    {
        grid.BeginInit();
        try
        {
            grid.DataRows.Clear();
            if (hasLabOrder)
                foreach (var tx in transactions)
                {
                    var row = grid.DataRows.AddNew();
                    row.Cells[@"type"].Value = EnumUtils.EnumDescription(tx.TxType);
                    row.Cells[@"type"].BackColor = WinUtils.GetTransactionColor(tx.TxType);
                    row.Cells[@"amount"].Value = SharedUtilities.MoneyToString(tx.TxAmount, false);
                    row.Cells[@"noncash"].Value = SharedUtilities.MoneyToString(tx.NonCashAmount, false);
                    //row.Cells[@"date"].Value = SharedUtilities.DateToStringShort(transaction.TxTime);
                    //row.Cells[@"time"].Value = SharedUtilities.TimeToString(transaction.TxTime);
                    row.Cells[@"time"].Value = SharedUtilities.DateTimeToString24(tx.TxTime);
                    row.Cells[@"user"].Value = tx.StaffName;
                    row.Cells[@"auth"].Value = tx.AuthorizerName;
                    row.Cells[@"pmt_mode"].Value = EnumUtils.EnumDescription(tx.PaymentMethod);
                    row.Cells[@"notes"].Value = tx.UserRemarks;
                    row.Cells[@"pmt_ref"].Value = tx.PaymentReference;

                    if (showToolTip != null)
                        grid.GridWireCellToolTips(row.Cells, showToolTip);

                    row.Height = 21;
                    row.VerticalAlignment = VerticalAlignment.Center;
                    row.Tag = tx;
                    row.EndEdit();
                }
        }
        finally
        {
            grid.EndInit();
        }
    }

    public static void CreateGridColumns(GridControl grid)
    {
        grid.BeginInit();
        try
        {
            grid.Columns.Clear();
            var i = 0;
            //grdTransactionHistory.GridAddColumn(@"date", "Date", i++, 80);
            grid.GridAddColumn(@"time", "Time", i++, 150);
            grid.GridAddColumn(@"type", "Type", i++, 105);
            grid.GridAddColumn(@"amount", "Cash", i++, 70);
            grid.GridAddColumn(@"noncash", "Cash-less", i++, 70);
            grid.GridAddColumn(@"user", "Performed By", i++, 90);
            grid.GridAddColumn(@"auth", "Auth. By", i++, 90);
            grid.GridAddColumn(@"pmt_mode", "Pmt. Mode", i++, 60);
            grid.GridAddColumn(@"notes", "Notes", i, 90);
            grid.GridAddColumn(@"pmt_ref", "Pmt. Ref.", i, 125);
        }
        finally
        {
            grid.EndInit();
        }
    }


    public static void UpdateTransactionsGrid(GridControl grid, List<InvoiceTransactionDetailedExSlice> transactions,
        bool hasLabOrder,
        MouseEventHandler showToolTip)
    {
        grid.BeginInit();
        try
        {
            grid.DataRows.Clear();
            if (hasLabOrder)
                foreach (var tx in transactions)
                {
                    var row = grid.DataRows.AddNew();
                    row.Cells[@"type"].Value = EnumUtils.EnumDescription((InvoiceTransactionType)tx.TxType);
                    row.Cells[@"type"].BackColor = WinUtils.GetTransactionColor((InvoiceTransactionType)tx.TxType);
                    row.Cells[@"amount"].Value = SharedUtilities.MoneyToString(tx.TxAmount, false);
                    row.Cells[@"noncash"].Value = SharedUtilities.MoneyToString(tx.NonCashAmount, false);
                    //row.Cells[@"date"].Value = SharedUtilities.DateToStringShort(transaction.TxTime);
                    //row.Cells[@"time"].Value = SharedUtilities.TimeToString(transaction.TxTime);
                    row.Cells[@"time"].Value = SharedUtilities.DateTimeToString24(tx.TxTime);
                    row.Cells[@"user"].Value = tx.StaffName;
                    row.Cells[@"auth"].Value = tx.AuthorizerName;
                    row.Cells[@"pmt_mode"].Value = EnumUtils.EnumDescription((PaymentMethod)tx.PaymentMethod);
                    row.Cells[@"notes"].Value = tx.UserRemarks;
                    row.Cells[@"pmt_ref"].Value = tx.PaymentReference;

                    if (showToolTip != null)
                        grid.GridWireCellToolTips(row.Cells, showToolTip);

                    row.Height = 21;
                    row.VerticalAlignment = VerticalAlignment.Center;
                    row.Tag = tx;
                    row.EndEdit();
                }
        }
        finally
        {
            grid.EndInit();
        }
    }
}