﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2015, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id$
// 
// Last modified: 2015.08.30 12:40 PM
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using DevExpress.Utils;
using DevExpress.Utils.Menu;
using DevExpress.XtraBars;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraEditors.Repository;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraGrid.Views.Grid;
using LabMaestro.BusinessLogic;
using LabMaestro.BusinessLogic.LabOrderSearch;
using LabMaestro.Controls.Win.LabOrder;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Infrastructure.Search;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.Controls.Win;

public partial class InvoiceSearchForm : XtraForm, IExecutableDialog
{
    private const WorkflowStageType FINANCIAL_CUTOFF_STAGE = WorkflowStageType.Canceled;
    private DateTime _dateEnd;
    private DateTime _dateStart;
    private bool _ignoreCheckChangeEvent;
    private InvoiceSearchMode _searchMode;
    private ILabOrderSearchProvider _searchProvider;
    private int _selectedReferrerId;
    private IEnumerable<CorporateClientSlice> _corporateClients = [];
    private IEnumerable<AffiliateSlice> _affiliates = [];
    private IEnumerable<AssociateOrganizationSlice> _associateLabs = [];
    private short _associateLabId = -1;
    private short _corporateId = -1;


    public InvoiceSearchForm()
    {
        InitializeComponent();
        _searchMode = InvoiceSearchMode.DateRange;
        WinUtils.SetFormTitle(this, "Search Invoices");
        WinUtils.ApplyTheme(this);

        FaultHandler.Shield(PatientLabOrdersRepository.Reset);
        refreshAccessoryCatalogs();
        CachedRolePermissionChecker.PrefetchCache(true);
        applyControlPermissions();
        createGridViewImageColumns();
        resetAllControls();
        toggleTimeRangeFilterControls(false);
        updateOrderDetailsPanel(null);
    }

    private void refreshAccessoryCatalogs()
    {
        FaultHandler.Shield(
            () => WaitFormControl.WaitOperation(this,
                                                () =>
                                                {
                                                    _corporateClients = CorporateClientRepository.FetchActive();
                                                    _affiliates = AffiliateRepository.FetchActive();
                                                    _associateLabs = AssociateOrganizationRepository.FetchActive();
                                                }
            )
        );
    }

    public bool ExecuteDialog(IWin32Window parent)
    {
        UpdateControls();
        ShowDialog(parent);
        return true;
    }

    public void UpdateControls()
    {
        // nothing to do
    }

    private void createGridViewImageColumns()
    {
        var sexImages = new ImageCollection();
        sexImages.AddImage(Resources.questionb_16);
        sexImages.AddImage(Resources.male_16);
        sexImages.AddImage(Resources.female_16);

        var sexImageCombo = grdInvoices.RepositoryItems.Add("ImageComboBoxEdit") as RepositoryItemImageComboBox;
        sexImageCombo.SmallImages = sexImages;
        sexImageCombo.Items.Add(new ImageComboBoxItem(SexType.Unknown, 0));
        sexImageCombo.Items.Add(new ImageComboBoxItem(SexType.Male, 1));
        sexImageCombo.Items.Add(new ImageComboBoxItem(SexType.Female, 2));
        sexImageCombo.GlyphAlignment = HorzAlignment.Center;
        gvwInvoices.Columns[@"Sex"].ColumnEdit = sexImageCombo;

        var cancelImages = new ImageCollection();
        cancelImages.AddImage(Resources.cancel2_16);

        var cancelImageCombo = grdInvoices.RepositoryItems.Add("ImageComboBoxEdit") as RepositoryItemImageComboBox;
        cancelImageCombo.SmallImages = cancelImages;
        cancelImageCombo.Items.Add(new ImageComboBoxItem(true, 0));
        cancelImageCombo.GlyphAlignment = HorzAlignment.Center;
        gvwInvoices.Columns[@"IsCancelled"].ColumnEdit = cancelImageCombo;
        gvwInvoices.Columns[@"DisallowReferral"].ColumnEdit = cancelImageCombo;
        gvwInvoices.Columns[@"IsReferrerUnknown"].ColumnEdit = cancelImageCombo;

        var externalImages = new ImageCollection();
        externalImages.AddImage(Resources.truck_arrow_16);

        var externalImageCombo = grdInvoices.RepositoryItems.Add("ImageComboBoxEdit") as RepositoryItemImageComboBox;
        externalImageCombo.SmallImages = externalImages;
        externalImageCombo.Items.Add(new ImageComboBoxItem(true, 0));
        externalImageCombo.GlyphAlignment = HorzAlignment.Center;
        gvwInvoices.Columns[@"IsExternalSubOrder"].ColumnEdit = externalImageCombo;
    }

    private void applyControlPermissions()
    {
        btnFacesheet.Enabled = CachedRolePermissionChecker.CheckPermission(PermissionCodes.Workflow.PrintFacesheet);
        //btnCancelLabOrder.Enabled = CachedRolePermissionChecker.CheckPermission(PermissionCodes.LabOrders.Edit);

        btnPrintInvoice.Enabled = CachedRolePermissionChecker.CheckPermission(PermissionCodes.LabOrders.ReprintSlips);
        btnPrintReqFoil.Enabled = CachedRolePermissionChecker.CheckPermission(PermissionCodes.LabOrders.ReprintSlips);

        btnPatientDemographicsEdit.Enabled = CachedRolePermissionChecker.CheckPermission(PermissionCodes.LabOrders.EditDemographic);
        btnAuditTrail.Enabled = CachedRolePermissionChecker.CheckPermission(PermissionCodes.LabOrders.ViewAuditTrail);
        btnTxHistory.Enabled = CachedRolePermissionChecker.CheckPermission(PermissionCodes.LabOrders.ViewTransactions);

        btnEditLabOrder.Enabled = CachedRolePermissionChecker.CheckPermission(PermissionCodes.LabOrders.Edit);
        btnDiscount.Enabled = CachedRolePermissionChecker.CheckPermission(PermissionCodes.Finances.IssueDiscount);
        btnDiscountRebate.Enabled = CachedRolePermissionChecker.CheckPermission(PermissionCodes.Finances.IssueDiscountRebate);
        btnRefund.Enabled = CachedRolePermissionChecker.CheckPermission(PermissionCodes.Finances.IssueRefund);
    }

    private void btnSearch_Click(object sender, EventArgs e) => performSearch();

    private void performSearch()
    {
        _searchProvider = null;
        if (_searchMode != InvoiceSearchMode.InvoiceId && !validateDateRange()) return;
        FaultHandler.Shield(PatientLabOrdersRepository.Reset);
        switch (_searchMode) {
            case InvoiceSearchMode.DateRange:
                _searchProvider = new LabOrderSearchByDateRangeProvider(_dateStart, _dateEnd);
                break;

            case InvoiceSearchMode.InvoiceId:
                if (!long.TryParse(txtInvoiceId.Text.Trim(), out var invoiceId) || invoiceId <= 0) {
                    MessageDlg.Error("Invalid Invoice ID provided!");
                    return;
                }

                _searchProvider = new LabOrderSearchByInvoiceIdProvider(invoiceId);
                break;

            case InvoiceSearchMode.OrderId:
                var orderId = txtOrderNum.Text.Trim().ToUpperInvariant();
                if (string.IsNullOrEmpty(orderId)) {
                    MessageDlg.Error("Invalid or empty Order ID provided!");
                    return;
                }

                _searchProvider = new LabOrderSearchByPatientIdAndDateRangeProvider(orderId, _dateStart, _dateEnd);
                break;

            case InvoiceSearchMode.PhoneNumber:
                var phone = SharedUtilities.SanitizeMobileNumber(txtPhoneNumber.Text.Trim());
                _searchProvider = new LabOrderSearchByPhoneAndDateRangeProvider(phone, _dateStart, _dateEnd);
                break;

            case InvoiceSearchMode.CustomerId:
                var upin = txtCustomerUpin.Text.Trim().ToUpperInvariant();
                if (string.IsNullOrEmpty(upin)) {
                    MessageDlg.Error("Invalid or empty Personal Health Number provided!");
                    return;
                }

                _searchProvider = new LabOrderSearchByCustomerUPINAndDateRangeProvider(upin, _dateStart, _dateEnd);
                break;

            case InvoiceSearchMode.PatientName:
                var ptName = txtPatientName.Text.Trim().ToUpperInvariant();
                if (string.IsNullOrEmpty(ptName)) {
                    MessageDlg.Error("Invalid or empty Patient Name provided!");
                    return;
                }

                //TODO: implement
                //_searchProvider = new LabOrderSearchByDateRangeProvider(_dateStart, _dateEnd);
                MessageDlg.Info("Searching by Patient Name is not implemented yet!");
                break;

            case InvoiceSearchMode.Referrer:
                if (_selectedReferrerId <= 0) {
                    MessageDlg.Error("No referrer selected!");
                    return;
                }

                _searchProvider = new LabOrderSearchByReferrerIdAndDateRangeProvider(_selectedReferrerId, _dateStart, _dateEnd);
                break;

            case InvoiceSearchMode.WorkflowStage:
                if (cboWorkflowStatus.SelectedIndex == -1) {
                    MessageDlg.Error("No workflow stage selected!");
                    return;
                }

                //var wfStart = WorkflowStageType.OrderEntry;
                //var wfEnd = getSelectedWorkflowStage();

                var tuple = getSelectedWorkflowStages();
                var wfStart = tuple.Item1;
                var wfEnd = tuple.Item2;

                if (wfEnd == WorkflowStageType.RepeatProcedure) {
                    _searchProvider = new LabOrderSearchByDateRangeAndResultBundleWorkflowProvider(
                        wfStart,
                        _dateStart,
                        _dateEnd);
                }
                else {
                    _searchProvider = new LabOrderSearchByDateRangeAndResultBundleWorkflowBetweenProvider(
                        wfStart,
                        wfEnd,
                        _dateStart,
                        _dateEnd);
                }

                break;

            case InvoiceSearchMode.AssocLab:
                if (_associateLabId <= 0) {
                    MessageDlg.Error("No lab selected!");
                    return;
                }

                _searchProvider = new LabOrderSearchByAssocLabDateRangeProvider(_associateLabId, _dateStart, _dateEnd);
                break;

            case InvoiceSearchMode.Corporate:
                if (_corporateId <= 0) {
                    MessageDlg.Error("No corporate client selected!");
                    return;
                }

                _searchProvider = new LabOrderSearchByCorporateDateRangeProvider(_corporateId, _dateStart, _dateEnd);
                break;
        }

        if (_searchProvider == null) return;

        labOrderSearchResultSliceBindingSource.DataSource = null;
        activeResultBundleSliceBindingSource.DataSource = null;

        IEnumerable<SearchedLabOrderInfo> items = null;
        FaultHandler.Shield(() => WaitFormControl.WaitOperation(this,
                                                                () => items = _searchProvider.SearchLabOrders(),
                                                                "Searching..."));
        labOrderSearchResultSliceBindingSource.DataSource = items;
    }

    private WorkflowStageType getSelectedWorkflowStage() =>
        cboWorkflowStatus.SelectedIndex switch
        {
            0 => WorkflowStageType.ReportFinalization, // Pending Collation
            1 => WorkflowStageType.ReportCollation, // Pending Dispatch
            2 => WorkflowStageType.RepeatProcedure, // Repeat Procedure
            3 => WorkflowStageType.ResultEntry, // Pending Result Entry
            4 => WorkflowStageType.ResultValidation, // Pending Validation
            5 => WorkflowStageType.ReportFinalization, // Pending Finalization
            _ => WorkflowStageType.ReportFinalization
        };

    private Tuple<WorkflowStageType, WorkflowStageType> getSelectedWorkflowStages()
    {
        switch (cboWorkflowStatus.SelectedIndex) {
            case 0:
                // Pending Result Entry
                return new Tuple<WorkflowStageType, WorkflowStageType>(WorkflowStageType.OrderEntry, WorkflowStageType.SpecimenLabelPrint);
            case 1:
                // Pending Validation
                return new Tuple<WorkflowStageType, WorkflowStageType>(WorkflowStageType.ResultEntry, WorkflowStageType.HL7ResultImport);
            case 2:
                // Pending Finalization
                return new Tuple<WorkflowStageType, WorkflowStageType>(WorkflowStageType.ResultValidation, WorkflowStageType.ResultValidation);
            case 3:
                // Pending Collation
                return new Tuple<WorkflowStageType, WorkflowStageType>(WorkflowStageType.ReportFinalization, WorkflowStageType.ReportFinalization);
            case 4:
                // Pending Dispatch
                return new Tuple<WorkflowStageType, WorkflowStageType>(WorkflowStageType.ReportCollation, WorkflowStageType.ReportCollation);
            case 5:
                // Repeat Procedure
                return new Tuple<WorkflowStageType, WorkflowStageType>(WorkflowStageType.RepeatProcedure, WorkflowStageType.RepeatProcedure);
            default:
                return new Tuple<WorkflowStageType, WorkflowStageType>(WorkflowStageType.OrderEntry, WorkflowStageType.ReportFinalization);
        }
    }

    private void populateResultBundlesGrid(long invoiceId)
    {
        if (invoiceId <= 0) return;

        List<ActiveResultBundleSlice> slices = null;
        WaitFormControl.WaitOperation(
            this,
            () => { slices = CachedResultBundleSearchProvider.GetResultBundles(invoiceId); },
            "Searching...");
        activeResultBundleSliceBindingSource.DataSource = slices;
    }

    private void gvwInvoices_FocusedRowChanged(object sender, FocusedRowChangedEventArgs e)
    {
        var invoice = getSelectedInvoice();
        if (invoice != null) {
            populateResultBundlesGrid(invoice.InvoiceId);
        }

        updateOrderDetailsPanel(invoice);
    }

    private void updateOrderDetailsPanel(SearchedLabOrderInfo? invoice)
    {
        if (invoice == null) {
            lblDetailsAffiliate.Text = string.Empty;
            lblDetailsCorporate.Text = string.Empty;
            lblDetailsCustomer.Text = string.Empty;
            lblDetailsAssocLabTracking.Text = string.Empty;
            lblDetailsAssocLab.Text = string.Empty;
        }
        else {
            lblDetailsAffiliate.Text = invoice.AffiliateName;
            lblDetailsCorporate.Text = invoice.CorporateClientName;
            lblDetailsCustomer.Text = invoice.CustomerUPIN;
            lblDetailsAssocLabTracking.Text = invoice.AssociateLabName;
            lblDetailsAssocLab.Text = invoice.AssociateLabAccessionId;
        }
    }

    private void gvwInvoices_CustomColumnDisplayText(object sender, CustomColumnDisplayTextEventArgs e)
    {
        if (e.Column.FieldName == "WorkflowStage") {
            var wfStage = (WorkflowStageType)e.Value;
            e.DisplayText = EnumUtils.EnumDescription(wfStage);
        }
    }

    private long getSelectedInvoiceId()
    {
        if (gvwInvoices.SelectedRowsCount == 1 && gvwInvoices.GetFocusedRow() is SearchedLabOrderInfo item) {
            return item.InvoiceId;
        }

        return -1;
    }

    private SearchedLabOrderInfo? getSelectedInvoice() =>
        gvwInvoices.SelectedRowsCount == 1
            ? gvwInvoices.GetFocusedRow() as SearchedLabOrderInfo
            : null;

    private long getSelectedResultBundleId()
    {
        if (gvwResultBundles.SelectedRowsCount == 1) {
            var item = gvwResultBundles.GetFocusedRow() as ActiveResultBundleSlice;
            if (item != null) {
                return item.Id;
            }
        }

        return -1;
    }

    private bool hasSelectedInvoice() => getSelectedInvoiceId() > 0;

    private void disableTextControl(TextEdit txt)
    {
        txt.Text = string.Empty;
        txt.Enabled = false;
    }

    private void enableTextControl(TextEdit txt)
    {
        txt.Enabled = true;
        txt.Select();
        txt.Focus();
    }

    private void resetSearchControls()
    {
        disableTextControl(txtInvoiceId);
        disableTextControl(txtOrderNum);
        disableTextControl(txtPatientName);
        disableTextControl(txtPhoneNumber);
        disableTextControl(txtCustomerUpin);

        lblReferrer.Text = string.Empty;
        btnSelectReferrer.Enabled = false;

        lblCorporate.Text = string.Empty;
        btnCorporate.Enabled = false;

        lblAssocLab.Text = string.Empty;
        btnAssocLab.Enabled = false;

        cboWorkflowStatus.SelectedIndex = -1;
        cboWorkflowStatus.Enabled = false;

        chkSearchInvoiceId.Checked = false;
        chkSearchOrderId.Checked = false;
        chkSearchBundleStage.Checked = false;
        chkSearchPatientName.Checked = false;
        chkSearchReferrer.Checked = false;
        chkSearchCorporate.Checked = false;
        chkSearchAssocLab.Checked = false;
        chkSearchPhone.Checked = false;
        chkSearchCustomer.Checked = false;
    }

    private void checkedChangeHandler(object sender, EventArgs e)
    {
        if (_ignoreCheckChangeEvent) return;
        var ticked = (sender as CheckEdit).Checked;
        var tag = (string)(sender as CheckEdit).Tag;
        _searchMode = (InvoiceSearchMode)byte.Parse(tag);

        _ignoreCheckChangeEvent = true;
        resetSearchControls();

        if (ticked) {
            switch (_searchMode) {
                case InvoiceSearchMode.InvoiceId:
                    chkSearchInvoiceId.Checked = true;
                    enableTextControl(txtInvoiceId);
                    break;
                case InvoiceSearchMode.OrderId:
                    chkSearchOrderId.Checked = true;
                    enableTextControl(txtOrderNum);
                    break;
                case InvoiceSearchMode.PatientName:
                    chkSearchPatientName.Checked = true;
                    enableTextControl(txtPatientName);
                    break;
                case InvoiceSearchMode.PhoneNumber:
                    chkSearchPhone.Checked = true;
                    enableTextControl(txtPhoneNumber);
                    break;
                case InvoiceSearchMode.CustomerId:
                    chkSearchCustomer.Checked = true;
                    enableTextControl(txtCustomerUpin);
                    break;
                case InvoiceSearchMode.Referrer:
                    chkSearchReferrer.Checked = true;
                    btnSelectReferrer.Enabled = true;
                    break;
                case InvoiceSearchMode.Corporate:
                    chkSearchCorporate.Checked = true;
                    btnCorporate.Enabled = true;
                    break;
                case InvoiceSearchMode.AssocLab:
                    chkSearchAssocLab.Checked = true;
                    btnAssocLab.Enabled = true;
                    break;
                case InvoiceSearchMode.WorkflowStage:
                    chkSearchBundleStage.Checked = true;
                    cboWorkflowStatus.Enabled = true;
                    cboWorkflowStatus.Focus();
                    break;
            }
        }
        else {
            _searchMode = InvoiceSearchMode.DateRange;
            switch (_searchMode) {
                case InvoiceSearchMode.InvoiceId:
                    break;
                case InvoiceSearchMode.OrderId:
                    break;
                case InvoiceSearchMode.PatientName:
                    break;
                case InvoiceSearchMode.Referrer:
                    _selectedReferrerId = -1;
                    break;
                case InvoiceSearchMode.WorkflowStage:
                    break;
            }
        }

        _ignoreCheckChangeEvent = false;
    }

    private void btnClear_Click(object sender, EventArgs e)
    {
        resetAllControls();
    }

    private void resetAllControls()
    {
        _searchMode = InvoiceSearchMode.DateRange;
        _searchProvider = null;
        _selectedReferrerId = -1;

        _ignoreCheckChangeEvent = true;
        resetSearchControls();
        chkSearchInvoiceId.Checked = false;
        chkSearchOrderId.Checked = false;
        chkSearchBundleStage.Checked = false;
        chkSearchPatientName.Checked = false;
        chkSearchReferrer.Checked = false;
        chkSearchCorporate.Checked = false;
        chkSearchAssocLab.Checked = false;
        chkApplyTimeFilter.Checked = false;
        _ignoreCheckChangeEvent = false;

        dtFrom.DateTime = DateTime.Now;
        dtTo.DateTime = DateTime.Now;
        dtTimeFrom.Time = DateTime.Now;
        dtTimeTo.Time = DateTime.Now;

        updateOrderDetailsPanel(null);
    }

    private void btnSelectReferrer_Click(object sender, EventArgs e)
    {
        _selectedReferrerId = ReferrerQuickSelectionDialog.ExecuteDialog(this, false, true, SearchEntityType.Physician);
        if (_selectedReferrerId > 0) {
            var referrer = ReferrersRepository.FindById(_selectedReferrerId);
            if (referrer != null) lblReferrer.Text = referrer.FullName;
        }
    }

    private static DateTime constructDateTime(DateTime dtPart, DateTime tmPart) =>
        new(dtPart.Year, dtPart.Month, dtPart.Day, tmPart.Hour, tmPart.Minute, 0);

    private bool validateDateRange()
    {
        if (chkApplyTimeFilter.Checked) {
            _dateStart = constructDateTime(_dateStart, dtTimeFrom.Time);
            _dateEnd = SharedUtilities.LastMinuteOfDay(constructDateTime(_dateEnd, dtTimeTo.Time));
        }
        else {
            _dateStart = dtFrom.DateTime.Date;
            _dateEnd = SharedUtilities.LastMinuteOfDay(dtTo.DateTime);
        }

        dtFrom.DateTime = _dateStart;
        dtTo.DateTime = _dateEnd;

        var result = _dateStart < _dateEnd;
        if (!result) {
            MessageDlg.Error("Please select a valid date-range");
        }

        return result;
    }

    private void btnClose_ItemClick(object sender, ItemClickEventArgs e) => Close();

    private bool orderCanBeModified(SearchedLabOrderInfo order, WorkflowStageType wfThreshold)
    {
        if (order == null) {
            MessageDlg.Error("No order selected. Please select a valid lab order.");
            return false;
        }

        if (wfThreshold != WorkflowStageType.Unknown) {
            if (order.IsCancelled) {
                MessageDlg.Error("Order has already been cancelled.");
                return false;
            }

            if (order.WorkflowStage >= wfThreshold) {
                MessageDlg.Error(
                    "Order was fulfilled.\nOrder modification beyond fulfillment stage is not permitted.");
                return false;
            }
        }

        return true;
    }

    private void showInvoiceReqSlipPrintDialog(bool printReqSlips)
    {
        var order = getSelectedInvoice();
        FaultHandler.Shield(PatientLabOrdersRepository.Reset);
        if (printReqSlips) {
            if (!orderCanBeModified(order, WorkflowStageType.OrderFulfillment)) return;
        }

        var reason = InputDialog.ExecuteDialog(this, "Specify reason for reprint:");
        if (string.IsNullOrEmpty(reason)) {
            MessageDlg.Warning("A valid reason must be specified to perform reprint.\nOperation aborted.");
            return;
        }

        new PreviewPrintDialog(order.InvoiceId, printReqSlips, reason).ExecuteDialog(this);
        var evType = printReqSlips ? AuditEventType.ordReqSlipPrinted : AuditEventType.ordInvoicePrinted;
        AuditTrailRepository.LogLabOrderEvent(order.InvoiceId, evType, note: reason);
    }

    private void btnPrintInvoice_ItemClick(object sender, ItemClickEventArgs e) =>
        showInvoiceReqSlipPrintDialog(false);

    private void btnPrintReqFoil_ItemClick(object sender, ItemClickEventArgs e) =>
        showInvoiceReqSlipPrintDialog(true);

    private void btnFacesheet_ItemClick(object sender, ItemClickEventArgs e)
    {
        var order = getSelectedInvoice();
        if (order == null) {
            MessageDlg.Warning("No lab order selected!");
            return;
        }

        //if (!orderCanBeModified(order, WorkflowStageType.ReportDispatch)) return;
        var bundleId = getSelectedResultBundleId();

        FaultHandler.Shield(PatientLabOrdersRepository.Reset);
        // get the exam name
        // first grab the possible exam name from result bundle
        string examName = null;
        if (bundleId > 0) {
            var bundle = ResultBundlesRepository.FindById(bundleId);
            if (bundle != null) {
                examName = bundle.ComponentLabTests;
            }
        }

        examName = InputDialog.ExecuteDialog(this, "Specify Exam Name:", examName, true);

        InvoiceSlicePrintDto invdto = null;
        ResultBundlePrintDto bundleDto = null;
        WaitFormControl.WaitOperation(this, () => FaultHandler.Shield(() =>
        {
            var labOrder = PatientLabOrdersRepository.FindById(order.InvoiceId);
            invdto = InvoiceSlicePrintDto.AssembleFrom(labOrder, true);
            bundleDto = new ResultBundlePrintDto
            {
                ResultBundleId = bundleId
            };
        }), "Preparing...");


        if (invdto != null) {
            FaultHandler.Shield(() =>
            {
                PrintHelper.PrintFacesheetReport(true, invdto, bundleDto, examName);
                AuditTrailRepository.LogOrderWorkflowEvent(AuditEventType.wfFacesheetPrinted,
                                                           order.InvoiceId,
                                                           null,
                                                           null,
                                                           order.WorkflowStage,
                                                           examName);
            });
        }
    }

    private void btnAuditTrail_ItemClick(object sender, ItemClickEventArgs e)
    {
        var order = getSelectedInvoice();
        FaultHandler.Shield(PatientLabOrdersRepository.Reset);
        if (!orderCanBeModified(order, WorkflowStageType.Unknown)) return;

        AuditTrailComposer atComposer = null;
        FaultHandler.Shield(() =>
        {
            atComposer = new AuditTrailComposer(order.InvoiceId);
            atComposer.GetAllAuditTrailsForLabOrder();
        });

        new AuditTrailViewerDialog(atComposer.LabOrderContext, atComposer.AuditEvents).ExecuteDialog(this);
    }

    private void btnTxHistory_ItemClick(object sender, ItemClickEventArgs e)
    {
        var order = getSelectedInvoice();
        FaultHandler.Shield(PatientLabOrdersRepository.Reset);
        if (!orderCanBeModified(order, WorkflowStageType.Unknown)) return;

        new InvoiceTransactionsViewerDialog(order.InvoiceId).ExecuteDialog(this);
    }

    private void btnPatientDemographicsEdit_ItemClick(object sender, ItemClickEventArgs e)
    {
        var order = getSelectedInvoice();
        FaultHandler.Shield(PatientLabOrdersRepository.Reset);
        if (!orderCanBeModified(order, FINANCIAL_CUTOFF_STAGE)) return;
        var canEdit = CachedRolePermissionChecker.CheckPermission(PermissionCodes.LabOrders.EditReferrer);
        new LabOrderDemographicsEditDialog(order.InvoiceId, canEdit).ExecuteDialog(this);

        cacheReset();
        performSearch();
    }

    private bool checkUserHasActiveShift()
    {
        var hasShift = CurrentUserContext.WorkShiftId > 0;
        if (!hasShift) {
            MessageDlg.Warning("You must have an active workshift to perform this task.\nOperation aborted.");
        }

        return hasShift;
    }

    private void btnEditLabOrder_ItemClick(object sender, ItemClickEventArgs e)
    {
        if (!checkUserHasActiveShift()) return;

        var order = getSelectedInvoice();
        if (!orderCanBeModified(order, FINANCIAL_CUTOFF_STAGE)) return;

        if (!CurrentUserContext.CheckShiftIsOpen()) {
            MessageDlg.Error("You do not have an active workshift.\nYou must start a shift to perform this task.");
            return;
        }

        FaultHandler.Shield(PatientLabOrdersRepository.Reset);
        new LabOrderEditDialog(order.InvoiceId).ExecuteDialog(this);

        cacheReset();
        performSearch();
    }

    private void btnDiscount_ItemClick(object sender, ItemClickEventArgs e)
    {
        if (!checkUserHasActiveShift()) return;

        var order = getSelectedInvoice();
        if (!orderCanBeModified(order, FINANCIAL_CUTOFF_STAGE)) return;

        showRefundDiscountDialog(order.InvoiceId, RefundDiscountOperationMode.Discount);
    }

    private void btnDiscountRebate_ItemClick(object sender, ItemClickEventArgs e)
    {
        if (!checkUserHasActiveShift()) return;

        var order = getSelectedInvoice();
        if (!orderCanBeModified(order, FINANCIAL_CUTOFF_STAGE)) return;

        showRefundDiscountDialog(order.InvoiceId, RefundDiscountOperationMode.DiscountRebate);
    }

    private void btnRefund_ItemClick(object sender, ItemClickEventArgs e)
    {
        if (!checkUserHasActiveShift()) return;

        var order = getSelectedInvoice();
        if (!orderCanBeModified(order, FINANCIAL_CUTOFF_STAGE)) return;

        showRefundDiscountDialog(order.InvoiceId, RefundDiscountOperationMode.Refund);
    }

    private void showRefundDiscountDialog(long invoiceId, RefundDiscountOperationMode operationMode)
    {
        if (invoiceId > 0) {
            FaultHandler.Shield(PatientLabOrdersRepository.Reset);

            var order = PatientLabOrdersRepository.FindById(invoiceId);

            if (order == null || order.IsCancelled) {
                MessageDlg.Error("Non-existent or canceled order.\nOperation aborted.");
                return;
            }

            new RefundDiscountDialog(order, operationMode).ExecuteDialog(this);
        }
    }

    private void txtInvoiceId_KeyUp(object sender, KeyEventArgs e)
    {
        if (e.KeyCode == Keys.Enter)
            btnSearch_Click(null, null);
    }

    private void InvoiceSearchForm_KeyUp(object sender, KeyEventArgs e)
    {
        switch (e.KeyCode) {
            case Keys.F5:
                btnClear_Click(null, null);
                chkSearchInvoiceId.Checked = true;
                txtInvoiceId.Enabled = true;
                txtInvoiceId.Select();
                txtInvoiceId.Focus();
                e.Handled = true;
                break;
            case Keys.Escape:
                //e.Handled = true;
                //DialogResult = DialogResult.OK;
                break;
            case Keys.F12:
                resultBundlePreFinalizationDraftPrinting(e.Modifiers == Keys.Shift);
                e.Handled = true;
                break;
        }
    }

    private void btnCopyDemographic_ItemClick(object sender, ItemClickEventArgs e)
    {
        var order = getSelectedInvoice();
        if (order != null) {
            FaultHandler.Shield(PatientLabOrdersRepository.Reset);
            var item = LabOrderDemographicInfoSlice.AssembleFrom(order);
            LabOrderDemographicHistory.Add(item);
            MessageDlg.Info("The selected invoice is added to Lab Order Demographic History.");
        }
    }

    private void chkApplyTimeFilter_CheckedChanged(object sender, EventArgs e)
    {
        var enabled = chkApplyTimeFilter.Checked;
        toggleTimeRangeFilterControls(enabled);
    }

    private void toggleTimeRangeFilterControls(bool enabled)
    {
        dtTimeFrom.Enabled = enabled;
        dtTimeTo.Enabled = enabled;
    }

    private DXMenuItem createMenuItem(string caption, EventHandler onClick, Image image, bool beginGroup = false)
    {
        var item = new DXMenuItem(caption, onClick, image);
        if (beginGroup) item.BeginGroup = beginGroup;
        return item;
    }

    private bool checkPopupPermission(string permCode, bool warn)
    {
        var result = CachedRolePermissionChecker.CheckPermission(permCode);
        if (!result && warn) MessageDlg.Warning("You do not have the permission to use this feature!");
        return result;
    }

    private void popInvoiceGenerateResultBundles(object sender, EventArgs eventArgs)
    {
        if (!checkPopupPermission(PermissionCodes.LabOrders.GenerateResultBundles, true)) return;

#if !DEBUG
        if (!CaptchaDialog.ConfirmCaptcha(4)) return;
#endif

        var invoiceId = getSelectedInvoiceId();
        if (invoiceId > 0 && checkInvoiceIsNotCancelled(invoiceId)) {
            createBundleForInvoice(invoiceId);
            CachedResultBundleSearchProvider.Reset();
            PatientLabOrdersRepository.Reset();
        }
    }

    private void popInvoiceRecreateResultBundles(object sender, EventArgs eventArgs)
    {
        if (!checkPopupPermission(PermissionCodes.LabOrders.GenerateResultBundles, true)) return;

#if !DEBUG
        if (!CaptchaDialog.ConfirmCaptcha(4)) return;
#endif

        var invoiceId = getSelectedInvoiceId();
        if (invoiceId > 0 && checkInvoiceIsNotCancelled(invoiceId)) {
            //FaultHandler.Shield(() => ResultBundlesRepository.RemoveAllResultBundlesForInvoice(invoiceId));
            FaultHandler.Shield(() => ResultBundlesRepository.CancelAllResultBundlesForInvoice(invoiceId));
            createBundleForInvoice(invoiceId);
            CachedResultBundleSearchProvider.Reset();
            PatientLabOrdersRepository.Reset();
        }
    }

    private static bool checkInvoiceIsNotCancelled(long invoiceId)
    {
        var order = FaultHandler.Shield(() => PatientLabOrdersRepository.FetchLabOrderDetails(invoiceId));
        if (order == null || order.IsCancelled) {
            MessageDlg.Warning("The selected lab order cannot be found or it was canceled.\nOperation aborted.");
            return false;
        }

        return true;
    }

    private void popResultBundleExportPdf(object sender, EventArgs eventArgs)
    {
        if (!checkPopupPermission(PermissionCodes.Workflow.Export, true)) return;
        var bundleId = getSelectedResultBundleId();
        if (bundleId > 0) {
            FaultHandler.Shield(() => exportResultBundle(bundleId, true));
        }
    }

    private void popResultBundleExportWord(object sender, EventArgs eventArgs)
    {
        if (!checkPopupPermission(PermissionCodes.Workflow.Export, true)) return;
        var bundleId = getSelectedResultBundleId();
        if (bundleId > 0) {
            FaultHandler.Shield(() => exportResultBundle(bundleId, false));
        }
    }

    private void compileAndExportBundles(bool pdf,
                                         string filepath,
                                         LabOrderDetailsSlice order,
                                         List<ResultBundlesForInvoice> bundles,
                                         string exportReason)
    {
        var dtoInvoice = InvoiceSlicePrintDto.AssembleFrom(order);
        foreach (var bundle in bundles) {
            var resultType = (TestResultType)bundle.TestResultType;
            if (resultType == TestResultType.Unarchived) {
                continue;
            }

            var compiler = new ResultBundlePrintDtoCompiler(bundle.Id);
            var dtoBundle = compiler.CompileBundle();

            switch (resultType) {
                case TestResultType.Discrete:
                    var discreteItems = compiler.GetDiscreteResultItems();
                    PrintHelper.ExportDiscreteReport(pdf, filepath, dtoInvoice, dtoBundle, discreteItems);
                    break;
                case TestResultType.UserTemplate:
                case TestResultType.Template:
                    var templateItem = compiler.GetTemplateResultContent();
                    PrintHelper.ExportTemplateReport(pdf, filepath, dtoInvoice, dtoBundle, templateItem);
                    break;
            }

            AuditTrailRepository.LogOrderWorkflowEvent(AuditEventType.wfReportExported,
                                                       order.InvoiceId,
                                                       bundle.Id,
                                                       null,
                                                       (WorkflowStageType)order.WorkflowStage,
                                                       exportReason);
        }
    }

    private void printBundles(LabOrderDetailsSlice order,
                              List<ResultBundlesForInvoice> bundles,
                              string exportReason)
    {
        var dtoInvoice = InvoiceSlicePrintDto.AssembleFrom(order);
        foreach (var bundle in bundles) {
            var resultType = (TestResultType)bundle.TestResultType;
            if (resultType == TestResultType.Unarchived) {
                continue;
            }

            var compiler = new ResultBundlePrintDtoCompiler(bundle.Id);
            var dtoBundle = compiler.CompileBundle();

            switch (resultType) {
                case TestResultType.Discrete:
                    var discreteItems = compiler.GetDiscreteResultItems();
                    PrintHelper.PrintDiscreteReport(true, dtoInvoice, dtoBundle, discreteItems);
                    break;
                case TestResultType.UserTemplate:
                case TestResultType.Template:
                    var templateItem = compiler.GetTemplateResultContent();
                    PrintHelper.PrintTemplateReport(true, dtoInvoice, dtoBundle, templateItem);
                    break;
            }

            AuditTrailRepository.LogOrderWorkflowEvent(AuditEventType.wfReportPrinted,
                                                       order.InvoiceId,
                                                       bundle.Id,
                                                       null,
                                                       (WorkflowStageType)order.WorkflowStage,
                                                       exportReason);
        }
    }

    private void printPreFinalizationDraftBundles(LabOrderDetailsSlice order, List<ResultBundlesForInvoice> bundles,
                                                  bool showPreview)
    {
        var dtoInvoice = InvoiceSlicePrintDto.AssembleFrom(order);
        foreach (var bundle in bundles) {
            if (bundle.TestResultType != (byte)TestResultType.Discrete) {
                continue;
            }

            var compiler = new ResultBundlePrintDtoCompiler(bundle.Id);
            var dtoBundle = compiler.CompileBundle();

            var discreteItems = compiler.GetDiscreteResultItems();
            PrintHelper.PrintDiscreteDraftReport(showPreview, dtoInvoice, dtoBundle, discreteItems);
            AuditTrailRepository.LogOrderWorkflowEvent(AuditEventType.wfPreliminaryReportPrinted,
                                                       order.InvoiceId,
                                                       bundle.Id,
                                                       null,
                                                       (WorkflowStageType)order.WorkflowStage,
                                                       string.Empty);
        }
    }

    private void exportResultBundle(long bundleId, bool generatePdf)
    {
        var extension = generatePdf ? ".pdf" : ".docx";

        var reason = InputDialog.ExecuteDialog(this, "Reason for exporting report:");
        if (string.IsNullOrEmpty(reason)) {
            MessageDlg.Warning("No valid reason provided!");
            return;
        }

        var item = ResultBundlesRepository.FindById(bundleId);
        var invoiceId = (long)item.InvoiceId;
        var orderSlice = PatientLabOrdersRepository.FetchLabOrderDetails(invoiceId);
        var bundleSlice = ResultBundlesRepository.GetActiveResultBundlesForInvoice(invoiceId)
                                                 .SingleOrDefault(b => b.Id == bundleId);

        if (orderSlice.IsCancelled) {
            MessageDlg.Warning("The selected lab order was canceled.\nOperation aborted.");
            return;
        }

        if (bundleSlice == null) {
            MessageDlg.Warning("The selected result bundle was canceled.\nOperation aborted.");
            return;
        }

        var bundles = new List<ResultBundlesForInvoice> { bundleSlice };

        var folder = Path.Combine(SharedUtilities.AssemblyDirectory, "Exports");
        try {
            if (!Directory.Exists(folder))
                Directory.CreateDirectory(folder);
        }
        finally {
            // ignore
        }

        if (string.IsNullOrEmpty(saveFileDialog.InitialDirectory))
            saveFileDialog.InitialDirectory = folder;
        saveFileDialog.Filter =
            $"{(generatePdf ? "PDF" : "Word")} files (*{extension})|*{extension}|All files (*.*)|*.*";
        saveFileDialog.FilterIndex = 0;
        saveFileDialog.FileName = string.Format("Invoice {0} Order {1} Bundle {2}{3}", invoiceId,
                                                orderSlice.OrderId,
                                                bundleId, extension);

        if (saveFileDialog.ShowDialog(this) != DialogResult.OK) return;

        var fileName = saveFileDialog.FileName;
        if (string.IsNullOrEmpty(Path.GetExtension(fileName))) {
            fileName = Path.ChangeExtension(fileName, extension);
        }

        compileAndExportBundles(generatePdf, fileName, orderSlice, bundles, reason);
        openFileWithAssociatedApp(fileName);
    }

    private void openFileWithAssociatedApp(string fileName) => Process.Start(fileName);

    private void popResultBundleExtractRtf(object sender, EventArgs eventArgs)
    {
        if (!checkPopupPermission(PermissionCodes.Workflow.Export, true)) return;
        var bundleId = getSelectedResultBundleId();
        if (bundleId <= 0) return;

        var item = ResultBundlesRepository.FindById(bundleId);
        var invoiceId = item.InvoiceId ?? (long)-1;
        var orderSlice = PatientLabOrdersRepository.FetchLabOrderDetails(invoiceId);
        var bundleSlice = ResultBundlesRepository.GetActiveResultBundlesForInvoice(invoiceId)
                                                 .SingleOrDefault(b => b.Id == bundleId);
        if (bundleSlice.TestResultType != (byte)TestResultType.Template &&
            bundleSlice.TestResultType != (byte)TestResultType.UserTemplate) {
            MessageDlg.Warning("Selected Result Bundle is not a Template-based test!");
            return;
        }

        var compiler = new ResultBundlePrintDtoCompiler(bundleId);
        compiler.CompileBundle();
        var templateItem = compiler.GetTemplateResultContent();

        if (templateItem == null || !templateItem.HasValidContent()) {
            MessageDlg.Warning("Selected Result Bundle does not contain any actual content!");
            return;
        }

        var reason = InputDialog.ExecuteDialog(this, "Reason for exporting report:");
        if (string.IsNullOrEmpty(reason)) {
            MessageDlg.Warning("No valid reason provided!");
            return;
        }

        var extension = @".rtf";
        if (string.IsNullOrEmpty(saveFileDialog.InitialDirectory))
            saveFileDialog.InitialDirectory = Environment.CurrentDirectory;
        saveFileDialog.Filter = @"RTF files (*.rtf)|*.rtf|All files (*.*)|*.*";
        saveFileDialog.FileName = $"Invoice {invoiceId} Order {orderSlice.OrderId} Bundle {bundleId}{extension}";
        if (saveFileDialog.ShowDialog(this) != DialogResult.OK) return;

        var fileName = saveFileDialog.FileName;
        if (string.IsNullOrEmpty(Path.GetExtension(fileName)))
            fileName = Path.ChangeExtension(fileName, extension);

        File.WriteAllText(fileName, templateItem.ResultContent);

        AuditTrailRepository.LogOrderWorkflowEvent(AuditEventType.wfReportExported,
                                                   orderSlice.InvoiceId,
                                                   bundleId,
                                                   null,
                                                   (WorkflowStageType)orderSlice.WorkflowStage,
                                                   reason);
        openFileWithAssociatedApp(fileName);
    }

    private void popResultBundleRepeatProcedure(object sender, EventArgs eventArgs)
    {
        // only the consultant physician or the admins can repeat laboratory tests
        if (!checkPopupPermission(PermissionCodes.Workflow.Finalize, true) &&
            !CachedRolePermissionChecker.UserIsAdmin()) return;

        var bundleId = getSelectedResultBundleId();
        if (bundleId <= 0) return;

        var reason = InputDialog.ExecuteDialog(this, "Reason for repeat:");
        if (string.IsNullOrEmpty(reason)) {
            MessageDlg.Warning("No valid reason provided!");
            return;
        }

        cacheReset();

        var item = FaultHandler.Shield(() => ResultBundlesRepository.FindById(bundleId));
        var invoiceId = item.InvoiceId ?? (long)-1;

        var orderSlice =
            FaultHandler.Shield(() => PatientLabOrdersRepository.FetchLabOrderDetails(invoiceId));
        if (orderSlice.IsCancelled) {
            MessageDlg.Warning("The selected lab order was canceled.\nOperation aborted.");
            return;
        }

        var bundleSlice = ResultBundlesRepository.GetActiveResultBundlesForInvoice(invoiceId)
                                                 .SingleOrDefault(b => b.Id == bundleId);
        if (bundleSlice == null) {
            MessageDlg.Warning("The selected result bundle was canceled.\nOperation aborted.");
            return;
        }

        var wfStage = (WorkflowStageType)bundleSlice.WorkflowStage;
        if (wfStage != WorkflowStageType.Canceled &&
            wfStage >= WorkflowStageType.ResultEntry &&
            wfStage <= WorkflowStageType.ReportCollation) {
            saveResultBundleWorkflowStage(orderSlice.InvoiceId, bundleSlice.Id,
                                          WorkflowStageType.RepeatProcedure);
            // audit log
            AuditTrailRepository.LogOrderWorkflowEvent(AuditEventType.wfRepeatProcedure,
                                                       orderSlice.InvoiceId,
                                                       bundleSlice.Id,
                                                       null,
                                                       wfStage);
        }
        else
            MessageDlg.Warning("The selected result bundle is not eligible for repeat.\nOperation aborted.");
    }

    private void saveResultBundleWorkflowStage(long invoiceId, long bundleId, WorkflowStageType wfStage)
    {
        ResultBundlesRepository.UpdateResultBundleWorkflowStage(bundleId, wfStage);

        /*
        var estimator = new LabOrderWorkflowStageEstimator(_currentLabOrder.InvoiceId);
        estimator.EstimateWorkflowStage();
        */
        //AppSysRepository.SendLabOrderWorkflowStageEstimateRequest(_currentLabOrder.InvoiceId);
        AsyncEsbMessenger.SendMessagesAsync(
            EsbMessageChain.CreateNew()
                           .AddWorkflowEstimator(invoiceId));

        // send delayed hooks request
        AsyncEsbMessenger.SendMessagesAsync(
            EsbMessageChain.CreateNewDelayed()
                           .AddResultBundleWorkflowHook(invoiceId, bundleId)
                           .AddLabOrderWorkflowHook(invoiceId));
    }

    private void popResultBundlePrintReport(object sender, EventArgs eventArgs)
    {
        if (!checkPopupPermission(PermissionCodes.Workflow.Print, true)) return;
        var bundleId = getSelectedResultBundleId();
        if (bundleId > 0) {
            var reason = InputDialog.ExecuteDialog(this, "Reason for printing report:");
            if (string.IsNullOrEmpty(reason)) {
                MessageDlg.Warning("No valid reason provided!");
                return;
            }

            var item = ResultBundlesRepository.FindById(bundleId);
            var invoiceId = item.InvoiceId ?? (long)-1;
            var orderSlice = PatientLabOrdersRepository.FetchLabOrderDetails(invoiceId);
            var bundleSlice = ResultBundlesRepository.GetActiveResultBundlesForInvoice(invoiceId)
                                                     .SingleOrDefault(b => b.Id == bundleId);

            if (orderSlice.IsCancelled) {
                MessageDlg.Warning("The selected lab order was canceled.\nOperation aborted.");
                return;
            }

            if (bundleSlice == null) {
                MessageDlg.Warning("The selected result bundle was canceled.\nOperation aborted.");
                return;
            }

            var bundles = new List<ResultBundlesForInvoice> { bundleSlice };

            printBundles(orderSlice, bundles, reason);
        }
    }

    private void popResultBundlePrintPreFinalizationDraft(object sender, EventArgs eventArgs) =>
        resultBundlePreFinalizationDraftPrinting(true);

    private void resultBundlePreFinalizationDraftPrinting(bool showPreview)
    {
        if (!checkPopupPermission(PermissionCodes.Workflow.Print, true)) return;
        var bundleId = getSelectedResultBundleId();
        if (bundleId > 0) {
            var item = ResultBundlesRepository.FindById(bundleId);
            var invoiceId = item.InvoiceId ?? (long)-1;
            var orderSlice = PatientLabOrdersRepository.FetchLabOrderDetails(invoiceId);
            var bundleSlice = ResultBundlesRepository.GetActiveResultBundlesForInvoice(invoiceId)
                                                     .SingleOrDefault(b => b.Id == bundleId);

            if (orderSlice.IsCancelled) {
                MessageDlg.Warning("The selected lab order was canceled.\nOperation aborted.");
                return;
            }

            if (bundleSlice == null) {
                MessageDlg.Warning("The selected result bundle was canceled.\nOperation aborted.");
                return;
            }

            if (bundleSlice.WorkflowStage != (byte)WorkflowStageType.ResultValidation) {
                MessageDlg.Warning(
                    "Only validated result bundles are allowed for pre-finalization draft printing.\nOperation aborted.");
                return;
            }

            var bundles = new List<ResultBundlesForInvoice> { bundleSlice };
            printPreFinalizationDraftBundles(orderSlice, bundles, showPreview);
        }
    }

    private void popResultBundleViewAuditTrail(object sender, EventArgs eventArgs)
    {
        var bundleId = getSelectedResultBundleId();
        var order = getSelectedInvoice();
        if (bundleId > 0) {
            var orderDetails = string.Format("{0} ({2}) #{1}",
                                             order.OrderId,
                                             order.InvoiceId,
                                             SharedUtilities.DateToStringOnlySlash(order.OrderDateTime));
            ResultBundleAuditTrailViewerDialog.ExecuteDialog(this, bundleId, orderDetails, bundleId.ToString());
        }
    }

    private void createBundleForInvoice(long id)
    {
        FaultHandler.Shield(PatientLabOrdersRepository.Reset);
        var count = FaultHandler.Shield(() => ResultBundlesRepository.GetActiveResultBundlesCountForInvoice(id));
        if (count > 0) {
            MessageDlg.Warning("Result bundles have already been created for this lab order!");
            return;
        }

        FaultHandler.Shield(
            () => WaitFormControl.WaitOperation(
                this,
                () =>
                {
                    var comp = new ResultBundleCompiler(id, new InvoiceOrderedTestsDataProvider());
                    comp.CompileBundles();
                    ResultBundleDbPersister.InsertNewResultBundles(comp);
                    LabOrderWorkflowStageEstimator.PerformEstimation(id);
                    PatientLabOrdersRepository.Reset();
                },
                "Generating result bundles..."));
    }


    private void gvwInvoices_PopupMenuShowing(object sender, PopupMenuShowingEventArgs e)
    {
        if (e.MenuType != GridMenuType.Row) return;

        var menu = e.Menu;
        if (menu.Items.Count > 0) return;
        if (!checkPopupPermission(PermissionCodes.LabOrders.GenerateResultBundles, false)) return;

        menu.Items.Clear();
        menu.Items.Add(createMenuItem("Generate Result Bundles",
                                      popInvoiceGenerateResultBundles,
                                      ResourceIcons.report_go_16));

        if (CachedRolePermissionChecker.UserIsAdmin()) {
            menu.Items.Add(createMenuItem("Re-create Result Bundles",
                                          popInvoiceRecreateResultBundles,
                                          Resources.red_element_fire));
        }
    }

    private void gvwResultBundles_PopupMenuShowing(object sender, PopupMenuShowingEventArgs e)
    {
        if (e.MenuType != GridMenuType.Row) return;

        var menu = e.Menu;
        if (menu.Items.Count > 0) return;

        menu.Items.Clear();
        menu.Items.Add(createMenuItem("View Audit Trail",
                                      popResultBundleViewAuditTrail,
                                      ResourceIcons.list_information_16));

        if (checkPopupPermission(PermissionCodes.Workflow.Finalize, false) ||
            CachedRolePermissionChecker.UserIsAdmin()) {
            menu.Items.Add(createMenuItem("Repeat Lab Procedure(s)",
                                          popResultBundleRepeatProcedure,
                                          Resources.reload16,
                                          true));
        }

        if (!checkPopupPermission(PermissionCodes.Workflow.Print, false)) return;
        if (CachedRolePermissionChecker.UserIsAdmin()) {
            menu.Items.Add(createMenuItem("Print Report",
                                          popResultBundlePrintReport,
                                          Resources.print,
                                          true));
            menu.Items.Add(createMenuItem("Print Pre-Finalization Draft",
                                          popResultBundlePrintPreFinalizationDraft,
                                          Resources.print2,
                                          false));
        }

        if (!checkPopupPermission(PermissionCodes.Workflow.Export, false)) return;
        menu.Items.Add(createMenuItem("Export PDF Report",
                                      popResultBundleExportPdf,
                                      ResourceIcons.pdf_16,
                                      true));
        menu.Items.Add(createMenuItem("Export Word 2007 Report",
                                      popResultBundleExportWord,
                                      ResourceIcons.word_16));
        menu.Items.Add(createMenuItem("Extract Raw RTF Template",
                                      popResultBundleExtractRtf,
                                      ResourceIcons.rtf_16));
    }

    private void btnResetCache_Click(object sender, EventArgs e)
    {
        if (MessageDlg.Confirm("Really reset entity cache?")) cacheReset();
    }

    private static void cacheReset()
    {
        if (PatientLabOrdersRepository.HasChanges) {
            PatientLabOrdersRepository.Save();
        }

        PatientLabOrdersRepository.Reset();
        CachedResultBundleSearchProvider.Reset();
    }

    private void popMenuItemClick(object sender, ItemClickEventArgs e)
    {
        var tag = (short)e.Item.Tag;
        switch (tag) {
            case 1:
                dtFrom.DateTime = DateTime.Now;
                dtTo.DateTime = DateTime.Now;
                break;
            case 2:
                dtFrom.DateTime = DateTime.Now.AddDays(-1);
                dtTo.DateTime = DateTime.Now.AddDays(-1);
                break;
            case 3:
                dtFrom.DateTime = DateTime.Now.AddDays(-2);
                dtTo.DateTime = DateTime.Now;
                break;
            case 4:
                dtFrom.DateTime = SharedUtilities.StartOfWeek(DateTime.Now);
                dtTo.DateTime = DateTime.Now;
                break;
            case 5:
                dtFrom.DateTime = DateTime.Now.AddDays(-6);
                dtTo.DateTime = DateTime.Now;
                break;
            case 6:
                dtFrom.DateTime = SharedUtilities.StartOfMonth(DateTime.Now);
                dtTo.DateTime = DateTime.Now;
                break;
            case 7:
                dtFrom.DateTime = DateTime.Now.AddDays(-29);
                dtTo.DateTime = DateTime.Now;
                break;
        }
    }

    private void btnCopyDate_Click(object sender, EventArgs e)
    {
        dtTo.DateTime = dtFrom.DateTime;
    }

    internal enum InvoiceSearchMode : byte
    {
        DateRange = 100,
        InvoiceId,
        OrderId,
        WorkflowStage,
        PatientName,
        Referrer,
        AssocLab,
        Corporate,
        PhoneNumber,
        CustomerId
    }

    private void btnAssocLab_Click(object sender, EventArgs e)
    {
        using var form = new AssociatedLabDialog();
        form.SetLabel("Lab");
        form.SetAssociateLabs(_associateLabs);
        var res = form.ShowDialog(this);
        switch (res) {
            case DialogResult.OK:
                // assign
                setAssociateLab(form.SelectedId, form.SelectedName);
                break;
            case DialogResult.Ignore:
                // clear assignment
                setAssociateLab(null);
                return;
            default:
                return;
        }
    }

    private void setAssociateLab(short? id, string? name = null)
    {
        _associateLabId = id ?? -1;
        lblAssocLab.Text = name;
    }

    private void setCorporateClient(short? id, string? name = null)
    {
        _corporateId = id ?? -1;
        lblCorporate.Text = name;
    }

    private void btnCorporate_Click(object sender, EventArgs e)
    {
        using var form = new AssociatedLabDialog();
        form.SetLabel("Corporate");
        form.SetCorporateClients(_corporateClients);
        var res = form.ShowDialog(this);
        switch (res) {
            case DialogResult.OK:
                // assign
                setCorporateClient(form.SelectedId, form.SelectedName);
                break;
            case DialogResult.Ignore:
                // clear assignment
                setCorporateClient(null);
                return;
            default:
                return;
        }
    }
}