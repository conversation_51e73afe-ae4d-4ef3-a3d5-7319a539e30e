﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2015, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id$
// 
// Last modified: 2015.08.22 2:24 PM
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Drawing;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using LabMaestro.BusinessLogic;
using LabMaestro.BusinessLogic.GroupedIncomeStatement;
using LabMaestro.Controls.Win.Accounts;
using LabMaestro.Controls.Win.CRM;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.Controls.Win;

public partial class LaunchPadForm : XtraForm
{
    public LaunchPadForm()
    {
        InitializeComponent();
    }

    private void LaunchPadForm_Load(object sender, EventArgs e)
    {
        Text = $"{CurrentUserContext.UserDisplayName} - LabMaestro {AppVersion.GetVersionString()}";
        lblCopyright.Text =
            $"Copyright © 2012-{DateTime.Now.Date.ToString("yy")} Dr. Masroor Ehsan. All rights reserved.";
        lblGreeting.Text = $"Hello {CurrentUserContext.UserDisplayName}!";
        Bounds = Screen.PrimaryScreen.Bounds;
        FormBorderStyle = FormBorderStyle.None;
#if PRODUCTION_RELEASE
    //TopMost = true;
#endif
        updateLabelControls();
        CachedRolePermissionChecker.PrefetchCache(true);

        updateWorkShiftTiles(UserWorkshiftHelper.UserHasOpenWorkShift());

        if (!ensureCurrentSessionMeetsActiveWorkShiftRequirement())
            disableAllTilesExceptShiftCreate();
        else
            applyUserPermissions();

        PreCacheFrequentlyUsedEntities();
    }

    private void PreCacheFrequentlyUsedEntities()
    {
        LabsRepository.PreCacheLabNames();
        // user display names
        // 
    }

    private void updateLabelControls()
    {
        lblIpAddress.Text = SharedUtilities.GetLocalIpAddressString(GlobalSettingsHelper.System.IPSubnetPrefix);
        lblLoginTime.Text = SharedUtilities.DateTimeToString24(CurrentUserContext.LoginTime);
        lblStaffname.Text = CurrentUserContext.UserDisplayName;
        lblVersion.Text = AppVersion.GetVersionString();
    }

    private void disableAllTilesExceptShiftCreate()
    {
        foreach (TileGroup grp in tileControl.Groups)
        {
            foreach (TileItem item in grp.Items)
            {
                // allow the create shift button
                if (item.Tag != null && (int)item.Tag == (int)LaunchPadAccessFlag.ShiftCreate)
                    continue;

                // change password
                if (item.Tag != null && (int)item.Tag == (int)LaunchPadAccessFlag.ChangePassword)
                    continue;

                toggleTile(item, false);
            }
        }
    }

    private bool ensureCurrentSessionMeetsActiveWorkShiftRequirement()
    {
        var hasShift = UserWorkshiftHelper.UserHasOpenWorkShift();
        if (UserWorkshiftHelper.UserMustHaveOpenWorkShift() && !hasShift)
        {
            updateWorkShiftTiles(hasShift);
            return MessageDlg.Confirm(
                       "Your user account requires an active work shift.\nDo you want to open a new work shift now?")
                   && startNewWorkshift();
        }

        updateWorkShiftTiles(true);
        return true;
    }

    private bool startNewWorkshift()
    {
        if (!ShiftStartDialog.ExecuteDialog(this, out var balance, out var note))
            return false;

        UserWorkshiftHelper.StartNewWorkshift(balance, note);
        var hasShift = UserWorkshiftHelper.UserHasOpenWorkShift();
        updateWorkShiftTiles(hasShift);
        return hasShift;
    }

    private static void toggleTilePermCode(TileItem item, string permCode, bool overrideAdmin = false)
    {
        var enabled = CachedRolePermissionChecker.CheckPermission(permCode);
        if (!enabled && overrideAdmin) enabled = true;
        toggleTile(item, enabled);
    }

    private static void toggleTile(TileItem item, bool enabled)
    {
        item.Enabled = enabled;
        item.Visible = enabled;
        return;

        if (enabled)
        {
            //item.AppearanceItem.Normal.BackColor = Color.Black;
            //item.AppearanceItem.Normal.BorderColor = Color.DimGray;
            item.AppearanceItem.Normal.ForeColor = Color.White;
        }

        item.ItemClick -= (s, a) => { };
        item.ItemPress -= (s, a) => { };

        item.AppearanceItem.Normal.BackColor = Color.Black;
        //item.AppearanceItem.Normal.BorderColor = Color.DimGray;
        item.AppearanceItem.Normal.ForeColor = Color.Black;
    }

    private void applyUserPermissions()
    {
        tileControl.BeginUpdate();
        try
        {
            // PROE
            toggleTilePermCode(tiNewLabOrder, PermissionCodes.LabOrders.Create);
            //toggleTilePermCode(tiNewExternalOrder, PermissionCodes.LabOrders.Create);
            toggleTile(tiNewExternalOrder, false);
            toggleTilePermCode(tiProcessReceivables, PermissionCodes.LabOrders.ProcessReceivables);
            toggleTilePermCode(tiSearchInvoices, PermissionCodes.LabOrders.Search);
            toggleTilePermCode(tiCustomers, PermissionCodes.LabOrders.Create,
                CachedRolePermissionChecker.UserIsAdmin());

            // Workflow
            toggleTilePermCode(tiResultEntry, PermissionCodes.Workflow.Entry);
            toggleTilePermCode(tiResultsVerification, PermissionCodes.Workflow.Verify);
            toggleTilePermCode(tiReportFinalization, PermissionCodes.Workflow.Finalize);
            toggleTilePermCode(tiReportCollation, PermissionCodes.Workflow.Collate);
            toggleTilePermCode(tiReportDispatch, PermissionCodes.Workflow.Dispatch);
            toggleTilePermCode(tiBulkDispatch, PermissionCodes.Workflow.Dispatch);
            toggleTilePermCode(tiFacesheet, PermissionCodes.Workflow.PrintFacesheet);

            // User shift
            toggleTilePermCode(tiShiftOpen, PermissionCodes.Finances.ManageWorkshift);
            toggleTilePermCode(tiShiftClose, PermissionCodes.Finances.ManageWorkshift);
            toggleTilePermCode(tiUserShiftHistory, PermissionCodes.Finances.ManageWorkshift);
            toggleTilePermCode(tiUserShiftReports, PermissionCodes.Finances.ManageWorkshift);
            if (CachedRolePermissionChecker.CheckPermission(PermissionCodes.Finances.ManageWorkshift))
            {
                updateWorkShiftTiles(UserWorkshiftHelper.UserHasOpenWorkShift());
            }

            //toggleTile(ti, PermissionCodes.LabOrders.Edit);
            toggleTilePermCode(tiCatReferrerAdd, PermissionCodes.Catalog.ReferrerEdit);
            toggleTilePermCode(tiCatReferrerBrowse, PermissionCodes.Catalog.ReferrerBrowse);
            toggleTilePermCode(tiTestNew, PermissionCodes.Catalog.ReferrerEdit);
            toggleTilePermCode(tiTestEdit, PermissionCodes.Catalog.ReferrerEdit);

            // accounting reports
            toggleTilePermCode(tiBOIncomeStatement, PermissionCodes.Finances.GenerateReports);
            toggleTilePermCode(tiBOReceivablesSummary, PermissionCodes.Finances.GenerateReports);
            toggleTilePermCode(tiBOUserSummaries, PermissionCodes.Finances.GenerateReports);
            toggleTilePermCode(tiBODailyReceivables, PermissionCodes.Finances.GenerateReports);
            toggleTilePermCode(tiBOFinancialAudit, PermissionCodes.Finances.GenerateReports);
            toggleTilePermCode(tiBOOutstandingCollection, PermissionCodes.Finances.GenerateReports);
            toggleTilePermCode(tiBOOutstandingStatement, PermissionCodes.Finances.GenerateReports);
            toggleTilePermCode(tiBOReferrerBizContrib, PermissionCodes.Finances.GenerateReports);
            toggleTilePermCode(tiBODailyDuesCollection, PermissionCodes.Finances.GenerateReports);
            toggleTilePermCode(tiPhleboUtilization, PermissionCodes.Finances.GenerateReports);
            toggleTilePermCode(tiDuesRegister, PermissionCodes.Finances.GenerateReports);
            toggleTilePermCode(tiBOCancellationReport, PermissionCodes.Finances.GenerateReports);
            toggleTilePermCode(tiTestUtilizationReport, PermissionCodes.Finances.GenerateReports);
            toggleTilePermCode(tiBIUtilizationReport, PermissionCodes.Finances.GenerateReports);
            toggleTilePermCode(tiStaffShiftHistory, PermissionCodes.Finances.GenerateReports);
            toggleTilePermCode(tiBODiscountOverageReport, PermissionCodes.Finances.GenerateReports);
            toggleTilePermCode(tiBOLabwiseDailyIncomeReport, PermissionCodes.Finances.GenerateReports);

            //toggleTilePermCode(tiBORefEligibilitySum, PermissionCodes.Finances.GenerateReports);
            toggleTile(tiBORefEligibilitySum, CachedRolePermissionChecker.UserIsAdmin());


            // marketing
            toggleTilePermCode(tiMarketingReports, PermissionCodes.Marketing.ViewReferralReports);

            // misc
            toggleTilePermCode(tiThermalLabelPrint, PermissionCodes.Phlebotomy.PrintLabels);

            toggleTile(tiAdminBackend, CachedRolePermissionChecker.UserIsAdmin());

            toggleTilePermCode(tiCollatorManifest, PermissionCodes.Collation.GenerateManifestReport);

            toggleTilePermCode(tiStaffPerformanceReport, PermissionCodes.Finances.GenerateReports);
        }
        finally
        {
            tileControl.EndUpdate();
        }
    }

    private void btnLogOut_Click(object sender, EventArgs e)
    {
        WinUtils.DefaultLookAndFeel = LookAndFeel;
        /*
        if (UserWorkshiftHelper.UserMustHaveOpenWorkShift()
            && UserWorkshiftHelper.UserHasOpenWorkShift()
            && MessageDlg.Confirm("You have an open workshift.\nDo you want to close the shift before exiting?"))
        {
            UserWorkshiftHelper.CloseWorkshift();
            exitWindow();
        }
        else
        */
        {
            if (MessageDlg.Confirm("Really close?"))
            {
                exitWindow();
            }
        }
    }

    private void exitWindow()
    {
#if PRODUCTION_RELEASE
            Application.Exit();
#else
        Close();
#endif
    }

    private void tiNewLabOrder_ItemClick(object sender, TileItemEventArgs e)
    {
        if (!verifyOpenShift()) return;

        new LabOrderDialog(LabOrderDialogAccessType.CreateNew).ExecuteDialog(this);
    }

    private static bool verifyOpenShift()
    {
        if (!UserWorkshiftHelper.UserHasOpenWorkShift())
        {
            MessageDlg.Warning("You do not have an active work shift!");
            return false;
        }

        return true;
    }

    private void tiShiftClose_ItemClick(object sender, TileItemEventArgs e)
    {
        if (!UserWorkshiftHelper.UserHasOpenWorkShift())
        {
            MessageDlg.Warning("You do not have an active work shift!");
            return;
        }

        if (!MessageDlg.Confirm("Do you really want to close current shift?"))
            return;

        var shiftId = CurrentUserContext.WorkShiftId;
        UserWorkshiftHelper.CloseWorkshift();

        AuditTrailRepository.InitializeCurrentContext(CurrentUserContext.UserId,
            CurrentUserContext.WorkShiftId,
            SharedUtilities.GetLocalIpAddress(GlobalSettingsHelper.System.IPSubnetPrefix));
        CachedRolePermissionChecker.PrefetchCache(false);
        applyUserPermissions();
        updateWorkShiftTiles(false);

        new ShiftDetailsDialog(shiftId, true).ExecuteDialog(this);
    }

    private void updateWorkShiftTiles(bool shiftOpen)
    {
        if (shiftOpen)
        {
            toggleTile(tiShiftClose, true);
            toggleTile(tiShiftOpen, false);
        }
        else
        {
            toggleTile(tiShiftClose, false);
            toggleTile(tiShiftOpen, true);
        }
    }

    private void tiShiftOpen_ItemClick(object sender, TileItemEventArgs e)
    {
        if (UserWorkshiftHelper.UserHasOpenWorkShift())
        {
            MessageDlg.Warning("You already have an active workshift!");
            return;
        }

        if (!startNewWorkshift())
            disableAllTilesExceptShiftCreate();
        else
        {
            CachedRolePermissionChecker.PrefetchCache(false);
            applyUserPermissions();
            updateWorkShiftTiles(true);
        }

        AuditTrailRepository.InitializeCurrentContext(CurrentUserContext.UserId,
            CurrentUserContext.WorkShiftId,
            SharedUtilities.GetLocalIpAddress(GlobalSettingsHelper.System.IPSubnetPrefix));
    }

    private void tiProcessReceivables_ItemClick(object sender, TileItemEventArgs e)
    {
        if (!verifyOpenShift()) return;

        new AccountsReceivableDialog(TransactionOperationTypes.Payment, true, true).ExecuteDialog(this);
    }

    private void tiSearchInvoices_ItemClick(object sender, TileItemEventArgs e)
    {
        //if (!verifyOpenShift()) return;

        new InvoiceSearchForm().ExecuteDialog(this);
    }

    private void tiUserShiftHistory_ItemClick(object sender, TileItemEventArgs e)
    {
        if (!UserWorkshiftHelper.UserMustHaveOpenWorkShift())
        {
            MessageDlg.Warning("You are not allowed to view user shift reports!");
            return;
        }

        new ShiftSearchDialog(CurrentUserContext.UserId).ExecuteDialog(this);
    }

    private void tiUserShiftReports_ItemClick(object sender, TileItemEventArgs e)
    {
        if (!verifyOpenShift()) return;
        new ShiftDetailsDialog(CurrentUserContext.WorkShiftId, false).ExecuteDialog(this);
    }

    private void tiResultEntry_ItemClick(object sender, TileItemEventArgs e) =>
        new ResultEditForm().ExecuteDialog(this);

    private void tiResultsVerification_ItemClick(object sender, TileItemEventArgs e) =>
        new ResultEditForm().ExecuteDialog(this);

    private void tiReportFinalization_ItemClick(object sender, TileItemEventArgs e) =>
        new ResultEditForm().ExecuteDialog(this);

    private void tiReportCollation_ItemClick(object sender, TileItemEventArgs e) =>
        new CollationDispatchDialog(CollationDispatchDialogType.CollationDialog).ExecuteDialog(this);

    private void tiReportDispatch_ItemClick(object sender, TileItemEventArgs e) =>
        new CollationDispatchDialog(CollationDispatchDialogType.DispatchDialog).ExecuteDialog(this);

    private void tiFacesheet_ItemClick(object sender, TileItemEventArgs e) =>
        new InvoiceSearchForm().ExecuteDialog(this);

    private void btnCheckUpdate_Click(object sender, EventArgs e) => autoUpdater.ForceCheckForUpdate(true);

    private void tiProfilePasswordChange_ItemClick(object sender, TileItemEventArgs e) =>
        new ChangePasswordDialog().ExecuteDialog(this);

    private void tiCatReferrerAdd_ItemClick(object sender, TileItemEventArgs e)
    {
        if (CachedRolePermissionChecker.CheckPermission(PermissionCodes.Catalog.ReferrerEdit))
        {
            new ReferrerEditorDialog(null).ExecuteDialog(this);
        }
    }

    private void tiCatReferrerBrowse_ItemClick(object sender, TileItemEventArgs e)
    {
        CachedRolePermissionChecker.PrefetchCache(true);
        var accessFlags = ReferrerCatalogAccessLevel.None;
        if (CachedRolePermissionChecker.CheckPermission(PermissionCodes.Catalog.ReferrerBrowse))
            accessFlags = ReferrerCatalogAccessLevel.BrowseCatalog;
        if (CachedRolePermissionChecker.CheckPermission(PermissionCodes.Catalog.ReferrerEdit))
            accessFlags |= ReferrerCatalogAccessLevel.EditCatalog;
        new ReferrerCatalogBrowserDialog(accessFlags).ExecuteDialog(this);
    }

    private void btnMinimize_Click(object sender, EventArgs e) => WindowState = FormWindowState.Minimized;

    private void tiBOIncomeStatement_ItemClick(object sender, TileItemEventArgs e)
    {
        DateTime dtFrom = DateTime.Now, dtTo = DateTime.Now;
        var generateDynamicReport = false;
        var summateCanceled = true;
        var inclAuxLabs = true;
        GroupedLabOrdersCollection groupedColl = null;
        while (true)
        {
            using var frm = new IncomeStatementReportDialog(true);
            frm.GenerateDynamicReport = generateDynamicReport;
            frm.IncludeAuxiliaryLabs = inclAuxLabs;
            frm.SummateCanceledInvoices = summateCanceled;
            frm.GroupedOrdersCollection = groupedColl;

            if (!frm.ExecuteDialog(this, ref dtFrom, ref dtTo))
                return;

            var dto = frm.IncomeStatementPrintDto();
            PrintHelper.PrintIncomeStatementReport(dto);

            // cache the items for subsequent use
            generateDynamicReport = frm.GenerateDynamicReport;
            inclAuxLabs = frm.IncludeAuxiliaryLabs;
            summateCanceled = frm.SummateCanceledInvoices;
            groupedColl = frm.GroupedOrdersCollection;
        }
    }

    private void tiBOReceivablesSummary_ItemClick(object sender, TileItemEventArgs e)
    {
        using var frm = new ReceivablesSummaryReportDialog();
        if (!frm.ExecuteDialog(this)) return;
        var dto = frm.CompilePrintDto();
        PrintHelper.PrintReceivablesSummaryReport(dto);
    }

    private void tiBODailyReceivables_ItemClick(object sender, TileItemEventArgs e)
    {
        if (!AccountsDateReferrerCategorySelectionDialog.ExecuteDialog(this,
                DateReferrerCategoryDialogOption.OnlySingleDate, out var dt, out var _, out _)) return;
        var report = new DailyReceivablesReportBuilder { TargetDate = dt };
        WaitFormControl.WaitOperation(this, report.CompileReport, "Compiling report...");
        var dto = report.CompilePrintDto(CurrentUserContext.UserDisplayName);
        PrintHelper.PrintDailyReceivablesSummaryReport(dto);
    }

    private void tiBOFinancialAudit_ItemClick(object sender, TileItemEventArgs e)
    {
        if (!AccountsDateReferrerCategorySelectionDialog.ExecuteDialog(this,
                DateReferrerCategoryDialogOption.OnlyDateRange,
                out var dtFrom, out var dtTo, out _)) return;

        var compiler = new InvoiceFinancialAuditTrailReportBuilder
        {
            DateFrom = dtFrom.Date,
            DateTo = SharedUtilities.LastMinuteOfDay(dtTo),
            TransactionExclude = InvoiceTransactionType.Payment,
            RemoveInitialTransactions = false
        };
        InvoiceFinancialAuditTrailReportPrintDto dto = null;
        WaitFormControl.WaitOperation(this,
            () => dto = compiler.ToPrintDto(CurrentUserContext.UserDisplayName),
            "Compiling report...");
        PrintHelper.PrintInvoiceAuditTrailReport(dto);
    }

    private void tiBOOutstandingCollection_ItemClick(object sender, TileItemEventArgs e)
    {
        if (!AccountsDateReferrerCategorySelectionDialog.ExecuteDialog(
                this,
                DateReferrerCategoryDialogOption.SingleDateAndReferrer,
                out var dt, out var @_, out var refCat)) return;

        var reportBuilder = new OutstandingDuesCollectionReportBuilder();
        reportBuilder.SetSingleDateRange(dt);
        if (refCat > 0)
        {
            reportBuilder.ReferrerCategory = refCat;
        }

        WaitFormControl.WaitOperation(this,
            () => reportBuilder.CompileReport(false),
            "Compiling report...");
        var dto = reportBuilder.ToPrintDto(CurrentUserContext.UserDisplayName);
        PrintHelper.PrintOutstandingCollectionReport(dto);
    }

    private void tiBODailyDuesCollection_ItemClick(object sender, TileItemEventArgs e)
    {
        if (!AccountsDateReferrerCategorySelectionDialog.ExecuteDialog(
                this,
                DateReferrerCategoryDialogOption.SingleDateAndReferrer,
                out var dt, out var @_, out var refCat)) return;

        var reportBuilder = new OutstandingDuesCollectionReportBuilder
        {
            OrderCreationMustBeWithinDateRange = true
        };
        reportBuilder.SetSingleDateRange(dt);
        if (refCat > 0)
        {
            reportBuilder.ReferrerCategory = refCat;
        }

        WaitFormControl.WaitOperation(this, () => reportBuilder.CompileReport(false), "Compiling report...");
        var dto = reportBuilder.ToPrintDto(CurrentUserContext.UserDisplayName);
        PrintHelper.PrintOutstandingCollectionReport(dto);
    }

    private void tiAdminBackend_ItemClick(object sender, TileItemEventArgs e) => AdminBackendDialog.ExecuteDialog(this);

    private void tiThermalLabelPrint_ItemClick(object sender, TileItemEventArgs e)
    {
    }

    private void tiMQHeartbeat_ItemClick(object sender, TileItemEventArgs e) =>
        MessageQueueToolDialog.ExecuteDialog(this);

    private void tiMarketingReports_ItemClick(object sender, TileItemEventArgs e)
    {
        using var frm = new ReferralViewerDialog();
        frm.ShowDialog(this);
    }

    private void tiPhleboUtilization_ItemClick(object sender, TileItemEventArgs e)
    {
        DateTime start = DateTime.Now, end = DateTime.Now;
        using var frm = new DateRangeSelectDialog();
        if (frm.ShowDialog(this) == DialogResult.OK)
        {
            start = frm.DateFrom;
            end = frm.DateTo;
        }

        var compiler = new PhlebotomyUtilizationReportCompiler { DateFrom = start, DateTo = end };
        compiler.CompileReport();
        if (compiler.InvoiceUtilizations.Count > 0)
        {
            PrintHelper.PrintPhlebotomyUtilizationReport(compiler.ToPrintDto());
        }
        else
        {
            MessageDlg.Info("No utilization data found.");
        }
    }

    private void tiTestNew_ItemClick(object sender, TileItemEventArgs e)
    {
        using var frm = new SimpleTestAddEditDialog();
        frm.UpdateControls();
        frm.ShowDialog(this);
    }

    private void tiTestEdit_ItemClick(object sender, TileItemEventArgs e)
    {
        var testIds = LabTestQuickSelectionDialog.ExecuteDialog(this, false);
        foreach (var testId in testIds)
        {
            using var frm = new SimpleTestAddEditDialog();
            frm.CurrentLabTest = LabTestSlice.AssembleFrom(LabTestsRepository.FindById(testId), true);
            frm.UpdateControls();
            frm.ShowDialog(this);
        }
    }

    private void tiBOUserSummaries_ItemClick(object sender, TileItemEventArgs e)
    {
        using var frm = new ReportDateRangeSelectorDialog();
        frm.SingleDate = true;
        frm.UpdateControls();
        if (frm.ShowDialog(this) != DialogResult.OK) return;
        var report = new ReceptionistSummaryReportBuilder();
        report.SetSingleDateRange(frm.DateFrom);
        report.CompileReport();
        var dto = report.ToPrintDto();
        PrintHelper.PrintReceptionistSummaryReport(dto);
    }

    private void tiCollatorManifest_ItemClick(object sender, TileItemEventArgs e)
    {
        using var frm = new CollatorManifestReportDialog();
        frm.ShowDialog(this);
    }

    private void tiDuesRegister_ItemClick(object sender, TileItemEventArgs e)
    {
        using var frm = new DuesRegisterReportDialog();
        if (frm.ShowDialog(this) != DialogResult.OK) return;

        var report = new DuesRegisterReportBuilder
        {
            InvoiceDate = frm.InvoiceDate,
            ReferrerCategory = frm.ReferrerCategory
        };
        report.CompilePrimalReport();
        var dto = report.ToPrintDto();
        PrintHelper.PrintDailyDuesRegister(dto);
    }

    private void tiBOCancellationReport_ItemClick(object sender, TileItemEventArgs e)
    {
        using var frm = new ReportDateRangeSelectorDialog();
        frm.SingleDate = false;
        if (frm.ShowDialog(this) != DialogResult.OK) return;

        var report = new OrderCancellationReportBuilder
        {
            DateFrom = frm.DateFrom,
            DateTo = frm.DateTo
        };
        WaitFormControl.WaitOperation(this,
            () => FaultHandler.Shield(report.CompileReport),
            "Compiling report...");
        var dto = report.ToPrintDto();
        PrintHelper.PrintServiceCancellationReport(dto);
    }

    private void tiBOReferrerBizContrib_ItemClick(object sender, TileItemEventArgs e)
    {
        using var frm = new ReferralCriteriaDialog();
        if (frm.ShowDialog(this) != DialogResult.OK)
            return;

        ReferrerBusinessContributionsReportBuilder builder;

        if (frm.AssociateLabId > 0)
        {
            builder = new ReferrerBusinessContributionsReportBuilder
            {
                DateFrom = frm.DateFrom,
                DateTo = frm.DateTo,
                AssociateLabId = frm.AssociateLabId
            };
        }
        else if (frm.CorporateClientId > 0)
        {
            builder = new ReferrerBusinessContributionsReportBuilder
            {
                DateFrom = frm.DateFrom,
                DateTo = frm.DateTo,
                CorporateId = frm.CorporateClientId
            };
        }
        else if (frm.AffiliateId > 0)
        {
            builder = new ReferrerBusinessContributionsReportBuilder
            {
                DateFrom = frm.DateFrom,
                DateTo = frm.DateTo,
                AffiliateId = frm.AffiliateId
            };
        }
        else if (frm.ReferrerId > 0)
        {
            builder = new ReferrerBusinessContributionsReportBuilder
            {
                DateFrom = frm.DateFrom,
                DateTo = frm.DateTo,
                ReferrerId = frm.ReferrerId
            };
        }
        else
        {
            MessageDlg.Warning("No referrer selected!");
            return;
        }

        WaitFormControl.WaitOperation(this,
            () => FaultHandler.Shield(builder.CompileReport),
            "Compiling report...");
        var dto = builder.ToPrintDto(false);
        PrintHelper.PrintReferrerBusinessContributionsReport(dto);
    }

    private void tiBOOutstandingStatement_ItemClick(object sender, TileItemEventArgs e)
    {
        var proceed = true;
        DateTime dtFrom = DateTime.Now, dtTo = DateTime.Now;
        while (proceed)
        {
            using var frm = new IncomeStatementReportDialog(false);
            proceed = frm.ExecuteDialog(this, ref dtFrom, ref dtTo);
            if (!proceed) continue;
            var dto = frm.OutstandingStatementPrintDto();
            PrintHelper.PrintOutstandingStatementReport(dto);
        }
    }

    private void tiStaffPerformanceReport_ItemClick(object sender, TileItemEventArgs e)
    {
        using var frm = new StaffPerformanceReportDialog();
        if (frm.ShowDialog(this) != DialogResult.OK) return;

        var sprc = new StaffPerformanceReportCompiler(frm.SelectedRoleCode, frm.SelectedRoleName);
        var evType = frm.SelectedAuditEventType;
        var dtFrom = frm.DateFrom;
        var dtTill = frm.DateTill;
        WaitFormControl.WaitOperation(this, () =>
            FaultHandler.Shield(() => sprc.CompileReport(evType, dtFrom, SharedUtilities.LastMinuteOfDay(dtTill))));

        var dto = sprc.ToPrintDto();
        PrintHelper.PrintStaffPerformanceReport(dto);
    }

    private void tiBulkDispatch_ItemClick(object sender, TileItemEventArgs e)
    {
        using var frm = new BulkDispatchDialog();
        frm.ShowDialog(this);
    }

    private void tiWorkstationRegistry_ItemClick(object sender, TileItemEventArgs e)
    {
        using var frm = new WorkstationRegistryViewerDialog();
        frm.ShowDialog(this);
    }

    private void tiTestUtilizationReport_ItemClick(object sender, TileItemEventArgs e)
    {
        using var frm = new LabTestUtilizationReportDialog();
        frm.ShowDialog(this);
    }

    private void tiBIUtilizationReport_ItemClick(object sender, TileItemEventArgs e)
    {
        using var frm = new ReportDateRangeSelectorDialog();
        frm.SingleDate = false;
        if (frm.ShowDialog(this) != DialogResult.OK) return;
        var compiler = new BillableItemUtilizationReportCompiler
        {
            DateStart = frm.DateFrom,
            DateEnd = frm.DateTo,
            SortType = UtilizationSortType.Volume
        };
        WaitFormControl.WaitOperation(this,
            () => FaultHandler.Shield(compiler.CompileReport),
            "Compiling report...");
        var dto = compiler.ToPrintDto();
        dto.PrintDate = SharedUtilities.DateTimeToShortString(AppSysRepository.GetServerTime());
        dto.UserName = CurrentUserContext.UserDisplayName;
        PrintHelper.PrintBillableItemUtilizationReport(dto);
    }

    private void tiBORefEligibilitySum_ItemClick(object sender, TileItemEventArgs e)
    {
        using var frm = new ReportDateRangeSelectorDialog();
        if (frm.ShowDialog(this) != DialogResult.OK) return;

        var rb = new ReferralEligibilitySummaryReportBuilder
        {
            BeginDate = frm.DateFrom,
            EndDate = frm.DateTo,
            LabsFilterMode = FilterMode.ExcludeItems
        };
        rb.LabIds.Clear();
        WaitFormControl.WaitOperation(this, rb.CompileInvoicesList, "Compiling report...");
        var dto = rb.ToPrintDto();
        PrintHelper.PrintReferralEligibilitySummaryReport(dto);
    }

    private void tiStaffShiftHistory_ItemClick(object sender, TileItemEventArgs e)
    {
        using var frm = new ShiftStaffSelectionDialog();
        if (frm.ShowDialog(this) != DialogResult.OK) return;

        using var dlg = new ShiftSearchDialog(frm.StaffId);
        dlg.ShowDialog(this);
    }

    private void tiNewExternalOrder_ItemClick(object sender, TileItemEventArgs e)
    {
        if (!verifyOpenShift()) return;

        short labId = -1;
        var labName = string.Empty;

        using var frm = new RequestingLabsDialog();
        if (frm.ExecuteDialog(this))
        {
            labId = frm.RequestingLabId;
            labName = frm.RequestingLabName;
        }

        if (labId > 0)
        {
            new LabOrderDialog(labId, labName).ExecuteDialog(this);
        }
    }

    private void tiBODiscountOverageReport_ItemClick(object sender, TileItemEventArgs e)
    {
        using var dlg = new DiscountOverageReportDialog();
        if (dlg.ShowDialog(this) != DialogResult.OK) return;

        var rb = new DiscountOverageReportBuilder
        {
            DateFrom = dlg.DateFrom,
            DateTo = dlg.DateTo,
            DiscountThreshold = dlg.DiscountThreshold,
            SortByDiscountPercentage = dlg.SortByDiscountPercentage
        };

        var dto = rb.CompileReport();
        PrintHelper.PrintDiscountOverages(dto);
    }

    private void tiBOLabwiseDailyIncomeReport_ItemClick(object sender, TileItemEventArgs e)
    {
        DateTime dtFrom = DateTime.Now, dtTo = DateTime.Now;
        var generateDynamicReport = false;
        var summateCanceled = true;
        var inclAuxLabs = false;
        GroupedLabOrdersCollection groupedColl = null;
        while (true)
        {
            using var frm = new IncomeStatementReportDialog(true);
            frm.GenerateDynamicReport = generateDynamicReport;
            frm.IncludeAuxiliaryLabs = inclAuxLabs;
            frm.SummateCanceledInvoices = summateCanceled;
            frm.GroupedOrdersCollection = groupedColl;

            if (!frm.ExecuteDialog(this, ref dtFrom, ref dtTo))
                return;

            var dto = frm.LabwiseDailyIncomePrintDto();
            PrintHelper.PrintLabwiseDailyIncomeSummaryReport(dto);

            // cache the items for subsequent use
            generateDynamicReport = frm.GenerateDynamicReport;
            inclAuxLabs = frm.IncludeAuxiliaryLabs;
            summateCanceled = frm.SummateCanceledInvoices;
            groupedColl = frm.GroupedOrdersCollection;
        }
    }

    private void tiCustomers_ItemClick(object sender, TileItemEventArgs e)
    {
        using var frm = new CustomerSearchForm();
        frm.ShowDialog(this);
    }
}