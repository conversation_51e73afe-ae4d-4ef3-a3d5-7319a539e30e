﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: DateRangeSelectDialog.cs 1215 2014-03-11 08:35:35Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Windows.Forms;
using DevExpress.XtraEditors;

namespace LabMaestro.Controls.Win
{
    public partial class DateRangeSelectDialog : XtraForm
    {
        public DateRangeSelectDialog()
        {
            InitializeComponent();
            WinUtils.ApplyTheme(this);
            chkCustomDateRange_CheckedChanged(null, null);
        }

        public DateTime DateFrom { get; set; }
        public DateTime DateTo { get; set; }

        private void DateRangeSelectDialog_Load(object sender, EventArgs e)
        {
            dtFrom.DateTime = DateTime.Now;
            dtTo.DateTime = DateTime.Now;
        }

        private void btnApply_Click(object sender, EventArgs e)
        {
            if (chkCustomDateRange.Checked)
            {
                DateFrom = dtFrom.DateTime;
                DateTo = dtTo.DateTime;
                DialogResult = DialogResult.OK;
                return;
            }

            var index = cboQuickDateRange.SelectedIndex;
            if (index < 1)
            {
                MessageDlg.Info("Please select a date-range.");
                return;
            }

            switch (index)
            {
                case 1:
                    DateFrom = DateTime.Now;
                    break;
                case 2:
                    DateFrom = DateTime.Now.AddDays(-1);
                    DateTo = DateFrom;
                    DialogResult = DialogResult.OK;
                    return;
                case 3:
                    DateFrom = DateTime.Now.AddDays(-3);
                    break;
                case 4:
                    DateFrom = DateTime.Now.AddDays(-7);
                    break;
                case 5:
                    DateFrom = DateTime.Now.AddDays(-15);
                    break;
                case 6:
                    DateFrom = DateTime.Now.AddDays(-30);
                    break;
                case 7:
                    DateFrom = DateTime.Now.AddDays(-90);
                    break;
            }
            DateTo = DateTime.Now;
            DialogResult = DialogResult.OK;
        }

        private void chkCustomDateRange_CheckedChanged(object sender, EventArgs e)
        {
            dtFrom.Enabled = chkCustomDateRange.Checked;
            dtTo.Enabled = chkCustomDateRange.Checked;
        }
    }
}