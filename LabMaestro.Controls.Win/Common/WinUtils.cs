﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: WinUtils.cs 750 2013-07-05 13:13:26Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using DevExpress.LookAndFeel;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraGrid.Views.Grid;
using LabMaestro.BusinessLogic;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Shared;
using Xceed.Grid;
using Xceed.Grid.Collections;

namespace LabMaestro.Controls.Win
{
    internal sealed class StringComparer : IComparer
    {
        public int Compare(object x, object y) => string.Compare(((string)x), (string)y, StringComparison.Ordinal);
    }

    public static class WinUtils
    {
        public static UserLookAndFeel DefaultLookAndFeel { get; set; }

        public static string InputBox(string title, string promptText)
        {
            var value = string.Empty;
            using var form = new XtraForm();
            ApplyTheme(form);
            form.Font = new Font("Segoe UI", 10.0f);
            var label = new Label();
            var textBox = new TextEdit();
            var buttonOk = new SimpleButton();
            var buttonCancel = new SimpleButton();

            form.Text = title;
            label.Text = promptText;
            textBox.Text = value;

            buttonOk.Text = "OK";
            buttonCancel.Text = "Cancel";
            buttonOk.DialogResult = DialogResult.OK;
            buttonCancel.DialogResult = DialogResult.Cancel;

            label.SetBounds(9, 20, 372, 13);
            textBox.SetBounds(12, 36, 372, 20);
            buttonOk.SetBounds(228, 72, 75, 23);
            buttonCancel.SetBounds(309, 72, 75, 23);

            label.AutoSize = true;
            textBox.Anchor = textBox.Anchor | AnchorStyles.Right;
            buttonOk.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            buttonCancel.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;

            form.ClientSize = new Size(396, 107);
            form.Controls.AddRange(new Control[] { label, textBox, buttonOk, buttonCancel });
            form.ClientSize = new Size(Math.Max(300, label.Right + 10), form.ClientSize.Height);
            form.FormBorderStyle = FormBorderStyle.FixedDialog;
            form.StartPosition = FormStartPosition.CenterScreen;
            form.MinimizeBox = false;
            form.MaximizeBox = false;
            form.AcceptButton = buttonOk;
            form.CancelButton = buttonCancel;

            if (form.ShowDialog() == DialogResult.OK)
            {
                value = textBox.Text;
            }

            return value;
        }

        public static bool TextEditVerifyString(this TextEdit editor, int maxLen, int minLen = 1)
        {
            var data = editor.Text.Trim();
            var result = (data.Length >= minLen && data.Length <= maxLen);
            if (!result)
            {
                editor.Select();
                editor.Focus();
            }

            return result;
        }

        public static void UnlockControls()
        {
            XtraMessageBox.AllowCustomLookAndFeel = false;
            //Licenser.LicenseKey = @"GRD43-R00G0-UZFRW-ZRLA";
            Licenser.LicenseKey = @"GRD43-U8J50-TXM81-GTGA";
            Licenser.Unlock();
        }

        public static void ApplyTheme(XtraForm form)
        {
            form.AutoScaleMode = AutoScaleMode.None; // disable autoscaling
            form.LookAndFeel.SetSkinStyle(CurrentUserContext.SelectedTheme);
            form.Refresh();

            /*
            var barmans = GetAllChildControls(form, typeof (BarAndDockingController));
            if (barmans != null)
            {
                foreach (var control in barmans)
                {
                    //var barman = control as BarAndDockingController;
                }
            }
            */

            DefaultLookAndFeel = form.LookAndFeel;
        }

        public static IEnumerable<Control> GetAllChildControls(Control control, Type type)
        {
            var controls = control.Controls.Cast<Control>();

            var enumerable = controls as IList<Control> ?? controls.ToList();
            return enumerable.SelectMany(ctrl => GetAllChildControls(ctrl, type))
                .Concat(enumerable)
                .Where(c => c.GetType() == type);
        }

        private static string buildTitleString(string title)
        {
            return String.Format("{0} - {1} @ {2} <{3}>",
                title,
                CurrentUserContext.UserDisplayName,
                SharedUtilities.GetLocalIpAddressString(GlobalSettingsHelper.System.IPSubnetPrefix),
                AppVersion.GetVersionString());
        }

        public static void SetFormTitle(Form form, string title)
        {
            form.Text = buildTitleString(title);
        }

        public static void PopulateLookupControl(LookUpEdit ctl,
            object dataSource,
            string displayMember,
            string valueMember,
            IEnumerable<string> columns,
            int searchColumnIndex = 1)
        {
            ctl.Properties.Columns.Clear();
            foreach (var column in columns)
            {
                ctl.Properties.Columns.Add(new LookUpColumnInfo(column, 0));
            }

            ctl.Properties.DisplayMember = displayMember;
            ctl.Properties.ValueMember = valueMember;
            ctl.Properties.DataSource = dataSource;
            ctl.Properties.BestFitMode = BestFitMode.BestFitResizePopup;
            if (searchColumnIndex > 0)
            {
                ctl.Properties.SearchMode = SearchMode.AutoComplete;
                ctl.Properties.AutoSearchColumnIndex = searchColumnIndex;
            }
        }

        public static void SetFormTitle(XtraForm form, string title)
        {
            form.Text = buildTitleString(title);
        }

        public static void ClearGridViewRows(GridView gridView)
        {
            try
            {
                gridView.BeginUpdate();
                for (var i = 0; i < gridView.RowCount; i++)
                {
                    var handle = gridView.GetRowHandle(i);
                    gridView.DeleteRow(handle);
                }
            }
            finally
            {
                gridView.EndUpdate();
            }
        }

        public static Column GridAddColumn(this GridControl grid, string fieldName, string title,
            int index, int width, bool canBeSorted = false)
        {
            var col = new Column(fieldName)
            {
                Title = title,
                VisibleIndex = index,
                Width = width,
                MinWidth = width,
                //MaxWidth = width,
                CanBeGrouped = false,
                CanBeSorted = canBeSorted,
                Visible = true
            };

            if (canBeSorted)
            {
                col.DataComparer = new StringComparer();
            }

            grid.Columns.Add(col);
            return col;
        }

        public static void GridWireCellToolTips(this GridControl grid, CellList cells, MouseEventHandler handler)
        {
            foreach (Cell cell in cells)
            {
                cell.MouseMove += handler;
            }
        }

        public static IEnumerable<Control> EnumerateControl(Control form)
        {
            foreach (Control childControl in form.Controls)
            {
                // Recurse child controls.
                foreach (var grandChild in EnumerateControl(childControl))
                {
                    yield return grandChild;
                }

                yield return childControl;
            }
        }

        public static Color GetTransactionColor(InvoiceTransactionType txType)
        {
            return txType switch
            {
                InvoiceTransactionType.Payment => Color.GreenYellow,
                InvoiceTransactionType.CashDiscount => Color.Gold,
                InvoiceTransactionType.DiscountRebate => Color.LightCyan,
                InvoiceTransactionType.Refund => Color.LightPink,
                _ => Color.White
            };
        }
    }
}