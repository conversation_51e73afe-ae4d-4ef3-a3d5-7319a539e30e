﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="DevExpress.Utils.v24.2" name="DevExpress.Utils.v24.2, Version=24.2.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="imlLanguages.ImageStream" type="DevExpress.Utils.ImageCollectionStreamer, DevExpress.Utils.v24.2" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAEAAAD/////AQAAAAAAAAAMAgAAAFpEZXZFeHByZXNzLlV0aWxzLnYxMS4yLCBWZXJzaW9uPTExLjIu
        OC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI4OGQxNzU0ZDcwMGU0OWEMAwAAAFFT
        eXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRv
        a2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAAChEZXZFeHByZXNzLlV0aWxzLkltYWdlQ29sbGVjdGlvblN0
        cmVhbWVyAgAAAAlJbWFnZVNpemUERGF0YQQHE1N5c3RlbS5EcmF3aW5nLlNpemUDAAAAAgIAAAAF/P//
        /xNTeXN0ZW0uRHJhd2luZy5TaXplAgAAAAV3aWR0aAZoZWlnaHQAAAgIAwAAABYAAAAPAAAACQUAAAAP
        BQAAAKwHAAACQQQAAIlQTkcNChoKAAAADUlIRFIAAAAWAAAADwgDAAAA17PPWgAAAAFzUkdCAK7OHOkA
        AAAEZ0FNQQAAsY8L/GEFAAADAFBMVEVFkn0gfGNXnIoyhnApgWpOl4M7jHb1U2X2Y3T3coH3anr2W2z1
        RFj1S1/4eoj0PVL0N034gI74hpPzMUctZFG3l5YRclgAcFMYdl0LZUxrlojtiJP2eogBfFuiTFDbUWAl
        emKCr6LlQVKwPkfyPVKSlI3Fk5W0PUY4dWJyfHLZeYLvZHSCiICHpJlmcGWAtaeXcnHxN03bN0iqjowl
        bFd+sKJdZFlpppXHjZB+T02Dr6J9sKLYlJkVa1MQaFD3gI4Ha1B3p5m0kZF4rZ87i3XtVWWnV1tjeW1/
        W1eVfHjjb3tDWk3ImJvoM0eFU1IDWkKESUnBmpt7saLve4hgoY8CaEwDaU1+rJ+XSEsEZ0xnkIEBZEmc
        pJ1xhXrya3r0h5N3oZRcmolGeWfalJkBdFXLTloMZUv1W2ylopzuTF+/gIRTVkzwW2ySRkiIb2oYd15g
        oZARc1loppYAcVMBY0kKb1Rvqpp2rp8FbFB7saMAaU2AtaYAaEwBeloDX0V/f3+AgICBgYGCgoKDg4OE
        hISFhYWGhoaHh4eIiIiJiYmKioqLi4uMjIyNjY2Ojo6Pj4+QkJCRkZGSkpKTk5OUlJSVlZWWlpaXl5eY
        mJiZmZmampqbm5ucnJydnZ2enp6fn5+goKChoaGioqKjo6OkpKSlpaWmpqanp6eoqKipqamqqqqrq6us
        rKytra2urq6vr6+wsLCxsbGysrKzs7O0tLS1tbW2tra3t7e4uLi5ubm6urq7u7u8vLy9vb2+vr6/v7/A
        wMDBwcHCwsLDw8PExMTFxcXGxsbHx8fIyMjJycnKysrLy8vMzMzNzc3Ozs7Pz8/Q0NDR0dHS0tLT09PU
        1NTV1dXW1tbX19fY2NjZ2dna2trb29vc3Nzd3d3e3t7f39/g4ODh4eHi4uLj4+Pk5OTl5eXm5ubn5+fo
        6Ojp6enq6urr6+vs7Ozt7e3u7u7v7+/w8PDx8fHy8vLz8/P09PT19fX29vb39/f4+Pj5+fn6+vr7+/v8
        /Pz9/f3+/v7///9ORSYAAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAALpJREFU
        KFNtyNsLwWAYx/F3Ww0RtVGORc5JSVEkKadEuZFDcSFyIafa2Cj+dr/3fXa5z83z/L4s9XVxZMabNNoZ
        58MxmWEJq9mhs+3Tb1nIL65a0DQtERE/ID9gkQwKeT4AWYa4j5z3fMkyMoOJl1R2fDGGrKrqoOYnlzIW
        ICtw85DhmC9FQZagFCCjNF+ShHyHaC4kNPkA5CcXXuu6Xm+JH5BtoXstZqc9+m0b+UM2p5jz4Zhs+XMx
        /wNsS2GAQvIsuAAAAABJRU5ErkJggmMDAACJUE5HDQoaCgAAAA1JSERSAAAAFgAAAA8IAgAAAG8PqD8A
        AAABc1JHQgCuzhzpAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29mdHdhcmUAQWRvYmUgSW1hZ2VSZWFk
        eXHJZTwAAALoSURBVDhPnZJdSFNhGMfP1RJOJudCtvnRFMdgF4JBMkeRYLtQWoEgg8aW85jmxxBpqKXm
        bNoqPxKaIH724TRYZEZhfoxCFBIE05puKZlzO6hbO3Med+bY8PRMb6sL4Xfx/z/P83/e97wcZPOqnDT0
        MAHaSwXb2ydxvAfHn+F4Hwj6IORUqwEQR3UoQmvgQat5fcTsrW+ipqYZhkGqa0ddb8Y82och2+ohw4y8
        XYAhHO/G8V76IBxZUV4OAmxBQTdeYhwemnP3D3t0rWG7I8wwHR1mRCi8o3s0tWwyk9X3qNEx2LqwRCgU
        fTKZwR8IO1QqAARYeaFxxjTja9CThj4mHHKRdFvbRGpqDcLnV8nlndfyuoe7zE6Ndkf7mKF8G5tkdnaL
        PxCy5+UBIMAudr76XaqhPprhmPEpW27uUwjCDRAeTyOVdojFTcC7MQs9PUtNfIIhO7FH0aF1iQQAEfj6
        DerBTQfh2ocxsbgRkEpb+fxKxE1SVCC0R/+dtfR04F9dCLrJfWQpJWVNJLIJBCcAgksCAfI9PX01Lc2a
        lHQCIGgRiZBtt9fnD/4La2Ii8J8BiMNzlsfHq6OjC4HnQ/PwkKEd965plJ6dg+RKfDwAYnWDhNZxHcaO
        5yGYnKxG4uLUHE5Z9OkSfcsk/FrUh0kiQzKv1qEoHlnB5QIgwFqs24x/z6Es+3lR2lNn5PK1bHZpQkIF
        wmYXZWTo5xedcIirrnlbLBlvfnFWqIuKUvn2g8tsNgACbEyM+v2YJXKXvkHiXOZigyFXPgBbEKWyy0uH
        GYIgcmSEDNfXms5wqlgsBYuVvwsrYmMBEGBZLOUpVlFVzcgBfKxlxXEhx1l8+67mJeIhSbi8U3T5R23r
        ddUghlUcUYphxXB/K48HgACLYSUYVolht7KynqzZSeYwvKWpX750BbFVaxfSMl839grP30dRFYreRNEi
        FM1HUcWGY+sLmwOAAIuiN45akS6HW9478Nnj8fzqN/4BbJxDRIChqGYAAAAASUVORK5CYIIL
</value>
  </data>
</root>