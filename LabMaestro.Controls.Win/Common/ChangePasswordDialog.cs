﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ChangePasswordDialog.cs 740 2013-07-04 15:40:09Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;

namespace LabMaestro.Controls.Win
{
    public partial class ChangePasswordDialog : XtraForm, IExecutableDialog
    {
        public ChangePasswordDialog()
        {
            InitializeComponent();
            WinUtils.ApplyTheme(this);
            //WinUtils.SetFormTitle(this, "Change Password");
        }

        public bool ExecuteDialog(IWin32Window parent)
        {
            UpdateControls();
            ShowDialog(parent);
            return true;
        }

        public void UpdateControls()
        {
        }

        private void ChangePasswordDialog_Load(object sender, EventArgs e)
        {
            if (CurrentUserContext.UserId <= 0)
            {
                MessageDlg.Error("You are not logged in!");
                DialogResult = DialogResult.Abort;
            }
        }

        private void btnChangePassword_Click(object sender, EventArgs e)
        {
            if (!validateInput()) return;

            var password = txtPasswordNew.Text;
            WaitFormControl.WaitOperation(this,
                                          () =>
                                              {
                                                  UsersRepository.UpdateUserPassword(CurrentUserContext.UserId,
                                                                                     password);
                                                  CurrentUserContext.UpdatePassword(password);
                                                  PatientLabOrdersRepository.Reset();
                                              },
                                          "Updating...");
            
            MessageDlg.Info("Password updated.");
            DialogResult = DialogResult.OK;
        }

        private bool validateInput()
        {
            var currPass = txtPasswordCurrent.Text;
            if (string.IsNullOrEmpty(currPass))
            {
                MessageDlg.Error("Please enter your current password!");
                return false;
            }

            if (!UsersRepository.VerifyUserPassword(CurrentUserContext.UserId, currPass))
            {
                MessageDlg.Error("You current password does not match!");
                return false;
            }

            var newPass = txtPasswordNew.Text;
            if (string.IsNullOrEmpty(newPass) || !AuthHelper.IsValidPassword(newPass))
            {
                var msg = "You new password does not conform to the recommended criteria!\n";
                msg += "Password must be between 5 to 10 characters in length and\n";
                msg += "must contain only English alphabets (A-Z, a-z) and/or numeric digits (0-9).";
                MessageDlg.Error(msg);
                return false;
            }

            var newPassConfirm = txtPasswordNewConfirm.Text;
            if (string.IsNullOrEmpty(newPassConfirm) || newPass.CompareTo(newPassConfirm) != 0)
            {
                MessageDlg.Error("You new passwords do not match!");return false;
            }

            return true;
        }
    }
}