﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: WaitFormControl.cs 740 2013-07-04 15:40:09Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Windows.Forms;
using DevExpress.XtraSplashScreen;
using DevExpress.XtraWaitForm;
using LabMaestro.Infrastructure.Client;

namespace LabMaestro.Controls.Win
{
    public partial class WaitFormControl : WaitForm
    {
        public WaitFormControl()
        {
            InitializeComponent();
            progressPanel1.AutoHeight = true;
            if (!string.IsNullOrEmpty(CurrentUserContext.SelectedTheme))
            {
                progressPanel1.LookAndFeel.SetSkinStyle(CurrentUserContext.SelectedTheme);
            }
        }

        public static void WaitOperation(SplashScreenManager manager, Action command)
        {
            try
            {
                manager.ShowWaitForm();
                command();
            }
            finally
            {
                manager.CloseWaitForm();
            }
        }

        public static void WaitOperation(Form parent, Action command, string description = "Loading...")
        {
            var oldCur = WaitStart(parent, description: description);
            try
            {
                command();
            }
            finally
            {
                WaitEnd(parent, oldCur);
            }
        }

        public static Cursor WaitStart(Form parent, string caption = "Please wait", string description = "Loading...")
        {
            var oldCur = parent.Cursor;
            parent.Cursor = Cursors.WaitCursor;

            if (SplashScreenManager.Default == null || !SplashScreenManager.Default.IsSplashFormVisible)
            {
                SplashScreenManager.ShowForm(parent, typeof (WaitFormControl), false, false);
                SplashScreenManager.Default.SetWaitFormCaption(caption);
                SplashScreenManager.Default.SetWaitFormDescription(description);
            }
            return oldCur;
        }

        public static void WaitStart(string caption = "Please wait", string description = "Loading...")
        {
            if (SplashScreenManager.Default == null || !SplashScreenManager.Default.IsSplashFormVisible)
            {
                SplashScreenManager.ShowForm(typeof (WaitFormControl), false, false);
                SplashScreenManager.Default.SetWaitFormCaption(caption);
                SplashScreenManager.Default.SetWaitFormDescription(description);
            }
        }

        public static void WaitEnd(Form parent, Cursor oldCursor)
        {
            if (SplashScreenManager.Default != null && SplashScreenManager.Default.IsSplashFormVisible)
            {
                SplashScreenManager.CloseForm();
            }
            parent.Cursor = oldCursor;
        }

        public static void WaitEnd()
        {
            if (SplashScreenManager.Default != null && SplashScreenManager.Default.IsSplashFormVisible)
            {
                SplashScreenManager.CloseForm();
            }
        }

        public override void SetCaption(string caption)
        {
            base.SetCaption(caption);
            progressPanel1.Caption = caption;
        }

        public override void SetDescription(string description)
        {
            base.SetDescription(description);
            progressPanel1.Description = description;
        }
    }
}