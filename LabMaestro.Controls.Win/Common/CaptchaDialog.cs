﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the organization nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: CaptchaDialog.cs 1053 2013-10-30 17:19:53Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;

namespace LabMaestro.Controls.Win
{
    public partial class CaptchaDialog : XtraForm
    {
        private string _captchaCode;

        public CaptchaDialog()
        {
            InitializeComponent();
            WinUtils.ApplyTheme(this);
            CaptchaLen = -1;
            AllowUpperCase = true;
            AllowNumeric = true;
        }

        public int CaptchaLen { get; set; }

        public bool AllowUpperCase { get; set; }

        public bool AllowNumeric { get; set; }

        private void generateCaptchaCode()
        {
            var strTable = @"abcdefghkmnpqrstuvwxyz";

            if (AllowUpperCase)
            {
                strTable += @"ABCDEFGHKMNPQRSTUVWXYZ";
            }

            if (AllowNumeric)
            {
                strTable += @"23456789";
            }

            var alphaTable = strTable.ToCharArray();
            var rand = new Random(Environment.TickCount);
            var captchaLen = CaptchaLen == -1 ? rand.Next(4, 6) : CaptchaLen;
            var sb = new StringBuilder();
            for (var i = 0; i < captchaLen; i++)
            {
                sb.Append(alphaTable[rand.Next(alphaTable.Length)]);
            }

            _captchaCode = sb.ToString();
            lblCaptcha.Text = _captchaCode;
            txtCaptcha.Text = "";
        }

        private void CaptchaDialog_Load(object sender, EventArgs e)
        {
            generateCaptchaCode();
        }

        private void btnOk_Click(object sender, EventArgs e)
        {
            if (txtCaptcha.Text.Trim() == _captchaCode)
            {
                DialogResult = DialogResult.OK;
            }
            else
            {
                XtraMessageBox.Show("You have entered the wrong code!", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                generateCaptchaCode();
            }
        }

        /// <summary>
        ///     Shows the CAPTCHA dialog
        /// </summary>
        /// <returns>True if the entered code matches the CAPTCHA, false otherwise</returns>
        public static bool ConfirmCaptcha(int captchaLen = -1, bool allowUpperCase = true, bool allowNumeric = true)
        {
            using (var form = new CaptchaDialog())
            {
                form.CaptchaLen = captchaLen;
                form.AllowNumeric = allowNumeric;
                form.AllowUpperCase = allowUpperCase;
                return (form.ShowDialog() == DialogResult.OK);
            }
        }

        private void CaptchaDialog_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                e.Handled = true;
                btnOk_Click(null, null);
            }
        }
    }
}