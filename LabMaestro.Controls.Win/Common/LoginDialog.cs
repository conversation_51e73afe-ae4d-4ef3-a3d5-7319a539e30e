﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: LoginDialog.cs 740 2013-07-04 15:40:09Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.ComponentModel;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using IdeaBlade.EntityModel;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;

namespace LabMaestro.Controls.Win
{
    /// <summary>
    ///     Displays a login dialog box for users
    /// </summary>
    public partial class LoginDialog : XtraForm, IExecutableDialog
    {
        public LoginDialog()
        {
            InitializeComponent();
            //WinUtils.ApplyTheme(this);
        }

        public string UserName { get; private set; }

        public string Password { get; private set; }

        public bool ExecuteDialog(IWin32Window parent)
        {
            UpdateControls();
            return (ShowDialog(parent) == DialogResult.OK);
        }

        public void UpdateControls()
        {
#if DEBUG
        txtUsername.Text = "masroor";
        txtPassword.Text = "samsam";
#endif
        }

        public static bool ExecuteUserLogin()
        {
            var success = false;
            while (!success)
            {
                using var login = new LoginDialog();
                if (login.ExecuteDialog(null))
                {
                    try
                    {
                        WaitFormControl.WaitStart(description: "Logging in...");
                        try
                        {
                            if (!DomainManager.IsConnected)
                            {
                                DomainManager.ConnectServer();
                            }

                            AuthHelper.Login(login.UserName, login.Password);
                            success = DomainManager.IsLoggedIn;
                        }
                        finally
                        {
                            WaitFormControl.WaitEnd();
                        }
                    }
                    catch (LoginException exc)
                    {
                        if (XtraMessageBox.Show(exc.Message + "\r\nDo you wish to retry logging in?",
                                "Login Error",
                                MessageBoxButtons.RetryCancel,
                                MessageBoxIcon.Error) != DialogResult.Retry)
                        {
                            break;
                        }
                    }
                }
                else if (MessageDlg.Confirm("Really quit?"))
                {
                    return false;
                }
            }

            return success;
        }

        private void btnLogin_Click(object sender, EventArgs e)
        {
            var username = txtUsername.Text.Trim();
            if (!AuthHelper.IsValidUsername(username))
            {
                XtraMessageBox.Show("Invalid username", "Error!", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            var password = txtPassword.Text.Trim();
            if (!AuthHelper.IsValidPassword(password))
            {
                XtraMessageBox.Show("Invalid password", "Error!", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            UserName = username;
            Password = password;

            DialogResult = DialogResult.OK;
        }

        private void txtPassword_Validating(object sender, CancelEventArgs e)
        {
            string error = null;
            var password = txtPassword.Text;
            if (password.Length == 0)
            {
                error = "Please enter the password";
                e.Cancel = true;
            }

            if (!AuthHelper.IsValidPassword(password))
            {
                error =
                    "Invalid password!\r\nThe password must be 4-16 characters long and should contain alphabets and numbers";
                e.Cancel = true;
            }

            errorProvider1.SetError((Control)sender, error);
        }

        private void txtUsername_Validating(object sender, CancelEventArgs e)
        {
            string error = null;
            var value = txtUsername.Text;
            if (value.Length == 0)
            {
                error = "Please enter the username";
                e.Cancel = true;
            }

            if (!AuthHelper.IsValidUsername(value))
            {
                error =
                    "Invalid username!\r\nThe username must be 4-16 characters long and should contain small letters and numbers";
                e.Cancel = true;
            }

            errorProvider1.SetError((Control)sender, error);
        }

        private void LoginDialog_Load(object sender, EventArgs e)
        {
            txtUsername.Select();
            txtUsername.Focus();
        }
    }
}