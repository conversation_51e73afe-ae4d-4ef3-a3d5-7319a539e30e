﻿using System;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;

namespace LabMaestro.Controls.Win;

public partial class RequestingLabsDialog : XtraForm, IExecutableDialog
{
    public RequestingLabsDialog()
    {
        InitializeComponent();
        WinUtils.ApplyTheme(this);
    }

    public short RequestingLabId { get; private set; }
    public string RequestingLabName { get; private set; }

    public bool ExecuteDialog(IWin32Window parent)
    {
        UpdateControls();
        return ShowDialog(parent) == DialogResult.OK;
    }

    public void UpdateControls() => populateLabsListbox();

    private void populateLabsListbox()
    {
        var labs = FaultHandler.Shield(() => AssociateOrganizationsRepository.GetAllLabs());
        lbReqLabs.Items.Clear();
        foreach (var lab in labs.Where(lab => lab.IsActive))
            lbReqLabs.Items.Add(new RequestingLabInfo(lab.Id, lab.Name));

        lbReqLabs.SelectedIndex = -1;
    }

    private void btnOk_Click(object sender, EventArgs e)
    {
        if (lbReqLabs.SelectedIndex != -1)
        {
            var lab = lbReqLabs.SelectedValue as RequestingLabInfo;
            if (lab != null)
            {
                RequestingLabId = lab.Id;
                RequestingLabName = lab.Name;
                DialogResult = DialogResult.OK;
            }
        }
        else
        {
            MessageDlg.Warning("Please select the requesting lab.");
        }
    }
}

internal sealed class RequestingLabInfo(short id, string name)
{
    public short Id { get; } = id;
    public string Name { get; } = name;
    public override string ToString() => Name;
}