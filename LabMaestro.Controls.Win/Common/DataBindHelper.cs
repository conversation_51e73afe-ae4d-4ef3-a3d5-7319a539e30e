﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2012, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: DataBindHelper.cs 516 2013-04-17 08:03:02Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using DevExpress.XtraEditors;

namespace LabMaestro.Controls.Win
{
    public static class DataBindHelper
    {
        public static void BindData(this ComboBoxEdit control, object dataSource, string dataMember, string valueMember,
                                    object targetDataSource, string targetDataMember)
        {
            //TODO: bind display and target data
            //control.DataSource = dataSource;
            //control.DisplayMember = dataMember;
            //control.ValueMember = valueMember;
            control.DataBindings.Add("SelectedValue", targetDataSource, targetDataMember);
        }

        //public static void BindData(this TextEdit control, object dataSource, string dataMember)
        //{
        //    control.DataBindings.Add("Text", dataSource, dataMember, true, DataSourceUpdateMode.OnPropertyChanged);
        //}

        //public static void BindData(this CheckBox control, object dataSource, string dataMember)
        //{
        //    control.DataBindings.Add("Checked", dataSource, dataMember, true, DataSourceUpdateMode.OnPropertyChanged);
        //}

        //public static void BindData(this GridControl control, object dataSource)
        //{
        //    BindData(control, dataSource, new[] { "EntityAspect", "Self" });
        //}

        //public static void BindData(this GridControl control, object dataSource, string[] columnsToExclude)
        //{
        //    control.DataSource = dataSource;
        //    return;

        //    foreach (var field in columnsToExclude)
        //    {
        //        var column = (control.MainView as GridView).Columns.ColumnByFieldName(field);
        //        if (column != null) column.Visible = false;
        //    }
        //}
    }
}