﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id:$
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using LabMaestro.BusinessLogic;
using LabMaestro.Shared;

namespace LabMaestro.Controls.Win;

public partial class InputDialog : XtraForm
{
    public InputDialog()
    {
        InitializeComponent();
        WinUtils.ApplyTheme(this);
    }

    public string Message { get; set; }
    public string InputString { get; set; }
    public bool AllowNullInput { get; set; }

    public void UpdateControls()
    {
        lblMessage.Text = Message;
        cboInput.Text = InputString;
        var reasons = SharedUtilities.DelimitedStringToList(GlobalSettingsHelper.System.PrintConfirmationReasons);
        foreach (var s in reasons)
        {
            cboInput.Properties.Items.Add(s);
        }
    }

    private void InputDialog_Load(object sender, EventArgs e) => cboInput.Focus();

    private void btnOk_Click(object sender, EventArgs e)
    {
        var str = cboInput.Text.Trim();

        if (!AllowNullInput)
        {
            if (string.IsNullOrEmpty(str))
            {
                //MessageDlg.Error("Please enter some text!");
                cboInput.Focus();
                return;
            }
        }

        InputString = str;
        DialogResult = DialogResult.OK;
    }

    public static string ExecuteDialog(Form parent, string message, string defaultInput = null,
        bool allowNullInput = false)
    {
        using var frm = new InputDialog();
        frm.Message = message;
        frm.InputString = defaultInput;
        frm.AllowNullInput = allowNullInput;
        frm.UpdateControls();
        frm.cboInput.Focus();
        frm.cboInput.Select();
        return frm.ShowDialog(parent) == DialogResult.OK ? frm.InputString : string.Empty;
    }

    protected void ResetCombobox()
    {
        cboInput.Properties.Items.Clear();
        cboInput.Properties.AutoComplete = false;
        cboInput.Properties.ContextButtons.Clear();
    }

    public static string ExecuteDialogOnlyString(Form parent, string message, string defaultInput = null,
        bool allowNullInput = false)
    {
        using var frm = new InputDialog();
        frm.ResetCombobox();
        frm.Message = message;
        frm.InputString = defaultInput;
        frm.AllowNullInput = allowNullInput;
        frm.UpdateControls();
        frm.cboInput.Focus();
        frm.cboInput.Select();
        return frm.ShowDialog(parent) == DialogResult.OK ? frm.InputString : string.Empty;
    }

    private void cboInput_KeyUp(object sender, KeyEventArgs e)
    {
        if (e.KeyCode == Keys.Enter)
        {
            e.Handled = true;
            btnOk_Click(sender, e);
        }
    }
}