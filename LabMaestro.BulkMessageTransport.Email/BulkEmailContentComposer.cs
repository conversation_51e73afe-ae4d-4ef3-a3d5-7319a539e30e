﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: BulkEmailContentComposer.cs 1331 2014-05-28 12:41:03Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.IO;
using LabMaestro.BulkMessageTransport.Core;
using LabMaestro.ServiceDAL;
using LabMaestro.ServiceDAL.Model;
using LabMaestro.Services.ReportExport;
using LabMaestro.Shared;
using MailBee.Mime;
using NLog;
using SmartFormat;

namespace LabMaestro.BulkMessageTransport.Email;

public sealed class BulkEmailContentComposer : IBulkMessageContentComposer
{
    private readonly Logger _logger = LogManager.GetCurrentClassLogger();

    public IBulkMessage ComposeMessage(InvoiceContactDetailsFullDto invoice, ResultBundleDetailsDto bundle, Guid jobId)
    {
        if (bundle == null || invoice == null) return null;
        if (invoice.IsCancelled || !bundle.IsActive) return null;
        if ((WorkflowStageType)bundle.WorkflowStage != WorkflowStageType.ReportCollation) return null;

        _logger.Info(fmtLogMessage(jobId, "Composing email"));

        var message = new BulkEmailMessage(jobId);
        message.UpdateFrom(invoice);
        message.Mailer.Message.XMailer = $"LabMaestro mailer-daemon {AppVersion.GetVersionString()}";
        message.Mailer.Message.From.Email = "<EMAIL>";
        message.Mailer.Message.From.DisplayName = "Chevron Clinical Lab";
        message.Mailer.Message.Subject = $"Your {bundle.LabName} e-report from Chevron Clinical Lab";
        message.Mailer.Message.To.Add(invoice.EmailAddress);

        renderEmailBody(message, invoice, bundle);
        using (var ms = new MemoryStream())
        {
            try
            {
                ReportExporterFactory.Initialize(true, false);
                var svcExport = new PdfReportExportService(invoice.InvoiceId, bundle.Id)
                {
                    AllowedWorkflowStage = WorkflowStageType.ReportCollation
                };
                svcExport.InitializeService();
                svcExport.ExportToStream(ms);
            }
            catch (Exception exc)
            {
                _logger.ErrorException(fmtLogMessage(jobId, "Error exporting PDF report"), exc);
                return null;
            }

            if (ms.Length <= 0)
            {
                _logger.Warn(fmtLogMessage(jobId, "Exported PDF length is zero"));
                return null;
            }

            ms.Seek(0, SeekOrigin.Begin);
            message.Mailer.Message.Attachments.Add(ms,
                generateFilename(invoice, bundle),
                null, null, null,
                NewAttachmentOptions.None,
                MailTransferEncoding.Base64);
        }

        _logger.Info(fmtLogMessage(jobId, string.Format("Email composed. Size: {0}",
            SharedUtilities.BytesToString(message.Mailer.Message.Size))));
        return message;
    }

    private bool renderEmailBody(BulkEmailMessage message,
        InvoiceContactDetailsFullDto invoice,
        ResultBundleDetailsDto bundle)
    {
        string template;
        using (var dal = DataAccessFactory.GetClient())
        {
            template = dal.GlobalSettingsGetString(GlobalSettingKeys.EHealth.EmailReportTemplate);
        }

        if (string.IsNullOrEmpty(template)) return false;

        message.Mailer.Message.BodyPlainText = Smart.Format(template, invoice, bundle);
        return true;
    }

    private string fmtLogMessage(Guid jobId, string message) => $"[{jobId}] {message}";

    private string generateFilename(InvoiceContactDetailsFullDto invoice, ResultBundleDetailsDto bundle)
    {
        //var labName = bundle.LabName.Replace(" ", "-").Trim().ToUpper();
        //labName = Path.GetInvalidFileNameChars().Aggregate(labName, (x, c) => x.Replace(c, '-'));

        return string.Format("{0}_{1}_{2}_{3:yyMMddHHmmss}.pdf",
            invoice.OrderId, //labName,
            invoice.InvoiceId,
            bundle.Id, DateTime.Now);
    }
}