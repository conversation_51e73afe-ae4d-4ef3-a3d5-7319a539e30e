﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: BulkEmailMessage.cs 1349 2014-06-01 17:03:47Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using LabMaestro.BulkMessageTransport.Core;
using LabMaestro.ServiceDAL.Model;
using MailBee;
using MailBee.AddressCheck;
using MailBee.SmtpMail;

namespace LabMaestro.BulkMessageTransport.Email;

public sealed class BulkEmailMessage : IBulkMessage
{
    public BulkEmailMessage(Guid jobId)
    {
        JobId = jobId;
        Global.LicenseKey = @"MN800-2CD32C2AD370D3D5D3775BBAC7DE-4023";
        RetryCount = 0;
        DeliveryStatus = BulkMessageDeliveryStatus.New;
        Mailer = new Smtp();
    }

    public Smtp Mailer { get; private set; }
    public Guid JobId { get; }
    public string MessageContent { get; set; }
    public string DestinationAddress { get; set; }
    public BulkMessageDeliveryStatus DeliveryStatus { get; set; }
    public int RetryCount { get; set; }
    public DateTime DateQueued { get; set; }
    public DateTime LastUpdated { get; set; }

    public bool HasValidDestination()
    {
        if (string.IsNullOrEmpty(DestinationAddress)) return false;
        using var validator = new EmailAddressValidator();
        validator.ValidationLevel = AddressValidationLevel.RegexCheck;
        //validator.DnsServers.Autodetect();
        var result = validator.Verify(DestinationAddress);

        return result == AddressValidationLevel.OK;
    }

    public void UpdateFrom(InvoiceContactDetailsFullDto invoice)
    {
        DestinationAddress = invoice.EmailAddress;
    }
}