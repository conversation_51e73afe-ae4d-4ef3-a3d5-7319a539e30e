﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <configSections>
        <section name="nlog" type="NLog.Config.ConfigSectionHandler, NLog"/>
    </configSections>
    <nlog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.nlog-project.org/schemas/NLog.xsd">
        <targets>
            <target name="file" xsi:type="File"
                    layout="${date:format=HH\:mm\:ss} ${machinename} ${threadid} ${level:uppercase=true} ${logger} ${message} ${exception:format=message,type,method,stacktrace}"
                    fileName="${basedir}/logs/mservicebus.log"
                    archiveFileName="${basedir}/logs/archive_${shortdate}.log" archiveEvery="Day" maxArchiveFiles="30"
                    concurrentWrites="false" keepFileOpen="false" encoding="iso-8859-2"/>
        </targets>
        <rules>
            <logger name="*" minlevel="Debug" writeTo="file"/>
        </rules>
    </nlog>
    <startup>
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.5"/>
    </startup>
    <runtime>
        <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
            <dependentAssembly>
                <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a"
                                  culture="neutral"/>
                <bindingRedirect oldVersion="0.0.0.0-4.0.6.0" newVersion="4.0.6.0"/>
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.Threading.Tasks.Extensions" publicKeyToken="cc7b13ffcd2ddd51"
                                  culture="neutral"/>
                <bindingRedirect oldVersion="0.0.0.0-4.2.0.1" newVersion="4.2.0.1"/>
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.ValueTuple" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral"/>
                <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0"/>
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral"/>
                <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0"/>
            </dependentAssembly>
        </assemblyBinding>
    </runtime>
</configuration>