﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: BulkEmailTransportAgent.cs 1349 2014-06-01 17:03:47Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using LabMaestro.BulkMessageTransport.Core;
using LabMaestro.ServiceDAL.Model;
using NLog;

namespace LabMaestro.BulkMessageTransport.Email;

public sealed class BulkEmailTransportAgent : IBulkMessageTransportAgent
{
    private const string DEFAULT_PICKUP_FOLDER = @"C:\LabMaestro.Mailer\pickup";
    private readonly Logger _logger = LogManager.GetCurrentClassLogger();

    public BulkEmailTransportAgent(IBulkMessageContentComposer messageComposer)
    {
        MessageComposer = messageComposer;
        PickupFolder = DEFAULT_PICKUP_FOLDER;
        JobId = Guid.NewGuid();
    }

    public string PickupFolder { get; set; }
    public Guid JobId { get; }
    public IBulkMessageContentComposer MessageComposer { get; }

    public bool DeliverMessage(IBulkMessage message)
    {
        var emailMessage = message as BulkEmailMessage;
        if (emailMessage == null)
        {
            _logger.Error(fmtLogMessage("Null or invalid email message provided"));
            return false;
        }

        if (!emailMessage.HasValidDestination())
        {
            _logger.Error(fmtLogMessage("Bulk message has no valid destination"));
            return false;
        }

        emailMessage.DeliveryStatus = BulkMessageDeliveryStatus.Sending;
        try
        {
            _logger.Info(fmtLogMessage("Submitting email to pickup folder..."));
            emailMessage.Mailer.SubmitToPickupFolder(PickupFolder, false);
            emailMessage.DeliveryStatus = BulkMessageDeliveryStatus.Sent;
            _logger.Info(fmtLogMessage("Email submitted"));
        }
        catch (Exception e)
        {
            _logger.ErrorException(fmtLogMessage("Error submitting email"), e);
            emailMessage.DeliveryStatus = BulkMessageDeliveryStatus.Failed;
        }

        return true;
    }

    public bool DeliverMessage(InvoiceContactDetailsFullDto invoice, ResultBundleDetailsDto bundle)
    {
        var message = MessageComposer.ComposeMessage(invoice, bundle, JobId);
        return DeliverMessage(message);
    }

    private string fmtLogMessage(string message)
    {
        return string.Format("[{0}] {1}", JobId, message);
    }
}