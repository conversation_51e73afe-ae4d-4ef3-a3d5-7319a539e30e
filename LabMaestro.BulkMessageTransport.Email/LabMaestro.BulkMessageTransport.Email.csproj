<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{6A94F78F-8314-4E00-965F-D42A0CE02F65}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>LabMaestro.BulkMessageTransport.Email</RootNamespace>
    <AssemblyName>LabMaestro.BulkMessageTransport.Email</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SolutionDir Condition="$(SolutionDir) == '' Or $(SolutionDir) == '*Undefined*'">..\</SolutionDir>
    <TargetFrameworkProfile />
    <LangVersion>latestmajor</LangVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <SignAssembly>false</SignAssembly>
  </PropertyGroup>
  <PropertyGroup>
    <AssemblyOriginatorKeyFile>labmaestro.snk</AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.IO.Compression" />
    <Reference Include="System.Numerics" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Transactions" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="BulkEmailMessage.cs" />
    <Compile Include="BulkEmailContentComposer.cs" />
    <Compile Include="BulkEmailTransportAgent.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\LabMaestro.BulkMessageTransport.Core\LabMaestro.BulkMessageTransport.Core.csproj">
      <Project>{a0450d82-4dbe-4fcd-b87b-12dacd96c3ad}</Project>
      <Name>LabMaestro.BulkMessageTransport.Core</Name>
    </ProjectReference>
    <ProjectReference Include="..\LabMaestro.ServiceDAL.Model\LabMaestro.ServiceDAL.Model.csproj">
      <Project>{d6c2bf28-5823-4b3a-be17-84ecc60a7d3c}</Project>
      <Name>LabMaestro.ServiceDAL.Model</Name>
    </ProjectReference>
    <ProjectReference Include="..\LabMaestro.ServiceDAL\LabMaestro.ServiceDAL.csproj">
      <Project>{ad31717e-94c6-4963-ad95-398d8ab5308d}</Project>
      <Name>LabMaestro.ServiceDAL</Name>
    </ProjectReference>
    <ProjectReference Include="..\LabMaestro.Services.ReportExport\LabMaestro.Services.ReportExport.csproj">
      <Project>{B60B239D-ED1D-418E-9272-191E7A3F7A67}</Project>
      <Name>LabMaestro.Services.ReportExport</Name>
    </ProjectReference>
    <ProjectReference Include="..\LabMaestro.Shared\LabMaestro.Shared.csproj">
      <Project>{b15584a4-1ea1-48aa-a823-29307bb8d16a}</Project>
      <Name>LabMaestro.Shared</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
    <None Include="labmaestro.snk" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="MailBee.NET">
      <Version>12.5.0</Version>
    </PackageReference>
    <PackageReference Include="NLog">
      <Version>6.0.1</Version>
    </PackageReference>
    <PackageReference Include="SmartFormat.NET">
      <Version>3.6.0</Version>
    </PackageReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
         Other similar extension points exist, see Microsoft.Common.targets.
    <Target Name="BeforeBuild">
    </Target>
    <Target Name="AfterBuild">
    </Target>
    -->
</Project>