﻿using System;
using System.Configuration;
using Hangfire;
using LabMaestro.BackgroundJobs;

namespace BackgroundJobProcessor.Cli
{
    internal class Program
    {
        private static void Main(string[] args)
        {
            var cs = ConfigurationManager.AppSettings[@"jobDb"];
            JobClient.Initialize(ConfigurationManager.AppSettings[@"jobDb"]);

            GlobalConfiguration.Configuration
                .UseSqlServerStorage(cs)
                .UseColouredConsoleLogProvider()
                .UseNLogLogProvider();

            using (var server = new BackgroundJobServer())
            {
                Console.WriteLine("Hangfire Server started. Press any key to exit...");
                Console.ReadKey();
            }
        }
    }
}