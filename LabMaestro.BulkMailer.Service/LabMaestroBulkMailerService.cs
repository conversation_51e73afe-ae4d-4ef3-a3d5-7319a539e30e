﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: LabMaestroBulkMailerService.cs 1308 2014-05-24 15:35:04Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Threading;
using LabMaestro.BulkMailer.Core;
using MailBee.SmtpMail;
using Topshelf;

namespace LabMaestro.BulkMailer.Service
{
    internal sealed class LabMaestroBulkMailerService : IDisposable, ServiceControl
    {
        private readonly Smtp _mailer = null;
        private readonly QueueRunner _queueRunner;
        private int _pollingInterval = 5000;
        private bool _stopped;
        private bool disposed;
        public Timer timer = null;

        public LabMaestroBulkMailerService()
        {
            _queueRunner = new QueueRunner(true);
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        public bool Start(HostControl hostControl)
        {
            return Start();
        }

        public bool Stop(HostControl hostControl)
        {
            return Stop();
        }

        public bool Start()
        {
            _queueRunner.SetRootFolderIfNotSet();
            _queueRunner.AssertPickupFolder();
            _queueRunner.AssertBadFolder();
            _queueRunner.AssertLogsFolder();
            _stopped = false;
            timer = new Timer(timer1_Elapsed);
            timer.Change(1, Timeout.Infinite);
            return true;
        }

        public bool Stop()
        {
            _stopped = true;
            timer.Change(Timeout.Infinite, Timeout.Infinite);
            timer.Dispose();
            timer = null;
            if (_queueRunner.Mailer.IsBusy)
            {
                _queueRunner.Mailer.Abort();
            }
            return true;
        }

        private void Dispose(bool disposing)
        {
            if (!disposed)
            {
                if (disposing)
                {
                    // Dispose managed resources.
                    if (timer != null)
                    {
                        timer.Dispose();
                    }
                }

                // Dispose unmanaged managed resources.

                disposed = true;
            }
        }

        public bool Pause()
        {
            _stopped = true;
            timer.Change(Timeout.Infinite, Timeout.Infinite);
            _mailer.StopJobs();
            return true;
        }

        public bool Continue()
        {
            _stopped = false;
            timer.Change(1, Timeout.Infinite);
            return true;
        }

        private void timer1_Elapsed(object state)
        {
            timer.Change(Timeout.Infinite, Timeout.Infinite);

            if (_stopped)
            {
                return;
            }

            _queueRunner.Run();

            if (_stopped)
            {
                return;
            }

            timer.Change(_pollingInterval, Timeout.Infinite);
        }
    }
}