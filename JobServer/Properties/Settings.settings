﻿<?xml version="1.0" encoding="UTF-8"?>
<SettingsFile xmlns="http://schemas.microsoft.com/VisualStudio/2004/01/settings" CurrentProfile="(Default)" GeneratedClassNamespace="JobServer.Properties" GeneratedClassName="Settings">
  <Profiles />
  <Settings>
    <Setting Name="bulkEmailPickupFolder" Type="System.String" Scope="Application">
      <Value Profile="(Default)">C:\LabMaestro.MailQueue\pickup</Value>
    </Setting>
    <Setting Name="bulkSmsPickupFolder" Type="System.String" Scope="Application">
      <Value Profile="(Default)">C:\LabMaestro.Sms\pickup</Value>
    </Setting>
    <Setting Name="bulkSmsDelay" Type="System.Int32" Scope="Application">
      <Value Profile="(Default)">15</Value>
    </Setting>
    <Setting Name="bulkSmsExpiry" Type="System.Int32" Scope="Application">
      <Value Profile="(Default)">6</Value>
    </Setting>
    <Setting Name="serviceDb" Type="System.String" Scope="Application">
      <Value Profile="(Default)">Data Source=(local);Initial Catalog=LabMaestro;Integrated Security=True;TrustServerCertificate=True;Encrypt=false;</Value>
    </Setting>
    <Setting Name="jobsDb" Type="System.String" Scope="Application">
      <Value Profile="(Default)">Data Source=(local);Initial Catalog=Hangfire;Integrated Security=True;TrustServerCertificate=True;Encrypt=false;</Value>
    </Setting>
    <Setting Name="smsGateway" Type="System.String" Scope="Application">
      <Value Profile="(Default)">adn</Value>
    </Setting>
    <Setting Name="adnApiKey" Type="System.String" Scope="Application">
      <Value Profile="(Default)">KEY-ffmm114yldleb1pfsl5c61wbyovz7zwp</Value>
    </Setting>
    <Setting Name="adnApiSecret" Type="System.String" Scope="Application">
      <Value Profile="(Default)">LUd9t53CjdzM@xEa</Value>
    </Setting>
    <Setting Name="mailbeeLicense" Type="System.String" Scope="Application">
      <Value Profile="(Default)">MN120-A66E91126F226E096EBE6E216E9D-4C76</Value>
    </Setting>
    <Setting Name="smtpHost" Type="System.String" Scope="Application">
      <Value Profile="(Default)">smtp.office365.com</Value>
    </Setting>
    <Setting Name="smtpPort" Type="System.Int32" Scope="Application">
      <Value Profile="(Default)">587</Value>
    </Setting>
    <Setting Name="smtpLogin" Type="System.String" Scope="Application">
      <Value Profile="(Default)"><EMAIL></Value>
    </Setting>
    <Setting Name="smtpPassword" Type="System.String" Scope="Application">
      <Value Profile="(Default)">MI$#ChevroN</Value>
    </Setting>
    <Setting Name="smtpSender" Type="System.String" Scope="Application">
      <Value Profile="(Default)"><EMAIL></Value>
    </Setting>
    <Setting Name="smtpTls" Type="System.Boolean" Scope="Application">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="adtechApiKey" Type="System.String" Scope="Application">
      <Value Profile="(Default)">4TL5ytQk0iLp7EGn1YKI</Value>
    </Setting>
    <Setting Name="adtechSenderId" Type="System.String" Scope="Application">
      <Value Profile="(Default)">8809617617359</Value>
    </Setting>
    <Setting Name="adnApiEndpoint" Type="System.String" Scope="Application">
      <Value Profile="(Default)">https://portal.adnsms.com/api/v1/secure/send-sms</Value>
    </Setting>
    <Setting Name="adtechApiEndpoint" Type="System.String" Scope="Application">
      <Value Profile="(Default)">http://sms.adtechsoft.com/api/smsapi</Value>
    </Setting>
    <Setting Name="stimulsoftLicense" Type="System.String" Scope="Application">
      <Value Profile="(Default)">6vJhGtLLLz2GNviWmUTrhSqnOItdDwjBylQzQcAOiHkgpgFGkUl79uxVs8X+uspx6K+tqdtOB5G1S6PFPRrlVNvMUiSiNYl724EZbrUAWwAYHlGLRbvxMviMExTh2l9xZJ2xc4K1z3ZVudRpQpuDdFq+fe0wKXSKlB6okl0hUd2ikQHfyzsAN8fJltqvGRa5LI8BFkA/f7tffwK6jzW5xYYhHxQpU3hy4fmKo/BSg6yKAoUq3yMZTG6tWeKnWcI6ftCDxEHd30EjMISNn1LCdLN0/4YmedTjM7x+0dMiI2Qif/yI+y8gmdbostOE8S2ZjrpKsgxVv2AAZPdzHEkzYSzx81RHDzZBhKRZc5mwWAmXsWBFRQol9PdSQ8BZYLqvJ4Jzrcrext+t1ZD7HE1RZPLPAqErO9eo+7Zn9Cvu5O73+b9dxhE2sRyAv9Tl1lV2WqMezWRsO55Q3LntawkPq0HvBkd9f8uVuq9zk7VKegetCDLb0wszBAs1mjWzN+ACVHiPVKIk94/QlCkj31dWCg8YTrT5btsKcLibxog7pv1+2e4yocZKWsposmcJbgG0</Value>
    </Setting>
  </Settings>
</SettingsFile>

