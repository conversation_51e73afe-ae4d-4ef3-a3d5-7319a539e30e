﻿using System.Net;
using System.Web.Mvc;
using Hangfire;
using LabMaestro.BackgroundJobs;
using Serilog;

namespace JobServer.Controllers;

[RoutePrefix("Bundle")]
public class BundleController : Controller
{
    [HttpGet]
    [Route("Compile/{invoice:long}")]
    public ActionResult Compile(long invoice)
    {
        Log.Information("Bundle.Compile {Invoice}", invoice);
        BackgroundJob.Enqueue(() => ResultBundleCompilationProcessor.Perform(invoice));
        return new HttpStatusCodeResult(HttpStatusCode.NoContent);
    }

    [HttpGet]
    [Route("WorkflowHook/{invoice:long}/{bundle:long}")]
    public ActionResult WorkflowHook(long invoice, long bundle)
    {
        Log.Information("Bundle.WorkflowHook {Invoice}:{Bundle}", invoice, bundle);
        BackgroundJob.Enqueue(() => ResultBundleWorkflowHookProcessor.Perform(invoice, bundle));
        return new HttpStatusCodeResult(HttpStatusCode.NoContent);
    }
}