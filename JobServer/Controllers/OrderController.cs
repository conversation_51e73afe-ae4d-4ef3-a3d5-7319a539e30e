﻿using System.Net;
using System.Web.Mvc;
using Hangfire;
using LabMaestro.BackgroundJobs;
using Serilog;

namespace JobServer.Controllers;

[RoutePrefix("Order")]
public class OrderController : Controller
{
    [HttpGet]
    [Route("Estimate/{invoice:long}")]
    public ActionResult Estimate(long invoice)
    {
        Log.Information("Order.Estimate {Invoice}", invoice);
        BackgroundJob.Enqueue(() => LabOrderWorkflowStageEstimateProcessor.Perform(invoice));
        return new HttpStatusCodeResult(HttpStatusCode.NoContent);
    }

    [HttpGet]
    [Route("WorkflowHook/{invoice:long}")]
    public ActionResult WorkflowHook(long invoice)
    {
        Log.Information("Order.WorkflowHook {Invoice}", invoice);
        BackgroundJob.Enqueue(() => LabOrderWorkflowHookProcessor.Perform(invoice));
        return new HttpStatusCodeResult(HttpStatusCode.NoContent);
    }
}