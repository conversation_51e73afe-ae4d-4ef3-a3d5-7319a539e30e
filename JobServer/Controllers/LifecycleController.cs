﻿using System;
using System.Net;
using System.Web.Mvc;
using Hangfire;
using LabMaestro.BackgroundJobs.Lifecycle;
using LabMaestro.Shared;
using Serilog;

namespace JobServer.Controllers;

[RoutePrefix("Lifecycle")]
public class LifecycleController : Controller
{
    [HttpPost]
    [Route("Process")]
    public ActionResult Process(OrderLifecycleEvent data)
    {
        Log.Information("LifeCycle.Process: {@Data}", data);

        if (data.DeferSeconds > 0)
            BackgroundJob.Schedule(
                () => EventHandlerFactory.Handle(data), TimeSpan.FromSeconds(data.DeferSeconds));
        else
            BackgroundJob.Enqueue(() => EventHandlerFactory.Handle(data));

        return new HttpStatusCodeResult(HttpStatusCode.NoContent);
    }
}