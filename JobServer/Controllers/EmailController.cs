﻿using System;
using System.Net;
using System.Web.Mvc;
using Hangfire;
using JobServer.Properties;
using LabMaestro.Messaging.Core;
using LabMaestro.Messaging.Transport.Email;

namespace JobServer.Controllers;

[RoutePrefix("Email")]
public class EmailController : Controller
{
    [HttpPost]
    public ActionResult SendPlain(GenericEmail msg)
    {
        if (msg.SendAfterMinutes > 0)
            BackgroundJob.Schedule(
                () => InitializeMailer().Send(msg), TimeSpan.FromMinutes(msg.SendAfterMinutes));
        else
            BackgroundJob.Enqueue(() => InitializeMailer().Send(msg));

        return new HttpStatusCodeResult(HttpStatusCode.NoContent);
    }

    [HttpPost]
    public ActionResult SendReport(PdfReportEmail msg)
    {
        //todo: implement
        //BackgroundJob.Enqueue(() => InitializeMailer().SendPlain(msg));

        return new HttpStatusCodeResult(HttpStatusCode.NoContent);
    }

    [HttpPost]
    public ActionResult SendArchive(PdfArchiveEmail msg)
    {
        //todo: implement
        //BackgroundJob.Enqueue(() => InitializeMailer().SendPlain(msg));

        return new HttpStatusCodeResult(HttpStatusCode.NoContent);
    }

    [HttpPost]
    public ActionResult SendCrmInvoice(CrmInvoiceEmail msg)
    {
        //todo: implement
        //BackgroundJob.Enqueue(() => InitializeMailer().SendPlain(msg));

        return new HttpStatusCodeResult(HttpStatusCode.NoContent);
    }

    private SimpleEmailSender InitializeMailer()
    {
        return new SimpleEmailSender
        {
            MailbeeLicense = Settings.Default.mailbeeLicense,
            SmtpHost = Settings.Default.smtpHost,
            SmtpPort = Settings.Default.smtpPort,
            Username = Settings.Default.smtpLogin,
            Password = Settings.Default.smtpPassword,
            StartTls = Settings.Default.smtpTls
        };
    }
}