﻿using System;
using System.Web.Mvc;
using JobServer.Properties;
using LabMaestro.BackgroundJobs;

namespace JobServer.Controllers;

[RoutePrefix("Status")]
public class StatusController : Controller
{
    [HttpGet]
    [Route("Ping")]
    public ActionResult Ping()
    {
        return Json(new
                    {
                        version = BgJobsLogger.VERSION,
                        status = "running",
                        time = DateTime.Now.ToString("U"),
                        sms_gateway = Settings.Default.smsGateway.ToUpperInvariant(),
                    },
                    JsonRequestBehavior.AllowGet);
    }
}