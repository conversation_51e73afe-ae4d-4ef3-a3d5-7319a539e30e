﻿using System;
using System.Net;
using System.Web.Mvc;
using Hangfire;
using LabMaestro.BackgroundJobs;
using LabMaestro.Shared.Domain;
using Serilog;

namespace JobServer.Controllers;

[RoutePrefix("Sms")]
public class SmsController : Controller
{
    [HttpPost]
    [Route("Send")]
    public ActionResult Send(SmsMessageRequest request)
    {
        Log.Information("Sms.Send {@Request}", request);

        if (request.SendAfterSeconds > 0)
            BackgroundJob.Schedule(() =>
                    SmsProcessor.Process(request), TimeSpan.FromSeconds(request.SendAfterSeconds)
            );
        else
            BackgroundJob.Enqueue(() => SmsProcessor.Process(request));

        return new HttpStatusCodeResult(HttpStatusCode.NoContent);
    }
}