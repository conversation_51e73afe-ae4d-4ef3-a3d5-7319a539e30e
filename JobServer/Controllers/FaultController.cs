﻿using System.Net;
using System.Web.Mvc;
using LabMaestro.ServiceDAL;
using LabMaestro.Shared.AppFaults;

namespace JobServer.Controllers;

[RoutePrefix("Fault")]
public class FaultController : Controller
{
    [HttpPost]
    [Route("Submit")]
    public ActionResult Submit(AppFaultData data)
    {
        try
        {
            using var db = DataAccessFactory.GetClient();
            db.InsertAppFaultData(data);
        }
        catch
        {
            // yawn
        }

        return new HttpStatusCodeResult(HttpStatusCode.NoContent);
    }
}