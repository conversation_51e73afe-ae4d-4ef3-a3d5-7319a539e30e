﻿<?xml version="1.0" encoding="utf-8"?>
<!--
  For more information on how to configure your ASP.NET application, please visit
  https://go.microsoft.com/fwlink/?LinkId=301880
  -->
<configuration>
    <configSections>
        <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
            <section name="JobServer.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
        </sectionGroup>
        <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
            <section name="JobServer.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
        </sectionGroup>
    </configSections>
    <appSettings>
        <add key="webpages:Version" value="*******" />
        <add key="webpages:Enabled" value="false" />
        <add key="ClientValidationEnabled" value="true" />
        <add key="UnobtrusiveJavaScriptEnabled" value="true" />
        <add key="owin:AutomaticAppStartup" value="false" />
        <add key="RouteDebugger:Enabled" value="false" />
    </appSettings>
    <system.web>
        <compilation debug="true" targetFramework="4.8" />
        <httpRuntime targetFramework="4.8" />
    </system.web>
    <runtime>
        <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
            <dependentAssembly>
                <assemblyIdentity name="Antlr3.Runtime" publicKeyToken="eb42632606e9261f" />
                <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="Microsoft.Web.Infrastructure" publicKeyToken="31bf3856ad364e35" />
                <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" />
                <bindingRedirect oldVersion="0.0.0.0-1*******" newVersion="1*******" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.Web.Optimization" publicKeyToken="31bf3856ad364e35" />
                <bindingRedirect oldVersion="1.0.0.0-1.1.0.0" newVersion="1.1.0.0" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35" />
                <bindingRedirect oldVersion="0.0.0.0-1.6.5135.21930" newVersion="1.6.5135.21930" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="Hangfire.Core" publicKeyToken="null" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-1.8.20.0" newVersion="1.8.20.0" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="mscorlib" publicKeyToken="b77a5c561934e089" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System" publicKeyToken="b77a5c561934e089" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.Collections.Immutable" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.IO.Compression" publicKeyToken="b77a5c561934e089" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-4.2.0.0" newVersion="4.2.0.0" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-4.0.5.0" newVersion="4.0.5.0" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.Reflection.Metadata" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-6.0.3.0" newVersion="6.0.3.0" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35" />
                <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35" />
                <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35" />
                <bindingRedirect oldVersion="0.0.0.0-5.3.0.0" newVersion="5.3.0.0" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="Microsoft.Owin" publicKeyToken="31bf3856ad364e35" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-4.2.2.0" newVersion="4.2.2.0" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="Microsoft.Extensions.DependencyInjection.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-8.0.0.1" newVersion="8.0.0.1" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="Microsoft.Extensions.Logging.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-8.0.0.1" newVersion="8.0.0.1" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="Microsoft.Extensions.Options" publicKeyToken="adb9793829ddae60" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-8.0.0.2" newVersion="8.0.0.2" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="Serilog" publicKeyToken="24c2f752a8e58a10" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-4.3.0.0" newVersion="4.3.0.0" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-4.0.5.0" newVersion="4.0.5.0" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.Diagnostics.DiagnosticSource" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.Text.Json" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="SerilogWeb.Classic" publicKeyToken="9462ddd55fbc0e7f" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-5.1.0.0" newVersion="5.1.0.0" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.Threading.Tasks.Extensions" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-4.2.4.0" newVersion="4.2.4.0" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.Threading.Channels" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="Microsoft.Bcl.AsyncInterfaces" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.Numerics.Vectors" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.IO.Pipelines" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.Text.Encodings.Web" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
            </dependentAssembly>
        </assemblyBinding>
    </runtime>
    <system.codedom>
        <compilers>
            <compiler language="c#;cs;csharp" extension=".cs" warningLevel="4" compilerOptions="/langversion:default /nowarn:1659;1699;1701;612;618" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.CSharpCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
            <compiler language="vb;vbs;visualbasic;vbscript" extension=".vb" warningLevel="4" compilerOptions="/langversion:default /nowarn:41008,40000,40008 /define:_MYTYPE=\&quot;Web\&quot; /optionInfer+" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.VBCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
        </compilers>
    </system.codedom>
    <applicationSettings>
        <JobServer.Properties.Settings>
            <setting name="bulkEmailPickupFolder" serializeAs="String">
                <value>C:\LabMaestro.MailQueue\pickup</value>
            </setting>
            <setting name="bulkSmsPickupFolder" serializeAs="String">
                <value>C:\LabMaestro.Sms\pickup</value>
            </setting>
            <setting name="bulkSmsDelay" serializeAs="String">
                <value>15</value>
            </setting>
            <setting name="bulkSmsExpiry" serializeAs="String">
                <value>6</value>
            </setting>
            <setting name="serviceDb" serializeAs="String">
                <value>Data Source=(local);Initial Catalog=LabMaestro;Integrated Security=True;TrustServerCertificate=True;Encrypt=false;</value>
            </setting>
            <setting name="jobsDb" serializeAs="String">
                <value>Data Source=(local);Initial Catalog=Hangfire;Integrated Security=True;TrustServerCertificate=True;Encrypt=false;</value>
            </setting>
            <setting name="smsGateway" serializeAs="String">
                <value>adn</value>
            </setting>
            <setting name="adnApiKey" serializeAs="String">
                <value>KEY-ffmm114yldleb1pfsl5c61wbyovz7zwp</value>
            </setting>
            <setting name="adnApiSecret" serializeAs="String">
                <value>LUd9t53CjdzM@xEa</value>
            </setting>
            <setting name="mailbeeLicense" serializeAs="String">
                <value>MN120-A66E91126F226E096EBE6E216E9D-4C76</value>
            </setting>
            <setting name="smtpHost" serializeAs="String">
                <value>smtp.office365.com</value>
            </setting>
            <setting name="smtpPort" serializeAs="String">
                <value>587</value>
            </setting>
            <setting name="smtpLogin" serializeAs="String">
                <value><EMAIL></value>
            </setting>
            <setting name="smtpPassword" serializeAs="String">
                <value>MI$#ChevroN</value>
            </setting>
            <setting name="smtpSender" serializeAs="String">
                <value><EMAIL></value>
            </setting>
            <setting name="smtpTls" serializeAs="String">
                <value>True</value>
            </setting>
            <setting name="adtechApiKey" serializeAs="String">
                <value>4TL5ytQk0iLp7EGn1YKI</value>
            </setting>
            <setting name="adtechSenderId" serializeAs="String">
                <value>8809617617359</value>
            </setting>
            <setting name="adnApiEndpoint" serializeAs="String">
                <value>https://portal.adnsms.com/api/v1/secure/send-sms</value>
            </setting>
            <setting name="adtechApiEndpoint" serializeAs="String">
                <value>http://sms.adtechsoft.com/api/smsapi</value>
            </setting>
            <setting name="stimulsoftLicense" serializeAs="String">
                <value>6vJhGtLLLz2GNviWmUTrhSqnOItdDwjBylQzQcAOiHkgpgFGkUl79uxVs8X+uspx6K+tqdtOB5G1S6PFPRrlVNvMUiSiNYl724EZbrUAWwAYHlGLRbvxMviMExTh2l9xZJ2xc4K1z3ZVudRpQpuDdFq+fe0wKXSKlB6okl0hUd2ikQHfyzsAN8fJltqvGRa5LI8BFkA/f7tffwK6jzW5xYYhHxQpU3hy4fmKo/BSg6yKAoUq3yMZTG6tWeKnWcI6ftCDxEHd30EjMISNn1LCdLN0/4YmedTjM7x+0dMiI2Qif/yI+y8gmdbostOE8S2ZjrpKsgxVv2AAZPdzHEkzYSzx81RHDzZBhKRZc5mwWAmXsWBFRQol9PdSQ8BZYLqvJ4Jzrcrext+t1ZD7HE1RZPLPAqErO9eo+7Zn9Cvu5O73+b9dxhE2sRyAv9Tl1lV2WqMezWRsO55Q3LntawkPq0HvBkd9f8uVuq9zk7VKegetCDLb0wszBAs1mjWzN+ACVHiPVKIk94/QlCkj31dWCg8YTrT5btsKcLibxog7pv1+2e4yocZKWsposmcJbgG0</value>
            </setting>
        </JobServer.Properties.Settings>
    </applicationSettings>
    <userSettings>
        <JobServer.Properties.Settings>
            <setting name="stimulsoftLicense" serializeAs="String">
                <value />
            </setting>
        </JobServer.Properties.Settings>
    </userSettings>
</configuration>