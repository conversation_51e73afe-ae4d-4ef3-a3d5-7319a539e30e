﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Web;
using System.Web.Hosting;
using System.Web.Mvc;
using System.Web.Routing;
using Hangfire;
using Hangfire.SqlServer;
using JobServer.Properties;
using LabMaestro.BackgroundJobs;
using Serilog;

namespace JobServer;

public class MvcApplication : HttpApplication
{
    private IEnumerable<IDisposable> GetHangfireServers()
    {
        var options = new SqlServerStorageOptions
        {
            SlidingInvisibilityTimeout = TimeSpan.FromMinutes(5),
            QueuePollInterval = TimeSpan.Zero
        };

        GlobalSettingsHelper.BulkEmailPickupFolder = Settings.Default.bulkEmailPickupFolder;
        GlobalSettingsHelper.BulkSmsPickupFolder = Settings.Default.bulkSmsPickupFolder;
        GlobalSettingsHelper.BulkSmsDelayMinutes = Settings.Default.bulkSmsDelay;
        GlobalSettingsHelper.BulkSmsExpiryHours = Settings.Default.bulkSmsExpiry;

        GlobalConfiguration.Configuration
                           .SetDataCompatibilityLevel(CompatibilityLevel.Version_180)
                           .UseSimpleAssemblyNameTypeSerializer()
                           .UseRecommendedSerializerSettings()
                           .UseSqlServerStorage(Settings.Default.jobsDb, options)
                           .UseNLogLogProvider();

        yield return new BackgroundJobServer();
    }

    private void ConfigureLogging()
    {
        var logPath = HostingEnvironment.MapPath("~/logs");
        //path = HttpContext.Current.Server.MapPath("/logs");
        Log.Logger = new LoggerConfiguration()
                     .MinimumLevel.Debug()
                     /*
                     .Enrich.WithMvcActionName()
                     .Enrich.WithMvcControllerName()
                     .Enrich.WithMvcRouteData()
                     .Enrich.WithMvcRouteTemplate()
                     */
                     .WriteTo.File(Path.Combine(logPath, "jobserver.log"),
                                   outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff} [{Level:u4}] {Message:lj}{NewLine}{Exception}",
                                   rollingInterval: RollingInterval.Day,
                                   buffered: true,
                                   flushToDiskInterval: TimeSpan.FromMinutes(5),
                                   retainedFileCountLimit: 30
                     )
                     .CreateLogger();
        BgJobsLogger.SetLogger(Log.Logger);
        Log.Information("Application_Start");
    }

    protected void Application_Start()
    {
        ConfigureLogging();
        AppStartup.Initialize();
        //AreaRegistration.RegisterAllAreas();
        FilterConfig.RegisterGlobalFilters(GlobalFilters.Filters);
        RouteConfig.RegisterRoutes(RouteTable.Routes);
        //BundleConfig.RegisterBundles(BundleTable.Bundles);
        HangfireAspNet.Use(GetHangfireServers);
    }
}