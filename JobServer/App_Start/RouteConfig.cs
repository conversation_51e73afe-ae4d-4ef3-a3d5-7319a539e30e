﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Web.Routing;

namespace JobServer
{
    public class RouteConfig
    {
        public static void RegisterRoutes(RouteCollection routes)
        {
            routes.IgnoreRoute("{resource}.axd/{*pathInfo}");

            routes.MapMvcAttributeRoutes();

            /*
            routes.MapRoute(
                name: "Default",
                url: "{controller}/{action}/{invoice}",
                //defaults: new { controller = "Job" },
                new { invoice = @"\d+" }
            );

            routes.MapRoute(
                name: "Default_Bundle",
                url: "{controller}/{action}/{invoice}/{bundle}",
                //defaults: new { controller = "Job" },
                new { invoice = @"\d+", bundle = @"\d+" }
            );

            routes.MapRoute(
                name: "Fault",
                url: "{controller}/{action}",
                defaults: new { controller = "Fault" }
            );
            */
            /*
            routes.MapRoute(
                name: "Default",
                url: "{controller}/{action}/{invoice}",
                defaults: new { controller = "Home", action = "Index", invoice = UrlParameter.Optional }
            );
            */
        }
    }
}