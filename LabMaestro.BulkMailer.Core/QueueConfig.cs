// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: QueueConfig.cs 1304 2014-05-24 14:57:43Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.IO;
using MailBee.DnsMX;
using MailBee.SmtpMail;

namespace LabMaestro.BulkMailer.Core
{
    [Serializable]
    public class QueueConfig
    {
        private readonly DnsServerCollection _dnsServers;
        private readonly SmtpServerCollection _smtpServers;
        private string _rootFolder;

        public QueueConfig()
        {
            _smtpServers = new SmtpServerCollection();
            _dnsServers = new DnsServerCollection();
            _rootFolder = string.Empty;
        }

        public SmtpServerCollection SmtpServers
        {
            get { return _smtpServers; }
        }

        public DnsServerCollection DnsServers
        {
            get { return _dnsServers; }
        }

        public string RootFolder
        {
            get { return _rootFolder; }
            set { _rootFolder = value; }
        }

        public string GetPickupFolderPath()
        {
            return GetPickupFolderPath(_rootFolder);
        }

        public static string GetPickupFolderPath(string rootFolder)
        {
            return Path.Combine(rootFolder, "pickup");
        }

        public string GetBadFolderPath()
        {
            return GetBadFolderPath(_rootFolder);
        }

        public static string GetBadFolderPath(string rootFolder)
        {
            return Path.Combine(rootFolder, "failed");
        }

        public string GetDefaultLogsPath()
        {
            return GetDefaultLogsPath(_rootFolder);
        }

        public static string GetDefaultLogsPath(string rootFolder)
        {
            return Path.Combine(rootFolder, "logs");
        }
    }
}