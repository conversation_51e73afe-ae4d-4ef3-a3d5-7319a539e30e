﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: QueueRunner.cs 1311 2014-05-24 15:51:09Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System.Diagnostics;
using System.IO;
using System.Reflection;
using MailBee;
using MailBee.DnsMX;
using MailBee.SmtpMail;
using NLog;
using Logger = NLog.Logger;

namespace LabMaestro.BulkMailer.Core
{
    public class QueueRunner
    {
        private readonly BulkMailerConfig _config;
        private readonly Logger _logger = LogManager.GetCurrentClassLogger();
        // These vars let us avoid logging similar errors multiple times.
        private bool _deleteExceptionAlreadyLogged;
        private EventLog _eventLog;
        private Smtp _mailer;
        private bool _moveExceptionAlreadyLogged;
        private bool _notSentExceptionAlreadyLogged;

        public QueueRunner(bool initMailer, string configPath)
        {
            if (configPath == null)
            {
                configPath = Path.Combine(Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location), "app.config");
            }
            _config = BulkMailerConfig.Load(configPath);
            if (initMailer)
            {
                InitMailer();
            }
        }

        public QueueRunner(bool initMailer) : this(initMailer, null)
        {
        }

        // You can use this property to call Mailer.Abort or Mailer.StopJobs to quit,
        // monitor Mailer.Jobs* properties to get stat, subscribe to events, etc.
        public Smtp Mailer
        {
            get { return _mailer; }
        }

        public BulkMailerConfig ServiceConfig
        {
            get { return _config; }
        }

        private void OnMessageSent(object sender, SmtpMessageSentEventArgs e)
        {
            try
            {
                File.Delete(e.MailMessage.Filename);
            }
            catch (IOException ex)
            {
                _eventLog.WriteEntry(ex.Message, EventLogEntryType.Error);
                if (!_deleteExceptionAlreadyLogged && _eventLog != null)
                {
                    _eventLog.WriteEntry(ex.Message, EventLogEntryType.Error);
                    _deleteExceptionAlreadyLogged = true;
                }
            }
        }

        private void OnMessageNotSent(object sender, SmtpMessageNotSentEventArgs e)
        {
            if (!_notSentExceptionAlreadyLogged && _eventLog != null)
            {
                _eventLog.WriteEntry(e.Reason.Message, EventLogEntryType.Error);
                _notSentExceptionAlreadyLogged = true;
            }

            var retryLater = true;
            if (e.Reason is IMailBeeSendException)
            {
                var reason = e.Reason as MailBeeSmtpSendNegativeResponseException;
                if (reason != null)
                {
                    var negRespException = reason;
                    retryLater = negRespException.IsTransientError;
                }
                else
                {
                    retryLater = false;
                }
            }
            else if (e.Reason is MailBeeInvalidArgumentException)
            {
                retryLater = false;
            }

            if (!retryLater)
            {
                try
                {
                    var newFilename = _config.Queue.GetBadFolderPath() + @"\" +
                                      Path.GetFileName(e.MailMessage.Filename);
                    if (File.Exists(newFilename))
                    {
                        File.Delete(newFilename);
                    }
                    File.Move(e.MailMessage.Filename, newFilename);
                }
                catch (IOException ex)
                {
                    if (!_moveExceptionAlreadyLogged && _eventLog != null)
                    {
                        _eventLog.WriteEntry(ex.Message, EventLogEntryType.Error);
                        _moveExceptionAlreadyLogged = true;
                    }
                }
            }
        }

        private void OnSendingMessage(object sender, SmtpSendingMessageEventArgs e)
        {
            if (!_config.KeepXSenderXReceiver)
            {
                e.MailMessage.Headers.Remove("x-sender");
                e.MailMessage.Headers.Remove("x-receiver");
            }
        }

        private void InitMailer()
        {
            Global.LicenseKey = _config.LicenseKey;
            _mailer = new Smtp();
            _mailer.Log.Enabled = _config.Logging.Enabled;
            _mailer.Log.Filename = LoggingConfig.GetActualLogFilePath(_config.Logging.Filename, _config.Queue.RootFolder);
            _mailer.Log.Clear();
            _mailer.Log.Format = LogFormatOptions.AddContextInfo | LogFormatOptions.AddDate;
            _mailer.Log.MaxSize = 4*1024*1024;
            _mailer.MaxThreadCount = _config.MaxThreadCount;

            // You can init DSN settings here.
            // _mailer.DeliveryNotification.NotifyCondition = DsnNotifyCondition.Default;
            // _mailer.DeliveryNotification.TrackingID = string.Empty;

            _mailer.MessageSent += OnMessageSent;
            _mailer.MessageNotSent += OnMessageNotSent;
            _mailer.SendingMessage += OnSendingMessage;

            foreach (SmtpServer smtpServer in _config.Queue.SmtpServers)
            {
                _mailer.SmtpServers.Add(smtpServer);
            }

            foreach (DnsServer dnsServer in _config.Queue.DnsServers)
            {
                _mailer.DnsServers.Add(dnsServer);
            }
        }

        private void RunBatch()
        {
            _mailer.SendJobs();
            _mailer.JobsFailed.Clear();
            _mailer.JobsSuccessful.Clear();
            _mailer.Log.Filename = LoggingConfig.GetActualLogFilePath(_config.Logging.Filename, _config.Queue.RootFolder);
        }

        public void Run()
        {
            foreach (var f in FastDirectoryEnumerator.EnumerateFiles(_config.Queue.GetPickupFolderPath(), "*.EML"))
            {
                _mailer.AddJob(f.Name, f.Path, true, null, null);
                if (_mailer.JobsPending.Count > 4096)
                {
                    RunBatch();
                }
            }

            if (_mailer.JobsPending.Count > 0)
            {
                RunBatch();
            }
        }

        public void RetryBads()
        {
            var files = Directory.GetFiles(_config.Queue.GetBadFolderPath());
            foreach (var file in files)
            {
                File.Move(file,
                    Path.Combine(_config.Queue.GetPickupFolderPath(), Path.GetFileName(file)));
            }
        }

        public int GetPickupCount()
        {
            return Directory.GetFiles(_config.Queue.GetPickupFolderPath()).Length;
        }

        public int GetBadCount()
        {
            return Directory.GetFiles(_config.Queue.GetBadFolderPath()).Length;
        }

        private void AssertFolder(string folderPath)
        {
            if (!Directory.Exists(folderPath))
            {
                throw new DirectoryNotFoundException(string.Format("\"{0}\" folder does not exist", folderPath));
            }
        }

        public void AssertPickupFolder()
        {
            AssertFolder(_config.Queue.GetPickupFolderPath());
        }

        public void AssertBadFolder()
        {
            AssertFolder(_config.Queue.GetBadFolderPath());
        }

        public void AssertLogsFolder()
        {
            if (_config.Logging.Enabled)
            {
                AssertFolder(Path.GetDirectoryName(LoggingConfig.GetActualLogFilePath(
                    _config.Logging.Filename,
                    _config.Queue.RootFolder)));
            }
        }

        public bool RootDirectoryExists()
        {
            return Directory.Exists(_config.Queue.RootFolder);
        }

        public string GetDefaultRootFolder()
        {
            return Path.Combine(Path.GetPathRoot(Assembly.GetExecutingAssembly().Location), "MailBeeNetQueue Files");
        }

        public void SetRootFolderIfNotSet()
        {
            if (string.IsNullOrEmpty(_config.Queue.RootFolder))
            {
                _config.Queue.RootFolder = GetDefaultRootFolder();
            }
        }

        public void CreateDirectoryStructure()
        {
            Directory.CreateDirectory(_config.Queue.RootFolder);
            Directory.CreateDirectory(_config.Queue.GetPickupFolderPath());
            Directory.CreateDirectory(_config.Queue.GetBadFolderPath());
            Directory.CreateDirectory(_config.Queue.GetDefaultLogsPath());
        }
    }
}