// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2015, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id$
// 
// Last modified: 2015.08.21 3:29 PM
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.IO;
using System.Xml.Serialization;

namespace LabMaestro.BulkMailer.Core
{
    [Serializable, XmlRoot(ElementName = "Configuration")]
    public class BulkMailerConfig
    {
        private bool _keepXSenderXReceiver;
        private string _licenseKey;
        private LoggingConfig _logging;
        private int _maxThreadCount;
        private QueueConfig _queue;

        public BulkMailerConfig()
        {
            _licenseKey = @"MN800-2CD32C2AD370D3D5D3775BBAC7DE-4023";
            _queue = new QueueConfig();
            _logging = new LoggingConfig();
            _maxThreadCount = -1;
            _keepXSenderXReceiver = false;
        }

        public QueueConfig Queue
        {
            get { return _queue; }
            set { _queue = value; }
        }

        public LoggingConfig Logging
        {
            get { return _logging; }
            set { _logging = value; }
        }

        public string LicenseKey
        {
            get { return _licenseKey; }
            set { _licenseKey = value; }
        }

        public int MaxThreadCount
        {
            get { return _maxThreadCount; }
            set { _maxThreadCount = value; }
        }

        public bool KeepXSenderXReceiver
        {
            get { return _keepXSenderXReceiver; }
            set { _keepXSenderXReceiver = value; }
        }

        public static BulkMailerConfig Load(string filename)
        {
            var ser = new XmlSerializer(typeof (BulkMailerConfig));

            if (File.Exists(filename))
            {
                using (var fs = new FileStream(filename, FileMode.Open))
                {
                    var config = (BulkMailerConfig) ser.Deserialize(fs);
                    return config;
                }
            }
            return new BulkMailerConfig();
        }

        public void Save(string filename)
        {
            var ser = new XmlSerializer(typeof (BulkMailerConfig));
            using (TextWriter tw = new StreamWriter(filename, false))
            {
                ser.Serialize(tw, this);
            }
        }
    }
}