// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: LoggingConfig.cs 1293 2014-05-23 18:16:56Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.IO;

namespace LabMaestro.BulkMailer.Core
{
    [Serializable]
    public class LoggingConfig
    {
        private bool _enabled;
        private string _filename;

        public LoggingConfig()
        {
            _enabled = false;
            _filename = "{yyyy-MM-dd}.txt";
        }

        public bool Enabled
        {
            get { return _enabled; }
            set { _enabled = value; }
        }

        public string Filename
        {
            get { return _filename; }
            set { _filename = value; }
        }


        public static string GetActualLogFilePath(string filename, string rootFolder)
        {
            if (!Path.IsPathRooted(filename))
            {
                string logsFolder = Path.Combine(rootFolder, "Logs");
                filename = Path.Combine(logsFolder, filename);
            }

            int delimPos1 = filename.IndexOf('{');
            if ((delimPos1 < 0) || (delimPos1 == filename.Length - 1))
            {
                return filename;
            }
            int delimPos2 = filename.IndexOf('}', delimPos1 + 1);
            if (delimPos2 < 0)
            {
                return filename;
            }
            return filename.Substring(0, delimPos1) +
                   DateTime.Now.ToString(filename.Substring(delimPos1 + 1, delimPos2 - delimPos1 - 1)) +
                   filename.Substring(delimPos2 + 1);
        }
    }
}