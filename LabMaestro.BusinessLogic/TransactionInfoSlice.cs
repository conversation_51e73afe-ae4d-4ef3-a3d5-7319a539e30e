﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: TransactionInfoSlice.cs 830 2013-07-18 08:15:23Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using AutoMapper;
using LabMaestro.Domain;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class TransactionInfoSlice
{
    public short PerformingUserId { get; set; }
    public string StaffName { get; set; }
    public string AuthorizerName { get; set; }
    public long WorkShiftId { get; set; }
    public InvoiceTransactionType TxType { get; set; }
    public TransactionFlag TxFlag { get; set; }
    public decimal TxAmount { get; set; }
    public decimal NonCashAmount { get; set; }
    public DateTime TxTime { get; set; }
    public string UserRemarks { get; set; }
    public WorkflowStageType WorkflowStage { get; set; }
    public PaymentMethod PaymentMethod { get; set; } = PaymentMethod.Cash;
    public string PaymentSource { get; set; }
    public string PaymentReference { get; set; }

    public static TransactionInfoSlice AssembleFrom(DetailedInvoiceTransactionInfo info)
    {
        Mapper.CreateMap<DetailedInvoiceTransactionInfo, TransactionInfoSlice>()
            .ForMember(dest => dest.TxType, opt => opt.MapFrom(src => (InvoiceTransactionType)src.TxType))
            .ForMember(dest => dest.TxFlag, opt => opt.MapFrom(src => (TransactionFlag)src.TxFlag));

        return Mapper.Map<DetailedInvoiceTransactionInfo, TransactionInfoSlice>(info);
    }

    public static TransactionInfoSlice AssembleFrom(InvoiceTransactionDetailedExSlice info)
    {
        Mapper.CreateMap<InvoiceTransactionDetailedExSlice, TransactionInfoSlice>()
            .ForMember(dest => dest.PaymentMethod, opt => opt.MapFrom(src => (PaymentMethod)src.PaymentMethod))
            .ForMember(dest => dest.TxType, opt => opt.MapFrom(src => (InvoiceTransactionType)src.TxType))
            .ForMember(dest => dest.TxFlag, opt => opt.MapFrom(src => (TransactionFlag)src.TxFlag));

        return Mapper.Map<InvoiceTransactionDetailedExSlice, TransactionInfoSlice>(info);
    }
}