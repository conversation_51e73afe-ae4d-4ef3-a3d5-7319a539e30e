﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: LabOrderWorkflowStageEstimator.cs 1239 2014-03-29 09:23:25Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class LabOrderWorkflowStageEstimator
{
    private readonly long _invoiceId;

    public LabOrderWorkflowStageEstimator(long invoiceId)
    {
        _invoiceId = invoiceId;
    }

    public static void PerformEstimation(long invoiceId)
    {
        var estimator = new LabOrderWorkflowStageEstimator(invoiceId);
        estimator.EstimateWorkflowStage();
    }

    public WorkflowStageType EstimateWorkflowStage()
    {
        var order = FaultHandler.Shield(() => PatientLabOrdersRepository.FindById(_invoiceId));

        if (order.IsCancelled)
        {
            FaultHandler.Shield(() =>
            {
                PatientLabOrdersRepository.UpdateLabOrderWorkflowStage(_invoiceId, WorkflowStageType.Canceled);
                var remark = string.Format(@"Invoice workflow stage set to '{0}' by system",
                    EnumUtils.EnumDescription(WorkflowStageType.Canceled));
                AuditTrailRepository.LogSystemEvent(AuditEventCategory.System,
                    AuditEventType.wfAutoEstimated,
                    WorkflowStageType.Canceled, _invoiceId, null, null, remark);
            });
            return WorkflowStageType.Canceled;
        }

        var bundles = ResultBundlesRepository.GetActiveResultBundlesForInvoice(_invoiceId);
        var wfStageBundles = (byte)WorkflowStageType.OrderFulfillment;

        //PatientLabOrdersRepository.AutoUpdateLabOrderWorkflowStageFromOrderedTests(_invoiceId, WorkflowStageType.OrderFulfillment);

        foreach (var bundle in bundles)
            /*
            switch ((TestResultType) bundle.TestResultType)
            {
                case TestResultType.Unarchived:
                    // TODO: explore this special case.. do we even need to
                    // filter out this type of bundle? what if the order
                    // consists only of unarchived result bundles?
                    break;
                default:
                    if (wfStageBundles > bundle.WorkflowStage)
                        wfStageBundles = bundle.WorkflowStage;
                    break;
            }
            */
            if (wfStageBundles > bundle.WorkflowStage)
                wfStageBundles = bundle.WorkflowStage;

        var orderWorkflow = (WorkflowStageType)wfStageBundles;
        var note = string.Format(@"Invoice workflow stage set to '{0}' by system",
            EnumUtils.EnumDescription(orderWorkflow));

        /* mark the order as "fulfilled" if all the following criterias have been met:
         * 1) all reports have been dispatched / remotely dispatched
         * 2) invoice is paid in full
         */
        if (orderWorkflow >= WorkflowStageType.ReportDispatch && orderWorkflow != WorkflowStageType.Canceled)
        {
            var invoice = FaultHandler.Shield(() => InvoiceMasterRepository.FindInvoiceById(_invoiceId));
            if (invoice != null
                && invoice.PaymentStatus == (byte)PaymentStatusType.PaidInFull
                && invoice.DueAmount == 0m)
            {
                orderWorkflow = WorkflowStageType.OrderFulfillment;
                note = string.Format(@"Invoice workflow stage boosted to '{0}' by system",
                    EnumUtils.EnumDescription(orderWorkflow));
            }
        }

        if (wfStageBundles != order.WorkflowStage)
            // TODO: what if the workflow is demoted?
            FaultHandler.Shield(() =>
            {
                PatientLabOrdersRepository.UpdateLabOrderWorkflowStage(_invoiceId, orderWorkflow);

                AuditTrailRepository.LogSystemEvent(AuditEventCategory.System,
                    AuditEventType.wfAutoEstimated,
                    orderWorkflow, _invoiceId, null, null, note);
            });

        return orderWorkflow;
    }
}