﻿using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using LabMaestro.Shared;
using NameParser;
using Newtonsoft.Json;

namespace LabMaestro.BusinessLogic;

public sealed class PatientDetailsParser(string inputString)
{
    public string Title { get; private set; }
    public string FirstName { get; private set; }
    public string LastName { get; private set; }
    public string TrackingId { get; private set; }
    public string Age { get; private set; }
    public bool HasTitle => !string.IsNullOrEmpty(Title);
    public bool HasFirstName => !string.IsNullOrEmpty(FirstName);

    private readonly Dictionary<string, string> _specialCases = new()
    {
        { "M.D.", "Md." },
    };

    private readonly List<char> _trimChars = [];
    private readonly Regex _rexId = new(@"\bid\s*-\s*(\S+)\b", RegexOptions.IgnoreCase);
    private readonly Regex _rexAge = new(@"\b(\d+[y|m|d])\b", RegexOptions.IgnoreCase);

    private readonly Regex _rexAgeComposite =
        new(@"\b(\d{1,3}[y|m|d]\-?\d{0,3}[m|d]?\-?\d{0,3}d?)\b", RegexOptions.IgnoreCase);

    private readonly Regex _rexAgeSplitter = new(@"(\d+[y|m|d])", RegexOptions.IgnoreCase);

    private char[] getTrimCharacters()
    {
        if (_trimChars.Count == 0)
        {
            for (var c = char.MinValue; c < char.MaxValue; c++)
                if (char.IsPunctuation(c) || char.IsWhiteSpace(c))
                    _trimChars.Add(c);
        }

        return _trimChars.ToArray();
    }

    private string trimNonWordChars(string s) => s.Trim(getTrimCharacters());

    private void reset() => FirstName = LastName = Title = TrackingId = null;

    private string extractRegexMatch(string[] parts, out string value)
    {
        var list = new List<string>(parts);
        value = list[1];
        list.RemoveAt(1);
        return trimNonWordChars(string.Join(" ", list));
    }

    void processFirstNameSpecialCases()
    {
        foreach (var k in _specialCases.Keys.Where(k => FirstName.Contains(k)))
            FirstName = FirstName.Replace(k, _specialCases[k]);
    }

    private string formatAge(string value)
    {
        var age = trimNonWordChars(value).Replace("-", " ").Trim().ToUpperInvariant();
        var parts = _rexAgeSplitter.Split(age);
        if (parts.Length > 0)
        {
            parts = parts.Where(x => !string.IsNullOrEmpty(x.Trim())).Select(x => x).ToArray();
            age = string.Join(", ", parts);
        }

        return age;
    }

    string innerTrimNameParts(string s) => Regex.Replace(s, @"[^a-z]", " ", RegexOptions.IgnoreCase);

    public bool Parse()
    {
        reset();

        var trimmed = trimNonWordChars(inputString);
        if (string.IsNullOrEmpty(trimmed))
            return false;

        // extract external tracking ID
        if (_rexId.IsMatch(trimmed))
        {
            var parts = _rexId.Split(trimmed).Where(s => !string.IsNullOrEmpty(s)).ToArray();
            trimmed = extractRegexMatch(parts, out var value);
            TrackingId = trimNonWordChars(value).ToUpperInvariant().Replace(" ", string.Empty);
        }

        // extract patient age
        /*
        if (_rexAge.IsMatch(trimmed)) {
            var parts = _rexAge.Split(trimmed).Where(s => !string.IsNullOrEmpty(s)).ToArray();
            trimmed = extractRegexMatch(parts, out var value);
            Age = trimNonWordChars(value).ToUpperInvariant();
        }
        else
        */
        if (_rexAgeComposite.IsMatch(trimmed))
        {
            var parts = _rexAgeComposite.Split(trimmed).Where(s => !string.IsNullOrEmpty(s)).ToArray();
            trimmed = extractRegexMatch(parts, out var value);
            Age = formatAge(value);
        }

        trimmed = innerTrimNameParts(trimmed);

        var hn = new HumanName(trimmed);
        hn.Normalize();

        Title = hn.Title;
        FirstName = string.Join(" ", new List<string>([hn.First, hn.Middle]).Where(s => !string.IsNullOrEmpty(s)));
        LastName = hn.Last;

        if (!HasFirstName)
        {
            // patient has only one name. swap the last name with the first.
            FirstName = LastName;
            LastName = null;
        }

        processFirstNameSpecialCases();

        return HasFirstName;
    }

    public override string ToString() => JsonConvert.SerializeObject(this);

    public SexType DeduceSexFromTitle()
    {
        if (!HasTitle) return SexType.Unknown;
        var title = trimNonWordChars(Title).ToLowerInvariant();
        return title switch
        {
            "mrs" or "ms" => SexType.Female,
            "mr" or "master" => SexType.Male,
            _ => SexType.Unknown
        };
    }
}