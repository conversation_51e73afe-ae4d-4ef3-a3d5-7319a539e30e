﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: LabTestUtilization.cs 949 2013-09-22 05:17:07Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using CuttingEdge.Conditions;
using LabMaestro.Domain;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class LabTestUtilization
{
    public LabTestUtilization()
    {
        VolumeTotal = 0;
        VolumeActive = 0;
        VolumeCanceled = 0;
        ValueTotal = 0;
        ValueActive = 0;
        ValueRefund = 0;
    }

    public short Id { get; set; }
    public LabUtilization Lab { get; set; }
    public string Name { get; set; }

    public decimal UnitPrice { get; set; }
    public decimal CostBasis { get; set; }
    public decimal SubOrderPrice { get; set; }

    public int VolumeTotal { get; set; }
    public int VolumeActive { get; set; }
    public int VolumeCanceled { get; set; }

    public decimal ValueTotal { get; set; }
    public decimal ValueActive { get; set; }
    public decimal ValueRefund { get; set; }

    public static LabTestUtilization AssembleFrom(OrderedTestInDateRangeSlice dto, LabUtilization lab)
    {
        return new LabTestUtilization
        {
            Id = dto.LabTestId,
            Lab = lab,
            UnitPrice = dto.UnitPrice,
            Name = dto.CanonicalName,
            CostBasis = dto.CostBasis,
            SubOrderPrice = dto.SubOrderPrice
        };
    }

    public void AddTestUtilization(OrderedTestInDateRangeSlice dto)
    {
        Condition.Requires(dto).IsNotNull();
        Condition.Requires(dto.LabTestId).IsEqualTo(Id);

        VolumeTotal++;
        ValueTotal += dto.UnitPrice;

        if (dto.IsCancelled)
        {
            VolumeCanceled++;
            ValueRefund += dto.UnitPrice;
        }
        else
        {
            VolumeActive++;
            ValueActive += dto.UnitPrice;
        }
    }

    public override string ToString()
    {
        return string.Format("{0}: {1} = {2} + {3}", Name, VolumeTotal, VolumeActive, VolumeCanceled);
    }

    public LabTestUtilizationPrintDto ToPrintDto()
    {
        return new LabTestUtilizationPrintDto
        {
            Id = SharedUtilities.IntToStringPositive(Id),
            Name = Name,
            ValueActive = SharedUtilities.MoneyToStringCulture(ValueActive),
            ValueRefund = SharedUtilities.MoneyToStringCulture(ValueRefund),
            ValueTotal = SharedUtilities.MoneyToStringCulture(ValueTotal),
            VolumeActive = SharedUtilities.IntToStringPositive(VolumeActive),
            VolumeCanceled = SharedUtilities.IntToStringPositive(VolumeCanceled),
            VolumeTotal = SharedUtilities.IntToStringPositive(VolumeTotal),
            UnitPrice = SharedUtilities.MoneyToStringCulture(UnitPrice),
            CostBasis = SharedUtilities.MoneyToStringCulture(CostBasis),
            SubOrderPrice = SharedUtilities.MoneyToStringCulture(SubOrderPrice)
        };
    }
}