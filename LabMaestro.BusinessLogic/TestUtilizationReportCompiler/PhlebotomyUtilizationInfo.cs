// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id$
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using LabMaestro.Domain;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class PhlebotomyUtilizationInfo
{
    public long InvoiceId { get; set; }
    public string OrderId { get; set; }
    public DateTime OrderDateTime { get; set; }
    public string UserName { get; set; }
    public short UserId { get; set; }
    public int Red { get; set; }
    public int Blue { get; set; }
    public int Grey { get; set; }
    public int Green { get; set; }
    public int Purple { get; set; }
    public int Black { get; set; }

    public static PhlebotomyUtilizationInfo AssembleFrom(PhlebotomySpecimen src)
    {
        return new PhlebotomyUtilizationInfo
        {
            InvoiceId = (long)src.InvoiceId,
            UserId = (short)src.CreatedBy
        }.UpdateInfo();
    }

    public PhlebotomyUtilizationInfo UpdateInfo()
    {
        var slice = PatientLabOrdersRepository.FetchLabOrderDetails(InvoiceId);
        OrderId = slice.OrderId;
        OrderDateTime = slice.OrderDateTime;
        return this;
    }

    public void UpdateVacutainerUsage(string vacType, int quantity)
    {
        switch (vacType.ToUpper().Trim())
        {
            case "RED":
                Red += quantity;
                break;
            case "BLUE":
                Blue += quantity;
                break;
            case "GREY":
                Grey += quantity;
                break;
            case "GREEN":
                Green += quantity;
                break;
            case "BLACK":
                Black += quantity;
                break;
            case "PURPLE":
                Purple += quantity;
                break;
        }
    }

    public PhlebotomyUtilizationInfoPrintDto ToPrintDto()
    {
        return new PhlebotomyUtilizationInfoPrintDto
        {
            OrderId = OrderId,
            InvoiceId = InvoiceId.ToString(),
            OrderDateTime = SharedUtilities.DateToStringOnlyDot(OrderDateTime),
            UserName = UserName,
            Black = Black,
            Blue = Blue,
            Green = Green,
            Grey = Grey,
            Purple = Purple,
            Red = Red
        };
    }
}