﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: LabUtilization.cs 949 2013-09-22 05:17:07Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System.Collections.Generic;
using System.Linq;
using C1.LiveLinq;
using C1.LiveLinq.Collections;
using CuttingEdge.Conditions;
using LabMaestro.Domain;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class LabUtilization
{
    private readonly IndexedCollection<LabTestUtilization> _testUtilizations;

    public LabUtilization()
    {
        _testUtilizations = new IndexedCollection<LabTestUtilization>();

        VolumeTotal = 0;
        VolumeActive = 0;
        VolumeCanceled = 0;

        ValueTotal = 0m;
        ValueActive = 0m;
        ValueRefund = 0m;
    }

    public short Id { get; set; }
    public string Name { get; set; }

    public int VolumeTotal { get; set; }
    public int VolumeActive { get; set; }
    public int VolumeCanceled { get; set; }

    public decimal ValueTotal { get; set; }
    public decimal ValueActive { get; set; }
    public decimal ValueRefund { get; set; }

    public int Count
    {
        get { return _testUtilizations.Count; }
    }

    public List<LabTestUtilization> LabTestUtilizationsByVolume
    {
        get { return _testUtilizations.AsIndexed().OrderByDescending(x => x.VolumeTotal).ThenBy(x => x.Name).ToList(); }
    }

    public List<LabTestUtilization> LabTestUtilizationsByValue
    {
        get { return _testUtilizations.AsIndexed().OrderByDescending(x => x.ValueTotal).ThenBy(x => x.Name).ToList(); }
    }

    public List<LabTestUtilization> LabTestUtilizationsByName
    {
        get { return _testUtilizations.AsIndexed().OrderBy(x => x.Name).ToList(); }
    }

    public List<LabTestUtilization> LabTestUtilizations
    {
        get
        {
            switch (SortType)
            {
                case UtilizationSortType.Name:
                    return LabTestUtilizationsByName;
                case UtilizationSortType.Value:
                    return LabTestUtilizationsByValue;
                default:
                    return LabTestUtilizationsByVolume;
            }
        }
    }

    public bool IsAuxProcedure { get; set; }
    public UtilizationSortType SortType { get; set; }

    public static LabUtilization AssembleFrom(LabSlice dto)
    {
        return new LabUtilization { Id = dto.Id, Name = dto.Name, IsAuxProcedure = dto.IsAuxProcedure };
    }

    public void AddTestUtilization(OrderedTestInDateRangeSlice dto)
    {
        Condition.Requires(dto).IsNotNull();
        var ltu = findOrAddLabTestUtilization(dto);
        ltu.AddTestUtilization(dto);
        updateLabUtilization(dto);
    }

    private LabTestUtilization findOrAddLabTestUtilization(OrderedTestInDateRangeSlice dto)
    {
        var ltu = _testUtilizations.AsIndexed().SingleOrDefault(x => x.Id == dto.LabTestId);
        if (ltu == null)
        {
            _testUtilizations.BeginUpdate();
            try
            {
                ltu = LabTestUtilization.AssembleFrom(dto, this);
                _testUtilizations.Add(ltu);
            }
            finally
            {
                _testUtilizations.EndUpdate();
            }
        }

        return ltu;
    }

    private void updateLabUtilization(OrderedTestInDateRangeSlice dto)
    {
        VolumeTotal++;
        ValueTotal += dto.UnitPrice;

        if (dto.IsCancelled)
        {
            VolumeCanceled++;
            ValueRefund += dto.UnitPrice;
        }
        else
        {
            VolumeActive++;
            ValueActive += dto.UnitPrice;
        }
    }

    public override string ToString()
    {
        return string.Format("{0}: {1} = {2} + {3}", Name, VolumeTotal, VolumeActive, VolumeCanceled);
    }

    public LabUtilizationPrintDto ToPrintDto()
    {
        var result = new LabUtilizationPrintDto
        {
            Id = SharedUtilities.IntToStringPositive(Id),
            Name = Name,

            ValueActive = SharedUtilities.MoneyToStringCulture(ValueActive),
            ValueRefund = SharedUtilities.MoneyToStringCulture(ValueRefund),
            ValueTotal = SharedUtilities.MoneyToStringCulture(ValueTotal),

            VolumeActive = SharedUtilities.IntToStringPositive(VolumeActive),
            VolumeCanceled = SharedUtilities.IntToStringPositive(VolumeCanceled),
            VolumeTotal = SharedUtilities.IntToStringPositive(VolumeTotal)
        };

        foreach (var testUtilization in LabTestUtilizations)
            result.LabTestUtilizations.Add(testUtilization.ToPrintDto());

        return result;
    }
}