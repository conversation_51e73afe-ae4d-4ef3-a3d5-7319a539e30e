﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id$
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

#define DUMMY_DATA

using System;
using System.Collections.Generic;
using System.Linq;
using C1.LiveLinq.Collections;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class PhlebotomyUtilizationReportCompiler
{
    private readonly IndexedCollection<PhlebotomyUtilizationInfo> _invoiceUtilizations;
    private readonly Dictionary<short, string> _userDict;

    public PhlebotomyUtilizationReportCompiler()
    {
        _userDict = new Dictionary<short, string>();
        _invoiceUtilizations = new IndexedCollection<PhlebotomyUtilizationInfo>();

        UtilizationSummary = new PhlebotomyUtilizationInfo();
    }

    public DateTime DateFrom { get; set; }
    public DateTime DateTo { get; set; }

    public List<PhlebotomyUtilizationInfo> InvoiceUtilizations
    {
        get { return _invoiceUtilizations.OrderBy(u => u.InvoiceId).ToList(); }
    }

    public PhlebotomyUtilizationInfo UtilizationSummary { get; }

    private string getUserDisplayName(short uid)
    {
        var displayName = string.Empty;
        if (!_userDict.TryGetValue(uid, out displayName))
        {
            displayName = UsersRepository.GetUserDisplayName(uid);
            _userDict.Add(uid, displayName);
        }

        return displayName;
    }

    private PhlebotomyUtilizationInfo getInvoiceUtilization(PhlebotomySpecimen specimen)
    {
        var info = _invoiceUtilizations.SingleOrDefault(x => x.InvoiceId == specimen.InvoiceId);
        if (info == null)
        {
            info = PhlebotomyUtilizationInfo.AssembleFrom(specimen);
            if (specimen.CreatedBy != null) info.UserName = getUserDisplayName((short)specimen.CreatedBy);
            _invoiceUtilizations.Add(info);
        }

        return info;
    }

    public void CompileReport()
    {
        DateFrom = DateFrom.Date;
        DateTo = SharedUtilities.LastMinuteOfDay(DateTo);
#if DUMMY_DATA
        generateDummyData();
#else
            var items = PhlebotomySpecimensRepository.FindAllInDateRange(DateFrom, DateTo);
            foreach (var specimen in items)
            {
                var info = getInvoiceUtilization(specimen);
                info.UpdateVacutainerUsage(specimen.VacutainerType, specimen.Quantity);
                UtilizationSummary.UpdateVacutainerUsage(specimen.VacutainerType, specimen.Quantity);
            }
#endif
    }

#if DUMMY_DATA
    private void generateDummyData()
    {
        var rand = new Random();
        for (var i = 0; i < 100; i++)
        {
            var x = new PhlebotomyUtilizationInfo
            {
                Black = rand.Next(10),
                Blue = rand.Next(10),
                Green = rand.Next(10),
                Grey = rand.Next(10),
                Purple = rand.Next(10),
                Red = rand.Next(10),
                InvoiceId = 99999 + rand.Next(1, 500),
                OrderId = "Z" + i,
                OrderDateTime = DateTime.Now.AddDays(-rand.Next(5)),
                UserName = getUserDisplayName((short)rand.Next(100, 110))
            };
            _invoiceUtilizations.Add(x);
        }

        UtilizationSummary.UpdateVacutainerUsage("RED", _invoiceUtilizations.Sum(x => x.Red));
        UtilizationSummary.UpdateVacutainerUsage("BLACK", _invoiceUtilizations.Sum(x => x.Black));
        UtilizationSummary.UpdateVacutainerUsage("BLUE", _invoiceUtilizations.Sum(x => x.Blue));
        UtilizationSummary.UpdateVacutainerUsage("GREY", _invoiceUtilizations.Sum(x => x.Grey));
        UtilizationSummary.UpdateVacutainerUsage("GREEN", _invoiceUtilizations.Sum(x => x.Green));
        UtilizationSummary.UpdateVacutainerUsage("PURPLE", _invoiceUtilizations.Sum(x => x.Purple));
    }
#endif

    public PhlebotomyUtilizationReportPrintDto ToPrintDto()
    {
        var dto = new PhlebotomyUtilizationReportPrintDto
        {
            PrintedBy = CurrentUserContext.UserDisplayName,
            PrintedOn = SharedUtilities.DateTimeToShortString(AppSysRepository.GetServerTime()),
            BeginDate = SharedUtilities.DateToString(DateFrom),
            EndDate = SharedUtilities.DateToString(DateTo),
            UtilizationSummary = UtilizationSummary.ToPrintDto()
        };

        foreach (var info in InvoiceUtilizations) dto.InvoiceUtilizations.Add(info.ToPrintDto());

        return dto;
    }
}