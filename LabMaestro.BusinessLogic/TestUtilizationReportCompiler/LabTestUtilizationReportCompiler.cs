﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: LabTestUtilizationReportCompiler.cs 1347 2014-06-01 05:27:07Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using C1.LiveLinq;
using C1.LiveLinq.Collections;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public enum UtilizationSortType
{
    Volume,
    Name,
    Value
}

public sealed class LabTestUtilizationReportCompiler
{
    private readonly IndexedCollection<LabUtilization> _labsCatalog;
    private readonly List<LabTestUtilization> _topLabTestsByValue;
    private readonly List<LabTestUtilization> _topLabTestsByVolume;

    public LabTestUtilizationReportCompiler()
    {
        _labsCatalog = new IndexedCollection<LabUtilization>();
        _topLabTestsByValue = new List<LabTestUtilization>();
        _topLabTestsByVolume = new List<LabTestUtilization>();
        FilteredLabId = -1;
        ExcludeEmptyLabs = false;
        ResetSummary();
    }

    public DateTime DateStart { get; set; }
    public DateTime DateEnd { get; set; }
    public short FilteredLabId { get; set; }
    public UtilizationSortType SortType { get; set; }
    public bool ExcludeEmptyLabs { get; set; }
    public bool ExcludeAuxLabs { get; set; }

    public int VolumeTotal { get; set; }
    public int VolumeActive { get; set; }
    public int VolumeCanceled { get; set; }

    public decimal ValueTotal { get; set; }
    public decimal ValueActive { get; set; }
    public decimal ValueRefund { get; set; }


    public List<LabUtilization> AllUtilizedLabs
    {
        get { return _labsCatalog.AsIndexed().OrderBy(x => x.Name).ToList(); }
    }

    public List<LabUtilization> AllUtilizedLabsByVolume
    {
        get { return _labsCatalog.AsIndexed().OrderByDescending(x => x.VolumeTotal).ThenBy(x => x.Name).ToList(); }
    }

    public List<LabUtilization> OnlyActiveUtilizedLabs
    {
        get { return _labsCatalog.AsIndexed().Where(x => x.VolumeTotal > 0).OrderBy(x => x.Name).ToList(); }
    }

    public List<LabUtilization> OnlyActiveUtilizedLabsByVolume
    {
        get
        {
            return
                _labsCatalog.AsIndexed()
                    .Where(x => x.VolumeTotal > 0)
                    .OrderByDescending(x => x.VolumeTotal)
                    .ThenBy(x => x.Name)
                    .ToList();
        }
    }

    public IEnumerable<LabTestUtilization> TopLabTestsByVolume
    {
        get { return _topLabTestsByVolume; }
    }

    public IEnumerable<LabTestUtilization> TopLabTestsByValue
    {
        get { return _topLabTestsByValue; }
    }

    private void compileTopLabTests()
    {
        var tests = new List<LabTestUtilization>();
        foreach (var lab in _labsCatalog) tests.AddRange(lab.LabTestUtilizations);

        _topLabTestsByVolume.Clear();
        _topLabTestsByVolume.AddRange(tests.OrderByDescending(t => t.VolumeActive).Take(10));

        _topLabTestsByValue.Clear();
        _topLabTestsByValue.AddRange(tests.OrderByDescending(t => t.ValueActive).Take(10));
    }

    public void ResetSummary()
    {
        VolumeTotal = 0;
        VolumeActive = 0;
        VolumeCanceled = 0;

        ValueTotal = 0m;
        ValueActive = 0m;
        ValueRefund = 0m;
    }

    public void PopulateLabs()
    {
        _labsCatalog.BeginUpdate();
        try
        {
            _labsCatalog.Clear();
            var labs = LabsRepository.GetAllActiveLabSlices();
            foreach (var lab in labs
                         .Select(LabUtilization.AssembleFrom)
                         .Where(lu => !ExcludeAuxLabs || !lu.IsAuxProcedure))
                _labsCatalog.Add(lab);
        }
        finally
        {
            _labsCatalog.EndUpdate();
        }
    }

    public void CompileReport()
    {
        ResetSummary();
        DateStart = DateStart.Date;
        DateEnd = SharedUtilities.LastMinuteOfDay(DateEnd);

        var orderedTests = FaultHandler.Shield(
            () => FilteredLabId >= 0
                ? OrderedTestsRepository.SearchOrderedTestsForLabInDateRange(FilteredLabId, DateStart, DateEnd)
                : OrderedTestsRepository.SearchOrderedTestsInDateRange(DateStart, DateEnd));

        foreach (var slice in orderedTests)
        {
            var lab = _labsCatalog.AsIndexed().SingleOrDefault(lu => lu.Id == slice.LabId);
            if (lab != null)
            {
                lab.SortType = SortType;
                lab.AddTestUtilization(slice);
            }

            updateUtilizationSummary(slice);
        }

        compileTopLabTests();
    }

    private void updateUtilizationSummary(OrderedTestInDateRangeSlice dto)
    {
        VolumeTotal++;
        ValueTotal += dto.UnitPrice;

        if (dto.IsCancelled)
        {
            VolumeCanceled++;
            ValueRefund += dto.UnitPrice;
        }
        else
        {
            VolumeActive++;
            ValueActive += dto.UnitPrice;
        }
    }


    public void UpdateSortOrder()
    {
        foreach (var labUtilization in _labsCatalog) labUtilization.SortType = SortType;
    }

    public TestUtilizationReportPrintDto ToPrintDto()
    {
        var dto = new TestUtilizationReportPrintDto
        {
            StartDate = SharedUtilities.DateToString(DateStart),
            EndDate = SharedUtilities.DateToString(DateEnd),

            ValueActive = SharedUtilities.MoneyToStringCulture(ValueActive),
            ValueRefund = SharedUtilities.MoneyToStringCulture(ValueRefund),
            ValueTotal = SharedUtilities.MoneyToStringCulture(ValueTotal),

            VolumeActive = SharedUtilities.IntToStringPositiveCulture(VolumeActive),
            VolumeCanceled = SharedUtilities.IntToStringPositiveCulture(VolumeCanceled),
            VolumeTotal = SharedUtilities.IntToStringPositiveCulture(VolumeTotal)
        };

        var labs = ExcludeEmptyLabs ? OnlyActiveUtilizedLabs : AllUtilizedLabs;
        dto.LabUtilizations.AddRange(labs.Select(u => u.ToPrintDto()));
        dto.TopLabTestsByValue.AddRange(TopLabTestsByValue.Select(u => u.ToPrintDto()));
        dto.TopLabTestsByVolume.AddRange(TopLabTestsByVolume.Select(u => u.ToPrintDto()));
        return dto;
    }
}