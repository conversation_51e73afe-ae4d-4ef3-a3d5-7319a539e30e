﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: WorkShiftSummaryCompiler.cs 783 2013-07-09 14:40:30Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System.Collections.Generic;
using System.Linq;
using C1.LiveLinq.Collections;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class WorkShiftSummaryCompiler(int shiftId)
{
    private readonly IndexedCollection<InvoiceInShiftSlice> _invoices = [];
    private readonly IndexedCollection<TransactionsInShiftSlice> _transactions = [];
    public IEnumerable<InvoiceInShiftSlice> InvoicesInShift => _invoices.OrderBy(x => x.InvoiceId).ToList();

    public List<ShiftReportDetailPrintDto> InvoicesInShiftPrintDto =>
        _invoices.OrderBy(x => x.InvoiceId).Select(x => x.ToPrintDto()).ToList();

    public List<TransactionsInShiftSlice> TransactionsInShift => _transactions.OrderBy(x => x.InvoiceId).ToList();

    private InvoiceInShiftSlice getInvoiceSlice(long invoiceId)
    {
        var item = InvoicesInShift.SingleOrDefault(x => x.InvoiceId == invoiceId);
        if (item == null)
            FaultHandler.Shield(() =>
            {
                var summary = InvoiceMasterRepository.GetInvoiceSummary(invoiceId);
                item = InvoiceInShiftSlice.AssembleFrom(summary);
                _invoices.Add(item);
            });
        return item;
    }

    public void CompileSummary()
    {
        _invoices.Clear();

        _transactions.BeginUpdate();
        try
        {
            _transactions.Clear();
            FaultHandler.Shield(() =>
                _transactions.AddRange(InvoiceTransactionsRepository.GetTransactionsInShift(shiftId)));
        }
        finally
        {
            _transactions.EndUpdate();
        }

        foreach (var tx in _transactions)
        {
            var invoice = getInvoiceSlice(tx.InvoiceId);

            switch ((InvoiceTransactionType)tx.TxType)
            {
                case InvoiceTransactionType.Payment:
                    invoice.AddCashReceived(tx.TxAmount);
                    invoice.AddNonCashReceived(tx.NonCashAmount);
                    invoice.AddReceivedTotal(tx.TxAmount + tx.NonCashAmount);
                    //invoice.AddReceived(tx.TxAmount);
                    break;
                case InvoiceTransactionType.Refund:
                    invoice.AddRefunded(tx.TxAmount);
                    break;
                case InvoiceTransactionType.CashDiscount:
                    invoice.AddDiscounted(tx.TxAmount);
                    break;
            }
        }
    }
}