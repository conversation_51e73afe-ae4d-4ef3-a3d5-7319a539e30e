﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: InvoiceInShiftSlice.cs 544 2013-04-27 07:31:16Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using AutoMapper;
using LabMaestro.Domain;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class InvoiceInShiftSlice
{
    public long InvoiceId { get; set; }

    public string OrderId { get; set; }

    public DateTime OrderDateTime { get; set; }

    public string PatientName { get; set; }

    public string ReferrerName { get; set; }

    public decimal InvoiceGrossPayable { get; set; }

    public decimal InvoiceNetPayable { get; set; }

    public decimal InvoiceDueAmount { get; set; }

    public decimal Refunded { get; set; }

    public decimal Discounted { get; set; }

    public decimal ReceivedTotal { get; set; }
    public decimal CashAmount { get; set; }
    public decimal NonCashAmount { get; set; }

    public static InvoiceInShiftSlice AssembleFrom(ShiftInvoiceSummary summary)
    {
        Mapper.CreateMap<ShiftInvoiceSummary, InvoiceInShiftSlice>()
            .ForMember(dest => dest.InvoiceGrossPayable, x => x.MapFrom(src => src.GrossPayable))
            .ForMember(dest => dest.InvoiceDueAmount, x => x.MapFrom(src => src.DueAmount))
            .ForMember(dest => dest.InvoiceNetPayable, x => x.MapFrom(src => src.NetPayable));
        var result = Mapper.Map<ShiftInvoiceSummary, InvoiceInShiftSlice>(summary);
        return result;
    }

    internal void AddReceivedTotal(decimal amount) => ReceivedTotal += amount;
    internal void AddCashReceived(decimal amount) => CashAmount += amount;
    internal void AddNonCashReceived(decimal amount) => NonCashAmount += amount;
    internal void AddDiscounted(decimal amount) => Discounted += amount;
    internal void AddRefunded(decimal amount) => Refunded += amount;

    internal ShiftReportDetailPrintDto ToPrintDto() =>
        new()
        {
            ActualInvoiceId = InvoiceId,
            InvoiceId = InvoiceId.ToString(),
            OrderId = OrderId,
            OrderDateTime = SharedUtilities.DateTimeToShortString(OrderDateTime),
            PatientName = PatientName,
            ReferredBy = ReferrerName,
            GrossBillAmount = SharedUtilities.MoneyToString(InvoiceGrossPayable),
            NetBillAmount = SharedUtilities.MoneyToString(InvoiceNetPayable),
            DiscountAmount = SharedUtilities.MoneyToString(Discounted),
            ReceivedAmount = SharedUtilities.MoneyToString(ReceivedTotal),
            CashAmount = SharedUtilities.MoneyToString(CashAmount),
            NonCashAmount = SharedUtilities.MoneyToString(NonCashAmount),
            RefundAmount = SharedUtilities.MoneyToString(Refunded)
        };
}