﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: DailyReceivablesReportBuilder.cs 1186 2014-03-03 04:32:42Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using C1.LiveLinq;
using C1.LiveLinq.Collections;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class DailyReceivablesReportBuilder
{
    private readonly IndexedCollection<UserDailyReceivablesSummary> _userSummaries = [];
    public DateTime TargetDate { get; set; }
    public int RoleId { get; set; } = -1;

    private int getRoleId(string roleCode)
    {
        var role = RolesRepository.FindRoleByCode(roleCode);
        return role?.Id ?? -1;
    }

    private UserDailyReceivablesSummary ensureUserSummary(TransactionDetailExSlice slice)
    {
        var summary = _userSummaries.AsIndexed().SingleOrDefault(x => x.UserId == slice.PerformingUserId);
        if (summary == null)
        {
            summary = UserDailyReceivablesSummary.AssembleFrom(slice, TargetDate);
            _userSummaries.Add(summary);
        }

        return summary;
    }

    public void CompileReport()
    {
        if (RoleId < 0) RoleId = getRoleId(UserRoles.AccountsReceivable);
        var dtFrom = TargetDate.Date;
        var dtTo = SharedUtilities.LastMinuteOfDay(TargetDate.Date);
        var slices = FaultHandler.Shield(() =>
            InvoiceTransactionsRepository.GetAllTransactionsForRoleDateRangeEx((short)RoleId, dtFrom, dtTo));
        foreach (var slice in slices) ensureUserSummary(slice).UpdateSummary(slice);
    }

    private UserReceivablesSummaryPrintDto getSummaryTotal(IReadOnlyCollection<UserDailyReceivablesSummary> slices,
        bool getCurrent)
    {
        var cash = slices.Sum(x => getCurrent ? x.CurrentCashAmount : x.BackdatedCashAmount);
        var noncash = slices.Sum(x => getCurrent ? x.CurrentNonCashAmount : x.BackdatedNonCashAmount);
        var discount = slices.Sum(x => getCurrent ? x.CurrentDiscountAmount : x.BackdatedDiscountAmount);
        var refund = slices.Sum(x => getCurrent ? x.CurrentRefundAmount : x.BackdatedRefundAmount);

        return new UserReceivablesSummaryPrintDto
        {
            CashAmount = SharedUtilities.MoneyToStringPlainCultureNonZero(cash),
            NonCashAmount = SharedUtilities.MoneyToStringPlainCultureNonZero(noncash),
            DiscountAmount = SharedUtilities.MoneyToStringPlainCultureNonZero(discount),
            RefundAmount = SharedUtilities.MoneyToStringPlainCultureNonZero(refund)
        };
    }

    public DailyReceivablesReportPrintDto CompilePrintDto(string printedBy)
    {
        var dto = new DailyReceivablesReportPrintDto
        {
            PrintedBy = printedBy,
            PrintedOn = SharedUtilities.DateTimeToString24(AppSysRepository.GetServerTime()),
            TargetDate = SharedUtilities.DateToStringOnlySlash(TargetDate)
        };

        var currentSummaries = _userSummaries.AsIndexed().Where(x => x.HasCurrentTransactions()).ToList();
        foreach (var summary in currentSummaries.OrderBy(u => u.UserName))
            dto.CurrentSummaries.Add(summary.ToPrintDto(true));
        dto.CurrentSummaryTotal = getSummaryTotal(currentSummaries, true);

        var pastSummaries = _userSummaries.AsIndexed().Where(x => x.HasBackdatedTransactions()).ToList();
        foreach (var summary in pastSummaries.OrderBy(u => u.UserName))
            dto.BackdatedSummaries.Add(summary.ToPrintDto(false));
        dto.BackdatedSummaryTotal = getSummaryTotal(pastSummaries, false);

        return dto;
    }
}