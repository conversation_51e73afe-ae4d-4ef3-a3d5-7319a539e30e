﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: InvoiceFinancialAuditTrailReportBuilder.cs 1003 2013-10-14 07:50:56Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Linq;
using C1.LiveLinq;
using C1.LiveLinq.Collections;
using CuttingEdge.Conditions;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class InvoiceFinancialAuditTrailReportBuilder
{
    private readonly IndexedCollection<FinancialAuditInvoiceMasterSlice> _invoices = new();
    private readonly IndexedCollection<UserFinancialAuditSummary> _userSummaries = new();

    public DateTime DateFrom { get; set; }
    public DateTime DateTo { get; set; }
    public InvoiceTransactionType TransactionExclude { get; set; }
    public bool RemoveInitialTransactions { get; set; }

    private FinancialAuditInvoiceMasterSlice findInvoice(long invoiceId) =>
        _invoices.AsIndexed().SingleOrDefault(x => x.InvoiceId == invoiceId);

    private void populateInvoices()
    {
        Condition.Requires(DateTo).IsGreaterThan(DateFrom);

        _invoices.BeginUpdate();
        try
        {
            _invoices.Clear();

            var slices = FaultHandler.Shield(
                () => InvoiceTransactionsRepository.GetAllNonMatchingTransactionsInDateRange(TransactionExclude,
                    DateFrom,
                    DateTo));

            if (slices is not { Count: > 0 }) return;

            foreach (var slice in slices)
            {
                ensureUserSummary(slice).UpdateSummary(slice);

                if (RemoveInitialTransactions && slice.TxFlag == (byte)TransactionFlag.InitialOperation)
                    continue;

                var dto = findInvoice(slice.InvoiceId);
                if (dto == null)
                {
                    dto = FinancialAuditInvoiceMasterSlice.AssembleFrom(slice);
                    _invoices.Add(dto);
                }
                else
                {
                    dto.AddTransaction(slice);
                }
            }
        }
        finally
        {
            _invoices.EndUpdate();
        }
    }

    private UserFinancialAuditSummary ensureUserSummary(TransactionInDateRangeSlice slice)
    {
        var summary = _userSummaries.AsIndexed().SingleOrDefault(x => x.UserId == slice.PerformingUserId);
        if (summary == null)
        {
            summary = UserFinancialAuditSummary.AssembleFrom(slice);
            _userSummaries.Add(summary);
        }

        return summary;
    }

    public void CompileReport() => populateInvoices();

    public InvoiceFinancialAuditTrailReportPrintDto ToPrintDto(string printedBy)
    {
        populateInvoices();

        var dto = new InvoiceFinancialAuditTrailReportPrintDto
        {
            DateFrom = SharedUtilities.DateToStringLong(DateFrom),
            DateTo = SharedUtilities.DateToStringLong(DateTo),
            PrintedOn = SharedUtilities.DateTimeToString24(AppSysRepository.GetServerTime()),
            PrintedBy = printedBy
        };

        foreach (var invoice in _invoices.AsIndexed().OrderBy(x => x.InvoiceId).ToList())
            dto.Invoices.Add(invoice.ToPrintDto());

        foreach (var userSummary in _userSummaries.AsIndexed().OrderBy(x => x.NumTransactions).ThenBy(x => x.UserName)
                     .ToList()) dto.UserSummaries.Add(userSummary.ToPrintDto());

        return dto;
    }
}