﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: InvoiceOutstandingDueCollectionSlice.cs 1537 2014-11-30 13:11:41Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using C1.LiveLinq.Collections;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class InvoiceOutstandingDueCollectionSlice
{
    public InvoiceOutstandingDueCollectionSlice()
    {
        Transactions = new List<InvoiceTransactionInfoSlice>();
    }

    public long InvoiceId { get; set; }
    public string OrderId { get; set; }
    public DateTime OrderDateTime { get; set; }

    public decimal GrossPayable { get; set; }
    public decimal NetPayable { get; set; }

    // the first discount given by reception desk
    public decimal InitialDiscount { get; set; }

    // sum-total of all subsequent discounts issued by various staff
    public decimal SubsequentDiscount { get; set; }
    public decimal TotalDiscount { get; set; }

    // first payment made at reception
    public decimal InitialPayment { get; set; }

    // due is calculated by deducting InitialPayment from NetPayable
    public decimal DueAmount { get; set; }

    // amount received (other than InitialPayment) till date
    public decimal DueCollection { get; set; }
    public decimal RefundAmount { get; set; }
    public List<InvoiceTransactionInfoSlice> Transactions { get; }

    public static InvoiceOutstandingDueCollectionSlice AssembleFrom(
        InvoiceTransactionInDateRangeTypeReferrerCategorySlice src)
    {
        var dto = new InvoiceOutstandingDueCollectionSlice
        {
            InvoiceId = src.InvoiceId,
            OrderId = src.OrderId,
            OrderDateTime = src.OrderDateTime,
            GrossPayable = src.GrossPayable,
            NetPayable = src.NetPayable
        };
        return dto;
    }

    public static InvoiceOutstandingDueCollectionSlice AssembleFrom(LabOrderSearchResultSlice src)
    {
        var dto = new InvoiceOutstandingDueCollectionSlice
        {
            InvoiceId = src.InvoiceId,
            OrderId = src.OrderId,
            OrderDateTime = src.OrderDateTime,
            GrossPayable = src.GrossPayable,
            NetPayable = src.NetPayable,
            RefundAmount = src.RefundAmount
        };
        return dto;
    }

    public static InvoiceOutstandingDueCollectionSlice AssembleFrom(PrimalInvoiceSlice src)
    {
        var dto = new InvoiceOutstandingDueCollectionSlice
        {
            InvoiceId = src.InvoiceId,
            OrderId = src.OrderId,
            OrderDateTime = src.OrderDateTime,
            GrossPayable = src.GrossPayable,
            NetPayable = src.NetPayable,
            DueAmount = src.DueAmount,
            InitialPayment = src.PaidAmount,
            InitialDiscount = src.DiscountAmount,
            TotalDiscount = src.DiscountAmount,
            RefundAmount = src.RefundAmount
        };
        return dto;
    }

    public void AdjustFinancesTillDate(DateTime beginDate, DateTime endDate)
    {
        var txs =
            FaultHandler.Shield(() => InvoiceTransactionsRepository.FindInvoiceTransactions(InvoiceId));
        var transactions = new IndexedCollection<TxInfo>();
        transactions.BeginUpdate();
        transactions.AddRange(txs.Select(TxInfo.AssembleFrom));
        transactions.EndUpdate();

        // initial discount
        var slice = transactions.SingleOrDefault(t =>
            t.TxType == InvoiceTransactionType.CashDiscount &&
            t.TxFlag == TransactionFlag.InitialOperation);

        InitialDiscount = slice != null ? slice.TxAmount : 0m;

        // subsequent discounts & rebates
        var discount = transactions.Where(t =>
                t.TxType == InvoiceTransactionType.CashDiscount &&
                t.TxFlag == TransactionFlag.None)
            .Sum(t => t.TxAmount);

        var discountRebate = transactions.Where(t =>
                t.TxType == InvoiceTransactionType.DiscountRebate &&
                t.TxFlag == TransactionFlag.None)
            .Sum(t => t.TxAmount);

        SubsequentDiscount = discount - discountRebate;
        TotalDiscount = InitialDiscount + SubsequentDiscount;

        // all refunds
        var refund = transactions.Where(t =>
                t.TxType == InvoiceTransactionType.Refund &&
                t.TxFlag == TransactionFlag.None)
            .Sum(t => t.TxAmount);

        // initial payment
        slice = transactions.SingleOrDefault(t =>
            t.TxType == InvoiceTransactionType.Payment &&
            t.TxFlag == TransactionFlag.InitialOperation);

        InitialPayment = slice != null ? slice.TxAmount : 0m;
        RefundAmount = refund;
        var actualPaid = InitialPayment - refund;
        DueAmount = NetPayable - actualPaid;

        if (endDate == DateTime.MinValue)
            DueCollection = transactions.Where(t =>
                    t.TxType == InvoiceTransactionType.Payment &&
                    t.TxFlag == TransactionFlag.None)
                .Sum(t => t.TxAmount);
        else
            DueCollection = transactions.Where(t =>
                    t.TxType == InvoiceTransactionType.Payment &&
                    t.TxFlag == TransactionFlag.None &&
                    t.TxTime >= beginDate &&
                    t.TxTime <= endDate)
                .Sum(t => t.TxAmount);
        //DueCollection -= refund;
    }

    public OutstandingDueCollectionDetailPrintDto ToPrintDto()
    {
        var dto = new OutstandingDueCollectionDetailPrintDto
        {
            InvoiceId = InvoiceId.ToString(),
            OrderId = OrderId,
            OrderDateTime = SharedUtilities.DateToStringOnlySlash(OrderDateTime),
            DueAmount = SharedUtilities.MoneyToStringPlainCultureNonZero(DueAmount),
            RefundAmount = SharedUtilities.MoneyToStringPlainCultureNonZero(RefundAmount),
            DueCollection = SharedUtilities.MoneyToStringPlainCultureNonZero(DueCollection),
            GrossPayable = SharedUtilities.MoneyToStringPlainCultureNonZero(GrossPayable),
            InitialPayment = SharedUtilities.MoneyToStringPlainCultureNonZero(InitialPayment),
            InitialDiscount = SharedUtilities.MoneyToStringPlainCultureNonZero(InitialDiscount),
            NetPayable = SharedUtilities.MoneyToStringPlainCultureNonZero(NetPayable),
            SubsequentDiscount = SharedUtilities.MoneyToStringPlainCultureNonZero(SubsequentDiscount),
            TotalDiscount = SharedUtilities.MoneyToStringPlainCultureNonZero(TotalDiscount)
        };

        foreach (var slice in Transactions) dto.Transactions.Add(slice.ToPrintDto());

        return dto;
    }
}

internal sealed class TxInfo
{
    public decimal TxAmount { get; set; }
    public DateTime TxTime { get; set; }
    public InvoiceTransactionType TxType { get; set; }
    public TransactionFlag TxFlag { get; set; }

    public static TxInfo AssembleFrom(SimpleInvoiceTransactionInfo src)
    {
        return new TxInfo
        {
            TxAmount = src.TxAmount,
            TxTime = src.TxTime,
            TxType = (InvoiceTransactionType)src.TxType,
            TxFlag = (TransactionFlag)src.TxFlag
        };
    }
}