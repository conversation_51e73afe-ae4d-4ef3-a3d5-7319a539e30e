﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: OutstandingDuesCollectionReportBuilder.cs 1537 2014-11-30 13:11:41Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using C1.LiveLinq;
using C1.LiveLinq.Collections;
using FluentDateTime;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class OutstandingDuesCollectionReportBuilder
{
    private readonly IndexedCollection<InvoiceOutstandingDueCollectionSlice> _paidInvoices = new();

    public DateTime BeginDate { get; set; }

    public DateTime EndDate { get; set; }

    public short ReferrerCategory { get; set; } = -1;

    public FilterMode ReferrerFilterMode { get; set; }

    public TransactionFlag ExclusionFlag { get; set; }

    public bool OrderCreationMustBeWithinDateRange { get; set; } = false;

    public List<InvoiceOutstandingDueCollectionSlice> PaidInvoices =>
        _paidInvoices.AsIndexed().OrderBy(x => x.InvoiceId).ToList();

    public void SetSingleDateRange(DateTime date)
    {
        BeginDate = date.Date;
        EndDate = SharedUtilities.LastMinuteOfDay(BeginDate);
    }

    public void CompileReport(bool latestReport)
    {
        var txExcludeFlag = TransactionFlag.InitialOperation;
        var txType = InvoiceTransactionType.Payment;
        /*
         * Strategy:
         *  1a) Scan InvoiceId, GrossBill based on transaction date + exclusion flag +/- referrer category
         *  For each InvoiceId:
         *      2a) Get first discount.
         *      2b) Get SUM of subsequent discounts (TxFlag <> Initial)
         *      2c) Get first payment. Calculate DUE (Net - first payment)
         *
         */

        // 1a: Scan all invoices with payments made in date range
        List<InvoiceTransactionInDateRangeTypeReferrerCategorySlice> txPayments = null;
        if (ReferrerCategory <= 0)
            // fetch all transactions - do not filter by referrer category
            txPayments = FaultHandler.Shield(() => InvoiceTransactionsRepository
                .GetInvoiceTransactionsInDateRangeType(
                    BeginDate,
                    EndDate,
                    txExcludeFlag,
                    txType));
        else
            FaultHandler.Shield(() =>
            {
                if (ReferrerFilterMode == FilterMode.IncludeItems)
                    txPayments = InvoiceTransactionsRepository
                        .GetInvoiceTransactionsInDateRangeTypeReferrerCategory(
                            BeginDate,
                            EndDate,
                            txExcludeFlag,
                            txType,
                            ReferrerCategory);
                else
                    txPayments = InvoiceTransactionsRepository
                        .GetInvoiceTransactionsInDateRangeTypeExcludeReferrerCategory(
                            BeginDate,
                            EndDate,
                            txExcludeFlag,
                            txType,
                            ReferrerCategory);
            });

        var invoices = new List<long>();

        _paidInvoices.BeginUpdate();
        try
        {
            _paidInvoices.Clear();
            foreach (var slice in txPayments)
            {
                // daily due collection: filter out orders created out of date-range
                if (!allowOrderCreationDate(slice, EndDate.BeginningOfDay())) continue;

                // filter out existing invoices
                if (invoices.All(x => x != slice.InvoiceId))
                {
                    _paidInvoices.Add(InvoiceOutstandingDueCollectionSlice.AssembleFrom(slice));
                    invoices.Add(slice.InvoiceId);
                }
            }
        }
        finally
        {
            _paidInvoices.EndUpdate();
        }

        //var invoiceIds = _paidInvoices.AsIndexed().Select(x => x.InvoiceId).ToList();
        var cutOffDate = latestReport ? AppSysRepository.GetServerTime() : EndDate;
        foreach (var slice in _paidInvoices) slice.AdjustFinancesTillDate(BeginDate, cutOffDate);
    }

    private bool allowOrderCreationDate(InvoiceTransactionInDateRangeTypeReferrerCategorySlice slice,
        DateTime cutoffTime)
    {
        return !OrderCreationMustBeWithinDateRange
            ? slice.OrderDateTime <
              cutoffTime // FIX #104: ASO reports should include lab orders only from previous dates
            : slice.OrderDateTime >= BeginDate && slice.OrderDateTime <= EndDate;
    }

    public OutstandingDuesCollectionMasterPrintDto ToPrintDto(string username)
    {
        var title = string.Format("{0} Sales Outstanding) Collection Report",
            OrderCreationMustBeWithinDateRange ? "DSO (Daily" : "ASO (Aggregate");
        var dto = new OutstandingDuesCollectionMasterPrintDto
        {
            ReportTitle = title,
            PrintedBy = username,
            PrintedOn = SharedUtilities.DateTimeToString24(AppSysRepository.GetServerTime()),
            DateStart = SharedUtilities.DateToStringLong(BeginDate),
            DateEnd = SharedUtilities.DateToStringLong(EndDate),
            GrossPayable = SharedUtilities.MoneyToString(_paidInvoices.Sum(x => x.GrossPayable)),
            NetPayable = SharedUtilities.MoneyToString(_paidInvoices.Sum(x => x.NetPayable)),
            InitialDiscount = SharedUtilities.MoneyToString(_paidInvoices.Sum(x => x.InitialDiscount)),
            SubsequentDiscount = SharedUtilities.MoneyToString(_paidInvoices.Sum(x => x.SubsequentDiscount)),
            InitialPayment = SharedUtilities.MoneyToString(_paidInvoices.Sum(x => x.InitialPayment)),
            DueAmount = SharedUtilities.MoneyToString(_paidInvoices.Sum(x => x.DueAmount)),
            DueCollection = SharedUtilities.MoneyToString(_paidInvoices.Sum(x => x.DueCollection)),
            RefundAmount = SharedUtilities.MoneyToString(_paidInvoices.Sum(x => x.RefundAmount))
        };

        //for (int i = 0; i < 800; i++)
        dto.InvoiceCollectionDetails.AddRange(_paidInvoices.AsIndexed()
            .OrderBy(x => x.InvoiceId)
            .Select(x => x.ToPrintDto()));
        return dto;
    }
}