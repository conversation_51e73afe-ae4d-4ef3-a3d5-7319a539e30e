﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2015, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id$
// 
// Last modified: 2015.08.22 2:44 PM
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Linq;
using C1.LiveLinq.Collections;
using LabMaestro.Domain;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class DiscountOverageReportBuilder
{
    private readonly IndexedCollection<DiscountOverageInfoSlice> _invoices;

    public DiscountOverageReportBuilder()
    {
        _invoices = new IndexedCollection<DiscountOverageInfoSlice>();
        Summary = new DiscountOverageInfoSlice();
    }

    public DateTime DateFrom { get; set; }
    public DateTime DateTo { get; set; }
    public bool SortByDiscountPercentage { get; set; }
    public int DiscountThreshold { get; set; }
    public float SummaryRevenuePercentage { get; set; }
    public DiscountOverageInfoSlice Summary { get; }

    private void calculateSummary()
    {
        var gross = 0m;
        var net = 0m;
        var paid = 0m;
        var due = 0m;
        var discount = 0m;
        var refund = 0m;

        foreach (var slice in _invoices)
        {
            gross += slice.GrossPayable;
            net += slice.NetPayable;
            paid += slice.Paid;
            due += slice.Due;
            discount += slice.Discount;
            refund += slice.Refund;
        }

        Summary.GrossPayable = gross;
        Summary.NetPayable = net;
        Summary.Paid = paid;
        Summary.Due = due;
        Summary.Refund = refund;
        Summary.Discount = discount;
        Summary.CalculateRatio();
        SummaryRevenuePercentage = 100 - Summary.DiscountPercentage;
    }

    public DiscountOverageReportPrintDto CompileReport()
    {
        var allInvoices = PatientLabOrdersRepository.FilterLabOrdersByDateRange(DateFrom.Date,
            SharedUtilities.LastMinuteOfDay(DateTo));

        foreach (var slice in allInvoices
                     .Where(i => i.IsCancelled == false &&
                                 i.CurrentInvoiceSnapshot.DiscountAmount > 0)
                     .Select(DiscountOverageInfoSlice.AssembleFrom)
                     .Where(s => s.DiscountPercentage >= DiscountThreshold)
                     .OrderBy(s => s.InvoiceId))
            _invoices.Add(slice);

        calculateSummary();

        var result = new DiscountOverageReportPrintDto
        {
            Summary = Summary.ToPrintDto(),
            SummaryRevenuePercentage = SharedUtilities.DoubleToStringSingleFractionPlain(SummaryRevenuePercentage),
            DateFrom = SharedUtilities.DateToStringLong(DateFrom),
            DateTo = SharedUtilities.DateToStringLong(DateTo),
            DiscountThreshold = string.Format("{0} %", DiscountThreshold),
            NumOrders = _invoices.Count.ToString()
        };

        result.Invoices.AddRange(SortByDiscountPercentage
            ? _invoices.OrderByDescending(s => s.DiscountPercentage).Select(x => x.ToPrintDto())
            : _invoices.Select(x => x.ToPrintDto()));
        return result;
    }
}