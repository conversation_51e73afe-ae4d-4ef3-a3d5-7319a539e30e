﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: InvoiceTransactionInfoSlice.cs 1178 2014-02-18 14:45:38Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using LabMaestro.Domain;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class InvoiceTransactionInfoSlice
{
    public DateTime TxTime { get; set; }
    public InvoiceTransactionType TxType { get; set; }
    public PaymentMethod PaymentMethod { get; set; }
    public decimal Amount { get; set; }
    public decimal NonCashAmount { get; set; }
    public string StaffName { get; set; }
    public string Remarks { get; set; }

    public static InvoiceTransactionInfoSlice AssembleFrom(DetailedInvoiceTransactionInfo src) =>
        new()
        {
            Amount = src.TxAmount,
            Remarks = src.UserRemarks,
            StaffName = src.StaffName,
            TxTime = src.TxTime,
            TxType = (InvoiceTransactionType)src.TxType,
            PaymentMethod = PaymentMethod.Cash,
        };

    public static InvoiceTransactionInfoSlice AssembleFrom(InvoiceTransactionDetailedExSlice src) =>
        new()
        {
            Amount = src.TxAmount,
            Remarks = src.UserRemarks,
            StaffName = src.StaffName,
            TxTime = src.TxTime,
            TxType = (InvoiceTransactionType)src.TxType,
            NonCashAmount = src.NonCashAmount,
            PaymentMethod = (PaymentMethod)src.PaymentMethod,
        };

    public InvoiceTransactionInfoPrintDto ToPrintDto() =>
        new()
        {
            Amount = SharedUtilities.MoneyToStringPlainCultureNonZero(Amount),
            NonCashAmount = SharedUtilities.MoneyToStringPlainCultureNonZero(NonCashAmount),
            Remarks = Remarks,
            StaffName = StaffName,
            TxTime = SharedUtilities.DateTimeToStringDMHM(TxTime)
        };
}