// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2015, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id$
// 
// Last modified: 2015.08.22 12:59 PM
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using LabMaestro.Domain;
using LabMaestro.Printing;

namespace LabMaestro.BusinessLogic;

internal sealed class DailyInvoicesCollection
{
    private readonly DateTime _period;
    private readonly InvoiceFinancialInfoSlice _summary;

    internal DailyInvoicesCollection(DateTime dt)
    {
        _period = dt;
        Invoices = new List<InvoiceFinancialInfoSlice>();
        _summary = new InvoiceFinancialInfoSlice();
    }

    internal InvoiceFinancialInfoSlice Summary
    {
        get { return updateSummary(); }
    }

    internal List<InvoiceFinancialInfoSlice> Invoices { get; }

    internal void Add(InvoiceSnapShotSlice invoice)
    {
        Invoices.Add(InvoiceFinancialInfoSlice.AssembleFrom(invoice));
    }

    private InvoiceFinancialInfoSlice updateSummary()
    {
        _summary.Reset();
        foreach (var invoice in Invoices)
        {
            _summary.GrossPayable += invoice.GrossPayable;
            _summary.NetPayable += invoice.NetPayable;
            _summary.DiscountAmount += invoice.DiscountAmount;
            _summary.DueAmount += invoice.DueAmount;
            _summary.PaidAmount += invoice.PaidAmount;
            _summary.RefundAmount += invoice.RefundAmount;
            _summary.TaxAmount += invoice.TaxAmount;
            _summary.SurchargeAmount += invoice.SurchargeAmount;
            _summary.Count++;
        }

        return _summary;
    }

    internal DailyIncomeSummaryPrintDto ToPrintDto()
    {
        return Summary.ToPrintDto(_period);
    }
}