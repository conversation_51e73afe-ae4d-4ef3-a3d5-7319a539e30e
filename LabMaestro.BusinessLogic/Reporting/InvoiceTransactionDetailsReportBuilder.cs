﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: InvoiceTransactionDetailsReportBuilder.cs 782 2013-07-09 14:09:34Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using CuttingEdge.Conditions;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class InvoiceTransactionDetailsReportBuilder(long invoiceId)
{
    private InvoiceMasterPrintDto fetchInvoiceDetails(string printedBy)
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);

        var slice = FaultHandler.Shield(() => PatientLabOrdersRepository.FetchLabOrderDetails(invoiceId));
        return new InvoiceMasterPrintDto
        {
            InvoiceId = slice.InvoiceId.ToString(),
            OrderId = slice.OrderId,
            OrderDateTime = SharedUtilities.DateTimeToString24(slice.OrderDateTime),
            PatientName = $"{slice.Title} {slice.FirstName} {slice.LastName}".Trim(),
            ReferredBy = slice.ReferrerName,
            GrossPayable = SharedUtilities.MoneyToString(slice.GrossPayable),
            NetPayable = SharedUtilities.MoneyToString(slice.NetPayable),
            DiscountAmount = SharedUtilities.MoneyToString(slice.DiscountAmount),
            PaidAmount = SharedUtilities.MoneyToString(slice.PaidAmount),
            DueAmount = SharedUtilities.MoneyToString(slice.DueAmount),
            PrintedOn = SharedUtilities.DateTimeToString24(AppSysRepository.GetServerTime()),
            PrintedBy = printedBy
        };
    }

    private void populateTransactionsList(InvoiceMasterPrintDto dto)
    {
        var txList = FaultHandler.Shield(
            () => InvoiceTransactionsRepository.FindInvoiceTransactionsDetailedEx(invoiceId));
        foreach (var slice in txList)
        {
            var txDto = new InvoiceTransactionDetailsPrintDto
            {
                AuthorizedBy = slice.AuthorizerName,
                PerformedBy = slice.StaffName,
                TransactionId = string.Empty,
                Remarks = slice.UserRemarks,
                TxAmount = SharedUtilities.MoneyToString(slice.TxAmount + slice.NonCashAmount),
                TxTime = SharedUtilities.DateTimeToString24(slice.TxTime),
                TxType = EnumUtils.EnumDescription((InvoiceTransactionType)slice.TxType),
                PaymentMethod = EnumUtils.EnumDescription((PaymentMethod)slice.PaymentMethod),
            };
            dto.TransactionDetails.Add(txDto);
        }
    }

    public InvoiceMasterPrintDto CompileReport(string printedBy)
    {
        var dto = fetchInvoiceDetails(printedBy);
        populateTransactionsList(dto);
        return dto;
    }
}