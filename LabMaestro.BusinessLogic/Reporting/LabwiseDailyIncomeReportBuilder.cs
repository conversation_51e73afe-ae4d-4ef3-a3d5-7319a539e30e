﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2015, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id$
// 
// Last modified: 2015.08.22 2:27 PM
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using LabMaestro.Domain;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class LabwiseDailyIncomeReportBuilder
{
    private readonly Dictionary<DateTime, DailyInvoicesCollection> _catalog = new();
    private readonly InvoiceFinancialInfoSlice _summary = new();

    public InvoiceFinancialInfoSlice this[DateTime key] => ensureCollection(key).Summary;

    public InvoiceFinancialInfoSlice Summary => updateSummary();

    public List<DateTime> Periods => _catalog.Keys.OrderBy(x => x).ToList();

    internal List<InvoiceFinancialInfoSlice> DailySummaries => Periods.Select(k => _catalog[k].Summary).ToList();

    internal List<DailyIncomeSummaryPrintDto> DailySummariesPrintDto =>
        Periods.Select(k => _catalog[k].ToPrintDto()).ToList();

    private InvoiceFinancialInfoSlice updateSummary()
    {
        _summary.Reset();
        foreach (var list in _catalog.Values)
        {
            _summary.GrossPayable += list.Summary.GrossPayable;
            _summary.NetPayable += list.Summary.NetPayable;
            _summary.DiscountAmount += list.Summary.DiscountAmount;
            _summary.DueAmount += list.Summary.DueAmount;
            _summary.PaidAmount += list.Summary.PaidAmount;
            _summary.RefundAmount += list.Summary.RefundAmount;
            _summary.TaxAmount += list.Summary.TaxAmount;
            _summary.SurchargeAmount += list.Summary.SurchargeAmount;
            _summary.Count += list.Summary.Count;
        }

        return _summary;
    }

    private DailyInvoicesCollection ensureCollection(DateTime dt)
    {
        var key = dt.Date;
        if (!_catalog.ContainsKey(key)) _catalog.Add(key, new DailyInvoicesCollection(key));
        return _catalog[key];
    }

    public void Add(DateTime orderDate, InvoiceSnapShotSlice slice) => ensureCollection(orderDate).Add(slice);

    public List<Tuple<DateTime, InvoiceFinancialInfoSlice>> GetSortedItems() =>
        _catalog.Keys
            .OrderBy(x => x)
            .Select(k => new Tuple<DateTime, InvoiceFinancialInfoSlice>(k, _catalog[k].Summary))
            .ToList();

    public LabwiseDailyIncomeReportPrintDto ToPrintDto(DateTime dtFrom, DateTime dtTill, string labHeading) =>
        new()
        {
            Summary = Summary.ToPrintDto(DateTime.Now),
            DailyIncomeSummaries = DailySummariesPrintDto,
            BeginDate = SharedUtilities.DateToString(dtFrom),
            EndDate = SharedUtilities.DateToString(dtTill),
            LabHeading = labHeading,
            PrintDate = SharedUtilities.DateTimeToShortString(AppSysRepository.GetServerTime())
        };
}