﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2015, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id$
// 
// Last modified: 2015.08.21 8:35 PM
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using LabMaestro.BusinessLogic.GroupedIncomeStatement;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Printing;
using Microsoft.Practices.EnterpriseLibrary.Common.Utility;

namespace LabMaestro.BusinessLogic;

public sealed class IncomeStatementReportBuilder
{
    private List<InvoiceMaster> _currentInvoices;
    private List<PrimalInvoiceSlice> _primalInvoices = [];

    public IncomeStatementReportBuilder() => resetItems();

    public List<short> LabIds { get; } = [];
    public List<FilteredInvoiceSlice> Invoices { get; private set; }
    public DateTime BeginDate { get; set; }
    public DateTime EndDate { get; set; }
    public FilterMode LabsFilterMode { get; set; }
    public int DiscountedInvoicesCount { get; private set; }
    public int RefundedInvoicesCount { get; private set; }
    public short ReferrerCategory { get; set; } = -1;
    public InvoiceSnapShotSlice CurrentSnapshotsSummary { get; private set; }
    public InvoiceSnapShotSlice PrimalSnapshotsSummary { get; private set; }
    public bool ProcessPrimalSnapshot { get; set; } = true;
    public int PaidUpInvoicesCount { get; set; }
    public int ReferredInvoicesCount { get; set; }
    public decimal ReferredInvoicesValue { get; set; }
    public double ReferredInvoicesPercent { get; set; }
    public int UnreferredInvoicesCount { get; set; }
    public decimal UnreferredInvoicesValue { get; set; }
    public double UnreferredInvoicesPercent { get; set; }
    public double PaidUpInvoicesPercent { get; set; }
    public string LabGroup { get; set; }
    public GroupedLabOrdersCollection GroupedOrdersCollection { get; set; }
    public bool IncludeAuxiliaryLabs { get; set; }

    public void CompileInvoicesList()
    {
        resetItems();

        if (!validate()) return;
        if (GroupedOrdersCollection == null)
        {
            GroupedOrdersCollection = new GroupedLabOrdersCollection(IncludeAuxiliaryLabs);
            FaultHandler.Shield(() => GroupedOrdersCollection.ScanLabOrders(BeginDate, EndDate));
        }

        var orders = string.IsNullOrEmpty(LabGroup)
            ? GroupedOrdersCollection.LabOrders
            : GroupedOrdersCollection.GetOrdersForLabGroup(LabGroup);
        var items = orders.Select(o => o.ToFilteredInvoiceSlice()).ToList();
        /*
        switch (LabsFilterMode)
        {
            case FilterMode.IncludeItems:
                items = ReferrerCategory > 0
                            ? FaultHandler.Shield(
                                                  () => PatientLabOrdersRepository
                                                            .FilterLabOrdersByIncludePerformingLabDateRangeReferrerCategory
                                                            (BeginDate,
                                                             EndDate,
                                                             ReferrerCategory,
                                                             LabIds))
                            : FaultHandler.Shield(
                                                  () => PatientLabOrdersRepository
                                                            .FilterLabOrdersByIncludePerformingLabDateRange(
                                                                                                            BeginDate,
                                                                                                            EndDate,
                                                                                                            LabIds));
                break;
            default:
                if (LabIds.Count > 0)
                {
                    items = ReferrerCategory > 0
                                ? FaultHandler.Shield(
                                                      () => PatientLabOrdersRepository
                                                                .FilterLabOrdersByExcludePerformingLabDateRangeReferrerCategory
                                                                (BeginDate,
                                                                 EndDate,
                                                                 ReferrerCategory,
                                                                 LabIds))
                                : FaultHandler.Shield(
                                                      () =>
                                                      PatientLabOrdersRepository
                                                          .FilterLabOrdersByExcludePerformingLabDateRange(
                                                                                                          BeginDate,
                                                                                                          EndDate,
                                                                                                          LabIds));
                }
                else
                {
                    items = FaultHandler.Shield(
                                                () =>
                                                PatientLabOrdersRepository.FilterLabOrdersByDateRange(BeginDate,
                                                                                                      EndDate));
                }
                break;
        }
        */
        _primalInvoices =
            FaultHandler.Shield(() => InvoiceMasterRepository.GetPrimalInvoicesByDateRange(BeginDate, EndDate));
        _currentInvoices =
            FaultHandler.Shield(() => InvoiceMasterRepository.GetInvoiceMastersByDateRange(BeginDate, EndDate));

        if (items.Any()) Invoices.AddRange(items);
        Invoices.ForEach(processFilteredInvoice);
        calculatePercentages();
    }

    private void resetItems()
    {
        Invoices = [];
        CurrentSnapshotsSummary = new();
        PrimalSnapshotsSummary = new();

        DiscountedInvoicesCount = 0;
        RefundedInvoicesCount = 0;

        PaidUpInvoicesCount = 0;
        PaidUpInvoicesPercent = 0;

        ReferredInvoicesCount = 0;
        ReferredInvoicesPercent = 0;
        ReferredInvoicesValue = 0;

        UnreferredInvoicesCount = 0;
        UnreferredInvoicesPercent = 0;
        UnreferredInvoicesValue = 0;
    }

    private void calculatePercentages()
    {
        var total = Invoices.Count;
        if (ReferredInvoicesCount > 0)
            ReferredInvoicesPercent = Math.Round((double)(100 * ReferredInvoicesCount) / total);
        if (UnreferredInvoicesCount > 0)
            UnreferredInvoicesPercent = Math.Round((double)(100 * UnreferredInvoicesCount) / total);
        if (PaidUpInvoicesCount > 0)
            PaidUpInvoicesPercent = Math.Round((double)(100 * PaidUpInvoicesCount) / total);
    }

    private void processFilteredInvoice(FilteredInvoiceSlice invoice)
    {
        if (ProcessPrimalSnapshot)
        {
            var primalInvoice = _primalInvoices.SingleOrDefault(i => i.InvoiceId == invoice.InvoiceId);
            invoice.ResetIfCancelled = false;
            invoice.UpdatePrimalSnapshot(primalInvoice);
        }
        else
        {
            var currentInvoice = _currentInvoices.SingleOrDefault(i => i.InvoiceId == invoice.InvoiceId);
            invoice.ResetIfCancelled = false;
            invoice.UpdateCurrentSnapshot(currentInvoice);
        }

        var theSnapshot = ProcessPrimalSnapshot ? invoice.PrimalInvoiceSnapshot : invoice.CurrentInvoiceSnapshot;

        if (LabsFilterMode == FilterMode.IncludeItems && LabIds.Count > 0) invoice.UpdateOrderedTestNames(LabIds[0]);

        if (theSnapshot.DiscountAmount > 0m) DiscountedInvoicesCount++;

        if (invoice.CurrentInvoiceSnapshot.RefundAmount > 0m) RefundedInvoicesCount++;

        if (theSnapshot.DueAmount == 0m && theSnapshot.NetPayable == theSnapshot.PaidAmount)
            PaidUpInvoicesCount++;

        CurrentSnapshotsSummary.AppendFrom(invoice.CurrentInvoiceSnapshot);

        if (ProcessPrimalSnapshot) PrimalSnapshotsSummary.AppendFrom(invoice.PrimalInvoiceSnapshot);

        if (invoice.DisallowReferral || invoice.IsReferrerUnknown)
        {
            UnreferredInvoicesCount++;
            UnreferredInvoicesValue += theSnapshot.NetPayable;
        }
        else
        {
            ReferredInvoicesCount++;
            ReferredInvoicesValue += theSnapshot.NetPayable;
        }

        if (!ProcessPrimalSnapshot) invoice.SwitchPrimalToCurrentSnapshot();
    }

    private bool validate() => BeginDate <= EndDate;

    public IncomeStatementReportPrintDto GenerateIncomeOutstandingReportPrintDto(string heading)
    {
        var dto = new IncomeStatementReportPrintDto
        {
            BeginDate = BeginDate,
            EndDate = EndDate,
            CurrentSnapshotsSummary = CurrentSnapshotsSummary,
            Invoices = Invoices,
            LabHeading = heading,
            DiscountedInvoicesCount = DiscountedInvoicesCount,
            RefundedInvoicesCount = RefundedInvoicesCount,
            InvoicesCount = Invoices.Count,
            CanceledInvoicesCount = Invoices.Count(x => x.IsCancelled),
            PrintDate = AppSysRepository.GetServerTime(),
            ReferredInvoicesCount = ReferredInvoicesCount,
            ReferredInvoicesPercent = ReferredInvoicesPercent,
            ReferredInvoicesValue = ReferredInvoicesValue,
            UnreferredInvoicesCount = UnreferredInvoicesCount,
            UnreferredInvoicesPercent = UnreferredInvoicesPercent,
            UnreferredInvoicesValue = UnreferredInvoicesValue,
            PaidUpInvoicesCount = PaidUpInvoicesCount,
            PaidUpInvoicesPercent = PaidUpInvoicesPercent,
            // ugly hack: in dynamic mode, assign current snapshots to primal
            PrimalSnapshotsSummary = ProcessPrimalSnapshot ? PrimalSnapshotsSummary : CurrentSnapshotsSummary
        };
        dto.Invoices.ForEach(x => x.LimitStrings());
        return dto;
    }
}