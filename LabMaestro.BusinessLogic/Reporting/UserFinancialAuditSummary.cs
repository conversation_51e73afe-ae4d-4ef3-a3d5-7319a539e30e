﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: UserFinancialAuditSummary.cs 1003 2013-10-14 07:50:56Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using LabMaestro.Domain;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class UserFinancialAuditSummary
{
    public decimal DiscountAmount { get; private set; }
    public decimal RefundAmount { get; private set; }
    public decimal DiscountRebateAmount { get; private set; }
    public decimal PaymentAmount { get; private set; }
    public int NumTransactions { get; private set; }
    public int NumDiscounts { get; private set; }
    public int NumRefunds { get; private set; }
    public int NumDiscountRebates { get; private set; }
    public int NumPayments { get; private set; }
    public short UserId { get; private set; }
    public string UserName { get; private set; }

    public static UserFinancialAuditSummary AssembleFrom(TransactionInDateRangeSlice slice)
    {
        return new UserFinancialAuditSummary
        {
            UserId = slice.PerformingUserId ?? -1,
            UserName = slice.PerformedBy
        };
    }

    public void UpdateSummary(TransactionInDateRangeSlice slice)
    {
        NumTransactions++;
        switch ((InvoiceTransactionType)slice.TxType)
        {
            case InvoiceTransactionType.CashDiscount:
                DiscountAmount += slice.TxAmount;
                NumDiscounts++;
                break;
            case InvoiceTransactionType.DiscountRebate:
                DiscountRebateAmount += slice.TxAmount;
                NumDiscountRebates++;
                break;
            case InvoiceTransactionType.Refund:
                RefundAmount += slice.TxAmount;
                NumRefunds++;
                break;
            case InvoiceTransactionType.Payment:
                PaymentAmount += slice.TxAmount;
                NumPayments++;
                break;
        }
    }

    public UserFinancialAuditSummaryPrintDto ToPrintDto()
    {
        return new UserFinancialAuditSummaryPrintDto
        {
            UserName = UserName,
            UserId = SharedUtilities.IntToStringPositive(UserId),
            DiscountAmount = SharedUtilities.MoneyToStringPlainNonZero(DiscountAmount),
            DiscountRebateAmount = SharedUtilities.MoneyToStringPlainNonZero(DiscountRebateAmount),
            RefundAmount = SharedUtilities.MoneyToStringPlainNonZero(RefundAmount),
            PaymentAmount = SharedUtilities.MoneyToStringPlainNonZero(PaymentAmount),
            NumDiscountRebates = SharedUtilities.IntToStringPositive(NumDiscountRebates),
            NumDiscounts = SharedUtilities.IntToStringPositive(NumDiscounts),
            NumRefunds = SharedUtilities.IntToStringPositive(NumRefunds),
            NumPayments = SharedUtilities.IntToStringPositive(NumPayments),
            NumTransactions = SharedUtilities.IntToStringPositive(NumTransactions)
        };
    }
}