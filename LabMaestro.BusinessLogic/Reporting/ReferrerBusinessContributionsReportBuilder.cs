﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ReferrerBusinessContributionsReportBuilder.cs 1230 2014-03-19 11:53:53Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class ReferrerBusinessContributionsReportBuilder
{
    private readonly List<ReferrerBusinessContributionSlice> _businessContributions = [];
    private readonly ReferralEligibleLabTestsCatalog _refCatalog = new();

    public DateTime DateFrom { get; set; }
    public DateTime DateTo { get; set; }
    public int ReferrerId { get; set; }
    public short AssociateLabId { get; set; }
    public short CorporateId { get; set; }
    public short AffiliateId { get; set; }

    public ReferrerSummaryInfo Summary { get; } = new();

    public IReadOnlyList<ReferrerBusinessContributionSlice> BusinessContributions => _businessContributions;

    public void CompileReport()
    {
        _refCatalog.PopulateCatalogFromDatabase();

        List<LabOrderSearchResultSliceEx> ordersByAssoc = [];
        List<LabOrderSearchResultSlice> ordersByDoc = [];

        if (CorporateId > 0) {
            ordersByAssoc = FaultHandler.Shield(
                () => PatientLabOrdersRepository.SearchLabOrderByCorporateIdAndDateRange(CorporateId, DateFrom.Date, SharedUtilities.LastMinuteOfDay(DateTo)));
        }
        else if (AssociateLabId > 0) {
            ordersByAssoc = FaultHandler.Shield(
                () => PatientLabOrdersRepository.SearchLabOrderByAssocLabAndDateRange(AssociateLabId, DateFrom.Date, SharedUtilities.LastMinuteOfDay(DateTo)));
        }
        else if (AffiliateId > 0) {
            ordersByAssoc = FaultHandler.Shield(
                () => PatientLabOrdersRepository.SearchLabOrderByAffiliateIdAndDateRange(AffiliateId, DateFrom.Date, SharedUtilities.LastMinuteOfDay(DateTo)));
        }
        else {
            ordersByDoc = FaultHandler.Shield(
                () => PatientLabOrdersRepository.SearchLabOrderByReferrerIdAndDateRange(ReferrerId, DateFrom.Date, SharedUtilities.LastMinuteOfDay(DateTo)));
        }

        foreach (var slice in ordersByAssoc.OrderBy(o => o.InvoiceId)) {
            _businessContributions.Add(ReferrerBusinessContributionSlice.AssembleFrom(slice, _refCatalog));
            updateSummary(slice);
        }

        foreach (var slice in ordersByDoc.OrderBy(o => o.InvoiceId)) {
            _businessContributions.Add(ReferrerBusinessContributionSlice.AssembleFrom(slice, _refCatalog));
            updateSummary(slice);
        }
    }

    private void updateSummary(LabOrderSearchResultSliceEx slice)
    {
        Summary.NumPatients += 1;
        Summary.NetBill += slice.NetPayable;
        Summary.GrossBill += slice.GrossPayable;
        Summary.Discount += slice.DiscountAmount;
        Summary.Due += slice.DueAmount;
        Summary.Payment += slice.PaidAmount;
        Summary.Refund += slice.RefundAmount;
        //_summary.PaidUpReferral += slice.PaidUpReferral; //TODO: implement
    }

    private void updateSummary(LabOrderSearchResultSlice slice)
    {
        Summary.NumPatients += 1;
        Summary.NetBill += slice.NetPayable;
        Summary.GrossBill += slice.GrossPayable;
        Summary.Discount += slice.DiscountAmount;
        Summary.Due += slice.DueAmount;
        Summary.Payment += slice.PaidAmount;
        Summary.Refund += slice.RefundAmount;
        //_summary.PaidUpReferral += slice.PaidUpReferral; //TODO: implement
    }

    public ReferrerBusinessContributionsReportPrintDto ToPrintDto(bool showRefDetails)
    {
        var dto = new ReferrerBusinessContributionsReportPrintDto
        {
            PrintedBy = CurrentUserContext.UserDisplayName,
            PrintedOn = SharedUtilities.DateTimeToShortString(AppSysRepository.GetServerTime()),
            DateFrom = SharedUtilities.DateToString(DateFrom),
            DateTo = SharedUtilities.DateToString(DateTo),
            Summary = Summary.ToPrintDto(),
        };

        if (CorporateId > 0) {
            var slice = CorporateClientRepository.Find(CorporateId);
            dto.ReferrerName = slice.Name;
            dto.ReferrerId = slice.UID;
        }
        else if (AssociateLabId > 0) {
            var slice = AssociateOrganizationRepository.Find(AssociateLabId);
            dto.ReferrerName = slice.Name;
            dto.ReferrerId = AssociateLabId.ToString();
        }
        else if (AffiliateId > 0) {
            var slice = AffiliateRepository.Find(AffiliateId);
            dto.ReferrerName = slice.Name;
            dto.ReferrerId = slice.UPIN;
        }
        else {
            dto.ReferrerName = ReferrersRepository.GetReferrerFullName(ReferrerId);
            dto.ReferrerId = ReferrerId.ToString();
        }

        foreach (var slice in _businessContributions) 
            dto.AddInvoiceReferralMaster(slice.ToPrintDto(showRefDetails));
        return dto;
    }
}