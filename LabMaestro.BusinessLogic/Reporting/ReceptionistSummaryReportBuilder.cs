﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ReceptionistSummaryReportBuilder.cs 1367 2014-06-12 06:54:27Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using C1.LiveLinq;
using C1.LiveLinq.Collections;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class ReceptionistSummaryReportBuilder
{
    private readonly IndexedCollection<ActiveUsersWithRoleSlice> _userCatalog = new(FaultHandler.Shield(() =>
                                                                                                            UsersRepository.GetAllActiveUsersWithRole()));

    private readonly IndexedCollection<UserSummarySlice> _userSummaries = new();
    private IndexedCollection<PrimalInvoiceSlice> _allInvoices;
    private IndexedCollection<TransactionsOfTypeInDateRangeWithInvoiceSlice> _allRefunds;

    public int InvoicesCount { get; private set; }
    public decimal GrossAmount { get; private set; }
    public decimal DiscountAmount { get; private set; }
    public decimal NetAmount { get; private set; }
    public decimal ReceivedAmount { get; private set; }
    public decimal RefundAmount { get; private set; }
    public decimal DueAmount { get; private set; }
    public short RoleId { get; set; }
    public DateTime BeginDate { get; set; }
    public DateTime EndDate { get; set; }
    public bool ExcludeCurrentDayInvoiceRefunds { get; set; } = true;

    public List<UserSummarySlice> ReceptionistSummaries =>
        _userSummaries
            .OrderBy(x => x.RoleName)
            .ThenBy(x => x.UserName)
            .ToList();

    public void SetSingleDateRange(DateTime date)
    {
        BeginDate = date.Date;
        EndDate = BeginDate.AddDays(1).AddMinutes(-1);
    }

    public void CompileReport()
    {
        ReceivedAmount = 0;
        NonCashAmount = 0;
        DiscountAmount = 0;
        RefundAmount = 0;
        InvoicesCount = 0;
        GrossAmount = 0;
        NetAmount = 0;
        DueAmount = 0;

        _allInvoices = new IndexedCollection<PrimalInvoiceSlice>(
            FaultHandler.Shield(() => InvoiceMasterRepository.GetPrimalInvoicesByDateRange(BeginDate, EndDate)));

        foreach (var userId in getDistinctOrderingStaffIdList())
            if (userBelongsToRole((short)userId, RoleCodes.Receptionist))
                processInvoices((short)userId);

        _allRefunds = new IndexedCollection<TransactionsOfTypeInDateRangeWithInvoiceSlice>(
            FaultHandler.Shield(() =>
                                    InvoiceTransactionsRepository.GetTransactionsOfTypeInDateRangeWithPrimalInvoice(
                                        BeginDate,
                                        EndDate,
                                        InvoiceTransactionType.Refund)));

        if (ExcludeCurrentDayInvoiceRefunds) {
            var filteredTxs = _allRefunds.AsIndexed().Where(tx => tx.OrderDateTime < BeginDate).ToList();
            _allRefunds = new IndexedCollection<TransactionsOfTypeInDateRangeWithInvoiceSlice>(filteredTxs);
        }

        foreach (var userId in getDistinctRefundingStaffIdList())
            if (userBelongsToRole((short)userId, RoleCodes.Receptionist))
                processRefunds((short)userId);
    }

    public decimal NonCashAmount { get; set; }

    private bool userBelongsToRole(short userId, string roleCode)
    {
        var user = _userCatalog.AsIndexed().SingleOrDefault(u => u.UserId == userId);
        if (user != null) return user.RoleCode == roleCode;
        return false;
    }

    private IEnumerable<short?> getDistinctOrderingStaffIdList() =>
        Enumerable.Select(_allInvoices.AsIndexed(), x => x.StaffId).Distinct().ToList();

    private IEnumerable<short?> getDistinctRefundingStaffIdList() =>
        Enumerable.Select(_allRefunds.AsIndexed(), x => x.StaffId).Distinct().ToList();


    private UserSummarySlice ensureUserSlice(PrimalInvoiceSlice invoice)
    {
        var slice = _userSummaries.AsIndexed().SingleOrDefault(u => u.Id == invoice.StaffId);
        if (slice == null) {
            slice = new UserSummarySlice((short)invoice.StaffId)
            {
                UserName = invoice.StaffName
            };
            _userSummaries.Add(slice);
        }

        return slice;
    }

    private UserSummarySlice ensureUserSlice(TransactionsOfTypeInDateRangeWithInvoiceSlice tx)
    {
        var slice = _userSummaries.AsIndexed().SingleOrDefault(u => u.Id == tx.StaffId);
        if (slice == null) {
            slice = new UserSummarySlice((short)tx.StaffId)
            {
                UserName = tx.StaffName
            };
            _userSummaries.Add(slice);
        }

        return slice;
    }

    private void processRefunds(short userId)
    {
        var transactions = _allRefunds.AsIndexed().Where(tx => tx.StaffId == userId).ToList();
        var dto = ensureUserSlice(transactions.First());
        foreach (var transaction in transactions) 
            dto.AddInvoiceRefund(transaction);
        dto.AdjustSummaries();

        RefundAmount += dto.RefundAmount;
    }

    private void processInvoices(short userId)
    {
        var invoices = _allInvoices.AsIndexed().Where(i => i.StaffId == userId).ToList();
        var dto = ensureUserSlice(invoices.First());
        foreach (var slice in invoices) dto.AddInvoice(slice);

        dto.AdjustSummaries();

        ReceivedAmount += dto.ReceivedAmount;
        NonCashAmount += dto.ReceivedAmount;
        DiscountAmount += dto.DiscountAmount;
        InvoicesCount += dto.InvoicesCount;
        GrossAmount += dto.GrossAmount;
        NetAmount += dto.NetAmount;
        DueAmount += dto.DueAmount;
    }

    public ReceptionistSummaryReportPrintDto ToPrintDto()
    {
        var users = _userSummaries.OrderBy(u => u.UserName).ToList();
        var dto = new ReceptionistSummaryReportPrintDto
        {
            DiscountAmount = SharedUtilities.MoneyToStringPlainCultureNonZero(DiscountAmount),
            DueAmount = SharedUtilities.MoneyToStringPlainCultureNonZero(DueAmount),
            GrossAmount = SharedUtilities.MoneyToStringPlainCultureNonZero(GrossAmount),
            InvoicesCount = SharedUtilities.IntToStringPositive(InvoicesCount),
            NetAmount = SharedUtilities.MoneyToStringPlainCultureNonZero(NetAmount),
            ReceivedAmount = SharedUtilities.MoneyToStringPlainCultureNonZero(ReceivedAmount),
            RefundAmount = SharedUtilities.MoneyToStringPlainCultureNonZero(RefundAmount),
            DateFrom = SharedUtilities.DateToString(BeginDate),
            DateTo = SharedUtilities.DateToString(EndDate),
            PrintedBy = CurrentUserContext.UserDisplayName,
            PrintedOn = SharedUtilities.DateTimeToShortString(AppSysRepository.GetServerTime())
        };

        foreach (var slice in users.OrderByDescending(x => x.InvoicesCount)) {
            dto.UserSummaries.Add(slice.ToPrintDto());
            foreach (var refundInvoice in slice.RefundInvoices.OrderBy(i => i.InvoiceId))
                dto.RefundInvoices.Add(refundInvoice.ToPrintDto());
        }

        return dto;
    }
}