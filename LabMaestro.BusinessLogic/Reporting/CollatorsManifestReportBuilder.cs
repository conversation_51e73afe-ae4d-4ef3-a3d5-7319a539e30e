﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: CollatorsManifestReportBuilder.cs 1459 2014-10-12 11:21:09Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class CollatorsManifestReportBuilder
{
    private static readonly Regex _regex = new(@"^(?<alpha>[A-Z]{1,2})(?<num>[0-9]+)$",
        RegexOptions.Compiled | RegexOptions.IgnoreCase);

    public DateTime InvoiceDateFrom { get; set; }
    public DateTime InvoiceDateTill { get; set; }
    public string OrderIdFrom { get; set; }
    public string OrderIdTo { get; set; }
    public List<CollatorInvoiceSlice> Invoices { get; } = [];
    public WorkflowStageType BundleCutoffStage { get; set; } = WorkflowStageType.Unknown;

    public void CompileReport()
    {
        sanitizeParams();
        var invoices = PatientLabOrdersRepository.SearchActiveLabOrdersByDateRange(
            InvoiceDateFrom.Date, SharedUtilities.LastMinuteOfDay(InvoiceDateTill));
        filterEligibleInvoices(invoices);
    }

    private void sanitizeParams()
    {
        if (InvoiceDateTill.Year < 2000)
            InvoiceDateTill = InvoiceDateFrom;
    }

    private void filterEligibleInvoices(IEnumerable<LabOrderSearchResultSlice> invoices)
    {
        var lwm = string.IsNullOrEmpty(OrderIdFrom) ? -1 : orderIdToIntValue(OrderIdFrom.ToUpperInvariant());
        var hwm = orderIdToIntValue(string.IsNullOrEmpty(OrderIdTo) ? "Z999" : OrderIdTo.ToUpperInvariant());

        foreach (var invoice in invoices.OrderBy(i => i.InvoiceId))
        {
            CollatorInvoiceSlice slice = null;
            // todo: optimization -
            // 1. collect eligible invoice ids
            // 2. fetch bundles for invoices
            // 3. populate the invoices
            if (lwm > 0)
            {
                if (isOrderIdInRange(invoice.OrderId, lwm, hwm))
                    slice = CollatorInvoiceSlice.AssembleFrom(invoice, BundleCutoffStage);
            }
            else
            {
                slice = CollatorInvoiceSlice.AssembleFrom(invoice, BundleCutoffStage);
            }

            if (slice != null && slice.ResultBundles.Count > 0)
                Invoices.Add(slice);
        }
    }

    private char lastChar(string data) => !string.IsNullOrEmpty(data) ? data[data.Length - 1] : char.MinValue;

    private long orderIdToIntValue(Match match) =>
        lastChar(match.Groups["alpha"].Value) * 1000 + int.Parse(match.Groups["num"].Value);

    private long orderIdToIntValue(string orderId)
    {
        var fMatch = _regex.Match(orderId);
        return fMatch.Success ? orderIdToIntValue(fMatch) : -1;
    }

    private bool isOrderIdInRange(string target, long lwm, long hwm)
    {
        var match = _regex.Match(target);
        try
        {
            if (match.Success)
            {
                var num = orderIdToIntValue(match);
                if (num >= lwm && num <= hwm) return true;
            }
        }
        catch
        {
            return false;
        }

        return false;
    }

    public CollatorsManifestReportPrintDto ToPrintDto()
    {
        var dto = new CollatorsManifestReportPrintDto
        {
            InvoiceDate = SharedUtilities.DateToString(InvoiceDateFrom),
            PrintedBy = CurrentUserContext.UserDisplayName,
            PrintedOn = SharedUtilities.DateTimeToString24(AppSysRepository.GetServerTime()),
            InvoiceDateMonth = InvoiceDateFrom.Month
        };
        dto.Invoices.AddRange(Invoices.Select(i => i.ToPrintDto()));
        return dto;
    }
}