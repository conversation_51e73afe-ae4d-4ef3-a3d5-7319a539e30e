﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2015, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id$
// 
// Last modified: 2015.08.22 12:27 PM
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using LabMaestro.Domain;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public class InvoiceFinancialInfoSlice
{
    public DateTime Period { get; set; }
    public int Count { get; set; }
    public decimal GrossPayable { get; set; }
    public decimal DiscountAmount { get; set; }
    public decimal TaxAmount { get; set; }
    public decimal SurchargeAmount { get; set; }
    public decimal NetPayable { get; set; }
    public decimal PaidAmount { get; set; }
    public decimal DueAmount { get; set; }
    public decimal RefundAmount { get; set; }

    public static InvoiceFinancialInfoSlice AssembleFrom(InvoiceSnapShotSlice src) =>
        new()
        {
            GrossPayable = src.GrossPayable,
            NetPayable = src.NetPayable,
            DiscountAmount = src.DiscountAmount,
            DueAmount = src.DueAmount,
            SurchargeAmount = src.SurchargeAmount,
            TaxAmount = src.TaxAmount,
            RefundAmount = src.RefundAmount,
            PaidAmount = src.PaidAmount
        };

    public void Reset()
    {
        GrossPayable = 0;
        NetPayable = 0;
        DiscountAmount = 0;
        DueAmount = 0;
        SurchargeAmount = 0;
        TaxAmount = 0;
        RefundAmount = 0;
        PaidAmount = 0;
        Count = 0;
    }

    public DailyIncomeSummaryPrintDto ToPrintDto(DateTime period) =>
        new()
        {
            Date = SharedUtilities.DateToStringOnlySlash(period),
            Count = SharedUtilities.IntToStringPositive(Count),
            DiscountAmount = SharedUtilities.MoneyToStringCulture(DiscountAmount),
            DueAmount = SharedUtilities.MoneyToStringCulture(DueAmount),
            GrossPayable = SharedUtilities.MoneyToStringCulture(GrossPayable),
            NetPayable = SharedUtilities.MoneyToStringCulture(NetPayable),
            PaidAmount = SharedUtilities.MoneyToStringCulture(PaidAmount),
            RefundAmount = SharedUtilities.MoneyToStringCulture(RefundAmount),
            SurchargeAmount = SharedUtilities.MoneyToStringCulture(SurchargeAmount),
            TaxAmount = SharedUtilities.MoneyToStringCulture(TaxAmount)
        };
}