﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ReferrerBusinessContributionSlice.cs 1230 2014-03-19 11:53:53Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Linq;
using AutoMapper;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class ReferrerBusinessContributionSlice
{
    private readonly GroupedReferralDetailsList _referralDetailsList = new();

    public long InvoiceId { get; set; }
    public DateTime OrderDateTime { get; set; }
    public string PatientName { get; set; }
    public string OrderId { get; set; }
    public string ReferrerName { get; set; }
    public bool IsCancelled { get; set; }
    public decimal GrossPayable { get; set; }
    public decimal DiscountAmount { get; set; }
    public decimal NetPayable { get; set; }
    public decimal PaidAmount { get; set; }
    public decimal DueAmount { get; set; }
    public bool IsExternalSubOrder { get; set; }
    public bool DisallowReferral { get; set; }
    public bool IsReferrerUnknown { get; set; }
    public decimal RefundAmount { get; set; }
    public int? ReferrerId { get; set; }

    internal static ReferrerBusinessContributionSlice AssembleFrom(LabOrderSearchResultSlice src,
                                                                   ReferralEligibleLabTestsCatalog catalog)
    {
        Mapper.CreateMap<LabOrderSearchResultSlice, ReferrerBusinessContributionSlice>();
        var dto = Mapper.Map<LabOrderSearchResultSlice, ReferrerBusinessContributionSlice>(src);
        dto.updateOrderedTestDetails(catalog);
        return dto;
    }

    internal static ReferrerBusinessContributionSlice AssembleFrom(LabOrderSearchResultSliceEx src,
                                                                   ReferralEligibleLabTestsCatalog catalog)
    {
        Mapper.CreateMap<LabOrderSearchResultSliceEx, ReferrerBusinessContributionSlice>();
        var dto = Mapper.Map<LabOrderSearchResultSliceEx, ReferrerBusinessContributionSlice>(src);
        dto.updateOrderedTestDetails(catalog);
        return dto;
    }

    private void updateOrderedTestDetails(ReferralEligibleLabTestsCatalog catalog)
    {
        var orderedTests = FaultHandler.Shield(
            () => OrderedTestsRepository.GetReferralEligibleOrderedTestIds(InvoiceId));
        foreach (var test in orderedTests) {
            string groupName, refModeLabel;
            decimal listPrice;
            var grossReferral = catalog.CalculateGrossReferralAmount((short)test,
                                                                     out listPrice,
                                                                     out groupName,
                                                                     out refModeLabel);
            _referralDetailsList.AddReferralDetail(groupName, grossReferral, listPrice, 1, refModeLabel);
        }
    }

    public InvoiceReferralMasterPrintDto ToPrintDto(bool showRefDetails)
    {
        var dto = new InvoiceReferralMasterPrintDto
        {
            Discount = SharedUtilities.MoneyToStringPlainCultureNonZero(DiscountAmount),
            Due = SharedUtilities.MoneyToStringPlainCultureNonZero(DueAmount),
            GrossPayable = SharedUtilities.MoneyToStringPlainCultureNonZero(GrossPayable),
            NetPayable = SharedUtilities.MoneyToStringPlainCultureNonZero(NetPayable),
            InvoiceId = InvoiceId.ToString(),
            OrderId = OrderId,
            OrderDateTime = SharedUtilities.DateToStringOnlySlash(OrderDateTime),
            Paid = SharedUtilities.MoneyToStringPlainCultureNonZero(PaidAmount),
            Name = PatientName
        };

        if (showRefDetails) {
            var numTests = 0;
            foreach (var detail in _referralDetailsList.ReferralDetails
                                                       .OrderByDescending(d => d.NumTests)
                                                       .ThenBy(d => d.GroupName)) {
                numTests += detail.NumTests;
                dto.ReferralDetails.Add(new InvoiceReferralDetailPrintDto
                {
                    BillAmount =
                        SharedUtilities.MoneyToStringPlainNonZero(detail.PriceTotal),
                    NumTests = SharedUtilities.IntToStringPositive(detail.NumTests),
                    Name = detail.GroupName
                });
            }

            dto.NumTests = SharedUtilities.IntToStringPositive(numTests);
        }

        return dto;
    }
}