﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: CollatorResultBundleSlice.cs 1170 2014-02-15 18:13:29Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using LabMaestro.Domain;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class CollatorResultBundleSlice
{
    public CollatorResultBundleSlice(ResultBundlesForInvoice bundle)
    {
        ComponentLabTests = new List<string>();
        BundleId = bundle.Id;
        DisplayTitle = bundle.DisplayTitle;
        WorkflowStage = (WorkflowStageType)bundle.WorkflowStage;
        LastUpdated = bundle.LastUpdated;
        populateComponentTests();
    }

    public List<string> ComponentLabTests { get; }
    public long BundleId { get; set; }
    public string DisplayTitle { get; set; }
    public WorkflowStageType WorkflowStage { get; set; }
    public DateTime LastUpdated { get; set; }
    public string Staff { get; set; }

    private void populateComponentTests()
    {
        var tests = OrderedTestsRepository.GetActiveTestsInResultBundle(BundleId);
        foreach (var test in tests) ComponentLabTests.Add(test.TestName);

        var trails = AuditTrailRepository.GetAuditRecordsForResultBundle(BundleId, AuditEventCategory.Workflow);
        if (trails != null)
        {
            if (trails.Count == 1)
                Staff = trails[0].PerformingUserName;
            else if (trails.Count > 1)
                Staff = trails.OrderByDescending(t => t.WorkflowStage).First().PerformingUserName;
        }
    }

    public CollatorResultBundlePrintDto ToPrintDto()
    {
        var dto = new CollatorResultBundlePrintDto
        {
            BundleId = BundleId,
            DisplayTitle = DisplayTitle,
            LastUpdated = SharedUtilities.DateTimeToShortString(LastUpdated),
            Staff = Staff
        };
        dto.ComponentLabTests.AddRange(ComponentLabTests);
        switch (WorkflowStage)
        {
            case WorkflowStageType.OrderEntry:
                dto.WorkflowStage = "Pending Results Entry";
                break;
            case WorkflowStageType.RepeatProcedure:
                dto.WorkflowStage = "Repeat Procedure";
                break;
            case WorkflowStageType.ReportCollation:
                dto.WorkflowStage = "Collated";
                break;
            case WorkflowStageType.RemoteDispatch:
            case WorkflowStageType.ReportDispatch:
                dto.WorkflowStage = "Dispatched";
                break;
            case WorkflowStageType.ResultEntry:
                dto.WorkflowStage = "Resulted";
                break;
            case WorkflowStageType.ResultValidation:
                dto.WorkflowStage = "Validated";
                break;
            case WorkflowStageType.ReportFinalization:
                dto.WorkflowStage = "Finalized";
                break;
            case WorkflowStageType.OrderFulfillment:
                dto.WorkflowStage = "Fulfilled";
                break;
        }

        return dto;
    }
}