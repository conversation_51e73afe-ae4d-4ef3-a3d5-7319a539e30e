﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ReceivablesSummaryReportBuilder.cs 1190 2014-03-04 16:31:49Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using C1.LiveLinq;
using C1.LiveLinq.Collections;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class ReceivablesSummaryReportBuilder
{
    private const string RECEPTIONIST_ROLENAME = @"Receptionist";
    private readonly List<UserReceivablesSummaryDto> _roleReceivablesSummaryList;
    private readonly List<UserReceivablesSummaryDto> _userReceivablesSummaryList = new();
    private IndexedCollection<TransactionsForUserDateRangeSlice> _transactions;
    public short UserId { get; set; }
    public short RoleId { get; set; }
    public DateTime BeginDate { get; set; }
    public DateTime EndDate { get; set; }

    public List<UserReceivablesSummaryDto> UserReceivablesSummaryList =>
        _userReceivablesSummaryList
            .OrderBy(x => x.RoleName)
            .ThenBy(x => x.UserName)
            .ToList();

    public void SetSingleDateRange(DateTime date)
    {
        BeginDate = date.Date;
        EndDate = BeginDate.AddDays(1).AddMinutes(-1);
    }

    public void CompileAllTransactionsReport()
    {
        _transactions = new IndexedCollection<TransactionsForUserDateRangeSlice>(
            FaultHandler.Shield(() => InvoiceTransactionsRepository.GetAllTransactionsForDateRange(
                BeginDate, EndDate)));

        processTransactions();
    }

    public void CompileUserTransactionsReport()
    {
        _transactions = new IndexedCollection<TransactionsForUserDateRangeSlice>(
            FaultHandler.Shield(() => InvoiceTransactionsRepository.GetAllTransactionsForUserDateRange(
                UserId,
                BeginDate,
                EndDate)));

        processTransactions();
    }

    public void CompileRoleTransactionsReport()
    {
        _transactions = new IndexedCollection<TransactionsForUserDateRangeSlice>(
            FaultHandler.Shield(() => InvoiceTransactionsRepository.GetAllTransactionsForRoleDateRange(
                RoleId,
                BeginDate,
                EndDate)));

        processTransactions();
    }

    private void processTransactions()
    {
        _userReceivablesSummaryList.Clear();
        foreach (var userId in getDistinctUserIdList()) processUserId((short)userId);
    }

    private void processUserId(short userId)
    {
        var txPayments = _transactions
            .AsIndexed()
            .Where(x => x.PerformingUserId == userId && x.TxType == (byte)InvoiceTransactionType.Payment);
        var received = txPayments.Sum(x => x.TxAmount);

        var refTransactions = getUserTransactions(userId, InvoiceTransactionType.Refund);
        var refunded = refTransactions.Sum(x => x.TxAmount);
        var refundedCount = refTransactions.Count();
        var refundedInvoices =
            refTransactions.Select(x => string.Format("{0} ({1})", x.InvoiceId, x.OrderId)).Distinct();

        var discTransactions = getUserTransactions(userId, InvoiceTransactionType.CashDiscount);
        var discounted = discTransactions.Sum(x => x.TxAmount);
        var discountedCount = discTransactions.Count();
        var discountedInvoices = discTransactions
            .Select(x => string.Format("{0} ({1})", x.InvoiceId, x.OrderId))
            .Distinct();

        var rebTransactions = getUserTransactions(userId, InvoiceTransactionType.DiscountRebate);
        var rebated = rebTransactions.Sum(x => x.TxAmount);
        var rebatedInvoices = rebTransactions
            .Select(x => string.Format("{0} ({1})", x.InvoiceId, x.OrderId))
            .Distinct();

        var invoicesCount = _transactions
            .AsIndexed()
            .Where(x => x.PerformingUserId == userId)
            .Select(x => x.InvoiceId)
            .Distinct()
            .Count();

        var grossPayable = 0m;
        var netPayable = 0m;
        var dueAmount = 0m;

        var item = _transactions.AsIndexed().FirstOrDefault(x => x.PerformingUserId == userId);

        // only calculate gross/net bills for receptionists
        if (item.RoleName == RECEPTIONIST_ROLENAME)
        {
            //TODO: What if no payment was made (i.e. a complementary lab order)?
            var uniqueInvoices = txPayments
                .Where(x => x.TxFlag == (byte)TransactionFlag.InitialOperation)
                .Select(x => x.InvoiceId)
                .Distinct()
                .ToList();
            invoicesCount = uniqueInvoices.Count();

            foreach (var invoiceId in uniqueInvoices)
            {
                var invoice = _transactions.AsIndexed().First(x => x.InvoiceId == invoiceId);
                grossPayable += invoice.GrossPayable;
                netPayable += invoice.NetPayable;
                dueAmount += invoice.DueAmount;
            }
        }

        var dto = new UserReceivablesSummaryDto
        {
            UserName = item.UserName,
            RoleName = item.RoleName,
            ReceiveAmount = received,
            DiscountAmount = discounted,
            DiscountRebateAmount = rebated,
            RefundAmount = refunded,
            NetBalanceAmount = received - refunded,
            InvoicesCount = invoicesCount,
            RefundedInvoicesCount = refundedCount,
            DiscountedInvoicesCount = discountedCount,
            RefundedInvoices = string.Join(",", refundedInvoices),
            DiscountedInvoices = string.Join(",", discountedInvoices),
            DiscountRebatedInvoices = string.Join(",", rebatedInvoices),
            GrossPayableAmount = grossPayable,
            NetPayableAmount = netPayable,
            DueAmount = dueAmount
        };
        _userReceivablesSummaryList.Add(dto);
    }

    private List<TransactionsForUserDateRangeSlice> getUserTransactions(short userId, InvoiceTransactionType txType)
    {
        return _transactions
            .AsIndexed()
            .Where(x => x.PerformingUserId == userId && x.TxType == (byte)txType)
            .OrderBy(x => x.InvoiceId)
            .ToList();
    }

    private IEnumerable<short?> getDistinctUserIdList() =>
        _transactions.AsIndexed().Select(x => x.PerformingUserId).Distinct().ToList();

    public ReceivablesSummaryReportPrintDto GetReceivablesSummaryReportPrintDto(string printedBy)
    {
        var dto = new ReceivablesSummaryReportPrintDto
        {
            BeginDate = SharedUtilities.DateToString(BeginDate),
            EndDate = SharedUtilities.DateToString(EndDate),
            UsersCount = UserReceivablesSummaryList.Count,
            PrintedOn = SharedUtilities.DateTimeToString(AppSysRepository.GetServerTime()),
            PrintedBy = printedBy
        };
        dto.UserReceivablesSummaryList.AddRange(
            _userReceivablesSummaryList
                .OrderBy(x => x.RoleName)
                .ThenBy(x => x.UserName));
        foreach (var userId in getDistinctUserIdList())
        {
            addRefundDiscountDetails((short)userId, InvoiceTransactionType.Refund, dto);
            addRefundDiscountDetails((short)userId, InvoiceTransactionType.CashDiscount, dto);
        }

        dto.SummaryTotal.InvoicesCount = _userReceivablesSummaryList.Sum(x => x.InvoicesCount);
        dto.SummaryTotal.RefundedInvoicesCount = _userReceivablesSummaryList.Sum(x => x.RefundedInvoicesCount);
        dto.SummaryTotal.DiscountedInvoicesCount = _userReceivablesSummaryList.Sum(x => x.DiscountedInvoicesCount);
        dto.SummaryTotal.ReceiveAmount = _userReceivablesSummaryList.Sum(x => x.ReceiveAmount);
        dto.SummaryTotal.RefundAmount = _userReceivablesSummaryList.Sum(x => x.RefundAmount);
        dto.SummaryTotal.DiscountAmount = _userReceivablesSummaryList.Sum(x => x.DiscountAmount);
        dto.SummaryTotal.DiscountRebateAmount = _userReceivablesSummaryList.Sum(x => x.DiscountRebateAmount);
        dto.SummaryTotal.NetBalanceAmount = _userReceivablesSummaryList.Sum(x => x.NetBalanceAmount);

        //var receptionists = _userReceivablesSummaryList.Where(x => x.RoleName == RECEPTIONIST_ROLENAME).ToList();
        var receptionists = _userReceivablesSummaryList;
        dto.SummaryTotal.GrossPayableAmount = receptionists.Sum(x => x.GrossPayableAmount);
        dto.SummaryTotal.NetPayableAmount = receptionists.Sum(x => x.NetPayableAmount);
        dto.SummaryTotal.DueAmount = receptionists.Sum(x => x.DueAmount);

        return dto;
    }

    private void addRefundDiscountDetails(short userId,
        InvoiceTransactionType txType,
        ReceivablesSummaryReportPrintDto dto)
    {
        var transactions = getUserTransactions(userId, txType);
        if (transactions.Count <= 0) return;
        var userName = transactions.First().UserName;
        var count = 0;
        foreach (var item in transactions)
            switch (txType)
            {
                case InvoiceTransactionType.CashDiscount:
                    // show user name every 10 line-items
                    var theUser = count++ % 10 == 0 ? userName : string.Empty;
                    dto.RefundDiscountDetails.AddDiscount(theUser, item);
                    break;
                case InvoiceTransactionType.Refund:
                    // for refund transactions display the username anyway
                    dto.RefundDiscountDetails.AddRefund(userName, item);
                    break;
            }
    }
}