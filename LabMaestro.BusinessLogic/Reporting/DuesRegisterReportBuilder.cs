﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: DuesRegisterReportBuilder.cs 1203 2014-03-07 06:41:01Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using C1.LiveLinq;
using C1.LiveLinq.Collections;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class DuesRegisterReportBuilder
{
    private readonly Dictionary<int, bool> _referrerEligibilityCache = new();
    private readonly IndexedCollection<InvoiceOutstandingDueCollectionSlice> _unpaidInvoices = new();
    public short ReferrerCategory { get; set; } = -1;
    public DateTime InvoiceDate { get; set; }

    public List<InvoiceOutstandingDueCollectionSlice> UnpaidInvoices => _unpaidInvoices.ToList();

    private InvoiceOutstandingDueCollectionSlice ensureSlice(LabOrderSearchResultSlice src)
    {
        var invoice = _unpaidInvoices.AsIndexed().SingleOrDefault(i => i.InvoiceId == src.InvoiceId);
        if (invoice == null)
        {
            invoice = InvoiceOutstandingDueCollectionSlice.AssembleFrom(src);
            _unpaidInvoices.Add(invoice);
        }

        return invoice;
    }

    private InvoiceOutstandingDueCollectionSlice ensureSlice(PrimalInvoiceSlice src)
    {
        var invoice = _unpaidInvoices.AsIndexed().SingleOrDefault(i => i.InvoiceId == src.InvoiceId);
        if (invoice == null)
        {
            invoice = InvoiceOutstandingDueCollectionSlice.AssembleFrom(src);
            _unpaidInvoices.Add(invoice);
        }

        return invoice;
    }

    private InvoiceOutstandingDueCollectionSlice ensureSlice(PrimalInvoiceSlice primal,
        InvoiceFinancialDetailsSlice updated)
    {
        var invoice = _unpaidInvoices.AsIndexed().SingleOrDefault(i => i.InvoiceId == primal.InvoiceId);
        if (invoice == null)
        {
            invoice = InvoiceOutstandingDueCollectionSlice.AssembleFrom(primal);
            invoice.GrossPayable = updated.GrossPayable;
            invoice.NetPayable = updated.NetPayable;
            invoice.DueAmount = invoice.NetPayable - primal.PaidAmount;
            _unpaidInvoices.Add(invoice);
        }

        return invoice;
    }

    public void CompileUpdatedReport()
    {
        var invoices = PatientLabOrdersRepository.SearchActiveLabOrdersByDateRange(InvoiceDate.Date,
            SharedUtilities.LastMinuteOfDay(
                InvoiceDate));
        foreach (var unpaid in invoices.Where(i => i.DueAmount > 0))
        {
            var transactions = InvoiceTransactionsRepository.FindInvoiceTransactionsDetailedEx(unpaid.InvoiceId);
            var payments = transactions.Where(t =>
                    t.TxType == (byte)InvoiceTransactionType.Payment &&
                    t.TxFlag == (byte)TransactionFlag.None)
                .ToList();
            foreach (var tx in payments)
                if (tx.TxTime.Date == InvoiceDate.Date)
                {
                    if (tx.TxAmount >= unpaid.DueAmount)
                    {
                        // fully paid invoice, so skip it
                    }
                    else
                    {
                        var slice = ensureSlice(unpaid);
                        slice.Transactions.Add(InvoiceTransactionInfoSlice.AssembleFrom(tx));
                    }
                }
                else
                {
                    var slice = ensureSlice(unpaid);
                    slice.Transactions.Add(InvoiceTransactionInfoSlice.AssembleFrom(tx));
                }
        }
    }

    private bool referrerBelongsToCategory(int referrerId)
    {
        if (_referrerEligibilityCache.Count == 0)
        {
            var referrers = ReferrersRepository.GetAllReferrersInCategory(ReferrerCategory);
            foreach (var slice in referrers.Where(x => x.IsActive)) _referrerEligibilityCache.Add(slice.Id, true);
        }

        return _referrerEligibilityCache.ContainsKey(referrerId) && _referrerEligibilityCache[referrerId];
    }

    private bool checkInvoiceEligibility(PrimalInvoiceSlice invoice)
    {
        if (invoice.DueAmount == 0) return false;
        if (ReferrerCategory > 0)
            return !invoice.IsReferrerUnknown && invoice.ReferrerId != null &&
                   referrerBelongsToCategory((int)invoice.ReferrerId);
        return true;
    }

    public void CompilePrimalReport()
    {
        var invoices = FaultHandler.Shield(() =>
            InvoiceMasterRepository.GetPrimalInvoicesByDateRange(
                InvoiceDate.Date,
                SharedUtilities.LastMinuteOfDay(InvoiceDate)));
        foreach (var unpaid in invoices.Where(checkInvoiceEligibility))
        {
            var transactions = FaultHandler.Shield(
                () => InvoiceTransactionsRepository.FindInvoiceTransactionsDetailed(unpaid.InvoiceId));
            var updatedInvoice = FaultHandler.Shield(
                () => InvoiceMasterRepository.GetInvoiceFinancialDetails(unpaid.InvoiceId));

            var payments = transactions.Where(t =>
                    t.TxType == (byte)InvoiceTransactionType.Payment &&
                    t.TxFlag == (byte)TransactionFlag.None)
                .ToList();
            if (payments.Count > 0)
            {
                foreach (var tx in payments)
                    if (tx.TxTime.Date == InvoiceDate.Date)
                    {
                        if (tx.TxAmount >= unpaid.DueAmount)
                        {
                            // fully paid invoice, so skip it
                        }
                        else
                        {
                            //var slice = ensureSlice(unpaid, updatedInvoice);
                            var slice = ensureSlice(unpaid);
                            slice.TotalDiscount = updatedInvoice.DiscountAmount;
                            slice.Transactions.Add(InvoiceTransactionInfoSlice.AssembleFrom(tx));
                        }
                    }
                    else
                    {
                        //var slice = ensureSlice(unpaid, updatedInvoice);
                        var slice = ensureSlice(unpaid);
                        slice.TotalDiscount = updatedInvoice.DiscountAmount;
                        slice.Transactions.Add(InvoiceTransactionInfoSlice.AssembleFrom(tx));
                    }
            }
            else
            {
                //var slice = ensureSlice(unpaid, updatedInvoice);
                var slice = ensureSlice(unpaid);
                slice.TotalDiscount = updatedInvoice.DiscountAmount;
            }
        }
    }

    public DueRegisterReportPrintDto ToPrintDto()
    {
        var dto = new DueRegisterReportPrintDto
        {
            InvoiceDate = SharedUtilities.DateToString(InvoiceDate),
            PrintedBy = CurrentUserContext.UserDisplayName,
            PrintedOn = SharedUtilities.DateTimeToStringDMHM(AppSysRepository.GetServerTime())
        };

        foreach (var slice in _unpaidInvoices) dto.DueCollections.Add(slice.ToPrintDto());
        return dto;
    }
}