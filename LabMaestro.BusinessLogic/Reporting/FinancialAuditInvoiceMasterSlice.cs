﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: FinancialAuditInvoiceMasterSlice.cs 995 2013-10-12 11:59:37Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using LabMaestro.Domain;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

internal sealed class FinancialAuditInvoiceMasterSlice
{
    internal long InvoiceId { get; set; }
    internal string OrderId { get; set; }
    internal DateTime OrderDateTime { get; set; }
    internal bool IsCancelled { get; set; }
    internal long ReferrerId { get; set; }
    internal string ReferredBy { get; set; }
    internal decimal GrossPayable { get; set; }
    internal decimal NetPayable { get; set; }
    internal decimal Paid { get; set; }
    internal decimal Due { get; set; }
    internal decimal Discount { get; set; }
    internal decimal Refund { get; set; }
    internal List<FinancialAuditInvoiceTransactionSlice> Transactions { get; } = [];

    internal static FinancialAuditInvoiceMasterSlice AssembleFrom(TransactionInDateRangeSlice slice)
    {
        var dto = new FinancialAuditInvoiceMasterSlice
        {
            InvoiceId = slice.InvoiceId,
            OrderId = slice.OrderId,
            OrderDateTime = slice.OrderDateTime,
            IsCancelled = slice.InvoiceIsCancelled,
            ReferredBy = slice.ReferredBy,
            GrossPayable = slice.InvoiceGross,
            NetPayable = slice.InvoicePayable,
            Paid = slice.InvoicePaid,
            Due = slice.InvoiceDue,
            Discount = slice.InvoiceDiscount,
            Refund = slice.InvoiceRefund,
            ReferrerId = slice.ReferrerId != null ? (long)slice.ReferrerId : 0
        };
        dto.AddTransaction(slice);
        return dto;
    }

    internal void AddTransaction(TransactionInDateRangeSlice slice) =>
        Transactions.Add(FinancialAuditInvoiceTransactionSlice.AssembleFrom(slice));

    internal FinancialAuditInvoiceMasterPrintDto ToPrintDto()
    {
        var dto = new FinancialAuditInvoiceMasterPrintDto
        {
            InvoiceId = InvoiceId.ToString(),
            OrderId = OrderId,
            OrderDateTime = SharedUtilities.DateTimeToShortString(OrderDateTime),
            ReferredBy = ReferredBy,
            ReferrerId = ReferrerId.ToString(),
            GrossPayable = SharedUtilities.MoneyToStringPlainNonZero(GrossPayable),
            NetPayable = SharedUtilities.MoneyToStringPlainNonZero(NetPayable),
            Paid = SharedUtilities.MoneyToStringPlainNonZero(Paid),
            Due = SharedUtilities.MoneyToStringPlainNonZero(Due),
            Discount = SharedUtilities.MoneyToStringPlainNonZero(Discount),
            Refund = SharedUtilities.MoneyToStringPlainNonZero(Refund)
        };

        foreach (var transaction in Transactions.OrderBy(x => x.TxTime).ToList())
            dto.Transactions.Add(transaction.ToPrintDto());

        return dto;
    }
}