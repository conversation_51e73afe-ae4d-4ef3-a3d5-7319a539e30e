﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: UserSummarySlice.cs 1165 2014-02-14 16:25:24Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Linq;
using C1.LiveLinq;
using C1.LiveLinq.Collections;
using LabMaestro.Domain;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class UserSummarySlice(short id)
{
    public short Id { get; private set; } = id;
    public string UserName { get; set; }
    public string RoleName { get; set; }
    public int InvoicesCount { get; set; }
    public decimal GrossAmount { get; set; }
    public decimal DiscountAmount { get; set; }
    public decimal NetAmount { get; set; }
    public decimal ReceivedAmount { get; set; }
    public decimal NonCashAmount { get; set; }
    public decimal RefundAmount { get; set; }
    public decimal DueAmount { get; set; }

    internal IndexedCollection<ReceptionistInvoiceSlice> PaidInvoices { get; } = [];
    internal IndexedCollection<ReceptionistInvoiceSlice> RefundInvoices { get; } = [];

    public void AddInvoice(InvoiceMaster invoice) => ensurePaidInvoiceSlice(invoice);

    public void AddInvoice(PrimalInvoiceSlice invoice) => ensurePaidInvoiceSlice(invoice);

    public void AddInvoicePayment(FilteredInvoiceSlice src)
    {
        var invoice = ensurePaidInvoiceSlice(src);
        //invoice.Paid += amount;
        //invoice.Due = invoice.NetPayable - invoice.Paid;
    }

    public void AddInvoiceRefund(InvoiceMaster invoice, decimal amount, DateTime txTime, string remarks)
    {
        addRefundInvoiceSlice(invoice, amount, txTime, remarks);
        //invoice.Refund += amount;
        //invoice.Paid -= amount;
        //invoice.Due = invoice.NetPayable - invoice.Paid;
    }

    public void AddInvoiceRefund(TransactionsOfTypeInDateRangeWithInvoiceSlice slice)
    {
        var invoice = new ReceptionistInvoiceSlice
        {
            InvoiceId = slice.InvoiceId,
            OrderId = slice.OrderId,
            OrderDateTime = slice.OrderDateTime,
            OrderingUserId = slice.StaffId ?? 0,
            OrderingUserName = slice.StaffName,
            GrossPayable = slice.GrossPayable,
            Discount = slice.DiscountAmount,
            NetPayable = slice.NetPayable,
            Due = slice.DueAmount,
            Paid = slice.PaidAmount,
            Refund = slice.TxAmount,
            Remarks = slice.UserRemarks,
            PerformingUser = slice.StaffName,
            TxTime = slice.TxTime,
            NonCashAmount = 0
        };

        // todo: update non-cash
        if (slice.TxAmount > 0) {
            
        }

        RefundInvoices.Add(invoice);
    }

    private ReceptionistInvoiceSlice ensurePaidInvoiceSlice(FilteredInvoiceSlice src)
    {
        var invoice = PaidInvoices.AsIndexed().FirstOrDefault(ui => ui.InvoiceId == src.InvoiceId);
        if (invoice == null) {
            invoice = new ReceptionistInvoiceSlice
            {
                InvoiceId = src.InvoiceId,
                GrossPayable = src.CurrentInvoiceSnapshot.GrossPayable,
                Discount = src.CurrentInvoiceSnapshot.DiscountAmount,
                NetPayable = src.CurrentInvoiceSnapshot.NetPayable,
                Due = src.CurrentInvoiceSnapshot.DueAmount,
                Paid = src.CurrentInvoiceSnapshot.PaidAmount,
                Refund = src.CurrentInvoiceSnapshot.RefundAmount
            };
            PaidInvoices.Add(invoice);
        }

        return invoice;
    }

    public void AdjustSummaries()
    {
        GrossAmount = 0;
        NetAmount = 0;
        DueAmount = 0;
        RefundAmount = 0;
        InvoicesCount = 0;
        ReceivedAmount = 0;
        NonCashAmount = 0;
        DiscountAmount = 0;

        foreach (var slice in PaidInvoices) {
            InvoicesCount += 1;
            GrossAmount += slice.GrossPayable;
            NetAmount += slice.NetPayable;
            DiscountAmount += slice.Discount;
            DueAmount += slice.Due;
            ReceivedAmount += slice.Paid;
        }

        foreach (var slice in RefundInvoices) RefundAmount += slice.Refund;
    }

    private ReceptionistInvoiceSlice ensurePaidInvoiceSlice(InvoiceMaster src)
    {
        var invoice = PaidInvoices.AsIndexed().FirstOrDefault(ui => ui.InvoiceId == src.InvoiceId);
        if (invoice == null) {
            invoice = new ReceptionistInvoiceSlice
            {
                InvoiceId = src.InvoiceId,
                OrderId = string.Empty,
                GrossPayable = src.GrossPayable,
                Discount = src.DiscountAmount,
                NetPayable = src.NetPayable,
                Due = src.DueAmount,
                Paid = src.PaidAmount,
                Refund = src.RefundAmount
            };
            PaidInvoices.Add(invoice);
        }

        return invoice;
    }

    private ReceptionistInvoiceSlice ensurePaidInvoiceSlice(PrimalInvoiceSlice src)
    {
        var invoice = PaidInvoices.AsIndexed().FirstOrDefault(ui => ui.InvoiceId == src.InvoiceId);
        if (invoice == null) {
            invoice = new ReceptionistInvoiceSlice
            {
                InvoiceId = src.InvoiceId,
                OrderId = src.OrderId,
                OrderDateTime = src.OrderDateTime,
                OrderingUserId = src.StaffId ?? 0,
                OrderingUserName = src.StaffName,
                GrossPayable = src.GrossPayable,
                Discount = src.DiscountAmount,
                NetPayable = src.NetPayable,
                Due = src.DueAmount,
                Paid = src.PaidAmount,
                Refund = src.RefundAmount
            };
            PaidInvoices.Add(invoice);
        }

        return invoice;
    }

    private ReceptionistInvoiceSlice addRefundInvoiceSlice(InvoiceMaster src, decimal amount, DateTime txTime,
                                                           string remarks)
    {
        //var remarks = string.Empty;
        //var transactions = InvoiceTransactionsRepository.FindInvoiceTransactionsDetailed(src.InvoiceId);
        //var txTime = DateTime.MinValue;
        //foreach (var info in transactions)
        //{
        //    if (info.TxType == (byte) InvoiceTransactionType.Refund)
        //    {
        //        remarks = info.UserRemarks;
        //        txTime = info.TxTime;
        //        break;
        //    }
        //}

        var invoice = new ReceptionistInvoiceSlice
        {
            InvoiceId = src.InvoiceId,
            OrderId = string.Empty,
            GrossPayable = src.GrossPayable,
            Discount = src.DiscountAmount,
            NetPayable = src.NetPayable,
            Due = src.DueAmount,
            Paid = src.PaidAmount,
            Refund = amount,
            Remarks = remarks,
            PerformingUser = UserName,
            TxTime = txTime
        };
        RefundInvoices.Add(invoice);

        return invoice;
    }

    public UserSummaryPrintDto ToPrintDto()
    {
        var dto = new UserSummaryPrintDto
        {
            UserName = UserName,
            RoleName = RoleName,
            DiscountAmount = SharedUtilities.MoneyToStringPlainCultureNonZero(DiscountAmount),
            DueAmount = SharedUtilities.MoneyToStringPlainCultureNonZero(DueAmount),
            GrossAmount = SharedUtilities.MoneyToStringPlainCultureNonZero(GrossAmount),
            InvoicesCount = SharedUtilities.IntToStringPositive(InvoicesCount),
            NetAmount = SharedUtilities.MoneyToStringPlainCultureNonZero(NetAmount),
            ReceivedAmount = SharedUtilities.MoneyToStringPlainCultureNonZero(ReceivedAmount),
            RefundAmount = SharedUtilities.MoneyToStringPlainCultureNonZero(RefundAmount)
        };

        foreach (var slice in RefundInvoices.OrderBy(i => i.InvoiceId)) dto.RefundInvoices.Add(slice.ToPrintDto());
        return dto;
    }
}

internal class ReceptionistInvoiceSlice
{
    public long InvoiceId { get; set; }
    public string OrderId { get; set; }
    public DateTime OrderDateTime { get; set; }
    public short OrderingUserId { get; set; }
    public string OrderingUserName { get; set; }
    public decimal GrossPayable { get; set; }
    public decimal Discount { get; set; }
    public decimal NetPayable { get; set; }
    public decimal Paid { get; set; }
    public decimal NonCashAmount { get; set; }
    public decimal Due { get; set; }
    public decimal Refund { get; set; }
    public string Remarks { get; set; }
    public DateTime TxTime { get; set; }
    public string PerformingUser { get; set; }

    public ReceptionistInvoicePrintDto ToPrintDto()
    {
        return new ReceptionistInvoicePrintDto
        {
            InvoiceId = InvoiceId.ToString(),
            GrossPayable = SharedUtilities.MoneyToStringPlainNonZero(GrossPayable),
            Discount = SharedUtilities.MoneyToStringPlainNonZero(Discount),
            NetPayable = SharedUtilities.MoneyToStringPlainNonZero(NetPayable),
            Paid = SharedUtilities.MoneyToStringPlainNonZero(Paid),
            NonCashAmount = SharedUtilities.MoneyToStringPlainNonZero(NonCashAmount),
            Due = SharedUtilities.MoneyToStringPlainNonZero(Due),
            Refund = SharedUtilities.MoneyToStringPlainNonZero(Refund),
            Remarks = Remarks,
            PerformingUser = PerformingUser,
            TxTime = SharedUtilities.TimeToString(TxTime)
        };
    }
}