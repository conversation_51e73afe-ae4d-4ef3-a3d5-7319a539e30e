﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2015, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id$
// 
// Last modified: 2015.08.21 1:20 PM
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using LabMaestro.Domain;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class DiscountOverageInfoSlice
{
    internal long InvoiceId { get; set; }
    internal string OrderId { get; set; }
    internal string PatientName { get; set; }
    internal string ReferrerName { get; set; }
    internal string TxRemark { get; set; }
    internal DateTime OrderDateTime { get; set; }
    internal decimal GrossPayable { get; set; }
    internal decimal NetPayable { get; set; }
    internal decimal Paid { get; set; }
    internal decimal Due { get; set; }
    internal decimal Discount { get; set; }
    internal decimal Refund { get; set; }
    internal float DiscountPercentage { get; set; }

    public DiscountOverageInfoSlice CalculateRatio()
    {
        if (Discount <= 0)
            DiscountPercentage = 0;
        else if (Discount >= GrossPayable)
            DiscountPercentage = 100;
        else
            DiscountPercentage = (float)(Discount * 100 / GrossPayable);
        return this;
    }

    internal static DiscountOverageInfoSlice AssembleFrom(FilteredInvoiceSlice src)
    {
        return new DiscountOverageInfoSlice
        {
            InvoiceId = src.InvoiceId,
            OrderId = src.OrderId,
            OrderDateTime = src.OrderDateTime,
            PatientName = src.PatientName,
            ReferrerName = src.ReferrerName,
            GrossPayable = src.CurrentInvoiceSnapshot.GrossPayable,
            NetPayable = src.CurrentInvoiceSnapshot.NetPayable,
            Paid = src.CurrentInvoiceSnapshot.PaidAmount,
            Due = src.CurrentInvoiceSnapshot.DueAmount,
            Discount = src.CurrentInvoiceSnapshot.DiscountAmount,
            Refund = src.CurrentInvoiceSnapshot.RefundAmount
        }.CalculateRatio();
    }

    internal DiscountOverageInvoicePrintDto ToPrintDto()
    {
        return new DiscountOverageInvoicePrintDto
        {
            InvoiceId = InvoiceId.ToString(),
            OrderId = OrderId,
            OrderDateTime = SharedUtilities.DateToStringOnlyDot(OrderDateTime),
            PatientName = PatientName,
            ReferrerName = ReferrerName,
            GrossPayable = SharedUtilities.MoneyToStringCulture(GrossPayable),
            NetPayable = SharedUtilities.MoneyToStringCulture(NetPayable),
            Paid = SharedUtilities.MoneyToStringCulture(Paid),
            Due = SharedUtilities.MoneyToStringCulture(Due),
            Discount = SharedUtilities.MoneyToStringCulture(Discount),
            Refund = SharedUtilities.MoneyToStringCulture(Refund),
            DiscountPercentage = SharedUtilities.DoubleToStringSingleFractionPlain(DiscountPercentage)
        };
    }
}