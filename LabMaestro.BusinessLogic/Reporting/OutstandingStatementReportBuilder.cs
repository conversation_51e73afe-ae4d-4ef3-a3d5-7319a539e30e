﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: OutstandingStatementReportBuilder.cs 1368 2014-06-12 08:41:59Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public class OutstandingStatementReportBuilder
{
    public OutstandingStatementReportBuilder()
    {
        LabIds = [];
        ReferrerCategory = -1;
        resetItems();
    }

    public List<short> LabIds { get; }

    public List<FilteredInvoiceSlice> Invoices { get; private set; }

    public DateTime BeginDate { get; set; }

    public DateTime EndDate { get; set; }

    public FilterMode LabsFilterMode { get; set; }

    public int DiscountedInvoicesCount { get; private set; }

    public int RefundedInvoicesCount { get; private set; }

    public int ReferredInvoicesCount { get; private set; }

    public decimal ReferredInvoicesValue { get; private set; }

    public double ReferredInvoicesPercent { get; private set; }

    public int UnreferredInvoicesCount { get; private set; }

    public decimal UnreferredInvoicesValue { get; private set; }

    public double UnreferredInvoicesPercent { get; private set; }

    public short ReferrerCategory { get; set; }

    public InvoiceSnapShotSlice InvoiceSnapshotsSummary { get; private set; }
    public int PaidUpInvoicesCount { get; set; }
    public double PaidUpInvoicesPercent { get; set; }

    private void resetItems()
    {
        Invoices = new List<FilteredInvoiceSlice>();
        InvoiceSnapshotsSummary = new InvoiceSnapShotSlice();

        DiscountedInvoicesCount = 0;
        RefundedInvoicesCount = 0;

        PaidUpInvoicesCount = 0;
        PaidUpInvoicesPercent = 0;

        ReferredInvoicesCount = 0;
        ReferredInvoicesPercent = 0;
        ReferredInvoicesValue = 0;

        UnreferredInvoicesCount = 0;
        UnreferredInvoicesPercent = 0;
        UnreferredInvoicesValue = 0;
    }

    private void processFilteredInvoice(FilteredInvoiceSlice invoice)
    {
        invoice.UpdatePrimalSnapshot();

        if (LabsFilterMode == FilterMode.IncludeItems && LabIds.Count > 0) invoice.UpdateOrderedTestNames(LabIds[0]);

        if (invoice.PrimalInvoiceSnapshot.DiscountAmount > 0m) DiscountedInvoicesCount++;

        if (invoice.CurrentInvoiceSnapshot.RefundAmount > 0m) RefundedInvoicesCount++;

        if (invoice.PrimalInvoiceSnapshot.DueAmount == 0m
            && invoice.PrimalInvoiceSnapshot.NetPayable == invoice.PrimalInvoiceSnapshot.PaidAmount)
            PaidUpInvoicesCount++;


        InvoiceSnapshotsSummary.AppendFrom(invoice.CurrentInvoiceSnapshot);

        if (invoice.DisallowReferral || invoice.IsReferrerUnknown)
        {
            UnreferredInvoicesCount++;
            UnreferredInvoicesValue += invoice.PrimalInvoiceSnapshot.NetPayable;
        }
        else
        {
            ReferredInvoicesCount++;
            ReferredInvoicesValue += invoice.PrimalInvoiceSnapshot.NetPayable;
        }
    }

    private void calculatePercentages()
    {
        var total = Invoices.Count;
        if (ReferredInvoicesCount > 0)
            ReferredInvoicesPercent = Math.Round((double)(100 * ReferredInvoicesCount) / total);
        if (UnreferredInvoicesCount > 0)
            UnreferredInvoicesPercent = Math.Round((double)(100 * UnreferredInvoicesCount) / total);
        if (PaidUpInvoicesCount > 0)
            PaidUpInvoicesPercent = Math.Round((double)(100 * PaidUpInvoicesCount) / total);
    }

    private bool validate() => BeginDate <= EndDate;

    public void CompileInvoicesList()
    {
        resetItems();

        if (!validate()) return;

        BeginDate = BeginDate.Date;
        EndDate = SharedUtilities.LastMinuteOfDay(EndDate);

        List<FilteredInvoiceSlice> items;
        switch (LabsFilterMode)
        {
            case FilterMode.IncludeItems:
                items = ReferrerCategory > 0
                    ? FaultHandler.Shield(
                        () => PatientLabOrdersRepository
                            .FilterLabOrdersByIncludePerformingLabDateRangeReferrerCategory(BeginDate,
                                EndDate,
                                ReferrerCategory,
                                LabIds))
                    : FaultHandler.Shield(
                        () => PatientLabOrdersRepository
                            .FilterLabOrdersByIncludePerformingLabDateRange(BeginDate,
                                EndDate,
                                LabIds));
                break;
            default:
                if (LabIds.Count > 0)
                    items = ReferrerCategory > 0
                        ? FaultHandler.Shield(
                            () => PatientLabOrdersRepository
                                .FilterLabOrdersByExcludePerformingLabDateRangeReferrerCategory(BeginDate,
                                    EndDate,
                                    ReferrerCategory,
                                    LabIds))
                        : FaultHandler.Shield(
                            () => PatientLabOrdersRepository.FilterLabOrdersByExcludePerformingLabDateRange(
                                BeginDate,
                                EndDate,
                                LabIds));
                else
                    items = FaultHandler.Shield(
                        () => PatientLabOrdersRepository.FilterLabOrdersByDateRange(BeginDate, EndDate));
                break;
        }

        if (items is { Count: > 0 })
            Invoices.AddRange(items.Where(i => i.IsCancelled == false && i.CurrentInvoiceSnapshot.DueAmount > 0));

        Invoices.ForEach(processFilteredInvoice);
        calculatePercentages();
    }

    public OutstandingStatementReportPrintDto GenerateIncomeOutstandingReportPrintDto(string heading)
    {
        var dto = new OutstandingStatementReportPrintDto
        {
            BeginDate = BeginDate,
            EndDate = EndDate,
            Summary = InvoiceSnapshotsSummary,
            LabHeading = heading,
            DiscountedInvoicesCount = DiscountedInvoicesCount,
            RefundedInvoicesCount = RefundedInvoicesCount,
            InvoicesCount = Invoices.Count,
            CanceledInvoicesCount = Invoices.Count(x => x.IsCancelled),
            PrintDate = FaultHandler.Shield(() => AppSysRepository.GetServerTime()),
            PrintedBy = CurrentUserContext.UserDisplayName,
            TotalInvoicesCount = Invoices.Count,
            ReferredInvoicesCount = ReferredInvoicesCount,
            ReferredInvoicesPercent = ReferredInvoicesPercent,
            ReferredInvoicesValue = ReferredInvoicesValue,
            UnreferredInvoicesCount = UnreferredInvoicesCount,
            UnreferredInvoicesPercent = UnreferredInvoicesPercent,
            UnreferredInvoicesValue = UnreferredInvoicesValue,
            PaidUpInvoicesCount = PaidUpInvoicesCount,
            PaidUpInvoicesPercent = PaidUpInvoicesPercent
        };

        foreach (var invoice in Invoices) dto.AddInvoice(invoice);
        dto.AdjustFinances();
        return dto;
    }
}