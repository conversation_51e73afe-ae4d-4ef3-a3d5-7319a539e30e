﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: CollatorInvoiceSlice.cs 1170 2014-02-15 18:13:29Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using LabMaestro.Domain;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class CollatorInvoiceSlice
{
    public CollatorInvoiceSlice()
    {
        ResultBundles = new List<CollatorResultBundleSlice>();
    }

    public long InvoiceId { get; set; }
    public string OrderId { get; set; }
    public string PatientName { get; set; }
    public string Age { get; set; }
    public string Sex { get; set; }
    public DateTime OrderDateTime { get; set; }
    public List<CollatorResultBundleSlice> ResultBundles { get; }

    public static CollatorInvoiceSlice AssembleFrom(LabOrderSearchResultSlice src,
        WorkflowStageType bundleCutoffStage)
    {
        var slice = new CollatorInvoiceSlice
        {
            InvoiceId = src.InvoiceId,
            OrderId = src.OrderId,
            PatientName = src.PatientName,
            Age = src.Age,
            Sex = src.Sex == (byte)SexType.Male ? "M" : "F",
            OrderDateTime = src.OrderDateTime
        };
        slice.populateResultBundles(bundleCutoffStage);
        return slice;
    }

    private void populateResultBundles(WorkflowStageType bundleCutoffStage)
    {
        var bundles = ResultBundlesRepository.GetActiveResultBundlesForInvoice(InvoiceId);
        foreach (var bundle in bundles)
            if (bundleCutoffStage == WorkflowStageType.Unknown ||
                (WorkflowStageType)bundle.WorkflowStage < bundleCutoffStage)
                ResultBundles.Add(new CollatorResultBundleSlice(bundle));
    }

    public CollatorInvoicePrintDto ToPrintDto()
    {
        var dto = new CollatorInvoicePrintDto
        {
            Age = Age,
            InvoiceId = InvoiceId,
            OrderDateTime = SharedUtilities.TimeToStringShort(OrderDateTime),
            OrderId = OrderId,
            PatientName = PatientName,
            Sex = Sex
        };
        dto.ResultBundles.AddRange(ResultBundles.Select(rb => rb.ToPrintDto()));
        return dto;
    }
}