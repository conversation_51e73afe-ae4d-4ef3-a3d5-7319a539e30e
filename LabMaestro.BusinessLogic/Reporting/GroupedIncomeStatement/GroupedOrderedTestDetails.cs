// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: GroupedOrderedTestDetails.cs 1523 2014-11-26 11:45:04Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using LabMaestro.Domain;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic.GroupedIncomeStatement;

public sealed class GroupedOrderedTestDetails
{
    public short TestId { get; set; }
    public decimal UnitPrice { get; set; }
    public string TestName { get; set; }
    public bool IsCancelled { get; set; }
    public TestResultType ResultType { get; set; }
    public bool IsAuxProcedure { get; set; }
    public short LabId { get; set; }
    public string LabCode { get; set; }
    public string LabName { get; set; }

    public static GroupedOrderedTestDetails AssembleFrom(LabOrderWithFinanceTestDetails src)
    {
        return new GroupedOrderedTestDetails
        {
            TestId = src.TestId,
            UnitPrice = src.UnitPrice,
            TestName = src.TestName,
            IsCancelled = src.InvoiceCancelled,
            IsAuxProcedure = src.IsAuxProcedure,
            ResultType = (TestResultType)src.TestResultType,
            LabId = src.LabId,
            LabCode = src.LabCode,
            LabName = src.LabName
        };
    }
}