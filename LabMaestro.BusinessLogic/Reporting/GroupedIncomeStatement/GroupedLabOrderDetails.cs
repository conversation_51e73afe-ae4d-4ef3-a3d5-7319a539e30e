﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: GroupedLabOrderDetails.cs 1527 2014-11-26 13:24:58Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using LabMaestro.Domain;

namespace LabMaestro.BusinessLogic.GroupedIncomeStatement;

public sealed class GroupedLabOrderDetails
{
    public GroupedLabOrderDetails()
    {
        OrderedTests = new List<GroupedOrderedTestDetails>();
    }

    public long InvoiceId { get; set; }
    public string OrderId { get; set; }
    public DateTime OrderDateTime { get; set; }
    public bool IsCancelled { get; set; }
    public bool IsExternalSubOrder { get; set; }
    public string PatientName { get; set; }
    public string ReferrerName { get; set; }
    public decimal GrossPayable { get; set; }
    public decimal DiscountAmount { get; set; }
    public decimal TaxAmount { get; set; }
    public decimal SurchargeAmount { get; set; }
    public decimal NetPayable { get; set; }
    public decimal PaidAmount { get; set; }
    public decimal DueAmount { get; set; }
    public decimal RefundAmount { get; set; }
    public bool IsReferrerUnknown { get; set; }
    public bool DisallowReferral { get; set; }
    public List<GroupedOrderedTestDetails> OrderedTests { get; }
    public LabReportGroup AssociatedLabGroup { get; set; }

    public bool HasTestsBelongingToLab(short labId) => OrderedTests.Any(t => labId == t.LabId);

    public bool HasTestsBelongingToLabGroup(LabReportGroup lab) =>
        OrderedTests.Any(t => lab.MemberLabs.Contains(t.LabId));

    public static GroupedLabOrderDetails AssembleFrom(LabOrderWithFinanceTestDetails src)
    {
        var dto = new GroupedLabOrderDetails
        {
            InvoiceId = src.InvoiceId,
            OrderId = src.OrderId,
            DisallowReferral = src.DisallowReferral,
            DiscountAmount = src.DiscountAmount,
            DueAmount = src.DueAmount,
            GrossPayable = src.GrossPayable,
            IsCancelled = src.InvoiceCancelled,
            IsExternalSubOrder = src.IsExternalSubOrder,
            IsReferrerUnknown = src.IsReferrerUnknown,
            NetPayable = src.NetPayable,
            OrderDateTime = src.OrderDateTime,
            PaidAmount = src.PaidAmount,
            PatientName = src.PatientName,
            ReferrerName = src.ReferrerName,
            RefundAmount = src.RefundAmount,
            SurchargeAmount = src.SurchargeAmount,
            TaxAmount = src.TaxAmount
        };
        return dto;
    }

    public FilteredInvoiceSlice ToFilteredInvoiceSlice()
    {
        var slice = new FilteredInvoiceSlice
        {
            DisallowReferral = DisallowReferral,
            DiscountRemarks = "",
            InvoiceId = InvoiceId,
            IsCancelled = IsCancelled,
            IsExternalSubOrder = IsExternalSubOrder,
            IsReferrerUnknown = IsReferrerUnknown,
            OrderId = OrderId,
            OrderDateTime = OrderDateTime,
            PatientName = PatientName,
            ReferrerName = ReferrerName
        };

        // set the test name if the order doesn't belong to a catch-all (PATHOLOGY) group
        if (AssociatedLabGroup != null && !AssociatedLabGroup.IsCatchAllGroup)
        {
            var test = OrderedTests.FirstOrDefault(t => AssociatedLabGroup.MemberLabs.Contains(t.LabId));
            if (test != null) slice.OrderedTestNames = test.TestName;
        }

        return slice;
    }
}