﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2015, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id$
// 
// Last modified: 2015.08.21 8:35 PM
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using LabMaestro.Domain;

namespace LabMaestro.BusinessLogic.GroupedIncomeStatement;

public sealed class GroupedLabOrdersCollection
{
    public GroupedLabOrdersCollection(bool includeAuxLabs)
    {
        LabGroups = new List<LabReportGroup>();
        LabOrders = new List<GroupedLabOrderDetails>();
        IncludeAuxiliaryLabs = includeAuxLabs;
        initializeLabGroups();
    }

    public List<LabReportGroup> LabGroups { get; }

    public List<GroupedLabOrderDetails> LabOrders { get; }

    public bool IncludeAuxiliaryLabs { get; set; }

    private void initializeLabGroups()
    {
        var allLabs = LabsRepository.FindAllLabs();

        var mr = new LabReportGroup("MR", 1000);
        mr.MemberLabs.Add(allLabs.SingleOrDefault(l => l.LabCode == "MRI").Id);
        if (IncludeAuxiliaryLabs) mr.MemberLabs.Add(allLabs.SingleOrDefault(l => l.LabCode == "RDM_MR").Id);

        var ct = new LabReportGroup("CT", 800);
        ct.MemberLabs.Add(allLabs.SingleOrDefault(l => l.LabCode == "CT").Id);
        if (IncludeAuxiliaryLabs) ct.MemberLabs.Add(allLabs.SingleOrDefault(l => l.LabCode == "RDM_CT").Id);

        var spct = new LabReportGroup("SPECT", 500);
        spct.MemberLabs.Add(allLabs.SingleOrDefault(l => l.LabCode == "SPECT").Id);
        if (IncludeAuxiliaryLabs) spct.MemberLabs.Add(allLabs.SingleOrDefault(l => l.LabCode == "RDM_SPECT").Id);

        var path = new LabReportGroup("PATH", 0) { IsCatchAllGroup = true };

        LabGroups.Add(mr);
        LabGroups.Add(ct);
        LabGroups.Add(spct);
        LabGroups.Add(path);
    }

    private GroupedLabOrderDetails ensureLabOrder(LabOrderWithFinanceTestDetails src)
    {
        var order = LabOrders.SingleOrDefault(o => o.InvoiceId == src.InvoiceId);
        if (order == null)
        {
            order = GroupedLabOrderDetails.AssembleFrom(src);
            LabOrders.Add(order);
        }

        return order;
    }

    public void ScanLabOrders(DateTime dtFrom, DateTime dtTill)
    {
        var orders = PatientLabOrdersRepository.ScanLabOrderDetailsByDateRange(dtFrom, dtTill);

        LabOrders.Clear();
        if (orders.Any())
            foreach (var details in orders)
            {
                var order = ensureLabOrder(details);
                order.OrderedTests.Add(GroupedOrderedTestDetails.AssembleFrom(details));
            }

        associateGroupsToOrders();
    }

    private void associateGroupsToOrders()
    {
        // sort by highest priority first
        foreach (var lab in LabGroups)
        foreach (var order in LabOrders)
            if (order.HasTestsBelongingToLabGroup(lab))
                // attach a lab to order if it is null, or has lower priority
                if (order.AssociatedLabGroup == null ||
                    lab.Priority > order.AssociatedLabGroup.Priority)
                    order.AssociatedLabGroup = lab;

        // bulk apply catch-all lab to all unassociated orders
        var catchAllLab = LabGroups.Single(l => l.IsCatchAllGroup);
        foreach (var order in LabOrders.Where(o => o.AssociatedLabGroup == null))
            order.AssociatedLabGroup = catchAllLab;
    }

    public List<GroupedLabOrderDetails> GetOrdersForLabGroup(string groupName)
    {
        var grp = LabGroups.Single(g => g.Name == groupName.ToUpperInvariant());
        var orders = new List<GroupedLabOrderDetails>();
        orders.AddRange(LabOrders.Where(o => o.AssociatedLabGroup == grp));
        return orders;
    }
}