﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: OrderCancellationReportBuilder.cs 1207 2014-03-08 09:35:28Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using C1.LiveLinq.Collections;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class OrderCancellationReportBuilder
{
    private readonly IndexedCollection<ModifiedLabOrderSlice> _cancelledInvoices;
    private readonly IndexedCollection<ModifiedLabOrderSlice> _partiallyCancelledInvoices;

    public OrderCancellationReportBuilder()
    {
        _cancelledInvoices = new IndexedCollection<ModifiedLabOrderSlice>();
        _partiallyCancelledInvoices = new IndexedCollection<ModifiedLabOrderSlice>();
    }

    public IReadOnlyList<ModifiedLabOrderSlice> CancelledInvoices
    {
        get { return _cancelledInvoices; }
    }

    public IReadOnlyList<ModifiedLabOrderSlice> PartiallyCancelledInvoices
    {
        get { return _partiallyCancelledInvoices; }
    }

    public DateTime DateFrom { get; set; }
    public DateTime DateTo { get; set; }

    public void CompileReport()
    {
        var allInvoices = InvoiceMasterRepository.GetPrimalInvoicesByDateRange(DateFrom.Date,
            SharedUtilities.LastMinuteOfDay(
                DateTo));
        // step #1: get all cancelled orders
        foreach (var slice in allInvoices.Where(i => i.IsCancelled))
            _cancelledInvoices.Add(ModifiedLabOrderSlice.AssembleFrom(slice));

        // step #2: scan for all partially cancelled orders
        foreach (var slice in allInvoices.Where(i => !i.IsCancelled))
        {
            var invoice = ModifiedLabOrderSlice.AssembleFrom(slice);
            var hasCancellations = false;

            var orderedTests = OrderedTestsRepository.GetAllOrderedTestDetailsInInvoice(slice.InvoiceId);
            foreach (var ot in orderedTests.Where(ot => ot.IsCancelled))
            {
                hasCancellations = true;
                invoice.AddOrderedTest(ModifiedOrderedTestSlice.AssembleFrom(ot));
            }

            if (hasCancellations)
            {
                var summary = InvoiceMasterRepository.GetInvoiceSummary(invoice.InvoiceId);
                if (summary != null) invoice.UpdateFinancialDetails(summary);
                _partiallyCancelledInvoices.Add(invoice);
            }
        }
    }

    public OrderCancellationReportPrintDto ToPrintDto()
    {
        var dto = new OrderCancellationReportPrintDto
        {
            DateFrom = SharedUtilities.DateToString(DateFrom),
            DateTo = SharedUtilities.DateToString(DateTo),
            PrintedBy = CurrentUserContext.UserDisplayName,
            PrintedOn = SharedUtilities.DateTimeToString(AppSysRepository.GetServerTime())
        };
        dto.CancelledOrders.AddRange(CancelledInvoices.Select(x => x.ToPrintDto()));
        dto.PartiallyCancelledOrders.AddRange(PartiallyCancelledInvoices.Select(x => x.ToPrintDto()));
        return dto;
    }
}

public class CancelledServiceItem
{
    public DateTime CancellationDateTime { get; set; }
    public string CancellingUserName { get; set; }
    public string CancellationRemarks { get; set; }

    protected void UpdateCancellationDetails(AuditTrailSlice audit)
    {
        if (audit != null)
        {
            CancellationDateTime = audit.EventTime;
            CancellationRemarks = audit.Note;
            CancellingUserName = audit.PerformingUserName;
        }
    }
}

public sealed class ModifiedLabOrderSlice : CancelledServiceItem
{
    private readonly List<ModifiedOrderedTestSlice> _orderedTests;

    public ModifiedLabOrderSlice()
    {
        _orderedTests = new List<ModifiedOrderedTestSlice>();
    }

    public long InvoiceId { get; set; }
    public string OrderId { get; set; }
    public string PatientName { get; set; }
    public string ReferrerName { get; set; }
    public DateTime OrderDateTime { get; set; }
    public decimal GrossPayable { get; set; }
    public decimal DiscountAmount { get; set; }
    public decimal NetPayable { get; set; }
    public decimal PaidAmount { get; set; }
    public decimal DueAmount { get; set; }

    public IReadOnlyList<ModifiedOrderedTestSlice> OrderedTests
    {
        get { return _orderedTests; }
    }

    public decimal RefundAmount { get; set; }

    public static ModifiedLabOrderSlice AssembleFrom(PrimalInvoiceSlice src)
    {
        var dto = new ModifiedLabOrderSlice
        {
            InvoiceId = src.InvoiceId,
            OrderId = src.OrderId,
            DiscountAmount = src.DiscountAmount,
            DueAmount = src.DueAmount,
            GrossPayable = src.GrossPayable,
            NetPayable = src.NetPayable,
            OrderDateTime = src.OrderDateTime,
            PaidAmount = src.PaidAmount,
            PatientName = src.PatientName,
            ReferrerName = src.ReferrerName
        };

        if (src.IsCancelled)
        {
            dto.updateCancellationInfo();
            dto.populateOrderedTests();
        }

        return dto;
    }

    public void UpdateFinancialDetails(ShiftInvoiceSummary src)
    {
        GrossPayable = src.GrossPayable;
        DiscountAmount = src.DiscountAmount;
        NetPayable = src.NetPayable;
        RefundAmount = src.RefundAmount;
        DueAmount = src.DueAmount;
        PaidAmount = src.PaidAmount;
    }

    private void populateOrderedTests()
    {
        var tests = OrderedTestsRepository.GetAllOrderedTestDetailsInInvoice(InvoiceId);
        foreach (var slice in tests) _orderedTests.Add(ModifiedOrderedTestSlice.AssembleFrom(slice));
    }

    private void updateCancellationInfo()
    {
        var audit = AuditTrailRepository
            .GetAuditRecordForInvoiceByType(InvoiceId, AuditEventType.ordOrderCanceled)
            .SingleOrDefault();
        UpdateCancellationDetails(audit);
    }

    public void AddOrderedTest(ModifiedOrderedTestSlice test)
    {
        _orderedTests.Add(test);
    }

    public ModifiedLabOrderSlicePrintDto ToPrintDto()
    {
        var dto = new ModifiedLabOrderSlicePrintDto
        {
            OrderDateTime = SharedUtilities.DateToStringOnlySlash(OrderDateTime),
            CancellationDateTime = SharedUtilities.DateTimeToStringWithSep24(CancellationDateTime),
            CancellationRemarks = CancellationRemarks,
            CancellingUserName = CancellingUserName,
            DueAmount = SharedUtilities.MoneyToStringPlainCultureNonZero(DueAmount),
            DiscountAmount = SharedUtilities.MoneyToStringPlainCultureNonZero(DiscountAmount),
            GrossPayable = SharedUtilities.MoneyToStringPlainCultureNonZero(GrossPayable),
            NetPayable = SharedUtilities.MoneyToStringPlainCultureNonZero(NetPayable),
            PaidAmount = SharedUtilities.MoneyToStringPlainCultureNonZero(PaidAmount),
            RefundAmount = SharedUtilities.MoneyToStringPlainCultureNonZero(RefundAmount),
            ReferrerName = ReferrerName,
            InvoiceId = InvoiceId.ToString(),
            OrderId = OrderId,
            PatientName = PatientName
        };
        foreach (var slice in OrderedTests) dto.CancelledTests.Add(slice.ToPrintDto());
        return dto;
    }
}

public sealed class ModifiedOrderedTestSlice : CancelledServiceItem
{
    public string TestName { get; set; }
    public string LabName { get; set; }
    public decimal UnitPrice { get; set; }
    public bool IsCancelled { get; set; }
    public DateTime OrderDateTime { get; set; }

    public static ModifiedOrderedTestSlice AssembleFrom(Domain.OrderedTestDetailSlice slice)
    {
        var dto = new ModifiedOrderedTestSlice
        {
            TestName = slice.TestName,
            LabName = slice.LabName,
            UnitPrice = slice.UnitPrice,
            IsCancelled = slice.IsCancelled,
            OrderDateTime = slice.DateCreated
        };

        if (dto.IsCancelled)
            dto.updateCancellationInfo(slice.Id);
        return dto;
    }

    private void updateCancellationInfo(long ordTestId)
    {
        var audit = AuditTrailRepository
            .GetAuditRecordForOrderedTestByType(ordTestId,
                AuditEventType.ordTestCanceled)
            .SingleOrDefault();
        UpdateCancellationDetails(audit);
    }

    public ModifiedOrderedTestSlicePrintDto ToPrintDto()
    {
        return new ModifiedOrderedTestSlicePrintDto
        {
            CancellationDateTime = SharedUtilities.DateTimeToStringWithSep24(CancellationDateTime),
            CancellationRemarks = CancellationRemarks,
            CancellingUserName = CancellingUserName,
            IsCancelled = IsCancelled,
            LabName = LabName,
            OrderDateTime = SharedUtilities.DateTimeToStringWithSep24(OrderDateTime),
            TestName = TestName,
            UnitPrice = SharedUtilities.MoneyToStringPlainCultureNonZero(UnitPrice)
        };
    }
}