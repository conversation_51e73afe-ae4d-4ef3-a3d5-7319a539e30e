﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the organization nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: FinancialAuditInvoiceTransactionSlice.cs 1003 2013-10-14 07:50:56Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using LabMaestro.Domain;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

internal sealed class FinancialAuditInvoiceTransactionSlice
{
    internal string PerformedBy { get; set; }
    internal short PerformingUserId { get; set; }
    internal int UserIpAddress { get; set; }
    internal int WorkShiftId { get; set; }
    internal decimal TxAmount { get; set; }
    internal string TxRemarks { get; set; }
    internal DateTime TxTime { get; set; }
    internal InvoiceTransactionType TxType { get; set; }
    internal TransactionFlag TxFlag { get; set; }

    internal static FinancialAuditInvoiceTransactionSlice AssembleFrom(TransactionInDateRangeSlice slice) =>
        new()
        {
            PerformedBy = slice.PerformedBy,
            TxAmount = slice.TxAmount,
            TxFlag = (TransactionFlag)slice.TxFlag,
            TxRemarks = slice.TxRemarks,
            TxTime = slice.TxTime,
            TxType = (InvoiceTransactionType)slice.TxType,
            PerformingUserId = (short)(slice.PerformingUserId ?? 0),
            UserIpAddress = slice.UserIpAddress ?? 0,
            WorkShiftId = slice.WorkShiftId ?? 0
        };

    internal FinancialAuditInvoiceTransactionPrintDto ToPrintDto()
    {
        var dto = new FinancialAuditInvoiceTransactionPrintDto
        {
            PerformedBy = PerformedBy,
            PerformingUserId = PerformingUserId.ToString(),
            UserIpAddress = SharedUtilities.IpAddressToString(UserIpAddress),
            WorkShiftId = WorkShiftId.ToString(),
            TxAmount = SharedUtilities.MoneyToStringPlainNonZero(TxAmount),
            TxRemarks = TxRemarks,
            TxTime = SharedUtilities.DateTimeToShortString(TxTime)
        };
        var txType = TxFlag == TransactionFlag.InitialOperation ? "Initial " : "";
        switch (TxType)
        {
            case InvoiceTransactionType.CashDiscount:
                txType += "Discount";
                break;
            case InvoiceTransactionType.Refund:
                txType += "Refund";
                break;
            case InvoiceTransactionType.DiscountRebate:
                txType += "Discount Rebate";
                break;
        }

        dto.TxType = txType;

        return dto;
    }
}