﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ReferralEligibilitySummaryReportBuilder.cs 1370 2014-06-12 15:59:02Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class ReferralEligibilitySummaryReportBuilder
{
    public ReferralEligibilitySummaryReportBuilder()
    {
        LabIds = [];
        ReferrerCategory = -1;
        resetItems();
    }

    public List<short> LabIds { get; }

    public List<FilteredInvoiceSlice> Invoices { get; private set; }

    public DateTime BeginDate { get; set; }

    public DateTime EndDate { get; set; }

    public FilterMode LabsFilterMode { get; set; }

    public int DiscountedInvoicesCount { get; private set; }
    public int RefundedInvoicesCount { get; private set; }
    public int ReferredInvoicesCount { get; private set; }
    public decimal ReferredInvoicesNet { get; private set; }
    public decimal ReferredInvoicesGross { get; private set; }
    public decimal ReferredInvoicesDiscount { get; private set; }
    public decimal ReferredInvoicesPaid { get; private set; }
    public decimal ReferredInvoicesDue { get; private set; }
    public double ReferredInvoicesPercent { get; private set; }
    public int UnreferredInvoicesCount { get; private set; }
    public decimal UnreferredInvoicesNet { get; private set; }
    public decimal UnreferredInvoicesGross { get; private set; }
    public decimal UnreferredInvoicesDiscount { get; private set; }
    public decimal UnreferredInvoicesPaid { get; private set; }
    public decimal UnreferredInvoicesDue { get; private set; }
    public double UnreferredInvoicesPercent { get; private set; }

    public short ReferrerCategory { get; set; }

    public decimal TotalInvoicesPaid { get; set; }

    public decimal TotalInvoicesDue { get; set; }

    public decimal TotalInvoicesDiscount { get; set; }

    public decimal TotalInvoicesNet { get; set; }

    public decimal TotalInvoicesGross { get; set; }

    private void resetItems()
    {
        Invoices = new List<FilteredInvoiceSlice>();

        DiscountedInvoicesCount = 0;
        RefundedInvoicesCount = 0;

        ReferredInvoicesCount = 0;
        ReferredInvoicesPercent = 0;
        ReferredInvoicesNet = 0;
        ReferredInvoicesDue = 0;
        ReferredInvoicesGross = 0;
        ReferredInvoicesPaid = 0;
        ReferredInvoicesDiscount = 0;

        UnreferredInvoicesCount = 0;
        UnreferredInvoicesPercent = 0;
        UnreferredInvoicesNet = 0;
        UnreferredInvoicesDiscount = 0;
        UnreferredInvoicesDue = 0;
        UnreferredInvoicesGross = 0;
        UnreferredInvoicesPaid = 0;

        TotalInvoicesGross = 0m;
        TotalInvoicesNet = 0m;
        TotalInvoicesDiscount = 0m;
        TotalInvoicesDue = 0m;
        TotalInvoicesPaid = 0m;
    }

    private void processFilteredInvoice(FilteredInvoiceSlice invoice)
    {
        invoice.UpdatePrimalSnapshot();

        if (LabsFilterMode == FilterMode.IncludeItems && LabIds.Count > 0) invoice.UpdateOrderedTestNames(LabIds[0]);

        if (invoice.CurrentInvoiceSnapshot.DiscountAmount > 0m) DiscountedInvoicesCount++;

        if (invoice.CurrentInvoiceSnapshot.RefundAmount > 0m) RefundedInvoicesCount++;

        TotalInvoicesDiscount += invoice.CurrentInvoiceSnapshot.DiscountAmount;
        TotalInvoicesDue += invoice.CurrentInvoiceSnapshot.DueAmount;
        TotalInvoicesGross += invoice.CurrentInvoiceSnapshot.GrossPayable;
        TotalInvoicesNet += invoice.CurrentInvoiceSnapshot.NetPayable;
        TotalInvoicesPaid += invoice.CurrentInvoiceSnapshot.PaidAmount;

        if (invoice.DisallowReferral || invoice.IsReferrerUnknown)
        {
            UnreferredInvoicesCount++;
            UnreferredInvoicesNet += invoice.CurrentInvoiceSnapshot.NetPayable;
            UnreferredInvoicesGross += invoice.CurrentInvoiceSnapshot.GrossPayable;
            UnreferredInvoicesDiscount += invoice.CurrentInvoiceSnapshot.DiscountAmount;
            UnreferredInvoicesDue += invoice.CurrentInvoiceSnapshot.DueAmount;
            UnreferredInvoicesPaid += invoice.CurrentInvoiceSnapshot.PaidAmount;
        }
        else
        {
            ReferredInvoicesCount++;
            ReferredInvoicesNet += invoice.CurrentInvoiceSnapshot.NetPayable;
            ReferredInvoicesGross += invoice.CurrentInvoiceSnapshot.GrossPayable;
            ReferredInvoicesDiscount += invoice.CurrentInvoiceSnapshot.DiscountAmount;
            ReferredInvoicesDue += invoice.CurrentInvoiceSnapshot.DueAmount;
            ReferredInvoicesPaid += invoice.CurrentInvoiceSnapshot.PaidAmount;
        }
    }

    private void calculatePercentages()
    {
        var total = Invoices.Count;
        if (ReferredInvoicesCount > 0)
            ReferredInvoicesPercent = Math.Round((double)(100 * ReferredInvoicesCount) / total);
        if (UnreferredInvoicesCount > 0)
            UnreferredInvoicesPercent = Math.Round((double)(100 * UnreferredInvoicesCount) / total);
    }

    private bool validate()
    {
        return BeginDate <= EndDate;
    }

    public void CompileInvoicesList()
    {
        resetItems();

        if (!validate()) return;

        BeginDate = BeginDate.Date;
        EndDate = SharedUtilities.LastMinuteOfDay(EndDate);

        List<FilteredInvoiceSlice> items;
        switch (LabsFilterMode)
        {
            case FilterMode.IncludeItems:
                items = ReferrerCategory > 0
                    ? FaultHandler.Shield(
                        () => PatientLabOrdersRepository
                            .FilterLabOrdersByIncludePerformingLabDateRangeReferrerCategory(BeginDate,
                                EndDate,
                                ReferrerCategory,
                                LabIds))
                    : FaultHandler.Shield(
                        () => PatientLabOrdersRepository
                            .FilterLabOrdersByIncludePerformingLabDateRange(BeginDate,
                                EndDate,
                                LabIds));
                break;
            default:
                if (LabIds.Count > 0)
                    items = ReferrerCategory > 0
                        ? FaultHandler.Shield(
                            () => PatientLabOrdersRepository
                                .FilterLabOrdersByExcludePerformingLabDateRangeReferrerCategory(BeginDate,
                                    EndDate,
                                    ReferrerCategory,
                                    LabIds))
                        : FaultHandler.Shield(
                            () => PatientLabOrdersRepository.FilterLabOrdersByExcludePerformingLabDateRange(
                                BeginDate,
                                EndDate,
                                LabIds));
                else
                    items = FaultHandler.Shield(
                        () => PatientLabOrdersRepository.FilterLabOrdersByDateRange(BeginDate, EndDate));
                break;
        }

        if (items != null && items.Count > 0) Invoices.AddRange(items.Where(i => !i.IsCancelled));

        Invoices.ForEach(processFilteredInvoice);
        calculatePercentages();
    }

    public ReferralEligibilitySummaryReportPrintDto ToPrintDto()
    {
        var dto = new ReferralEligibilitySummaryReportPrintDto
        {
            BeginDate = BeginDate,
            EndDate = EndDate,
            DiscountedInvoicesCount = DiscountedInvoicesCount,
            RefundedInvoicesCount = RefundedInvoicesCount,
            InvoicesCount = Invoices.Count,
            CanceledInvoicesCount = Invoices.Count(x => x.IsCancelled),
            PrintDate = FaultHandler.Shield(AppSysRepository.GetServerTime),
            PrintedBy = CurrentUserContext.UserDisplayName,
            TotalInvoicesCount = Invoices.Count,
            ReferredInvoicesCount = ReferredInvoicesCount,
            ReferredInvoicesPercent = ReferredInvoicesPercent,
            ReferredInvoicesNet = ReferredInvoicesNet,
            ReferredInvoicesDiscount = ReferredInvoicesDiscount,
            ReferredInvoicesPaid = ReferredInvoicesPaid,
            ReferredInvoicesDue = ReferredInvoicesDue,
            ReferredInvoicesGross = ReferredInvoicesGross,
            UnreferredInvoicesCount = UnreferredInvoicesCount,
            UnreferredInvoicesPercent = UnreferredInvoicesPercent,
            UnreferredInvoicesNet = UnreferredInvoicesNet,
            UnreferredInvoicesDiscount = UnreferredInvoicesDiscount,
            UnreferredInvoicesPaid = UnreferredInvoicesPaid,
            UnreferredInvoicesDue = UnreferredInvoicesDue,
            UnreferredInvoicesGross = UnreferredInvoicesGross,
            TotalInvoicesDiscount = TotalInvoicesDiscount,
            TotalInvoicesDue = TotalInvoicesDue,
            TotalInvoicesGross = TotalInvoicesGross,
            TotalInvoicesNet = TotalInvoicesNet,
            TotalInvoicesPaid = TotalInvoicesPaid
        };
        dto.UpdateChartDatapoints();
        return dto;
    }
}