﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: UnknownReferrersReportBuilder.cs 1192 2014-03-04 18:25:07Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class UnknownReferrersReportBuilder
{
    public UnknownReferrersReportBuilder()
    {
        LabOrders = new List<LabOrderSearchResultSlice>();
    }

    public DateTime DateFrom { get; set; }
    public DateTime DateTo { get; set; }
    public List<LabOrderSearchResultSlice> LabOrders { get; }

    public void CompileReport()
    {
        var invoices = FaultHandler.Shield(
            () =>
                PatientLabOrdersRepository
                    .SearchActiveLabOrdersByDateRange(DateFrom.Date,
                        SharedUtilities.LastMinuteOfDay(DateTo)));
        foreach (var invoice in invoices.Where(i =>
                     i.IsReferrerUnknown && !i.IsCancelled && !string.IsNullOrEmpty(i.ReferrerName)))
            LabOrders.Add(invoice);
    }

    public UnknownReferrersReportPrintDto ToPrintDto()
    {
        var dto = new UnknownReferrersReportPrintDto
        {
            DateFrom = SharedUtilities.DateToString(DateFrom),
            DateTo = SharedUtilities.DateToString(DateTo),
            PrintedOn = SharedUtilities.DateToString(AppSysRepository.GetServerTime()),
            PrintedBy = CurrentUserContext.UserDisplayName
        };
        foreach (var slice in LabOrders)
            dto.ReferrerInvoices.Add(UnknownReferrerInvoiceSlicePrintDto.AssembleFrom(slice));
        return dto;
    }
}