﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: UserDailyReceivablesSummary.cs 1186 2014-03-03 04:32:42Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using LabMaestro.Domain;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public class UserDailyReceivablesSummary
{
    public decimal CurrentCashAmount { get; private set; }
    public decimal CurrentNonCashAmount { get; private set; }
    public decimal CurrentDiscountAmount { get; private set; }
    public decimal CurrentRefundAmount { get; private set; }

    public decimal BackdatedCashAmount { get; private set; }
    public decimal BackdatedNonCashAmount { get; private set; }
    public decimal BackdatedDiscountAmount { get; private set; }
    public decimal BackdatedRefundAmount { get; private set; }

    public short UserId { get; private set; }
    public string UserName { get; private set; }
    public DateTime TargetDate { get; private set; }

    public static UserDailyReceivablesSummary AssembleFrom(TransactionDetailExSlice slice, DateTime targetDate) =>
        new()
        {
            UserId = slice.PerformingUserId ?? -1,
            UserName = slice.UserName,
            TargetDate = targetDate
        };

    private bool isBackdatedTransaction(TransactionDetailExSlice slice) =>
        TargetDate.Subtract(slice.OrderDateTime.Date).Days > 0;

    public void UpdateSummary(TransactionDetailExSlice slice)
    {
        switch ((InvoiceTransactionType)slice.TxType)
        {
            case InvoiceTransactionType.CashDiscount:
                if (isBackdatedTransaction(slice))
                    BackdatedDiscountAmount += slice.TxAmount;
                else
                    CurrentDiscountAmount += slice.TxAmount;
                break;
            case InvoiceTransactionType.Refund:
                if (isBackdatedTransaction(slice))
                    BackdatedRefundAmount += slice.TxAmount;
                else
                    CurrentRefundAmount += slice.TxAmount;
                break;
            case InvoiceTransactionType.Payment:
                if (isBackdatedTransaction(slice))
                {
                    BackdatedCashAmount += slice.TxAmount;
                    BackdatedNonCashAmount += slice.NonCashAmount;
                }
                else
                {
                    CurrentCashAmount += slice.TxAmount;
                    CurrentNonCashAmount += slice.NonCashAmount;
                }

                break;
        }
    }

    public bool HasCurrentTransactions() => CurrentCashAmount > 0 || CurrentDiscountAmount > 0;

    public bool HasBackdatedTransactions() => BackdatedCashAmount > 0 || BackdatedDiscountAmount > 0;

    public UserReceivablesSummaryPrintDto ToPrintDto(bool currentSummary)
    {
        if (currentSummary)
            return new UserReceivablesSummaryPrintDto
            {
                DiscountAmount = SharedUtilities.MoneyToStringPlainCultureNonZero(CurrentDiscountAmount),
                CashAmount = SharedUtilities.MoneyToStringPlainCultureNonZero(CurrentCashAmount),
                NonCashAmount = SharedUtilities.MoneyToStringPlainCultureNonZero(CurrentNonCashAmount),
                RefundAmount = SharedUtilities.MoneyToStringPlainCultureNonZero(CurrentRefundAmount),
                UserId = UserId.ToString(),
                UserName = UserName
            };

        return new UserReceivablesSummaryPrintDto
        {
            DiscountAmount = SharedUtilities.MoneyToStringPlainCultureNonZero(BackdatedDiscountAmount),
            CashAmount = SharedUtilities.MoneyToStringPlainCultureNonZero(BackdatedCashAmount),
            NonCashAmount = SharedUtilities.MoneyToStringPlainCultureNonZero(BackdatedNonCashAmount),
            RefundAmount = SharedUtilities.MoneyToStringPlainCultureNonZero(BackdatedRefundAmount),
            UserId = UserId.ToString(),
            UserName = UserName
        };
    }
}