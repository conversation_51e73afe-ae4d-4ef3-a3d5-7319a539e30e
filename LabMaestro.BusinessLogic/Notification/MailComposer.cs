﻿using System.Collections.Generic;
using System.IO;
using MimeKit;

namespace LabMaestro.BusinessLogic.Notification;

public sealed class MailComposer
{
    public string SenderEmail { get; set; }
    public string? SenderName { get; set; } = null;
    public List<string> Recipients { get; } = new();
    public string Subject { get; set; }
    public string TextContent { get; set; }
    public string MessageOutput { get; set; }
    public string? HtmlContent { get; set; } = null;
    public List<MimePart> Attachments { get; } = new();

    public void Attach(string filename, string mediaType = "application", string mediaSubtype = "octet-stream") =>
        Attach(File.OpenRead(filename), filename, mediaType, mediaSubtype);

    public void Attach(Stream stream, string filename, string mediaType = "application",
        string mediaSubtype = "octet-stream")
    {
        var attachment = new MimePart(mediaType, mediaSubtype)
        {
            Content = new MimeContent(stream, ContentEncoding.Default),
            ContentDisposition = new ContentDisposition(ContentDisposition.Attachment),
            ContentTransferEncoding = ContentEncoding.Base64,
            FileName = Path.GetFileName(filename)
        };
        Attachments.Add(attachment);
    }

    public void Compose()
    {
        MessageOutput = string.Empty;
        var message = new MimeMessage();
        message.Subject = Subject;

        message.From.Add(new MailboxAddress(SenderEmail, SenderName ?? SenderEmail));
        foreach (var recipient in Recipients)
            message.To.Add(new MailboxAddress(recipient, recipient));

        var builder = new BodyBuilder();

        if (!string.IsNullOrEmpty(TextContent))
            builder.TextBody = TextContent;

        if (!string.IsNullOrEmpty(HtmlContent))
            builder.HtmlBody = HtmlContent;

        if (Attachments is { Count: > 0 })
            foreach (var attachment in Attachments)
                builder.Attachments.Add(attachment);

        message.Body = builder.ToMessageBody();
        MessageOutput = message.ToString();
    }
}