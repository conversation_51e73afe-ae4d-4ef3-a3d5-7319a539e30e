﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: BillableItemUtilizationReportCompiler.cs 950 2013-09-22 06:37:40Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using C1.LiveLinq;
using C1.LiveLinq.Collections;
using CuttingEdge.Conditions;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class BillableItemUtilizationReportCompiler
{
    private readonly IndexedCollection<BillableItemUtilization> _biUtilizations;

    public BillableItemUtilizationReportCompiler()
    {
        _biUtilizations = new IndexedCollection<BillableItemUtilization>();
        SortType = UtilizationSortType.Volume;
        ResetSummary();
    }

    public UtilizationSortType SortType { get; set; }

    public List<BillableItemUtilization> BillableItemUtilizationsByVolume
    {
        get { return _biUtilizations.AsIndexed().OrderByDescending(x => x.VolumeGross).ThenBy(x => x.Name).ToList(); }
    }

    public List<BillableItemUtilization> BillableItemUtilizationsByValue
    {
        get { return _biUtilizations.AsIndexed().OrderByDescending(x => x.ValueGross).ThenBy(x => x.Name).ToList(); }
    }

    public List<BillableItemUtilization> BillableItemUtilizationsByName
    {
        get { return _biUtilizations.AsIndexed().OrderBy(x => x.Name).ToList(); }
    }

    public DateTime DateStart { get; set; }
    public DateTime DateEnd { get; set; }

    public decimal ValueGross { get; set; }
    public decimal ValueNet { get; set; }
    public decimal ValueRefunded { get; set; }

    public int VolumeGross { get; set; }
    public int VolumeNet { get; set; }
    public int VolumeCanceled { get; set; }

    public void ResetSummary()
    {
        VolumeGross = 0;
        VolumeNet = 0;
        VolumeCanceled = 0;

        ValueNet = 0m;
        ValueGross = 0m;
        ValueRefunded = 0m;
    }

    public void CompileReport()
    {
        DateStart = DateStart.Date;
        DateEnd = SharedUtilities.LastMinuteOfDay(DateEnd);

        ResetSummary();
        _biUtilizations.BeginUpdate();
        try
        {
            _biUtilizations.Clear();
            var itemSlices = FaultHandler.Shield(
                () => OrderedBillableItemsRepository.SearchOrderedBillableItemsInDateRange(DateStart, DateEnd));
            if (itemSlices != null)
                foreach (var slice in itemSlices)
                    AddTestUtilization(slice);
        }
        finally
        {
            _biUtilizations.EndUpdate();
        }
    }

    public void AddTestUtilization(OrderedBillableItemInDateRangeSlice dto)
    {
        Condition.Requires(dto).IsNotNull();
        var item = findOrAddBIUtilization(dto);
        item.UpdateUtilization(dto);
        updateUtilization(dto);
    }

    private void updateUtilization(OrderedBillableItemInDateRangeSlice dto)
    {
        var value = dto.UnitPrice * dto.Quantity;
        VolumeGross += dto.Quantity;
        ValueGross += value;
        if (dto.IsCancelled)
        {
            ValueRefunded += value;
            VolumeCanceled += dto.Quantity;
        }
        else
        {
            ValueNet += value;
            VolumeNet += dto.Quantity;
        }
    }

    private BillableItemUtilization findOrAddBIUtilization(OrderedBillableItemInDateRangeSlice dto)
    {
        var item = _biUtilizations.AsIndexed().SingleOrDefault(x => x.Id == dto.Id);
        if (item == null)
        {
            item = BillableItemUtilization.AssembleFrom(dto);
            _biUtilizations.Add(item);
        }

        return item;
    }

    public BillableItemUtilizationReportPrintDto ToPrintDto()
    {
        var dto = new BillableItemUtilizationReportPrintDto
        {
            StartDate = SharedUtilities.DateToString(DateStart),
            EndDate = SharedUtilities.DateToString(DateEnd),

            ValueNet = SharedUtilities.MoneyToStringCulture(ValueNet),
            ValueRefunded = SharedUtilities.MoneyToStringCulture(ValueRefunded),
            ValueGross = SharedUtilities.MoneyToStringCulture(ValueGross),

            VolumeNet = SharedUtilities.IntToStringPositive(VolumeNet),
            VolumeCanceled = SharedUtilities.IntToStringPositive(VolumeCanceled),
            VolumeGross = SharedUtilities.IntToStringPositive(VolumeGross)
        };

        List<BillableItemUtilization> items;
        switch (SortType)
        {
            case UtilizationSortType.Name:
                items = BillableItemUtilizationsByName;
                break;
            case UtilizationSortType.Value:
                items = BillableItemUtilizationsByValue;
                break;
            default:
                items = BillableItemUtilizationsByVolume;
                break;
        }

        dto.Utilizations.AddRange(items.Select(biu => biu.ToPrintDto()));
        return dto;
    }
}