﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: BillableItemUtilization.cs 950 2013-09-22 06:37:40Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using LabMaestro.Domain;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public class BillableItemUtilization
{
    public short Id { get; set; }
    public string Name { get; set; }
    public decimal UnitPrice { get; set; }

    public decimal ValueGross { get; set; }
    public decimal ValueNet { get; set; }
    public decimal ValueRefunded { get; set; }

    public int VolumeGross { get; set; }
    public int VolumeNet { get; set; }
    public int VolumeCanceled { get; set; }

    public static BillableItemUtilization AssembleFrom(OrderedBillableItemInDateRangeSlice dto)
    {
        var result = new BillableItemUtilization
        {
            Id = dto.Id,
            Name = dto.Name,
            UnitPrice = dto.UnitPrice
        };
        //result.UpdateUtilization(dto);
        return result;
    }

    public void UpdateUtilization(OrderedBillableItemInDateRangeSlice dto)
    {
        var value = dto.UnitPrice * dto.Quantity;
        VolumeGross += dto.Quantity;
        ValueGross += value;
        if (dto.IsCancelled)
        {
            ValueRefunded += value;
            VolumeCanceled += dto.Quantity;
        }
        else
        {
            ValueNet += value;
            VolumeNet += dto.Quantity;
        }
    }

    public override string ToString()
    {
        return string.Format("{0}: {1} = {2} + {3}", Name, VolumeGross, ValueNet, VolumeCanceled);
    }

    public BillableItemUtilizationPrintDto ToPrintDto()
    {
        return new BillableItemUtilizationPrintDto
        {
            Id = SharedUtilities.IntToStringPositive(Id),
            Name = Name,
            ValueNet = SharedUtilities.MoneyToStringCulture(ValueNet),
            ValueRefunded = SharedUtilities.MoneyToStringCulture(ValueRefunded),
            ValueGross = SharedUtilities.MoneyToStringCulture(ValueGross),
            VolumeNet = SharedUtilities.IntToStringPositive(VolumeNet),
            VolumeCanceled = SharedUtilities.IntToStringPositive(VolumeCanceled),
            VolumeGross = SharedUtilities.IntToStringPositive(VolumeGross),
            UnitPrice = SharedUtilities.MoneyToStringCulture(UnitPrice)
        };
    }
}