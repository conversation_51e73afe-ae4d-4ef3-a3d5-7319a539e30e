// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: InvoiceWorkflowEvents.cs 1275 2014-05-20 10:32:23Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using LabMaestro.Domain;

namespace LabMaestro.BusinessLogic;

public sealed class InvoiceWorkflowEvents
{
    private readonly List<ResultBundleWorkflowEvents> _bundles;

    public InvoiceWorkflowEvents(long invoiceId, string orderId, DateTime orderDateTime)
    {
        OrderDateTime = orderDateTime;
        OrderId = orderId;
        InvoiceId = invoiceId;
        _bundles = new List<ResultBundleWorkflowEvents>();
    }

    public long InvoiceId { get; }
    public string OrderId { get; }
    public DateTime OrderDateTime { get; }

    public List<ResultBundleWorkflowEvents> Bundles
    {
        get { return _bundles.ToList(); }
    }

    public List<WorkflowAuditEventInfo> AllAuditEvents()
    {
        var list = new List<WorkflowAuditEventInfo>();
        foreach (var bundle in _bundles)
        {
            if (bundle.ResultEntry.IsValid) list.Add(bundle.ResultEntry);
            if (bundle.ResultValidation.IsValid) list.Add(bundle.ResultValidation);
            if (bundle.ResultFinalization.IsValid) list.Add(bundle.ResultFinalization);
            if (bundle.ReportCollation.IsValid) list.Add(bundle.ReportCollation);
            if (bundle.ReportDispatch.IsValid) list.Add(bundle.ReportDispatch);
        }

        return list;
    }

    private ResultBundleWorkflowEvents findOrAddBundle(long bundleId)
    {
        var bundle = _bundles.SingleOrDefault(x => x.BundleId == bundleId);
        if (bundle == null)
        {
            bundle = new ResultBundleWorkflowEvents(bundleId, InvoiceId, OrderId, OrderDateTime);
            _bundles.Add(bundle);
        }

        return bundle;
    }

    public void AddWorkflowEvent(long bundleId, short eventType, short userId, DateTime eventTime)
    {
        var bundle = findOrAddBundle(bundleId);
        bundle.AddWorkflowEvent(eventType, userId, eventTime);
    }

    public void UpdateWorkflowEvent(AuditTrailSlice slice)
    {
        if (slice.ResultBundleId != null)
        {
            var bundle = findOrAddBundle((long)slice.ResultBundleId);
            bundle.UpdateWorkflowEvent(slice);
        }
    }

    public void ProcessWorkflowEvent(AuditRecordsInCategoryByDateRangeSlice slice)
    {
        if (slice.ResultBundleId != null)
        {
            var bundle = findOrAddBundle((long)slice.ResultBundleId);
            bundle.ProcessWorkflowEvent(slice);
        }
    }
}