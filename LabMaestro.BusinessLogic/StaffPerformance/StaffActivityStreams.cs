// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: StaffActivityStreams.cs 1277 2014-05-20 13:10:38Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class StaffActivityStreams
{
    private readonly List<StaffWorkflowActivityInfo> _activities;

    public StaffActivityStreams(short staffId, string staffName)
    {
        StaffName = staffName;
        StaffId = staffId;
        _activities = new List<StaffWorkflowActivityInfo>();
    }

    public short StaffId { get; private set; }
    public string StaffName { get; }
    public TimeSpan WorstResponseTime { get; private set; }
    public TimeSpan BestResponseTime { get; private set; }
    public TimeSpan AverageResponseTime { get; private set; }

    public int ActivityCount
    {
        get { return _activities.Count; }
    }

    public List<StaffWorkflowActivityInfo> Activities
    {
        get { return _activities.OrderBy(x => x.EventTime).ToList(); }
    }

    public void AddActivity(WorkflowAuditEventInfo info)
    {
        _activities.Add(StaffWorkflowActivityInfo.AssembleFrom(info));
    }

    private List<StaffWorkflowActivityInfo> activitiesWithInterval()
    {
        return _activities.Where(x => x.ResponseInterval.Ticks > 0).ToList();
    }

    public void CompileSummary()
    {
        _activities.Sort((a, b) => a.EventTime.CompareTo(b.EventTime));
        WorstResponseTime = activitiesWithInterval().Max(x => x.ResponseInterval);
        BestResponseTime = activitiesWithInterval().Min(x => x.ResponseInterval);
        var totalTicks = _activities.Sum(x => x.ResponseInterval.Ticks);
        var avgTicks = totalTicks / activitiesWithInterval().Count;
        AverageResponseTime = new TimeSpan(avgTicks);
    }

    public StaffPerformanceInfoPrintDto ToPrintDto()
    {
        return new StaffPerformanceInfoPrintDto
        {
            StaffName = StaffName,
            ActivitiesCount = ActivityCount,
            AverageMinutes = (int)AverageResponseTime.TotalMinutes,
            AverageResponseTime = SharedUtilities.TimeSpanToString(AverageResponseTime),
            BestResponseTime = SharedUtilities.TimeSpanToString(BestResponseTime),
            WorstResponseTime = SharedUtilities.TimeSpanToString(WorstResponseTime)
        };
    }
}