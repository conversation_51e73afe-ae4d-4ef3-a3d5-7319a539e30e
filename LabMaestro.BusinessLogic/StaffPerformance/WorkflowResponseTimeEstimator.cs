﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: WorkflowResponseTimeEstimator.cs 1275 2014-05-20 10:32:23Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using C1.LiveLinq;
using C1.LiveLinq.Collections;
using LabMaestro.Domain;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class WorkflowResponseTimeEstimator
{
    private readonly IndexedCollection<WorkflowAuditEventInfo> _allAuditEvents;
    private readonly IndexedCollection<InvoiceWorkflowEvents> _orderEvents;

    public WorkflowResponseTimeEstimator()
    {
        _orderEvents = new IndexedCollection<InvoiceWorkflowEvents>();
        _allAuditEvents = new IndexedCollection<WorkflowAuditEventInfo>();
    }

    public List<InvoiceWorkflowEvents> Invoices
    {
        get { return _orderEvents.ToList(); }
    }

    public List<WorkflowAuditEventInfo> AllAuditEvents
    {
        get { return _allAuditEvents.ToList(); }
    }

    public void Compile(DateTime dtFrom, DateTime dtTo)
    {
        dtFrom = dtFrom.Date;
        dtTo = SharedUtilities.LastMinuteOfDay(dtTo);

        var auditRecords = AuditTrailRepository.GetAuditRecordsInCategoryByDateRange(AuditEventCategory.Workflow,
            dtFrom, dtTo);
        if (auditRecords != null)
            foreach (var slice in auditRecords)
            {
                var wfEvents = findOrAddLabOrderWorkflowAuditEvents(slice);
                wfEvents.ProcessWorkflowEvent(slice);
            }

        var invoiceIds = AuditTrailRepository.GetDistinctAuditedInvoicesInDateRange(AuditEventCategory.Workflow,
            dtFrom, dtTo);
        foreach (var wfEvents in invoiceIds
                     .Where(x => x != null)
                     .Select(id => findLabOrderWorkflowAuditEvents((long)id))
                     .Where(wfEvent => wfEvent != null))
        {
            var auditEvents = AuditTrailRepository.GetAuditRecordsForInvoice(
                wfEvents.InvoiceId,
                AuditEventCategory.Workflow);
            foreach (var slice in auditEvents) wfEvents.UpdateWorkflowEvent(slice);
        }

        _allAuditEvents.BeginUpdate();
        try
        {
            _allAuditEvents.Clear();
            foreach (var invoice in _orderEvents) _allAuditEvents.AddRange(invoice.AllAuditEvents());
        }
        finally
        {
            _allAuditEvents.EndUpdate();
        }
    }

    public List<WorkflowAuditEventInfo> FindWorkflowEventsForUser(
        short userId,
        DateTime dtFrom,
        DateTime dtTo)
    {
        return _allAuditEvents.AsIndexed()
            .Where(x => x.UserId == userId
                        && x.EventTimeIsWithinRange(dtFrom, dtTo))
            .ToList();
    }

    public List<WorkflowAuditEventInfo> FindWorkflowEventsForUserType(
        short userId,
        WorkflowAuditEventType wfType,
        DateTime dtFrom,
        DateTime dtTo)
    {
        return _allAuditEvents.AsIndexed()
            .Where(x => x.UserId == userId
                        && x.WorkflowEventType == wfType
                        && x.EventTimeIsWithinRange(dtFrom, dtTo))
            .ToList();
    }

    public List<WorkflowAuditEventInfo> FindWorkflowEventsForType(
        WorkflowAuditEventType wfType,
        DateTime dtFrom,
        DateTime dtTo)
    {
        return _allAuditEvents.AsIndexed()
            .Where(x => x.WorkflowEventType == wfType
                        && x.EventTimeIsWithinRange(dtFrom, dtTo))
            .ToList();
    }

    private InvoiceWorkflowEvents findOrAddLabOrderWorkflowAuditEvents(
        AuditRecordsInCategoryByDateRangeSlice slice)
    {
        var invoiceId = (long)slice.InvoiceId;
        var result = _orderEvents.AsIndexed().SingleOrDefault(x => x.InvoiceId == invoiceId);
        if (result == null)
        {
            result = new InvoiceWorkflowEvents(invoiceId, slice.OrderId, slice.OrderDateTime);
            _orderEvents.Add(result);
        }

        return result;
    }

    private InvoiceWorkflowEvents findLabOrderWorkflowAuditEvents(
        long invoiceId)
    {
        var result = _orderEvents.AsIndexed().SingleOrDefault(x => x.InvoiceId == invoiceId);
        return result;
    }
}