// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ResultBundleWorkflowEvents.cs 1275 2014-05-20 10:32:23Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using LabMaestro.Domain;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class ResultBundleWorkflowEvents
{
    public ResultBundleWorkflowEvents(long bundleId, long invoiceId, string orderId, DateTime orderDateTime)
    {
        OrderDateTime = orderDateTime;
        OrderId = orderId;
        InvoiceId = invoiceId;
        BundleId = bundleId;

        ResultEntry = new WorkflowAuditEventInfo(InvoiceId, BundleId, WorkflowAuditEventType.ResultEntry)
        {
            PreviousEventTime = orderDateTime
        };
        ResultValidation = new WorkflowAuditEventInfo(InvoiceId, BundleId, WorkflowAuditEventType.ResultValidation);
        ResultFinalization = new WorkflowAuditEventInfo(InvoiceId, BundleId,
            WorkflowAuditEventType.ResultFinalization);
        ReportCollation = new WorkflowAuditEventInfo(InvoiceId, BundleId, WorkflowAuditEventType.ReportCollation);
        ReportDispatch = new WorkflowAuditEventInfo(InvoiceId, BundleId, WorkflowAuditEventType.ReportDispatch);
    }

    public WorkflowAuditEventInfo ResultEntry { get; }
    public WorkflowAuditEventInfo ResultValidation { get; }
    public WorkflowAuditEventInfo ResultFinalization { get; }
    public WorkflowAuditEventInfo ReportCollation { get; }
    public WorkflowAuditEventInfo ReportDispatch { get; }
    public long InvoiceId { get; }
    public string OrderId { get; private set; }
    public DateTime OrderDateTime { get; private set; }
    public long BundleId { get; }

    public void ProcessWorkflowEvent(AuditRecordsInCategoryByDateRangeSlice slice)
    {
        AddWorkflowEvent(slice.EventType, slice.StaffId ?? -1, slice.EventTime);
    }

    public void UpdateWorkflowEvent(AuditTrailSlice slice)
    {
        switch ((AuditEventType)slice.EventType)
        {
            case AuditEventType.wfResultBundleCreated:
                ResultEntry.PreviousEventTime = slice.EventTime;
                break;
            case AuditEventType.wfResultEntered:
                ResultEntry.EventTime = slice.EventTime;
                ResultEntry.UserId = slice.PerformingUserId ?? -1;
                ResultValidation.PreviousEventTime = slice.EventTime;
                break;
            case AuditEventType.wfResultVerified:
                ResultValidation.EventTime = slice.EventTime;
                ResultValidation.UserId = slice.PerformingUserId ?? -1;
                ResultFinalization.PreviousEventTime = slice.EventTime;
                break;
            case AuditEventType.wfResultFinalized:
                ResultFinalization.EventTime = slice.EventTime;
                ResultFinalization.UserId = slice.PerformingUserId ?? -1;
                ReportCollation.PreviousEventTime = slice.EventTime;
                break;
            case AuditEventType.wfReportCollated:
                ReportCollation.EventTime = slice.EventTime;
                ReportCollation.UserId = slice.PerformingUserId ?? -1;
                ReportDispatch.PreviousEventTime = slice.EventTime;
                break;
            case AuditEventType.wfReportDispatched:
                ReportDispatch.UserId = slice.PerformingUserId ?? -1;
                ReportDispatch.EventTime = slice.EventTime;
                break;
        }
    }

    public void AddWorkflowEvent(short eventType, short userId, DateTime eventTime)
    {
        switch ((AuditEventType)eventType)
        {
            case AuditEventType.wfResultBundleCreated:
                ResultEntry.PreviousEventTime = eventTime;
                break;
            case AuditEventType.wfResultEntered:
                ResultEntry.UserId = userId;
                ResultEntry.EventTime = eventTime;
                ResultValidation.PreviousEventTime = eventTime;
                break;
            case AuditEventType.wfResultVerified:
                ResultValidation.UserId = userId;
                ResultValidation.EventTime = eventTime;
                ResultFinalization.PreviousEventTime = eventTime;
                break;
            case AuditEventType.wfResultFinalized:
                ResultFinalization.UserId = userId;
                ResultFinalization.EventTime = eventTime;
                ReportCollation.PreviousEventTime = eventTime;
                break;
            case AuditEventType.wfReportCollated:
                ReportCollation.UserId = userId;
                ReportCollation.EventTime = eventTime;
                ReportDispatch.PreviousEventTime = eventTime;
                break;
            case AuditEventType.wfReportDispatched:
                ReportDispatch.UserId = userId;
                ReportDispatch.EventTime = eventTime;
                break;
        }
    }
}