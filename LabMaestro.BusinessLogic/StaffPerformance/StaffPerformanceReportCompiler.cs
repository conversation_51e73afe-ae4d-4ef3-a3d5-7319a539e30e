﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: StaffPerformanceReportCompiler.cs 1278 2014-05-20 15:25:03Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class StaffPerformanceReportCompiler
{
    private readonly StaffRegistry _staffRegistry;
    private readonly List<StaffActivityStreams> _staffs;
    private DateTime _dateFrom;
    private DateTime _dateTill;

    public StaffPerformanceReportCompiler(string roleCode, string staffRole)
    {
        RoleCode = roleCode;
        StaffRole = staffRole;
        _staffs = new List<StaffActivityStreams>();
        _staffRegistry = new StaffRegistry(roleCode);
        _staffRegistry.Initialize();
    }

    public string RoleCode { get; private set; }

    public List<StaffActivityStreams> Staffs
    {
        get { return _staffs.OrderBy(s => s.StaffName).ToList(); }
    }

    public List<StaffActivityStreams> TopStaffByAvgResponse { get; private set; }

    public List<StaffActivityStreams> TopStaffByBestResponse { get; private set; }

    public List<StaffActivityStreams> TopStaffByCount { get; private set; }

    public List<StaffActivityStreams> TopStaffByWorstResponse { get; private set; }

    public string StaffRole { get; }

    private StaffActivityStreams findOrAddStaffActivity(short userId)
    {
        var staff = _staffs.SingleOrDefault(u => u.StaffId == userId);
        if (staff == null)
        {
            staff = new StaffActivityStreams(userId, _staffRegistry.GetStaffName(userId));
            _staffs.Add(staff);
        }

        return staff;
    }

    public void CompileReport(WorkflowAuditEventType wfType, DateTime dtFrom, DateTime dtTo)
    {
        _dateFrom = dtFrom.Date;
        _dateTill = SharedUtilities.LastMinuteOfDay(dtTo);

        var wrte = new WorkflowResponseTimeEstimator();
        wrte.Compile(dtFrom, dtTo);
        var wfEvents = wrte.FindWorkflowEventsForType(wfType, _dateFrom, _dateTill);
        foreach (var info in wfEvents)
            if (_staffRegistry.IsStaffEligible(info.UserId))
            {
                var staff = findOrAddStaffActivity(info.UserId);
                staff.AddActivity(info);
            }

        foreach (var staff in _staffs) staff.CompileSummary();

        compileTopReports();
    }

    private void compileTopReports()
    {
        TopStaffByCount = _staffs.OrderByDescending(x => x.ActivityCount).ToList();
        TopStaffByBestResponse = _staffs.OrderBy(x => x.BestResponseTime).ToList();
        TopStaffByWorstResponse = _staffs.OrderByDescending(x => x.WorstResponseTime).ToList();
        TopStaffByAvgResponse = _staffs.OrderBy(x => x.AverageResponseTime).ToList();
    }

    public StaffPerformanceReportPrintDto ToPrintDto()
    {
        var dto = new StaffPerformanceReportPrintDto
        {
            DateFrom = SharedUtilities.DateToStringShort(_dateFrom),
            DateTill = SharedUtilities.DateToStringShort(_dateTill),
            StaffRole = StaffRole
        };

        foreach (var staff in Staffs) dto.AllStaff.Add(staff.ToPrintDto());

        foreach (var staff in TopStaffByCount) dto.BestStaffByActivity.Add(staff.ToPrintDto());

        foreach (var staff in TopStaffByAvgResponse) dto.BestStaffByResponseTime.Add(staff.ToPrintDto());
        return dto;
    }
}