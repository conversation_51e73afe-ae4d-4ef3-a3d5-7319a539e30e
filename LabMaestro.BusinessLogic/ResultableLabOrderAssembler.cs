﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ResultableLabOrderAssembler.cs 1511 2014-11-18 11:54:36Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Linq;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public static class ResultableLabOrderAssembler
{
    public static ResultableLabOrder AssembleResultBundlesChain(long invoiceId)
    {
        var qryOrder = FaultHandler.Shield(() => PatientLabOrdersRepository.GetLabOrderFullDetails(invoiceId));

        if (qryOrder == null)
            return null;

        // Prevent orders which have already been finalized
        if (qryOrder.WorkflowStage >= (byte)WorkflowStageType.ReportFinalization)
            throw new Exception($"Order #{invoiceId} has already been finalized");

        var labOrder = ResultableLabOrder.AssembleFrom(qryOrder);

        var qryBundles =
            FaultHandler.Shield(() => ResultBundlesRepository.GetActiveResultBundlesInInvoiceForUser(invoiceId,
                CurrentUserContext
                    .UserId));
        if (qryBundles == null) return null;

        labOrder.ResultBundles.AddRange(
            qryBundles.OrderBy(x => x.WorkflowStage)
                .Select(ResultBundleSlice.AssembleFrom)
                .ToList());

        foreach (var bundleSlice in labOrder.ResultBundles)
        {
            var qryTests =
                FaultHandler.Shield(() => OrderedTestsRepository.GetActiveTestsInResultBundle(bundleSlice.Id));
            if (qryTests == null) continue;
            var tests = qryTests.Select(OrderedTestSlice.AssembleFrom).OrderByDescending(t => t.ReportSortPriority)
                .ToList();

            foreach (var testSlice in tests)
                switch (bundleSlice.TestResultType)
                {
                    case TestResultType.Discrete:
                    {
                        var resultItems =
                            FaultHandler.Shield(() => DiscreteResultsRepository
                                .GetDiscreteResultLineItemsForOrderedTest(
                                    testSlice.OrderedTestId,
                                    bundleSlice.Id));
                        if (resultItems != null)
                            foreach (var resultItem in resultItems)
                                testSlice.ResultItems.Add(DiscreteResultLineItemSlice.AssembleFrom(resultItem,
                                    testSlice));
                    }
                        break;
                    case TestResultType.Template:
                    case TestResultType.UserTemplate:
                        testSlice.TemplateResult =
                            FaultHandler.Shield(() => TemplateResultsRepository
                                .GetTemplateResultForOrderedTest(testSlice.OrderedTestId,
                                    bundleSlice.Id));
                        break;
                }

            bundleSlice.OrderedTests.AddRange(tests);
        }

        return labOrder;
    }

    public static WorkflowStageType UpdateLabOrderWorkflowStage(ResultableLabOrder order)
    {
        var wfStage = WorkflowStageType.OrderFulfillment;

        if (order != null && order.HasResultBundles)
        {
            foreach (var bundleSlice in order.ResultBundles)
            {
                var bundleStage = getWorkflowStageForOrderedTests(bundleSlice);
                if (bundleStage < wfStage)
                    wfStage = bundleStage;
            }

            if (order.WorkflowStage != wfStage)
            {
                FaultHandler.Shield(
                    () =>
                        PatientLabOrdersRepository.UpdateLabOrderWorkflowStage(order.InvoiceId, wfStage));
                order.WorkflowStage = wfStage;
            }
        }

        return wfStage;
    }

    private static WorkflowStageType getWorkflowStageForOrderedTests(ResultBundleSlice bundle)
    {
        var wfStage = WorkflowStageType.OrderFulfillment;
        foreach (var testSlice in bundle.OrderedTests)
        {
            var testStage = testSlice.WorkflowStage;
            if (testStage < wfStage)
                wfStage = testStage;
        }

        return wfStage;
    }
}