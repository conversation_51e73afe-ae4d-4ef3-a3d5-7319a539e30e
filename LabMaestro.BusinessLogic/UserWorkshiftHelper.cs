﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id:$
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using CuttingEdge.Conditions;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;

namespace LabMaestro.BusinessLogic;

public static class UserWorkshiftHelper
{
    public static bool UserMustHaveOpenWorkShift()
    {
        return UserMustHaveOpenWorkShift(CurrentUserContext.UserId);
    }

    public static bool UserMustHaveOpenWorkShift(short userId)
    {
        Condition.Requires(userId).IsGreaterThan(0);
        return CachedRolePermissionChecker.UserHasPermission(userId, PermissionCodes.Finances.ManageWorkshift);
    }

    public static bool UserHasOpenWorkShift()
    {
        return UserHasOpenWorkShift(CurrentUserContext.UserId);
    }

    public static bool UserHasOpenWorkShift(short userId)
    {
        Condition.Requires(userId).IsGreaterThan(0);

        var shiftId = FaultHandler.Shield(() => WorkShiftsRepository.GetFirstOpenWorkShiftForUser(userId));
        if (CurrentUserContext.WorkShiftId != shiftId)
            FaultHandler.Shield(() => CurrentUserContext.UpdateShiftInfo(shiftId));
        return shiftId > 0;
    }

    public static void StartNewWorkshift(decimal balance, string note)
    {
        StartNewWorkshift(CurrentUserContext.UserId, balance, note);
    }

    public static void StartNewWorkshift(short userId, decimal balance, string note)
    {
        Condition.Requires(userId).IsGreaterThan(0);
        Condition.Requires(balance).IsGreaterOrEqual(0m);

        FaultHandler.Shield(() =>
        {
            var shiftId = WorkShiftsRepository.StartNewShift(userId, balance, note);
            CurrentUserContext.UpdateShiftInfo(shiftId);
        });
    }

    public static void CloseWorkshift()
    {
        CloseWorkshift(CurrentUserContext.UserId, CurrentUserContext.WorkShiftId);
    }

    public static void CloseWorkshift(short userId, int shiftId)
    {
        Condition.Requires(userId).IsGreaterThan(0);
        Condition.Requires(shiftId).IsGreaterThan(0);

        FaultHandler.Shield(() =>
        {
            WorkShiftsRepository.EndShift(userId, shiftId);
            CurrentUserContext.UpdateShiftInfo(-1);
        });
    }
}