// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ResultBundleSliceInternal.cs 753 2013-07-05 15:59:31Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System.Collections.Generic;
using System.Text;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

internal sealed class ResultBundleSliceInternal
{
    private const int MAX_TEST_NAME_LEN = 16;
    private const int MAX_COMPONENT_NAME_LEN = MAX_TEST_NAME_LEN * 10;

    internal ResultBundleSliceInternal()
    {
        OrderedTests = new List<OrderedTestDetailSlice>();
        WorkflowStage = WorkflowStageType.Unknown;
    }

    internal short LabId { get; set; }

    internal TATRankingType TatRank { get; set; }

    internal TestResultType ResultType { get; set; }

    internal string Name { get; set; }

    internal string ComponentLabTests
    {
        get { return buildComponentName(); }
    }

    internal WorkflowStageType WorkflowStage { get; private set; }

    internal List<OrderedTestDetailSlice> OrderedTests { get; }

    internal void AddTest(OrderedTestDetailSlice slice)
    {
        OrderedTests.Add(slice);
        if (slice.WorkflowStage > WorkflowStage)
            WorkflowStage = slice.WorkflowStage;
    }

    private string buildComponentName()
    {
        if (OrderedTests.Count == 1) return OrderedTests[0].Name;

        var sb = new StringBuilder();
        foreach (var slice in OrderedTests)
        {
            var length = slice.Name.Length > MAX_TEST_NAME_LEN ? MAX_TEST_NAME_LEN : slice.Name.Length;
            sb.Append(slice.Name.Substring(0, length).Trim() + ",");
        }

        if (sb.Length > MAX_COMPONENT_NAME_LEN) sb.Length = MAX_COMPONENT_NAME_LEN;
        return sb.ToString(0, sb.Length - 1);
    }
}