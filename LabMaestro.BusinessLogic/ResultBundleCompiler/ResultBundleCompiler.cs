﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ResultBundleCompiler.cs 1134 2014-01-25 09:34:59Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;

namespace LabMaestro.BusinessLogic;

public sealed class ResultBundleCompiler
{
    private readonly IInvoiceOrderedTestsDataProvider _dataProvider;
    private readonly List<OrderedTestDetailSlice> _orderedTests;
    private readonly Random _rand;
    private readonly Dictionary<string, ResultBundleSliceInternal> _resultBundleSlices;
    private List<short> _allLabIds;

    public ResultBundleCompiler(long invoiceId, IInvoiceOrderedTestsDataProvider dataProvider)
    {
        _resultBundleSlices = new Dictionary<string, ResultBundleSliceInternal>();
        _orderedTests = new List<OrderedTestDetailSlice>();
        InvoiceId = invoiceId;
        _rand = new Random();
        _dataProvider = dataProvider;
    }

    internal List<ResultBundleSliceInternal> ResultBundleSlices
    {
        get { return _resultBundleSlices.Values.ToList(); }
    }

    public long InvoiceId { get; }

    private void fetchOrderedTests()
    {
        _orderedTests.Clear();
        _orderedTests.AddRange(_dataProvider.FetchOrderedTestSlices(InvoiceId));
    }

    public void CompileBundles()
    {
        fetchOrderedTests();
        _resultBundleSlices.Clear();
        _allLabIds = _orderedTests.Select(x => x.LabId).Distinct().ToList();
        foreach (var slice in
                 _orderedTests.OrderByDescending(x => x.ReportSortPriority)) // BUG FIX #95: test result ordering issue
        {
            // skip if the test is an auxiliary procedure/service
            if (slice.IsAuxProcedure)
                continue;

            var bundle = ensureBundleForTest(slice);
            if (bundle != null)
                bundle.AddTest(slice);
        }
    }

    private ResultBundleSliceInternal ensureBundleForTest(OrderedTestDetailSlice slice)
    {
        if (slice.IsCancelled)
            return null;

        ResultBundleSliceInternal bundle;
        if (slice.RequiresExclusiveBundle())
        {
            bundle = new ResultBundleSliceInternal
            {
                LabId = slice.LabId,
                Name = slice.LabName,
                TatRank = slice.TatRank,
                ResultType = slice.ResultType
            };
            _resultBundleSlices.Add(generateUniqueKey(), bundle);
            return bundle;
        }

        var bundleKey = generateBundleKey(slice);
        if (_resultBundleSlices.ContainsKey(bundleKey)) return _resultBundleSlices[bundleKey];

        bundle = new ResultBundleSliceInternal
        {
            LabId = slice.LabId,
            Name = slice.LabName,
            TatRank = slice.TatRank,
            ResultType = slice.ResultType
        };
        _resultBundleSlices.Add(bundleKey, bundle);
        return bundle;
    }

    private string generateBundleKey(OrderedTestDetailSlice slice)
    {
        return string.Format(@"__lab__{0}_{1}", slice.LabId, (byte)slice.TatRank);
    }

    private string generateUniqueKey()
    {
        while (true)
        {
            var rKey = (short)_rand.Next(5000, 10000);
            if (_allLabIds.All(x => x != rKey))
                return string.Format(@"__uniq__{0}", rKey);
        }
    }
}