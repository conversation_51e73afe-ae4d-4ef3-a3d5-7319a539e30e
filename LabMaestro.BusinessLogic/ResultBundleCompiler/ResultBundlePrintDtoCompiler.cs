﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ResultBundlePrintDtoCompiler.cs 1286 2014-05-22 08:30:39Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System.Collections.Generic;
using System.Drawing;
using System.IO;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class ResultBundlePrintDtoCompiler
{
    private readonly long _bundleId;

    public ResultBundlePrintDtoCompiler(long bundleId)
    {
        _bundleId = bundleId;
    }

    public ResultBundlePrintDto CompileBundle()
    {
        var slice = FaultHandler.Shield(() => ResultBundlesRepository
            .GetResultBundleDetailsForReportGeneration(_bundleId));
        if (slice == null)
            return null;

        var dto = new ResultBundlePrintDto
        {
            ResultBundleId = _bundleId,
            LabName = string.IsNullOrEmpty(slice.LabPrintName) ? slice.LabName : slice.LabPrintName,
            ConsultantName = slice.ConsultantName,
            ReportHeader = CompressionUtils.DecompressToString(slice.ReportHeader),
            ResultNotes = CompressionUtils.DecompressToString(slice.ResultNotes),
            ConsultantSignatureText = slice.ConsultantSignatureText
        };

        if (slice.ConsultantSignatureImage != null)
            using (var ms = new MemoryStream(slice.ConsultantSignatureImage))
            {
                dto.ConsultantSignatureImage = new Bitmap(ms);
            }

        return dto;
    }

    public List<DiscreteResultItemPrintDto> GetDiscreteResultItems()
    {
        /*
        var list = new List<DiscreteResultItemPrintDto>();
        var slices = FaultHandler.Shield(() => ResultBundlesRepository
                                                   .GetDiscreteResultLineItemsForReportGeneration(_bundleId));
        if (slices != null)
        {
            list.AddRange(slices.Select(DiscreteResultItemPrintDto.AssembleFrom));
        }
        return list;
        */
        var compiler = new DiscreteGroupedTestReportCompiler(_bundleId);
        compiler.Compile();
        return compiler.GenerateResultLineItems();
    }

    public TemplateResultItemPrintDto GetTemplateResultContent()
    {
        var slice = FaultHandler.Shield(() => TemplateResultsRepository
            .GetTemplateResultForReportGeneration(_bundleId));
        return slice != null ? TemplateResultItemPrintDto.AssembleFrom(slice) : null;
    }
}