﻿//  * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
//  
//  Copyright (c) 2012, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
//  All rights reserved.
//  
//  Redistribution and use in source and binary forms, with or without
//  modification, are permitted provided that the following conditions are met:
//     * Redistributions of source code must retain the above copyright
//       notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above copyright
//       notice, this list of conditions and the following disclaimer in the
//       documentation and/or other materials provided with the distribution.
//     * Neither the name of the <organization> nor the
//       names of its contributors may be used to endorse or promote products
//       derived from this software without specific prior written permission.
//  
//  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
//  ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
//  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
//  DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
//  DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
//  (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
//  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
//  ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
//  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
//  SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
//  
//  $Id: OrderedTestDetailSlice.cs 753 2013-07-05 15:59:31Z <EMAIL> $
//  
//  * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using CuttingEdge.Conditions;
using LabMaestro.Domain;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class OrderedTestDetailSlice
{
    internal long OrderedTestId { get; set; }

    internal short LabTestId { get; set; }

    internal string Name { get; set; }

    internal string LabName { get; set; }

    internal short LabId { get; set; }

    internal TestResultType ResultType { get; set; }

    internal ReqSlipGenerationOptionType ReqSlipPrintOption { get; set; }

    internal byte ReportSortPriority { get; set; }

    internal bool IsCancelled { get; set; }

    internal TATRankingType TatRank { get; set; }

    internal bool IsAuxProcedure { get; set; }

    internal WorkflowStageType WorkflowStage { get; set; }

    internal static OrderedTestDetailSlice AssembleFrom(InvoiceOrderedTest test)
    {
        Condition.Requires(test).IsNotNull();
        /*

                    Mapper.CreateMap<InvoiceOrderedTest, OrderedTestDetailSlice>()
                        .ForMember(dest => dest.TatRank, opt => opt.MapFrom(src => (TATRankingType) src.TATRank))
                        .ForMember(dest => dest.ReqSlipPrintOption,
                                   opt => opt.MapFrom(src => (ReqSlipGenerationOptionType) src.ReqSlipPrintOption))
                        .ForMember(dest => dest.ResultType, opt => opt.MapFrom(src => (TestResultType) src.TestResultType))
                        .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.ShortName));

                    var dto = Mapper.Map<InvoiceOrderedTest, OrderedTestDetailSlice>(test);
        */
        var dto = new OrderedTestDetailSlice
        {
            OrderedTestId = test.OrderedTestId,
            IsCancelled = test.IsCancelled,
            LabTestId = test.TestId,
            Name = test.ShortName,
            LabName = test.LabName,
            LabId = test.LabId,
            TatRank = (TATRankingType)test.TATRank,
            ReqSlipPrintOption = (ReqSlipGenerationOptionType)test.ReqSlipPrintOption,
            ReportSortPriority = test.ReportSortPriority,
            ResultType = (TestResultType)test.TestResultType,
            IsAuxProcedure = test.IsAuxProcedure,
            WorkflowStage = (WorkflowStageType)test.WorkflowStage
        };
        return dto;
    }

    internal bool RequiresExclusiveBundle()
    {
        return ReqSlipPrintOption == ReqSlipGenerationOptionType.GenerateExclusiveSlip ||
               ResultType == TestResultType.Template ||
               ResultType == TestResultType.UserTemplate;
    }
}