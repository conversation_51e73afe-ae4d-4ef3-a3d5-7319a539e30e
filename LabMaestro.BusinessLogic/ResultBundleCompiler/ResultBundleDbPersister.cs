﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ResultBundleDbPersister.cs 827 2013-07-17 08:41:26Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public static class ResultBundleDbPersister
{
    public static void InsertNewResultBundles(ResultBundleCompiler compiler)
    {
        var recencyThreshold = GlobalSettingsHelper.ResultEntry.RecentlyUpdatedBundlesMinimumThreshold;

        foreach (var bundleSlice in compiler.ResultBundleSlices)
            FaultHandler.Shield(() =>
            {
                var bundle = ResultBundlesRepository.InsertResultBundle(CurrentUserContext.UserId,
                    bundleSlice.LabId,
                    compiler.InvoiceId,
                    bundleSlice.TatRank,
                    bundleSlice.WorkflowStage,
                    bundleSlice.ResultType,
                    bundleSlice.Name,
                    bundleSlice.ComponentLabTests,
                    null);
                AuditTrailRepository.LogGenericEvent(AuditEventCategory.System,
                    AuditEventType.wfResultBundleCreated,
                    WorkflowStageType.Unknown,
                    CurrentUserContext.UserId,
                    null,
                    compiler.InvoiceId,
                    null,
                    bundle.Id);

                updateOrderedTestsInBundle(bundle.Id, bundleSlice);

                // in case of an auto-finalized (usually un-archived) bundle, put it in
                // the recently updated history with a low priority
                if (bundleSlice.WorkflowStage >= recencyThreshold)
                    RecentlyUpdatedResultBundlesRepository.AddResultBundleToRecentUpdatesList(
                        bundle.Id,
                        compiler.InvoiceId,
                        bundleSlice.WorkflowStage,
                        SortPriorityType.Low);
            });
    }

    private static void updateOrderedTestsInBundle(long bundleId, ResultBundleSliceInternal bundle)
    {
        foreach (var slice in bundle.OrderedTests)
        {
            OrderedTestsRepository.UpdateTestWithResultBundle(slice.OrderedTestId, bundleId);
            insertDiscreteResultLineItems(slice.OrderedTestId, bundleId);
        }
    }

    private static void insertDiscreteResultLineItems(long ordTestId, long resBundleId)
    {
        var lineItems = DiscreteResultsRepository.GetDiscreteReportLineItemsForOrderedTest(ordTestId);
        foreach (var lineItem in lineItems)
            DiscreteResultsRepository.InsertDiscreteResultLineItem(ordTestId,
                resBundleId,
                lineItem.Parameter,
                lineItem.DefaultResult,
                lineItem.Units,
                lineItem.ReferenceRange,
                lineItem.SortOrder,
                lineItem.IndentLevel,
                lineItem.IsResultableItem);
    }
}