﻿//  * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
//  
//  Copyright (c) 2012, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
//  All rights reserved.
//  
//  Redistribution and use in source and binary forms, with or without
//  modification, are permitted provided that the following conditions are met:
//     * Redistributions of source code must retain the above copyright
//       notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above copyright
//       notice, this list of conditions and the following disclaimer in the
//       documentation and/or other materials provided with the distribution.
//     * Neither the name of the <organization> nor the
//       names of its contributors may be used to endorse or promote products
//       derived from this software without specific prior written permission.
//  
//  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
//  ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
//  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
//  DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
//  DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
//  (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
//  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
//  ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
//  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
//  SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
//  
//  $Id: OrderedTestSlice.cs 516 2013-04-17 08:03:02Z <EMAIL> $
//  
//  * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using LabMaestro.Domain;

namespace LabMaestro.BusinessLogic
{
    internal sealed class OrderedTestSlice
    {
        internal long OrderedTestId { get; set; }

        internal short TestId { get; set; }

        internal string Name { get; set; }

        internal short LabId { get; set; }

        internal TestResultType ResultType { get; set; }

        internal ReqSlipGenerationOptionType ReqSlipPrintOption { get; set; }

        internal byte ReportSortPriority { get; set; }

        internal bool IsCancelled { get; set; }

        internal TATRankingType TatRank { get; set; }

        internal static OrderedTestSlice AssembleFrom(InvoiceOrderedTest test)
        {
            var dto = new OrderedTestSlice
                          {
                              OrderedTestId = test.OrderedTestId,
                              IsCancelled = test.IsCancelled,
                              TestId = test.TestId,
                              Name = test.ShortName,
                              TatRank = (TATRankingType) test.TATRank,
                              ReqSlipPrintOption = (ReqSlipGenerationOptionType) test.ReqSlipPrintOption,
                              ReportSortPriority = test.ReportSortPriority,
                              LabId = test.LabId,
                              ResultType = (TestResultType) test.TestResultType,
                          };

            return dto;
        }

        internal bool RequiresExclusiveBundle()
        {
            return (ReqSlipPrintOption == ReqSlipGenerationOptionType.GenerateExclusiveSlip ||
                    ResultType == TestResultType.Template ||
                    ResultType == TestResultType.UserTemplate);
        }
    }
}