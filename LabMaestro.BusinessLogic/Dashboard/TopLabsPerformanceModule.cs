﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: TopLabsPerformanceModule.cs 1124 2013-12-19 09:57:26Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System.Collections.Generic;
using System.Linq;

namespace LabMaestro.BusinessLogic.Dashboard;

public sealed class TopLabsPerformanceModule
{
    private readonly Databank _bank;
    private readonly int _maxCount;

    internal TopLabsPerformanceModule(Databank bank, int max)
    {
        _bank = bank;
        _maxCount = max;
    }

    public List<PerformanceInfo> TopPerformers { get; set; }

    public void Process()
    {
        var dict = new Dictionary<short, PerformanceInfo>();
        var activeTests = _bank.OrderedTests.Where(t => !t.IsCancelled).ToList();
        foreach (var slice in activeTests)
        {
            PerformanceInfo item;
            if (!dict.TryGetValue(slice.LabId, out item)) item = PerformanceInfo.AssembleFrom(slice);
            item.AppendStats(slice.UnitPrice, 1);
        }

        var totalRevenue = dict.Values.Sum(l => l.Revenue);
        TopPerformers = dict.Values.OrderBy(l => l.Revenue).Take(_maxCount).ToList();
        TopPerformers.ForEach(l => l.UpdatePercent(totalRevenue));
    }
}