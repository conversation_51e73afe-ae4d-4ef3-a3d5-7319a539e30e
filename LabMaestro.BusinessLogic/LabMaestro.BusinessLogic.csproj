<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{A13D1C5D-3BBF-4E7D-88FC-47BA5DCCCD83}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>LabMaestro.BusinessLogic</RootNamespace>
    <AssemblyName>LabMaestro.BusinessLogic</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
    <SolutionDir Condition="$(SolutionDir) == '' Or $(SolutionDir) == '*Undefined*'">..\LabMaestro\</SolutionDir>
    <LangVersion>latestmajor</LangVersion>
    <Nullable>annotations</Nullable>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <PlatformTarget>x86</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>
    </DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup>
    <SignAssembly>false</SignAssembly>
  </PropertyGroup>
  <PropertyGroup>
    <AssemblyOriginatorKeyFile>labmaestro.snk</AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="C1.LiveLinq.4">
      <HintPath>..\libs\C1.LiveLinq.4.dll</HintPath>
    </Reference>
    <Reference Include="Syncfusion.Compression.Base, Version=30.1462.37.0, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89, processorArchitecture=MSIL" />
    <Reference Include="Syncfusion.XlsIO.Base, Version=30.1462.37.0, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89, processorArchitecture=MSIL" />
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Runtime.Caching" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AuditTrail\AuditEventSlice.cs" />
    <Compile Include="AuditTrail\AuditResultBundle.cs" />
    <Compile Include="AuditTrail\AuditResultBundlesList.cs" />
    <Compile Include="AuditTrail\AuditTrailComposer.cs" />
    <Compile Include="BillableItemUtilizationReportCompiler\BillableItemUtilization.cs" />
    <Compile Include="BillableItemUtilizationReportCompiler\BillableItemUtilizationReportCompiler.cs" />
    <Compile Include="BulkDispatch\BulkDispatchableResultBundleInfo.cs" />
    <Compile Include="BulkDispatch\ResultBundlesBulkDispatchManager.cs" />
    <Compile Include="CachedRolePermissionChecker.cs" />
    <Compile Include="Catalog\BillableItemsDataAccessProviderDb.cs" />
    <Compile Include="Catalog\DiscountLevelsDataAccessProviderDb.cs" />
    <Compile Include="Catalog\ICatalogDataAccessProvider.cs" />
    <Compile Include="Catalog\LabReportHeadersDataAccessProvider.cs" />
    <Compile Include="Catalog\LabsDataAccessProviderDb.cs" />
    <Compile Include="Catalog\LabTestsDataAccessProviderDb.cs" />
    <Compile Include="Catalog\ReferralGroupsDataAccessProviderDb.cs" />
    <Compile Include="Catalog\ReferrerCatalogExporter.cs" />
    <Compile Include="Catalog\ReferrerCategoryDataAccessProviderDb.cs" />
    <Compile Include="CRM\CrmNotification.cs" />
    <Compile Include="CRM\CustomerInvoiceSlice.cs" />
    <Compile Include="CRM\CustomerLoginSearchProvider.cs" />
    <Compile Include="CRM\PatientDemographicInfo.cs" />
    <Compile Include="CRM\SimilarCustomerMatcher.cs" />
    <Compile Include="CRM\CustomerPhoneSearchProvider.cs" />
    <Compile Include="CRM\CustomerRegistrationService.cs" />
    <Compile Include="CRM\CustomerUpinGenerator.cs" />
    <Compile Include="CRM\CustomerUPINSearchProvider.cs" />
    <Compile Include="CRM\ICustomerSearchProvider.cs" />
    <Compile Include="Dashboard\DailyRevenueStatsModule.cs" />
    <Compile Include="Dashboard\DashboardCompiler.cs" />
    <Compile Include="Dashboard\Databank.cs" />
    <Compile Include="Dashboard\IDashboardModule.cs" />
    <Compile Include="Dashboard\PerformanceInfo.cs" />
    <Compile Include="Dashboard\TopLabsPerformanceModule.cs" />
    <Compile Include="Dashboard\TopReferrersPerformanceModule.cs" />
    <Compile Include="Discounts\ApplicableDiscount.cs" />
    <Compile Include="Discounts\DiscountContext.cs" />
    <Compile Include="Discounts\DiscountInfo.cs" />
    <Compile Include="Discounts\DiscountEstimationPipeline.cs" />
    <Compile Include="Discounts\DiscountInfoType.cs" />
    <Compile Include="Discounts\DiscountPipelineSteps.cs" />
    <Compile Include="Discounts\DiscountService.cs" />
    <Compile Include="DiscreteGroupedTestReportCompiler\DiscreteGroupedTestReportCompiler.cs" />
    <Compile Include="DiscreteGroupedTestReportCompiler\DiscreteTestReportGroup.cs" />
    <Compile Include="DiscreteGroupedTestReportCompiler\DiscreteOrderedTestResults.cs" />
    <Compile Include="GlobalSettingsHelper.cs" />
    <Compile Include="InvoiceTransactionsManager.cs" />
    <Compile Include="LabOrderDemographicHistory.cs" />
    <Compile Include="LabOrderMirrorFlagger.cs" />
    <Compile Include="LabOrderSearch\ActiveLabOrderFilterByDateRangeProvider.cs" />
    <Compile Include="LabOrderSearch\ActiveLabOrderSearchByDateRangeProvider.cs" />
    <Compile Include="LabOrderSearch\ILabOrderSearchProvider.cs" />
    <Compile Include="LabOrderFinancialContext.cs" />
    <Compile Include="LabOrderSearch\ActiveLabOrderFilterByWorkflowAndDateRangeProvider.cs" />
    <Compile Include="LabOrderSearch\ActiveLabOrderSearchByInvoiceIdProvider.cs" />
    <Compile Include="LabOrderSearch\ActiveLabOrderSearchByPatientIdAndDateRangeProvider.cs" />
    <Compile Include="LabOrderSearch\ActiveLabOrderSearchByWorkflowAndDateRangeProvider.cs" />
    <Compile Include="LabOrderSearch\LabOrderSearchByAffiliateDateRangeProvider.cs" />
    <Compile Include="LabOrderSearch\LabOrderSearchByAssocLabDateRangeProvider.cs" />
    <Compile Include="LabOrderSearch\LabOrderSearchByCorporateDateRangeProvider.cs" />
    <Compile Include="LabOrderSearch\LabOrderSearchByCustomerUPINAndDateRangeProvider.cs" />
    <Compile Include="LabOrderSearch\LabOrderSearchByDateRangeAndResultBundleWorkflowBetweenProvider.cs" />
    <Compile Include="LabOrderSearch\LabOrderSearchByDateRangeProvider.cs" />
    <Compile Include="LabOrderSearch\LabOrderSearchByInvoiceIdProvider.cs" />
    <Compile Include="LabOrderSearch\LabOrderSearchByPatientIdAndDateRangeProvider.cs" />
    <Compile Include="LabOrderSearch\LabOrderSearchByPhoneAndDateRangeProvider.cs" />
    <Compile Include="LabOrderSearch\LabOrderSearchByReferrerIdAndDateRangeProvider.cs" />
    <Compile Include="LabOrderSearch\LabOrderSearchByWorkflowAndDateRangeProvider.cs" />
    <Compile Include="LabOrderSearch\LabOrderSearchByDateRangeAndResultBundleWorkflowProvider.cs" />
    <Compile Include="LabOrderSearch\SearchedLabOrderInfo.cs" />
    <Compile Include="Notification\MailComposer.cs" />
    <Compile Include="PatientDetailsParser.cs" />
    <Compile Include="ReferralCalc\ReferralSummaryExporter.cs" />
    <Compile Include="Reporting\DailyInvoicesCollection.cs" />
    <Compile Include="Reporting\DiscountOverageInfoSlice.cs" />
    <Compile Include="Reporting\DiscountOverageReportBuilder.cs" />
    <Compile Include="Reporting\GroupedIncomeStatement\GroupedLabOrderDetails.cs" />
    <Compile Include="Reporting\GroupedIncomeStatement\GroupedLabOrdersCollection.cs" />
    <Compile Include="Reporting\GroupedIncomeStatement\GroupedOrderedTestDetails.cs" />
    <Compile Include="Reporting\GroupedIncomeStatement\LabReportGroup.cs" />
    <Compile Include="Reporting\InvoiceFinancialInfoSlice.cs" />
    <Compile Include="Reporting\LabwiseDailyIncomeReportBuilder.cs" />
    <Compile Include="Reporting\ReferralEligibilitySummaryReportBuilder.cs" />
    <Compile Include="StaffPerformance\InvoiceWorkflowEvents.cs" />
    <Compile Include="StaffPerformance\ResultBundleWorkflowEvents.cs" />
    <Compile Include="StaffPerformance\StaffActivityStreams.cs" />
    <Compile Include="StaffPerformance\StaffPerformanceReportCompiler.cs" />
    <Compile Include="StaffPerformance\StaffWorkflowActivityInfo.cs" />
    <Compile Include="StaffPerformance\StaffRegistry.cs" />
    <Compile Include="StaffPerformance\WorkflowAuditEventInfo.cs" />
    <Compile Include="StaffPerformance\WorkflowAuditEventType.cs" />
    <Compile Include="StaffPerformance\WorkflowResponseTimeEstimator.cs" />
    <Compile Include="WorkflowStage\IWorkflowStageHook.cs" />
    <Compile Include="WorkflowStage\LabOrderWorkflowStageEstimator.cs" />
    <Compile Include="NewLabOrderCreator.cs" />
    <Compile Include="OrderCompiler\BillableItemsCompiler.cs" />
    <Compile Include="OrderCompiler\BillableItemsTriageGroup.cs" />
    <Compile Include="OrderCompiler\ILabOrderEditorPersister.cs" />
    <Compile Include="OrderCompiler\InvoiceCalculator.cs" />
    <Compile Include="OrderCompiler\InvoiceSummary.cs" />
    <Compile Include="OrderCompiler\LabOrderCompiler.cs" />
    <Compile Include="OrderCompiler\LabOrderContextInfo.cs" />
    <Compile Include="LabOrderDemographicInfoSlice.cs" />
    <Compile Include="OrderCompiler\LabOrderEditor.cs" />
    <Compile Include="OrderCompiler\LabOrderEditorDbPersister.cs" />
    <Compile Include="OrderCompiler\OrphanedResultBundlesScavenger.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="RecentlyUpdatedLabOrder\RecentlyUpdatedResultBundlesProvider.cs" />
    <Compile Include="RecentlyUpdatedLabOrder\RecentlyUpdatedResultBundlesFetcherJob.cs" />
    <Compile Include="RecentlyUpdatedLabOrder\RecentlyUpdatedResultBundlesReceiver.cs" />
    <Compile Include="ReferralCalc\GroupedReferralDetail.cs" />
    <Compile Include="ReferralCalc\ReferralCalculator.cs" />
    <Compile Include="ReferralCalc\ReferralEligibleLabTestsCatalog.cs" />
    <Compile Include="ReferralCalc\GroupedReferralDetailsList.cs" />
    <Compile Include="ReferralCalc\ReferralLabTest.cs" />
    <Compile Include="ReferralCalc\ReferralPeriod.cs" />
    <Compile Include="ReferralCalc\ReferredLabOrderDetails.cs" />
    <Compile Include="ReferralCalc\ReferrerCatalog.cs" />
    <Compile Include="ReferralCalc\ReferrerSummaryInfo.cs" />
    <Compile Include="Reporting\CollatorInvoiceSlice.cs" />
    <Compile Include="Reporting\CollatorResultBundleSlice.cs" />
    <Compile Include="Reporting\CollatorsManifestReportBuilder.cs" />
    <Compile Include="Reporting\DailyReceivablesReportBuilder.cs" />
    <Compile Include="Reporting\DuesRegisterReportBuilder.cs" />
    <Compile Include="Reporting\FilterMode.cs" />
    <Compile Include="Reporting\FinancialAuditInvoiceMasterSlice.cs" />
    <Compile Include="Reporting\FinancialAuditInvoiceTransactionSlice.cs" />
    <Compile Include="Reporting\IncomeStatementReportBuilder.cs" />
    <Compile Include="Reporting\InvoiceFinancialAuditTrailReportBuilder.cs" />
    <Compile Include="Reporting\InvoiceOutstandingDueCollectionSlice.cs" />
    <Compile Include="Reporting\InvoiceTransactionDetailsReportBuilder.cs" />
    <Compile Include="Reporting\InvoiceTransactionInfoSlice.cs" />
    <Compile Include="Reporting\OrderCancellationReportBuilder.cs" />
    <Compile Include="Reporting\OutstandingDuesCollectionReportBuilder.cs" />
    <Compile Include="Reporting\OutstandingStatementReportBuilder.cs" />
    <Compile Include="Reporting\ReceivablesSummaryReportBuilder.cs" />
    <Compile Include="Reporting\ReferrerBusinessContributionSlice.cs" />
    <Compile Include="Reporting\ReferrerBusinessContributionsReportBuilder.cs" />
    <Compile Include="Reporting\UnknownReferrersReportBuilder.cs" />
    <Compile Include="Reporting\UserDailyReceivablesSummary.cs" />
    <Compile Include="Reporting\UserFinancialAuditSummary.cs" />
    <Compile Include="Reporting\UserShiftInfoSlice.cs" />
    <Compile Include="Reporting\ReceptionistSummaryReportBuilder.cs" />
    <Compile Include="Reporting\UserSummarySlice.cs" />
    <Compile Include="ReqSlipBundleCompiler.cs" />
    <Compile Include="ResultableLabOrderAssembler.cs" />
    <Compile Include="ResultBundleCompiler\ActiveResultBundleSlice.cs" />
    <Compile Include="ResultBundleCompiler\EditedInvoiceOrderedTestsDataProvider.cs" />
    <Compile Include="ResultBundleCompiler\IInvoiceOrderedTestsDataProvider.cs" />
    <Compile Include="ResultBundleCompiler\InvoiceOrderedTestsDataProvider.cs" />
    <Compile Include="ResultBundleCompiler\CachedResultBundleSearchProvider.cs" />
    <Compile Include="ResultBundleCompiler\OrderedTestDetailSlice.cs" />
    <Compile Include="ResultBundleCompiler\ResultBundleCompiler.cs" />
    <Compile Include="ResultBundleCompiler\ResultBundleDbPersister.cs" />
    <Compile Include="ResultBundleCompiler\ResultBundlePrintDtoCompiler.cs" />
    <Compile Include="ResultBundleCompiler\ResultBundleSliceInternal.cs" />
    <Compile Include="ServerHeartbeatChecker.cs" />
    <Compile Include="ServiceBusDiagnosticUtilities.cs" />
    <Compile Include="ShiftManagement\InvoiceInShiftSlice.cs" />
    <Compile Include="ShiftManagement\WorkShiftSummaryCompiler.cs" />
    <Compile Include="TestUtilizationReportCompiler\LabTestUtilization.cs" />
    <Compile Include="TestUtilizationReportCompiler\LabTestUtilizationReportCompiler.cs" />
    <Compile Include="TestUtilizationReportCompiler\LabUtilization.cs" />
    <Compile Include="TestUtilizationReportCompiler\PhlebotomyUtilizationInfo.cs" />
    <Compile Include="TestUtilizationReportCompiler\PhlebotomyUtilizationReportCompiler.cs" />
    <Compile Include="TransactionInfoSlice.cs" />
    <Compile Include="UserWorkshiftHelper.cs" />
    <Compile Include="WorkflowStage\LabOrderWorkflowStageHooksRegistry.cs" />
    <Compile Include="WorkflowStage\ResultBundleWorkflowStageHooksRegistry.cs" />
    <Compile Include="WorkflowStage\WorkflowStageHooksRegistryBase.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\LabMaestro.Domain\LabMaestro.Domain.csproj">
      <Project>{72642B75-E640-41A5-A394-FB8E95B17599}</Project>
      <Name>LabMaestro.Domain</Name>
    </ProjectReference>
    <ProjectReference Include="..\LabMaestro.Infrastructure.Client\LabMaestro.Infrastructure.Client.csproj">
      <Project>{0F4FD7B4-6342-44FA-B190-FF9F482E8798}</Project>
      <Name>LabMaestro.Infrastructure.Client</Name>
    </ProjectReference>
    <ProjectReference Include="..\LabMaestro.Infrastructure.Search\LabMaestro.Infrastructure.Search.csproj">
      <Project>{ABB2C9E0-BF9C-4DCF-951F-D8BA032A2E5C}</Project>
      <Name>LabMaestro.Infrastructure.Search</Name>
    </ProjectReference>
    <ProjectReference Include="..\LabMaestro.Printing\LabMaestro.Printing.csproj">
      <Project>{32d5bffa-5141-461e-985f-58a219660599}</Project>
      <Name>LabMaestro.Printing</Name>
    </ProjectReference>
    <ProjectReference Include="..\LabMaestro.Shared\LabMaestro.Shared.csproj">
      <Project>{B15584A4-1EA1-48AA-A823-29307BB8D16A}</Project>
      <Name>LabMaestro.Shared</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="labmaestro.snk" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="AutoMapper">
      <Version>2.2.1</Version>
    </PackageReference>
    <PackageReference Include="Common.Logging.Core">
      <Version>3.4.1</Version>
    </PackageReference>
    <PackageReference Include="CuttingEdge.Conditions">
      <Version>1.2.0</Version>
    </PackageReference>
    <PackageReference Include="FluentDateTime">
      <Version>3.0.0</Version>
    </PackageReference>
    <PackageReference Include="IdeaBlade.DevForce.Core">
      <Version>7.5.5</Version>
    </PackageReference>
    <PackageReference Include="MimeKitLite">
      <Version>4.13.0</Version>
    </PackageReference>
    <PackageReference Include="NameParserSharp">
      <Version>1.5.0</Version>
    </PackageReference>
    <PackageReference Include="Newtonsoft.Json">
      <Version>13.0.3</Version>
    </PackageReference>
    <PackageReference Include="NinjaNye.SearchExtensions">
      <Version>4.0.1</Version>
    </PackageReference>
    <PackageReference Include="NinjaNye.SearchExtensions.Soundex">
      <Version>4.0.1</Version>
    </PackageReference>
    <PackageReference Include="Slugify.Core">
      <Version>5.1.1</Version>
    </PackageReference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="resources\customer-registration-email.txt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
         Other similar extension points exist, see Microsoft.Common.targets.
    <Target Name="BeforeBuild">
    </Target>
    <Target Name="AfterBuild">
    </Target>
    -->
</Project>