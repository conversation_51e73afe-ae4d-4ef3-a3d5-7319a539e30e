﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: BulkDispatchableResultBundleInfo.cs 1283 2014-05-22 06:21:17Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class BulkDispatchableResultBundleInfo
{
    public BulkDispatchableResultBundleInfo()
    {
        ResultBundleId = -1;
        InvoiceId = -1;
    }

    public long ResultBundleId { get; set; }
    public long InvoiceId { get; set; }
    public int ResultBundlesCount { get; set; }
    public string OrderId { get; set; }
    public DateTime OrderDateTime { get; set; }
    public string PatientName { get; set; }
    public string ReferrerName { get; set; }
    public string ResultBundleTitle { get; set; }
    public TestResultType ResultBundleType { get; set; }
    public WorkflowStageType ResultBundleStage { get; set; }

    public static BulkDispatchableResultBundleInfo AssembleFrom(ActiveResultBundleWithInvoiceDetailsSlice slice)
    {
        return new BulkDispatchableResultBundleInfo
        {
            ResultBundleId = slice.BundleId,
            InvoiceId = slice.InvoiceId,
            OrderDateTime = slice.OrderDateTime,
            OrderId = slice.OrderId,
            PatientName = slice.PatientName,
            ResultBundleTitle = slice.BundleTitle,
            ResultBundleStage = (WorkflowStageType)slice.BundleWorkflowStage,
            ResultBundleType = (TestResultType)slice.BundleResultType
        };
    }

    public BulkDispatchableResultBundleInfo UpdateCount()
    {
        if (InvoiceId > 0)
            ResultBundlesCount = FaultHandler.Shield(() =>
                ResultBundlesRepository.GetActiveResultBundlesCountForInvoice(InvoiceId));
        return this;
    }

    public bool CheckValidity()
    {
        if (ResultBundleId <= 0)
            return false;
        var slice = FaultHandler.Shield(() =>
            ResultBundlesRepository.GetActiveResultBundleWithInvoiceDetails(ResultBundleId));
        return slice != null &&
               (WorkflowStageType)slice.BundleWorkflowStage == WorkflowStageType.ReportCollation &&
               slice.DueAmount == 0m;
    }
}