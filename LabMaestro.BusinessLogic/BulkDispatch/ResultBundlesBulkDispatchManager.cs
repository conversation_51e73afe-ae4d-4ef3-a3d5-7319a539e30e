﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ResultBundlesBulkDispatchManager.cs 1434 2014-10-01 07:45:37Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using CuttingEdge.Conditions;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class ResultBundlesBulkDispatchManager
{
    private readonly List<BulkDispatchableResultBundleInfo> _eligibleBundles;

    public ResultBundlesBulkDispatchManager()
    {
        _eligibleBundles = new List<BulkDispatchableResultBundleInfo>();
    }

    public int EligibleBundlesCount
    {
        get { return _eligibleBundles.Count; }
    }

    public List<BulkDispatchableResultBundleInfo> EligibleBundles
    {
        get { return _eligibleBundles.ToList(); }
    }

    public string ErrorMessage { get; private set; }
    public int DispatchedBundlesCount { get; private set; }

    public bool AlreadyAdded(long bundleId)
    {
        return _eligibleBundles.SingleOrDefault(x => x.ResultBundleId == bundleId) != null;
    }

    public void SearchAndAddEligibleBundle(long bundleId)
    {
        Condition.Requires(bundleId).IsGreaterThan(0);

        var slice = FaultHandler.Shield(() =>
            ResultBundlesRepository.GetActiveResultBundleWithInvoiceDetails(bundleId));

        if (slice == null) throw new Exception("No reports were found or report had been cancelled.");

        if ((WorkflowStageType)slice.BundleWorkflowStage != WorkflowStageType.ReportCollation)
            throw new Exception("This report is not eligible for dispatch.");

        if (slice.DueAmount > 0m)
            throw new Exception(string.Format("This invoice still has unpaid dues of Tk. {0}!",
                SharedUtilities.MoneyToStringPlainCulture(slice.DueAmount)));

        _eligibleBundles.Add(BulkDispatchableResultBundleInfo.AssembleFrom(slice).UpdateCount());
    }

    public void PerformBulkDispatch()
    {
        var eligibleList = new List<BulkDispatchableResultBundleInfo>(_eligibleBundles.Count);
        eligibleList.AddRange(_eligibleBundles.Where(x => x.CheckValidity()));
        ErrorMessage = string.Empty;
        if (eligibleList.Count > 0) performBulkDispatch(eligibleList);
    }

    private void performBulkDispatch(List<BulkDispatchableResultBundleInfo> bundles)
    {
        var sbFailed = new StringBuilder();
        var uniqueInvoices = new List<long>();
        var successCount = 0;

        foreach (var bundle in bundles)
        {
            var wfBundle = bundle.ResultBundleStage;

            // Skip if over
            if (wfBundle >= WorkflowStageType.ReportDispatch) continue;

            var theBundle = bundle;
            FaultHandler.Shield(() =>
            {
                if (
                    ResultBundlesRepository.VerifyResultBundleWorkflowStageMatch(
                        theBundle.ResultBundleId, wfBundle))
                {
                    ResultBundlesRepository.UpdateResultBundleWorkflowStage(
                        theBundle.ResultBundleId,
                        WorkflowStageType.ReportDispatch);

                    AsyncEsbMessenger.SendMessagesAsync(
                        EsbMessageChain.CreateNew()
                            .AddResultBundleWorkflowHook(theBundle.InvoiceId,
                                theBundle.ResultBundleId));


                    AuditTrailRepository.LogOrderWorkflowEvent(
                        AuditEventType.wfReportDispatched,
                        theBundle.InvoiceId,
                        theBundle.ResultBundleId,
                        null,
                        WorkflowStageType.ReportDispatch);

                    // end of the line...
                    RecentlyUpdatedResultBundlesRepository
                        .RemoveResultBundleFromRecentUpdatesList(
                            theBundle.ResultBundleId);

                    if (!uniqueInvoices.Contains(theBundle.InvoiceId))
                        uniqueInvoices.Add(theBundle.InvoiceId);
                    successCount++;
                }
                else
                {
                    sbFailed.AppendLine(string.Format("{0} - {1}", theBundle.ResultBundleId,
                        theBundle.ResultBundleTitle));
                }
            });
        }

        DispatchedBundlesCount = successCount;
        /*
        var estimator = new LabOrderWorkflowStageEstimator(order.InvoiceId);
        estimator.EstimateWorkflowStage();
        */
        foreach (var id in uniqueInvoices)
        {
            //AppSysRepository.SendLabOrderWorkflowStageEstimateRequest(order.InvoiceId);
            var invoiceId = id;
            FaultHandler.Shield(() => AsyncEsbMessenger.SendMessagesAsync(
                EsbMessageChain.CreateNew().AddWorkflowEstimator(invoiceId)));

            // send delayed SMS hook request
            AsyncEsbMessenger.SendMessagesAsync(
                EsbMessageChain.CreateNew(AsyncEsbMessenger.DealyedSleepSeconds).AddLabOrderWorkflowHook(invoiceId));
        }

        if (sbFailed.Length > 0)
        {
            var msg = "The following report bundles could not be dispatched:\n" + sbFailed;
            msg += "\nSomeone else may have processed these report bundles in the meantime.";
            msg += "\nPlease reload the lab order to fetch latest updates.";
            ErrorMessage = msg;
        }
    }

    public void RemoveBundle(BulkDispatchableResultBundleInfo info)
    {
        _eligibleBundles.Remove(info);
    }

    public void Reset()
    {
        _eligibleBundles.Clear();
        ErrorMessage = string.Empty;
        DispatchedBundlesCount = 0;
    }
}