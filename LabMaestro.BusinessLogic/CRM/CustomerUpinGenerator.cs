﻿using System;
using System.Linq;
using LabMaestro.Domain;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic.CRM;

public sealed class CustomerUpinGenerator
{
    private const string CHAR_TABLE = "23456789ABCDEFGHJKLMNPQRSTUVWXYZ";
    private static Random _random = new();

    public static int GetUPINLength()
    {
        var globalLen = GlobalSettingsRepository.GetIntValue("crm.upin_length");
        if (globalLen < 4)
        {
            var count = (int)AppCache.Fetch("crm.customers_count", () => CustomerRepository.Count(), 30);
            var result = count switch
            {
                < 5_000 => 4,
                < 10_000 => 5,
                < 100_000 => 6,
                _ => 7
            };

            if (result != globalLen)
                GlobalSettingsRepository.Save("crm.upin_length", result, null);

            return result;
        }

        return globalLen;
    }

    private static string randomCode(int length) =>
        new(Enumerable.Repeat(CHAR_TABLE, length).Select(s => s[_random.Next(s.Length)]).ToArray());

    public static string GenerateUPIN(int length = -1)
    {
        length = length < 4 ? GetUPINLength() : length;
        var upin = randomCode(length);
        while (true)
        {
            if (!CustomerRepository.UPINExists(upin))
                break;

            upin = randomCode(length);
        }

        return upin;
    }
}