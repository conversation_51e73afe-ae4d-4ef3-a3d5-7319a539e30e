﻿using System;
using System.Collections.Generic;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Shared;
using LabMaestro.Shared.Domain;

namespace LabMaestro.BusinessLogic.CRM;

public class CustomerRegistrationService
{
    public long? SubscriptionId { get; private set; }
    public long? InvoiceId { get; private set; }
    public short? StaffId { get; set; }
    public CustomerInfo? RegisteredCustomer { get; private set; }
    public CustomerInfo Customer { get; set; }
    public PlanInfo Plan { get; set; }
    public decimal AmountPaid { get; set; }
    public string Password { get; set; }

    public bool PreFlightCheck() => CustomerRepository.LoginOrPhoneAlreadyRegistered(Customer.Login, Customer.Phone);

    public void Execute()
    {
        if (Customer == null || Plan == null)
            throw new InvalidOperationException("Customer and Plan must be set before registration.");

        InitializeNewCustomer();
        if (RegisteredCustomer == null)
            throw new InvalidOperationException("Customer registration failed.");

        CreateSubscription();
        if (SubscriptionId == null)
            throw new InvalidOperationException("Customer subscription failed.");

        CreateInvoice();
        if (InvoiceId == null)
            throw new InvalidOperationException("Customer invoicing failed.");

        NotifyCustomer();
    }

    private void InitializeNewCustomer()
    {
        Customer.SetUPIN(CustomerUpinGenerator.GenerateUPIN());
        Customer.EnrollingUserId ??= StaffId;
        var customer = CustomerRepository.CreateFrom(Customer, Password);
        if (customer != null)
            RegisteredCustomer = CustomerInfo.AssembleFrom(customer);
    }

    private void CreateSubscription()
    {
        var subscription = new Subscription
        {
            CustomerId = RegisteredCustomer.Id,
            PlanId = Plan.Id,
            PerformingUserId = (short)StaffId,
            PlanName = Plan.Name,
            AmountPaid = AmountPaid,
            IsActive = true,
            WasSwitched = false,
            StartedAt = Plan.StartDate,
            ExpiresAt = Plan.ExpiryDate
        };
        SubscriptionsRepository.Add(subscription);
        SubscriptionsRepository.Save();

        SubscriptionId = subscription.Id;
    }

    private void CreateInvoice()
    {
        var invoice = new Invoice
        {
            CustomerId = RegisteredCustomer.Id,
            SubscriptionId = (long)SubscriptionId,
            InvoiceAmount = Plan.ListPrice,
            PlanListPrice = Plan.ListPrice,
            PaidAmount = AmountPaid,
            PaymentMethod = (byte)PaymentMethod.Cash,
            PaymentGateway = (byte)(AmountPaid > 0 ? PaymentGatewayType.Cash : PaymentGatewayType.Other),
            PerformingStaffId = StaffId,
            //InvoiceDate = DateTime.Now,
        };
        SubscriptionInvoicesRepository.Add(invoice);
        SubscriptionInvoicesRepository.Save();

        InvoiceId = invoice.Id;
    }

    private void NotifyCustomer()
    {
        var notification = CrmNotification.Make();
        notification.CustomerId = RegisteredCustomer.Id;
        notification.CustomerName = RegisteredCustomer.GetName();
        notification.CustomerUPIN = RegisteredCustomer.UPIN;
        notification.Phone = RegisteredCustomer.Phone;
        notification.Email = RegisteredCustomer.Email;
        notification.Login = RegisteredCustomer.Login;
        notification.Password = Password;
        notification.PlanName = Plan.Name;
        notification.PlanId = Plan.Id;
        notification.SubscriptionId = SubscriptionId;
        notification.SubscriptionStart = Plan.StartDate;
        notification.SubscriptionEnd = Plan.ExpiryDate;
        notification.InvoiceId = InvoiceId;

        var chain = EsbMessageChain.CreateNew();

        if (SharedUtilities.IsValidMobileNumber(RegisteredCustomer.Phone))
        {
            // todo: get SMS template from DB
            var shortMessage =
                $"Your CHEVRON APP Login: {RegisteredCustomer.Login.ToUpperInvariant()}\nPassword: {Password}\nVisit http://MYLABCTG.COM/";
            var sms = SmsMessageRequest.Create(SharedUtilities.GetE164Phone(RegisteredCustomer.Phone), shortMessage);
            chain.AddSmsRequest(sms);
        }

        if (SharedUtilities.IsValidEmail(RegisteredCustomer.Email))
        {
            // todo: send Invoice and login data via email
            // todo: craft email message from template
            var content =
                $"Your CHEVRON APP Login: {RegisteredCustomer.Login.ToUpperInvariant()}\nPassword: {Password}\nVisit https://MYLABCTG.COM/";
            var email = new EmailMessageRequest
            {
                Subject = "Your Chevron app login",
                Sender = "<EMAIL>",
                Recipient = RegisteredCustomer.Email,
                Message = content
            };
            chain.AddEmailRequest(email);
        }

        AsyncEsbMessenger.SendMessagesAsync(chain);
    }

    public static List<string> SimilarLoginNames(string namePattern)
        => CustomerRepository.SimilarLoginNames(namePattern.ToUpperInvariant());

    public static string GenerateUniqueLoginName(string sourceName)
    {
        sourceName = sourceName.ToUpperInvariant();
        var existingNames = SimilarLoginNames(sourceName);
        if (existingNames.Count == 0) return sourceName;

        for (var j = 1; j < 999; j++)
        {
            var remainder = j % 10;
            if (remainder < 2) continue; // skip 0,1

            var fn = $"{sourceName}{j}";
            if (!existingNames.Contains(fn)) return fn;
        }

        return sourceName + SharedUtilities.RandomStringSafe(4);
    }
}