﻿using System;
using System.Collections.Generic;
using LabMaestro.Domain;
using LabMaestro.Shared;
using Slugify;

namespace LabMaestro.BusinessLogic.CRM;

public sealed class PatientDemographicInfo : IEquatable<PatientDemographicInfo>
{
    public DateTime LastVisited { get; set; }
    public string Title { get; set; }
    public string FirstName { get; set; }
    public string LastName { get; set; }
    public SexType Sex { get; set; }
    public string? Age { get; set; }

    public static PatientDemographicInfo From(PatientLabOrder source) =>
        new()
        {
            LastVisited = source.OrderDateTime.Date,
            //PatientId = source.CustomerId,
            Title = source.Title.Trim(),
            FirstName = source.FirstName.Trim(),
            LastName = source.LastName.Trim(),
            Age = source.Age?.Trim().ToUpperInvariant(),
            Sex = (SexType)source.Sex
        };

    public bool Equals(PatientDemographicInfo? other)
    {
        if (other is null) return false;
        if (ReferenceEquals(this, other)) return true;
        return Title == other.Title && FirstName == other.FirstName && LastName == other.LastName && Sex == other.Sex;
    }

    public override bool Equals(object? obj) =>
        ReferenceEquals(this, obj) || obj is PatientDemographicInfo other && Equals(other);

    public string GetSlug() =>
        new SlugHelper().GenerateSlug(
            string.Join(" ",
                (List<string>) [Title, LastName, FirstName, Sex.ToString()])
        );

    public override int GetHashCode() => GetSlug().GetHashCode();
}