﻿using System;
using LabMaestro.Domain;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic.CRM;

public sealed class CustomerInvoiceSlice
{
    public DateTime OrderDate { get; set; }
    public long InvoiceId { get; set; }
    public string OrderId { get; set; }
    public string PatientName { get; set; }
    public SexType Sex { get; set; }
    public string? Age { get; set; }
    public long? PatientId { get; set; }

    public static CustomerInvoiceSlice From(PatientLabOrder source) =>
        new()
        {
            InvoiceId = source.InvoiceId,
            OrderId = source.OrderId,
            OrderDate = source.OrderDateTime,
            PatientId = source.CustomerId,
            PatientName = source.FullName,
            Age = source.Age,
            Sex = (SexType)source.Sex
        };
}