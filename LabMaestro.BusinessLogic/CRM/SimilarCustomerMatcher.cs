﻿using System.Collections.Generic;
using System.Linq;
using NinjaNye.SearchExtensions.Soundex;

namespace LabMaestro.BusinessLogic.CRM;

public class SimilarCustomerMatcher(List<CustomerInvoiceSlice> customers)
{
    private readonly List<CustomerInvoiceSlice> _customers = customers;

    public List<CustomerInvoiceSlice> Match(string term) =>
        _customers
            .SoundexOf(c => c.PatientName)
            .Matching(term)
            .ToList();
}