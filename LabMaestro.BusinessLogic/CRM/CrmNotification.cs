﻿using System;

namespace LabMaestro.BusinessLogic.CRM;

public sealed class CrmNotification
{
    public string EmailTemplate { get; set; }
    public string EmailFrom { get; set; }
    public string EmailSubject { get; set; }
    public string SmsTemplate { get; set; }
    public long CustomerId { get; set; }
    public string? CustomerName { get; set; }
    public string CustomerUPIN { get; set; }
    public short? PlanId { get; set; }
    public string? PlanName { get; set; }
    public string? Phone { get; set; }
    public string? Email { get; set; }
    public string Login { get; set; }
    public string Password { get; set; }
    public string WebUrl { get; set; }
    public string AppUrl { get; set; }
    public DateTime SubscriptionStart { get; set; }
    public DateTime SubscriptionEnd { get; set; }
    public long? SubscriptionId { get; set; }
    public long? InvoiceId { get; set; }

    public static CrmNotification Make() =>
        new()
        {
            // todo: populate from DB settings
            EmailTemplate = "",
            EmailFrom = "",
            EmailSubject = "",
            SmsTemplate = "",
            AppUrl = "",
            WebUrl = "",
        };
}