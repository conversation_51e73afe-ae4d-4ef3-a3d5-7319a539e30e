﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: SearchedLabOrderInfo.cs 723 2013-07-03 06:56:29Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using AutoMapper;
using LabMaestro.Domain;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class SearchedLabOrderInfo
{
    public long InvoiceId { get; set; }
    public string OrderId { get; set; }
    public string PatientName { get; set; }
    public string ReferrerName { get; set; }
    public SexType Sex { get; set; }
    public string Age { get; set; }
    public DateTime? DoB { get; set; }
    public WorkflowStageType WorkflowStage { get; set; }
    public ResultsReleaseFlagType ResultsReleaseFlag { get; set; }
    public DateTime OrderDateTime { get; set; }
    public bool IsCancelled { get; set; }
    public bool IsExternalSubOrder { get; set; }
    public decimal GrossPayable { get; set; }
    public decimal DiscountAmount { get; set; }
    public decimal NetPayable { get; set; }
    public decimal PaidAmount { get; set; }
    public decimal DueAmount { get; set; }
    public bool DisallowReferral { get; set; }
    public bool IsReferrerUnknown { get; set; }

    public long? CustomerId { get; set; }
    public string CustomerUPIN { get; set; }

    public short? AffiliateId { get; set; }
    public string AffiliateName { get; set; }
    public string AffiliateUPIN { get; set; }

    public short? AssociateLabId { get; set; }
    public string AssociateLabName { get; set; }
    public string AssociateLabAccessionId { get; set; }

    public short? CorporateClientId { get; set; }
    public string CorporateClientName { get; set; }

    internal static SearchedLabOrderInfo AssembleFrom(LabOrderSearchResultSlice slice)
    {
        Mapper.CreateMap<LabOrderSearchResultSlice, SearchedLabOrderInfo>()
              .ForMember(dest => dest.Sex, opt => opt.MapFrom(src => (SexType)src.Sex))
              .ForMember(dest => dest.WorkflowStage,
                         opt => opt.MapFrom(src => (WorkflowStageType)src.WorkflowStage));
        return Mapper.Map<LabOrderSearchResultSlice, SearchedLabOrderInfo>(slice);
    }

    internal static SearchedLabOrderInfo AssembleFrom(LabOrderSearchResultSliceEx slice)
    {
        Mapper.CreateMap<LabOrderSearchResultSliceEx, SearchedLabOrderInfo>()
              .ForMember(dest => dest.Sex, opt => opt.MapFrom(src => (SexType)src.Sex))
              .ForMember(dest => dest.WorkflowStage,
                         opt => opt.MapFrom(src => (WorkflowStageType)src.WorkflowStage));
        return Mapper.Map<LabOrderSearchResultSliceEx, SearchedLabOrderInfo>(slice);
    }

    internal static SearchedLabOrderInfo AssembleFrom(LabOrderFilterResultSlice slice)
    {
        Mapper.CreateMap<LabOrderFilterResultSlice, SearchedLabOrderInfo>()
              .ForMember(dest => dest.Sex, opt => opt.MapFrom(src => (SexType)src.Sex))
              .ForMember(dest => dest.WorkflowStage,
                         opt => opt.MapFrom(src => (WorkflowStageType)src.WorkflowStage));
        return Mapper.Map<LabOrderFilterResultSlice, SearchedLabOrderInfo>(slice);
    }

    internal static SearchedLabOrderInfo AssembleFrom(PatientLabOrder source)
    {
        Mapper.CreateMap<PatientLabOrder, SearchedLabOrderInfo>()
              .ForMember(dest => dest.PatientName, opt => opt.MapFrom(src => src.FullName))
              .ForMember(dest => dest.Sex, opt => opt.MapFrom(src => (SexType)src.Sex))
              .ForMember(dest => dest.WorkflowStage, opt => opt.MapFrom(src => (WorkflowStageType)src.WorkflowStage));
        return Mapper.Map<PatientLabOrder, SearchedLabOrderInfo>(source);
    }
}