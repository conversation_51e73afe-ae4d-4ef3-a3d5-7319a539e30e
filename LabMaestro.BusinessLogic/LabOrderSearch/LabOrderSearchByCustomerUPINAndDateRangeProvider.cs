﻿using System;
using System.Collections.Generic;
using System.Linq;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;

namespace LabMaestro.BusinessLogic;

public sealed class LabOrderSearchByCustomerUPINAndDateRangeProvider(string upin, DateTime dtFrom, DateTime dtTo)
    : ILabOrderSearchProvider
{
    public IEnumerable<SearchedLabOrderInfo> SearchLabOrders()
    {
        List<SearchedLabOrderInfo> list = null;
        FaultHandler.Shield(() => list = PatientLabOrdersRepository
                                         .SearchLabOrderByCustomerUPINAndDateRange(upin, dtFrom, dtTo)
                                         .Select(SearchedLabOrderInfo.AssembleFrom)
                                         .ToList());
        return list;
    }
}