﻿using System;
using System.Collections.Generic;
using System.Linq;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;

namespace LabMaestro.BusinessLogic.LabOrderSearch;

public class LabOrderSearchByAssocLabDateRangeProvider(short id, DateTime dtFrom, DateTime dtTo)
    : ILabOrderSearchProvider
{
    public IEnumerable<SearchedLabOrderInfo> SearchLabOrders()
    {
        List<SearchedLabOrderInfo> list = null;
        FaultHandler.Shield(() => list = PatientLabOrdersRepository
            .SearchLabOrderByAssocLabAndDateRange(id, dtFrom, dtTo)
            .Select(SearchedLabOrderInfo.AssembleFrom)
            .ToList());
        return list;
    }
}