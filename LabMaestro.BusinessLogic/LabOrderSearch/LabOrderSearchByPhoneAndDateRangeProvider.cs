﻿using System;
using System.Collections.Generic;
using System.Linq;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;

namespace LabMaestro.BusinessLogic;

public sealed class LabOrderSearchByPhoneAndDateRangeProvider(string phone, DateTime dtFrom, DateTime dtTo)
    : ILabOrderSearchProvider
{
    public IEnumerable<SearchedLabOrderInfo> SearchLabOrders()
    {
        List<SearchedLabOrderInfo> list = null;
        FaultHandler.Shield(() => list = PatientLabOrdersRepository
                                         .SearchLabOrdersByPhoneAndDateRange(phone, dtFrom, dtTo)
                                         .Select(SearchedLabOrderInfo.AssembleFrom)
                                         .ToList());
        return list;
    }
}