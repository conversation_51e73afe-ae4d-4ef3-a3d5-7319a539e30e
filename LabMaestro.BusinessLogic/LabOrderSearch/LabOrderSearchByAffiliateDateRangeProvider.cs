﻿using System;
using System.Collections.Generic;
using System.Linq;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;

namespace LabMaestro.BusinessLogic.LabOrderSearch;

public class LabOrderSearchByAffiliateDateRangeProvider(short id, DateTime dtFrom, DateTime dtTo)
    : ILabOrderSearchProvider
{
    public IEnumerable<SearchedLabOrderInfo> SearchLabOrders()
    {
        List<SearchedLabOrderInfo> list = null;
        FaultHandler.Shield(() => list = PatientLabOrdersRepository
            .SearchLabOrderByAffiliateIdAndDateRange(id, dtFrom, dtTo)
            .Select(SearchedLabOrderInfo.AssembleFrom)
            .ToList());
        return list;
    }
}