// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the organization nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: LabOrderSearchByDateRangeAndResultBundleWorkflowBetweenProvider.cs 973 2013-10-09 12:38:09Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class LabOrderSearchByDateRangeAndResultBundleWorkflowBetweenProvider : ILabOrderSearchProvider
{
    private readonly DateTime _dtFrom;

    private readonly DateTime _dtTo;

    private readonly WorkflowStageType _wfFrom;

    private readonly WorkflowStageType _wfTo;

    public LabOrderSearchByDateRangeAndResultBundleWorkflowBetweenProvider(WorkflowStageType wfFrom,
        WorkflowStageType wfTo,
        DateTime dtFrom,
        DateTime dtTo)
    {
        _wfFrom = wfFrom;
        _dtFrom = dtFrom;
        _dtTo = dtTo;
        _wfTo = wfTo;
    }

    public IEnumerable<SearchedLabOrderInfo> SearchLabOrders()
    {
        List<SearchedLabOrderInfo> list = null;
        FaultHandler.Shield(() =>
            list = PatientLabOrdersRepository
                .SearchActiveLabOrdersByDateRangeAndResultBundleWorkflowBetween(
                    _wfFrom,
                    _wfTo,
                    _dtFrom,
                    _dtTo)
                .Select(SearchedLabOrderInfo.AssembleFrom)
                .ToList());
        return list;
    }
}