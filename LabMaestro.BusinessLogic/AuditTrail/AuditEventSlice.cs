﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: AuditEventSlice.cs 1045 2013-10-29 16:33:36Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using LabMaestro.Domain;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class AuditEventSlice
{
    public long Id { get; private set; }

    //public int WorkShiftId { get; private set; }

    //public OrderedTestSlice OrderedTest { get; private set; }

    public AuditResultBundle ResultBundle { get; private set; }

    public string PerformingUser { get; private set; }

    public WorkflowStageType WorkflowStage { get; private set; }

    public string WorkflowStageDescr { get; private set; }

    public DateTime EventTime { get; private set; }

    public AuditEventCategory EventCategory { get; private set; }

    public string EventCategoryDescr { get; private set; }

    public AuditEventType EventType { get; private set; }

    public string EventTypeDescr { get; private set; }

    public string UserIpAddress { get; private set; }

    public string Remarks { get; private set; }

    internal static AuditEventSlice Assemble(AuditTrailSlice slice, AuditResultBundlesList list)
    {
        var result = new AuditEventSlice
        {
            Id = slice.Id,
            EventCategory = (AuditEventCategory)slice.EventCategory,
            EventCategoryDescr = EnumUtils.EnumDescription((AuditEventCategory)slice.EventCategory),
            EventTime = slice.EventTime,
            EventType = (AuditEventType)slice.EventType,
            EventTypeDescr = EnumUtils.EnumDescription((AuditEventType)slice.EventType),
            PerformingUser = slice.PerformingUserName,
            UserIpAddress = SharedUtilities.IpAddressToString(slice.UserIpAddress),
            WorkflowStage = (WorkflowStageType)slice.WorkflowStage,
            WorkflowStageDescr = EnumUtils.EnumDescription((WorkflowStageType)slice.WorkflowStage),
            Remarks = slice.Note
        };

        if (slice.ResultBundleId != null)
        {
            var bundleId = (long)slice.ResultBundleId;
            if (bundleId > 0) result.ResultBundle = list.GetResultBundle(bundleId);
        }

        return result;
    }
}