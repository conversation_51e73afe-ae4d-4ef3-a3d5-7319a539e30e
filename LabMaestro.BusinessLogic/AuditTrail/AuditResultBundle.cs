﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: AuditResultBundle.cs 723 2013-07-03 06:56:29Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using CuttingEdge.Conditions;
using LabMaestro.Domain;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class AuditResultBundle
{
    public long Id { get; private set; }
    public bool IsActive { get; private set; }
    public string DisplayTitle { get; private set; }
    public string ComponentLabTests { get; private set; }
    public string LabName { get; private set; }
    public string CreatedBy { get; private set; }
    public string FinalizedBy { get; private set; }
    public WorkflowStageType WorkflowStage { get; private set; }
    public TestResultType TestResultType { get; private set; }
    public DateTime DateCreated { get; private set; }
    public DateTime LastUpdated { get; private set; }

    /// <summary>
    ///     Loads the specified bundle from database.
    /// </summary>
    /// <param name="bundleId">The bundle id.</param>
    /// <returns>AuditResultBundle. Null if not found.</returns>
    internal static AuditResultBundle Assemble(long bundleId)
    {
        Condition.Requires(bundleId).IsGreaterOrEqual(0);
        AuditResultBundle result = null;
        var bundle = ResultBundlesRepository.FindById(bundleId);
        if (bundle != null)
            result = new()
            {
                Id = bundle.Id,
                IsActive = bundle.IsActive,
                DisplayTitle = bundle.DisplayTitle,
                ComponentLabTests = bundle.ComponentLabTests,
                CreatedBy = bundle.CreatingUserId == null
                    ? string.Empty
                    : UsersRepository.GetUserDisplayName((short)bundle.CreatingUserId),
                FinalizedBy = bundle.FinalizingConsultantId == null
                    ? string.Empty
                    : UsersRepository.GetUserDisplayName((short)bundle.FinalizingConsultantId),
                DateCreated = bundle.DateCreated,
                LastUpdated = bundle.LastUpdated,
                LabName = LabsRepository.GetLabName(bundle.LabId),
                WorkflowStage = (WorkflowStageType)bundle.WorkflowStage,
                TestResultType = (TestResultType)bundle.TestResultType
            };
        return result;
    }

    public override string ToString() => ComponentLabTests;
}