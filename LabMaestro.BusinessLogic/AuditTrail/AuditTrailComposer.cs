﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: AuditTrailComposer.cs 779 2013-07-09 13:39:03Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System.Collections.Generic;
using System.Linq;
using C1.LiveLinq;
using C1.LiveLinq.Collections;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class AuditTrailComposer(PatientLabOrder order)
{
    private readonly IndexedCollection<AuditEventSlice> _auditEvents = new();
    private readonly AuditResultBundlesList _resultBundlesList = new();

    public AuditTrailComposer(long invoiceId) : this(PatientLabOrdersRepository.FindById(invoiceId))
    {
    }

    public LabOrderContextInfo LabOrderContext { get; } = LabOrderContextInfo.AssembleFrom(order);

    public List<AuditEventSlice> AuditEvents => _auditEvents.ToList();

    public AuditEventCategory AuditFilterCategory { get; set; }

    public List<AuditEventSlice> FilterByCategory(AuditEventCategory category) =>
        _auditEvents.AsIndexed()
            .Where(x => x.EventCategory == category)
            .OrderBy(x => x.Id)
            .ToList();

    public void GetAuditTrailsForResultBundle(long bundleId)
    {
        var trails = AuditTrailRepository.GetAuditRecordsForResultBundle(bundleId, AuditFilterCategory);
        addAuditTrails(trails);
    }

    public void GetAuditTrailsForLabOrder()
    {
        List<AuditTrailSlice> trails = null;
        FaultHandler.Shield(() => trails = AuditTrailRepository.GetAuditRecordsForInvoice(
            LabOrderContext.InvoiceId,
            AuditFilterCategory));
        addAuditTrails(trails);
    }

    public void GetAllAuditTrailsForLabOrder()
    {
        List<AuditTrailSlice> trails = null;
        FaultHandler.Shield(
            () => trails = AuditTrailRepository.GetAllAuditRecordsForInvoice(LabOrderContext.InvoiceId));
        addAuditTrails(trails);
    }

    private void addAuditTrails(List<AuditTrailSlice> trails)
    {
        _auditEvents.BeginUpdate();
        try
        {
            _auditEvents.Clear();
            if (trails != null)
                foreach (var slice in trails)
                    _auditEvents.Add(AuditEventSlice.Assemble(slice, _resultBundlesList));
        }
        finally
        {
            _auditEvents.EndUpdate();
        }
    }
}