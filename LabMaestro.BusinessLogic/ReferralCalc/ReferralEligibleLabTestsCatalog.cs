﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ReferralEligibleLabTestsCatalog.cs 1101 2013-12-03 07:15:32Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System.Linq;
using C1.LiveLinq.Collections;
using C1.LiveLinq.Indexing;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;

namespace LabMaestro.BusinessLogic;

internal sealed class ReferralEligibleLabTestsCatalog : IReferralEligibleLabTestsCatalog
{
    private readonly IndexedCollection<ReferralLabTest> _catalog = [];
    private Index<ReferralLabTest, short> _indexByTestId;

    public decimal CalculateGrossReferralAmount(short testId, out decimal listPrice, out string testGroupName,
        out string refModeLabel)
    {
        var result = 0m;
        listPrice = 0m;
        testGroupName = string.Empty;
        refModeLabel = string.Empty;

        var testSlice = _indexByTestId.Find(testId).SingleOrDefault();
        if (testSlice != null)
        {
            result = testSlice.CalculateGrossReferralAmount();
            testGroupName = testSlice.ReferralGroupDisplayName;
            listPrice = testSlice.ListPrice;
            refModeLabel = testSlice.ReferralModeLabel;
        }

        return result;
    }

    public void PopulateCatalogFromDatabase()
    {
        var slices = FaultHandler.Shield(LabTestsRepository.GetReferralEligibleLabTestsCatalog);
        if (slices is { Count: > 0 })
        {
            _catalog.BeginUpdate();
            try
            {
                _catalog.AddRange(slices.Select(ReferralLabTest.AssembleFrom));
            }
            finally
            {
                _catalog.EndUpdate();
            }

            _indexByTestId = _catalog.Indexes.Add(x => x.TestId);
        }
    }
}