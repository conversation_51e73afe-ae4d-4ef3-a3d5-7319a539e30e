﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ReferralLabTest.cs 1101 2013-12-03 07:15:32Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using LabMaestro.Domain;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

internal sealed class ReferralLabTest
{
    internal short TestId { get; private set; }

    internal string TestName { get; private set; }

    internal decimal CostBasis { get; private set; }

    internal string LabCode { get; private set; }

    internal short LabId { get; private set; }

    internal string LabName { get; private set; }

    internal decimal ListPrice { get; private set; }

    internal decimal ReferralAmount { get; private set; }

    internal IncentiveType ReferralMode { get; private set; }

    internal decimal ReferralPercent { get; private set; }

    internal string ReferralGroupDisplayName { get; private set; }

    internal string ReferralModeLabel =>
        ReferralMode switch
        {
            IncentiveType.Percentage => $"{ReferralPercent}%",
            IncentiveType.FlatRate => $"{ReferralAmount}F",
            _ => string.Empty
        };

    internal static ReferralLabTest AssembleFrom(ReferralEligibleLabTestSlice slice) =>
        new()
        {
            TestId = slice.TestId,
            TestName = slice.TestName,
            CostBasis = slice.CostBasis,
            LabCode = slice.LabCode,
            LabId = slice.LabId,
            LabName = slice.LabName,
            ListPrice = slice.ListPrice,
            ReferralAmount = slice.ReferralAmount,
            ReferralMode = (IncentiveType)slice.ReferralMode,
            ReferralPercent = (decimal)slice.ReferralPercent,
            ReferralGroupDisplayName = slice.ReferralGroupDisplayName
        };

    public decimal CalculateGrossReferralAmount()
    {
        return ReferralMode switch
        {
            IncentiveType.None => 0m,
            IncentiveType.Percentage => ListPrice * ReferralPercent / 100m,
            IncentiveType.FlatRate => ReferralAmount,
            _ => 0m
        };
    }
}