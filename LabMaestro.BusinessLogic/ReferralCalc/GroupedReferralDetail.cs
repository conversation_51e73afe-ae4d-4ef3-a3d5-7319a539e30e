﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: GroupedReferralDetail.cs 1101 2013-12-03 07:15:32Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using LabMaestro.Domain;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class GroupedReferralDetail
{
    public string GroupName { get; set; }
    public decimal GrossReferral { get; set; }
    public decimal ListPrice { get; set; }
    public int NumTests { get; set; }
    public decimal PriceTotal { get; set; }
    public IncentiveType ReferralMode { get; set; }
    public string ReferralModeLabel { get; set; }

    public void AddReferrals(decimal gross, decimal listPrice, int numTests)
    {
        GrossReferral += gross;
        ListPrice = listPrice;
        PriceTotal += listPrice;
        NumTests += numTests;
    }

    public ReferralSubCategoryInfo ToSubCategoryInfo() =>
        new()
        {
            CategoryName = GroupName,
            NumTests = NumTests,
            BillAmount = ListPrice * NumTests,
            ReferralAmount = GrossReferral
        };
}