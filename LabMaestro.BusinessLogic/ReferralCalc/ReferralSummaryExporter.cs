﻿using System.Drawing;
using System.Linq;
using LabMaestro.Domain;
using LabMaestro.Shared;
using Syncfusion.XlsIO;

namespace LabMaestro.BusinessLogic.ReferralCalc;

public sealed class ReferralSummaryExporter(ReferralCalculator calculator)
{
    public void ExportExcel(string filename)
    {
        var row = 1;
        var referrers =
            calculator.ReferrersList.FilterReferrersWithActiveReferrals().OrderBy(x => x.ReferrerId).ToList();
        using var engine = new ExcelEngine();
        var application = engine.Excel;
        var workbook = application.Workbooks.Create(1);
        var sheet = workbook.Worksheets[0];

        insertHeader(sheet, row++);
        foreach (var referrer in referrers)
            insertRow(sheet, referrer, row++);

        workbook.Version = ExcelVersion.Excel2007;
        workbook.SaveAs(filename);
        workbook.Close();
    }

    private static void insertHeader(IWorksheet sheet, int row)
    {
        var col = 1;
        setHeaderCellText(sheet.Range[row, col++], "Id");
        setHeaderCellText(sheet.Range[row, col++], "Name");
        setHeaderCellText(sheet.Range[row, col++], "Num Patients");
        setHeaderCellText(sheet.Range[row, col++], "Gross Referral");
        setHeaderCellText(sheet.Range[row, col++], "Net Referral");
        setHeaderCellText(sheet.Range[row, col++], "Gross Payable");
        setHeaderCellText(sheet.Range[row, col++], "Discount");
        setHeaderCellText(sheet.Range[row, col++], "Net Payable");
        setHeaderCellText(sheet.Range[row, col++], "Paid");
        setHeaderCellText(sheet.Range[row, col++], "Due");
    }

    private static void setHeaderCellText(IRange cell, string text)
    {
        cell.CellStyle.Color = Color.FromArgb(0, 0, 112, 192);
        cell.CellStyle.Font.FontName = "Verdana";
        cell.CellStyle.Font.Size = 12;
        cell.CellStyle.Font.Color = ExcelKnownColors.White;
        cell.CellStyle.Font.Bold = true;
        cell.Text = text;
    }

    private static void setDataCellText(IRange cell, string text)
    {
        cell.CellStyle.Font.FontName = "Verdana";
        cell.CellStyle.Font.Size = 10;
        cell.Text = text;
    }

    private static void insertRow(IWorksheet sheet, ReferrerInfo referrer, int row)
    {
        var col = 1;
        setDataCellText(sheet.Range[row, col++], referrer.ReferrerId.ToString());
        setDataCellText(sheet.Range[row, col++], SharedUtilities.EmptyString(referrer.ReferrerName));
        setDataCellText(sheet.Range[row, col++], SharedUtilities.IntToStringPositive(referrer.NumInvoices));
        setDataCellText(sheet.Range[row, col++], SharedUtilities.MoneyToStringCulture(referrer.GrossReferralTotal));
        setDataCellText(sheet.Range[row, col++], SharedUtilities.MoneyToStringCulture(referrer.NetReferralTotal));
        setDataCellText(sheet.Range[row, col++], SharedUtilities.MoneyToStringCulture(referrer.GrossBillTotal));
        setDataCellText(sheet.Range[row, col++], SharedUtilities.MoneyToStringCulture(referrer.DiscountTotal));
        setDataCellText(sheet.Range[row, col++], SharedUtilities.MoneyToStringCulture(referrer.NetBillTotal));
        setDataCellText(sheet.Range[row, col++], SharedUtilities.MoneyToStringCulture(referrer.PaidTotal));
        setDataCellText(sheet.Range[row, col++], SharedUtilities.MoneyToStringCulture(referrer.DueTotal));
    }
}