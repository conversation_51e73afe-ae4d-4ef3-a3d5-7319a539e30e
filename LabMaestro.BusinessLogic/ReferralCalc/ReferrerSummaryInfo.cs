﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ReferrerSummaryInfo.cs 1212 2014-03-10 11:09:21Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System.Collections.Generic;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class ReferrerSummaryInfo
{
    public long ReferrerId { get; set; }

    public string ReferrerName { get; set; }

    public decimal GrossReferral { get; set; }

    public decimal NetReferral { get; set; }

    public decimal GrossBill { get; set; }

    public decimal Discount { get; set; }

    public decimal NetBill { get; set; }

    public decimal Payment { get; set; }

    public decimal Due { get; set; }

    public decimal Refund { get; set; }

    public decimal PaidUpReferral { get; set; }

    public int NumPatients { get; set; }

    public List<ReferredLabOrderDetails> OrderDetails { get; private set; } = [];

    public GroupedReferralDetailsList GroupedReferralDetails { get; private set; } = new();

    public InvoiceReferralMasterPrintDto ToPrintDto() =>
        new()
        {
            Due = SharedUtilities.MoneyToStringPlainCultureNonZero(Due),
            Discount = SharedUtilities.MoneyToStringPlainCultureNonZero(Discount),
            GrossPayable = SharedUtilities.MoneyToStringPlainCultureNonZero(GrossBill),
            NetPayable = SharedUtilities.MoneyToStringPlainCultureNonZero(NetBill),
            Paid = SharedUtilities.MoneyToStringPlainCultureNonZero(Payment),
            Refund = SharedUtilities.MoneyToStringPlainCultureNonZero(Refund),
            PaidUpReferral = SharedUtilities.MoneyToStringPlainCultureNonZero(PaidUpReferral),
            NumTests = SharedUtilities.IntToStringPositive(NumPatients)
        };
}