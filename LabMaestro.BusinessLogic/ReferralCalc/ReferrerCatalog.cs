﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ReferrerCatalog.cs 1440 2014-10-03 04:25:17Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System.Collections.Generic;
using System.Linq;
using C1.LiveLinq.Collections;
using C1.LiveLinq.Indexing;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;

namespace LabMaestro.BusinessLogic;

public sealed class ReferrerCatalog
{
    private readonly IndexedCollection<ReferrerInfo> _catalog = [];
    private Index<ReferrerInfo, int> _indexById;

    public short ReferrerCategory { get; set; } = -1;

    public int ReferrerIdRangeBegin { get; set; }

    public int ReferrerIdRangeEnd { get; set; }

    public List<ReferrerInfo> Referrers => _catalog.ToList();

    public void PopulateReferrersFromDatabase()
    {
        var referrers = FaultHandler.Shield(
            () => ReferrerCategory > 0
                ? ReferrersRepository.GetEligibleReferrersInCategory(ReferrerCategory)
                : ReferrersRepository.GetAllEligibleReferrers());

        _catalog.BeginUpdate();
        try
        {
            _catalog.Clear();
            if (referrers != null && referrers.Count > 0)
            {
                IEnumerable<ReferrerInfo> list;
                if (ReferrerIdRangeBegin > 0)
                    list = referrers.Where(x => x.Id >= ReferrerIdRangeBegin && x.Id <= ReferrerIdRangeEnd)
                        .Select(ReferrerInfo.AssembleFrom);
                else
                    list = referrers.Select(ReferrerInfo.AssembleFrom);
                _catalog.AddRange(list.OrderBy(r => r.ReferrerId));
            }

            _indexById = _catalog.Indexes.Add(x => x.ReferrerId);
        }
        finally
        {
            _catalog.EndUpdate();
        }
    }

    public void ProcessLabOrder(ReferralEligibleLabOrderSlice slice, GroupedReferralDetailsList referralDetails)
    {
        var referrer = _catalog.SingleOrDefault(x => x.ReferrerId == slice.ReferrerId);
        if (referrer == null) return;
        
        var invoice = referrer.ProcessInvoice(slice);

        foreach (var referralDetail in referralDetails.ReferralDetails)
        {
            invoice.ReferralSubCategories.AddOrUpdateCategory(referralDetail.GroupName,
                referralDetail.PriceTotal,
                referralDetail.GrossReferral,
                referralDetail.NumTests,
                referralDetail.ReferralModeLabel);

            referrer.ReferralSubCategories.AddOrUpdateCategory(referralDetail.GroupName,
                referralDetail.PriceTotal,
                referralDetail.GrossReferral,
                referralDetail.NumTests,
                referralDetail.ReferralModeLabel);
        }

        var summary = referralDetails.GetReferralDetailSum();
        invoice.GrossReferral = summary.GrossReferral;
        invoice.CalculateNetReferral(referrer.SuppressNetReferral);

        referrer.GrossReferralTotal += invoice.GrossReferral;
        referrer.NetReferralTotal += invoice.NetReferral;
    }

    public bool ReferrerExists(int refId) => _indexById.ContainsKey(refId);

    public List<ReferrerInfo> FilterReferrersWithActiveReferrals()
        => _catalog.Where(x => x.HasInvoices() && x.NetReferralTotal > 0m).ToList();
}