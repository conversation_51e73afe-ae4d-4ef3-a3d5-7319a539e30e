﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ReferralCalculator.cs 1352 2014-06-02 13:56:33Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Linq;
using C1.LiveLinq.Collections;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class ReferralCalculator
{
    private readonly IndexedCollection<ReferralEligibleLabOrderSlice> _eligibleLabOrders = new();
    private readonly GroupedReferralDetailsList _groupedReferralDetails = new();
    private readonly ReferralEligibleLabTestsCatalog _testCatalog = new();

    public short ReferrerCategory { get; set; } = 0;
    public int ReferrerIdRangeBegin { get; set; }
    public int ReferrerIdRangeEnd { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }

    public ReferrerCatalog ReferrersList { get; } = new();

    public int DaysInPeriod
    {
        get
        {
            var days = (EndDate - StartDate).Days + 1;
            //if (days <= 0) days = 1;
            return days;
        }
    }

    public void SetSingleReferrerId(int id)
    {
        ReferrerIdRangeBegin = id;
        ReferrerIdRangeEnd = id;
    }

    private void normalizeDates()
    {
        StartDate = SharedUtilities.AbsoluteStart(StartDate);
        EndDate = SharedUtilities.AbsoluteEnd(EndDate);
    }

    private void scanEligibleReferrers()
    {
        _testCatalog.PopulateCatalogFromDatabase();
        ReferrersList.ReferrerCategory = ReferrerCategory;
        ReferrersList.ReferrerIdRangeBegin = ReferrerIdRangeBegin;
        ReferrersList.ReferrerIdRangeEnd = ReferrerIdRangeEnd;
        ReferrersList.PopulateReferrersFromDatabase();
    }

    private void scanEligibleLabOrders()
    {
        normalizeDates();

        var orders = FaultHandler.Shield(
            () => PatientLabOrdersRepository.GetReferralEligibleLabOrdersByDateRange(StartDate, EndDate));

        _eligibleLabOrders.BeginUpdate();
        try
        {
            _eligibleLabOrders.Clear();

            foreach (var slice in orders.Where(x => x.ReferrerId != null &&
                                                    ReferrersList.ReferrerExists((int)x.ReferrerId)))
                _eligibleLabOrders.Add(slice);
        }
        finally
        {
            _eligibleLabOrders.EndUpdate();
        }
    }

    public void Execute()
    {
        scanEligibleReferrers();
        scanEligibleLabOrders();
        if (_eligibleLabOrders.Count > 0)
            foreach (var orderSlice in _eligibleLabOrders)
            {
                var orderGroupDetails = new GroupedReferralDetailsList();

                var orderedTests = FaultHandler.Shield(
                    () => OrderedTestsRepository.GetReferralEligibleOrderedTestIds(orderSlice.InvoiceId));

                foreach (var testId in orderedTests)
                    try
                    {
                        string groupName, refModeLabel;
                        decimal listPrice;
                        var grossReferral = _testCatalog.CalculateGrossReferralAmount((short)testId,
                            out listPrice,
                            out groupName,
                            out refModeLabel);
                        if (!string.IsNullOrEmpty(groupName))
                        {
                            orderGroupDetails.AddReferralDetail(groupName, grossReferral, listPrice, 1, refModeLabel);
                            _groupedReferralDetails.AddReferralDetail(groupName, grossReferral, listPrice, 1,
                                refModeLabel);
                        }
                    }
                    catch (Exception e)
                    {
                        //EsbAppFaultDataSubmitter
                    }

                ReferrersList.ProcessLabOrder(orderSlice, orderGroupDetails);
            }
    }

    public ReferralSummaryPrintDto ToPrintDto()
    {
        var netBill = ReferrersList.Referrers.Sum(x => x.NetBillTotal);
        var disc = ReferrersList.Referrers.Sum(x => x.DiscountTotal);
        var due = ReferrersList.Referrers.Sum(x => x.DueTotal);
        var netRef = ReferrersList.Referrers.Sum(x => x.NetReferralTotal);
        var grossRef = ReferrersList.Referrers.Sum(x => x.GrossReferralTotal);
        var numPatients = ReferrersList.Referrers.Sum(x => x.NumInvoices);

        var dto = new ReferralSummaryPrintDto
        {
            DateFrom = SharedUtilities.DateToStringOnlySlash(StartDate),
            DateTo = SharedUtilities.DateToStringOnlySlash(EndDate),
            Business = SharedUtilities.MoneyToStringPlainCultureNonZero(netBill),
            Discount = SharedUtilities.MoneyToStringPlainCultureNonZero(disc),
            Due = SharedUtilities.MoneyToStringPlainCultureNonZero(due),
            NetReferral = SharedUtilities.MoneyToStringPlainCultureNonZero(netRef),
            GrossReferral = SharedUtilities.MoneyToStringPlainCultureNonZero(grossRef),
            NumPatients = SharedUtilities.IntToStringPositive(numPatients)
        };

        var referrers = ReferrersList.FilterReferrersWithActiveReferrals().OrderBy(x => x.ReferrerId);
        foreach (var referrerInfo in referrers)
        {
            dto.ReferralSummaryDetails.Add(ReferrerDetailPrintDto.AssembleFrom(referrerInfo));
            foreach (var detail in referrerInfo.Invoices.SelectMany(invoice => invoice.ReferralSubCategories.Items))
                dto.LabGroupWiseSummary.Add(InvoiceReferralDetailPrintDto.AssembleFrom(detail));
        }

        return dto;
    }
}