﻿using System.Linq;
using LabMaestro.Shared.Pipeline;

namespace LabMaestro.BusinessLogic.Discounts;

public class DiscountPipelineSteps
{
    public class GlobalLab : IPipelineStep
    {
        public void Execute(IPipelineContext context)
        {
            var ctx = (DiscountContext)context;
            var entry = ctx.DiscountTable.FirstOrDefault(
                x =>
                    x.CorporateId <= 0 &&
                    x.GroupId == ctx.GroupId &&
                    x.LabId == ctx.LabId);

            if (entry != null) ctx.FoundMatch(entry.Discount);
        }
    }

    public class GlobalLabTest : IPipelineStep
    {
        public void Execute(IPipelineContext context)
        {
            var ctx = (DiscountContext)context;
            var entry = ctx.DiscountTable.FirstOrDefault(
                x =>
                    x.CorporateId <= 0 &&
                    x.LabId <= 0 &&
                    x.GroupId == ctx.GroupId &&
                    x.LabTestId == ctx.LabTestId);

            if (entry != null) ctx.FoundMatch(entry.Discount);
        }
    }

    public class CorporateLab : IPipelineStep
    {
        public void Execute(IPipelineContext context)
        {
            var ctx = (DiscountContext)context;
            var entry = ctx.DiscountTable.FirstOrDefault(
                x =>
                    x.LabTestId <= 0 &&
                    x.GroupId == ctx.GroupId &&
                    x.CorporateId == ctx.CorporateId &&
                    x.LabId == ctx.LabId);

            if (entry != null) ctx.FoundMatch(entry.Discount);
        }
    }

    public class CorporateLabTest : IPipelineStep
    {
        public void Execute(IPipelineContext context)
        {
            var ctx = (DiscountContext)context;
            var entry = ctx.DiscountTable.FirstOrDefault(
                x =>
                    x.LabId <= 0 &&
                    x.GroupId == ctx.GroupId &&
                    x.CorporateId == ctx.CorporateId &&
                    x.LabTestId == ctx.LabTestId);

            if (entry != null) ctx.FoundMatch(entry.Discount);
        }
    }
}