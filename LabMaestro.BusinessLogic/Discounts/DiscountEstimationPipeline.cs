﻿using System.Collections.Generic;
using LabMaestro.Shared.Pipeline;

namespace LabMaestro.BusinessLogic.Discounts;

public class DiscountEstimationPipeline : SequentialPipeline
{
    private readonly DiscountService _service;
    private readonly Queue<IPipelineStep> _steps;

    public DiscountEstimationPipeline(DiscountService service)
    {
        _service = service;
        _steps = new Queue<IPipelineStep>();
    }

    public DiscountContext GetContext(short groupId, short labTestId, short labId, int corporateId) =>
        new(groupId, labTestId, labId, corporateId, _service.DiscountTable);

    public ApplicableDiscount Estimate(short groupId, short labTestId, short labId, int corporateId)
    {
        _steps.Clear();

        if (corporateId > 0)
        {
            _steps.Enqueue(new DiscountPipelineSteps.CorporateLabTest());
            _steps.Enqueue(new DiscountPipelineSteps.CorporateLab());
        }

        _steps.Enqueue(new DiscountPipelineSteps.GlobalLabTest());
        _steps.Enqueue(new DiscountPipelineSteps.GlobalLab());

        var context = GetContext(groupId, labTestId, labId, corporateId);

        while (_steps.Count > 0)
        {
            var step = _steps.Dequeue();
            step.Execute(context);
            if (context.HaltExecution) break;
        }

        return context.Discount;
    }
}