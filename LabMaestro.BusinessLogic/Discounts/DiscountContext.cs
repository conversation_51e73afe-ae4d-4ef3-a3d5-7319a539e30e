﻿using System.Collections.Generic;
using LabMaestro.Shared.Pipeline;

namespace LabMaestro.BusinessLogic.Discounts;

public class DiscountContext : IPipelineContext
{
    public DiscountContext(short groupId, short labTestId, short labId, int corporateId,
        List<DiscountInfo> discountTable)
    {
        GroupId = groupId;
        LabId = labId;
        LabTestId = labTestId;
        CorporateId = corporateId;
        DiscountTable = discountTable;
        Discount = null;
        HaltExecution = false;
    }

    public short GroupId { get; }
    public short LabId { get; }
    public short LabTestId { get; }
    public int CorporateId { get; }
    public List<DiscountInfo> DiscountTable { get; }
    public ApplicableDiscount Discount { get; private set; }
    public bool HaltExecution { get; set; }

    public void FoundMatch(ApplicableDiscount discount)
    {
        Discount = discount;
        HaltExecution = true;
    }
}