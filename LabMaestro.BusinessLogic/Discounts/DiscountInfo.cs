﻿using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic.Discounts;

public sealed class DiscountInfo
{
    public long Id { get; }
    public short GroupId { get; set; }
    public short LabId { get; set; }
    public short LabTestId { get; set; }
    public int CorporateId { get; set; }
    public ApplicableDiscount Discount { get; set; } = new();
    public DiscountInfoType InfoType { get; set; } = DiscountInfoType.None;

    public DiscountInfo UpdateParameters(decimal amount, float percentage, decimal maxAmount, IncentiveType mode)
    {
        Discount.DiscountAmount = amount;
        Discount.DiscountPercentage = percentage;
        Discount.MaximumDiscountAmount = maxAmount;
        Discount.DiscountType = mode;
        Discount.Source = InfoType;
        return this;
    }

    public static DiscountInfo ForLab(short labId, short groupId) =>
        new()
        {
            GroupId = groupId,
            InfoType = DiscountInfoType.GlobalLab,
            LabId = labId
        };

    public static DiscountInfo ForLabTest(short testId, short groupId) =>
        new()
        {
            GroupId = groupId,
            InfoType = DiscountInfoType.GlobalTest,
            LabTestId = testId
        };

    public static DiscountInfo ForCorporateLab(int corporateId, short labId, short groupId) =>
        new()
        {
            GroupId = groupId,
            InfoType = DiscountInfoType.CorporateLab,
            CorporateId = corporateId,
            LabId = labId
        };

    public static DiscountInfo ForCorporateLabTest(int corporateId, short testId, short groupId) =>
        new()
        {
            GroupId = groupId,
            InfoType = DiscountInfoType.CorporateTest,
            CorporateId = corporateId,
            LabTestId = testId
        };
}