﻿using System.Collections.Generic;

namespace LabMaestro.BusinessLogic.Discounts;

public sealed class DiscountService
{
    private readonly DiscountEstimationPipeline _pipeline;

    public DiscountService()
    {
        DiscountTable = [];
        _pipeline = new(this);
    }

    public List<DiscountInfo> DiscountTable { get; }

    public void LoadDiscounts()
    {
        DiscountTable.Clear();
        // TODO: load from DB
        /*
         * CT -> CL -> GT -> GL
         * if corp <= 0:
         *      if lab <= 0:
         *          addGlobalTest
         *      else:
         *          addGlobalLab
         * else:
         *      if lab <= 0:
         *          addCorpTest
         *      else:
         *          addCorpLab
         */
    }

    public ApplicableDiscount Estimate(short groupId, short labTestId = 0, short labId = 0, int corporateId = 0)
    {
        return _pipeline.Estimate(groupId, labTestId, labId, corporateId);
    }
}