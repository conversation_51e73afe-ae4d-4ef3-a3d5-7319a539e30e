﻿using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic.Discounts;

public class ApplicableDiscount
{
    public IncentiveType DiscountType { get; set; }
    public decimal DiscountAmount { get; set; }
    public float DiscountPercentage { get; set; }
    public decimal MaximumDiscountAmount { get; set; }
    public DiscountInfoType Source { get; set; }

    public decimal Calculate(decimal price)
    {
        decimal discount = 0;
        switch (DiscountType)
        {
            case IncentiveType.FlatRate:
                discount = DiscountAmount;
                break;
            case IncentiveType.Percentage:
                var fract = (decimal)(DiscountPercentage / 100);
                discount = decimal.Multiply(price, fract);
                break;
        }

        if (MaximumDiscountAmount > 0 && discount > MaximumDiscountAmount)
            discount = MaximumDiscountAmount;

        return discount;
    }
}