﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ReferrerCatalogExporter.cs 1141 2014-01-29 13:25:55Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System.Drawing;
using LabMaestro.Domain;
using LabMaestro.Shared;
using Syncfusion.XlsIO;

namespace LabMaestro.BusinessLogic;

public static class ReferrerCatalogExporter
{
    public static int ExportCatalogToExcelDocument(string filename, bool onlyActive)
    {
        var referrers = ReferrersRepository.FindAllReferrers();
        if (referrers.Count == 0) return -1;

        var row = 1;
        using (var engine = new ExcelEngine())
        {
            var application = engine.Excel;
            var workbook = application.Workbooks.Create(1);
            var sheet = workbook.Worksheets[0];

            insertHeader(sheet, row++);
            foreach (var referrer in referrers) insertRow(sheet, referrer, row++);

            workbook.Version = ExcelVersion.Excel2007;
            workbook.SaveAs(filename);
            workbook.Close();
        }

        return row;
    }

    private static void insertHeader(IWorksheet sheet, int row)
    {
        var col = 1;
        setHeaderCellText(sheet.Range[row, col++], "Id");
        setHeaderCellText(sheet.Range[row, col++], "Prefix");
        setHeaderCellText(sheet.Range[row, col++], "Name");
        setHeaderCellText(sheet.Range[row, col++], "Suffix");
        setHeaderCellText(sheet.Range[row, col++], "Identifying Tag");
        setHeaderCellText(sheet.Range[row, col++], "Mobile");
        setHeaderCellText(sheet.Range[row, col++], "Active?");
    }

    private static void setHeaderCellText(IRange cell, string text)
    {
        cell.CellStyle.Color = Color.FromArgb(0, 0, 112, 192);
        cell.CellStyle.Font.FontName = "Verdana";
        cell.CellStyle.Font.Size = 12;
        cell.CellStyle.Font.Color = ExcelKnownColors.White;
        cell.CellStyle.Font.Bold = true;
        cell.Text = text;
    }

    private static void setDataCellText(IRange cell, string text)
    {
        cell.CellStyle.Font.FontName = "Verdana";
        cell.CellStyle.Font.Size = 10;
        cell.Text = text;
    }

    private static void insertRow(IWorksheet sheet, Referrer referrer, int row)
    {
        var col = 1;
        setDataCellText(sheet.Range[row, col++], referrer.Id.ToString());
        setDataCellText(sheet.Range[row, col++], SharedUtilities.EmptyString(referrer.Prefix));
        setDataCellText(sheet.Range[row, col++], SharedUtilities.EmptyString(referrer.Name));
        setDataCellText(sheet.Range[row, col++], SharedUtilities.EmptyString(referrer.Suffix));
        setDataCellText(sheet.Range[row, col++], SharedUtilities.EmptyString(referrer.IdentifyingTag));
        setDataCellText(sheet.Range[row, col++], SharedUtilities.EmptyString(referrer.MobilePhone));
        setDataCellText(sheet.Range[row, col++], SharedUtilities.BooleanToStringYN(referrer.IsActive));
    }
}