﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: LabOrderFinancialContext.cs 783 2013-07-09 14:40:30Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using AutoMapper;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public class LabOrderFinancialContext
{
    public long InvoiceId { get; set; }
    public string OrderId { get; set; }
    public string PatientName { get; set; }
    public string PhoneNumber { get; set; }
    public string OrderNotes { get; set; }
    public string DiscountNotes { get; set; }
    public string PhysicianName { get; set; }
    public SexType Sex { get; set; }
    public string Age { get; set; }
    public DateTime? DoB { get; set; }
    public DateTime OrderDateTime { get; set; }
    public WorkflowStageType WorkflowStage { get; set; }
    public List<TransactionInfoSlice> Transactions { get; } = [];
    public InvoiceMaster InvoiceMaster { get; private set; }
    public short OrderingUserId { get; private set; }

    public static LabOrderFinancialContext AssembleFrom(SearchedLabOrderInfo slice)
    {
        Mapper.CreateMap<SearchedLabOrderInfo, LabOrderFinancialContext>();
        var dto = Mapper.Map<SearchedLabOrderInfo, LabOrderFinancialContext>(slice);
        IEnumerable<InvoiceTransactionDetailedExSlice> transactions = [];
        FaultHandler.Shield(() =>
        {
            dto.InvoiceMaster = InvoiceMasterRepository.FindInvoiceById(slice.InvoiceId);
            transactions = InvoiceTransactionsRepository.FindInvoiceTransactionsDetailedEx(slice.InvoiceId);
        });

        dto.Transactions.Clear();
        foreach (var txInfo in transactions) dto.Transactions.Add(TransactionInfoSlice.AssembleFrom(txInfo));
        return dto;
    }

    public static LabOrderFinancialContext AssembleFrom(LabOrderSearchResultSlice slice)
    {
        Mapper.CreateMap<LabOrderSearchResultSlice, LabOrderFinancialContext>()
            .ForMember(dest => dest.Sex, opt => opt.MapFrom(src => (SexType)src.Sex))
            .ForMember(dest => dest.WorkflowStage,
                opt => opt.MapFrom(src => (WorkflowStageType)src.WorkflowStage));
        var dto = Mapper.Map<LabOrderSearchResultSlice, LabOrderFinancialContext>(slice);
        IEnumerable<InvoiceTransactionDetailedExSlice> transactions = [];
        FaultHandler.Shield(() =>
        {
            dto.InvoiceMaster = InvoiceMasterRepository.FindInvoiceById(slice.InvoiceId);
            transactions = InvoiceTransactionsRepository.FindInvoiceTransactionsDetailedEx(slice.InvoiceId);
        });

        dto.Transactions.Clear();
        foreach (var txInfo in transactions) dto.Transactions.Add(TransactionInfoSlice.AssembleFrom(txInfo));
        return dto;
    }

    public static LabOrderFinancialContext AssembleFrom(PatientLabOrder order)
    {
        Mapper.CreateMap<PatientLabOrder, LabOrderFinancialContext>()
            .ForMember(dest => dest.PatientName, opt => opt.MapFrom(src => src.FullName))
            .ForMember(dest => dest.PhysicianName, opt => opt.MapFrom(src => src.GetReferringPhysicianName()))
            .ForMember(dest => dest.Sex, opt => opt.MapFrom(src => (SexType)src.Sex))
            .ForMember(dest => dest.WorkflowStage,
                opt => opt.MapFrom(src => (WorkflowStageType)src.WorkflowStage));
        var dto = Mapper.Map<PatientLabOrder, LabOrderFinancialContext>(order);
        IEnumerable<InvoiceTransactionDetailedExSlice> transactions = [];
        FaultHandler.Shield(() =>
        {
            dto.InvoiceMaster = InvoiceMasterRepository.FindInvoiceById(order.InvoiceId);
            transactions = InvoiceTransactionsRepository.FindInvoiceTransactionsDetailedEx(order.InvoiceId);
        });

        dto.Transactions.Clear();
        foreach (var txInfo in transactions) dto.Transactions.Add(TransactionInfoSlice.AssembleFrom(txInfo));
        return dto;
    }
}