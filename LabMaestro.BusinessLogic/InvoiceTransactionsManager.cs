﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: InvoiceTransactionsManager.cs 831 2013-07-18 08:27:00Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using CuttingEdge.Conditions;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public class InvoiceTransactionsManager
{
    private readonly PatientLabOrder _labOrder;
    private readonly SortedList<int, TransactionInfoSlice> _transactions;
    private readonly List<TransactionInfoSlice> _transactionsHistory;
    private int _currIndex;

    public InvoiceTransactionsManager(PatientLabOrder labOrder)
    {
        _transactions = new SortedList<int, TransactionInfoSlice>();
        _labOrder = labOrder;
        _currIndex = 0;
    }

    public bool RecordTransaction(
        InvoiceTransactionType txType,
        TransactionFlag txFlag,
        decimal txAmount,
        string txRemarks,
        decimal nonCashAmount,
        PaymentMethod method,
        string? pmtSrc = null,
        string? pmtRef = null
    )
    {
        Condition.Requires(txAmount + nonCashAmount, "Transaction Amount").IsGreaterOrEqual(0m);

        _transactions.Add(_currIndex++,
            new TransactionInfoSlice
            {
                TxType = txType,
                TxFlag = txFlag,
                TxAmount = txAmount,
                UserRemarks = txRemarks,
                PerformingUserId = CurrentUserContext.UserId,
                WorkShiftId = CurrentUserContext.WorkShiftId,
                WorkflowStage = (WorkflowStageType)_labOrder.WorkflowStage,
                NonCashAmount = nonCashAmount,
                PaymentMethod = method,
                PaymentSource = pmtSrc,
                PaymentReference = pmtRef,
            });

        /*
        switch (txType)
        {
            case InvoiceTransactionType.Payment:
                _invoiceCalc.AddPayment(txAmount + nonCashAmount);
                break;
            case InvoiceTransactionType.Refund:
                _invoiceCalc.AddRefund(txAmount);
                break;
            case InvoiceTransactionType.CashDiscount:
                _invoiceCalc.AddCashDiscount(txAmount);
                break;
        }
        */

        return true;
    }

    public void ApplyChanges()
    {
        if (_labOrder == null) throw new Exception("No lab order loaded!");

        if (_labOrder.IsCancelled)
            throw new Exception($"Lab order #{_labOrder.InvoiceId} ({_labOrder.OrderId}) has been canceled!");

        //_labOrder.InvoiceMaster.PaymentStatus = (byte)_invoiceCalc.DeducePaymentStatus();

        // Record all the transactions...
        var shift = FaultHandler.Shield(() => WorkShiftsRepository.FindUserShiftById(CurrentUserContext.UserId,
            CurrentUserContext.WorkShiftId));

        foreach (var slice in _transactions.Values)
        {
            switch (slice.TxType)
            {
                case InvoiceTransactionType.Payment:
                    if (slice.PaymentMethod == PaymentMethod.Cash)
                    {
                        _labOrder.InvoiceMaster.IssuePayment(shift, slice.TxAmount, slice.TxFlag, slice.UserRemarks,
                            slice.WorkflowStage);
                    }
                    else
                    {
                        _labOrder.InvoiceMaster.IssuePaymentEx(
                            shift,
                            slice.TxAmount,
                            slice.TxFlag,
                            slice.UserRemarks,
                            slice.WorkflowStage,
                            slice.NonCashAmount,
                            slice.PaymentMethod,
                            slice.PaymentSource,
                            slice.PaymentReference
                        );
                    }

                    break;
                case InvoiceTransactionType.Refund:
                    //TODO: investigate [authorizingUserId]
                    _labOrder.InvoiceMaster.IssueRefund(shift, slice.TxAmount, slice.TxFlag, slice.UserRemarks,
                        CurrentUserContext.UserId, slice.WorkflowStage);
                    break;
                case InvoiceTransactionType.CashDiscount:
                    _labOrder.InvoiceMaster.IssueDiscount(shift, slice.TxAmount, slice.TxFlag, slice.UserRemarks,
                        slice.WorkflowStage);
                    break;
            }
        }

        FaultHandler.Shield(PatientLabOrdersRepository.Save);

        _labOrder.InvoiceMaster.ReconcileInvoiceFinances(true);
        // TODO: If the order consists of non-reportable/aux items, adjust the workflowstage (fulfilled) if fully paid

        // Shift balances are now being updated by InvoiceMaster.IssueXXX() method.
        // shift.ReconcileShiftFinances();  // update the users shift financial info

        FaultHandler.Shield(PatientLabOrdersRepository.Save);

        /*
        FaultHandler.Shield(() => AuditTrailRepository.LogOrderWorkflowEvent(AuditEventType.ordNewLabOrder,
                                                                             _labOrder.InvoiceId,
                                                                             null,
                                                                             null,
                                                                             WorkflowStageType.OrderEntry));
        */

        AsyncEsbMessenger.SendMessagesAsync(EsbMessageChain.CreateNew().AddWorkflowEstimator(_labOrder.InvoiceId));
    }
}