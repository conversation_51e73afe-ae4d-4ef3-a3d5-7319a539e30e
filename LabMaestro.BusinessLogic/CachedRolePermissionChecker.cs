﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: CachedRolePermissionChecker.cs 876 2013-07-25 13:50:39Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Linq;
using System.Runtime.Caching;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;

namespace LabMaestro.BusinessLogic;

public static class CachedRolePermissionChecker
{
    private const string CACHE_KEY_PREFIX = @"$$USER_ROLE_PERMISSION_CHECKER$$_";
    private const string CACHE_TITLE = @"USER_ROLE_PERMISSION_CHECKER";
    private static readonly TimeSpan _cacheLifespan = TimeSpan.FromMinutes(5);
    private static MemoryCache _cache = new(CACHE_TITLE);
    private static DateTime _prefetchCacheExpiry = DateTime.MinValue;

    private static readonly CacheItemPolicy _policy = new()
    {
        SlidingExpiration = _cacheLifespan
    };

    public static bool UserHasPermission(short userId, string permCode)
    {
        var cacheKey = buildCacheKey(userId, permCode);

        if (!_cache.Contains(cacheKey))
        {
            var perm = FaultHandler.Shield(() => PermissionsRepository.UserHasPermCode(userId, permCode));
            _cache.Set(cacheKey, perm, _policy);
            return perm;
        }

        return (bool)_cache.Get(cacheKey);
    }

    private static string buildCacheKey(short userId, string permCode)
    {
        return string.Format("{0}{1}_{2}", CACHE_KEY_PREFIX, userId, permCode);
    }

    public static bool CheckPermission(string permCode)
    {
        return UserHasPermission(CurrentUserContext.UserId, permCode);
    }

    public static bool UserIsAdmin()
    {
        var key = buildCacheKey(CurrentUserContext.UserId, "$$IS_ADMIN$$");
        if (!_cache.Contains(key))
        {
            var user = FaultHandler.Shield(() => UsersRepository.FindById(CurrentUserContext.UserId));
            if (user != null && user.IsActive && user.RoleId != null)
            {
                var role = FaultHandler.Shield(() => RolesRepository.FindRoleById((short)user.RoleId));
                if (role != null && role.IsActive && role.IsAdmin)
                {
                    _cache.Set(key, true, _policy);
                    return true;
                }

                _cache.Set(key, false, _policy);
                return false;
            }
        }

        return (bool)_cache.Get(key);
    }

    public static void Reset()
    {
        _cache.Dispose();
        _cache = new MemoryCache(CACHE_TITLE);
    }

    public static void PrefetchCache(bool forceRefresh)
    {
        // don't overload the server
        if (!forceRefresh && DateTime.Now < _prefetchCacheExpiry)
            return;

        Reset();

        FaultHandler.Shield(() =>
        {
            if (CurrentUserContext.UserId > 0)
            {
                var user = UsersRepository.FindById(CurrentUserContext.UserId);
                if (user != null && user.IsActive && user.RoleId != null)
                {
                    var role = RolesRepository.FindRoleById((short)user.RoleId);
                    if (role != null && role.IsActive && role.IsAdmin)
                    {
                        // admin role... everything is permitted
                        var keys = PermissionsRepository
                            .FindAll()
                            .Where(x => x.IsActive)
                            .Select(p => buildCacheKey(CurrentUserContext.UserId, p.PermCode));

                        foreach (var key in keys) _cache.Set(key, true, _policy);
                    }
                    else
                    {
                        var permCodes = PermissionsRepository
                            .GetAllPermCodesForUser(CurrentUserContext.UserId);
                        foreach (var key in permCodes.Select(c => buildCacheKey(CurrentUserContext.UserId, c)))
                            _cache.Set(key, true, _policy);
                    }

                    _prefetchCacheExpiry = DateTime.Now.AddMinutes(5);
                }
            }
        });
    }
}