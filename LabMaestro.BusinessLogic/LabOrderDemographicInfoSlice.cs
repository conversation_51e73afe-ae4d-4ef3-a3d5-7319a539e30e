﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: LabOrderDemographicInfoSlice.cs 1532 2014-11-29 08:57:12Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using AutoMapper;
using CuttingEdge.Conditions;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class LabOrderDemographicInfoSlice
{
    public long InvoiceId { get; set; }
    public string OrderId { get; set; }
    public int? ReferrerId { get; set; }
    public string Title { get; set; }
    public string FirstName { get; set; }
    public string LastName { get; set; }
    public string PhoneNumber { get; set; }
    public string EmailAddress { get; set; }
    public string OrderNotes { get; set; }
    public string DiscountNotes { get; set; }
    public bool IsReferrerUnknown { get; set; }
    public bool DisallowReferral { get; set; }
    public string ReferrerCustomName { get; set; }
    public bool IsCancelled { get; set; }
    public SexType Sex { get; set; }
    public string Age { get; set; }
    public DateTime? DoB { get; set; }
    public DateTime OrderDateTime { get; set; }
    public WorkflowStageType WorkflowStage { get; set; }
    public ResultsReleaseFlagType ResultsReleaseFlag { get; set; }
    public string OrderingUserName { get; set; }
    public string FullName { get; set; }

    public bool IsExternalSubOrder { get; set; }
    //public string SubOrderTrackingId { get; set; }
    //public int? RequestingLabId { get; set; }

    public long? CustomerId { get; set; }

    //public long? RegisteredMemberId { get; set; }
    public short? AffiliateId { get; set; }
    public short? CorporateClientId { get; set; }
    public short? HealthPackageId { get; set; }
    public short? AssociateLabId { get; set; }
    public string AssociateLabAccessionId { get; set; }
    public bool IsHidden { get; set; }
    public bool IsNonFungibleOrder { get; set; }


    public void AssignFrom(LabOrderDemographicInfoSlice src)
    {
        Condition.Requires(src).IsNotNull();

        InvoiceId = src.InvoiceId;
        OrderId = src.OrderId;
        ReferrerId = src.ReferrerId;
        Title = src.Title;
        FirstName = src.FirstName;
        LastName = src.LastName;
        PhoneNumber = src.PhoneNumber;
        EmailAddress = src.EmailAddress;
        OrderNotes = src.OrderNotes;
        DiscountNotes = src.DiscountNotes;
        IsReferrerUnknown = src.IsReferrerUnknown;
        DisallowReferral = src.DisallowReferral;
        ReferrerCustomName = src.ReferrerCustomName;
        IsCancelled = src.IsCancelled;
        Sex = src.Sex;
        Age = src.Age;
        DoB = src.DoB;
        OrderDateTime = src.OrderDateTime;
        WorkflowStage = src.WorkflowStage;
        OrderingUserName = src.OrderingUserName;
        FullName = $"{src.FirstName} {src.LastName}";
        IsExternalSubOrder = src.IsExternalSubOrder;
        CorporateClientId = src.CorporateClientId;
        CustomerId = src.CustomerId;
        /*
        RequestingLabId = src.RequestingLabId;
        SubOrderTrackingId = src.SubOrderTrackingId;
        RegisteredMemberId = src.RegisteredMemberId;
        */
        AffiliateId = src.AffiliateId;
        AssociateLabId = src.AssociateLabId;
        AssociateLabAccessionId = src.AssociateLabAccessionId;
        IsHidden = src.IsHidden;
        IsNonFungibleOrder = src.IsNonFungibleOrder;
        HealthPackageId = src.HealthPackageId;
        ResultsReleaseFlag = ResultsReleaseFlag;
        SanitizeNames();
    }

    public static LabOrderDemographicInfoSlice AssembleFrom(SearchedLabOrderInfo slice)
    {
        Mapper.CreateMap<SearchedLabOrderInfo, LabOrderDemographicInfoSlice>()
            .ForMember(dest => dest.FullName, opt => opt.MapFrom(src => src.PatientName))
            .ForMember(dest => dest.ReferrerCustomName, opt => opt.MapFrom(src => src.ReferrerName));
        var dto = Mapper.Map<SearchedLabOrderInfo, LabOrderDemographicInfoSlice>(slice);
        dto.SanitizeNames();
        return dto;
    }

    public void SanitizeNames()
    {
        if (string.IsNullOrEmpty(FullName)) FullName = $"{FirstName} {LastName}";
        else if (string.IsNullOrEmpty(FirstName)) FirstName = FullName;
    }
}