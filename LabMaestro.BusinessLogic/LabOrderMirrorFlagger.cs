﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: LabOrderMirrorFlagger.cs 783 2013-07-09 14:40:30Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using CuttingEdge.Conditions;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

/// <summary>
///     Purpose of this class is to determine appropriate mirroring flag for a
///     given lab order based on certain configuration parameters
/// </summary>
internal sealed class LabOrderMirrorFlagger
{
    internal static void UpdateLabOrderMirrorFlag(long invoiceId)
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);

        if (shouldMirrorAllLabOrders() || invoiceIsEligibleForMirroring(invoiceId))
            FaultHandler.Shield(
                () => PatientLabOrdersRepository.UpdateLabOrderMirrorFlag(invoiceId, MirrorFlag.Eligible));
    }

    /// <summary>
    ///     Returns true if a boolean switch is set in the global configuration.
    ///     Typically the accounts department staff will toggle the flag.
    /// </summary>
    private static bool shouldMirrorAllLabOrders()
    {
        var value =
            FaultHandler.Shield(
                () => GlobalSettingsRepository.GetIntValue(GlobalSettingKeys.Finance.MirrorAllLabOrders));
        return value != 0;
    }

    /// <summary>
    ///     Returns true if the current invoice id eligible for mirroring.
    ///     Default mirror threshold is 3 - which means every third invoice is deemed eligible for mirroring.
    ///     We take the invoice id and divide it by the threshold value (which is fetched from the global
    ///     setting). If there's no modulus, the invoice is eligible for mirroring.
    /// </summary>
    private static bool invoiceIsEligibleForMirroring(long invoiceId)
    {
        try
        {
            var threshold = 0;
            FaultHandler.Shield(() =>
            {
                threshold = GlobalSettingsRepository.GetIntValue(GlobalSettingKeys.Finance.MirroringThreshold);

                // fall back to default threshold value
                if (threshold < 1 || threshold > 10)
                {
                    threshold = 3;
                    GlobalSettingsRepository.Save(GlobalSettingKeys.Finance.MirroringThreshold, threshold,
                        string.Empty);
                }
            });
            return invoiceId % threshold == 0;
        }
        catch
        {
            return false;
        }
    }
}