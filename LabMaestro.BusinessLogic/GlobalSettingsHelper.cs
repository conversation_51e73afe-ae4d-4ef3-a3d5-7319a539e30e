﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2015, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id$
// 
// Last modified: 2015.08.10 8:48 AM
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public static class GlobalSettingsHelper
{
    public static class Crm
    {
        public static string DefaultPassword =>
            FaultHandler.Shield(() => GlobalSettingsRepository.GetStringValue(GlobalSettingKeys.Crm.DefaultPassword, "ctg123"));
    }

    public static class ReportCollation
    {
        public static int RecentlyUpdatedBundlesUpdateInterval
        {
            get => getRecentlyUpdatedBundlesUpdateInterval();
            set => setRecentlyUpdatedBundlesUpdateInterval(value);
        }

        private static int getRecentlyUpdatedBundlesUpdateInterval()
        {
            var threshold = FaultHandler.Shield(
                () => GlobalSettingsRepository.GetIntValue(GlobalSettingKeys.ReportCollation.RecentlyUpdatedBundles_UpdateInterval));
            return threshold != -1 ? threshold : 1;
        }

        private static void setRecentlyUpdatedBundlesUpdateInterval(int value)
        {
            GlobalSettingsRepository.Save(GlobalSettingKeys.ReportCollation.RecentlyUpdatedBundles_UpdateInterval, value, null);
        }
    }

    public static class ResultEntry
    {
        public static WorkflowStageType RecentlyUpdatedBundlesMinimumThreshold
        {
            get => getRecentlyUpdatedBundlesMinimumThreshold();
            set => setRecentlyUpdatedBundlesMinimumThreshold(value);
        }

        private static WorkflowStageType getRecentlyUpdatedBundlesMinimumThreshold()
        {
            var threshold = GlobalSettingsRepository.GetIntValue(GlobalSettingKeys.ResultEntry.RecentlyUpdatedBundles_MinimumThreshold);
            return threshold != -1 ? (WorkflowStageType)threshold : WorkflowStageType.ResultValidation;
        }

        private static void setRecentlyUpdatedBundlesMinimumThreshold(WorkflowStageType wfStage)
        {
            GlobalSettingsRepository.Save(GlobalSettingKeys.ResultEntry.RecentlyUpdatedBundles_MinimumThreshold, (int)wfStage, null);
        }
    }

    public static class System
    {
        public static string IPSubnetPrefix
        {
            get =>
                FaultHandler.Shield(() => GlobalSettingsRepository.GetStringValue(GlobalSettingKeys.System.IPSubnetPrefix));
            set =>
                FaultHandler.Shield(() => GlobalSettingsRepository.Save(GlobalSettingKeys.System.IPSubnetPrefix, -1, value));
        }

        public static class Surcharge
        {
            public static int DefaultPlanId
                => FaultHandler.Shield(() => GlobalSettingsRepository.GetIntValue(GlobalSettingKeys.System.SurchargeDefaultPlanId));

            public static int RatePaisa
                => FaultHandler.Shield(() => GlobalSettingsRepository.GetIntValue(GlobalSettingKeys.System.SurchargeRatePaisa, 60));

            public static int BillableItemId
                => FaultHandler.Shield(() => GlobalSettingsRepository.GetIntValue(GlobalSettingKeys.System.SurchargeBillableItemId));

            public static int MaxAmount
                => FaultHandler.Shield(() => GlobalSettingsRepository.GetIntValue(GlobalSettingKeys.System.SurchargeMaxAmount, 200));

            public static int MinAmount
                => FaultHandler.Shield(() => GlobalSettingsRepository.GetIntValue(GlobalSettingKeys.System.SurchargeMinAmount, 20));
        }

        public static string ThermalLabelRemarks
        {
            get => FaultHandler.Shield(() => GlobalSettingsRepository.GetStringValue(GlobalSettingKeys.System.ThermalLabelRemarks));
            set => FaultHandler.Shield(() => GlobalSettingsRepository.Save(GlobalSettingKeys.System.ThermalLabelRemarks, -1, value));
        }

        public static string PrintConfirmationReasons
        {
            get => FaultHandler.Shield(() => GlobalSettingsRepository.GetStringValue(GlobalSettingKeys.System.PrintConfirmationReasons));
            set => FaultHandler.Shield(() => GlobalSettingsRepository.Save(GlobalSettingKeys.System.PrintConfirmationReasons, -1, value));
        }

        public static string DiscountRefundReasons
        {
            get => FaultHandler.Shield(() => GlobalSettingsRepository.GetStringValue(GlobalSettingKeys.System.DiscountRefundReasons));
            set => FaultHandler.Shield(() => GlobalSettingsRepository.Save(GlobalSettingKeys.System.DiscountRefundReasons, -1, value));
        }
    }
}