﻿//  * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
//  
//  Copyright (c) 2012, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
//  All rights reserved.
//  
//  Redistribution and use in source and binary forms, with or without
//  modification, are permitted provided that the following conditions are met:
//     * Redistributions of source code must retain the above copyright
//       notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above copyright
//       notice, this list of conditions and the following disclaimer in the
//       documentation and/or other materials provided with the distribution.
//     * Neither the name of the <organization> nor the
//       names of its contributors may be used to endorse or promote products
//       derived from this software without specific prior written permission.
//  
//  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
//  ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
//  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
//  DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
//  DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
//  (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
//  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
//  ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
//  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
//  SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
//  
//  $Id: BillableItemsTriageGroup.cs 723 2013-07-03 06:56:29Z <EMAIL> $
//  
//  * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System.Collections.Generic;
using System.Linq;
using LabMaestro.Domain;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class BillableItemsTriageGroup
{
    private readonly List<BillableItemSlice> _itemsListRaw;

    public BillableItemsTriageGroup(string name)
    {
        Name = name;
        _itemsListRaw = new List<BillableItemSlice>();
        OptimizedItems = new List<BillableItemSlice>();
    }

    internal string Name { get; private set; }

    public List<BillableItemSlice> OptimizedItems { get; }

    internal void AddItem(BillableItemSlice item)
    {
        _itemsListRaw.Add(item);
    }

    internal void RemoveItem(BillableItemSlice item)
    {
        var slice = _itemsListRaw.FirstOrDefault(x => x.Id == item.Id && x.Quantity == item.Quantity);
        if (slice != null) _itemsListRaw.Remove(slice);
    }

    internal void DecreaseItemQuantity(BillableItemSlice item)
    {
        var slice = _itemsListRaw.FirstOrDefault(x => x.Id == item.Id && x.Quantity >= item.Quantity);
        if (slice != null)
        {
            slice.Quantity -= item.Quantity;
            if (slice.Quantity <= 0)
                _itemsListRaw.Remove(slice);
        }
    }

    internal void Reset()
    {
        _itemsListRaw.Clear();
        OptimizedItems.Clear();
    }

    internal void Optimize()
    {
        OptimizedItems.Clear();

        foreach (var slice in _itemsListRaw)
        {
            var ordered = OptimizedItems.FirstOrDefault(x => x.Id == slice.Id);
            if (ordered == null)
            {
                OptimizedItems.Add(BillableItemSlice.AssembleFrom(slice));
            }
            else
            {
                if (slice.OptimizationLevel == BillableItemOptimizationLevelType.DoNotOptimize)
                {
                    // just tally up the quantities
                    ordered.Quantity += slice.Quantity;
                }
                else
                {
                    // retain only the highest quantity
                    if (slice.Quantity > ordered.Quantity)
                        ordered.Quantity = slice.Quantity;
                }
            }
        }
    }
}