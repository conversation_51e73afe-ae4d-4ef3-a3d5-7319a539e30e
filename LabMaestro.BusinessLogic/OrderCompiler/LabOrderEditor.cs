﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id$
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using CuttingEdge.Conditions;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class LabOrderEditor
{
    private readonly BillableItemsCompiler _biCompiler = new();
    private readonly List<BillableItemSlice> _exclusiveBillableItems = new();

    public LabOrderEditor(long invoiceId)
    {
        InvoiceId = invoiceId;
        OriginalInvoiceSummary = InvoiceSummary.AssembleFrom(InvoiceId);
        WorkingInvoiceSummary = InvoiceSummary.AssembleFrom(InvoiceId);
        loadLabOrderDetails();
    }

    public long InvoiceId { get; private set; }
    public string OrderId { get; set; }
    public bool IsCancelled { get; set; }
    public WorkflowStageType WorkflowStage { get; set; }

    public int NumberOfActiveOrderedTests =>
        OrderedTests.Count(x =>
            !x.OrderedTestIsCancelled &&
            x.PendingModification is PendingModificationType.None or PendingModificationType.Addition);

    public List<OrderedTestWithWorkflowSlice> OrderedTests { get; } = new();

    public List<OrderedTestWithWorkflowSlice> EffectiveOrderedTests =>
        OrderedTests.Where(x =>
                !x.OrderedTestIsCancelled &&
                x.PendingModification is PendingModificationType.None or PendingModificationType.Addition)
            .ToList();

    public decimal OrderedBillableItemsTotal => OrderedBillableItems.Sum(bi => bi.LineTotal);

    public List<BillableItemSlice> OrderedBillableItems { get; private set; } = new();

    public InvoiceSummary OriginalInvoiceSummary { get; }

    public InvoiceSummary WorkingInvoiceSummary { get; private set; }

    private void loadLabOrderDetails()
    {
        var tests = FaultHandler.Shield(() => OrderedTestsRepository.GetAllOrderedTestsInInvoice(InvoiceId));
        foreach (var test in tests)
            // TODO: populate from InvoiceOrderedTest
            OrderedTests.Add(new OrderedTestWithWorkflowSlice(test.OrderedTestId));

        var items =
            FaultHandler.Shield(() => OrderedBillableItemsRepository.GetAllOrderedBillableItemsInInvoice(InvoiceId));
        foreach (var item in items) OrderedBillableItems.Add(BillableItemSlice.AssembleFrom(item));
    }

    public void ChangeBillableItemQuantity(BillableItemSlice slice, short newQuantity)
    {
        Condition.Requires(slice).IsNotNull();
        Condition.Requires(slice.Id).IsGreaterThan(0);

        var item = OrderedBillableItems.FirstOrDefault(x => x.Id == slice.Id);
        if (item != null)
        {
            if (newQuantity <= 0)
                OrderedBillableItems.Remove(item);
            else
                item.Quantity = newQuantity;
        }
    }

    public void CompileBillableItemsList()
    {
        _biCompiler.Reset();

        foreach (var testSlice in EffectiveOrderedTests) _biCompiler.AddTest(testSlice);

        foreach (var item in _exclusiveBillableItems) _biCompiler.AddExclusiveItem(item);

        _biCompiler.Optimize();
        OrderedBillableItems = _biCompiler.Compile();
    }

    private List<BillableItemSlice> compileBillableItemsInternal(List<OrderedTestWithWorkflowSlice> tests)
    {
        var biComp = new BillableItemsCompiler();
        foreach (var testSlice in tests) biComp.AddTest(testSlice);

        foreach (var item in _exclusiveBillableItems) biComp.AddExclusiveItem(item);

        biComp.Optimize();
        return biComp.Compile();
    }

    public void Reset()
    {
        IsCancelled = false;
        InvoiceId = -1;
        OrderId = null;
        WorkflowStage = WorkflowStageType.OrderEntry;

        OrderedBillableItems.Clear();
        _exclusiveBillableItems.Clear();
        OrderedTests.Clear();
    }

    public void ClearOrderedTests() => OrderedTests.Clear();

    public OrderableTestSlice AddTest(short testId, string reason)
    {
        Condition.Requires(testId > 0);
        var slice = FaultHandler.Shield(() =>
            new OrderedTestWithWorkflowSlice(-1, OrderableTestSliceCatalogRepository.GetOrderableTestDetails(testId))
            {
                PendingModification = PendingModificationType.Addition,
                PendingOperationReason = reason
            });
        slice.EstimateDeliveryTime(AppSysRepository.GetServerTime());
        OrderedTests.Add(slice);
        return slice;
    }

    public OrderableTestSlice AddTest(OrderedTestWithWorkflowSlice test, DateTime bookingTime)
    {
        Condition.Requires(test).IsNotNull();
        var slice = test.Clone();
        slice.PendingModification = PendingModificationType.Addition;
        slice.EstimateDeliveryTime(bookingTime);
        OrderedTests.Add(slice);
        return slice;
    }

    public void RemoveTest(OrderedTestWithWorkflowSlice slice) => OrderedTests.Remove(slice);

    public void CancelTest(OrderedTestWithWorkflowSlice slice)
    {
        slice.OrderedTestIsCancelled = true;
        slice.PendingModification = PendingModificationType.Cancellation;
    }

    public void CancelTest(short testId, string reason)
    {
        var slice = OrderedTests.SingleOrDefault(x => x.TestId == testId);
        if (slice != null)
        {
            slice.PendingOperationReason = reason;
            CancelTest(slice);
        }
    }

    public bool TestAlreadyOrdered(short testId) => OrderedTests.Any(x => x.TestId == testId);

    public bool RemoveBillableItem(BillableItemSlice slice)
    {
        // remove the slice from the exclusive items list
        var item = _exclusiveBillableItems.SingleOrDefault(x => x.Id == slice.Id && x.Quantity == slice.Quantity);
        if (item != null) _exclusiveBillableItems.Remove(item);

        // remove it from the curated (compiled) list
        item = OrderedBillableItems.SingleOrDefault(x => x.Id == slice.Id && x.Quantity == slice.Quantity);
        return item != null && OrderedBillableItems.Remove(item);
    }

    public void ClearBillableItems()
    {
        _exclusiveBillableItems.Clear();
        OrderedBillableItems.Clear();
    }

    public bool ModifyBillableItemQuantity(short itemId, short qtyOld, short qtyNew)
    {
        var item = OrderedBillableItems.SingleOrDefault(x => x.Id == itemId && x.Quantity == qtyOld);
        if (item != null)
        {
            item.Quantity = qtyNew;
            return true;
        }

        return false;
    }

    public void AddBillableItem(short itemId, short qty)
    {
        var item = BillableItemSlice.AssembleFrom(FaultHandler.Shield(() =>
            OrderableBillableItemsRepository.FindById(itemId)));
        item.Quantity = qty;
        item.LineTotal = qty * item.UnitPrice;
        _exclusiveBillableItems.Add(item);
    }

    public bool HasBillableItem(short itemId) => OrderedBillableItems.Any(x => x.Id == itemId);

    public bool CheckTestCancelability(OrderedTestWithWorkflowSlice slice, bool updateDatabase, out string message)
    {
        message = string.Empty;
        if (!slice.TestCanBeCancelled(true))
        {
            message = "This Lab Test cannot be canceled because it has been processed already!\n";
            message += string.Format("Expected Workflow Status: '{0}'\nCurrent Workflow Status: '{1}'",
                EnumUtils.EnumDescription(slice.PerformingLabPostOrderEntryWorkflowStage),
                EnumUtils.EnumDescription(slice.OrderedTestWorkflowStage));
            return false;
        }

        var testPrice = slice.CalculateEffectivePrice();

        // perform a dry run...
        var calc = new InvoiceCalculator();
        calc.SetDiscountAmount(OriginalInvoiceSummary.DiscountAmount);
        calc.SetPaidAmount(OriginalInvoiceSummary.PaidAmount);
        var orderedTests = EffectiveOrderedTests;
        orderedTests.Remove(slice);
        calc.CalculateInvoice(orderedTests, compileBillableItemsInternal(orderedTests));

        if (calc.DiscountAmount > calc.GrossPayable)
        {
            message = "Discount amount exceeds the Gross bill!\n";
            message += "You must issue discount rebate before canceling this test.\n\n";
            message += string.Format("* Base Test Price: {0}\n* Effective Test Price: {1}\n",
                SharedUtilities.MoneyToStringPlain(slice.ListPrice),
                SharedUtilities.MoneyToStringPlain(testPrice));
            var rebate = calc.DiscountAmount - calc.GrossPayable;
            var discount = calc.DiscountAmount - rebate;
            var net = calc.GrossPayable - discount;
            var refund = 0m;
            if (calc.PaidAmount > net) refund = calc.PaidAmount - net;
            message += string.Format("* Gross Payable (post-cancellation): {0}\n* Min. Discount Rebate amount: {1}\n",
                SharedUtilities.MoneyToStringPlain(calc.GrossPayable),
                SharedUtilities.MoneyToStringPlain(rebate));
            message += string.Format("* Net Payable (post-rebate): {0}\n* Refund amount: {1}",
                SharedUtilities.MoneyToStringPlain(net),
                SharedUtilities.MoneyToStringPlain(refund));
            return false;
        }

        if (calc.PaidAmount > calc.NetPayable)
        {
            message = "Amount paid exceeds the Net bill!\n";
            message += "You must issue refund before canceling this test.\n";
            message += string.Format("Effective Test Price: {0}\nMinimum Refundable Amount: {1}",
                SharedUtilities.MoneyToStringPlain(testPrice),
                SharedUtilities.MoneyToStringPlain(calc.PaidAmount - calc.NetPayable));
            return false;
        }

        return true;
    }

    public int GetActiveResultBundlesCountForInvoice() => FaultHandler.Shield(() =>
        ResultBundlesRepository.GetActiveResultBundlesCountForInvoice(InvoiceId));

    public void ApplyChanges(InvoiceCalculator calc)
    {
        /*
         * TODO:
         * If Order is Cancelled => cancel and exit
         * Cycle through all tests:
         *  Apply pending operations
         * Remove all billable items
         * Re-populate billable items
         * Persist invoice / payment status
         * Reconfigure/Add result bundles
         */
        WorkingInvoiceSummary = InvoiceSummary.AssembleFrom(calc);
        if (IsCancelled)
        {
            FaultHandler.Shield(() =>
            {
                PatientLabOrdersRepository.CancelLabOrder(InvoiceId, WorkflowStageType.Canceled);
                AuditTrailRepository.LogLabOrderEvent(InvoiceId, AuditEventType.ordOrderCanceled);
                // todo: lifecycle hook OrderLifecycleEventType.OrderCanceled
            });
            return;
        }

        new LabOrderEditorDbPersister(this).Execute();
    }

    /// <summary>
    ///     Checks if the order can be canceled.
    /// </summary>
    /// <param name="deepCheck">
    ///     if set to <c>true</c> it will scan ordered tests to see if they have been modified.
    /// </param>
    /// <returns>
    ///     <c>true</c> if order is eligible for cancellation, <c>false</c> otherwise
    /// </returns>
    public bool LabOrderCanBeCanceled(bool deepCheck)
    {
        OrderedTestsRepository.Reset(); // Clear the cache

        return FaultHandler.Shield(() =>
        {
            var testsCount = OrderedTestsRepository.GetActiveOrderedTestsCountInInvoice(InvoiceId);
            if (testsCount == 0) return true;

            if (!deepCheck) return false;

            var activeTests = OrderedTestsRepository.GetActiveOrderedTestsInInvoice(InvoiceId);
            return activeTests.All(t =>
                OrderedTestsRepository.CheckOrderedTestWorkflowStageIsModified(t.OrderedTestId));
        });
    }
}