﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: LabOrderContextInfo.cs 1532 2014-11-29 08:57:12Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using CuttingEdge.Conditions;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class LabOrderContextInfo : ILabOrderContextInfo
{
    private readonly BillableItemsCompiler _biCompiler = new();
    private readonly List<BillableItemSlice> _exclusiveBillableItems = [];
    private readonly List<BillableItemSlice> _staticBillableItems = [];

    private readonly List<OrderableTestSlice> _orderedTests = [];
    private List<BillableItemSlice> _orderedBillableItems = [];

    public short? HealthPackageId { get; set; }

    public static LabOrderContextInfo AssembleFrom(PatientLabOrder order)
    {
        Condition.Requires(order).IsNotNull();
        Mapper.CreateMap<PatientLabOrder, LabOrderContextInfo>()
              .ForMember(dest => dest.OrderedTests, opt => opt.Ignore())
              .ForMember(dest => dest.OrderedBillableItems, opt => opt.Ignore())
              .ForMember(dest => dest.Sex, opt => opt.MapFrom(src => (SexType)src.Sex))
              .ForMember(dest => dest.ReferrerCustomName, opt => opt.MapFrom(src => src.GetReferringPhysicianName()))
              .ForMember(dest => dest.WorkflowStage, opt => opt.MapFrom(src => (WorkflowStageType)src.WorkflowStage));

        var dto = Mapper.Map<PatientLabOrder, LabOrderContextInfo>(order);
        dto.OrderDemoInfo.SanitizeNames();
        return dto;
    }

    public static LabOrderContextInfo AssembleFrom(HeldLabOrder order)
    {
        Condition.Requires(order).IsNotNull();
        Mapper.CreateMap<HeldLabOrder, LabOrderContextInfo>()
              .ForMember(dest => dest.Sex, opt => opt.MapFrom(src => (SexType)src.Sex))
              .ForMember(dest => dest.OrderNotes, opt => opt.MapFrom(src => src.Notes));

        var ctxInfo = Mapper.Map<HeldLabOrder, LabOrderContextInfo>(order);
        ctxInfo.ClearOrderedTests();

        if (order.HeldLabOrderTests.Count > 0)
            foreach (var ht in order.HeldLabOrderTests)
                if (ht.ResultsETA == null)
                    ctxInfo.AddTest(ht.LabTestId);
                else
                    ctxInfo.AddTestWithDeliveryTime(ht.LabTestId, (DateTime)ht.ResultsETA);
        return ctxInfo;
    }

    public void ChangeBillableItemQuantity(BillableItemSlice slice, short newQuantity)
    {
        Condition.Requires(slice).IsNotNull();
        Condition.Requires(slice.Id).IsGreaterThan(0);

        var item = _orderedBillableItems.FirstOrDefault(x => x.Id == slice.Id);
        if (item != null) {
            if (newQuantity <= 0)
                _orderedBillableItems.Remove(item);
            else
                item.Quantity = newQuantity;
        }
    }

    public void CompileBillableItemsList()
    {
        _biCompiler.Reset();

        foreach (var testSlice in OrderedTests) _biCompiler.AddTest(testSlice);

        foreach (var item in _exclusiveBillableItems) _biCompiler.AddExclusiveItem(item);

        foreach (var item in _staticBillableItems) _biCompiler.AddStaticItem(item);

        _biCompiler.Optimize();
        _orderedBillableItems = _biCompiler.Compile();
    }

    public void Reset()
    {
        IsCancelled = false;
        InvoiceId = -1;
        OrderId = string.Empty;
        OrderDateTime = DateTime.MinValue;
        WorkflowStage = WorkflowStageType.OrderEntry;
        ReferrerId = null;
        AffiliateId = null;
        /*
        RequestingLabId = null;
        SubOrderTrackingId = string.Empty;
        */
        AssociateLabId = null;
        AssociateLabAccessionId = string.Empty;
        IsHidden = false;
        IsNonFungibleOrder = false;
        CustomerId = null;
        //RegisteredMemberId = null;
        CorporateClientId = null;
        Title = string.Empty;
        FirstName = string.Empty;
        LastName = string.Empty;
        PhoneNumber = string.Empty;
        OrderNotes = string.Empty;
        DiscountNotes = string.Empty;
        IsReferrerUnknown = false;
        DisallowReferral = false;
        ReferrerCustomName = string.Empty;
        Sex = SexType.Unknown;
        Age = string.Empty;
        DoB = null;
        EmailAddress = string.Empty;

        _orderedBillableItems.Clear();
        _exclusiveBillableItems.Clear();
        _staticBillableItems.Clear();
        _orderedTests.Clear();
    }

    public void ClearOrderedTests() => _orderedTests.Clear();

    public void ClearBillableItems()
    {
        _orderedBillableItems.Clear();
        _exclusiveBillableItems.Clear();
    }

    public OrderableTestSlice AddTest(short testId)
    {
        Condition.Requires(testId > 0);
        var slice = FaultHandler.Shield(() => OrderableTestSliceCatalogRepository.FindById(testId));
        slice.EstimateDeliveryTime(AppSysRepository.GetServerTime());
        _orderedTests.Add(slice);
        return slice;
    }

    public OrderableTestSlice AddTest(OrderableTestSlice test, DateTime bookingTime)
    {
        Condition.Requires(test).IsNotNull();
        var slice = test.Clone();
        slice.EstimateDeliveryTime(bookingTime);
        _orderedTests.Add(slice);
        return slice;
    }

    public OrderableTestSlice AddTestWithDeliveryTime(short testId, DateTime etd)
    {
        Condition.Requires(testId > 0);
        var slice = FaultHandler.Shield(() => OrderableTestSliceCatalogRepository.FindById(testId));
        slice.DeliveryTime = etd;
        _orderedTests.Add(slice);
        return slice;
    }

    public bool RemoveTest(OrderableTestSlice slice) => RemoveTest(slice.TestId);

    public bool RemoveTest(short testId)
    {
        var slice = _orderedTests.SingleOrDefault(x => x.TestId == testId);
        return slice != null && _orderedTests.Remove(slice);
    }

    public bool TestAlreadyOrdered(short testId) => _orderedTests.Any(x => x.TestId == testId);

    public LabOrderContextInfo ShallowClone()
    {
        var clone = MemberwiseClone() as LabOrderContextInfo;
        clone.SetDemographic(OrderDemoInfo);
        return clone;
    }

    internal void SetDemographic(LabOrderDemographicInfoSlice demo) => OrderDemoInfo.AssignFrom(demo);

    public bool RemoveBillableItem(BillableItemSlice slice)
    {
        // remove the slice from the exclusive items list
        var item = _exclusiveBillableItems.SingleOrDefault(x => x.Id == slice.Id && x.Quantity == slice.Quantity);
        if (item != null) _exclusiveBillableItems.Remove(item);

        // remove it from the curated (compiled) list
        item = _orderedBillableItems.SingleOrDefault(x => x.Id == slice.Id && x.Quantity == slice.Quantity);
        return item != null && _orderedBillableItems.Remove(item);
    }

    public bool ModifyBillableItemQuantity(short itemId, short qtyOld, short qtyNew)
    {
        var item = _orderedBillableItems.SingleOrDefault(x => x.Id == itemId && x.Quantity == qtyOld);
        if (item == null) return false;

        item.Quantity = qtyNew;
        return true;
    }

    public void AddBillableItem(short itemId, short qty)
    {
        var item = FaultHandler.Shield(() => BillableItemSlice.AssembleFrom(OrderableBillableItemsRepository.FindById(itemId)));
        item.Quantity = qty;
        item.LineTotal = qty * item.UnitPrice;
        _exclusiveBillableItems.Add(item);
    }

    public bool HasBillableItem(short itemId) => OrderedBillableItems.Any(x => x.Id == itemId);

    public static LabOrderContextInfo? GetHeldLabOrderContextInfoFromRepository(long invoiceId)
    {
        var heldLabOrder = FaultHandler.Shield(() => HeldLabOrdersRepository.GetHeldLabOrder(invoiceId));
        return heldLabOrder != null ? AssembleFrom(heldLabOrder) : null;
    }

    public static LabOrderContextInfo? FindOrderContext(long invoiceId)
    {
        var order = FaultHandler.Shield(() => PatientLabOrdersRepository.FindById(invoiceId));
        return order == null ? null : AssembleFrom(order);
    }

    private WorkflowStageType getNextWorkflowStageForTest(short testId)
    {
        var key = $"labtest_next_workflow_{testId}";
        if (AppCache.Contains(key)) return (WorkflowStageType)AppCache.Get(key);

        var test = LabTestsRepository.FindTestWithLab(testId);
        var wfStageNext = test.ReportingLab is { PostOrderEntryWorkflowStage: not null }
            ? (WorkflowStageType)test.ReportingLab.PostOrderEntryWorkflowStage
            : WorkflowStageType.OrderEntry;
        AppCache.Store(key, wfStageNext);
        return wfStageNext;
    }

    public NewLabOrderContextDto ToNewLabOrderContextDto()
    {
        var dto = new NewLabOrderContextDto
        {
            Title = Title,
            FirstName = FirstName,
            LastName = LastName,
            Age = Age,
            DoB = DoB,
            Sex = (byte)Sex,
            WorkflowStage = (byte)WorkflowStage,
            PhoneNumber = PhoneNumber,
            EmailAddress = EmailAddress,
            OrderNotes = OrderNotes,
            IsReferrerUnknown = IsReferrerUnknown,
            ReferrerCustomName = ReferrerCustomName,
            DisallowReferral = DisallowReferral,
            ReferrerId = ReferrerId,
            CustomerId = CustomerId,
            //RegisteredMemberId = RegisteredMemberId,
            AffiliateId = AffiliateId,
            CorporateClientId = CorporateClientId,
            HealthPackageId = HealthPackageId,
            IsNonFungibleOrder = IsNonFungibleOrder,
            IsHidden = IsHidden,
            MirrorFlag = (byte)MirrorFlag.None,
            ResultsReleaseFlag = (byte)ResultsReleaseFlag,
        };
        dto.EmailTestResults = !string.IsNullOrEmpty(dto.EmailAddress);

        if (OrderDemoInfo.AssociateLabId > 0) {
            /*
            dto.RequestingLabId = OrderDemoInfo.RequestingLabId;
            dto.SubOrderTrackingId = OrderDemoInfo.SubOrderTrackingId;
            */
            dto.AssociateLabId = OrderDemoInfo.AssociateLabId;
            dto.IsExternalSubOrder = OrderDemoInfo.IsExternalSubOrder;
            dto.AssociateLabAccessionId = OrderDemoInfo.AssociateLabAccessionId;

            dto.EmailTestResults = false;
            //dto.EmailAddress = null;
            //dto.PhoneNumber = null;
            dto.DisallowReferral = true;
        }

        foreach (var testSlice in OrderedTests) {
            var wfNext = FaultHandler.Shield(() => getNextWorkflowStageForTest(testSlice.TestId));
            dto.OrderedTests.Add(new NewOrderedTestDto
            {
                LabTestId = testSlice.TestId,
                ResultsETA = testSlice.DeliveryTime,
                UnitPrice = testSlice.ListPrice,
                WorkflowStage = (byte)wfNext
            });
        }

        foreach (var itemSlice in OrderedBillableItems)
            dto.OrderedItems.Add(new NewOrderedBillableItemDto
            {
                BillableItemId = itemSlice.Id,
                Quantity = itemSlice.Quantity,
                UnitPrice = itemSlice.UnitPrice
            });
        return dto;
    }

    public LabOrderDemographicInfoSlice OrderDemoInfo { get; } = new();

    public string EmailAddress
    {
        get => OrderDemoInfo.EmailAddress;
        set => OrderDemoInfo.EmailAddress = value;
    }

    public bool IsExternalSubOrder
    {
        get => OrderDemoInfo.IsExternalSubOrder;
        set => OrderDemoInfo.IsExternalSubOrder = value;
    }

    /*
    public string SubOrderTrackingId
    {
        get => OrderDemoInfo.SubOrderTrackingId;
        set => OrderDemoInfo.SubOrderTrackingId = value;
    }

    public int? RequestingLabId
    {
        get => OrderDemoInfo.RequestingLabId;
        set => OrderDemoInfo.RequestingLabId = value;
    }
    */
    public short? AssociateLabId
    {
        get => OrderDemoInfo.AssociateLabId;
        set => OrderDemoInfo.AssociateLabId = value;
    }

    public long InvoiceId
    {
        set => OrderDemoInfo.InvoiceId = value;
        get => OrderDemoInfo.InvoiceId;
    }

    public string OrderId
    {
        set => OrderDemoInfo.OrderId = value;
        get => OrderDemoInfo.OrderId;
    }

    public int? ReferrerId
    {
        set => OrderDemoInfo.ReferrerId = value;
        get => OrderDemoInfo.ReferrerId;
    }

    public short? AffiliateId
    {
        set => OrderDemoInfo.AffiliateId = value;
        get => OrderDemoInfo.AffiliateId;
    }

    public string AssociateLabAccessionId
    {
        set => OrderDemoInfo.AssociateLabAccessionId = value;
        get => OrderDemoInfo.AssociateLabAccessionId;
    }

    public short? CorporateClientId
    {
        set => OrderDemoInfo.CorporateClientId = value;
        get => OrderDemoInfo.CorporateClientId;
    }

    public long? CustomerId
    {
        set => OrderDemoInfo.CustomerId = value;
        get => OrderDemoInfo.CustomerId;
    }

    /*
    public long? RegisteredMemberId
    {
        set => OrderDemoInfo.RegisteredMemberId = value;
        get => OrderDemoInfo.RegisteredMemberId;
    }
    */

    public string Title
    {
        set => OrderDemoInfo.Title = value;
        get => OrderDemoInfo.Title;
    }

    public string FirstName
    {
        set => OrderDemoInfo.FirstName = value;
        get => OrderDemoInfo.FirstName;
    }

    public string LastName
    {
        set => OrderDemoInfo.LastName = value;
        get => OrderDemoInfo.LastName;
    }

    public string PhoneNumber
    {
        set => OrderDemoInfo.PhoneNumber = value;
        get => OrderDemoInfo.PhoneNumber;
    }

    public string OrderNotes
    {
        set => OrderDemoInfo.OrderNotes = value;
        get => OrderDemoInfo.OrderNotes;
    }

    public string DiscountNotes
    {
        set => OrderDemoInfo.DiscountNotes = value;
        get => OrderDemoInfo.DiscountNotes;
    }

    public bool IsReferrerUnknown
    {
        set => OrderDemoInfo.IsReferrerUnknown = value;
        get => OrderDemoInfo.IsReferrerUnknown;
    }

    public bool DisallowReferral
    {
        set => OrderDemoInfo.DisallowReferral = value;
        get => OrderDemoInfo.DisallowReferral;
    }

    public string ReferrerCustomName
    {
        set => OrderDemoInfo.ReferrerCustomName = value;
        get => OrderDemoInfo.ReferrerCustomName;
    }

    public bool IsCancelled
    {
        set => OrderDemoInfo.IsCancelled = value;
        get => OrderDemoInfo.IsCancelled;
    }

    public SexType Sex
    {
        set => OrderDemoInfo.Sex = value;
        get => OrderDemoInfo.Sex;
    }

    public string Age
    {
        set => OrderDemoInfo.Age = value;
        get => OrderDemoInfo.Age;
    }

    public DateTime? DoB
    {
        set => OrderDemoInfo.DoB = value;
        get => OrderDemoInfo.DoB;
    }

    public DateTime OrderDateTime
    {
        set => OrderDemoInfo.OrderDateTime = value;
        get => OrderDemoInfo.OrderDateTime;
    }

    public WorkflowStageType WorkflowStage
    {
        set => OrderDemoInfo.WorkflowStage = value;
        get => OrderDemoInfo.WorkflowStage;
    }

    public ResultsReleaseFlagType ResultsReleaseFlag
    {
        set => OrderDemoInfo.ResultsReleaseFlag = value;
        get => OrderDemoInfo.ResultsReleaseFlag;
    }

    public string OrderingUserName
    {
        set => OrderDemoInfo.OrderingUserName = value;
        get => OrderDemoInfo.OrderingUserName;
    }

    public List<OrderableTestSlice> OrderedTests => _orderedTests.ToList();

    public List<BillableItemSlice> OrderedBillableItems => _orderedBillableItems.ToList();

    public bool IsHidden
    {
        set => OrderDemoInfo.IsHidden = value;
        get => OrderDemoInfo.IsHidden;
    }

    public bool IsNonFungibleOrder
    {
        set => OrderDemoInfo.IsNonFungibleOrder = value;
        get => OrderDemoInfo.IsNonFungibleOrder;
    }

    public void UpsertStaticBillableItem(BillableItemSlice item)
    {
        if (!_staticBillableItems.Contains(item))
            _staticBillableItems.Add(item);
        else {
            _staticBillableItems[_staticBillableItems.IndexOf(item)] = item;
        }
    }

    public void ClearStaticBillableItems() => _staticBillableItems.Clear();
}