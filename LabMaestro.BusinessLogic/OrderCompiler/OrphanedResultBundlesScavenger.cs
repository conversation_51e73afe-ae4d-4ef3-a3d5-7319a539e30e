﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: OrphanedResultBundlesScavenger.cs 1097 2013-11-24 13:34:15Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System.Linq;
using LabMaestro.Domain;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class OrphanedResultBundlesScavenger
{
    public OrphanedResultBundlesScavenger(long invoiceId)
    {
        InvoiceId = invoiceId;
        TargetStage = WorkflowStageType.Canceled;
        IgnoreResultType = TestResultType.Unarchived;
    }

    public TestResultType IgnoreResultType { get; set; }

    public WorkflowStageType TargetStage { get; set; }

    public long InvoiceId { get; }

    public void ScavengeAllOrphanedResultBundlesInInvoice()
    {
        var bundles = ResultBundlesRepository.GetActiveResultBundlesForInvoice(InvoiceId);
        foreach (var bundle in bundles.Where(b => b.TestResultType != (byte)IgnoreResultType)) processBundle(bundle);
    }

    private void processBundle(ResultBundlesForInvoice bundle)
    {
        var tests = OrderedTestsRepository.GetActiveTestsInResultBundle(bundle.Id);
        if (tests == null || !tests.Any())
            // bundle contains no active tests
            // this bundle is eligible for cancellation
            if (ResultBundlesRepository.VerifyResultBundleWorkflowStageMatch(
                    bundle.Id,
                    (WorkflowStageType)bundle.WorkflowStage))
                // ok to go...
                ResultBundlesRepository.UpdateResultBundleActiveStage(bundle.Id, false, TargetStage);
    }
}