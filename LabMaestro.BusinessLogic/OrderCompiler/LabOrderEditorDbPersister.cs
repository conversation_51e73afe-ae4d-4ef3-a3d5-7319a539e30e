﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: LabOrderEditorDbPersister.cs 1221 2014-03-18 05:24:41Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System.Collections.Generic;
using System.Linq;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class LabOrderEditorDbPersister : ILabOrderEditorPersister
{
    public LabOrderEditorDbPersister(LabOrderEditor editor)
    {
        TestsAdditionQueue = editor
            .OrderedTests
            .Where(test => test.PendingModification == PendingModificationType.Addition)
            .ToList();

        TestsCancelationQueue = editor
            .OrderedTests
            .Where(test => test.PendingModification == PendingModificationType.Cancellation)
            .ToList();

        BillableItemsQueue = editor.OrderedBillableItems;
        InvoiceId = editor.InvoiceId;
        InvoiceSummary = (InvoiceSummary)editor.WorkingInvoiceSummary.Clone();
    }

    public List<OrderedTestWithWorkflowSlice> TestsAdditionQueue { get; }

    public List<OrderedTestWithWorkflowSlice> TestsCancelationQueue { get; }

    public InvoiceSummary InvoiceSummary { get; }

    public List<BillableItemSlice> BillableItemsQueue { get; }

    public long InvoiceId { get; }

    public void Execute()
    {
        FaultHandler.Shield(() =>
        {
            // Ordered tests
            processTestsCancelationQueue();
            processTestsAdditionQueue();

            // Result bundles
            createResultBundles();
            scavengeOrphanedResultBundles();

            // Billable items
            removeBillableIems();
            addBillableItems();

            updateInvoiceDetails();
            updateOrderWorkflowStage();
        });
    }

    private void updateOrderWorkflowStage()
    {
        /*
        var estimator = new LabOrderWorkflowStageEstimator(InvoiceId);
        estimator.EstimateWorkflowStage();
        */
        //AppSysRepository.SendLabOrderWorkflowStageEstimateRequest(InvoiceId);
        AsyncEsbMessenger.SendMessagesAsync(EsbMessageChain.CreateNew().AddWorkflowEstimator(InvoiceId));
    }

    private void createResultBundles()
    {
        var provider = new EditedInvoiceOrderedTestsDataProvider(InvoiceId, TestsAdditionQueue);
        var compiler = new ResultBundleCompiler(InvoiceId, provider);
        compiler.CompileBundles();
        ResultBundleDbPersister.InsertNewResultBundles(compiler);
    }

    private void scavengeOrphanedResultBundles()
    {
        var scavenger = new OrphanedResultBundlesScavenger(InvoiceId);
        scavenger.ScavengeAllOrphanedResultBundlesInInvoice();

        //PatientLabOrdersRepository.ScavengeAllOrphanedResultBundlesInInvoice(
        //    InvoiceId,
        //    WorkflowStageType.Canceled,
        //    TestResultType.Unarchived);
    }

    private void updateInvoiceDetails()
    {
        var invoice = InvoiceMasterRepository.FindInvoiceById(InvoiceId);
        if (invoice != null)
        {
            invoice.GrossPayable = InvoiceSummary.GrossPayable;
            invoice.NetPayable = InvoiceSummary.NetPayable;
            invoice.DiscountAmount = InvoiceSummary.DiscountAmount;
            invoice.PaidAmount = InvoiceSummary.PaidAmount;
            invoice.DueAmount = InvoiceSummary.DueAmount;
            invoice.RefundAmount = InvoiceSummary.RefundAmount;
            invoice.TaxAmount = InvoiceSummary.TaxAmount;
            invoice.SurchargeAmount = InvoiceSummary.SurchargeAmount;
            invoice.AdjustPaymentStatus();

            InvoiceMasterRepository.Save();
        }
    }

    private void addBillableItems()
    {
        foreach (var slice in BillableItemsQueue)
            OrderedBillableItemsRepository.CreateNewOrderedBillableItem(InvoiceId,
                slice.Id,
                slice.UnitPrice,
                slice.Quantity);
    }

    private void removeBillableIems()
    {
        OrderedBillableItemsRepository.RemoveAllBillableItemsInInvoice(InvoiceId);
    }

    private void processTestsAdditionQueue()
    {
        foreach (var slice in TestsAdditionQueue)
        {
            var newId = OrderedTestsRepository.CreateNewOrderedTest(InvoiceId,
                slice.TestId,
                slice.ListPrice,
                slice.PerformingLabPostOrderEntryWorkflowStage,
                slice.OrderedTestETA);

            AuditTrailRepository.LogGenericEvent(AuditEventCategory.OrderEntry,
                AuditEventType.ordNewTestOrdered,
                WorkflowStageType.Unknown,
                CurrentUserContext.UserId,
                CurrentUserContext.WorkShiftId,
                InvoiceId,
                newId,
                -1,
                slice.PendingOperationReason);

            // update OrderedTestId because it will be used to create result bundles
            slice.OrderedTestId = newId;
        }
    }

    private void processTestsCancelationQueue()
    {
        foreach (var slice in TestsCancelationQueue) cancelLabTest(slice);
    }

    private int getNumberOfActiveTestsInBundle(long bundleId)
    {
        if (bundleId > 0)
        {
            var list = OrderedTestsRepository.GetActiveTestsInResultBundle(bundleId);
            if (list != null) return list.Count;
            return 0;
        }

        return -1;
    }

    private void cancelLabTest(OrderedTestWithWorkflowSlice slice)
    {
        OrderedTestsRepository.CancelOrderedTest(InvoiceId,
            slice.OrderedTestId,
            WorkflowStageType.Canceled);

        // TODO: BUG #124 - update the result bundle status
        // if this is the only test in the bundle:
        //  1) bundle status = canceled
        //  2) bundle.isactive = false


        long bundleId = -1;
        if (slice.ResultBundleId != null)
        {
            bundleId = (long)slice.ResultBundleId;
        }
        else
        {
            var detail = OrderedTestsRepository.FindById(slice.OrderedTestId);
            if (detail != null && detail.ResultBundleId != null) bundleId = (long)detail.ResultBundleId;
        }

        if (bundleId > 0 && getNumberOfActiveTestsInBundle(bundleId) == 0)
        {
            ResultBundlesRepository.UpdateResultBundleActiveStage(bundleId,
                false,
                WorkflowStageType.Canceled);
            AuditTrailRepository.LogGenericEvent(AuditEventCategory.Workflow,
                AuditEventType.wfResultBundleCanceled,
                WorkflowStageType.Canceled,
                CurrentUserContext.UserId,
                CurrentUserContext.WorkShiftId,
                InvoiceId,
                slice.OrderedTestId,
                bundleId,
                slice.PendingOperationReason);
        }

        AuditTrailRepository.LogGenericEvent(AuditEventCategory.OrderEntry,
            AuditEventType.ordTestCanceled,
            WorkflowStageType.Unknown,
            CurrentUserContext.UserId,
            CurrentUserContext.WorkShiftId,
            InvoiceId,
            slice.OrderedTestId,
            slice.ResultBundleId,
            slice.PendingOperationReason);
    }
}