// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: InvoiceCalculator.cs 1462 2014-10-12 14:11:27Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using CuttingEdge.Conditions;
using LabMaestro.Domain;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class InvoiceCalculator
{
    public static InvoiceCalculator AssembleFrom(InvoiceInfo info) =>
        new()
        {
            GrossPayable = info.GrossPayable,
            NetPayable = info.NetPayable,
            DueAmount = info.NetPayable,
            TaxAmount = info.TaxAmount,
            SurchargeAmount = info.SurchargeAmount,
            //DiscountAmount = info.DiscountAmount;
            //PaidAmount = info.PaidAmount;
            //DueAmount = info.DueAmount;
        };

    public InvoiceCalculator()
    {
        Reset();
        RoundBy = 10;
    }

    public int RoundBy { get; set; }

    public decimal GrossPayable { get; private set; }

    public decimal NetPayable { get; private set; }

    public decimal PaidAmount { get; private set; }

    public decimal DueAmount { get; private set; }

    public decimal DiscountAmount { get; set; }

    public decimal DiscountRebateAmount { get; private set; }

    public decimal TaxAmount { get; set; }

    public decimal MaxApplicableDiscountAmount { get; private set; }

    public float MaxApplicableDiscountPercent { get; private set; }
    public decimal SurchargeAmount { get; set; }
    public decimal SurchargeMaxAmount { get; set; } = 50;
    public decimal SurchargeMinAmount { get; set; } = 20;
    public decimal SurchargePercent { get; set; } = 0.006m;

    public void Reset()
    {
        GrossPayable = 0m;
        NetPayable = 0m;
        DiscountAmount = 0m;
        DiscountRebateAmount = 0m;
        TaxAmount = 0m;
        SurchargeAmount = 0m;
        DueAmount = 0m;
        PaidAmount = 0m;
        MaxApplicableDiscountAmount = 0m;
        MaxApplicableDiscountPercent = 0;
    }

    public decimal CalculateDiscountPercent(float percent, bool enforceDiscount)
    {
        var pctFrac = decimal.Divide((decimal)percent, 100m);
        var result = decimal.Multiply(GrossPayable, pctFrac);
        result = Math.Round(result / RoundBy, MidpointRounding.ToEven) * RoundBy;

        if (enforceDiscount)
            return result;

        return MaxApplicableDiscountAmount > 0 && result > MaxApplicableDiscountAmount
            ? MaxApplicableDiscountAmount
            : result;
    }

    public void SetDiscountAmount(decimal amount)
    {
        DiscountAmount = amount;
        NetPayable = GrossPayable - DiscountAmount;
        DueAmount = NetPayable - PaidAmount;
        sanitizeAmounts();
    }

    public void SetPaidAmount(decimal amount)
    {
        PaidAmount = amount;
        DueAmount = NetPayable - PaidAmount;
        sanitizeAmounts();
    }

    public void AddPayment(decimal amount)
    {
        Condition.Requires(amount).IsGreaterOrEqual(0m);

        PaidAmount += amount;
        DueAmount -= amount;

        // Post conditions
        Condition.Ensures(PaidAmount, "PaidAmount").IsLessOrEqual(NetPayable);
        Condition.Ensures(DueAmount, "DueAmount").IsGreaterOrEqual(0m);
    }

    public void AddRefund(decimal amount)
    {
        Condition.Requires(amount).IsGreaterThan(0m);

        PaidAmount -= amount;
        DueAmount += amount;

        // Post conditions
        Condition.Ensures(PaidAmount, "PaidAmount").IsGreaterOrEqual(0m);
        Condition.Ensures(DueAmount, "DueAmount").IsLessOrEqual(NetPayable);
    }

    public void AddCashDiscount(decimal amount)
    {
        Condition.Requires(amount).IsGreaterThan(0m);

        DiscountAmount += amount;
        NetPayable = GrossPayable - DiscountAmount;
        DueAmount = NetPayable - PaidAmount;
        sanitizeAmounts();

        // Post conditions
        Condition.Ensures(DiscountAmount, "DiscountAmount").IsLessOrEqual(GrossPayable);
        Condition.Ensures(DueAmount, "DueAmount").IsLessOrEqual(NetPayable);
    }

    private void sanitizeAmounts()
    {
        //if (DueAmount < 0) DueAmount = 0;
    }

    public void AddDiscountRebate(decimal amount)
    {
        Condition.Requires(amount).IsGreaterThan(0m);
        Condition.Requires(amount).IsLessOrEqual(DiscountAmount);

        DiscountRebateAmount += amount;
        NetPayable = GrossPayable - DiscountAmount + DiscountRebateAmount;
        DueAmount = NetPayable - PaidAmount;
        sanitizeAmounts();

        // Post conditions
        Condition.Ensures(DiscountRebateAmount, "DiscountRebateAmount").IsLessOrEqual(DiscountAmount);
        Condition.Ensures(DueAmount, "DueAmount").IsLessOrEqual(NetPayable);
    }

    public PaymentStatusType DeducePaymentStatus()
    {
        if (DueAmount == 0 && PaidAmount == NetPayable) return PaymentStatusType.PaidInFull;
        if (PaidAmount > NetPayable) return PaymentStatusType.OverPaid;
        if (DueAmount == NetPayable) return PaymentStatusType.UnPaid;
        if (DueAmount > 0 && PaidAmount > 0) return PaymentStatusType.PartiallyPaid;
        return PaymentStatusType.Unknown;
    }

    public void CalculateInvoice(LabOrderContextInfo ctx) =>
        CalculateInvoice(ctx.OrderedTests, ctx.OrderedBillableItems);

    public void CalculateInvoice(List<OrderableTestSlice> orderedTests, List<BillableItemSlice> orderedItems)
    {
        GrossPayable = orderedTests.Sum(x => x.ListPrice);
        MaxApplicableDiscountAmount = orderedTests.Sum(x => x.MaxApplicableDiscount);
        if (MaxApplicableDiscountAmount == 0)
            MaxApplicableDiscountPercent = 0;
        else
            MaxApplicableDiscountPercent = (float)Math.Round(MaxApplicableDiscountAmount * 100 / GrossPayable, 2);

        GrossPayable += orderedItems.Sum(x => x.Quantity * x.UnitPrice);
        if (GrossPayable < DiscountAmount) DiscountAmount = 0m;
        GrossPayable += TaxAmount;
        NetPayable = GrossPayable - DiscountAmount + DiscountRebateAmount;
        DueAmount = NetPayable - PaidAmount;
        var roundUp = 5;
        MaxApplicableDiscountAmount = RoundOff(MaxApplicableDiscountAmount, roundUp);
        MaxApplicableDiscountAmount =
            Math.Round(MaxApplicableDiscountAmount / roundUp, MidpointRounding.AwayFromZero) * roundUp;
        sanitizeAmounts();
    }

    public static InvoiceInfo AssembleTo(InvoiceCalculator calc)
    {
        Mapper.CreateMap<InvoiceCalculator, InvoiceInfo>();
        var info = Mapper.Map<InvoiceCalculator, InvoiceInfo>(calc);
        return info;
    }

    public void CalculateInvoice(List<OrderedTestWithWorkflowSlice> orderedTests, List<BillableItemSlice> billItems)
    {
        GrossPayable = orderedTests.Sum(x => x.ListPrice);
        MaxApplicableDiscountAmount = orderedTests.Sum(x => x.MaxApplicableDiscount);
        if (MaxApplicableDiscountAmount == 0)
            MaxApplicableDiscountPercent = 0;
        else
            MaxApplicableDiscountPercent = (float)Math.Round(MaxApplicableDiscountAmount * 100 / GrossPayable, 2);

        GrossPayable += billItems.Sum(bi => bi.Quantity * bi.UnitPrice);
        GrossPayable += TaxAmount;
        NetPayable = GrossPayable - DiscountAmount + DiscountRebateAmount;
        DueAmount = NetPayable - PaidAmount;
        MaxApplicableDiscountAmount = Math.Round(MaxApplicableDiscountAmount / 5, MidpointRounding.AwayFromZero) * 5;
    }

    public static int RoundOff(decimal i, decimal roundBy) => (int)((int)Math.Round(i / roundBy) * roundBy);

    public void SetSurchargePercent(int paisa) => SurchargePercent = decimal.Divide(paisa, 10_000);

    public void CalculateSurcharge(bool applySurcharge)
    {
        if (!applySurcharge)
        {
            SurchargeAmount = 0m;
            return;
        }

        var effectiveAmount = NetPayable;
        // account for 50% of discount
        if (DiscountAmount > 0) effectiveAmount += (int)(DiscountAmount / 2);
        var amount = decimal.Multiply(effectiveAmount, SurchargePercent);
        //amount = Math.Round(amount / RoundBy, MidpointRounding.AwayFromZero) * RoundBy;
        amount = RoundOff(amount, RoundBy);
        amount = Math.Max(amount, SurchargeMinAmount);
        SurchargeAmount = Math.Min(amount, SurchargeMaxAmount);
    }

    public void CalculateSurchargeCustom(decimal netPayable, decimal discount)
    {
        var effectiveAmount = netPayable;
        // account for 50% of discount
        if (discount > 0) effectiveAmount += (int)(discount / 2);
        var amount = decimal.Multiply(effectiveAmount, SurchargePercent);
        //amount = Math.Round(amount / RoundBy, MidpointRounding.AwayFromZero) * RoundBy;
        amount = RoundOff(amount, RoundBy);
        amount = Math.Max(amount, SurchargeMinAmount);
        SurchargeAmount = Math.Min(amount, SurchargeMaxAmount);
    }
}