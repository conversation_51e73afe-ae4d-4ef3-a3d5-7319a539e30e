﻿//  * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
//  
//  Copyright (c) 2012, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
//  All rights reserved.
//  
//  Redistribution and use in source and binary forms, with or without
//  modification, are permitted provided that the following conditions are met:
//     * Redistributions of source code must retain the above copyright
//       notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above copyright
//       notice, this list of conditions and the following disclaimer in the
//       documentation and/or other materials provided with the distribution.
//     * Neither the name of the <organization> nor the
//       names of its contributors may be used to endorse or promote products
//       derived from this software without specific prior written permission.
//  
//  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
//  ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
//  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
//  DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
//  DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
//  (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
//  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
//  ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
//  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
//  SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
//  
//  $Id: BillableItemsCompiler.cs 723 2013-07-03 06:56:29Z <EMAIL> $
//  
//  * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System.Collections.Generic;
using System.Linq;
using LabMaestro.Domain;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

internal sealed class BillableItemsCompiler
{
    private const string OPT_NONE = @"$$_NO_OPTIMIZATION_$$";
    private const string OPT_GLOB = @"$$_GLOBAL_OPTIMIZATION_$$";
    private const string OPT_EXCL = @"$$_EXCLUSIVE_OPTIMIZATION_$$";
    private const string OPT_STATIC = @"$$_STATIC_NO_OPTIMIZATION_$$";

    public readonly List<OrderableTestSlice> _orderedTests = [];
    public readonly Dictionary<string, BillableItemsTriageGroup> _triageGroups = new();

    internal BillableItemsCompiler() => Reset();

    internal void Reset()
    {
        _triageGroups.Clear();
        _orderedTests.Clear();

        // Pre-create special groups
        //findOrAddTriageGroup(OPT_NONE); // No optimization
        //findOrAddTriageGroup(OPT_GLOB); // Globally shared optimization
        //findOrAddTriageGroup(OPT_EXCL); // -- not used currently
    }

    private static string sanitizeGroupName(string name) => name.Trim().ToUpperInvariant();

    private BillableItemsTriageGroup findOrAddTriageGroup(string groupName)
    {
        groupName = sanitizeGroupName(groupName);
        if (_triageGroups.ContainsKey(groupName))
            return _triageGroups[groupName];

        var grp = new BillableItemsTriageGroup(groupName);
        _triageGroups.Add(groupName, grp);
        return grp;
    }

    internal void AddExclusiveItem(BillableItemSlice item)
    {
        item.OptimizationLevel = BillableItemOptimizationLevelType.DoNotOptimize;
        var triagedGroup = findOrAddTriageGroup(OPT_EXCL);
        triagedGroup.AddItem(item);
    }

    internal void AddStaticItem(BillableItemSlice item)
    {
        item.OptimizationLevel = BillableItemOptimizationLevelType.DoNotOptimize;
        findOrAddTriageGroup(OPT_STATIC).AddItem(item);
    }

    internal void RemoveExclusiveItem(BillableItemSlice item)
    {
        item.OptimizationLevel = BillableItemOptimizationLevelType.DoNotOptimize;

        var triagedGroup = findOrAddTriageGroup(OPT_EXCL);
        triagedGroup.DecreaseItemQuantity(item);
    }

    internal void AddTest(OrderableTestSlice test)
    {
        _orderedTests.Add(test);

        foreach (var item in test.BillableItems)
        {
            var triageGroup = findTriageGroup(item.OptimizationLevel, test.PerformingLabName);
            triageGroup.AddItem(item);
        }
    }

    internal void Optimize()
    {
        foreach (var triageGroup in _triageGroups.Values) triageGroup.Optimize();
    }

    internal List<BillableItemSlice> Compile()
    {
        var list = new List<BillableItemSlice>();
        foreach (var triageGroup in _triageGroups.Values)
        foreach (var item in triageGroup.OptimizedItems)
        {
            var foundItem = list.FirstOrDefault(x => x.Id == item.Id);
            if (foundItem == null)
                list.Add(item);
            else
                /*
                    if (string.Compare(triageGroup.Name, OPT_NONE) != 0)
                    {
                        if (item.Quantity > foundItem.Quantity)
                        {
                            foundItem.Quantity = item.Quantity;
                        }
                    }
                    else
                    */
                foundItem.Quantity += item.Quantity;
        }

        return list;
    }

    private BillableItemsTriageGroup findTriageGroup(BillableItemOptimizationLevelType level, string lab) =>
        level switch
        {
            BillableItemOptimizationLevelType.DoNotOptimize => findOrAddTriageGroup(OPT_NONE),
            BillableItemOptimizationLevelType.OptimizePerReq => findOrAddTriageGroup(lab),
            BillableItemOptimizationLevelType.OptimizeGlobally => findOrAddTriageGroup(OPT_GLOB),
            _ => null
        };

    internal void RemoveTest(OrderableTestSlice test)
    {
        _orderedTests.Remove(test);

        foreach (var item in test.BillableItems)
            findTriageGroup(item.OptimizationLevel, test.PerformingLabName).RemoveItem(item);
    }
}