﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: LabOrderCompiler.cs 781 2013-07-09 13:58:24Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using CuttingEdge.Conditions;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class LabOrderCompiler
{
    public static PatientLabOrder BuildLabOrderChain(InvoiceCalculator calculator, LabOrderContextInfo context)
    {
        Condition.Requires(calculator).IsNotNull();
        Condition.Requires(context).IsNotNull();

        var labOrder = new PatientLabOrder
        {
            Age = context.Age,
            DoB = context.DoB,
            Title = context.Title,
            FirstName = context.FirstName,
            LastName = context.LastName,
            OrderNotes = context.OrderNotes,
            PhoneNumber = context.PhoneNumber,
            EmailAddress = context.EmailAddress,
            IsReferrerUnknown = context.IsReferrerUnknown,
            ReferrerCustomName = context.ReferrerCustomName,
            ReferrerId = context.ReferrerId,
            Sex = (byte)context.Sex,
            IsCancelled = context.IsCancelled,
            WorkflowStage = (byte)context.WorkflowStage,

            CustomerId = context.CustomerId,
            AffiliateId = context.AffiliateId,
            CorporateClientId = context.CorporateClientId,
            AssociateLabId = context.AssociateLabId,
            AssociateLabAccessionId = context.AssociateLabAccessionId,
            //RequestingLabId = context.RequestingLabId,
            //SubOrderTrackingId = context.SubOrderTrackingId,
            IsExternalSubOrder = context.IsExternalSubOrder,
            HealthPackageId = context.HealthPackageId,
            IsHidden = context.IsHidden,
            IsNonFungibleOrder = context.IsNonFungibleOrder,
        };

        foreach (var slice in context.OrderedTests)
        {
            var labTest = FaultHandler.Shield(() => LabTestsRepository.FindById(slice.TestId));
            var wfStageNext = labTest.ReportingLab is { PostOrderEntryWorkflowStage: not null }
                ? (WorkflowStageType)labTest.ReportingLab.PostOrderEntryWorkflowStage
                : WorkflowStageType.OrderEntry;

            var orderedTest = new OrderedTest
            {
                PatientLabOrder = labOrder,
                LabTest = labTest,
                IsCancelled = false,
                UnitPrice = labTest.ListPrice,
                WorkflowStage = (byte)wfStageNext,
                ResultsETA = labTest.GetTestResultsETA()
            };

            labOrder.OrderedTests.Add(orderedTest);
        }

        foreach (var biSlice in context.OrderedBillableItems)
        {
            var item = FaultHandler.Shield(() => BillableItemsRepository.FindActiveItemById(biSlice.Id));
            var orderedItem = new OrderedBillableItem
            {
                BillableItem = item,
                PatientLabOrder = labOrder,
                Quantity = biSlice.Quantity,
                IsCancelled = false,
                UnitPrice = biSlice.UnitPrice
            };

            labOrder.OrderedBillableItems.Add(orderedItem);
        }

        FaultHandler.Shield(() => calculator.CalculateInvoice(context));
        var invoice = new InvoiceMaster(labOrder)
        {
            GrossPayable = calculator.GrossPayable,
            NetPayable = calculator.NetPayable,
            DiscountAmount = calculator.DiscountAmount,
            TaxAmount = calculator.TaxAmount,
            PaidAmount = 0m,
            PaymentStatus = (byte)PaymentStatusType.UnPaid
        };
        labOrder.InvoiceMaster = invoice;

        return labOrder;

        /* TODO:
         * lab order
         * ordered tests
         * billable items
         * ?invoice master?
         */
    }
}