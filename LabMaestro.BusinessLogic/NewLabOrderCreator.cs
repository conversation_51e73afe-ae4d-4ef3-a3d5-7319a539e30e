﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: NewLabOrderCreator.cs 1532 2014-11-29 08:57:12Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

#define FAST_ORDER_CREATION

using System;
using System.Collections.Generic;
using System.Threading;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class NewLabOrderCreator(LabOrderContextInfo orderContext, InvoiceInfo invoiceInfo)
{
    private readonly InvoiceCalculator _invoiceCalc = InvoiceCalculator.AssembleFrom(invoiceInfo);
    private readonly SortedList<int, TransactionInfoSlice> _transactions = new();
    private int _currIndex;

    public bool RecordTransaction(
        InvoiceTransactionType txType,
        TransactionFlag txFlag,
        decimal txAmount,
        string txRemarks,
        decimal nonCashAmount,
        PaymentMethod method,
        string? pmtSrc = null,
        string? pmtRef = null
    )
    {
        _transactions.Add(_currIndex++,
                          new TransactionInfoSlice
                          {
                              TxType = txType,
                              TxAmount = txAmount,
                              TxFlag = txFlag,
                              UserRemarks = txRemarks,
                              PerformingUserId = CurrentUserContext.UserId,
                              WorkShiftId = CurrentUserContext.WorkShiftId,
                              WorkflowStage = WorkflowStageType.OrderEntry,
                              NonCashAmount = nonCashAmount,
                              PaymentMethod = method,
                              PaymentSource = pmtSrc,
                              PaymentReference = pmtRef
                          });

        switch (txType) {
            case InvoiceTransactionType.Payment:
                _invoiceCalc.AddPayment(txAmount + nonCashAmount);
                break;
            case InvoiceTransactionType.Refund:
                _invoiceCalc.AddRefund(txAmount);
                break;
            case InvoiceTransactionType.CashDiscount:
                _invoiceCalc.AddCashDiscount(txAmount);
                break;
        }

        return true;
    }

    public long CreateOrder()
    {
#if FAST_ORDER_CREATION
        var dto = orderContext.ToNewLabOrderContextDto();
        dto.OrderingUserId = CurrentUserContext.UserId;
        dto.WorkShiftId = CurrentUserContext.WorkShiftId;
        dto.WebAccessToken = SharedUtilities.RandomStringSafe(4);

        // blank for localhost
        var ipaddr = SharedUtilities.GetLocalIpAddress(GlobalSettingsHelper.System.IPSubnetPrefix);
        dto.UserIpAddress = ipaddr == -1 ? null : ipaddr;

        /*
        dto.MirrorFlag = (byte)MirrorFlag.None;
        dto.IsExternalSubOrder = _orderContext.OrderDemoInfo.IsExternalSubOrder;
        dto.SubOrderTrackingId = _orderContext.OrderDemoInfo.SubOrderTrackingId;
        dto.RequestingLabId = null;
        if (dto.IsExternalSubOrder && _orderContext.OrderDemoInfo.RequestingLabId > 0)
        {
            dto.RequestingLabId = _orderContext.OrderDemoInfo.RequestingLabId;
            dto.EmailTestResults = false;
        }
        */

        dto.GrossPayable = _invoiceCalc.GrossPayable;
        dto.NetPayable = _invoiceCalc.NetPayable;
        dto.DiscountAmount = _invoiceCalc.DiscountAmount;
        dto.TaxAmount = _invoiceCalc.TaxAmount;
        dto.SurchargeAmount = _invoiceCalc.SurchargeAmount;
        dto.PaymentStatus = (byte)_invoiceCalc.DeducePaymentStatus();
        dto.PaidAmount = 0; //_invoiceCalc.PaidAmount;
        dto.DueAmount = _invoiceCalc.NetPayable; //_invoiceCalc.DueAmount;

        //TODO: requesting lab, surcharge

        foreach (var tx in _transactions.Values)
            dto.Transactions.Add(new NewTransactionDto
            {
                TxAmount = tx.TxAmount,
                TxType = (byte)tx.TxType,
                AuthorizingUserId = null,
                TxFlag = (byte)tx.TxFlag,
                UserRemarks = tx.UserRemarks,
                NonCashAmount = tx.NonCashAmount,
                PaymentMethod = (byte)tx.PaymentMethod,
                PaymentSource = tx.PaymentSource,
                PaymentReference = tx.PaymentReference
            });

        var invoiceId = FaultHandler.Shield(() => PatientLabOrdersRepository.CreateNewLabOrder(dto));
#else
    // TODO: refactor >> smaller chunks
            var labOrder = FaultHandler.Shield(() =>
                                               PatientLabOrdersRepository.CreateNew(_orderContext.FirstName,
                                                                                    _orderContext.Sex,
                                                                                    _orderContext.IsReferrerUnknown));
            FaultHandler.Shield(() => AuditTrailRepository.LogLabOrderEvent(labOrder.InvoiceId,
                                                                            AuditEventType.ordNewLabOrder,
                                                                            WorkflowStageType.OrderEntry));

            labOrder.Title = _orderContext.Title;
            labOrder.LastName = _orderContext.LastName;
            labOrder.Sex = (byte) _orderContext.Sex;

            if (_orderContext.Age != null)
            {
                labOrder.Age = _orderContext.Age;
            }
            else if (_orderContext.DoB != null)
            {
                labOrder.DoB = _orderContext.DoB;
            }

            labOrder.DisallowReferral = _orderContext.DisallowReferral;
            labOrder.PhoneNumber = _orderContext.PhoneNumber;
            labOrder.EmailAddress = _orderContext.EmailAddress;
            labOrder.EmailTestResults = _orderContext.EmailTestResults;
            labOrder.OrderNotes = _orderContext.OrderNotes;

            labOrder.OrderingUserId = CurrentUserContext.UserId;
            labOrder.WorkShiftId = CurrentUserContext.WorkShiftId;
            labOrder.WebAccessToken = SharedUtilities.RandomStringSafe(4);

            if (_orderContext.ReferrerId != null)
            {
                labOrder.ReferrerId = _orderContext.ReferrerId;
            }
            labOrder.IsReferrerUnknown = _orderContext.IsReferrerUnknown;
            labOrder.ReferrerCustomName = _orderContext.ReferrerCustomName;
            labOrder.MirrorFlag = (byte) MirrorFlag.None;

            labOrder.IsExternalSubOrder = _orderContext.LabOrderDemographicInfoSlice.IsExternalSubOrder;
            labOrder.SubOrderTrackingId = _orderContext.LabOrderDemographicInfoSlice.SubOrderTrackingId;

            labOrder.InvoiceMaster.GrossPayable = _invoiceCalc.GrossPayable;
            labOrder.InvoiceMaster.NetPayable = _invoiceCalc.NetPayable;
            labOrder.InvoiceMaster.DiscountAmount = _invoiceCalc.DiscountAmount;
            labOrder.InvoiceMaster.TaxAmount = _invoiceCalc.TaxAmount;
            labOrder.InvoiceMaster.PaymentStatus = (byte) _invoiceCalc.DeducePaymentStatus();
            labOrder.InvoiceMaster.PaidAmount = 0; //_invoiceCalc.PaidAmount;
            labOrder.InvoiceMaster.DueAmount = _invoiceCalc.NetPayable; //_invoiceCalc.DueAmount;
            WorkShift shift = null;
            FaultHandler.Shield(() =>
                {
                    // Check-point #1 
                    PatientLabOrdersRepository.Save();

                    // Record all the transactions...
                    shift = WorkShiftsRepository.FindUserShiftById(CurrentUserContext.UserId,
                                                                   CurrentUserContext.WorkShiftId);
                });

            foreach (var slice in _transactions.Values)
            {
                switch (slice.TxType)
                {
                    case InvoiceTransactionType.Payment:
                        labOrder.InvoiceMaster.IssuePayment(shift, slice.TxAmount, slice.TxFlag, slice.UserRemarks,
                                                            slice.WorkflowStage);
                        break;
                    case InvoiceTransactionType.Refund:
                        //TODO: investigate [authorizingUserId]
                        labOrder.InvoiceMaster.IssueRefund(shift, slice.TxAmount, slice.TxFlag, slice.UserRemarks,
                                                           CurrentUserContext.UserId, slice.WorkflowStage);
                        break;
                    case InvoiceTransactionType.CashDiscount:
                        labOrder.InvoiceMaster.IssueDiscount(shift, slice.TxAmount, slice.TxFlag, slice.UserRemarks,
                                                             slice.WorkflowStage);
                        break;
                }
            }

            // Check-point #2
            FaultHandler.Shield(PatientLabOrdersRepository.Save);

            // now add all the ordered lab tests
            foreach (var slice in _orderContext.OrderedTests)
            {
                var test = labOrder.OrderLabTest(slice.TestId);
                test.ResultsETA = slice.DeliveryTime;
            }

            // and the billable items
            foreach (var slice in _orderContext.OrderedBillableItems)
            {
                labOrder.AdjustOrAddBillableItem(slice.Id, slice.Quantity, false);
            }

            labOrder.InvoiceMaster.ReconcileInvoiceFinances(true);

            // Shift balances are now being updated by InvoiceMaster.IssueXXX() method.
            // shift.ReconcileShiftFinances(); // update the users shift financial info

            // Final check-point
            FaultHandler.Shield(PatientLabOrdersRepository.Save);

            var invoiceId = labOrder.InvoiceId;
#endif
        if (invoiceId > 0) {
            var retryCount = 5;
            var queueSuccess = false;
            while (retryCount > 0) {
                var chain = EsbMessageChain.CreateNew()
                                           .AddResultBundler(invoiceId)
                                           .AddMirrorFlagger(invoiceId);
                if (AsyncEsbMessenger.SendMessagesAsync(chain)) {
                    queueSuccess = true;
                    break;
                }

                retryCount--;
                Thread.Sleep(100);
            }

            if (!queueSuccess) createResultBundlesForInvoice(invoiceId);
        }

        return invoiceId;
    }

    private void createResultBundlesForInvoice(long invoiceId)
    {
        FaultHandler.Shield(() =>
        {
            if (ResultBundlesRepository.GetActiveResultBundlesCountForInvoice(invoiceId) <= 0)
                try {
                    var comp = new ResultBundleCompiler(invoiceId,
                                                        new InvoiceOrderedTestsDataProvider());
                    comp.CompileBundles();
                    ResultBundleDbPersister.InsertNewResultBundles(comp);
                }
                catch (Exception e) {
                    AppLogger.Exception("Error creating result bundles", e);
                }
        });
    }
}