﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id$
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using LabMaestro.Infrastructure.Client.BackgroundJobs;

namespace LabMaestro.BusinessLogic;

public sealed class ServiceBusResponse
{
    public long MessagesSent { get; set; }
    public long MessagesReceived { get; set; }
    public long ErrorCount { get; set; }
    public DateTime RunningSince { get; set; }
    public Guid CorrelationId { get; set; }
    public DateTime ServerTime { get; set; }
}

public static class ServiceBusDiagnosticUtilities
{
    public static string SendHeartBeatRequest()
    {
        var task = ApiClient.For<IBackgroundJobs>().Ping();
        task.Wait();
        return task.Result;
        /*
        using (var esb = EsbClientFactory.GetClient())
        {
            var resp = esb.SendHeartBeatRequest();
            return new ServiceBusResponse
            {
                CorrelationId = resp.CorrelationId,
                ErrorCount = resp.ErrorCount,
                MessagesSent = resp.MessagesSent,
                MessagesReceived = resp.MessagesReceived,
                RunningSince = resp.RunningSince
            };
        }
        return new ServiceBusResponse();
        */
    }

    public static ServiceBusResponse SendDatabaseConnectivityCheckRequest()
    {
        /*
        using (var esb = EsbClientFactory.GetClient())
        {
            var resp = esb.SendDatabaseConnectivityCheckRequest();
            return new ServiceBusResponse {CorrelationId = resp.CorrelationId, ServerTime = resp.ServerTime};
        }
        */
        return new ServiceBusResponse();
    }
}