﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: DiscreteTestReportGroup.cs 1342 2014-05-30 05:03:06Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

internal sealed class DiscreteTestReportGroup
{
    private readonly List<DiscreteOrderedTestResults> _testsList;

    internal DiscreteTestReportGroup(string groupName)
    {
        GroupName = groupName;
        _testsList = new List<DiscreteOrderedTestResults>();
        AggregatePriority = 0;
    }

    internal string GroupName { get; }

    public int AggregatePriority { get; private set; }

    public bool IsNullGroup
    {
        get { return string.CompareOrdinal(GroupName, DiscreteGroupedTestReportCompiler.NULL_GROUP_TAG) == 0; }
    }

    internal void AddOrderedTest(long bundleId, long testId, string canonicalName, byte priority)
    {
        var test = new DiscreteOrderedTestResults
        {
            ResultBundleId = bundleId,
            CanonicalName = canonicalName,
            OrderedTestId = testId,
            ReportSortPriority = (SortPriorityType)priority
        };
        AggregatePriority += priority;
        test.PopulateLineItems();
        _testsList.Add(test);
    }

    internal List<DiscreteResultItemPrintDto> GenerateResultLineItems()
    {
        var list = new List<DiscreteResultItemPrintDto>();
        foreach (var testResults in _testsList.OrderByDescending(x => x.ReportSortPriority))
        {
            if (string.Compare(testResults.CanonicalName,
                    testResults.ResultLineItems[0].RawParameter,
                    StringComparison.InvariantCultureIgnoreCase) == 0)
            {
                // merge if header and first parameter matches
                var headerParam = testResults.ResultLineItems[0];
                headerParam.IsRootNode = true;
                headerParam.DecreaseIndent(1);
            }
            else
            {
                list.Add(testResults.ToPrintDto());
            }

            list.AddRange(testResults.ResultLineItems);
        }

        return list;
    }
}