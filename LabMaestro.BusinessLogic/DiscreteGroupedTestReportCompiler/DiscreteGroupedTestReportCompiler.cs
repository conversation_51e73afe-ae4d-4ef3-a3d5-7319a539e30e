﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: DiscreteGroupedTestReportCompiler.cs 1261 2014-05-19 10:36:29Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Printing;

namespace LabMaestro.BusinessLogic;

public sealed class DiscreteGroupedTestReportCompiler
{
    internal const string NULL_GROUP_TAG = "~";
    private readonly long _resultBundleId;
    private readonly Dictionary<string, DiscreteTestReportGroup> _testReportGroups;

    public DiscreteGroupedTestReportCompiler(long resultBundleId)
    {
        _resultBundleId = resultBundleId;
        _testReportGroups = new Dictionary<string, DiscreteTestReportGroup>();
    }

    private DiscreteTestReportGroup ensureSlot(string key)
    {
        key = key.Trim().ToUpperInvariant();
        if (string.IsNullOrEmpty(key))
            throw new ArgumentNullException("key");

        if (!_testReportGroups.ContainsKey(key))
        {
            var grp = new DiscreteTestReportGroup(key);
            _testReportGroups.Add(key, grp);
        }

        return _testReportGroups[key];
    }

    public void AddOrderedTestWithGroupTag(string groupTag, long testId, string canonicalName, byte priority)
    {
        var group = ensureSlot(groupTag);
        group.AddOrderedTest(_resultBundleId, testId, canonicalName, priority);
    }

    public void AddOrderedTestWithoutGroupTag(long testId, string canonicalName, byte priority)
    {
        var group = ensureSlot(NULL_GROUP_TAG);
        group.AddOrderedTest(_resultBundleId, testId, canonicalName, priority);
    }

    public void Compile()
    {
        var tests = FaultHandler.Shield(() =>
            OrderedTestsRepository.GetDiscreteOrderedTestsInResultBundle(_resultBundleId));
        foreach (var slice in tests)
            if (string.IsNullOrEmpty(slice.ReportLineGroupingTag))
                AddOrderedTestWithoutGroupTag(slice.Id,
                    slice.CanonicalName,
                    slice.ReportSortPriority);
            else
                AddOrderedTestWithGroupTag(slice.ReportLineGroupingTag,
                    slice.Id,
                    slice.CanonicalName,
                    slice.ReportSortPriority);
    }

    public List<DiscreteResultItemPrintDto> GenerateResultLineItems()
    {
        var list = new List<DiscreteResultItemPrintDto>();

        // segregate grouped and non-grouped discrete test results
        // get grouped report line items first
        var groups = _testReportGroups.Values
            .Where(g => !g.IsNullGroup)
            .OrderByDescending(x => x.AggregatePriority);
        foreach (var reportGroup in groups) list.AddRange(reportGroup.GenerateResultLineItems());

        // get non-grouped line items
        var nullGroups = _testReportGroups.Values
            .Where(g => g.IsNullGroup)
            .OrderByDescending(x => x.AggregatePriority);
        foreach (var reportGroup in nullGroups) list.AddRange(reportGroup.GenerateResultLineItems());

        return list;
    }
}