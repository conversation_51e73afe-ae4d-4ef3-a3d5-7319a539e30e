﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ReqSlipBundleCompiler.cs 1501 2014-11-16 07:28:52Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Printing;
using LabMaestro.Shared;

namespace LabMaestro.BusinessLogic;

public sealed class ReqSlipBundleCompiler(List<OrderedTestPrintDto> orderedTests)
{
    private readonly Dictionary<short, ReqSlipBundle> _reqSlipBundles = new();

    public List<ReqSlipBundle> ReqSlipBundles => _reqSlipBundles.Values.ToList();

    public int ReqSlipBundlesCount => _reqSlipBundles.Values.Count;

    /// <summary>
    ///     Builds a list of invoice and requisition foils for a given invoice.
    /// </summary>
    /// <param name="invoiceId"> The invoice id. </param>
    /// <param name="onlyReqFoils">
    ///     if set to <c>true</c> only requisition foils will be returned in the tuple, otherwise the first item in the list
    ///     will contain the invoice data followed by the requisition foil data
    /// </param>
    /// <returns>
    ///     returns a tuple - first item contains the invoice data, the second item contains a list of req foils (and
    ///     invoice data)
    /// </returns>
    public static Tuple<InvoicePrintDto, List<InvoiceRequisitionFoilsPackage>> BuildInvoiceReqBundlesChain(
        long invoiceId, bool onlyReqFoils)
    {
        InvoicePrintDto invdto = null;
        var list = new List<InvoiceRequisitionFoilsPackage>();
        FaultHandler.Shield(() =>
        {
            // BUG FIX #96: Clear the cache to purge stale data
            PatientLabOrdersRepository.Reset();

            var labOrder = PatientLabOrdersRepository.GetLabOrderFullDetails(invoiceId);
            invdto = InvoicePrintDto.AssembleFrom(labOrder);
            var comp = new ReqSlipBundleCompiler(invdto.TestsOrdered);
            comp.CompileBundles();

            if (!onlyReqFoils) list.Add(InvoiceRequisitionFoilsPackage.FromInvoice(invdto));
            list.AddRange(
                comp.ReqSlipBundles.Select(InvoiceRequisitionFoilsPackage.FromReqBundle));
        });
        return Tuple.Create(invdto, list);
    }

    public void CompileBundles()
    {
        Reset();
        foreach (var orderedTest in orderedTests)
        {
            if (orderedTest.ReqSlipPrintOption == ReqSlipGenerationOptionType.DoNotGenerate) continue;

            var bundle = ensureBundle(orderedTest);
            bundle?.OrderedTests.Add(orderedTest);
        }

        foreach (var bundle in _reqSlipBundles.Values)
            bundle.Sort();
    }

    public List<string> GetLabNames() => _reqSlipBundles.Values.Select(x => x.LabName).ToList();

    public List<short> GetLabIds() => _reqSlipBundles.Values.Select(x => x.LabUID).ToList();

    public ReqSlipBundle GetReqSlipBundleByLabName(string lab)
    {
        //TODO: this is a bug - multiple foils may have same lab name
        //return _reqSlipBundles.Values.SingleOrDefault(x => String.CompareOrdinal(x.LabName, lab) == 0);
        return _reqSlipBundles.Values.FirstOrDefault(x => string.CompareOrdinal(x.LabName, lab) == 0);
    }

    public ReqSlipBundle GetReqSlipBundleByLabId(short labId) =>
        _reqSlipBundles.Values.SingleOrDefault(x => x.LabUID == labId);

    public void Reset() => _reqSlipBundles?.Clear();

    private ReqSlipBundle ensureBundle(OrderedTestPrintDto dto)
    {
        if (dto.IsCancelled) return null;

        ReqSlipBundle newBundle;
        if (dto.RequiresExclusiveBundle())
        {
            newBundle = new ReqSlipBundle { LabUID = generateUniqueKey(), LabName = dto.LabName };
            _reqSlipBundles.Add(newBundle.LabUID, newBundle);
            return newBundle;
        }

        if (_reqSlipBundles.ContainsKey(dto.LabId)) return _reqSlipBundles[dto.LabId];

        newBundle = new ReqSlipBundle { LabUID = dto.LabId, LabName = dto.LabName };
        _reqSlipBundles.Add(newBundle.LabUID, newBundle);
        return newBundle;
    }

    private short generateUniqueKey()
    {
        var keys = orderedTests.Select(x => x.LabId).Distinct().ToList();
        keys.AddRange(_reqSlipBundles.Keys.ToList());

        var rand = new Random();
        while (true)
        {
            var rKey = (short)rand.Next(5000, 10000);
            if (keys.All(x => x != rKey)) return rKey;
        }
    }
}