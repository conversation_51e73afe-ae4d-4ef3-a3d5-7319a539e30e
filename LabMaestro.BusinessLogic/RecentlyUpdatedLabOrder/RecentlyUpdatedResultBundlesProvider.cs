﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: RecentlyUpdatedResultBundlesProvider.cs 782 2013-07-09 14:09:34Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
#if COLLATION_RECENT_BUNDLES
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using LabMaestro.Domain;
using LabMaestro.Infrastructure.Client;
using LabMaestro.Shared;
using Quartz;
using Quartz.Impl;
using BundlesList = System.Collections.Generic.List<LabMaestro.Domain.RecentlyUpdatedResultBundleDetails>;

namespace LabMaestro.BusinessLogic;

public sealed class RecentlyUpdatedResultBundlesProvider : IObservable<BundlesList>
{
    private readonly int _interval;
    private readonly List<IObserver<BundlesList>> _observers;
    private readonly string _password;
    private readonly IScheduler _scheduler;
    private readonly string _username;
    private bool _shutdown;

    public RecentlyUpdatedResultBundlesProvider(int interval,
        string username,
        string password,
        WorkflowStageType minWorkflow,
        WorkflowStageType maxWorkflow)
    {
        _username = username;
        _password = password;
        var credential = LoginCredentialFactory.BuildCredential(_username, _password);
        ResultBundlesDataProvider = new RecentlyUpdatedResultBundlesProviderEx(credential);

        _observers = new List<IObserver<BundlesList>>();
        _interval = interval;
        MinWorkflowStage = minWorkflow;
        MaxWorkflowStage = maxWorkflow;

        var properties = new NameValueCollection
        {
            ["quartz.jobStore.type"] = "Quartz.Simpl.RAMJobStore, Quartz",
            ["quartz.threadPool.type"] = "Quartz.Simpl.SimpleThreadPool, Quartz",
            ["quartz.threadPool.threadCount"] = "1"
        };

        // First we must get a reference to a scheduler
        ISchedulerFactory sf = new StdSchedulerFactory(properties);
        _scheduler = sf.GetScheduler();

        // computer a time that is on the next round minute
        var runTime = DateTimeOffset.UtcNow;

        var dataMap = new JobDataMap
        {
            [@"provider"] = this
        };

        // define the job and tie it to our HelloJob class
        var job = JobBuilder.Create<RecentlyUpdatedResultBundlesFetcherJob>()
            .WithIdentity("recentBundlesJob", "recentBundles")
            .UsingJobData(dataMap)
            .Build();

        // Trigger the job to run on the next round minute
        var trigger = TriggerBuilder.Create()
            .WithIdentity("recentBundlesTrigger", "recentBundles")
            .StartAt(runTime)
            //.WithSimpleSchedule(x => x.WithIntervalInSeconds(5))
            .WithSimpleSchedule(x => x.WithIntervalInMinutes(_interval))
            .Build();
        _shutdown = false;
        // Tell quartz to schedule the job using our trigger
        _scheduler.ScheduleJob(job, trigger);
    }

    internal WorkflowStageType MaxWorkflowStage { get; }

    internal WorkflowStageType MinWorkflowStage { get; }

    public RecentlyUpdatedResultBundlesProviderEx ResultBundlesDataProvider { get; }

    #region IObservable<List<RecentlyUpdatedResultBundleDetails>> Members

    public IDisposable Subscribe(IObserver<BundlesList> observer)
    {
        if (!_observers.Contains(observer))
            _observers.Add(observer);
        return new Unsubscriber(_observers, observer);
    }

    #endregion

    internal void BroadcastUpdatedBundles(BundlesList bundles)
    {
        foreach (var observer in _observers) observer.OnNext(bundles);
    }

    /// <summary>
    ///     Starts the broadcast.
    /// </summary>
    public void StartBroadcast()
    {
        if (_shutdown) return;
        try
        {
            FaultHandler.Shield(() =>
            {
                ResultBundlesDataProvider.Login();
                _scheduler.Start();
                _shutdown = false;
            });
        }
        catch (Exception)
        {
            // TODO: log
        }
    }

    /// <summary>
    ///     Ends the broadcast.
    /// </summary>
    public void EndBroadcast()
    {
        _shutdown = true;
        try
        {
            FaultHandler.Shield(() =>
            {
                _scheduler.Shutdown(true);

                foreach (var observer in _observers.ToArray())
                    if (_observers.Contains(observer))
                        observer.OnCompleted();

                _observers.Clear();

                ResultBundlesDataProvider.Shutdown();
            });
        }
        catch (Exception)
        {
            // TODO: log
        }
    }

    /// <summary>
    ///     Pauses the broadcast.
    /// </summary>
    public void PauseBroadcast()
    {
        try
        {
            FaultHandler.Shield(() => _scheduler.PauseAll());
        }
        catch (Exception)
        {
            // TODO: log
        }
    }

    /// <summary>
    ///     Resumes the broadcast.
    /// </summary>
    public void ResumeBroadcast()
    {
        try
        {
            FaultHandler.Shield(() => _scheduler.ResumeAll());
        }
        catch (Exception)
        {
            // TODO: log
        }
    }

    #region Nested type: Unsubscriber

    private class Unsubscriber : IDisposable
    {
        private readonly IObserver<BundlesList> _observer;
        private readonly List<IObserver<BundlesList>> _observers;

        public Unsubscriber(List<IObserver<BundlesList>> observers,
            IObserver<BundlesList> observer)
        {
            _observers = observers;
            _observer = observer;
        }

        #region IDisposable Members

        public void Dispose()
        {
            if (_observer != null && _observers.Contains(_observer))
                _observers.Remove(_observer);
        }

        #endregion
    }

    #endregion
}
#endif