﻿using System;
using CuttingEdge.Conditions;

namespace LabMaestro.BackgroundJobs;

public class ResultBundleWorkflowHookProcessor(long invoiceId, long bundleId) : BackgroundJobProcessor
{
    public static void Perform(long invoiceId, long bundleId) =>
        new ResultBundleWorkflowHookProcessor(invoiceId, bundleId).Handle();

    public override void Handle()
    {
        BgJobsLogger.Log.Debug(formatMessage("Received request"));

        try
        {
            ResultBundleWorkflowStageHooksRegistry.Instance.Execute(invoiceId, bundleId);
            BgJobsLogger.Log.Information(formatMessage("Complete"));
        }
        catch (Exception e)
        {
            BgJobsLogger.Log.Error(e, formatMessage("Exception"));
        }
    }

    private string formatMessage(string msg) => $"[{GetType().Name}] inv:{invoiceId}, bun:{bundleId}] {msg}";
}