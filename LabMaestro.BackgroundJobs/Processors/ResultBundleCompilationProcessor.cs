﻿using System;
using CuttingEdge.Conditions;
using LabMaestro.BackgroundJobs.ResultBundleCompiler;
using LabMaestro.ServiceDAL;

namespace LabMaestro.BackgroundJobs;

public sealed class ResultBundleCompilationProcessor(long invoiceId) : BackgroundJobProcessor
{
    public static void Perform(long invoiceId) => new ResultBundleCompilationProcessor(invoiceId).Handle();

    public override void Handle()
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);
        BgJobsLogger.Log.Debug(formatMessage("Received request"));

        try
        {
            using var db = DataAccessFactory.GetClient();
            var compiler =
                new ResultBundleCompiler.ResultBundleCompiler(invoiceId, new InvoiceOrderedTestsDataProvider(db));
            compiler.CompileBundles();
            BgJobsLogger.Log.Information(formatMessage("Compilation done"));

            ResultBundleDbPersister.InsertNewResultBundles(compiler, db);
            BgJobsLogger.Log.Information(formatMessage("Save complete"));

            // update the lab order workflow stage once result bundles have been created
            LabOrderWorkflowStageEstimateProcessor.Perform(invoiceId);
        }
        catch (Exception e)
        {
            BgJobsLogger.Log.Error(e, formatMessage("Exception"));
        }
    }

    private string formatMessage(string msg) => $"[{GetType().Name}] #{invoiceId} {msg}";
}