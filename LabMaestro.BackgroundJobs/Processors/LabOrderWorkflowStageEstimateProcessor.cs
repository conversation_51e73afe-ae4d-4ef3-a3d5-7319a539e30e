﻿using System;
using CuttingEdge.Conditions;
using LabMaestro.ServiceDAL;

namespace LabMaestro.BackgroundJobs;

public class LabOrderWorkflowStageEstimateProcessor(long invoiceId) : BackgroundJobProcessor
{
    public static void Perform(long invoiceId) => new LabOrderWorkflowStageEstimateProcessor(invoiceId).Handle();

    public override void Handle()
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);

        BgJobsLogger.Log.Debug(formatMessage("Received request"));

        try
        {
            LabOrderWorkflowStageEstimator.Execute(invoiceId);
            BgJobsLogger.Log.Information(formatMessage("Complete"));
        }
        catch (Exception e)
        {
            BgJobsLogger.Log.Error(e, formatMessage("Exception"));
        }
    }

    private string formatMessage(string msg) => $"[{GetType().Name}] #{invoiceId} {msg}";
}