﻿using System;
using CuttingEdge.Conditions;

namespace LabMaestro.BackgroundJobs;

public sealed class LabOrderWorkflowHookProcessor(long invoiceId) : BackgroundJobProcessor
{
    public static void Perform(long invoiceId) => new LabOrderWorkflowHookProcessor(invoiceId).Handle();

    public override void Handle()
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);

        BgJobsLogger.Log.Debug(formatMessage("Received request"));

        try
        {
            LabOrderWorkflowStageHooksRegistry.Instance.Execute(invoiceId, -1);
            BgJobsLogger.Log.Information(formatMessage("Complete"));
        }
        catch (Exception e)
        {
            BgJobsLogger.Log.Error(e, formatMessage("Exception"));
        }
    }

    private string formatMessage(string msg) => $"[{GetType().Name}] #{invoiceId} {msg}";
}