﻿using CuttingEdge.Conditions;
using LabMaestro.Messaging.Core;
using LabMaestro.BackgroundJobs.Messaging;
using LabMaestro.Shared.Domain;

namespace LabMaestro.BackgroundJobs;

public sealed class SmsProcessor(SmsMessageRequest payload) : BackgroundJobProcessor
{
    public static void Process(SmsMessageRequest request) => new SmsProcessor(request).Handle();

    public override void Handle()
    {
        Condition.Ensures(!string.IsNullOrEmpty(payload.PhoneNumber));
        Condition.Ensures(!string.IsNullOrEmpty(payload.Message));

        SmsSender.SendMessage(SmsMessage.AssembleFrom(payload));
    }
}