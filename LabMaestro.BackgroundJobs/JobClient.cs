﻿using System;
using Hangfire;
using Hangfire.SqlServer;

namespace LabMaestro.BackgroundJobs;

public sealed class JobClient
{
    public static void Initialize(string connectionString)
    {
        var options = new SqlServerStorageOptions
        {
            SlidingInvisibilityTimeout = TimeSpan.FromMinutes(5),
            QueuePollInterval = TimeSpan.Zero
        };

        GlobalConfiguration.Configuration
            .UseSqlServerStorage(connectionString, options)
            .UseNLogLogProvider();
    }

    public static void CompileResultBundles(long invoiceId) =>
        BackgroundJob.Enqueue(() => ResultBundleCompilationProcessor.Perform(invoiceId));

    public static void LabOrderWorkflowHook(long invoiceId) =>
        BackgroundJob.Enqueue(() => LabOrderWorkflowHookProcessor.Perform(invoiceId));

    public static void EstimateLabOrderWorkflowStage(long invoiceId) =>
        BackgroundJob.Enqueue(() => LabOrderWorkflowStageEstimateProcessor.Perform(invoiceId));

    public static void ResultBundleWorkflowHook(long invoiceId, long bundleId) =>
        BackgroundJob.Enqueue(() => ResultBundleWorkflowHookProcessor.Perform(invoiceId, bundleId));
}