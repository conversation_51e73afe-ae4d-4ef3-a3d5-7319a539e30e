<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{1F05D6C3-7153-458D-B1A9-4D1E4774DBC1}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>LabMaestro.BackgroundJobs</RootNamespace>
    <AssemblyName>LabMaestro.BackgroundJobs</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <LangVersion>latestmajor</LangVersion>
    <SignAssembly>false</SignAssembly>
    <AssemblyOriginatorKeyFile>..\Keys\labmaestro.snk</AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="BgJobsLogger.cs" />
    <Compile Include="JobClient.cs" />
    <Compile Include="Processors\BackgroundJobProcessor.cs" />
    <Compile Include="Processors\LabOrderWorkflowHookProcessor.cs" />
    <Compile Include="Processors\LabOrderWorkflowStageEstimateProcessor.cs" />
    <Compile Include="Processors\ResultBundleCompilationProcessor.cs" />
    <Compile Include="Processors\ResultBundleWorkflowHookProcessor.cs" />
    <Compile Include="Processors\SmsProcessor.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\LabMaestro.BackgroundJobs.Processors\LabMaestro.BackgroundJobs.Processors.csproj">
      <Project>{5b650925-f69e-4f59-b03a-bc2dbf680e01}</Project>
      <Name>LabMaestro.BackgroundJobs.Processors</Name>
    </ProjectReference>
    <ProjectReference Include="..\LabMaestro.BulkMessageTransport.Core\LabMaestro.BulkMessageTransport.Core.csproj">
      <Project>{a0450d82-4dbe-4fcd-b87b-12dacd96c3ad}</Project>
      <Name>LabMaestro.BulkMessageTransport.Core</Name>
    </ProjectReference>
    <ProjectReference Include="..\LabMaestro.BulkMessageTransport.Email\LabMaestro.BulkMessageTransport.Email.csproj">
      <Project>{6a94f78f-8314-4e00-965f-d42a0ce02f65}</Project>
      <Name>LabMaestro.BulkMessageTransport.Email</Name>
    </ProjectReference>
    <ProjectReference Include="..\LabMaestro.BulkMessageTransport.Sms\LabMaestro.BulkMessageTransport.Sms.csproj">
      <Project>{21dbad7b-8e1b-46e1-bbca-a27d3e7cdbd7}</Project>
      <Name>LabMaestro.BulkMessageTransport.Sms</Name>
    </ProjectReference>
    <ProjectReference Include="..\LabMaestro.Messaging.Core\LabMaestro.Messaging.Core.csproj">
      <Project>{e0426849-eafc-4e1d-898e-564bdc18c6e6}</Project>
      <Name>LabMaestro.Messaging.Core</Name>
    </ProjectReference>
    <ProjectReference Include="..\LabMaestro.Messaging.Transport\LabMaestro.Messaging.Transport.csproj">
      <Project>{ea45fb03-a09f-489d-b646-935a8d3eea09}</Project>
      <Name>LabMaestro.Messaging.Transport</Name>
    </ProjectReference>
    <ProjectReference Include="..\LabMaestro.ServiceDAL.Model\LabMaestro.ServiceDAL.Model.csproj">
      <Project>{d6c2bf28-5823-4b3a-be17-84ecc60a7d3c}</Project>
      <Name>LabMaestro.ServiceDAL.Model</Name>
    </ProjectReference>
    <ProjectReference Include="..\LabMaestro.ServiceDAL\LabMaestro.ServiceDAL.csproj">
      <Project>{ad31717e-94c6-4963-ad95-398d8ab5308d}</Project>
      <Name>LabMaestro.ServiceDAL</Name>
    </ProjectReference>
    <ProjectReference Include="..\LabMaestro.Shared\LabMaestro.Shared.csproj">
      <Project>{b15584a4-1ea1-48aa-a823-29307bb8d16a}</Project>
      <Name>LabMaestro.Shared</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Hangfire.Core">
      <Version>1.8.20</Version>
    </PackageReference>
    <PackageReference Include="Hangfire.SqlServer">
      <Version>1.8.20</Version>
    </PackageReference>
    <PackageReference Include="Serilog">
      <Version>4.3.0</Version>
    </PackageReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="..\Keys\labmaestro.snk">
      <Link>labmaestro.snk</Link>
    </None>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>