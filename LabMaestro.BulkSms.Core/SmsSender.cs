﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2015, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: SmsSender.cs 1550 2015-06-25 11:38:07Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Net.Sockets;
using System.Text;
using LabMaestro.Messaging.Core;
using LabMaestro.Messaging.Transport.Sms;
using LabMaestro.Messaging.Transport.Sms.Gateways;
using Newtonsoft.Json;
using NLog;

namespace LabMaestro.BulkSms.Core;

internal sealed class SmsSender
{
    private readonly Logger _logger = LogManager.GetCurrentClassLogger();
    public string Username { get; set; }
    public string Password { get; set; }
    public string Originator { get; set; }
    public string Gateway { get; set; }
    public SmsSenderEventHandler MessageSent { get; set; }
    public SmsSenderEventHandler MessageNotSent { get; set; }

    private bool tcpSocketTest()
    {
        return true;
        try
        {
            using var client = new TcpClient("www.google.com", 80);
            return client.Connected;
        }
        catch (Exception exc)
        {
            _logger.Error(exc);
        }

        return false;
    }

    public void RunJob(string filename, QueuedSmsMessage sms)
    {
        try
        {
            if (tcpSocketTest())
                DeliverMessage(sms.PhoneNumber, sms.MessageContent, filename);
            else
                _logger.Warn($"Skipping delivery to {sms.PhoneNumber} due to connectivity disruption.");
        }
        catch (Exception exc)
        {
            _logger.Error(exc);
            MessageNotSent?.Invoke(this, new SmsEventArgs { Filename = filename, IsTransientError = false });
        }
    }

    public void DeliverMessage(string phoneNumber, string msgText, string filename)
    {
        var args = new SmsEventArgs { MsgText = msgText, Phone = phoneNumber, Filename = filename };

        try
        {
            SmsGatewayFactory.ApiKey = Username;
            SmsGatewayFactory.ApiSecret = Password;

            var sender = SmsGatewayFactory.GetSmsGateway(Gateway);
            var success = sender.SendMessage(new SmsMessage
            {
                PhoneNumber = phoneNumber,
                Message = msgText
            });

            _logger.Debug("{0} send: {1}", phoneNumber, success ? "OK" : "FAIL");

            MessageSent?.Invoke(this, args);
        }
        catch (Exception exc)
        {
            _logger.Error(exc);
            args.IsTransientError = true;
            MessageNotSent?.Invoke(this, args);
        }
    }

    private bool within24Hours(DateTime dt) => dt >= DateTime.Now.AddDays(-1) && dt <= DateTime.Now.AddDays(+1);

    public QueuedSmsMessage ExtractMessage(string filename)
    {
        try
        {
            if (File.Exists(filename))
            {
                var data = File.ReadAllText(filename);
                var sms = QueuedSmsMessage.FromJson(data);
                var now = DateTime.Now;
                var modified = false;

                // sanitize
                if (!within24Hours(sms.CreatedOn))
                {
                    _logger.Debug("Fixing bad creation time. Num: {0}, Created: {1}, Sched: {2}, Expire: {3}",
                        sms.PhoneNumber,
                        sms.CreatedOn.ToString("g"),
                        sms.SendOn.ToString("g"),
                        sms.ExpireOn.ToString("g"));
                    sms.CreatedOn = now;
                    modified = true;
                }

                if (!within24Hours(sms.SendOn))
                {
                    _logger.Debug("Fixing bad schedule time. Num: {0}, Created: {1}, Sched: {2}, Expire: {3}",
                        sms.PhoneNumber,
                        sms.CreatedOn.ToString("g"),
                        sms.SendOn.ToString("g"),
                        sms.ExpireOn.ToString("g"));
                    sms.SendOn = sms.CreatedOn.AddMinutes(15);
                    modified = true;
                }

                if (!within24Hours(sms.ExpireOn))
                {
                    _logger.Debug("Fixing bad expiry time. Num: {0}, Created: {1}, Sched: {2}, Expire: {3}",
                        sms.PhoneNumber,
                        sms.CreatedOn.ToString("g"),
                        sms.SendOn.ToString("g"),
                        sms.ExpireOn.ToString("g"));
                    sms.ExpireOn = sms.CreatedOn.AddHours(6);
                    modified = true;
                }

                // if message is expired then remove it from the queue
                if (sms.ExpireOn <= now)
                {
                    _logger.Debug("Expired. Num: {0}, Created: {1}, Sched: {2}, Expire: {3}",
                        sms.PhoneNumber,
                        sms.CreatedOn.ToString("g"),
                        sms.SendOn.ToString("g"),
                        sms.ExpireOn.ToString("g"));
                    File.Delete(filename);
                    return null;
                }

                if (sms.SendOn <= now)
                    return sms;

                if (modified) File.WriteAllText(filename, sms.ToJson());

                _logger.Debug("Skipped. Num: {0}, Created: {1}, Sched: {2}, Expire: {3}",
                    sms.PhoneNumber,
                    sms.CreatedOn.ToString("g"),
                    sms.SendOn.ToString("g"),
                    sms.ExpireOn.ToString("g"));
            }
        }
        catch (Exception exc)
        {
            _logger.Error(exc);
        }

        return null;
    }
}