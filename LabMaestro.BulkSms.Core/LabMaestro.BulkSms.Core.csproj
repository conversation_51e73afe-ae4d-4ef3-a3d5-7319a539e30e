<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{04F51CFD-6520-455D-AE53-FBF51E03823F}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>LabMaestro.BulkSms.Core</RootNamespace>
    <AssemblyName>LabMaestro.BulkSms.Core</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SolutionDir Condition="$(SolutionDir) == '' Or $(SolutionDir) == '*Undefined*'">..\</SolutionDir>
    <TargetFrameworkProfile />
    <LangVersion>latestmajor</LangVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <SignAssembly>false</SignAssembly>
  </PropertyGroup>
  <PropertyGroup>
    <AssemblyOriginatorKeyFile>labmaestro.snk</AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.IO.Compression" />
    <Reference Include="System.Net" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Net.Http.WebRequest" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Transactions" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="BulkSmsConfig.cs" />
    <Compile Include="FastDirectoryEnumerator.cs" />
    <Compile Include="Providers\AbstractSmsProvider.cs" />
    <Compile Include="Providers\AdnSmsProvider.cs" />
    <Compile Include="Providers\AdtechLegacySmsProvider.cs" />
    <Compile Include="Providers\ISmsProvider.cs" />
    <Compile Include="SmsEventArgs.cs" />
    <Compile Include="SmsQueueConfig.cs" />
    <Compile Include="QueuedSmsMessage.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="SmsQueueRunner.cs" />
    <Compile Include="SmsSender.cs" />
    <Compile Include="SmsSenderEventHandler.cs" />
    <Compile Include="Utilities.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="labmaestro.snk" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json">
      <Version>13.0.3</Version>
    </PackageReference>
    <PackageReference Include="NLog">
      <Version>6.0.1</Version>
    </PackageReference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\LabMaestro.Messaging.Core\LabMaestro.Messaging.Core.csproj">
      <Project>{e0426849-eafc-4e1d-898e-564bdc18c6e6}</Project>
      <Name>LabMaestro.Messaging.Core</Name>
    </ProjectReference>
    <ProjectReference Include="..\LabMaestro.Messaging.Transport\LabMaestro.Messaging.Transport.csproj">
      <Project>{ea45fb03-a09f-489d-b646-935a8d3eea09}</Project>
      <Name>LabMaestro.Messaging.Transport</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
         Other similar extension points exist, see Microsoft.Common.targets.
    <Target Name="BeforeBuild">
    </Target>
    <Target Name="AfterBuild">
    </Target>
    -->
</Project>