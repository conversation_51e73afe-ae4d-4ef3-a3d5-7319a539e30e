﻿using System;
using System.Collections.Specialized;
using System.Net;

namespace LabMaestro.BulkSms.Core.Providers;

internal class AdtechLegacySmsProvider : AbstractSmsProvider
{
    public override string ProviderName => "ADTECH_L";
    public override string Endpoint => @"http://brandsms.adtechsoft.com/api.php";
    public string Username { get; set; }
    public string Password { get; set; }
    public string Originator { get; set; }

    public override bool SendMessage(string phoneNumber, string msgText, string filename)
    {
        var args = new SmsEventArgs { MsgText = msgText, Phone = phoneNumber, Filename = filename };
        //username=&password=&number=8801XXXXXXXXX&sender=&type=0&message=This Text Message 
        var smsParams = new NameValueCollection
        {
            { "username", Username },
            { "password", Password },
            { "sender", Originator },
            { "number", phoneNumber },
            { "message", msgText },
            { "type", "0" }
        };
        var smsUrl = $"{Endpoint}{toQueryString(smsParams)}";
        try
        {
            using var client = new WebClient();
            CurrentLogger.Info("Sending sms to {0}", phoneNumber);
            var response = client.DownloadString(smsUrl);
            CurrentLogger.Debug("{0} response: {1}", phoneNumber, response);

            //1101|8801815125841|6a47df354568f6403626a06aa9699a2a 
            if (!string.IsNullOrEmpty(response) && response.Contains("|"))
            {
                if (response.StartsWith("1101|"))
                {
                    MessageSent?.Invoke(this, args);
                    return true;
                }

                args.IsTransientError = false;
                CurrentLogger.Warn("{0} Failure.", phoneNumber);
                MessageNotSent?.Invoke(this, args);
            }
            else
            {
                args.IsTransientError = false;
                CurrentLogger.Warn("{0} Permanent failure.", phoneNumber);
                MessageNotSent?.Invoke(this, args);
            }
        }
        catch (Exception exc)
        {
            CurrentLogger.Error(exc);
            args.IsTransientError = true;
            MessageNotSent?.Invoke(this, args);
        }

        return false;
    }
}