﻿using System;
using System.Collections.Specialized;
using System.Linq;
using System.Net.Sockets;
using System.Web;
using NLog;

namespace LabMaestro.BulkSms.Core.Providers;

internal abstract class AbstractSmsProvider : ISmsProvider
{
    protected readonly Logger CurrentLogger = LogManager.GetCurrentClassLogger();
    public SmsSenderEventHandler MessageSent { get; set; }
    public SmsSenderEventHandler MessageNotSent { get; set; }
    public abstract string ProviderName { get; }
    public abstract string Endpoint { get; }

    public bool CheckOnline()
    {
        var hostname = new Uri(Endpoint).Host;
        CurrentLogger.Info(@"Checking connectivity to {0}", hostname);
        try
        {
            using var client = new TcpClient(hostname, 80);
            return client.Connected;
        }
        catch (Exception exc)
        {
            CurrentLogger.Error(exc);
        }

        return false;
    }

    public abstract bool SendMessage(string phoneNumber, string msgText, string filename);

    protected string toQueryString(NameValueCollection nvc)
    {
        var array = (from key in nvc.AllKeys
                from value in nvc.GetValues(key)
                select $"{HttpUtility.UrlEncode(key)}={HttpUtility.UrlEncode(value)}")
            .ToArray();
        return $"?{string.Join("&", array)}";
    }
}