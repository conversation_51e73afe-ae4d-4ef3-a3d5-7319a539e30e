﻿using System;

namespace LabMaestro.BulkSms.Core.Providers;

internal class AdnSmsProvider : AbstractSmsProvider
{
    public override string ProviderName => "ADN";
    public override string Endpoint => @"ADN";

    public override bool SendMessage(string phoneNumber, string msgText, string filename)
    {
        CurrentLogger.Info($"Sending sms to {phoneNumber}");
        throw new NotImplementedException();
    }
}