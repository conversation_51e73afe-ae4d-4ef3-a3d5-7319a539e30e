﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: FastDirectoryEnumerator.cs 1412 2014-10-01 04:36:18Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Runtime.ConstrainedExecution;
using System.Runtime.InteropServices;
using System.Security;
using System.Security.Permissions;
using Microsoft.Win32.SafeHandles;

namespace LabMaestro.BulkSms.Core;

/// <summary>
///     Contains information about a file returned by the
///     <see cref="FastDirectoryEnumerator" /> class.
/// </summary>
[Serializable]
internal class FileData
{
    /// <summary>
    ///     Attributes of the file.
    /// </summary>
    public readonly FileAttributes Attributes;

    /// <summary>
    ///     File creation time in UTC
    /// </summary>
    public readonly DateTime CreationTimeUtc;

    /// <summary>
    ///     File last access time in UTC
    /// </summary>
    public readonly DateTime LastAccessTimeUtc;

    /// <summary>
    ///     File last write time in UTC
    /// </summary>
    public readonly DateTime LastWriteTimeUtc;

    /// <summary>
    ///     Name of the file
    /// </summary>
    public readonly string Name;

    /// <summary>
    ///     Full path to the file.
    /// </summary>
    public readonly string Path;

    /// <summary>
    ///     Size of the file in bytes
    /// </summary>
    public readonly long Size;

    /// <summary>
    ///     Initializes a new instance of the <see cref="FileData" /> class.
    /// </summary>
    /// <param name="dir">The directory that the file is stored at</param>
    /// <param name="findData">
    ///     WIN32_FIND_DATA structure that this
    ///     object wraps.
    /// </param>
    internal FileData(string dir, WIN32_FIND_DATA findData)
    {
        Attributes = findData.dwFileAttributes;


        CreationTimeUtc = ConvertDateTime(findData.ftCreationTime_dwHighDateTime,
            findData.ftCreationTime_dwLowDateTime);

        LastAccessTimeUtc = ConvertDateTime(findData.ftLastAccessTime_dwHighDateTime,
            findData.ftLastAccessTime_dwLowDateTime);

        LastWriteTimeUtc = ConvertDateTime(findData.ftLastWriteTime_dwHighDateTime,
            findData.ftLastWriteTime_dwLowDateTime);

        Size = CombineHighLowInts(findData.nFileSizeHigh, findData.nFileSizeLow);

        Name = findData.cFileName;
        Path = System.IO.Path.Combine(dir, findData.cFileName);
    }

    public DateTime CreationTime
    {
        get { return CreationTimeUtc.ToLocalTime(); }
    }

    /// <summary>
    ///     Gets the last access time in local time.
    /// </summary>
    public DateTime LastAccesTime
    {
        get { return LastAccessTimeUtc.ToLocalTime(); }
    }

    /// <summary>
    ///     Gets the last access time in local time.
    /// </summary>
    public DateTime LastWriteTime
    {
        get { return LastWriteTimeUtc.ToLocalTime(); }
    }

    /// <summary>
    ///     Returns a <see cref="T:System.String" /> that represents the current <see cref="T:System.Object" />.
    /// </summary>
    /// <returns>
    ///     A <see cref="T:System.String" /> that represents the current <see cref="T:System.Object" />.
    /// </returns>
    public override string ToString()
    {
        return Name;
    }

    private static long CombineHighLowInts(uint high, uint low)
    {
        return ((long)high << 0x20) | low;
    }

    private static DateTime ConvertDateTime(uint high, uint low)
    {
        var fileTime = CombineHighLowInts(high, low);
        return DateTime.FromFileTimeUtc(fileTime);
    }
}

/// <summary>
///     Contains information about the file that is found
///     by the FindFirstFile or FindNextFile functions.
/// </summary>
[Serializable]
[StructLayout(LayoutKind.Sequential, CharSet = CharSet.Auto)]
[BestFitMapping(false)]
internal class WIN32_FIND_DATA
{
    [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 14)]
    public string cAlternateFileName;

    [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 260)]
    public string cFileName;

    public FileAttributes dwFileAttributes;
    public int dwReserved0;
    public int dwReserved1;
    public uint ftCreationTime_dwHighDateTime;
    public uint ftCreationTime_dwLowDateTime;
    public uint ftLastAccessTime_dwHighDateTime;
    public uint ftLastAccessTime_dwLowDateTime;
    public uint ftLastWriteTime_dwHighDateTime;
    public uint ftLastWriteTime_dwLowDateTime;
    public uint nFileSizeHigh;
    public uint nFileSizeLow;

    /// <summary>
    ///     Returns a <see cref="T:System.String" /> that represents the current <see cref="T:System.Object" />.
    /// </summary>
    /// <returns>
    ///     A <see cref="T:System.String" /> that represents the current <see cref="T:System.Object" />.
    /// </returns>
    public override string ToString()
    {
        return "File name=" + cFileName;
    }
}

/// <summary>
///     A fast enumerator of files in a directory.  Use this if you need to get attributes for
///     all files in a directory.
/// </summary>
/// <remarks>
///     This enumerator is substantially faster than using <see cref="Directory.GetFiles(string)" />
///     and then creating a new FileInfo object for each path.  Use this version when you
///     will need to look at the attibutes of each file returned (for example, you need
///     to check each file in a directory to see if it was modified after a specific date).
/// </remarks>
internal static class FastDirectoryEnumerator
{
    /// <summary>
    ///     Gets <see cref="FileData" /> for all the files in a directory.
    /// </summary>
    /// <param name="path">The path to search.</param>
    /// <returns>
    ///     An object that implements <see cref="IEnumerable{FileData}" /> and
    ///     allows you to enumerate the files in the given directory.
    /// </returns>
    /// <exception cref="ArgumentNullException">
    ///     <paramref name="path" /> is a null reference (Nothing in VB)
    /// </exception>
    public static IEnumerable<FileData> EnumerateFiles(string path)
    {
        return EnumerateFiles(path, "*");
    }

    /// <summary>
    ///     Gets <see cref="FileData" /> for all the files in a directory that match a
    ///     specific filter.
    /// </summary>
    /// <param name="path">The path to search.</param>
    /// <param name="searchPattern">The search string to match against files in the path.</param>
    /// <returns>
    ///     An object that implements <see cref="IEnumerable{FileData}" /> and
    ///     allows you to enumerate the files in the given directory.
    /// </returns>
    /// <exception cref="ArgumentNullException">
    ///     <paramref name="path" /> is a null reference (Nothing in VB)
    /// </exception>
    /// <exception cref="ArgumentNullException">
    ///     <paramref name="filter" /> is a null reference (Nothing in VB)
    /// </exception>
    public static IEnumerable<FileData> EnumerateFiles(string path, string searchPattern)
    {
        return EnumerateFiles(path, searchPattern, SearchOption.TopDirectoryOnly);
    }

    /// <summary>
    ///     Gets <see cref="FileData" /> for all the files in a directory that
    ///     match a specific filter, optionally including all sub directories.
    /// </summary>
    /// <param name="path">The path to search.</param>
    /// <param name="searchPattern">The search string to match against files in the path.</param>
    /// <param name="searchOption">
    ///     One of the SearchOption values that specifies whether the search
    ///     operation should include all subdirectories or only the current directory.
    /// </param>
    /// <returns>
    ///     An object that implements <see cref="IEnumerable{FileData}" /> and
    ///     allows you to enumerate the files in the given directory.
    /// </returns>
    /// <exception cref="ArgumentNullException">
    ///     <paramref name="path" /> is a null reference (Nothing in VB)
    /// </exception>
    /// <exception cref="ArgumentNullException">
    ///     <paramref name="filter" /> is a null reference (Nothing in VB)
    /// </exception>
    /// <exception cref="ArgumentOutOfRangeException">
    ///     <paramref name="searchOption" /> is not one of the valid values of the
    ///     <see cref="System.IO.SearchOption" /> enumeration.
    /// </exception>
    public static IEnumerable<FileData> EnumerateFiles(string path, string searchPattern, SearchOption searchOption)
    {
        if (path == null) throw new ArgumentNullException("path");
        if (searchPattern == null) throw new ArgumentNullException("searchPattern");
        if (searchOption != SearchOption.TopDirectoryOnly && searchOption != SearchOption.AllDirectories)
            throw new ArgumentOutOfRangeException("searchOption");

        var fullPath = Path.GetFullPath(path);

        return new FileEnumerable(fullPath, searchPattern, searchOption);
    }

    /// <summary>
    ///     Gets <see cref="FileData" /> for all the files in a directory that match a
    ///     specific filter.
    /// </summary>
    /// <param name="path">The path to search.</param>
    /// <param name="searchPattern">The search string to match against files in the path.</param>
    /// <returns>
    ///     An object that implements <see cref="IEnumerable{FileData}" /> and
    ///     allows you to enumerate the files in the given directory.
    /// </returns>
    /// <exception cref="ArgumentNullException">
    ///     <paramref name="path" /> is a null reference (Nothing in VB)
    /// </exception>
    /// <exception cref="ArgumentNullException">
    ///     <paramref name="filter" /> is a null reference (Nothing in VB)
    /// </exception>
    public static FileData[] GetFiles(string path, string searchPattern, SearchOption searchOption)
    {
        var e = EnumerateFiles(path, searchPattern, searchOption);
        var list = new List<FileData>(e);

        var retval = new FileData[list.Count];
        list.CopyTo(retval);

        return retval;
    }

    /// <summary>
    ///     Provides the implementation of the
    ///     <see cref="T:System.Collections.Generic.IEnumerable`1" /> interface
    /// </summary>
    private class FileEnumerable : IEnumerable<FileData>
    {
        private readonly string m_filter;
        private readonly string m_path;
        private readonly SearchOption m_searchOption;

        /// <summary>
        ///     Initializes a new instance of the <see cref="FileEnumerable" /> class.
        /// </summary>
        /// <param name="path">The path to search.</param>
        /// <param name="filter">The search string to match against files in the path.</param>
        /// <param name="searchOption">
        ///     One of the SearchOption values that specifies whether the search
        ///     operation should include all subdirectories or only the current directory.
        /// </param>
        public FileEnumerable(string path, string filter, SearchOption searchOption)
        {
            m_path = path;
            m_filter = filter;
            m_searchOption = searchOption;
        }

        #region IEnumerable<FileData> Members

        /// <summary>
        ///     Returns an enumerator that iterates through the collection.
        /// </summary>
        /// <returns>
        ///     A <see cref="T:System.Collections.Generic.IEnumerator`1" /> that can
        ///     be used to iterate through the collection.
        /// </returns>
        public IEnumerator<FileData> GetEnumerator()
        {
            return new FileEnumerator(m_path, m_filter, m_searchOption);
        }

        #endregion

        #region IEnumerable Members

        /// <summary>
        ///     Returns an enumerator that iterates through a collection.
        /// </summary>
        /// <returns>
        ///     An <see cref="T:System.Collections.IEnumerator" /> object that can be
        ///     used to iterate through the collection.
        /// </returns>
        IEnumerator IEnumerable.GetEnumerator()
        {
            return new FileEnumerator(m_path, m_filter, m_searchOption);
        }

        #endregion
    }

    /// <summary>
    ///     Provides the implementation of the
    ///     <see cref="T:System.Collections.Generic.IEnumerator`1" /> interface
    /// </summary>
    [SuppressUnmanagedCodeSecurity]
    private class FileEnumerator : IEnumerator<FileData>
    {
        private readonly Stack<SearchContext> m_contextStack;
        private readonly string m_filter;
        private readonly SearchOption m_searchOption;
        private readonly WIN32_FIND_DATA m_win_find_data = new();
        private SearchContext m_currentContext;

        private SafeFindHandle m_hndFindFile;
        private string m_path;

        /// <summary>
        ///     Initializes a new instance of the <see cref="FileEnumerator" /> class.
        /// </summary>
        /// <param name="path">The path to search.</param>
        /// <param name="filter">The search string to match against files in the path.</param>
        /// <param name="searchOption">
        ///     One of the SearchOption values that specifies whether the search
        ///     operation should include all subdirectories or only the current directory.
        /// </param>
        public FileEnumerator(string path, string filter, SearchOption searchOption)
        {
            m_path = path;
            m_filter = filter;
            m_searchOption = searchOption;
            m_currentContext = new SearchContext(path);

            if (m_searchOption == SearchOption.AllDirectories) m_contextStack = new Stack<SearchContext>();
        }

        #region IEnumerator<FileData> Members

        /// <summary>
        ///     Gets the element in the collection at the current position of the enumerator.
        /// </summary>
        /// <value></value>
        /// <returns>
        ///     The element in the collection at the current position of the enumerator.
        /// </returns>
        public FileData Current
        {
            get { return new FileData(m_path, m_win_find_data); }
        }

        #endregion

        #region IDisposable Members

        /// <summary>
        ///     Performs application-defined tasks associated with freeing, releasing,
        ///     or resetting unmanaged resources.
        /// </summary>
        public void Dispose()
        {
            if (m_hndFindFile != null) m_hndFindFile.Dispose();
        }

        #endregion

        [DllImport("kernel32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        private static extern SafeFindHandle FindFirstFile(string fileName,
            [In] [Out] WIN32_FIND_DATA data);

        [DllImport("kernel32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        private static extern bool FindNextFile(SafeFindHandle hndFindFile,
            [In] [Out] [MarshalAs(UnmanagedType.LPStruct)]
            WIN32_FIND_DATA
                lpFindFileData);

        /// <summary>
        ///     Hold context information about where we current are in the directory search.
        /// </summary>
        private class SearchContext
        {
            public readonly string Path;
            public Stack<string> SubdirectoriesToProcess;

            public SearchContext(string path)
            {
                Path = path;
            }
        }

        #region IEnumerator Members

        /// <summary>
        ///     Gets the element in the collection at the current position of the enumerator.
        /// </summary>
        /// <value></value>
        /// <returns>
        ///     The element in the collection at the current position of the enumerator.
        /// </returns>
        object IEnumerator.Current
        {
            get { return new FileData(m_path, m_win_find_data); }
        }

        /// <summary>
        ///     Advances the enumerator to the next element of the collection.
        /// </summary>
        /// <returns>
        ///     true if the enumerator was successfully advanced to the next element;
        ///     false if the enumerator has passed the end of the collection.
        /// </returns>
        /// <exception cref="T:System.InvalidOperationException">
        ///     The collection was modified after the enumerator was created.
        /// </exception>
        public bool MoveNext()
        {
            var retval = false;

            //If the handle is null, this is first call to MoveNext in the current 
            // directory.  In that case, start a new search.
            if (m_currentContext.SubdirectoriesToProcess == null)
            {
                if (m_hndFindFile == null)
                {
                    new FileIOPermission(FileIOPermissionAccess.PathDiscovery, m_path).Demand();

                    var searchPath = Path.Combine(m_path, m_filter);
                    m_hndFindFile = FindFirstFile(searchPath, m_win_find_data);
                    retval = !m_hndFindFile.IsInvalid;
                }
                else
                {
                    //Otherwise, find the next item.
                    retval = FindNextFile(m_hndFindFile, m_win_find_data);
                }
            }

            //If the call to FindNextFile or FindFirstFile succeeded...
            if (retval)
            {
                if ((m_win_find_data.dwFileAttributes & FileAttributes.Directory) == FileAttributes.Directory)
                    //Ignore folders for now.   We call MoveNext recursively here to 
                    // move to the next item that FindNextFile will return.
                    return MoveNext();
            }
            else if (m_searchOption == SearchOption.AllDirectories)
            {
                //SearchContext context = new SearchContext(m_hndFindFile, m_path);
                //m_contextStack.Push(context);
                //m_path = Path.Combine(m_path, m_win_find_data.cFileName);
                //m_hndFindFile = null;

                if (m_currentContext.SubdirectoriesToProcess == null)
                {
                    var subDirectories = Directory.GetDirectories(m_path);
                    m_currentContext.SubdirectoriesToProcess = new Stack<string>(subDirectories);
                }

                if (m_currentContext.SubdirectoriesToProcess.Count > 0)
                {
                    var subDir = m_currentContext.SubdirectoriesToProcess.Pop();

                    m_contextStack.Push(m_currentContext);
                    m_path = subDir;
                    m_hndFindFile = null;
                    m_currentContext = new SearchContext(m_path);
                    return MoveNext();
                }

                //If there are no more files in this directory and we are 
                // in a sub directory, pop back up to the parent directory and
                // continue the search from there.
                if (m_contextStack.Count > 0)
                {
                    m_currentContext = m_contextStack.Pop();
                    m_path = m_currentContext.Path;
                    if (m_hndFindFile != null)
                    {
                        m_hndFindFile.Close();
                        m_hndFindFile = null;
                    }

                    return MoveNext();
                }
            }

            return retval;
        }

        /// <summary>
        ///     Sets the enumerator to its initial position, which is before the first element in the collection.
        /// </summary>
        /// <exception cref="T:System.InvalidOperationException">
        ///     The collection was modified after the enumerator was created.
        /// </exception>
        public void Reset()
        {
            m_hndFindFile = null;
        }

        #endregion
    }

    /// <summary>
    ///     Wraps a FindFirstFile handle.
    /// </summary>
    private sealed class SafeFindHandle : SafeHandleZeroOrMinusOneIsInvalid
    {
        /// <summary>
        ///     Initializes a new instance of the <see cref="SafeFindHandle" /> class.
        /// </summary>
        [SecurityPermission(SecurityAction.LinkDemand, UnmanagedCode = true)]
        internal SafeFindHandle()
            : base(true)
        {
        }

        [ReliabilityContract(Consistency.WillNotCorruptState, Cer.Success)]
        [DllImport("kernel32.dll")]
        private static extern bool FindClose(IntPtr handle);

        /// <summary>
        ///     When overridden in a derived class, executes the code required to free the handle.
        /// </summary>
        /// <returns>
        ///     true if the handle is released successfully; otherwise, in the
        ///     event of a catastrophic failure, false. In this case, it
        ///     generates a releaseHandleFailed MDA Managed Debugging Assistant.
        /// </returns>
        protected override bool ReleaseHandle()
        {
            return FindClose(handle);
        }
    }
}