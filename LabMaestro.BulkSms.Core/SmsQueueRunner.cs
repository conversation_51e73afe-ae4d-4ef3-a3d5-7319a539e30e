﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: SmsQueueRunner.cs 1464 2014-10-15 14:59:16Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System.IO;
using System.Reflection;
using NLog;

namespace LabMaestro.BulkSms.Core;

public class SmsQueueRunner
{
    private readonly Logger _logger = LogManager.GetCurrentClassLogger();
    private SmsSender _sender;

    public SmsQueueRunner(bool initSender, string configPath = null)
    {
        configPath ??= Utilities.DefaultConfigFilename;
        ServiceConfig = BulkSmsConfig.Load(configPath);

        if (initSender) InitTransport();
    }

    public BulkSmsConfig ServiceConfig { get; }

    private void OnMessageSent(object sender, SmsEventArgs e)
    {
        try
        {
            File.Delete(e.Filename);
        }
        catch (IOException ex)
        {
            _logger.Error(ex);
        }
    }

    private void OnMessageNotSent(object sender, SmsEventArgs e)
    {
        if (e.IsTransientError) return;

        try
        {
            var newPath = Path.Combine(ServiceConfig.SmsQueue.GetBadFolderPath(), Path.GetFileName(e.Filename));
            if (File.Exists(newPath)) File.Delete(newPath);
            File.Move(e.Filename, newPath);
        }
        catch (IOException ex)
        {
            _logger.Error(ex);
        }
    }

    private void InitTransport()
    {
        _sender = new SmsSender
        {
            Gateway = ServiceConfig.SmsQueue.SmsGateway.ToLowerInvariant(),
            Username = ServiceConfig.SmsQueue.SmsUsername,
            Password = ServiceConfig.SmsQueue.SmsPassword,
            Originator = ServiceConfig.SmsQueue.SmsOriginator
        };
        _sender.MessageSent += OnMessageSent;
        _sender.MessageNotSent += OnMessageNotSent;
    }

    public void Run()
    {
        _logger.Debug("running queue...");
        //foreach (var f in FastDirectoryEnumerator.EnumerateFiles(ServiceConfig.SmsQueue.GetPickupFolderPath(), "*.sms"))
        foreach (var f in Directory.EnumerateFiles(ServiceConfig.SmsQueue.GetPickupFolderPath(), "*.sms"))
        {
            var sms = _sender.ExtractMessage(f);
            if (sms != null) _sender.RunJob(f, sms);
        }
    }

    public void RetryBads()
    {
        var files = Directory.GetFiles(ServiceConfig.SmsQueue.GetBadFolderPath());
        foreach (var file in files)
            File.Move(file,
                Path.Combine(ServiceConfig.SmsQueue.GetPickupFolderPath(), Path.GetFileName(file))
            );
    }

    public int GetPickupCount() => Directory.GetFiles(ServiceConfig.SmsQueue.GetPickupFolderPath()).Length;

    public int GetBadCount() => Directory.GetFiles(ServiceConfig.SmsQueue.GetBadFolderPath()).Length;

    private void AssertFolder(string folderPath)
    {
        if (!Directory.Exists(folderPath))
            throw new DirectoryNotFoundException($"\"{folderPath}\" folder does not exist");
    }

    public void AssertPickupFolder() => AssertFolder(ServiceConfig.SmsQueue.GetPickupFolderPath());

    public void AssertBadFolder() => AssertFolder(ServiceConfig.SmsQueue.GetBadFolderPath());

    public bool RootDirectoryExists() => Directory.Exists(ServiceConfig.SmsQueue.RootFolder);

    public string GetDefaultRootFolder() =>
        Path.Combine(Path.GetPathRoot(Assembly.GetExecutingAssembly().Location), "BulkSMS");

    public void SetRootFolderIfNotSet()
    {
        if (string.IsNullOrEmpty(ServiceConfig.SmsQueue.RootFolder))
            ServiceConfig.SmsQueue.RootFolder = GetDefaultRootFolder();
    }

    public void CreateDirectoryStructure()
    {
        Directory.CreateDirectory(ServiceConfig.SmsQueue.RootFolder);
        Directory.CreateDirectory(ServiceConfig.SmsQueue.GetPickupFolderPath());
        Directory.CreateDirectory(ServiceConfig.SmsQueue.GetBadFolderPath());
    }
}