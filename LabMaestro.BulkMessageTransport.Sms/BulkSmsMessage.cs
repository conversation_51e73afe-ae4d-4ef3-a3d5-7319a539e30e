﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: BulkSmsMessage.cs 1505 2014-11-16 11:28:09Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using LabMaestro.BulkMessageTransport.Core;
using LabMaestro.BulkSms.Core;
using LabMaestro.ServiceDAL.Model;

namespace LabMaestro.BulkMessageTransport.Sms;

public sealed class BulkSmsMessage : IBulkMessage
{
    public string SmsFilename { get; set; }
    public long InvoiceId { get; set; }
    public int DelayMinutes { get; set; }
    public int ExpiryHours { get; set; }
    public Guid JobId { get; } = Guid.NewGuid();
    public string MessageContent { get; set; }
    public string DestinationAddress { get; set; }
    public BulkMessageDeliveryStatus DeliveryStatus { get; set; } = BulkMessageDeliveryStatus.New;
    public int RetryCount { get; set; }
    public DateTime DateQueued { get; set; }
    public DateTime LastUpdated { get; set; }

    public bool HasValidDestination()
    {
        var mobileOperatorPrefixes = new[] { "011", "015", "016", "017", "018", "019" };
        //var regex = new Regex(@"[\d]+");
        // @"\+?[0-9]{10,16}
        if (Regex.IsMatch(DestinationAddress, @"[\d]{8,18}", RegexOptions.IgnoreCase))
        {
            if (mobileOperatorPrefixes.Any(x => DestinationAddress.StartsWith(x)))
            {
                DestinationAddress = "88" + DestinationAddress;
                return true;
            }

            // check if the phone number contains ISD code
            // if (DestinationAddress.StartsWith("+")) { return true; }
            if (DestinationAddress.StartsWith("88")) return true;
        }

        return false;
    }

    public void UpdateFrom(InvoiceContactDetailsFullDto invoice)
    {
        DestinationAddress = invoice.PhoneNumber.Trim();
        InvoiceId = invoice.InvoiceId;
    }

    public void SaveSmsFile(string folder)
    {
        var path = Path.Combine(folder, SmsFilename);
        var sms = new QueuedSmsMessage
        {
            CreatedOn = DateTime.Now,
            SendOn = DateTime.Now.AddMinutes(DelayMinutes),
            ExpireOn = DateTime.Now.AddHours(ExpiryHours),
            RetryCount = 0,
            InvoiceId = InvoiceId,
            MessageContent = MessageContent,
            PhoneNumber = DestinationAddress
        };
        var message = sms.ToJson();

        if (File.Exists(path)) File.Delete(path);

        File.WriteAllText(path, message);
    }
}