﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: BulkSmsContentComposer.cs 1481 2014-10-28 05:01:14Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using LabMaestro.BulkMessageTransport.Core;
using LabMaestro.ServiceDAL;
using LabMaestro.ServiceDAL.Model;
using LabMaestro.Shared;
using NLog;
using SmartFormat;

namespace LabMaestro.BulkMessageTransport.Sms;

public class BulkSmsContentComposer : IBulkMessageContentComposer
{
    private const string TEMPLATE_DUE =
        @"Your lab order {0.OrderId} ({0.InvoiceId}) is ready for delivery.You have Tk {0.DueAmount} due.Please collect your reports from 9AM-10PM.For online reports visit CHEVRONLAB.COM";

    private const string TEMPLATE_PAID =
        @"Your lab order {0.OrderId} ({0.InvoiceId}) is ready for delivery. Please collect your test reports between 9AM & 10PM. Visit CHEVRONLAB.COM to download reports online";

    private readonly Logger _logger = LogManager.GetCurrentClassLogger();

    public IBulkMessage ComposeMessage(InvoiceContactDetailsFullDto invoice, ResultBundleDetailsDto bundle, Guid jobId)
    {
        if (invoice == null) return null;
        if (invoice.IsCancelled) return null;
        //if ((WorkflowStageType) invoice.WorkflowStage != WorkflowStageType.ReportCollation) return null;

        _logger.Info(fmtLogMessage(jobId, "Composing SMS"));

        var message = new BulkSmsMessage();
        message.UpdateFrom(invoice);

        var info = new SmsInvoiceInfo(invoice);
        renderMessageContent(message, info, invoice.DueAmount <= 0m);
        message.SmsFilename = generateFilename(invoice);
        _logger.Info(fmtLogMessage(jobId,
            string.Format("SMS composed. Size: {0}",
                SharedUtilities.BytesToString(message.MessageContent.Length))));
        return message;
    }

    private static void renderMessageContent(BulkSmsMessage message, SmsInvoiceInfo invoice, bool paidInFull)
    {
        string tplDue, tplPaid;
        using (var dal = DataAccessFactory.GetClient())
        {
            tplPaid = dal.GlobalSettingsGetString(GlobalSettingKeys.EHealth.SmsOrderDispatchPaid);
            tplDue = dal.GlobalSettingsGetString(GlobalSettingKeys.EHealth.SmsOrderDispatchDue);
        }

        if (string.IsNullOrEmpty(tplPaid)) tplPaid = TEMPLATE_PAID;
        if (string.IsNullOrEmpty(tplDue)) tplDue = TEMPLATE_DUE;

        var template = paidInFull ? tplPaid : tplDue;
        var smsContent = Smart.Format(template, invoice);
        message.MessageContent = smsContent;
    }

    private string fmtLogMessage(Guid jobId, string message) => $"[{jobId}] {message}";

    private string generateFilename(InvoiceContactDetailsFullDto invoice) =>
        $"{DateTime.Now:yyMMddHHmmss}_{invoice.InvoiceId}_{invoice.OrderId}.sms";
}

public sealed class SmsInvoiceInfo(InvoiceContactDetailsFullDto src)
{
    public string InvoiceId { get; private set; } = src.InvoiceId.ToString();
    public string OrderId { get; private set; } = src.OrderId;
    public string OrderDateTime { get; private set; } = SharedUtilities.DateToStringOnlySlash(src.OrderDateTime);
    public string PatientName { get; private set; } = src.PatientName;
    public string ReferrerName { get; private set; } = src.ReferrerName;
    public string Age { get; private set; } = src.Age;
    public string Sex { get; private set; } = SharedUtilities.SexToString((SexType)src.Sex);

    public string GrossPayable { get; private set; } =
        SharedUtilities.MoneyToStringPlainCultureNonZero(src.GrossPayable).Trim();

    public string NetPayable { get; private set; } =
        SharedUtilities.MoneyToStringPlainCultureNonZero(src.NetPayable).Trim();

    public string PaidAmount { get; private set; } =
        SharedUtilities.MoneyToStringPlainCultureNonZero(src.PaidAmount).Trim();

    public string DueAmount { get; private set; } =
        SharedUtilities.MoneyToStringPlainCultureNonZero(src.DueAmount).Trim();

    public string DiscountAmount { get; private set; } =
        SharedUtilities.MoneyToStringPlainCultureNonZero(src.DiscountAmount).Trim();

    public string RefundAmount { get; private set; } =
        SharedUtilities.MoneyToStringPlainCultureNonZero(src.RefundAmount).Trim();

    public bool IsCancelled { get; private set; } = src.IsCancelled;
    public string PhoneNumber { get; private set; } = src.PhoneNumber;
    public string EmailAddress { get; private set; } = src.EmailAddress;
    public bool EmailTestResults { get; private set; } = src.EmailTestResults;
    public string WebAccessToken { get; private set; } = src.WebAccessToken;
    public bool IsExternalSubOrder { get; private set; } = src.IsExternalSubOrder;

    public string WorkflowStage { get; private set; } =
        SharedUtilities.WorkflowStageToHumanString((WorkflowStageType)src.WorkflowStage);

    public bool ReleaseResult { get; private set; } =
        src.ResultsReleaseFlag >= (byte)ResultsReleaseFlagType.OverrideBalanceVerification;

    public string CustomerId { get; private set; } = src.CustomerId.ToString();
    public string CorporateClientId { get; private set; } = src.CorporateClientId.ToString();
    public string AssociateLabId { get; private set; } = src.AssociateLabId.ToString();
    public string AffiliateId { get; private set; } = src.AffiliateId.ToString();
}