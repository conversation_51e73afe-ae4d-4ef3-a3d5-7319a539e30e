﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: BulkSmsTransportAgent.cs 1463 2014-10-15 14:40:56Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using LabMaestro.BulkMessageTransport.Core;
using LabMaestro.ServiceDAL.Model;
using NLog;

namespace LabMaestro.BulkMessageTransport.Sms;

public class BulkSmsTransportAgent : IBulkMessageTransportAgent
{
    private const string DEFAULT_PICKUP_FOLDER = @"C:\LabMaestro.BulkSms\pickup";
    private readonly Logger _logger = LogManager.GetCurrentClassLogger();
    public string PickupFolder { get; set; } = DEFAULT_PICKUP_FOLDER;
    public int DelayMinutes { get; set; }
    public int ExpiryHours { get; set; }
    public Guid JobId { get; } = Guid.NewGuid();
    public IBulkMessageContentComposer MessageComposer { get; } = new BulkSmsContentComposer();

    public bool DeliverMessage(IBulkMessage message)
    {
        if (message is not BulkSmsMessage sms)
        {
            _logger.Error(fmtLogMessage("Null or invalid SMS message provided"));
            return false;
        }

        if (!sms.HasValidDestination())
        {
            _logger.Error(fmtLogMessage("Bulk message has no valid destination"));
            return false;
        }

        sms.DeliveryStatus = BulkMessageDeliveryStatus.Sending;
        sms.DelayMinutes = DelayMinutes;
        sms.ExpiryHours = ExpiryHours;
        try
        {
            _logger.Info(fmtLogMessage("Submitting SMS to pickup folder..."));
            sms.SaveSmsFile(PickupFolder);
            sms.DeliveryStatus = BulkMessageDeliveryStatus.Sent;
            _logger.Info(fmtLogMessage("SMS submitted"));
        }
        catch (Exception e)
        {
            _logger.Error(fmtLogMessage("Error submitting sms"), e);
            sms.DeliveryStatus = BulkMessageDeliveryStatus.Failed;
        }

        return true;
    }

    public bool DeliverMessage(InvoiceContactDetailsFullDto invoice, ResultBundleDetailsDto bundle)
    {
        var message = MessageComposer.ComposeMessage(invoice, bundle, JobId);
        return DeliverMessage(message);
    }

    private string fmtLogMessage(string message) => $"[{JobId}] {message}";
}