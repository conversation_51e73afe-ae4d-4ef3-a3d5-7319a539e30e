﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: OrderedTestsRepository.cs 1260 2014-05-19 10:24:50Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using CuttingEdge.Conditions;
using LabMaestro.Shared;
using Manager = LabMaestro.Domain.DomainManager;

namespace LabMaestro.Domain;

public sealed class OrderedTestsRepository : BaseRepository<OrderedTest>
{
    public static OrderedTest OrderNewTest(PatientLabOrder order, short testId)
    {
        Condition.Requires(testId).IsGreaterThan(0);

        var test = LabTestsRepository.FindActiveTestById(testId);
        return OrderNewTest(order, test);
    }

    public static OrderedTest OrderNewTest(PatientLabOrder order, LabTest test)
    {
        GuardAccess(false);
        Condition.Requires(order).IsNotNull().Evaluate(x => x.IsCancelled == false);
        Condition.Requires(test).IsNotNull().Evaluate(x => x.IsActive);

        var wfStageNext = test.ReportingLab != null &&
                          test.ReportingLab.PostOrderEntryWorkflowStage != null
            ? (WorkflowStageType)test.ReportingLab.PostOrderEntryWorkflowStage
            : WorkflowStageType.OrderEntry;

        var result = new OrderedTest
        {
            PatientLabOrder = order,
            LabTest = test,
            UnitPrice = test.ListPrice,
            WorkflowStage = (byte)wfStageNext,
            IsCancelled = false
        };
        DomainManager.EntityManager.AddEntity(result);
        return result;
    }

    public static List<InvoiceOrderedTest> GetActiveOrderedTestsInInvoice(long invoiceId)
    {
        GuardAccess();
        Condition.Requires(invoiceId).IsGreaterThan(0);

        var query =
            DomainManager.EntityManager.SP_GetActiveOrderedTestsInInvoice(invoiceId);
        return query.ToList();
    }

    public static List<InvoiceOrderedTest> GetAllOrderedTestsInInvoice(long invoiceId)
    {
        GuardAccess();
        Condition.Requires(invoiceId).IsGreaterThan(0);

        var query = DomainManager.EntityManager.SP_GetAllOrderedTestsInInvoice(invoiceId);
        return query.ToList();
    }

    public static int GetActiveOrderedTestsCountInInvoice(long invoiceId)
    {
        GuardAccess();
        Condition.Requires(invoiceId).IsGreaterThan(0);

        return (int)DomainManager.EntityManager.SP_GetActiveOrderedTestsCountInInvoice(invoiceId).Single();
    }

    public static void UpdateTestWithResultBundle(long testId, long bundleId)
    {
        GuardAccess(false);
        Condition.Requires(bundleId).IsGreaterThan(0);
        Condition.Requires(testId).IsGreaterThan(0);

        DomainManager.EntityManager.SP_UpdateTestWithResultBundle(testId, bundleId);
    }

    public static void UpdateTestWorkflowStage(long testId, WorkflowStageType wfStage)
    {
        GuardAccess(false);
        Condition.Requires(testId).IsGreaterThan(0);

        DomainManager.EntityManager.SP_UpdateTestWorkflowStage(testId, (byte)wfStage);
    }

    public static List<ActiveTestsInResultBundle> GetActiveTestsInResultBundle(long bundleId)
    {
        Condition.Requires(bundleId).IsGreaterThan(0);
        var query =
            Manager.EntityManager.SP_GetActiveTestsInResultBundle(bundleId);
        return query == null ? null : query.ToList();
    }

    public static OrderedTest FindById(long testId)
    {
        Condition.Requires(testId).IsGreaterThan(0);
        return NotNullo(DomainManager.EntityManager.OrderedTests.SingleOrDefault(x => x.Id == testId));
    }

    public static OrderableTestsSlice GetOrderableTestDetails(short testId)
    {
        Condition.Requires(testId).IsGreaterThan(0);

        return DomainManager.EntityManager.SP_GetOrderableTestDetails(testId).SingleOrDefault();
    }

    public static OrderableTestsSlice GetTestDetailsForOrderedTest(long ordTestId)
    {
        Condition.Requires(ordTestId).IsGreaterThan(0);

        return DomainManager.EntityManager.SP_GetTestDetailsForOrderedTest(ordTestId).SingleOrDefault();
    }

    public static void CancelOrderedTest(long invoiceId, long ordTestId, WorkflowStageType wfStage)
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);
        Condition.Requires(ordTestId).IsGreaterThan(0);

        DomainManager.EntityManager.SP_CancelOrderedTest(invoiceId, ordTestId, (byte)wfStage);
    }

    public static long CreateNewOrderedTest(long invoiceId, short testId, decimal price,
        WorkflowStageType wfStage, DateTime eta)
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);
        Condition.Requires(testId).IsGreaterThan(0);
        Condition.Requires(price).IsGreaterThan(0);
        long? newId = -1;
        DomainManager.EntityManager.SP_CreateNewOrderedTest(invoiceId, testId, price, (byte)wfStage, eta, ref newId);
        return (long)newId;
    }

    public static bool CheckOrderedTestWorkflowStageIsModified(long ordTestId)
    {
        Condition.Requires(ordTestId).IsGreaterThan(0);

        // TODO: FIX the sproc - should return Current WF & Post-O/E WF
        var result = DomainManager.EntityManager
            .SP_CheckOrderedTestWorkflowStageIsModified(ordTestId)
            .SingleOrDefault();
        if (result.HasValue) return result.Value == 1;

        return false;
    }

    public static List<string> GetOrderedTestNamesByPerformingLab(long invoiceId, short labId)
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);
        Condition.Requires(labId).IsGreaterThan(0);

        return DomainManager.EntityManager
            .SP_GetOrderedTestNamesByPerformingLab(invoiceId, labId)
            .ToList();
    }

    public static List<short?> GetReferralEligibleOrderedTestIds(long invoiceId)
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);

        return DomainManager.EntityManager
            .SP_GetReferralEligibleOrderedTests(invoiceId)
            .ToList();
    }

    public static List<OrderedTestInDateRangeSlice> SearchOrderedTestsInDateRange(DateTime dtStart, DateTime dtEnd)
    {
        Condition.Requires(dtStart).IsLessThan(dtEnd);
        dtStart = dtStart.Date;
        dtEnd = SharedUtilities.LastMinuteOfDay(dtEnd);

        return DomainManager.EntityManager
            .SP_SearchOrderedTestsInDateRange(dtStart, dtEnd)
            .ToList();
    }

    public static List<OrderedTestByDateRangeSlice> SearchOrderedTestsByDateRange(DateTime dtStart, DateTime dtEnd)
    {
        Condition.Requires(dtStart).IsLessThan(dtEnd);
        dtStart = dtStart.Date;
        dtEnd = SharedUtilities.LastMinuteOfDay(dtEnd);

        return DomainManager.EntityManager
            .SP_SearchOrderedTestsByDateRange(dtStart, dtEnd)
            .ToList();
    }

    public static List<OrderedTestInDateRangeSlice> SearchOrderedTestsForLabInDateRange(short labId,
        DateTime dtStart,
        DateTime dtEnd)
    {
        Condition.Requires(labId).IsGreaterThan(0);
        Condition.Requires(dtStart).IsLessThan(dtEnd);

        dtStart = dtStart.Date;
        dtEnd = SharedUtilities.LastMinuteOfDay(dtEnd);

        return DomainManager.EntityManager
            .SP_SearchOrderedTestsForLabInDateRange(labId, dtStart, dtEnd)
            .ToList();
    }

    public static List<OrderedTestDetailSlice> GetAllOrderedTestDetailsInResultBundle(long bundleId)
    {
        Condition.Requires(bundleId).IsGreaterThan(0);

        return DomainManager.EntityManager.SP_GetAllOrderedTestDetailsInResultBundle(bundleId).ToList();
    }

    public static List<DiscreteOrderedTestsInResultBundleSlice> GetDiscreteOrderedTestsInResultBundle(long bundleId)
    {
        Condition.Requires(bundleId).IsGreaterThan(0);

        return DomainManager.EntityManager.SP_GetDiscreteOrderedTestsInResultBundle(bundleId).ToList();
    }

    public static List<OrderedTestDetailSlice> GetAllOrderedTestDetailsInInvoice(long invoiceId)
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);

        return DomainManager.EntityManager.SP_GetAllOrderedTestDetailsInInvoice(invoiceId).ToList();
    }
}