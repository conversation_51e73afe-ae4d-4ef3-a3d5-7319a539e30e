﻿using System.Collections.Generic;
using C1.LiveLinq.Collections;
using LabMaestro.Shared;

namespace LabMaestro.Domain;

public abstract class CachedRepositoryBase<T>
{
    protected delegate IEnumerable<T> OnFetchFromServer();

    private static IndexedCollection<T> FetchFromServer(OnFetchFromServer callback, string cacheKey)
    {
        var catalog = new IndexedCollection<T>();
        catalog.BeginUpdate();
        catalog.AddRange(callback());
        catalog.EndUpdate();

        AppCache.Store(cacheKey, catalog, 30);
        return catalog;
    }

    protected static IndexedCollection<T> LoadCatalog(string cacheKey, bool disableCache, OnFetchFromServer callback)
    {
        IndexedCollection<T>? catalog = null;
        if (disableCache)
            AppCache.Remove(cacheKey);
        else
            catalog = (IndexedCollection<T>)AppCache.Get(cacheKey);

        if (catalog == null || catalog.Count == 0)
            catalog = FetchFromServer(callback, cacheKey);

        return catalog;
    }
}