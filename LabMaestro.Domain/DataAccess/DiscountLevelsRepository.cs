﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2012, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: DiscountLevelsRepository.cs 718 2013-07-02 14:48:27Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System.Collections.Generic;
using System.Linq;
using CuttingEdge.Conditions;
using Manager = LabMaestro.Domain.DomainManager;

namespace LabMaestro.Domain;

public sealed class DiscountLevelsRepository : BaseRepository<DiscountLevel>
{
    public static List<DiscountLevel> FetchAll()
    {
        return Manager.EntityManager.DiscountLevels.ToList();
    }

    public static List<DiscountLevelSlice> FetchAllSlices()
    {
        return Manager.EntityManager.SP_GetDiscountLevels().ToList();
    }

    public static DiscountLevel FindById(short id)
    {
        Condition.Requires(id).IsGreaterThan(0);
        return NotNullo(Manager.EntityManager.DiscountLevels.SingleOrDefault(x => x.Id == id));
    }

    public static void UpdateDiscountLevel(DiscountLevelSlice slice)
    {
        Condition.Requires(slice).IsNotNull();
        UpdateDiscountLevel(slice.Id, slice.Name, slice.DiscountMode,
            slice.DiscountPercent, slice.DiscountAmount);
    }

    public static void UpdateDiscountLevel(short id, string name, byte mode, double percent, decimal amount)
    {
        Condition.Requires(id).IsGreaterThan(0);
        Condition.Requires(name).IsNotNullOrEmpty();
        Condition.Requires(percent).IsGreaterOrEqual(0).IsLessOrEqual(100);
        Condition.Requires(amount).IsGreaterOrEqual(0);

        Manager.EntityManager.SP_UpdateDiscountLevel(id, name, mode, percent, amount);
    }

    public static DiscountLevel CreateNewLevel(DiscountLevelSlice slice)
    {
        Condition.Requires(slice).IsNotNull();
        var level = DiscountLevel.AssembleFrom(slice);
        Add(level);
        return level;
    }

    public static DiscountLevel CreateNewLevel(string name, byte discountMode, decimal amount, double percent)
    {
        Condition.Requires(name).IsNotNullOrEmpty();
        Condition.Requires(amount).IsGreaterOrEqual(0m);
        Condition.Requires(percent).IsGreaterOrEqual(0).IsLessOrEqual(100);

        var level = new DiscountLevel
        {
            Name = name,
            DiscountMode = discountMode,
            DiscountPercent = percent,
            DiscountAmount = amount
        };
        Add(level);
        return level;
    }
}