﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ReferrersRepository.cs 1440 2014-10-03 04:25:17Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System.Collections.Generic;
using System.Linq;
using CuttingEdge.Conditions;

namespace LabMaestro.Domain;

public class ReferrersRepository : BaseRepository<Referrer>
{
    public static List<Referrer> FindAllActivePhysicians()
    {
        GuardAccess();
        return DomainManager.EntityManager.Referrers.Where(p => p.IsActive).ToList();
    }

    public static List<Referrer> FindAllReferrers()
    {
        GuardAccess();
        return DomainManager.EntityManager.Referrers.ToList();
    }

    public static Referrer? FindById(int id)
    {
        GuardAccess();
        Condition.Requires(id).IsGreaterThan(0);

        var query = DomainManager.EntityManager.Referrers.SingleOrDefault(u => u.Id == id);
        return query != null && !query.EntityAspect.IsNullEntity ? query : null;
    }

    public static int CreateNewReferrer(string name, string webId)
    {
        int? id = 0;
        DomainManager.EntityManager.SP_CreateNewReferrer(name, true, true, webId, ref id);
        return id ?? 0;
    }

    public static void UpdateReferrer(int id, bool active, string prefix, string name, string suffix, string tag,
        string mobile, string email, bool webActive, string webid, string webPass, short? updUid, short? execId,
        short? refClass, bool suppress)
    {
        DomainManager.EntityManager.SP_UpdateReferrer(id, active, prefix, name, suffix, tag, mobile, email,
            webActive, webid, webPass, updUid, execId, refClass, suppress);
    }

    public static string? GetReferrerFullName(int id)
    {
        GuardAccess();
        Condition.Requires(id).IsGreaterThan(0);

        var query = DomainManager.EntityManager.Referrers.SingleOrDefault(r => r.Id == id);
        return query != null && !query.EntityAspect.IsNullEntity ? query.FullName : null;
    }

    public static string? GetReferrerName(int id)
    {
        GuardAccess();
        Condition.Requires(id).IsGreaterThan(0);

        var query = DomainManager.EntityManager.Referrers.SingleOrDefault(r => r.Id == id);
        return query != null && !query.EntityAspect.IsNullEntity ? query.Name : null;
    }

    public static List<ReferrerSlice> GetAllReferrersInCategory(short catId)
    {
        Condition.Requires(catId).IsGreaterThan(0);

        return DomainManager.EntityManager.SP_GetAllReferrersInCategory(catId).ToList();
    }

    public static List<ReferrerSlice> GetAllReferrersInReferralClass(short classId)
    {
        Condition.Requires(classId).IsGreaterThan(0);

        return DomainManager.EntityManager.SP_GetAllReferrersInReferralClass(classId).ToList();
    }

    public static List<ReferrerCategoryLink> GetCategoryLinksForReferrer(int refId)
    {
        Condition.Requires(refId).IsGreaterThan(0);

        return DomainManager.EntityManager
            .ReferrerCategoryLinks
            .Where(x => x.ReferrerId == refId)
            .ToList();
    }

    public static ReferrerCategoryLink CreateReferrerCategoryLink(int refId,
        short categoryId,
        bool autoSave = true)
    {
        Condition.Requires(refId).IsGreaterThan(0);
        Condition.Requires(categoryId).IsGreaterThan(0);

        var link = new ReferrerCategoryLink { ReferrerId = refId, CatergoryId = categoryId };
        DomainManager.EntityManager.AddEntity(link);
        if (autoSave) DomainManager.EntityManager.SaveChanges();
        return link;
    }

    public static void RemoveReferrerCategoryLink(int refId, int linkId)
    {
        Condition.Requires(refId).IsGreaterThan(0);
        Condition.Requires(linkId).IsGreaterThan(0);

        DomainManager.EntityManager.SaveChanges();
        DomainManager.EntityManager.SP_RemoveReferrerCategoryLink(linkId, refId);
        DomainManager.EntityManager.Clear();
    }

    public static ReferrerCategoryLink GetReferrerCategoryLink(int refId,
        short categoryId)
    {
        Condition.Requires(refId).IsGreaterThan(0);
        Condition.Requires(categoryId).IsGreaterThan(0);

        return DomainManager.EntityManager
            .ReferrerCategoryLinks
            .SingleOrDefault(x => x.CatergoryId == categoryId &&
                                  x.ReferrerId == refId);
    }

    public static List<ReferrerAddress> GetAddressesForReferrer(int refId)
    {
        Condition.Requires(refId).IsGreaterThan(0);
        return DomainManager.EntityManager
            .ReferrerAddresses
            .Where(x => x.ReferrerId == refId)
            .ToList();
    }

    public static List<ReferrerPhoneNumber> GetPhoneNumbersForReferrer(int refId)
    {
        Condition.Requires(refId).IsGreaterThan(0);

        return DomainManager.EntityManager
            .ReferrerPhoneNumbers
            .Where(x => x.ReferrerId == refId)
            .ToList();
    }

    public static ReferrerAddress AddNewAddress(int refId, string address, bool isMail,
        byte addressType, string notes)
    {
        Condition.Requires(refId).IsGreaterThan(0);

        var item = new ReferrerAddress
        {
            ReferrerId = refId,
            Address = address,
            Notes = notes,
            AddressType = addressType,
            IsMailingAddress = isMail
        };

        DomainManager.EntityManager.AddEntity(item);
        DomainManager.EntityManager.SaveChanges();
        return item;
    }

    public static ReferrerPhoneNumber AddNewPhoneNumber(int refId, string phone,
        byte phoneType, string notes)
    {
        Condition.Requires(refId).IsGreaterThan(0);

        var item = new ReferrerPhoneNumber
        {
            ReferrerId = refId,
            PhoneNumber = phone,
            PhoneNumberType = phoneType
        };

        DomainManager.EntityManager.AddEntity(item);
        DomainManager.EntityManager.SaveChanges();
        return item;
    }

    public static bool WebLoginIdIsUnique(string loginId)
    {
        var count = (int)DomainManager.EntityManager.SP_GetWebLoginIdCount(loginId).FirstOrDefault();
        return count == 0;
    }

    public static List<EligibleReferrerSlice> GetEligibleReferrersInCategory(short catId)
    {
        Condition.Requires(catId).IsGreaterThan(0);

        return DomainManager.EntityManager
            .SP_GetEligibleReferrersInCategory(catId)
            .ToList();
    }

    public static List<EligibleReferrerSlice> GetAllEligibleReferrers()
    {
        return DomainManager.EntityManager
            .SP_GetAllEligibleReferrers()
            .ToList();
    }
}