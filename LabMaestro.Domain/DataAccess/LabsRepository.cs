﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: LabsRepository.cs 960 2013-09-30 14:08:44Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System.Collections.Generic;
using System.Linq;
using CuttingEdge.Conditions;
using IdeaBlade.EntityModel;
using LabMaestro.Shared;

namespace LabMaestro.Domain;

public sealed class LabsRepository : BaseRepository<Lab>
{
    private const int CACHE_LIFESPAN = 30;

    private const string LabPrefix = "12D0F2B8_labname__";

    public static Lab CreateNew(string name, string code)
    {
        GuardAccess(false);
        var lab = new Lab { IsActive = true, Name = name, LabCode = code };
        DomainManager.EntityManager.AddEntity(lab);
        return lab;
    }

    public static IEnumerable<Lab> FindActiveLabs()
    {
        GuardAccess();
        return DomainManager.EntityManager.Labs.Where(l => l.IsActive).ToList();
    }

    public static IEnumerable<Lab> FindAllLabs()
    {
        GuardAccess();
        return DomainManager.EntityManager.Labs.ToList();
    }

    public static Lab FindById(short labid)
    {
        GuardAccess();

        Condition.Requires(labid).IsGreaterThan(0);
        var query = DomainManager.EntityManager.Labs.Where(u => u.Id == labid).FirstOrNullEntity();
        return query.EntityAspect.IsNullEntity ? null : query;
    }

    public static List<LabSlice> GetAllLabSlices()
    {
        GuardAccess();

        return DomainManager.EntityManager.SP_GetLabs().ToList();
    }

    public static List<LabSlice> GetAllActiveLabSlices()
    {
        GuardAccess();

        return DomainManager.EntityManager.SP_GetActiveLabs().ToList();
    }

    public static void UpdateLab(short labId, bool isActive, string code, string name, string printName,
        bool printCanon, byte resType, bool isAux, byte? postOE, byte? postRE, byte? postRV,
        byte? postRF, byte? postRC, short? refId, string refGrpName, string accGrpName,
        short? lvlId, int? defaultReportHeader)
    {
        GuardAccess(false);

        DomainManager.EntityManager.SP_UpdateLab(labId, isActive, code, name, printName, printCanon, resType, isAux,
            postOE, postRE, postRV, postRF, postRC, refId, refGrpName, accGrpName,
            lvlId, defaultReportHeader);
    }

    public static void CreateNewLabFromSlice(LabSlice slice)
    {
        DomainManager.EntityManager.SP_CreateNewLab(slice.IsActive,
            slice.LabCode,
            slice.Name,
            slice.ReqPrintName,
            slice.ReqPrintCanonicalTestName,
            slice.TestResultType,
            slice.IsAuxProcedure,
            slice.PostOrderEntryWorkflowStage,
            slice.PostResultEntryWorkflowStage,
            slice.PostResultVerificationWorkflowStage,
            slice.PostResultFinalizationWorkflowStage,
            slice.PostReportCollationWorkflowStage,
            slice.ReferralGroupId,
            slice.DiscountLevelId,
            slice.DefaultReportHeaderId);

        //var lab = Lab.AssembleFrom(slice);
        //Add(lab);
        //return lab;
    }

    public static void PreCacheLabNames()
    {
        foreach (var lab in DomainManager.EntityManager.Labs.ToList())
            AppCache.Store(cacheKey(lab.Id), lab.Name, CACHE_LIFESPAN);
    }

    private static string cacheKey(short labId) => $"{LabPrefix}{labId}";

    public static string GetLabName(short labId)
    {
        Condition.Requires(labId).IsGreaterThan(0);
        var key = cacheKey(labId);
        var name = (string)AppCache.Get(key);
        if (string.IsNullOrEmpty(name))
        {
            name = DomainManager.EntityManager.SP_GetLabName(labId).FirstOrDefault();
            if (string.IsNullOrEmpty(name)) AppCache.Store(key, name, CACHE_LIFESPAN);
        }

        return name;
    }
}