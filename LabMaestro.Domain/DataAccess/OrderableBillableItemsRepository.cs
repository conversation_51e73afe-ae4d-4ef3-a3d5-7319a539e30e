﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: OrderableBillableItemsRepository.cs 718 2013-07-02 14:48:27Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System.Collections.Generic;
using System.Linq;
using C1.LiveLinq.Collections;
using LabMaestro.Shared;

namespace LabMaestro.Domain;

public sealed class OrderableBillableItemsRepository
{
    private static string cacheKey() => "$orderable_billable_item_";

    public static List<BillableItemSlice> GetAllItems(bool nocache = false) => getCatalog(nocache).ToList();

    private static IndexedCollection<BillableItemSlice> getCatalog(bool nocache)
    {
        IndexedCollection<BillableItemSlice> catalog = null;
        if (nocache)
            // clear the cached item since we're refreshing it anyway
            AppCache.Remove(cacheKey());
        else
            catalog = (IndexedCollection<BillableItemSlice>)AppCache.Get(cacheKey());

        if (catalog == null || catalog.Count == 0)
            catalog = fetchFromServer();

        return catalog;
    }

    private static IndexedCollection<BillableItemSlice> fetchFromServer()
    {
        var query = DomainManager.EntityManager.SP_GetAllOrderableBillableItems().ToList();
        var list = new List<BillableItemSlice>();
        list.AddRange(query.Select(BillableItemSlice.AssembleFrom));

        var catalog = new IndexedCollection<BillableItemSlice>();
        catalog.BeginUpdate();
        try
        {
            catalog.AddRange(list);
        }
        finally
        {
            catalog.EndUpdate();
        }

        AppCache.Store(cacheKey(), catalog, 30);
        return catalog;
    }

    public static BillableItemSlice FindById(short id)
    {
        var query = getCatalog(false).SingleOrDefault(x => x.Id == id);
        return query != null ? BillableItemSlice.AssembleFrom(query) : null;
    }
}