﻿using System.Collections.Generic;
using System.Linq;

namespace LabMaestro.Domain;

using Manager = DomainManager;

public sealed class AssociateOrganizationRepository : BaseRepository<AssociateOrganization>
{
    public static IEnumerable<AssociateOrganizationSlice> FetchActive() =>
        Manager.EntityManager.SP_GetActiveAssociateOrganizations().ToList().OrderBy(x => x.Name);

    public static IEnumerable<AssociateOrganizationSlice> FetchAll() =>
        Manager.EntityManager.SP_GetActiveAssociateOrganizations().ToList().OrderBy(x => x.Name);

    public static AssociateOrganization? Find(short id) =>
        Manager.EntityManager.AssociateOrganizations.FirstOrDefault(c => c.Id == id);

    public static string? GetName(short id) =>
        Manager.EntityManager.AssociateOrganizations.FirstOrDefault(c => c.Id == id)?.Name;
}