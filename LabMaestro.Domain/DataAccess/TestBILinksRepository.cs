﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: TestBILinksRepository.cs 1248 2014-03-30 12:41:12Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System.Collections.Generic;
using System.Linq;
using C1.LiveLinq;
using CuttingEdge.Conditions;
using IdeaBlade.EntityModel;
using Manager = LabMaestro.Domain.DomainManager;

namespace LabMaestro.Domain;

public sealed class TestBILinksRepository : IndexedBaseRepository<TestBILink>
{
    public static void Refresh()
    {
        //GuardAccess();
        RefreshCache(fetchDataFromServer);
    }

    private static List<TestBILink> fetchDataFromServer()
    {
        return Manager.EntityManager.TestBILinks.Include(TestBILink.PathFor(x => x.BillableItem))
            .Include(TestBILink.PathFor(x => x.LabTest)).ToList();
    }

    public static List<TestBILink> FindAll()
    {
        //GuardAccess();
        ValidateCache(false, fetchDataFromServer);

        return indexedList.ToList();
    }

    public static TestBILink FindById(int id)
    {
        //GuardAccess();
        Condition.Requires(id).IsGreaterThan(0);
        ValidateCache(false, fetchDataFromServer);

        return indexedList.AsIndexed().SingleOrDefault(u => u.Id == id);
    }

    public static List<TestBILink> FindAllBILinksForTest(short testId)
    {
        //GuardAccess();
        Condition.Requires(testId).IsGreaterThan(0);
        return indexedList.AsIndexed().Where(x => x.TestId == testId).ToList();
    }

    public static void CreateNewTestBILink(short testId, short biId, byte optLevel, short qty)
    {
        Condition.Requires(testId).IsGreaterThan(0);
        Condition.Requires(biId).IsGreaterThan(0);
        Condition.Requires(qty).IsGreaterThan(0);

        //GuardAccess(false);
        Manager.EntityManager.SP_CreateNewTestBILink(testId, biId, optLevel, qty);
        Invalidate();
    }

    public static void UpdateTestBILink(int id, short testId, short biId, byte optLevel, short qty)
    {
        Condition.Requires(id).IsGreaterThan(0);
        Condition.Requires(testId).IsGreaterThan(0);
        Condition.Requires(biId).IsGreaterThan(0);
        Condition.Requires(qty).IsGreaterThan(0);

        //GuardAccess(false);
        Manager.EntityManager.SP_UpdateTestBILink(id, testId, biId, optLevel, qty);
        Invalidate();
    }

    public static void Delete(int id)
    {
        Condition.Requires(id).IsGreaterThan(0);
        //GuardAccess(false);

        var item = indexedList.AsIndexed().SingleOrDefault(x => x.Id == id);
        if (item != null) Delete(item);
        Invalidate();
    }
}