﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: AppReportTemplatesRepository.cs 1067 2013-11-01 03:40:06Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System.Collections.Generic;
using System.Linq;
using CuttingEdge.Conditions;
using LabMaestro.Shared;
using Manager = LabMaestro.Domain.DomainManager;

namespace LabMaestro.Domain;

public sealed class AppReportTemplatesRepository : BaseRepository<AppReportTemplate>
{
    private const int CACHE_LIFESPAN = 30;

    private static string cacheKey(string code) => "45487_app_rpt_tpl_" + code;

    // TODO: save/load from database

    private static string resolveReportCode(string reportCode) =>
        GlobalSettingsRepository.GetStringValue(reportCode);

    private static void resetCache() => AppCache.Reset();

    public static byte[] GetAppTemplateReport(string reportCode)
    {
        Condition.Requires(reportCode).IsNotNullOrEmpty();
        byte[] result = null;
        var key = cacheKey(reportCode);
        if (AppCache.Contains(key)) result = AppCache.Get(key) as byte[];

        if (result == null || result.Length <= 0)
        {
            var currentTemplateId = resolveReportCode(reportCode);
            var templateContent = new byte[] { };
            Manager.EntityManager.SP_GetAppTemplateReportContent(currentTemplateId, ref templateContent);
            result = CompressionUtils.DecompressToBytes(templateContent);
            AppCache.Store(key, result, CACHE_LIFESPAN);
        }

        return result;
    }

    public static void UpdateAppTemplateReportCode(string oldCode, string newCode)
    {
        Condition.Requires(oldCode).IsNotNullOrEmpty();
        Condition.Requires(newCode).IsNotNull();

        var tplId = resolveReportCode(oldCode);
        var item = Manager.EntityManager.AppReportTemplates.SingleOrDefault(r => r.ReportCode == oldCode);
        if (item != null)
        {
            item.ReportCode = newCode;
            Save();
            GlobalSettingsRepository.Save(tplId, -1, newCode);
        }

        AppCache.Remove(cacheKey(oldCode));
        resetCache();
    }

    public static void UpdateAppTemplateReport(string reportCode, byte[] templateContent)
    {
        Condition.Requires(reportCode).IsNotNullOrEmpty();
        Condition.Requires(templateContent).IsNotNull();

        var currentTemplateId = resolveReportCode(reportCode);
        var lobContent = CompressionUtils.CompressBytes(templateContent);
        Manager.EntityManager.SP_UpdateAppTemplateReportContent(currentTemplateId, lobContent);

        AppCache.Remove(cacheKey(reportCode));
        resetCache();
    }

    public static void CreateNewAppReportTemplate(string reportCode, string templateId, byte[] templateContent)
    {
        Condition.Requires(reportCode).IsNotNullOrEmpty();
        Condition.Requires(templateId).IsNotNullOrEmpty();
        Condition.Requires(templateContent).IsNotNull();

        GlobalSettingsRepository.Save(reportCode, -1, templateId);
        var lobContent = CompressionUtils.CompressBytes(templateContent);
        Manager.EntityManager.SP_CreateNewAppReportTemplate(templateId, lobContent);

        AppCache.Remove(cacheKey(reportCode));
        resetCache();
    }

    public static List<AppReportTemplateSlice> FindAllAppTemplateSlices()
    {
        return Manager.EntityManager.AppReportTemplates.Select(AppReportTemplateSlice.AssembleFrom).ToList();
    }

    public static AppReportTemplateSlice FindTemplateSlice(string reportCode)
    {
        Condition.Requires(reportCode).IsNotNullOrEmpty();

        var item = new AppReportTemplateSlice { ReportCode = reportCode };
        var tplId = resolveReportCode(reportCode);
        var qry = Manager.EntityManager.AppReportTemplates.SingleOrDefault(r => r.ReportCode == tplId);
        if (qry != null)
        {
            item.TemplateId = qry.ReportCode;
            item.Id = qry.Id;
            return item;
        }

        return null;
    }
}