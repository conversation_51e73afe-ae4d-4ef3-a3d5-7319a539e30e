﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2012, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: BaseRepository.cs 1100 2013-11-27 05:48:29Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

//#define SECURE_ACCESS

using System;
using IdeaBlade.EntityModel;
using Manager = LabMaestro.Domain.DomainManager;

namespace LabMaestro.Domain;

public delegate void EntityManagerAccessGuardHook(Type entityType, bool readOnly);

public abstract class BaseRepository<T> where T : Entity
{
    public static EntityManagerAccessGuardHook EntityManagerAccessGuard { get; set; }

    public static bool HasChanges => Manager.EntityManager.HasChanges();

    protected static void GuardAccess(bool readOnly = true)
    {
#if SECURE_ACCESS
        if (EntityManagerAccessGuard != null)
            EntityManagerAccessGuard(typeof(T), readOnly);
#endif
    }

    public static void Save()
    {
        GuardAccess(false);

        Manager.EntityManager.SaveChanges();
    }

    public static void Reset()
    {
        if (Manager.EntityManager.HasChanges()) Manager.EntityManager.TrySaveChanges();

        Manager.EntityManager.Clear();
    }

    public static void Refetch(T obj, bool overWriteChanges = true)
    {
        GuardAccess();
        Manager.EntityManager.RefetchEntity(obj,
            overWriteChanges
                ? MergeStrategy.OverwriteChanges
                : MergeStrategy.PreserveChanges);
    }

    public static void RejectChanges()
    {
        GuardAccess();
        Manager.EntityManager.RejectChanges();
    }

    public static void Add(T obj)
    {
        GuardAccess(false);

        Manager.EntityManager.AddEntity(obj);
    }

    public static void Attach(T obj)
    {
        GuardAccess(false);

        Manager.EntityManager.AttachEntity(obj);
    }

    public static void Delete(T obj)
    {
        GuardAccess(false);

        if (obj.EntityAspect.IsPendingEntity)
            obj.EntityAspect.RejectChanges();
        else
            obj.EntityAspect.Delete();
    }

    protected static T NotNullo(T obj) => obj != null && obj.EntityAspect.IsNullEntity ? null : obj;
}