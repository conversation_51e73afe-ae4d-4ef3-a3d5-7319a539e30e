﻿//  * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
//  
//  Copyright (c) 2012, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
//  All rights reserved.
//  
//  Redistribution and use in source and binary forms, with or without
//  modification, are permitted provided that the following conditions are met:
//     * Redistributions of source code must retain the above copyright
//       notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above copyright
//       notice, this list of conditions and the following disclaimer in the
//       documentation and/or other materials provided with the distribution.
//     * Neither the name of the <organization> nor the
//       names of its contributors may be used to endorse or promote products
//       derived from this software without specific prior written permission.
//  
//  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
//  ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
//  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
//  DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
//  DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
//  (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
//  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
//  ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
//  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
//  SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
//  
//  $Id: HeldLabOrdersRepository.cs 1143 2014-01-30 07:36:09Z <EMAIL> $
//  
//  * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System.Collections.Generic;
using System.Linq;
using CuttingEdge.Conditions;
using IdeaBlade.EntityModel;
using Manager = LabMaestro.Domain.DomainManager;

namespace LabMaestro.Domain;

public sealed class HeldLabOrdersRepository : BaseRepository<HeldLabOrder>
{
    public static HeldLabOrder PutOrderOnHold(short userId, int shiftId, ILabOrderContextInfo order)
    {
        Condition.Requires(userId).IsGreaterThan(0);
        Condition.Requires(shiftId).IsGreaterThan(0);

        var shift = WorkShiftsRepository.FindUserShiftById(userId, shiftId);
        return PutOrderOnHold(shift, order);
    }

    public static HeldLabOrder PutOrderOnHold(WorkShift shift, ILabOrderContextInfo order)
    {
        Condition.Requires(shift).IsNotNull();
        Condition.Requires(shift.IsClosed).IsFalse();
        Condition.Requires(order).IsNotNull();

        var onHold = new HeldLabOrder
        {
            Age = order.Age,
            DoB = order.DoB,
            FirstName = order.FirstName,
            LastName = order.LastName,
            PhoneNumber = order.PhoneNumber,
            Notes = order.OrderNotes,
            ReferrerId = order.ReferrerId,
            Sex = (byte)order.Sex,
            Title = order.Title,
            IsReferrerUnknown = order.IsReferrerUnknown,
            ReferrerCustomName = order.ReferrerCustomName,
            WorkShift = shift
        };

        Manager.EntityManager.AddEntity(onHold);

        if (order.OrderedTests.Count > 0)
            foreach (var slice in order.OrderedTests)
            {
                var test = new HeldLabOrderTest
                {
                    LabTestId = slice.TestId,
                    HeldLabOrder = onHold,
                    ResultsETA = slice.DeliveryTime,
                    WorkShiftId = shift.Id
                };
                Manager.EntityManager.AddEntity(test);
            }

        return onHold;
    }

    public static IEnumerable<HeldOrderSlice> GetHeldOrdersInShift(int shiftId)
    {
        Condition.Requires(shiftId).IsGreaterThan(0);
        var query = Manager.EntityManager.SP_GetHeldOrdersInShift(shiftId);
        return query.ToList();
    }

    public static void ClearHeldOrdersInShift(int shiftId)
    {
        Condition.Requires(shiftId).IsGreaterThan(0);

        Manager.EntityManager.SP_RemoveHeldOrdersInShift(shiftId);
    }

    public static void RemoveHeldOrder(long orderId)
    {
        Condition.Requires(orderId).IsGreaterThan(0);

        Manager.EntityManager.SP_RemoveHeldOrder(orderId);
    }

    public static HeldLabOrder GetHeldLabOrder(long orderId)
    {
        Condition.Requires(orderId).IsGreaterThan(0);

        return NotNullo(Manager.EntityManager.HeldLabOrders
            .Include(HeldLabOrder.PathFor(x => x.HeldLabOrderTests))
            .SingleOrDefault(x => x.Id == orderId));
    }
}