﻿//  * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
//  
//  Copyright (c) 2012, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
//  All rights reserved.
//  
//  Redistribution and use in source and binary forms, with or without
//  modification, are permitted provided that the following conditions are met:
//     * Redistributions of source code must retain the above copyright
//       notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above copyright
//       notice, this list of conditions and the following disclaimer in the
//       documentation and/or other materials provided with the distribution.
//     * Neither the name of the <organization> nor the
//       names of its contributors may be used to endorse or promote products
//       derived from this software without specific prior written permission.
//  
//  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
//  ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
//  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
//  DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
//  DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
//  (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
//  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
//  ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
//  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
//  SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
//  
//  $Id: RecentlyUpdatedResultBundlesRepository.cs 1248 2014-03-30 12:41:12Z <EMAIL> $
//  
//  * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System.Collections.Generic;
using CuttingEdge.Conditions;
using LabMaestro.Shared;
using Manager = LabMaestro.Domain.DomainManager;

namespace LabMaestro.Domain;

public sealed class RecentlyUpdatedResultBundlesRepository : BaseRepository<RecentlyUpdatedResultBundle>
{
    public static List<RecentlyUpdatedResultBundleDetails> GetRecentlyUpdatedResultBundleDetails(
        WorkflowStageType wfMinStage,
        WorkflowStageType wfMaxStage)
    {
        return
            Manager.EntityManager.SP_GetRecentlyUpdatedResultBundleDetails((byte)wfMinStage, (byte)wfMaxStage) as
                List<RecentlyUpdatedResultBundleDetails>;
    }

    public static List<RecentlyUpdatedResultBundleDetails> GetCollatableRecentlyUpdatedResultBundleDetails()
    {
        return GetRecentlyUpdatedResultBundleDetails(WorkflowStageType.ReportFinalization,
            WorkflowStageType.ReportDispatch);
    }

    public static List<RecentlyUpdatedResultBundleDetails> GetDispatchableRecentlyUpdatedResultBundleDetails()
    {
        return GetRecentlyUpdatedResultBundleDetails(WorkflowStageType.ReportCollation,
            WorkflowStageType.OrderFulfillment);
    }

    public static void AddResultBundleToRecentUpdatesList(long bundleId, long invoiceId, WorkflowStageType wfStage,
        SortPriorityType priority)
    {
        Condition.Requires(bundleId).IsGreaterThan(0);
        Condition.Requires(invoiceId).IsGreaterThan(0);

        Manager.EntityManager.SP_UpsertResultBundleToRecentUpdatesList(bundleId, invoiceId, (byte)wfStage,
            (byte)priority);
    }

    public static void RemoveResultBundleFromRecentUpdatesList(long bundleId)
    {
        Condition.Requires(bundleId).IsGreaterThan(0);
        Manager.EntityManager.SP_RemoveResultBundleFromRecentUpdatesList(bundleId);
    }
}