﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: AppSysRepository.cs 1288 2014-05-22 10:34:41Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using CuttingEdge.Conditions;
using LabMaestro.Shared;

namespace LabMaestro.Domain;

public sealed class AppSysRepository : BaseRepository<AuditTrail>
{
    public static ServerHeartbeatResponse SendBusHeartbeatRequest()
    {
        try
        {
            return (ServerHeartbeatResponse)DomainManager
                .EntityManager
                .InvokeServerMethod(@"MBoss.ServerMethods, MBoss",
                    "SendBusHeartbeatRequest");
        }
        catch (Exception e)
        {
            // TODO: logging
        }

        return null;
    }

    public static DateTime GetServerTime()
    {
        var serverTime = DomainManager.EntityManager.SP_GetServerTime().SingleOrDefault();
        return serverTime == null ? DateTime.Now : (DateTime)serverTime;
    }

    public static bool SendResultBundlerRequest(long invoiceId)
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);
        return invokeServerMethod(invoiceId, @"SendResultBundlerRequest");
    }

    public static bool SendLabOrderWorkflowStageEstimateRequest(long invoiceId)
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);
        return invokeServerMethod(invoiceId, @"SendLabOrderWorkflowStageEstimateRequest");
    }

    public static bool SendMirrorLabOrderRequest(long invoiceId)
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);

        return invokeServerMethod(invoiceId, @"SendMirrorLabOrderRequest");
    }

    private static bool invokeServerMethod(long invoiceId, string methodName)
    {
        try
        {
            return (bool)DomainManager
                .EntityManager
                .InvokeServerMethod(@"MBoss.ServerMethods, MBoss",
                    methodName,
                    invoiceId);
        }
        catch (Exception e)
        {
            // TODO: logging
            return false;
        }
    }

    public static void UpsertWorkstationRegistry(
        int ipAddr,
        string macAddr,
        string user,
        string osVer,
        string remarks)
    {
        DomainManager.EntityManager.SP_UpsertWorkstationRegistry(
            ipAddr,
            macAddr,
            AppVersion.GetVersionString(true),
            user,
            osVer,
            remarks);
    }

    public static List<WorkstationsRegistry> GeAllWorkstationRegistries()
    {
        return DomainManager.EntityManager.WorkstationsRegistries.ToList();
    }
}