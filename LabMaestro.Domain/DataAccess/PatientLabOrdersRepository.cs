﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: PatientLabOrdersRepository.cs 1521 2014-11-26 08:07:07Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using CuttingEdge.Conditions;
using IdeaBlade.EntityModel;
using LabMaestro.Shared;

namespace LabMaestro.Domain;

public sealed class PatientLabOrdersRepository : BaseRepository<PatientLabOrder>
{
    public static PatientLabOrder CreateNew(string firstName, SexType sex, bool isReferrerUnknown)
    {
        GuardAccess(false);
        long? orderId = null;
        DomainManager.EntityManager.SP_CreateNewLabOrder(firstName, (byte)sex, isReferrerUnknown, ref orderId);

        if (orderId == null) throw new Exception("Unable to create new lab order!");

        var order = FindById((long)orderId);
        order.InvoiceMaster = new InvoiceMaster(order);

        DomainManager.EntityManager.AddEntity(order);
        //DomainManager.EntityManager.AddEntity(order.InvoiceMaster);
        return order;
    }

    public static void UpdateLabOrderDemographic(PatientLabOrder order)
    {
        UpdateLabOrderDemographic(order.InvoiceId, order.Title, order.FirstName, order.LastName, order.Sex,
            order.Age, order.DoB, order.PhoneNumber, order.OrderNotes, order.EmailAddress,
            order.EmailTestResults,
            order.ReferrerId, order.ReferrerCustomName, order.IsReferrerUnknown,
            order.DisallowReferral, order.IsExternalSubOrder, order.SubOrderTrackingId,
            order.MirrorFlag);
    }

    public static void UpdateLabOrderDemographic(long? invoiceId, string title, string fname, string lname,
        byte? sex, string age, DateTime? dob, string phone, string notes,
        string email, bool emailResults,
        int? refId, string refCustName, bool? refUnk, bool? refDisallow,
        bool? isExtOrder, string subTrackingId, byte mirror)
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);
        Condition.Requires(fname).IsNotNullOrEmpty();

        DomainManager.EntityManager.SP_UpdateLabOrderDemographic(invoiceId, title, fname, lname, sex, age, dob,
            phone, email, emailResults, refId,
            refDisallow, refUnk, refCustName, isExtOrder,
            subTrackingId, mirror, notes);
    }

    public static IEnumerable<PatientLabOrder> FindActiveOrders(DateTime dtFrom, DateTime dtEnd)
    {
        Condition.Requires(dtFrom).Evaluate(d => d.Date > DateTime.MinValue && d.Date < DateTime.MaxValue);
        Condition.Requires(dtEnd).Evaluate(d => d.Date >= dtFrom.Date /* && d.Date <= DateTime.Now.Date*/);
        GuardAccess();

        return DomainManager.EntityManager.PatientLabOrders
            .Where(l => !l.IsCancelled && l.OrderDateTime >= dtFrom && l.OrderDateTime <= dtEnd)
            .ToList();
    }

    public static IEnumerable<PatientLabOrder> FindAllOrders(DateTime dtFrom, DateTime dtEnd)
    {
        Condition.Requires(dtFrom).Evaluate(d => d.Date > DateTime.MinValue && d.Date < DateTime.MaxValue);
        Condition.Requires(dtEnd).Evaluate(d => d.Date >= dtFrom.Date /* && d.Date <= DateTime.Now.Date*/);
        GuardAccess();

        return DomainManager.EntityManager.PatientLabOrders
            .Where(l => l.OrderDateTime >= dtFrom && l.OrderDateTime <= dtEnd).ToList();
    }

    public static PatientLabOrder FindById(long invoiceId)
    {
        GuardAccess();

        Condition.Requires(invoiceId).IsGreaterThan(0);
        return NotNullo(
            DomainManager.EntityManager.PatientLabOrders
                .Where(u => u.InvoiceId == invoiceId)
                .Include(PatientLabOrder.PathFor(o => o.InvoiceMaster))
                .SingleOrDefault());
    }

    public static LabOrderFullDetailsSlice? GetLabOrderFullDetails(long invoiceId)
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);

        var query = DomainManager.EntityManager.SP_GetLabOrderFullDetails(invoiceId);
        var orderSlice = query?.FirstOrDefault();

        // set the assoc lab name as referrer if referrer is missing
        if (orderSlice is { IsExternalSubOrder: true, AssociateLabId: not null })
            if (string.IsNullOrEmpty(orderSlice.ReferrerName))
            {
                orderSlice.ReferrerName = orderSlice.AssociateLabName;
            }

        return orderSlice;
    }

    public static List<LabOrderSearchResultSlice> SearchActiveLabOrderByInvoiceId(long invoiceId)
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);
        GuardAccess();

        var query = DomainManager.EntityManager.SP_SearchActiveLabOrderByInvoiceId(invoiceId);
        return query?.ToList();
    }

    public static List<LabOrderSearchResultSlice> SearchActiveLabOrdersByPatientIdAndDateRange(
        string patientId, DateTime dtFrom, DateTime dtTo)
    {
        Condition.Requires(patientId).IsNotNullOrEmpty();
        GuardAccess();

        var query = DomainManager.EntityManager
            .SP_SearchActiveLabOrdersByPatientIdAndDateRange(patientId, dtFrom, dtTo);
        return query?.ToList();
    }

    public static List<LabOrderSearchResultSlice> SearchActiveLabOrdersByWorkflowAndDateRange(
        WorkflowStageType wfStage,
        DateTime dtFrom,
        DateTime dtTo)
    {
        GuardAccess();

        var query = DomainManager.EntityManager
            .SP_SearchActiveLabOrdersByWorkflowAndDateRange((byte)wfStage, dtFrom, dtTo);
        return query?.ToList();
    }

    public static List<LabOrderSearchResultSlice> SearchActiveLabOrdersByDateRange(
        DateTime dtFrom,
        DateTime dtTo)
    {
        GuardAccess();

        var query = DomainManager.EntityManager.SP_SearchActiveLabOrdersByDateRange(dtFrom, dtTo);
        return query?.ToList();
    }

    public static List<LabOrderSearchResultSlice> SearchLabOrdersByDateRange(
        DateTime dtFrom,
        DateTime dtTo)
    {
        GuardAccess();

        var query = DomainManager.EntityManager.SP_SearchLabOrdersByDateRange(dtFrom, dtTo);
        return query?.ToList();
    }

    public static List<LabOrderSearchResultSlice> SearchLabOrderByInvoiceId(long invoiceId)
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);
        GuardAccess();

        var query = DomainManager.EntityManager.SP_SearchLabOrderByInvoiceId(invoiceId);
        return query?.ToList();
    }

    public static List<LabOrderSearchResultSliceEx> SearchLabOrderByAffiliateIdAndDateRange(short id,
        DateTime dtFrom,
        DateTime dtTo)
    {
        Condition.Requires(id).IsGreaterThan(0);
        GuardAccess();

        var query = DomainManager.EntityManager
            .SP_SearchLabOrderByAffiliateIdAndDateRange(id, dtFrom, dtTo);
        return query?.ToList();
    }

    public static List<LabOrderSearchResultSliceEx> SearchLabOrderByCustomerIdAndDateRange(int id,
        DateTime dtFrom,
        DateTime dtTo)
    {
        Condition.Requires(id).IsGreaterThan(0);
        GuardAccess();

        var query = DomainManager.EntityManager
            .SP_SearchLabOrderByCustomerIdAndDateRange(id, dtFrom, dtTo);
        return query?.ToList();
    }

    public static List<LabOrderSearchResultSliceEx> SearchLabOrderByCustomerUPINAndDateRange(string upin,
        DateTime dtFrom,
        DateTime dtTo)
    {
        Condition.Requires(upin).IsNotNullOrEmpty();
        GuardAccess();

        var query = DomainManager.EntityManager
            .SP_SearchLabOrderByCustomerUPINAndDateRange(upin, dtFrom, dtTo);
        return query?.ToList();
    }

    public static List<LabOrderSearchResultSlice> SearchLabOrderByReferrerIdAndDateRange(int referrerId,
        DateTime dtFrom,
        DateTime dtTo)
    {
        Condition.Requires(referrerId).IsGreaterThan(0);
        GuardAccess();

        var query = DomainManager.EntityManager
            .SP_SearchLabOrdersByReferrerIdAndDateRange(referrerId, dtFrom, dtTo);
        return query?.ToList();
    }

    public static List<LabOrderSearchResultSliceEx> SearchLabOrderByCorporateIdAndDateRange(short id,
        DateTime dtFrom,
        DateTime dtTo)
    {
        Condition.Requires(id).IsGreaterThan(0);
        GuardAccess();

        var query = DomainManager.EntityManager
            .SP_SearchLabOrderByCorporateClientIdAndDateRange(id, dtFrom, dtTo);
        return query?.ToList();
    }

    public static List<LabOrderSearchResultSliceEx> SearchLabOrderByAssocLabAndDateRange(short id,
        DateTime dtFrom,
        DateTime dtTo)
    {
        Condition.Requires(id).IsGreaterThan(0);
        GuardAccess();

        var query = DomainManager.EntityManager
            .SP_SearchLabOrderByAssociateLabIdAndDateRange(id, dtFrom, dtTo);
        return query?.ToList();
    }

    public static List<LabOrderSearchResultSlice> SearchLabOrdersByPatientIdAndDateRange(string patientId,
        DateTime dtFrom,
        DateTime dtTo)
    {
        Condition.Requires(patientId).IsNotNullOrEmpty();
        GuardAccess();

        var query = DomainManager.EntityManager
            .SP_SearchLabOrdersByPatientIdAndDateRange(patientId, dtFrom, dtTo);
        return query?.ToList();
    }

    public static List<LabOrderSearchResultSlice> SearchLabOrdersByWorkflowAndDateRange(
        WorkflowStageType wfStage,
        DateTime dtFrom,
        DateTime dtTo)
    {
        GuardAccess();

        var query = DomainManager.EntityManager
            .SP_SearchLabOrdersByWorkflowAndDateRange((byte)wfStage, dtFrom, dtTo);
        return query?.ToList();
    }

    public static List<LabOrderSearchResultSlice> SearchActiveLabOrdersByDateRangeAndResultBundleWorkflow(
        WorkflowStageType wfStage,
        DateTime dtFrom,
        DateTime dtTo)
    {
        GuardAccess();

        var query = DomainManager.EntityManager
            .SP_SearchActiveLabOrdersByDateRangeAndResultBundleWorkflow((byte)wfStage, dtFrom, dtTo);
        return query?.ToList();
    }

    public static List<LabOrderSearchResultSlice> SearchActiveLabOrdersByDateRangeAndResultBundleWorkflowBetween(
        WorkflowStageType wfFrom,
        WorkflowStageType wfTo,
        DateTime dtFrom,
        DateTime dtTo)
    {
        GuardAccess();

        var query = DomainManager.EntityManager
            .SP_SearchActiveLabOrdersByDateRangeAndResultBundleWorkflowBetween((byte)wfFrom,
                (byte)wfTo,
                dtFrom,
                dtTo);
        return query?.ToList();
    }

    public static List<LabOrderFilterResultSlice> FilterActiveLabOrdersByWorkflowAndDateRange(
        WorkflowStageType wfStage,
        DateTime dtFrom,
        DateTime dtTo,
        char[] filters)
    {
        GuardAccess();

        var sb = new StringBuilder();
        foreach (var filter in filters) sb.Append($@"{filter},");

        var strFilter = sb.ToString();
        if (strFilter.Length > 0) strFilter = strFilter.Substring(0, strFilter.Length - 1);

        var query = DomainManager.EntityManager
            .SP_FilterActiveLabOrdersByWorkflowAndDateRange((byte)wfStage, dtFrom, dtTo, strFilter);
        return query?.ToList();
    }

    public static List<LabOrderFilterResultSlice> FilterActiveLabOrdersByDateRange(DateTime dtFrom,
        DateTime dtTo,
        char[] filters)
    {
        GuardAccess();

        var sb = new StringBuilder();
        foreach (var filter in filters) sb.Append($@"{filter},");

        var strFilter = sb.ToString();
        if (strFilter.Length > 0) strFilter = strFilter.Substring(0, strFilter.Length - 1);

        var query = DomainManager.EntityManager
            .SP_FilterActiveLabOrdersByDateRange(dtFrom, dtTo, strFilter);
        return query?.ToList();
    }

    public static void UpdateLabOrderWorkflowStage(long invoiceId, WorkflowStageType wfStage)
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);
        GuardAccess(false);

        DomainManager.EntityManager.SP_UpdateLabOrderWorkflowStage(invoiceId, (byte)wfStage);
    }

    public static void UpdateLabOrderMirrorFlag(long invoiceId, MirrorFlag flag)
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);
        GuardAccess(false);

        DomainManager.EntityManager.SP_UpdateLabOrderMirrorFlag(invoiceId, (byte)flag);
    }

    public static LabOrderDetailsSlice FetchLabOrderDetails(long invoiceId)
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);
        GuardAccess(false);

        var query = DomainManager.EntityManager.SP_FetchLabOrderDetails(invoiceId);
        return query?.FirstOrDefault();
    }

    public static List<LabOrderDetailsSlice> GetCancelledOrdersInDateRange(DateTime dtFrom, DateTime dtTo)
    {
        Condition.Requires(dtTo).IsGreaterOrEqual(dtFrom);
        GuardAccess(false);

        return DomainManager.EntityManager.SP_GetCancelledOrdersInDateRange(dtFrom, dtTo).ToList();
    }

    public static bool CheckLabOrderIsCanceled(long invoiceId)
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);
        GuardAccess(false);

        var query = DomainManager.EntityManager.SP_CheckLabOrderIsCanceled(invoiceId).SingleOrDefault();
        return query.HasValue && query.Value;
    }

    public static void CancelLabOrder(long invoiceId, WorkflowStageType wfStage)
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);
        GuardAccess(false);

        DomainManager.EntityManager.SP_CancelLabOrder(invoiceId, (byte)wfStage);
    }

    public static void ScavengeAllOrphanedResultBundlesInInvoice(long invoiceId, WorkflowStageType wfStage,
        TestResultType ignoreResultType)
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);
        GuardAccess(false);

        DomainManager.EntityManager.SP_ScavengeAllOrphanedResultBundlesInInvoice(invoiceId,
            (byte)wfStage,
            (byte)ignoreResultType);
    }

    public static void AutoUpdateLabOrderWorkflowStageFromOrderedTests(long invoiceId,
        WorkflowStageType wfHighWaterMark)
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);
        GuardAccess(false);

        DomainManager.EntityManager.SP_AutoUpdateLabOrderWorkflowStageFromOrderedTests(invoiceId,
            (byte)wfHighWaterMark);
    }

    private static List<FilteredInvoiceSlice> invokeServerFilterMethod(DateTime beginDate,
        DateTime endDate,
        short catId,
        List<short> labIds,
        string methodName)
    {
        return (List<FilteredInvoiceSlice>)DomainManager
            .EntityManager
            .InvokeServerMethod(@"MBoss.ServerMethods, MBoss",
                methodName,
                beginDate,
                endDate,
                catId,
                labIds);
    }

    public static List<FilteredInvoiceSlice> FilterLabOrdersByDateRange(DateTime beginDate, DateTime endDate)
    {
        Condition.Requires(beginDate).IsLessOrEqual(endDate);

        return invokeServerFilterMethod(beginDate,
            endDate,
            -1,
            [],
            @"FilterLabOrdersByDateRange");
    }

    public static List<FilteredInvoiceSlice> FilterLabOrdersByIncludePerformingLabDateRange(
        DateTime beginDate,
        DateTime endDate,
        List<short> labIds)
    {
        Condition.Requires(labIds).IsNotNull().IsNotEmpty();
        Condition.Requires(beginDate).IsLessOrEqual(endDate);

        return invokeServerFilterMethod(beginDate,
            endDate,
            -1,
            labIds,
            @"FilterLabOrdersByIncludePerformingLabDateRange");
    }

    public static List<FilteredInvoiceSlice> FilterLabOrdersByExcludePerformingLabDateRange(
        DateTime beginDate,
        DateTime endDate,
        List<short> labIds)
    {
        Condition.Requires(labIds).IsNotNull().IsNotEmpty();
        Condition.Requires(beginDate).IsLessOrEqual(endDate);

        return invokeServerFilterMethod(beginDate,
            endDate,
            -1,
            labIds,
            @"FilterLabOrdersByExcludePerformingLabDateRange");
    }

    public static List<FilteredInvoiceSlice> FilterLabOrdersByIncludePerformingLabDateRangeReferrerCategory(
        DateTime beginDate,
        DateTime endDate,
        short catId,
        List<short> labIds)
    {
        Condition.Requires(labIds).IsNotNull().IsNotEmpty();
        Condition.Requires(beginDate).IsLessOrEqual(endDate);

        return invokeServerFilterMethod(beginDate,
            endDate,
            catId,
            labIds,
            @"FilterLabOrdersByIncludePerformingLabDateRangeReferrerCategory");
    }

    public static List<FilteredInvoiceSlice> FilterLabOrdersByExcludePerformingLabDateRangeReferrerCategory(
        DateTime beginDate,
        DateTime endDate,
        short catId,
        List<short> labIds)
    {
        Condition.Requires(labIds).IsNotNull().IsNotEmpty();
        Condition.Requires(beginDate).IsLessOrEqual(endDate);

        return invokeServerFilterMethod(beginDate,
            endDate,
            catId,
            labIds,
            @"FilterLabOrdersByExcludePerformingLabDateRangeReferrerCategory");
    }

    public static List<ReferralEligibleLabOrderSlice> GetReferralEligibleLabOrdersByDateRange(DateTime dtStart,
        DateTime dtEnd)
    {
        Condition.Requires(dtStart).IsLessOrEqual(dtEnd);

        return DomainManager.EntityManager
            .SP_GetReferralEligibleLabOrdersByDateRange(dtStart, dtEnd)
            .ToList();
    }

    public static List<ReferralEligibleLabOrderSlice> GetReferralEligibleLabOrdersByDateRangeAndCategory(
        DateTime dtStart,
        DateTime dtEnd,
        short catId)
    {
        Condition.Requires(dtStart).IsLessOrEqual(dtEnd);
        Condition.Requires(catId).IsGreaterThan(0);

        return DomainManager.EntityManager
            .SP_GetReferralEligibleLabOrdersByDateRangeAndCategory(dtStart, dtEnd, catId)
            .ToList();
    }

    public static List<ReferralEligibleLabOrderSlice> GetReferralEligibleLabOrdersByDateRangeForReferrer(
        DateTime dtStart,
        DateTime dtEnd,
        int refId)
    {
        Condition.Requires(dtStart).IsLessOrEqual(dtEnd);
        Condition.Requires(refId).IsGreaterThan(0);

        return DomainManager.EntityManager
            .SP_GetReferralEligibleLabOrdersByDateRangeForReferrer(dtStart, dtEnd, refId)
            .ToList();
    }

    public static long CreateNewLabOrder(NewLabOrderContextDto dto)
    {
        Condition.Requires(dto).IsNotNull();

        return (long)DomainManager
            .EntityManager
            .InvokeServerMethod(@"MBoss.ServerMethods, MBoss", @"CreateNewLabOrder", dto);
    }

    public static List<LabOrderWithFinanceTestDetails> ScanLabOrderDetailsByDateRange(DateTime dtFrom, DateTime dtTo)
    {
        Condition.Requires(dtFrom).IsLessOrEqual(dtTo);

        return DomainManager.EntityManager.SP_ScanLabOrderDetailsByDateRange(dtFrom, dtTo).ToList();
    }

    public static List<PatientLabOrder> SearchLabOrdersByPhoneAndDateRange(string phone, DateTime dtFrom, DateTime dtTo)
    {
        return DomainManager.EntityManager.PatientLabOrders
            .Where(l => !l.IsCancelled
                        && l.PhoneNumber == phone
                        && l.OrderDateTime >= dtFrom
                        && l.OrderDateTime <= dtTo)
            .ToList();
    }

    public static List<PatientLabOrder> SearchLabOrdersByCustomerUpinAndDateRange(string upin, DateTime dtFrom,
        DateTime dtTo)
    {
        return (
            from ord in DomainManager.EntityManager.PatientLabOrders
            join cust in DomainManager.EntityManager.Customers on ord.CustomerId equals cust.Id
            where cust.UPIN == upin
            where ord.IsCancelled == false
            where ord.OrderDateTime >= dtFrom
            where ord.OrderDateTime <= dtTo
            select ord
        ).ToList();
    }

    public static List<PatientLabOrder> FindAllOrdersByPhone(string phone)
    {
        return DomainManager.EntityManager.PatientLabOrders
            .Where(l => !l.IsCancelled && l.PhoneNumber == phone)
            .ToList();
    }

    public static void SetLabOrderCustomerId(long invoiceId, long customerId)
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);
        Condition.Requires(customerId).IsGreaterThan(0);

        var labOrder = DomainManager.EntityManager.PatientLabOrders.SingleOrDefault(u => u.InvoiceId == invoiceId);

        if (labOrder != null)
        {
            labOrder.CustomerId = customerId;
            labOrder.RegisteredMemberId = customerId;
            DomainManager.EntityManager.SaveChanges();
        }
    }

    public static void SetCustomerIdForLabOrders(List<long> invoiceIds, long customerId)
    {
        Condition.Requires(customerId).IsGreaterThan(0);

        var labOrders = DomainManager.EntityManager.PatientLabOrders
            .Where(u => invoiceIds.Contains(u.InvoiceId))
            .ToList();

        if (!labOrders.Any()) return;

        foreach (var order in labOrders)
        {
            order.CustomerId = customerId;
            order.RegisteredMemberId = customerId;
        }

        DomainManager.EntityManager.SaveChanges();
    }
}