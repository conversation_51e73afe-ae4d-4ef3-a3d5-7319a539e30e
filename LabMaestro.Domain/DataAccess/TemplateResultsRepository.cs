﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2012, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: TemplateResultsRepository.cs 718 2013-07-02 14:48:27Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System.Linq;
using CuttingEdge.Conditions;
using LabMaestro.Shared;
using Manager = LabMaestro.Domain.DomainManager;

namespace LabMaestro.Domain;

public sealed class TemplateResultsRepository : BaseRepository<TemplateResult>
{
    public static TemplateResultSlice GetTemplateResultForOrderedTest(long ordTestId, long resultBundleId)
    {
        Condition.Requires(ordTestId).IsGreaterThan(0);
        Condition.Requires(resultBundleId).IsGreaterThan(0);

        var query = Manager.EntityManager.SP_GetTemplateResultForOrderedTest(ordTestId, resultBundleId)
            .SingleOrDefault();
        return query != null ? TemplateResultSlice.AssembleFrom(query) : null;
    }

    public static void InsertTemplateResult(long ordTestId, long resultBundleId, string content)
    {
        Condition.Requires(ordTestId).IsGreaterThan(0);
        Condition.Requires(resultBundleId).IsGreaterThan(0);
        Condition.Requires(content).IsNotNullOrEmpty();

        var lobContent = CompressionUtils.CompressString(content);
        Manager.EntityManager.SP_InsertTemplateResult(ordTestId, resultBundleId, lobContent);
    }

    public static void UpdateTemplateResult(long itemId, string content)
    {
        Condition.Requires(itemId).IsGreaterThan(0);
        Condition.Requires(content).IsNotNullOrEmpty();

        var lobContent = CompressionUtils.CompressString(content);
        Manager.EntityManager.SP_UpdateTemplateResult(itemId, lobContent);
    }

    public static TemplateResult? Find(long id) =>
        Manager.EntityManager.TemplateResults.SingleOrDefault<TemplateResult>(t => t.Id == id);

    public static TemplateResultForReportGenerationSlice GetTemplateResultForReportGeneration(long resultBundleId)
    {
        Condition.Requires(resultBundleId).IsGreaterThan(0);
        return Manager.EntityManager.SP_GetTemplateResultForReportGeneration(resultBundleId).FirstOrDefault();
    }
}