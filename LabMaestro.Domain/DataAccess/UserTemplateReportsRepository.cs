﻿//  * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
//  
//  Copyright (c) 2012, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
//  All rights reserved.
//  
//  Redistribution and use in source and binary forms, with or without
//  modification, are permitted provided that the following conditions are met:
//     * Redistributions of source code must retain the above copyright
//       notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above copyright
//       notice, this list of conditions and the following disclaimer in the
//       documentation and/or other materials provided with the distribution.
//     * Neither the name of the <organization> nor the
//       names of its contributors may be used to endorse or promote products
//       derived from this software without specific prior written permission.
//  
//  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
//  ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
//  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
//  DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
//  DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
//  (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
//  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
//  ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
//  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
//  SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
//  
//  $Id: UserTemplateReportsRepository.cs 1248 2014-03-30 12:41:12Z <EMAIL> $
//  
//  * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System.Collections.Generic;
using System.Linq;
using CuttingEdge.Conditions;
using LabMaestro.Shared;
using Manager = LabMaestro.Domain.DomainManager;

namespace LabMaestro.Domain;

public sealed class UserTemplateReportsRepository : BaseRepository<UserTemplateReport>
{
    public static UserTemplateReport FindById(short templateId)
    {
        Condition.Requires(templateId).IsGreaterThan(0);
        return NotNullo(Manager.EntityManager.UserTemplateReports.SingleOrDefault(x => x.Id == templateId));
    }

    public static List<TemplateReportSlice> GetUserTemplatesListForLabTest(short testId, short userId)
    {
        Condition.Requires(testId).IsGreaterThan(0);

        var query = Manager.EntityManager.SP_GetUserTemplatesListForLabTest(testId, userId);
        return query != null ? query.Select(TemplateReportSlice.AssembleFrom).ToList() : null;
    }

    public static UserTemplateGroupLink CreateUserTemplateGroupLink(short userId, short tplGroupId)
    {
        short? newItem = null;
        Manager.EntityManager.SP_CreateUserTemplateGroupLink(userId, tplGroupId, ref newItem);
        return newItem != null
            ? Manager.EntityManager.UserTemplateGroupLinks.SingleOrDefault(x => x.Id == newItem)
            : null;
    }

    public static UserTemplateReport CreateNewUserTemplateReport(string name, string tags, string content,
        short tplGroupId, SortPriorityType priority)
    {
        short? newItem = null;
        var lobContent = CompressionUtils.CompressString(content);
        Manager.EntityManager.SP_CreateNewUserTemplateReport(name, tags, (byte)priority, tplGroupId, lobContent,
            ref newItem);
        return newItem != null
            ? Manager.EntityManager.UserTemplateReports.SingleOrDefault(x => x.Id == newItem)
            : null;
    }

    public static string GetTemplateReportContent(short templateId)
    {
        Condition.Requires(templateId).IsGreaterThan(0);

        var qry = NotNullo(Manager.EntityManager.UserTemplateReports.SingleOrDefault(x => x.Id == templateId));
        return qry != null ? CompressionUtils.DecompressToString(qry.Content) : string.Empty;
    }
}