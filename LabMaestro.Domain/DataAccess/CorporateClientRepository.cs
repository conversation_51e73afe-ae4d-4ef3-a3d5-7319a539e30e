﻿using System.Collections.Generic;
using System.Linq;

namespace LabMaestro.Domain;

using Manager = DomainManager;

public sealed class CorporateClientRepository : BaseRepository<CorporateClient>
{
    public static IEnumerable<CorporateClientSlice> FetchActive() =>
        Manager.EntityManager.SP_GetActiveCorporateClients().ToList().OrderBy(x => x.Name);

    public static IEnumerable<CorporateClientSlice> FetchAll() =>
        Manager.EntityManager.SP_GetAllCorporateClients().ToList().OrderBy(x => x.Name);

    public static CorporateClient? Find(short id) =>
        Manager.EntityManager.CorporateClients.FirstOrDefault(c => c.Id == id);

    public static string? GetName(short id) =>
        Manager.EntityManager.CorporateClients.FirstOrDefault(c => c.Id == id)?.Name;
}