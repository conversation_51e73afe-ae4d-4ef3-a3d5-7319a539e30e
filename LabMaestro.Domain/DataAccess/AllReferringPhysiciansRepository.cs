﻿using System.Collections.Generic;
using System.Linq;

namespace LabMaestro.Domain;

public sealed class AllReferringPhysiciansRepository : CachedRepositoryBase<ReferrerSlice>
{
    private const string CacheKey = @"referrer_catalog_all";

    public static List<ReferrerSlice> GetCatalog(bool disableCache) =>
        LoadCatalog(<PERSON>ache<PERSON><PERSON>, disableCache, fetchFromServer).ToList();

    private static IEnumerable<ReferrerSlice> fetchFromServer() =>
        ReferrersRepository.FindAllReferrers().Select(AssembleFrom).ToList();

    private static ReferrerSlice AssembleFrom(Referrer referrer) =>
        new()
        {
            Id = referrer.Id,
            IsActive = referrer.IsActive,
            Prefix = referrer.Prefix,
            Name = referrer.Name,
            Suffix = referrer.Suffix,
            IdentifyingTag = referrer.IdentifyingTag,
            MobilePhone = referrer.MobilePhone,
            FullName = referrer.FullName,
            ReferralClassId = referrer.ReferralClassId,
            ReferralClassName = string.Empty,
            ReferralEligible = true,
        };
}