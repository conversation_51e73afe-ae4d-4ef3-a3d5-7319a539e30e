﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2012, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: BillableItemsRepository.cs 718 2013-07-02 14:48:27Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System.Collections.Generic;
using System.Linq;
using C1.LiveLinq;
using CuttingEdge.Conditions;
using Manager = LabMaestro.Domain.DomainManager;

namespace LabMaestro.Domain;

public sealed class BillableItemsRepository : IndexedBaseRepository<BillableItem>
{
    public static void Refresh()
    {
        GuardAccess();
        RefreshCache(fetchDataFromServer);
    }

    public static List<BillableItem> FindActiveItems()
    {
        GuardAccess();
        ValidateCache(false, fetchDataFromServer);

        return indexedList.AsIndexed().Where(t => t.IsActive).ToList();
    }

    public static List<BillableItem> FindAllItems()
    {
        GuardAccess();
        ValidateCache(false, fetchDataFromServer);

        return indexedList.ToList();
    }

    public static BillableItem FindById(short itemId)
    {
        GuardAccess();
        Condition.Requires(itemId).IsGreaterThan(0);
        ValidateCache(false, fetchDataFromServer);

        var query = indexedList.AsIndexed().SingleOrDefault(u => u.Id == itemId);
        return query;
    }

    public static BillableItem FindActiveItemById(short itemId)
    {
        GuardAccess();
        Condition.Requires(itemId).IsGreaterThan(0);
        ValidateCache(false, fetchDataFromServer);

        var query = indexedList.AsIndexed().SingleOrDefault(u => u.Id == itemId && u.IsActive);
        return query;
    }

    public static BillableItem CreateNew(string name, decimal price)
    {
        GuardAccess(false);
        Condition.Requires(name).IsNotNullOrEmpty();
        Condition.Requires(price).IsGreaterOrEqual(0m);

        var test = new BillableItem
        {
            Name = name,
            UnitPrice = price,
            IsActive = true
        };
        Manager.EntityManager.AddEntity(test);
        return test;
    }

    private static List<BillableItem> fetchDataFromServer()
    {
        return Manager.EntityManager.BillableItems.ToList();
    }

    public static BillableItem CreateNew(string name, bool isActive, decimal unitPrice)
    {
        Condition.Requires(name).IsNotNullOrEmpty();
        Condition.Requires(unitPrice).IsGreaterOrEqual(0m);

        short? itemId = null;
        Manager.EntityManager.SP_CreateNewBillableItem(name, isActive, unitPrice, ref itemId);
        return itemId != null ? FindById((short)itemId) : null;
    }

    public static void Update(short itemId, string name, bool isActive, decimal unitPrice)
    {
        Condition.Requires(itemId).IsGreaterThan(0);
        Condition.Requires(name).IsNotNullOrEmpty();
        Condition.Requires(unitPrice).IsGreaterOrEqual(0m);

        Manager.EntityManager.SP_UpdateBillableItem(itemId, name, isActive, unitPrice);
    }

    public static List<LinkedBillableItemsForTestSlice> GetAllLinkedBillableItemsForTest(short testId)
    {
        Condition.Requires(testId).IsGreaterThan(0);

        return Manager.EntityManager.SP_GetAllLinkedBillableItemsForTest(testId).ToList();
    }

    public static List<OrderableBillableItemsSlice> GetAllOrderableBillableItems()
    {
        return Manager.EntityManager.SP_GetAllOrderableBillableItems().ToList();
    }
}