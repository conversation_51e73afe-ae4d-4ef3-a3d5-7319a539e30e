﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: UsersRepository.cs 1248 2014-03-30 12:41:12Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using CuttingEdge.Conditions;
using IdeaBlade.EntityModel;
using LabMaestro.Shared;
using Manager = LabMaestro.Domain.DomainManager;

namespace LabMaestro.Domain;

public sealed class UsersRepository : BaseRepository<User>
{
    public static User CreateUser(string username, string passhash, string displayname)
    {
        GuardAccess(false);

        Condition.Requires(username).IsNotNullOrEmpty();
        Condition.Requires(passhash).IsNotNullOrEmpty();
        Condition.Requires(displayname).IsNotNullOrEmpty();

        var user = new User
        {
            UserName = username,
            PassHash = passhash,
            DateCreated = DateTime.Now,
            DisplayName = displayname,
            LastModified = DateTime.Now
        };
        Manager.EntityManager.AddEntity(user);
        return user;
    }

    public static User FindById(short userid)
    {
        //GuardAccess();

        Condition.Requires(userid).IsGreaterThan(0);
        return NotNullo(Manager.EntityManager.Users.SingleOrDefault(u => u.Id == userid));
    }

    public static User FindConsultantById(short userid)
    {
        //GuardAccess();

        Condition.Requires(userid).IsGreaterThan(0);
        return NotNullo(Manager.EntityManager.Users.Include(u => u.Consultant).SingleOrDefault(u => u.Id == userid));
    }

    public static User FindByUserName(string username)
    {
        //GuardAccess();

        Condition.Requires(username).IsNotNullOrEmpty();
        return Manager.EntityManager.Users
            .SingleOrDefault(
                u => string.Compare(u.UserName, username, StringComparison.OrdinalIgnoreCase) == 0);
    }

    public static short GetActiveUserId(string username)
    {
        Condition.Requires(username).IsNotNullOrEmpty();

        var query = Manager.EntityManager.Users.Where(u => u.UserName == username && u.IsActive).FirstOrNullEntity();
        return !query.EntityAspect.IsNullEntity ? query.Id : (short)-1;
    }

    public static bool CheckUserLogin(string username, string password)
    {
        Condition.Requires(username).IsNotNullOrEmpty();
        Condition.Requires(password).IsNotNullOrEmpty();
        var passHash = AuthUtils.EncodePassword(password);
        var query =
            Manager.EntityManager.Users.Where(u => u.UserName == username && u.PassHash == passHash && u.IsActive)
                .FirstOrNullEntity();
        return !query.EntityAspect.IsNullEntity;
    }

    public static void UpdateUserLoginTime(User user)
    {
        Condition
            .Requires(user)
            .IsNotNull()
            .Evaluate(u => !u.EntityAspect.IsNullEntity)
            .Evaluate(u => u.Id > 0)
            .Evaluate(u => u.EntityAspect.EntityState != EntityState.AllButDetached);

        UpdateUserLoginTime(user.Id);
    }

    public static List<User> FindAllUsers()
    {
        return Manager.EntityManager.Users.OrderBy(u => u.UserName).ToList();
    }

    public static List<User> FindAllUsersInRole(short roleId)
    {
        Condition.Requires(roleId).IsGreaterThan(0);
        return Manager.EntityManager.Users.Where(u => u.RoleId == roleId).OrderBy(u => u.UserName).ToList();
    }

    public static void UpdateUserLoginTime(short userid)
    {
        GuardAccess();

        Condition.Requires(userid).IsGreaterThan(0);
        Manager.EntityManager.SP_UpdateUserLastLogin(userid);
    }

    public static string GetUserDisplayName(short userId)
    {
        Condition.Requires(userId).IsGreaterThan(0);
        return Manager.EntityManager.SP_GetUserDisplayName(userId).FirstOrDefault();
    }

    public static void UpsertConsultantSignature(short userId, byte[] sigImage, string sigText)
    {
        Condition.Requires(userId).IsGreaterThan(0);

        Manager.EntityManager.SP_UpsertConsultantSignature(userId, sigImage, sigText);
    }

    public static void UpdateUserPassword(short userId, string password)
    {
        Condition.Requires(userId).IsGreaterThan(0);
        Condition.Requires(password).IsNotNullOrEmpty();

        var passhash = AuthUtils.EncodePassword(password);
        Manager.EntityManager.SP_UpdateUserPassHash(userId, passhash);
    }

    public static bool VerifyUserPassword(short userId, string password)
    {
        Condition.Requires(userId).IsGreaterThan(0);
        Condition.Requires(password).IsNotNullOrEmpty();

        var passhash = AuthUtils.EncodePassword(password);
        var user = Manager.EntityManager.Users.SingleOrDefault(u => u.Id == userId);
        return user != null && user.PassHash.CompareTo(passhash) == 0;
    }

    public static List<ActiveUsersWithRoleSlice> GetAllActiveUsersWithRole()
    {
        return Manager.EntityManager.SP_GetAllActiveUsersWithRole() as List<ActiveUsersWithRoleSlice>;
    }
}