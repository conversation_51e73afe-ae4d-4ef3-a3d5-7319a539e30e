﻿//  * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
//  
//  Copyright (c) 2012, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
//  All rights reserved.
//  
//  Redistribution and use in source and binary forms, with or without
//  modification, are permitted provided that the following conditions are met:
//     * Redistributions of source code must retain the above copyright
//       notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above copyright
//       notice, this list of conditions and the following disclaimer in the
//       documentation and/or other materials provided with the distribution.
//     * Neither the name of the <organization> nor the
//       names of its contributors may be used to endorse or promote products
//       derived from this software without specific prior written permission.
//  
//  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
//  ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
//  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
//  DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
//  DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
//  (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
//  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
//  ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
//  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
//  SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
//  
//  $Id: UserSettingsRepository.cs 1248 2014-03-30 12:41:12Z <EMAIL> $
//  
//  * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System.Collections.Generic;
using System.Linq;
using C1.LiveLinq;
using CuttingEdge.Conditions;
using Manager = LabMaestro.Domain.DomainManager;

namespace LabMaestro.Domain;

public sealed class UserSettingsRepository : IndexedBaseRepository<UserSetting>
{
    public static void Refresh()
    {
        RefreshCache(fetchDataFromServer);
    }

    private static List<UserSetting> fetchDataFromServer()
    {
        return Manager.EntityManager.UserSettings.ToList();
    }

    public static IEnumerable<UserSetting> FindAll(short userId)
    {
        GuardAccess();

        return indexedList.ToList();
    }

    private static void ensureValidity(string key, bool readOnly = true)
    {
        GuardAccess(readOnly);
        key = key.Trim().ToLowerInvariant();
        Condition.Requires(key).IsNotNullOrEmpty();
        ValidateCache(false, fetchDataFromServer);
    }

    public static UserSetting FindByKey(short userId, string key)
    {
        ensureValidity(key);

        var query = indexedList.AsIndexed()
            .SingleOrDefault(x => x.UserId == userId && string.CompareOrdinal(x.SettingsKey, key) == 0);
        return query;
    }

    public static string GetStringValue(short userId, string key, string valDefault = "")
    {
        ensureValidity(key);

        var query = indexedList.AsIndexed()
            .Where(x => x.UserId == userId && string.CompareOrdinal(x.SettingsKey, key) == 0)
            .Select(x => x.StrValue).SingleOrDefault();
        return string.IsNullOrEmpty(query) ? valDefault : query;
    }

    public static int GetIntValue(short userId, string key)
    {
        ensureValidity(key);

        var query = indexedList.AsIndexed()
            .Where(x => x.UserId == userId && string.CompareOrdinal(x.SettingsKey, key) == 0)
            .Select(x => x.IntValue).SingleOrDefault();
        return query;
    }

    public static UserSetting Save(short userId, string key, string strVal, int intVal)
    {
        ensureValidity(key, false);

        var setting = indexedList.AsIndexed()
            .SingleOrDefault(x => x.UserId == userId && string.CompareOrdinal(x.SettingsKey, key) == 0);
        if (setting == null)
        {
            setting = new UserSetting { SettingsKey = key, StrValue = strVal, IntValue = intVal, UserId = userId };
            Manager.EntityManager.AddEntity(setting);
        }
        else
        {
            setting.StrValue = strVal;
            setting.IntValue = intVal;
        }

        Manager.EntityManager.SaveChanges();
        Refresh();
        return setting;
    }

    public static void Remove(short userId, string key)
    {
        ensureValidity(key, false);

        var setting = indexedList.AsIndexed()
            .SingleOrDefault(x => x.UserId == userId && string.CompareOrdinal(x.SettingsKey, key) == 0);
        if (setting != null)
        {
            Manager.EntityManager.RemoveEntity(setting);
            Manager.EntityManager.SaveChanges();
            Refresh();
        }
    }
}