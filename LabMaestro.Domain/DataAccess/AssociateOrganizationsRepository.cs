﻿using System.Collections.Generic;
using System.Linq;

namespace LabMaestro.Domain;

public sealed class AssociateOrganizationsRepository : BaseRepository<AssociateOrganization>
{
    public static List<AssociateOrganization> GetAllLabs() =>
        DomainManager.EntityManager.AssociateOrganizations.ToList();

    public static string? GetName(short id) =>
        DomainManager.EntityManager.AssociateOrganizations.FirstOrDefault(c => c.Id == id)?.Name;
}