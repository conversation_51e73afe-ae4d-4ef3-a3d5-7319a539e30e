﻿using System.Collections.Generic;
using System.Linq;
using C1.LiveLinq;
using C1.LiveLinq.Collections;
using CuttingEdge.Conditions;
using LabMaestro.Shared;
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2012, Dr<PERSON> <PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: OrderableTestSliceCatalogRepository.cs 718 2013-07-02 14:48:27Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

namespace LabMaestro.Domain;

public sealed class OrderableTestSliceCatalogRepository
{
    private const string CacheKey = @"LabTestCatalogRepository_catalog";

    public static OrderableTestSlice FindById(short testId) => GetCatalog(false).FindById(testId);

    public static OrderableTestsSlice GetOrderableTestDetails(short testId)
    {
        Condition.Requires(testId).IsGreaterThan(0);

        return DomainManager.EntityManager.SP_GetOrderableTestDetails(testId).SingleOrDefault();
    }

    public static OrderableTestSliceCatalog GetCatalog(bool noCache)
    {
        OrderableTestSliceCatalog catalog = null;
        if (noCache)
            AppCache.Remove(CacheKey);
        else
            catalog = (OrderableTestSliceCatalog)AppCache.Get(CacheKey);

        if (catalog == null || catalog.Count == 0) catalog = fetchFromServer();

        return catalog;
    }

    private static OrderableTestSliceCatalog fetchFromServer()
    {
        var catalog = new OrderableTestSliceCatalog();
        var testCatalog = DomainManager.EntityManager.SP_GetAllOrderableTests().ToList();
        var itemsCatalog = new IndexedCollection<OrderableTestsWithBillableItemsSlice>();
        itemsCatalog.AddRange(DomainManager.EntityManager.SP_GetAllOrderableTestsWithBillableItems().ToList());

        catalog.BeginUpdate();
        try
        {
            foreach (var test in testCatalog)
            {
                var testSlice = catalog.AddSlice(test);
                var items = itemsCatalog.AsIndexed().Where(x => x.TestId == testSlice.TestId);
                foreach (var bi in items) testSlice.AddItem(BillableItemSlice.AssembleFrom(bi));
            }
        }
        finally
        {
            catalog.EndUpdate();
        }

        AppCache.Store(CacheKey, catalog, 30);
        return catalog;
    }

    public static List<OrderableTestSlice> FindTestsByName(string name) => GetCatalog(false).FindTestsByName(name);

    public static OrderableTestSlice FindTestByName(string name) => GetCatalog(false).FindTestByName(name);
}