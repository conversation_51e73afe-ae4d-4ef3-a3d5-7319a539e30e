// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: WorkShiftsRepository.cs 1376 2014-06-20 13:54:04Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using CuttingEdge.Conditions;
using IdeaBlade.EntityModel;
using LabMaestro.Shared;
using Manager = LabMaestro.Domain.DomainManager;

namespace LabMaestro.Domain;

public delegate void ShiftEventHook(int shiftId, bool started);

public sealed class WorkShiftsRepository : BaseRepository<WorkShift>
{
    public static ShiftEventHook OnShiftEvent { get; set; }

    public static int StartNewShift(User user, decimal additionalBalance = 0, string note = null)
    {
        return StartNewShift(user.Id, additionalBalance, note);
    }

    public static int StartNewShift(short userId, decimal additionalBalance = 0, string note = null)
    {
        int? newShiftId = null;
        Manager.EntityManager.SP_BeginWorkShift(userId, additionalBalance, note, ref newShiftId);

        Reset(); // reset

        var openShiftId = newShiftId ?? -1;

        //TODO: Handle shift open failure

        // Notify all observers that we're in business
        if (OnShiftEvent != null && openShiftId > 0)
            OnShiftEvent(openShiftId, true);

        return openShiftId;
    }

    /// <summary>
    ///     Ends the currently open shift. It updates the shift financial records. Then closes the shift
    /// </summary>
    public static void EndShift(WorkShift shift)
    {
        Condition
            .Requires(shift)
            .IsNotNull()
            .Evaluate(e => !e.EntityAspect.IsNullEntity)
            .Evaluate(s => !s.IsClosed)
            .Evaluate(s => s.EntityAspect.EntityState != EntityState.AllButDetached);

        shift.ReconcileShiftFinances();

        //UpdateShiftFinances(shift);
        Manager.EntityManager.SaveChanges();

        // Close the shift by calling the sproc
        Manager.EntityManager.SP_EndWorkShift(shift.Id);

        // Notify all the observers that we're closing shop
        if (OnShiftEvent != null)
            OnShiftEvent(shift.Id, false);
    }

    public static void EndShift(int userId, int shiftId)
    {
        var shift = FindUserShiftById(userId, shiftId);
        EndShift(shift);
    }

    public static void ReconcileShiftFinances(int userId, int shiftId)
    {
        var shift = FindUserShiftById(userId, shiftId);
        shift.ReconcileShiftFinances();
    }

    public static WorkShift FindOpenShift(User user)
    {
        Condition
            .Requires(user)
            .IsNotNull()
            .Evaluate(u => !u.EntityAspect.IsNullEntity)
            .Evaluate(u => u.EntityAspect.EntityState != EntityState.AllButDetached);

        return FindOpenShift(user.Id);
    }

    public static int GetFirstOpenWorkShiftForUser(short userid)
    {
        Condition.Requires(userid).IsGreaterThan(0);
        var shiftId = Manager.EntityManager.SP_GetFirstOpenWorkShiftForUser(userid).SingleOrDefault();
        return shiftId.HasValue ? (int)shiftId : -1;
    }

    public static WorkShift FindOpenShift(short userid)
    {
        Condition.Requires(userid).IsGreaterThan(0);
        var shiftId = Manager.EntityManager.SP_GetFirstOpenWorkShiftForUser(userid).SingleOrDefault();
        if (shiftId.HasValue && shiftId.Value > 0) return FindShiftById((int)shiftId);
        return null;
        /*var query = Manager.EntityManager.WorkShifts
                           .Where(x => !x.IsClosed && x.UserId == userid)
                           .FirstOrNullEntity();
        return query.EntityAspect.IsNullEntity ? null : query;*/
    }

    public static WorkShift FindUserShiftById(User user, int shiftId)
    {
        var query = Manager.EntityManager.WorkShifts
            .Where(s => s.User == user && s.Id == shiftId)
            .FirstOrNullEntity();
        return query.EntityAspect.IsNullEntity ? null : query;
    }

    public static WorkShift FindUserShiftById(int userId, int shiftId)
    {
        var query = Manager.EntityManager.WorkShifts
            .Where(s => s.UserId == userId && s.Id == shiftId)
            .FirstOrNullEntity();
        return query.EntityAspect.IsNullEntity ? null : query;
    }

    public static WorkShift FindShiftById(int shiftId)
    {
        var query = Manager.EntityManager.WorkShifts
            .Where(s => s.Id == shiftId)
            .Include(WorkShift.PathFor(w => w.User))
            .FirstOrNullEntity();
        return query.EntityAspect.IsNullEntity ? null : query;
    }

    public static List<WorkShift> FindByDateRange(User user, DateTime dtStart, DateTime dtEnd)
    {
        Condition
            .Requires(user)
            .IsNotNull()
            .Evaluate(u => !u.EntityAspect.IsNullEntity)
            .Evaluate(u => u.EntityAspect.EntityState != EntityState.AllButDetached);

        return FindByDateRange(user.Id, dtStart, dtEnd);
    }

    public static List<WorkShift> FindByDateRange(short userid, DateTime dtStart, DateTime dtEnd)
    {
        Condition.Requires(userid).IsGreaterThan(0);
        Condition.Requires(dtStart).Evaluate(d => d.Date > DateTime.MinValue && d.Date < DateTime.MaxValue);
        Condition.Requires(dtEnd).Evaluate(d => d.Date >= dtStart.Date /* && d.Date <= DateTime.Now.Date*/);

        var query = Manager.EntityManager.WorkShifts
            .Where(s => s.UserId == userid &&
                        s.StartTime >= dtStart &&
                        s.StartTime <= dtEnd)
            .OrderByDescending(s => s.Id)
            .ToList();
        return query;
    }

    public static bool ShiftIsOpen(int shiftId)
    {
        Condition.Requires(shiftId).IsGreaterThan(0);

        var query = Manager.EntityManager.WorkShifts.Where(s => s.Id == shiftId && !s.IsClosed).FirstOrNullEntity();
        return !query.EntityAspect.IsNullEntity;
    }

    public static void UpdateShiftAdditionalBalance(int shiftId, decimal amount)
    {
        Condition.Requires(shiftId).IsGreaterThan(0);
        Condition.Requires(amount).IsGreaterOrEqual(0m);

        Manager.EntityManager.SP_UpdateShiftAdditionalBalance(shiftId, amount);
    }

    public static void UpdateShiftFinances(WorkShift shift)
    {
        Condition.Requires(shift).IsNotNull();
        Condition.Requires(shift.Id).IsGreaterThan(0);

        UpdateShiftFinances(shift.Id, shift.NumOrders,
            shift.ReceiveAmount, shift.DiscountAmount,
            shift.DiscountRebateAmount, shift.RefundAmount,
            shift.AdditionalBalance, shift.FinalBalance);
    }

    public static void UpdateShiftFinances(int shiftId, short numOrders,
        decimal payment, decimal discount,
        decimal discRebate, decimal refund,
        decimal addlBal, decimal finalBal)
    {
        Condition.Requires(shiftId).IsGreaterThan(0);
        Condition.Requires(numOrders).IsGreaterOrEqual(0);
        Condition.Requires(payment).IsGreaterOrEqual(0m);
        Condition.Requires(discount).IsGreaterOrEqual(0m);
        Condition.Requires(discRebate).IsGreaterOrEqual(0m);
        Condition.Requires(refund).IsGreaterOrEqual(0m);
        Condition.Requires(addlBal).IsGreaterOrEqual(0m);
        Condition.Requires(finalBal).IsGreaterOrEqual(0m);

        Manager.EntityManager.SP_UpdateShiftFinances(shiftId, numOrders,
            payment, discount,
            discRebate, refund,
            addlBal, finalBal);
    }

    public static List<WorkShift> FindAllOpenShiftsByDateRange(DateTime dtStart, DateTime dtEnd)
    {
        return FindShiftsByDateRangeInternal(dtStart, dtEnd, false);
    }

    public static IEnumerable<WorkShift> FindAllClosedShiftsByDateRange(DateTime dtStart, DateTime dtEnd)
    {
        return FindShiftsByDateRangeInternal(dtStart, dtEnd);
    }

    public static IEnumerable<WorkShift> FindAllShiftsByDateRange(DateTime dtStart, DateTime dtEnd)
    {
        return FindShiftsByDateRangeInternal(dtStart, dtEnd, false, true);
    }

    private static List<WorkShift> FindShiftsByDateRangeInternal(DateTime dtStart, DateTime dtEnd,
        bool onlyClosedShifts = true,
        bool allShifts = false)
    {
        Condition
            .Requires(dtStart)
            .Evaluate(d => d.Date > DateTime.MinValue && d.Date < DateTime.MaxValue);
        Condition
            .Requires(dtEnd)
            .Evaluate(d => d.Date >= dtStart.Date /* && d.Date <= DateTime.Now.Date*/);

        if (allShifts)
            return Manager.EntityManager.WorkShifts
                .Where(s => s.StartTime >= dtStart.Date && s.EndTime <= dtEnd.Date)
                .OrderByDescending(s => s.Id)
                .ToList();

        return Manager.EntityManager.WorkShifts
            .Where(s => s.IsClosed == onlyClosedShifts
                        && s.StartTime >= dtStart.Date
                        && s.StartTime <= dtEnd.Date)
            .OrderByDescending(s => s.Id)
            .ToList();
    }

    public static List<WorkshiftSearchResultSlice> FindWorkshiftByDateRange(
        DateTime dtStart,
        DateTime dtEnd)
    {
        Condition
            .Requires(dtStart)
            .Evaluate(d => d.Date > DateTime.MinValue && d.Date < DateTime.MaxValue);
        Condition
            .Requires(dtEnd)
            .Evaluate(d => d.Date >= dtStart.Date /* && d.Date <= DateTime.Now.Date*/);

        dtStart = dtStart.Date;
        dtEnd = SharedUtilities.LastMinuteOfDay(dtEnd);
        var qry = Manager.EntityManager.SP_FindWorkshiftByDateRange(dtStart, dtEnd);
        return qry != null ? qry.ToList() : null;
    }

    public static List<WorkshiftSearchResultSlice> FindWorkshiftByDateRangeRoles(
        DateTime dtStart,
        DateTime dtEnd,
        List<short> roles)
    {
        Condition
            .Requires(dtStart)
            .Evaluate(d => d.Date > DateTime.MinValue && d.Date < DateTime.MaxValue);
        Condition
            .Requires(dtEnd)
            .Evaluate(d => d.Date >= dtStart.Date /* && d.Date <= DateTime.Now.Date*/);
        Condition.Requires(roles).IsNotNull().IsLongerThan(0);

        dtStart = dtStart.Date;
        dtEnd = SharedUtilities.LastMinuteOfDay(dtEnd);

        return (List<WorkshiftSearchResultSlice>)DomainManager
            .EntityManager
            .InvokeServerMethod(@"MBoss.ServerMethods, MBoss",
                @"FindWorkshiftByDateRangeRoles",
                dtStart,
                dtEnd,
                roles);
    }
}