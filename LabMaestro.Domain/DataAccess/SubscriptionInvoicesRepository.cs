﻿using System.Linq;
using Manager = LabMaestro.Domain.DomainManager;

namespace LabMaestro.Domain;

public sealed class SubscriptionInvoicesRepository : BaseRepository<Invoice>
{
    public static CustomerInvoiceDetails? GetCustomerInvoiceDetails(long invoiceId) =>
        Manager.EntityManager.SP_GetCustomerInvoiceDetails(invoiceId).FirstOrDefault();

    public static CustomerSubscriptionDetails? GetCustomerSubscriptionDetails(long invoiceId) =>
        Manager.EntityManager.SP_GetCustomerSubscriptionDetails(invoiceId).FirstOrDefault();
}