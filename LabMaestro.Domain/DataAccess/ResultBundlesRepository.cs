﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ResultBundlesRepository.cs 1282 2014-05-21 14:21:17Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System.Collections.Generic;
using System.Linq;
using CuttingEdge.Conditions;
using LabMaestro.Shared;
using Manager = LabMaestro.Domain.DomainManager;

namespace LabMaestro.Domain;

public sealed class ResultBundlesRepository : BaseRepository<ResultBundle>
{
    public static ResultBundle InsertResultBundle(short userId,
        short labId,
        long invoiceId,
        TATRankingType tatRank,
        WorkflowStageType wfStage,
        TestResultType resultType,
        string displayTitle,
        string componentTests,
        string notes)
    {
        Condition.Requires(userId).IsGreaterThan(0);
        Condition.Requires(labId).IsGreaterThan(0);
        Condition.Requires(invoiceId).IsGreaterThan(0);

        byte[] lobContent = null;
        if (!string.IsNullOrEmpty(notes)) lobContent = CompressionUtils.CompressString(notes);

        long? bundleId = null;
        Manager.EntityManager.SP_InsertResultBundle(invoiceId,
            labId,
            (byte)tatRank,
            (byte)resultType,
            (byte)wfStage,
            userId,
            displayTitle,
            componentTests,
            lobContent,
            ref bundleId);
        return bundleId != null ? FindById((long)bundleId) : null;
    }

    public static void UpdateResultBundleLastUpdatedTime(long bundleId)
    {
        Condition.Requires(bundleId).IsGreaterThan(0);
        Manager.EntityManager.SP_UpdateResultBundleLastUpdatedTime(bundleId);
    }

    public static void UpdateResultBundleWorkflowStage(long bundleId, WorkflowStageType wfStage)
    {
        Condition.Requires(bundleId).IsGreaterThan(0);
        Manager.EntityManager.SP_UpdateResultBundleWorkflowStage(bundleId, (byte)wfStage);
    }

    public static ResultBundle FindById(long bundleId)
    {
        Condition.Requires(bundleId).IsGreaterThan(0);
        return NotNullo(Manager.EntityManager.ResultBundles.SingleOrDefault(x => x.Id == bundleId));
    }

    public static List<ResultBundlesForInvoice> GetActiveResultBundlesForInvoice(long invoiceId)
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);

        var query = Manager.EntityManager.SP_GetActiveResultBundlesForInvoice(invoiceId);
        return query == null ? null : query.ToList();
    }

    public static List<ResultBundlesForInvoice> GetAllResultBundlesForInvoiceEx(long invoiceId)
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);

        var query = Manager.EntityManager.SP_GetAllResultBundlesForInvoiceEx(invoiceId);
        return query == null ? null : query.ToList();
    }

    public static List<ResultBundlesForInvoice> GetActiveResultBundlesInInvoiceForUser(long invoiceId,
        short userId)
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);
        Condition.Requires(userId).IsGreaterThan(0);

        var query = Manager.EntityManager.SP_GetActiveResultBundlesInInvoiceForUser(invoiceId, userId);
        return query == null ? null : query.ToList();
    }

    public static int GetActiveResultBundlesCountForInvoice(long invoiceId)
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);

        var query = Manager.EntityManager.SP_GetActiveResultBundlesCountForInvoice(invoiceId).Single();
        return query == null ? 0 : (int)query;
    }

    public static int GetActiveResultBundlesCountForInvoiceLab(long invoiceId, short labId)
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);
        Condition.Requires(labId).IsGreaterThan(0);

        var query =
            Manager.EntityManager.SP_GetActiveResultBundlesCountForInvoiceLab(invoiceId, labId).FirstOrDefault();
        return query == null ? 0 : (int)query;
    }

    public static void RemoveResultBundle(long bundleId)
    {
        Condition.Requires(bundleId).IsGreaterThan(0);

        Manager.EntityManager.SP_RemoveResultBundle(bundleId);
    }

    public static void RemoveAllResultBundlesForInvoice(long invoiceId)
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);

        Manager.EntityManager.SP_RemoveAllResultBundlesForInvoice(invoiceId);
    }

    public static void CancelAllResultBundlesForInvoice(long invoiceId)
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);

        Manager.EntityManager.SP_CancelAllResultBundlesForInvoice(invoiceId);
    }

    public static bool OrderedTestHasResultItems(long ordTestId)
    {
        Condition.Requires(ordTestId).IsGreaterThan(0);
        var qry = Manager.EntityManager.SP_OrderedTestHasResultItems(ordTestId).FirstOrDefault();
        return qry != null && (bool)qry;
    }

    public static void UpdateResultBundleResultNotes(long bundleId, string notes)
    {
        Condition.Requires(bundleId).IsGreaterThan(0);

        byte[] lobContent = null;
        if (!string.IsNullOrEmpty(notes)) lobContent = CompressionUtils.CompressString(notes);

        Manager.EntityManager.SP_UpdateResultBundleResultNotes(bundleId, lobContent);
    }

    public static ResultBundleDetailsForReportGenerationSlice GetResultBundleDetailsForReportGeneration(
        long bundleId)
    {
        Condition.Requires(bundleId).IsGreaterThan(0);
        return Manager.EntityManager.SP_GetResultBundleDetailsForReportGeneration(bundleId).FirstOrDefault();
    }

    public static List<DiscreteResultLineItemsForReportGenerationSlice>
        GetDiscreteResultLineItemsForReportGeneration(long bundleId)
    {
        Condition.Requires(bundleId).IsGreaterThan(0);
        var query = Manager.EntityManager.SP_GetDiscreteResultLineItemsForReportGeneration(bundleId);
        return query != null ? query.ToList() : null;
    }

    public static bool VerifyResultBundleWorkflowStageMatch(long bundleId, WorkflowStageType wfStage)
    {
        Condition.Requires(bundleId).IsGreaterThan(0);
        var query = Manager.EntityManager.SP_VerifyResultBundleWorkflowStageMatch(bundleId, (byte)wfStage);
        return query != null && (bool)query.FirstOrDefault();
    }

    public static void UpdateResultBundleFinalizingConsultant(long bundleId, short consultantId)
    {
        Condition.Requires(bundleId).IsGreaterThan(0);
        Condition.Requires(consultantId).IsGreaterThan(0);

        Manager.EntityManager.SP_UpdateResultBundleFinalizingConsultant(bundleId, consultantId);
    }

    public static void UpdateResultBundleLabReportHeader(long bundleId, int headerId)
    {
        Condition.Requires(bundleId).IsGreaterThan(0);
        Condition.Requires(headerId).IsGreaterThan(0);

        Manager.EntityManager.SP_UpdateResultBundleLabReportHeader(bundleId, headerId);
    }

    public static void ScavengeOrphanedResultBundle(long bundleId,
        WorkflowStageType wfStage,
        TestResultType ignoreResultType)
    {
        Condition.Requires(bundleId).IsGreaterThan(0);

        Manager.EntityManager.SP_ScavengeOrphanedResultBundle(bundleId,
            (byte)wfStage,
            (byte)ignoreResultType);
    }

    public static string GetResultBundleWorkflowUserName(long bundleId, WorkflowStageType wfStage)
    {
        Condition.Requires(bundleId).IsGreaterThan(0);
        var users = Manager.EntityManager.SP_GetResultBundleWorkflowUserName(bundleId, (byte)wfStage);
        return users != null && users.Count() == 1 ? users.First() : string.Empty;
    }

    public static void UpdateResultBundleActiveStage(long bundleId, bool active, WorkflowStageType wfStage)
    {
        Condition.Requires(bundleId).IsGreaterThan(0);
        Manager.EntityManager.SP_UpdateResultBundleActiveStage(bundleId, active, (byte)wfStage);
    }

    public static ActiveResultBundleWithInvoiceDetailsSlice GetActiveResultBundleWithInvoiceDetails(long bundleId)
    {
        Condition.Requires(bundleId).IsGreaterThan(0);
        var query = Manager.EntityManager.SP_GetActiveResultBundleWithInvoiceDetails(bundleId);
        return query != null ? query.FirstOrDefault() : null;
    }
}