﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: InvoiceTransactionsRepository.cs 1248 2014-03-30 12:41:12Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using CuttingEdge.Conditions;
using LabMaestro.Shared;
using Manager = LabMaestro.Domain.DomainManager;

namespace LabMaestro.Domain;

public sealed class InvoiceTransactionsRepository : BaseRepository<InvoiceTransaction>
{
    public static IEnumerable<SimpleInvoiceTransactionInfo> FindInvoiceTransactions(long invoiceId)
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);
        return Manager.EntityManager.SP_GetTransactionsInInvoice(invoiceId).ToList();
    }

    public static IEnumerable<DetailedInvoiceTransactionInfo> FindInvoiceTransactionsDetailed(long invoiceId)
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);
        return Manager.EntityManager.SP_GetTransactionsInInvoiceDetailed(invoiceId).ToList();
    }

    public static IEnumerable<InvoiceTransactionDetailedExSlice> FindInvoiceTransactionsDetailedEx(long invoiceId)
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);
        return Manager.EntityManager.SP_GetTransactionsInInvoiceDetailedEx(invoiceId).ToList();
    }

    public static IEnumerable<TransactionsInShiftSlice> GetTransactionsInShift(int shiftId)
    {
        Condition.Requires(shiftId).IsGreaterThan(0);
        return Manager.EntityManager.SP_GetTransactionsInShiftEx(shiftId).ToList();
    }

    public static IEnumerable<SimpleInvoiceTransactionInfo> GetTransactionsInShiftEx(int shiftId)
    {
        Condition.Requires(shiftId).IsGreaterThan(0);
        return Manager.EntityManager.SP_GetTransactionsInShift(shiftId).ToList();
    }

    public static List<TransactionsInShiftDetailedSlice> GetTransactionsInShiftDetailed(int shiftId)
    {
        Condition.Requires(shiftId).IsGreaterThan(0);
        return Manager.EntityManager.SP_GetTransactionsInShiftDetailed(shiftId).ToList();
    }

    public static List<TransactionsInShiftDetailedExSlice> GetTransactionsInShiftDetailedEx(int shiftId)
    {
        Condition.Requires(shiftId).IsGreaterThan(0);
        return Manager.EntityManager.SP_GetTransactionsInShiftDetailedEx(shiftId).ToList();
    }

    public static InvoiceTransaction CreateNewTransaction(InvoiceMaster invoice,
        short userId,
        short? authorizerId,
        int workShiftId,
        InvoiceTransactionType txType,
        TransactionFlag txFlag,
        decimal txAmount,
        string userRemarks,
        int ipAddr)
    {
        long? txId = null;
        Manager.EntityManager.SP_CreateNewTransaction(invoice.InvoiceId,
            userId,
            authorizerId,
            workShiftId,
            (byte)txType,
            (byte)txFlag,
            txAmount,
            ipAddr,
            userRemarks,
            ref txId);

        return txId != null
            ? NotNullo(Manager.EntityManager.InvoiceTransactions.SingleOrDefault(x => x.Id == txId))
            : null;
    }

    public static InvoiceTransaction CreateNewTransactionEx(InvoiceMaster invoice,
        short userId,
        short? authorizerId,
        int workShiftId,
        InvoiceTransactionType txType,
        TransactionFlag txFlag,
        decimal txAmount,
        string userRemarks,
        int ipAddr,
        decimal nonCashAmount,
        PaymentMethod method,
        string? pmtSrc = null,
        string? pmtRef = null
    )
    {
        long? txId = null;
        Manager.EntityManager.SP_CreateNewTransactionEx(invoice.InvoiceId,
            userId,
            authorizerId,
            workShiftId,
            (byte)txType,
            (byte)txFlag,
            txAmount,
            ipAddr,
            userRemarks,
            nonCashAmount,
            (byte)method,
            pmtSrc,
            pmtRef,
            ref txId);

        return txId != null
            ? NotNullo(Manager.EntityManager.InvoiceTransactions.SingleOrDefault(x => x.Id == txId))
            : null;
    }

    public static List<TransactionsForUserDateRangeSlice> GetAllTransactionsForDateRange(DateTime startDate,
        DateTime endDate)
    {
        Condition.Requires(startDate).IsLessOrEqual(endDate);
        return Manager.EntityManager.SP_GetAllTransactionsForDateRange(startDate, endDate).ToList();
    }

    public static List<TransactionsForUserDateRangeSlice> GetAllTransactionsForUserDateRange(short userId,
        DateTime startDate,
        DateTime endDate)
    {
        Condition.Requires(userId).IsGreaterThan(0);
        Condition.Requires(startDate).IsLessOrEqual(endDate);

        return Manager.EntityManager.SP_GetAllTransactionsForUserDateRange(userId, startDate, endDate).ToList();
    }

    public static List<TransactionsForUserDateRangeSlice> GetAllTransactionsForRoleDateRange(short roleId,
        DateTime startDate,
        DateTime endDate)
    {
        Condition.Requires(roleId).IsGreaterThan(0);
        Condition.Requires(startDate).IsLessOrEqual(endDate);

        return Manager.EntityManager.SP_GetAllTransactionsForRoleDateRange(startDate, endDate, roleId).ToList();
    }

    public static List<TransactionDetailExSlice> GetAllTransactionsForRoleDateRangeEx(short roleId,
        DateTime startDate,
        DateTime endDate)
    {
        Condition.Requires(roleId).IsGreaterThan(0);
        Condition.Requires(startDate).IsLessOrEqual(endDate);

        return Manager.EntityManager.SP_GetAllTransactionsForRoleDateRangeEx(startDate, endDate, roleId).ToList();
    }

    public static List<InvoiceTransactionInDateRangeTypeReferrerCategorySlice> GetInvoiceTransactionsInDateRangeType
    (
        DateTime startDate,
        DateTime endDate,
        TransactionFlag txExclusionFlag,
        InvoiceTransactionType txType)
    {
        Condition.Requires(startDate).IsLessOrEqual(endDate);

        return Manager.EntityManager.SP_GetInvoiceTransactionsInDateRangeType((byte)txExclusionFlag,
            (byte)txType,
            startDate,
            endDate).ToList();
    }

    public static List<InvoiceTransactionInDateRangeTypeReferrerCategorySlice>
        GetInvoiceTransactionsInDateRangeTypeReferrerCategory(
            DateTime startDate,
            DateTime endDate,
            TransactionFlag txExclusionFlag,
            InvoiceTransactionType txType,
            short catId)
    {
        Condition.Requires(startDate).IsLessOrEqual(endDate);
        Condition.Requires(catId).IsGreaterThan(0);

        return Manager.EntityManager
            .SP_GetInvoiceTransactionsInDateRangeTypeReferrerCategory((byte)txExclusionFlag,
                (byte)txType,
                startDate,
                endDate,
                catId).ToList();
    }

    public static List<InvoiceTransactionInDateRangeTypeReferrerCategorySlice>
        GetInvoiceTransactionsInDateRangeTypeExcludeReferrerCategory(
            DateTime startDate,
            DateTime endDate,
            TransactionFlag txExclusionFlag,
            InvoiceTransactionType txType,
            short catId)
    {
        Condition.Requires(startDate).IsLessOrEqual(endDate);
        Condition.Requires(catId).IsGreaterThan(0);

        return
            Manager.EntityManager.SP_GetInvoiceTransactionsInDateRangeTypeExcludeReferrerCategory(
                (byte)txExclusionFlag,
                (byte)txType,
                startDate,
                endDate,
                catId).ToList();
    }

    public static FirstInvoiceTransactionInShiftByTypeFlagSlice GetFirstInvoiceTransactionByTypeFlag(
        long invoiceId,
        InvoiceTransactionType txType,
        TransactionFlag txFlag)
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);
        return Manager.EntityManager
            .SP_GetFirstInvoiceTransactionByTypeFlag(invoiceId,
                (byte)txType,
                (byte)txFlag)
            .SingleOrDefault();
    }

    public static decimal GetTransactionsSumByTypeFlag(long invoiceId,
        InvoiceTransactionType txType,
        TransactionFlag txFlag)
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);
        var result = Manager.EntityManager
            .SP_GetTransactionsSumByTypeFlag(invoiceId,
                (byte)txType,
                (byte)txFlag)
            .SingleOrDefault();
        return result ?? 0m;
    }

    public static decimal GetTransactionsSumByTypeFlagEndDate(long invoiceId,
        InvoiceTransactionType txType,
        TransactionFlag txFlag,
        DateTime endDate)
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);
        var result = Manager.EntityManager
            .SP_GetTransactionsSumByTypeFlagEndDate(invoiceId,
                endDate,
                (byte)txType,
                (byte)txFlag)
            .SingleOrDefault();
        return result ?? 0m;
    }

    public static List<TransactionInDateRangeSlice> GetAllNonMatchingTransactionsInDateRange(
        InvoiceTransactionType txType,
        DateTime dtFrom,
        DateTime dtTo)
    {
        Condition.Requires(dtFrom).IsLessOrEqual(dtTo);

        return Manager.EntityManager
            .SP_GetAllNonMatchingTransactionsInDateRange((byte)txType, dtFrom, dtTo)
            .ToList();
    }

    public static List<TransactionsOfTypeInDateRangeWithInvoiceSlice> GetTransactionsOfTypeInDateRangeWithPrimalInvoice(
        DateTime dtFrom,
        DateTime dtTo,
        InvoiceTransactionType txType)
    {
        Condition.Requires(dtFrom).IsLessOrEqual(dtTo);

        return Manager.EntityManager
            .SP_GetTransactionsOfTypeInDateRangeWithPrimalInvoice(dtFrom, dtTo, (byte)txType)
            .ToList();
    }

    public static List<TransactionsOfTypeInDateRangeWithInvoiceSlice>
        GetTransactionsOfTypeInDateRangeWithUpdatedInvoice(
            DateTime dtFrom,
            DateTime dtTo,
            InvoiceTransactionType txType)
    {
        Condition.Requires(dtFrom).IsLessOrEqual(dtTo);

        return Manager.EntityManager
            .SP_GetTransactionsOfTypeInDateRangeWithUpdatedInvoice(dtFrom, dtTo, (byte)txType)
            .ToList();
    }
}