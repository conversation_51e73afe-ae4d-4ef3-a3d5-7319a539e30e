﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: RolesRepository.cs 1248 2014-03-30 12:41:12Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using CuttingEdge.Conditions;
using Manager = LabMaestro.Domain.DomainManager;

namespace LabMaestro.Domain;

public class RolesRepository : BaseRepository<Role>
{
    public static IEnumerable<Role> FindAllRoles(bool activeOnly)
    {
        GuardAccess();

        return activeOnly
            ? Manager.EntityManager.Roles.Where(r => r.IsActive).OrderBy(r => r.Name).ToList()
            : Manager.EntityManager.Roles.OrderBy(r => r.Name).ToList();
    }

    public static Role FindRoleById(short id)
    {
        Condition.Requires(id).IsGreaterThan(0);
        GuardAccess();

        return NotNullo(Manager.EntityManager.Roles.Where(r => r.Id == id).SingleOrDefault());
    }

    public static Role FindRoleByCode(string roleCode)
    {
        Condition.Requires(roleCode).IsNotNullOrEmpty();
        GuardAccess();

        return NotNullo(Manager.EntityManager.Roles.Where(r => r.RoleCode == roleCode).SingleOrDefault());
    }

    public static Role CreateRole(string roleCode, string displayName, bool isActive, bool isAdmin)
    {
        Condition.Requires(roleCode).IsNotNullOrEmpty();
        Condition.Requires(displayName).IsNotNullOrEmpty();

        GuardAccess(false);

        var role = new Role
        {
            Name = displayName,
            RoleCode = roleCode,
            IsActive = isActive,
            IsAdmin = isAdmin
        };
        Manager.EntityManager.AddEntity(role);
        return role;
    }

    public static bool RoleHasPermission(string roleCode, string permCode)
    {
        Condition.Requires(roleCode).IsNotNullOrEmpty();
        Condition.Requires(permCode).IsNotNullOrEmpty();

        // first verify the role exists and is active
        var role = Manager.EntityManager.Roles.SingleOrDefault(r => r.IsActive && r.RoleCode == roleCode);
        return role != null && roleHasPermission(role, permCode);
    }

    public static bool RoleHasPermission(short roleId, string permCode)
    {
        Condition.Requires(roleId).IsGreaterThan(0);
        Condition.Requires(permCode).IsNotNullOrEmpty();

        // first verify the role exists and is active
        var role = Manager.EntityManager.Roles.SingleOrDefault(r => r.IsActive && r.Id == roleId);
        return role != null && roleHasPermission(role, permCode);
    }

    public static void RoleRemovePermission(short roleId, short permId)
    {
        Condition.Requires(roleId).IsGreaterThan(0);
        Condition.Requires(permId).IsGreaterThan(0);

        Manager.EntityManager.SP_RoleRemovePermission(roleId, permId);
    }

    private static bool roleHasPermission(Role role, string permCode)
    {
        // if this is an admin role, then just check if the permission is active
        if (role.IsAdmin)
        {
            var perm = Manager.EntityManager.Permissions.SingleOrDefault(p => p.PermCode == permCode);
            return perm != null && perm.IsActive;
        }

        // otherwise, check if the role is granted the permission
        var iReturnValue = Manager.EntityManager.SP_GetRolePermissionCount(role.Id, permCode).SingleOrDefault();
        return iReturnValue.HasValue && iReturnValue.Value > 0;
    }

    public static bool UserHasPermission(short userId, string permCode)
    {
        Condition.Requires(userId).IsGreaterThan(0);
        Condition.Requires(permCode).IsNotNullOrEmpty();

        var user =
            Manager.EntityManager.Users.Include(User.PathFor(u => u.Role)).SingleOrDefault(u => u.Id == userId);

        if (user == null) return false;

        if (!user.IsActive) return false;

        return user.Role != null && roleHasPermission(user.Role, permCode);
    }

    public static List<ActiveRoleWithPermissionSlice> GetActiveRolesWithPermissionCode(string permCode)
    {
        Condition.Requires(permCode).IsNotNullOrEmpty();

        return Manager.EntityManager.SP_GetActiveRolesWithPermissionCode(permCode).ToList();
    }
}