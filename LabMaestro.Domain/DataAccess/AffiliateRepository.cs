﻿using System.Collections.Generic;
using System.Linq;

namespace LabMaestro.Domain;

using Manager = DomainManager;

public sealed class AffiliateRepository : BaseRepository<Affiliate>
{
    public static IEnumerable<AffiliateSlice> FetchActive() =>
        Manager.EntityManager.SP_GetActiveAffiliates().ToList().OrderBy(x => x.Name);

    public static IEnumerable<AffiliateSlice> FetchAll() =>
        Manager.EntityManager.SP_GetAllAffiliates().ToList().OrderBy(x => x.Name);

    public static Affiliate? Find(short id) =>
        Manager.EntityManager.Affiliates.FirstOrDefault(c => c.Id == id);

    public static string? GetName(short id) =>
        Manager.EntityManager.Affiliates.FirstOrDefault(c => c.Id == id)?.Name;
}