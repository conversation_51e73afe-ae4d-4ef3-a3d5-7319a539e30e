﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: LabTestsRepository.cs 1256 2014-05-18 15:51:13Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System.Collections.Generic;
using System.Linq;
using C1.LiveLinq;
using CuttingEdge.Conditions;
using IdeaBlade.EntityModel;
using LabMaestro.Shared;
using Manager = LabMaestro.Domain.DomainManager;

namespace LabMaestro.Domain;

public sealed class LabTestsRepository : IndexedBaseRepository<LabTest>
{
    public static void Refresh()
    {
        GuardAccess();
        RefreshCache(fetchDataFromServer);
    }

    public static List<LabTest> FindActiveTests()
    {
        GuardAccess();
        ValidateCache(false, fetchDataFromServer);

        return indexedList.AsIndexed().Where(t => t.IsActive).ToList();
    }

    public static List<LabTest> FindAllTests()
    {
        GuardAccess();
        ValidateCache(false, fetchDataFromServer);

        return indexedList.ToList();
    }

    public static LabTest FindById(short testId)
    {
        GuardAccess();
        Condition.Requires(testId).IsGreaterThan(0);
        ValidateCache(false, fetchDataFromServer);

        var query = indexedList.AsIndexed().SingleOrDefault(u => u.Id == testId);
        return query;
    }

    public static LabTest FindTestWithLab(short testId)
    {
        GuardAccess();
        Condition.Requires(testId).IsGreaterThan(0);

        var query = Manager.EntityManager.LabTests
            .Include(t => t.PerformingLab)
            .SingleOrDefault(x => x.Id == testId);
        return query;
    }

    public static List<TestForPerformingLabSlice> FindAllTestsForPerformingLab(short labId)
    {
        GuardAccess();
        Condition.Requires(labId).IsGreaterThan(0);
        return Manager.EntityManager.SP_GetAllTestsForPerformingLab(labId).ToList();
    }

    public static LabTest FindActiveTestById(short testId)
    {
        GuardAccess();
        Condition.Requires(testId).IsGreaterThan(0);
        ValidateCache(false, fetchDataFromServer);

        var query = indexedList.AsIndexed().SingleOrDefault(u => u.Id == testId && u.IsActive);
        return query;
    }

    public static LabTest CreateNew(string shortName, decimal price)
    {
        GuardAccess(false);

        var test = new LabTest
        {
            ShortName = shortName,
            CanonicalName = shortName,
            ListPrice = price,
            IsActive = true,
            //NotOrderable = false,
            //PrintCanonicalName = false,
            ReportSortPriority = (byte)SortPriorityType.Normal,
            ReqSlipPrintOption = (byte)ReqSlipGenerationOptionType.GenerateSharedSlips
            //TestResultType = (byte) TestResultType.un
        };
        Manager.EntityManager.AddEntity(test);
        return test;
    }

    public static short CreateNewLabTest(string shortName, decimal price, short plab, short rlab)
    {
        short? id = 0;
        Manager.EntityManager.SP_CreateNewLabTest(true, shortName, shortName, price, plab, rlab, ref id);
        return (short)(id != null ? (short)id : 0);
    }

    internal static bool TestIsActive(int testId)
    {
        ValidateCache(false, fetchDataFromServer);

        var test = indexedList.AsIndexed().FirstOrDefault(x => x.Id == testId);
        return test != null && test.IsActive;
    }

    internal static bool TestIsOrderable(int testId)
    {
        ValidateCache(false, fetchDataFromServer);

        var test = indexedList.AsIndexed().FirstOrDefault(x => x.Id == testId);
        return test != null &&
               test.IsActive &&
               test.PerformingLab.IsActive &&
               test.ReportingLab.IsActive /* && !test.NotOrderable */;
    }

    private static List<LabTest> fetchDataFromServer()
    {
        return Manager.EntityManager.LabTests.Include(LabTest.PathFor(x => x.PerformingLab))
            .Include(LabTest.PathFor(x => x.ReportingLab)).ToList();
    }

    public static int GetLabTestCodeCount(string testCode)
    {
        return (int)Manager.EntityManager.SP_GetLabTestCodeCount(testCode).SingleOrDefault();
    }

    public static List<ReferralEligibleLabTestSlice> GetReferralEligibleLabTestsCatalog()
    {
        return Manager.EntityManager.SP_GetReferralEligibleLabTestsCatalog().ToList();
    }

    public static void UpdateLabTest(short id, bool active, string sku, string shortName,
        string canonName, decimal price, decimal subPrice, decimal costBasis,
        byte slipOpt, byte hl7, byte prio, string mnenonics, short plab,
        short rlab, short? tplId, short? tat, short? tplGrp, string rptLineGrp)
    {
        Manager.EntityManager.SP_UpdateLabTest(id, active, sku, shortName, canonName,
            price, subPrice, costBasis, slipOpt, hl7, prio, mnenonics,
            plab, rlab, tplId, tat, tplGrp, rptLineGrp);
    }
}