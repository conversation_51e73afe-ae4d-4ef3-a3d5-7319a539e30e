﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: BatteryComponentRepository.cs 1270 2014-05-20 05:28:47Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System.Collections.Generic;
using System.Linq;
using CuttingEdge.Conditions;
using LabMaestro.Shared;

namespace LabMaestro.Domain;

public sealed class BatteryComponentRepository : BaseRepository<BatteryComponent>
{
    private const string CACHE_KEY_FMT = @"BatteryComponentRepository_catalog_{0}";

    public static IEnumerable<short> GetAllTestIdsInBattery(int masterId)
    {
        Condition.Requires(masterId).IsGreaterThan(0);

        var details = DomainManager
            .EntityManager
            .BatteryComponents.Where(b => b.BatteryMasterId == masterId).ToList();
        var testIds = new List<short>();
        testIds.AddRange(details.Select(d => d.LabTestId));
        return testIds;
    }

    public static IEnumerable<BatteryComponentsDetailedSlice> GetBatteryComponentsDetailed(
        int masterId,
        bool nocache)
    {
        Condition.Requires(masterId).IsGreaterThan(0);

        IEnumerable<BatteryComponentsDetailedSlice> catalog = null;
        var cacheKey = string.Format(CACHE_KEY_FMT, masterId);

        if (!nocache)
            catalog = (IEnumerable<BatteryComponentsDetailedSlice>)AppCache.Get(cacheKey);
        else
            AppCache.Remove(cacheKey);

        if (catalog == null)
        {
            catalog = DomainManager.EntityManager.SP_GetBatteryComponentsDetailed(masterId).ToList();
            AppCache.Store(cacheKey, catalog, 30);
        }

        return catalog;
    }
}