﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: AuditTrailRepository.cs 1275 2014-05-20 10:32:23Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using CuttingEdge.Conditions;
using LabMaestro.Shared;

namespace LabMaestro.Domain;

public sealed class AuditTrailRepository : BaseRepository<AuditTrail>
{
    static AuditTrailRepository()
    {
        CurrentShiftId = -1;
        CurrentUserId = -1;
        CurrentIpAddress = -1;

        WorkShiftsRepository.OnShiftEvent += (n, b) => CurrentShiftId = b ? n : -1;
    }

    public static short CurrentUserId { get; private set; }

    public static int CurrentShiftId { get; private set; }

    public static int CurrentIpAddress { get; private set; }

    public static void InitializeCurrentContext(short userId, int shiftId, int ipAddress)
    {
        CurrentUserId = userId;
        CurrentShiftId = shiftId;
        CurrentIpAddress = ipAddress;
    }

    public static void LogEvent(AuditEventCategory evCat, AuditEventType evType,
        WorkflowStageType? stage = null,
        PatientLabOrder order = null,
        OrderedTest test = null,
        ResultBundle bundle = null,
        string note = null)
    {
        GuardAccess(false);
        byte? workflowStage = (byte)WorkflowStageType.Unknown;
        if (stage != null) workflowStage = (byte)stage;

        long? orderId = null;
        if (order != null) orderId = order.InvoiceId;

        long? testId = null;
        if (test != null) testId = test.Id;

        long? bundleId = null;
        if (bundle != null) bundleId = bundle.Id;

        DomainManager.EntityManager.SP_InsertAuditRecord(
            (byte)evCat,
            (short)evType,
            workflowStage,
            CurrentUserId,
            CurrentShiftId,
            orderId,
            testId,
            bundleId,
            CurrentIpAddress,
            note);
    }

    public static void LogGenericEvent(AuditEventCategory evCat,
        AuditEventType evType,
        WorkflowStageType? stage = null,
        short? userId = null,
        int? shiftId = null,
        long? orderId = null,
        long? orderedTestId = null,
        long? bundleId = null,
        string note = null)
    {
        logEventInternal(evCat, evType, stage, userId, shiftId, orderId, orderedTestId, bundleId, note);
    }


    private static void logEventInternal(AuditEventCategory evCat,
        AuditEventType evType,
        WorkflowStageType? stage = null,
        short? userId = null,
        int? shiftId = null,
        long? orderId = null,
        long? orderedTestId = null,
        long? bundleId = null,
        string note = null)
    {
        GuardAccess(false);
        byte? workflowStage = (byte)WorkflowStageType.Unknown;
        if (stage != null) workflowStage = (byte)stage;

        DomainManager.EntityManager.SP_InsertAuditRecord(
            (byte)evCat,
            (short)evType,
            workflowStage,
            userId,
            shiftId,
            orderId,
            orderedTestId,
            bundleId,
            CurrentIpAddress,
            note);
    }

    public static void LogSystemEvent(AuditEventCategory evCat,
        AuditEventType evType,
        WorkflowStageType? stage = null,
        long? orderId = null,
        long? orderedTestId = null,
        long? bundleId = null,
        string note = null)
    {
        logEventInternal(evCat, evType, stage, null, null,
            orderId, orderedTestId, bundleId, note);
    }

    public static void LogSystemEvent(AuditEventType evType,
        WorkflowStageType? stage = null,
        long? orderId = null,
        long? orderedTestId = null,
        long? bundleId = null,
        string note = null)
    {
        logEventInternal(AuditEventCategory.System, evType, stage, null, null,
            orderId, orderedTestId, bundleId, note);
    }

    public static void LogCatalogEvent(AuditEventType evType, string note)
    {
        logEventInternal(AuditEventCategory.Catalog, evType, userId: CurrentUserId, note: note);
    }

    /// <summary>
    ///     Logs the shift start/end event.
    /// </summary>
    /// <param name="startShift"> True if starting, false if ending the current shift </param>
    public static void LogShiftEvent(bool startShift)
    {
        LogEvent(AuditEventCategory.Staff,
            startShift ? AuditEventType.stfShiftStarted : AuditEventType.stfShiftEnded);
    }

    public static void LogLabOrderEvent(PatientLabOrder order, AuditEventType evType,
        WorkflowStageType? wfStage = null, OrderedTest test = null)
    {
        LogEvent(AuditEventCategory.OrderEntry, evType, wfStage, order, test);
    }

    public static void LogLabOrderEvent(long invoiceId, AuditEventType evType,
        WorkflowStageType? wfStage = null, string note = null)
    {
        logEventInternal(AuditEventCategory.OrderEntry, evType, wfStage, CurrentUserId, CurrentShiftId, invoiceId,
            note: note);
    }

    public static void LogFinancialEvent(PatientLabOrder order, AuditEventType evType, decimal amount,
        WorkflowStageType wfStage)
    {
        LogEvent(AuditEventCategory.Financial, evType, order: order, stage: wfStage,
            note: SharedUtilities.MoneyToString(amount, true));
    }

    public static void LogOrderWorkflowEvent(AuditEventType evType, long? labOrderId, long? bundleId,
        long? orderedTestId, WorkflowStageType wfStage, string notes = null)
    {
        var userId = CurrentUserId != -1 ? CurrentUserId : (short?)null;
        var shiftId = CurrentShiftId != -1 ? CurrentShiftId : (int?)null;
        logEventInternal(AuditEventCategory.Workflow, evType, wfStage, userId,
            shiftId, labOrderId, orderedTestId, bundleId, notes);
    }

    public static List<AuditTrailSlice> GetAuditRecordForInvoiceByType(long invoiceId, AuditEventType evType)
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);
        var query = DomainManager.EntityManager.SP_GetAuditRecordForInvoiceByType(invoiceId, (short)evType);
        return query != null ? query.ToList() : null;
    }

    public static List<AuditTrailSlice> GetAuditRecordsForResultBundle(long bundleId, AuditEventCategory evCategory)
    {
        Condition.Requires(bundleId).IsGreaterThan(0);
        var query = DomainManager.EntityManager.SP_GetAuditRecordsForResultBundle(bundleId, (byte)evCategory);
        return query != null ? query.ToList() : null;
    }

    public static List<AuditTrailSlice> GetAuditRecordsForInvoice(long invoiceId, AuditEventCategory evCategory)
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);
        var query = DomainManager.EntityManager.SP_GetAuditRecordsForInvoice(invoiceId, (byte)evCategory);
        return query != null ? query.ToList() : null;
    }

    public static List<AuditTrailSlice> GetAllAuditRecordsForInvoice(long invoiceId)
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);
        var query = DomainManager.EntityManager.SP_GetAllAuditRecordsForInvoice(invoiceId);
        return query != null ? query.ToList() : null;
    }

    public static List<AuditTrailSlice> GetAuditRecordForOrderedTestByType(long ordTestId, AuditEventType evType)
    {
        Condition.Requires(ordTestId).IsGreaterThan(0);
        var query = DomainManager.EntityManager.SP_GetAuditRecordForOrderedTestByType(ordTestId, (short)evType);
        return query != null ? query.ToList() : null;
    }

    public static IEnumerable<AuditRecordsInCategoryByDateRangeSlice> GetAuditRecordsInCategoryByDateRange(
        AuditEventCategory evCat, DateTime dtFrom, DateTime dtTill)
    {
        var query = DomainManager.EntityManager.SP_GetAuditRecordsInCategoryByDateRange((byte)evCat, dtFrom, dtTill);
        return query != null ? query.ToList() : null;
    }

    public static List<long?> GetDistinctAuditedInvoicesInDateRange(
        AuditEventCategory evCat, DateTime dtFrom, DateTime dtTill)
    {
        var query = DomainManager.EntityManager.SP_GetDistinctAuditedInvoicesInDateRange(
            (byte)evCat,
            dtFrom,
            dtTill);
        return query != null ? query.ToList() : null;
    }

    public static List<long?> GetDistinctAuditedResultBundlesInDateRange(
        AuditEventCategory evCat, DateTime dtFrom, DateTime dtTill)
    {
        var query = DomainManager.EntityManager.SP_GetDistinctAuditedResultBundlesInDateRange(
            (byte)evCat,
            dtFrom,
            dtTill);
        return query != null ? query.ToList() : null;
    }
}