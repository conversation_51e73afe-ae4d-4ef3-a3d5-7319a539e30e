﻿using System.Collections.Generic;
using System.Linq;
using C1.LiveLinq;

namespace LabMaestro.Domain;

public sealed class ActiveReferringPhysiciansRepository : CachedRepositoryBase<ReferrerSlice>
{
    private const string CacheKey = @"referrer_catalog_active";

    public static List<ReferrerSlice> GetCatalog(bool disableCache) =>
        LoadCatalog(<PERSON><PERSON><PERSON><PERSON>, disableCache, fetchFromServer).ToList();

    public static string GetPhysicianName(int id)
    {
        var item = LoadCatalog(CacheKey, false, fetchFromServer).AsIndexed().SingleOrDefault(x => x.Id == id);
        return item != null ? item.FullName : string.Empty;
    }

    public static ReferrerSlice FindByName(string name)
    {
        //TODO: Lucene searching
        return null;
    }

    private static IEnumerable<ReferrerSlice> fetchFromServer() =>
        DomainManager.EntityManager.SP_GetAllActiveReferrers().ToList();
}