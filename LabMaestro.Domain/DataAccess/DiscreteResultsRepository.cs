﻿//  * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
//  
//  Copyright (c) 2012, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
//  All rights reserved.
//  
//  Redistribution and use in source and binary forms, with or without
//  modification, are permitted provided that the following conditions are met:
//     * Redistributions of source code must retain the above copyright
//       notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above copyright
//       notice, this list of conditions and the following disclaimer in the
//       documentation and/or other materials provided with the distribution.
//     * Neither the name of the <organization> nor the
//       names of its contributors may be used to endorse or promote products
//       derived from this software without specific prior written permission.
//  
//  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
//  ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
//  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
//  DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
//  DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
//  (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
//  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
//  ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
//  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
//  SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
//  
//  $Id: DiscreteResultsRepository.cs 718 2013-07-02 14:48:27Z <EMAIL> $
//  
//  * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System.Collections.Generic;
using System.Linq;
using CuttingEdge.Conditions;
using Manager = LabMaestro.Domain.DomainManager;

namespace LabMaestro.Domain;

public sealed class DiscreteResultsRepository : BaseRepository<DiscreteResultLineItem>
{
    public static List<DiscreteReportLineItemsForOrderedTest> GetDiscreteReportLineItemsForOrderedTest(
        long ordTestId)
    {
        Condition.Requires(ordTestId).IsGreaterThan(0);

        var query = Manager.EntityManager.SP_GetDiscreteReportLineItemsForOrderedTest(ordTestId);
        return query == null ? null : query.ToList();
    }

    public static List<DiscreteReportLineItemForTestSlice> GetAllDiscreteReportLineItemsForTest(short testId)
    {
        Condition.Requires(testId).IsGreaterThan(0);

        var query = Manager.EntityManager.SP_GetAllDiscreteReportLineItemsForTest(testId);
        return query == null ? null : query.ToList();
    }

    public static List<GetDiscreteResultLineItemsForOrderedTest> GetDiscreteResultLineItemsForOrderedTest(
        long ordTestId, long resultBundleId)
    {
        Condition.Requires(ordTestId).IsGreaterThan(0);
        Condition.Requires(resultBundleId).IsGreaterThan(0);

        var query = Manager.EntityManager.SP_GetDiscreteResultLineItemsForOrderedTest(ordTestId, resultBundleId);
        return query == null ? null : query.ToList();
    }


    public static void InsertDiscreteResultLineItem(long ordTestId, long resultBundleId, string param, string result,
        string units,
        string refRange, byte sortOrder, byte indent, bool isResultable)
    {
        Condition.Requires(ordTestId).IsGreaterThan(0);
        Condition.Requires(resultBundleId).IsGreaterThan(0);
        Condition.Requires(param).IsNotNullOrEmpty();

        Manager.EntityManager.SP_InsertDiscreteResultLineItem(ordTestId, resultBundleId, param, result, units, refRange,
            sortOrder, indent, isResultable);
    }

    public static DiscreteResultLineItem FindById(long itemId)
    {
        return NotNullo(Manager.EntityManager.DiscreteResultLineItems.SingleOrDefault(x => x.Id == itemId));
    }

    public static void UpdateDiscreteResultLineItem(long itemId, string param, string units, string result,
        string refRange, string flags)
    {
        Manager.EntityManager.SP_UpdateDiscreteResultLineItem(itemId, param, units, result, refRange, flags);
    }
}