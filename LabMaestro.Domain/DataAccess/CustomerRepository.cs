﻿using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using IdeaBlade.EntityModel;
using LabMaestro.Shared;
using Manager = LabMaestro.Domain.DomainManager;

namespace LabMaestro.Domain;

public sealed class CustomerRepository : BaseRepository<Customer>
{
    public static bool UPINExists(string upin) =>
        Manager.EntityManager.Customers.FirstOrDefault(c => c.UPIN == upin.ToUpperInvariant()) != null;

    public static bool PhoneExists(string phone) =>
        Manager.EntityManager.Customers.FirstOrDefault(c => c.Phone == phone) != null;

    public static int Count() => Manager.EntityManager.Customers.Count();

    public static IEnumerable<Customer> FindByUPIN(string upin) =>
        Manager.EntityManager.Customers.Where(c => c.UPIN == upin.ToUpperInvariant()).ToList();

    public static bool LoginOrPhoneAlreadyRegistered(string login, string phone) =>
        Manager.EntityManager.SP_GetRegisteredLoginOrPhoneCount(login, phone).FirstOrDefault() is 0;

    public static IEnumerable<Customer> FindByLogin(string login) =>
        Manager.EntityManager.Customers.Where(c => c.Login == login.ToUpperInvariant()).ToList();

    public static Customer? FindByLoginFirst(string login) =>
        Manager.EntityManager.Customers.FirstOrDefault(c => c.Login == login.ToUpperInvariant());

    public static IEnumerable<Customer> FindByPhone(string phone) =>
        Manager.EntityManager.Customers.Where(c => c.Phone == phone.ToUpperInvariant()).ToList();

    public static IEnumerable<Customer> FindByRegistrationDate(DateTime dt) =>
        Manager.EntityManager.Customers.Where(c => c.EnrolledOn == dt).ToList();

    public static Customer? FindByPhoneFirst(string phone) =>
        Manager.EntityManager.Customers.FirstOrDefault(c => c.Phone == phone.ToUpperInvariant());

    public static IEnumerable<Customer> GetAll() =>
        Manager.EntityManager.Customers.ToList();

    public static List<string> SimilarLoginNames(string login) =>
        Manager.EntityManager.Customers
            .Where(c => DbFunctions.Like(c.Login, $"{login.ToUpperInvariant()}%"))
            .Select(c => c.Login)
            .ToList();


    public static Customer FindById(long id) => Manager.EntityManager.Customers.SingleOrDefault(c => c.Id == id);

    public static Customer CreateFrom(CustomerInfo info, string password)
    {
        var customer = new Customer
        {
            IsActive = true,
            UPIN = info.UPIN,
            Title = info.Title,
            FirstName = info.FirstName,
            LastName = info.LastName,
            Sex = (byte)info.Sex,
            Age = info.Age,
            Phone = info.Phone,
            Email = info.Email,
            Login = info.Login.ToLowerInvariant(),
            PassHash = AuthUtils.EncodePassword(password),
            DoB = info.DoB,
            EnrolledOn = info.EnrolledOn,
            EnrollingUserId = info.EnrollingUserId,
            CorporateClientId = info.CorporateClientId,
            NumOrders = 0,
            CLV = 0,
            WebAccess = true,
        };
        Add(customer);
        Save();

        return customer;
    }
}