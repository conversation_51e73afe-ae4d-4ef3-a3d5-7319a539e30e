// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2012, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: IndexedBaseRepository.cs 718 2013-07-02 14:48:27Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using C1.LiveLinq.Collections;
using CuttingEdge.Conditions;
using IdeaBlade.EntityModel;
using LabMaestro.Shared;

namespace LabMaestro.Domain;

public abstract class IndexedBaseRepository<T> : BaseRepository<T> where T : Entity
{
    protected static readonly IndexedCollection<T> indexedList = new();
    protected static DateTime indexLastUpdated = DateTime.MinValue;

    protected static void Invalidate() => indexLastUpdated = DateTime.MinValue;

    protected static void RefreshCache(OnFetchDataFromServer fetchProc)
    {
        Condition.Requires(fetchProc).IsNotNull();

        indexedList.BeginUpdate();
        try
        {
            indexedList.Clear();
            var list = fetchProc();
            indexedList.AddRange(list);
            indexLastUpdated = DateTime.Now;
        }
        finally
        {
            indexedList.EndUpdate();
        }
    }

    protected static void ValidateCache(bool forceUpdate, OnFetchDataFromServer fetchProc)
    {
        var bShouldUpdate = forceUpdate;

        if (!bShouldUpdate) bShouldUpdate = indexedList.Count == 0;

        if (!bShouldUpdate) bShouldUpdate = SharedUtilities.ShouldRefreshDataCache(indexLastUpdated);

        if (bShouldUpdate) RefreshCache(fetchProc);
    }

    protected delegate List<T> OnFetchDataFromServer();
}