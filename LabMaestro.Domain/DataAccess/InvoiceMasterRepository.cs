﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: InvoiceMasterRepository.cs 1248 2014-03-30 12:41:12Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using CuttingEdge.Conditions;
using LabMaestro.Shared;
using Manager = LabMaestro.Domain.DomainManager;

namespace LabMaestro.Domain;

public sealed class InvoiceMasterRepository : BaseRepository<InvoiceMaster>
{
    public static IEnumerable<ShiftInvoiceSummary> GetInvoiceSummariesInShift(int shiftId)
    {
        Condition.Requires(shiftId).IsGreaterThan(0);

        var query = Manager.EntityManager.SP_GetInvoicesInShift(shiftId);
        return query.ToList();
    }

    public static InvoiceMaster FindInvoiceById(long invoiceId)
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);

        return Manager.EntityManager.InvoiceMasters.SingleOrDefault(x => x.InvoiceId == invoiceId);
    }

    public static InvoiceFinancialDetailsSlice GetInvoiceFinancialDetails(long invoiceId)
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);

        return Manager.EntityManager.SP_GetInvoiceFinancialDetails(invoiceId).SingleOrDefault();
    }

    public static ShiftInvoiceSummary GetInvoiceSummary(long invoiceId)
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);

        return Manager.EntityManager.SP_GetInvoiceSummary(invoiceId).SingleOrDefault();
    }

    public static FirstInvoiceTransactionInShiftByTypeFlagSlice GetFirstInvoiceTransactionInShiftByTypeFlag(
        long invoiceId,
        InvoiceTransactionType txType,
        TransactionFlag txFlag)
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);
        return Manager.EntityManager
            .SP_GetFirstInvoiceTransactionByTypeFlag(invoiceId, (byte)txType, (byte)txFlag)
            .SingleOrDefault();
    }

    public static List<PrimalInvoiceSlice> GetPrimalInvoicesByDateRange(DateTime dtFrom, DateTime dtTo)
    {
        return Manager.EntityManager.SP_GetPrimalInvoicesByDateRange(dtFrom, dtTo).ToList();
    }

    public static List<InvoiceMaster> GetInvoiceMastersByDateRange(DateTime dtFrom, DateTime dtTo)
    {
        return Manager.EntityManager.InvoiceMasters
            .Where(i => i.DateCreated >= dtFrom && i.DateCreated <= dtTo)
            .ToList();
    }
}