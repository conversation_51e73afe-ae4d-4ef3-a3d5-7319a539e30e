﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: TemplateReportsRepository.cs 810 2013-07-13 12:31:31Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System.Collections.Generic;
using System.Linq;
using CuttingEdge.Conditions;
using LabMaestro.Shared;
using Manager = LabMaestro.Domain.DomainManager;

namespace LabMaestro.Domain;

public sealed class TemplateReportsRepository : BaseRepository<TemplateReport>
{
    public static short? CreateNewTemplateReport(string name, string tags, string content, SortPriorityType priority)
    {
        Condition.Requires(name).IsNotNullOrEmpty();
        Condition.Requires(content).IsNotNullOrEmpty();

        var lobContent = CompressionUtils.CompressString(content);
        short? newId = null;
        Manager.EntityManager.SP_CreateNewTemplateReport(name, tags, (byte)priority, lobContent, ref newId);
        return newId;
    }

    public static void UpdateTemplateReportContent(short templateId, string content)
    {
        Condition.Requires(templateId).IsGreaterThan(0);
        Condition.Requires(content).IsNotNullOrEmpty();

        var lobContent = CompressionUtils.CompressString(content);
        Manager.EntityManager.SP_UpdateTemplateReportContent(templateId, lobContent);
    }

    public static void UpdateTemplateReport(short id, bool active, string name, SortPriorityType sort, string tags)
    {
        Condition.Requires(id).IsGreaterThan(0);
        Condition.Requires(name).IsNotNullOrEmpty();

        Manager.EntityManager.SP_UpdateTemplateReport(id, active, name, (byte)sort, tags);
    }

    public static string GetTemplateReportContent(short id)
    {
        Condition.Requires(id).IsGreaterThan(0);
        var contentBytes = new byte[] { };
        var result = string.Empty;
        Manager.EntityManager.SP_GetTemplateReportContent(id, ref contentBytes);
        if (contentBytes.Length > 0) result = CompressionUtils.DecompressToString(contentBytes);
        return result;
    }

    public static string GetDefaultTemplateContentForLabTest(short testId)
    {
        Condition.Requires(testId).IsGreaterThan(0);

        var contentBytes = new byte[] { };
        var result = string.Empty;
        Manager.EntityManager.SP_GetDefaultTemplateContentForLabTest(testId, ref contentBytes);
        if (contentBytes.Length > 0) result = CompressionUtils.DecompressToString(contentBytes);
        return result;
    }

    public static List<TemplateReportSlice> GetTemplatesListForLabTest(short testId)
    {
        Condition.Requires(testId).IsGreaterThan(0);

        var query = Manager.EntityManager.SP_GetTemplatesListForLabTest(testId);
        return query != null ? query.Select(TemplateReportSlice.AssembleFrom).ToList() : null;
    }

    public static TemplateReport FindById(short templateId)
    {
        Condition.Requires(templateId).IsGreaterThan(0);
        return NotNullo(Manager.EntityManager.TemplateReports.SingleOrDefault(x => x.Id == templateId));
    }

    public static List<TemplateReportSlice> GetAllTemplateReports()
    {
        return Manager.EntityManager.SP_GetAllTemplateReports().ToList();
    }

    public static List<TemplateReportGroupMembershipSlice> GetGroupMembershipsForTemplateReport(short reportId)
    {
        Condition.Requires(reportId).IsGreaterThan(0);
        return Manager.EntityManager.SP_GetGroupMembershipsForTemplateReport(reportId).ToList();
    }

    public static void CreateNewTemplateReportGroupLink(short reportId, short groupId)
    {
        Condition.Requires(reportId).IsGreaterThan(0);
        Condition.Requires(groupId).IsGreaterThan(0);

        Manager.EntityManager.SP_CreateNewTemplateReportGroupLink(reportId, groupId);
    }

    public static void DeleteMembershipLink(short linkId)
    {
        Condition.Requires(linkId).IsGreaterThan(0);
        var link = Manager.EntityManager.TemplateGroupLinks.SingleOrDefault(x => x.Id == linkId);
        if (link != null)
        {
            link.EntityAspect.Delete();
            Save();
        }
    }

    public static List<TemplateReportSlice> GetAllTemplateReportsInGroup(short groupId)
    {
        Condition.Requires(groupId).IsGreaterThan(0);

        return Manager.EntityManager.SP_GetAllTemplateReportsInGroup(groupId).ToList();
    }

    public static List<TemplateReportSlice> GetAllActiveTemplateReportsInGroup(short groupId)
    {
        Condition.Requires(groupId).IsGreaterThan(0);

        return Manager.EntityManager.SP_GetAllActiveTemplateReportsInGroup(groupId).ToList();
    }
}