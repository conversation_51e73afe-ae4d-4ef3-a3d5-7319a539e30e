﻿//  * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
//  
//  Copyright (c) 2012, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
//  All rights reserved.
//  
//  Redistribution and use in source and binary forms, with or without
//  modification, are permitted provided that the following conditions are met:
//     * Redistributions of source code must retain the above copyright
//       notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above copyright
//       notice, this list of conditions and the following disclaimer in the
//       documentation and/or other materials provided with the distribution.
//     * Neither the name of the <organization> nor the
//       names of its contributors may be used to endorse or promote products
//       derived from this software without specific prior written permission.
//  
//  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
//  ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
//  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
//  DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
//  DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
//  (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
//  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
//  ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
//  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
//  SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
//  
//  $Id: GlobalSettingsRepository.cs 718 2013-07-02 14:48:27Z <EMAIL> $
//  
//  * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System.Collections.Generic;
using System.Linq;
using C1.LiveLinq;
using CuttingEdge.Conditions;
using Manager = LabMaestro.Domain.DomainManager;

namespace LabMaestro.Domain;

public sealed class GlobalSettingsRepository : IndexedBaseRepository<GlobalSetting>
{
    public static void Refresh() => RefreshCache(fetchFreshDataFromServer);

    private static void ensureValidity(string key, bool disableCache)
    {
        key = key.Trim().ToLowerInvariant();
        Condition.Requires(key).IsNotNullOrEmpty();
        if (!disableCache) ValidateCache(false, fetchFreshDataFromServer);
    }

    public static GlobalSetting? FindByKey(string key, bool disableCache = false)
    {
        ensureValidity(key, disableCache);

        return indexedList.AsIndexed().SingleOrDefault(x => string.CompareOrdinal(x.SettingsKey, key) == 0);
    }

    public static string GetStringValue(string key, string valDefault = "")
    {
        var query = FindByKey(key);
        return query == null || string.IsNullOrEmpty(query.StrValue) ? valDefault : query.StrValue;
    }

    public static int GetIntValue(string key, int defaultValue = 0)
    {
        var query = FindByKey(key);
        return query is not { IntValue: >= 0 } ? defaultValue : query.IntValue;

        /*
        var value = indexedList.AsIndexed()
            .Where(x => string.CompareOrdinal(x.SettingsKey, key) == 0)
            .Select(x => x.IntValue)
            .SingleOrDefault();
        return value <= 0 ? defaultValue : value;
        */
    }

    public static void Save(string key, int intVal, string? strVal)
    {
        GuardAccess(false);
        ensureValidity(key, true);

        Manager.EntityManager.SP_UpsertGlobalSettings(key, intVal, strVal);
        Refresh();
    }

    public static void Remove(string key)
    {
        GuardAccess(false);

        var setting = FindByKey(key, true);
        if (setting == null) return;
        Manager.EntityManager.RemoveEntity(setting);
        Manager.EntityManager.SaveChanges();
        Refresh();
    }

    private static List<GlobalSetting> fetchFreshDataFromServer()
    {
        Manager.EntityManager.Clear();
        return Manager.EntityManager.GlobalSettings.ToList();
    }
}