﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: OrderedBillableItemsRepository.cs 1248 2014-03-30 12:41:12Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using CuttingEdge.Conditions;

namespace LabMaestro.Domain;

public sealed class OrderedBillableItemsRepository : BaseRepository<OrderedBillableItem>
{
    public static OrderedBillableItem OrderItem(PatientLabOrder order, short itemId, short quantity,
        bool isCancelled)
    {
        Condition.Requires(itemId).IsGreaterThan(0);
        var item = BillableItemsRepository.FindActiveItemById(itemId);
        return OrderItem(order, item, quantity, isCancelled);
    }

    public static OrderedBillableItem OrderItem(PatientLabOrder order, BillableItem item, short quantity,
        bool isCancelled)
    {
        Condition.Requires(order)
            .IsNotNull();
        //.Evaluate(x => x.EntityAspect.EntityState == EntityState.AllButDetached);

        Condition.Requires(item)
            .IsNotNull();
        //.Evaluate(x => x.EntityAspect.EntityState == EntityState.AllButDetached);

        var result = new OrderedBillableItem
        {
            BillableItem = item,
            PatientLabOrder = order,
            IsCancelled = false,
            Quantity = quantity,
            UnitPrice = item.UnitPrice
        };
        DomainManager.EntityManager.AddEntity(result);
        return result;
    }

    public static void CreateNewOrderedBillableItem(long invoiceId, short itemId, decimal price, short quantity)
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);
        Condition.Requires(itemId).IsGreaterThan(0);
        Condition.Requires(price).IsGreaterThan(0);
        Condition.Requires(quantity).IsGreaterThan(0);

        DomainManager.EntityManager.SP_CreateNewOrderedBillableItem(invoiceId, itemId, price, quantity);
    }

    public static List<InvoiceOrderedItem> GetActiveOrderedItemsInInvoice(long invoiceId)
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);

        var query = DomainManager.EntityManager.SP_GetActiveOrderedItemsInInvoice(invoiceId);
        return query.ToList();
    }

    public static List<OrderedBillableItemInInvoiceSlice> GetAllOrderedBillableItemsInInvoice(long invoiceId)
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);

        var query = DomainManager.EntityManager.SP_GetAllOrderedBillableItemsInInvoice(invoiceId);
        return query.ToList();
    }

    public static void RemoveAllBillableItemsInInvoice(long invoiceId)
    {
        Condition.Requires(invoiceId).IsGreaterThan(0);

        DomainManager.EntityManager.SP_RemoveAllBillableItemsInInvoice(invoiceId);
    }

    public static List<OrderedBillableItemInDateRangeSlice> SearchOrderedBillableItemsInDateRange(DateTime dtStart,
        DateTime dtEnd)
    {
        Condition.Requires(dtStart).IsLessThan(dtEnd);
        return DomainManager.EntityManager
            .SP_SearchOrderedBillableItemsInDateRange(dtStart, dtEnd)
            .ToList();
    }
}