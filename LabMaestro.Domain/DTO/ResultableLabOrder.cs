﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ResultableLabOrder.cs 1248 2014-03-30 12:41:12Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using LabMaestro.Shared;

namespace LabMaestro.Domain;

public sealed class ResultableLabOrder
{
    public ResultableLabOrder() => ResultBundles = [];

    public long InvoiceId { get; set; }

    public string OrderId { get; set; }

    public DateTime OrderDateTime { get; set; }

    public string PatientName { get; set; }

    public SexType Sex { get; set; }

    public string AgeDoBLabel { get; set; }

    public string AgeDoBContent { get; set; }

    public string ReferringPhysician { get; set; }

    public List<ResultBundleSlice> ResultBundles { get; }

    public WorkflowStageType WorkflowStage { get; set; }
    public string CorporateClientName { get; set; }
    public string AffiliateName { get; set; }
    public string AssociateLabName { get; set; }
    public string CustomerUPIN { get; set; }
    public string AffiliateUPIN { get; set; }
    public string CorporateClientUID { get; set; }
    public string AssociateLabAccessionId { get; set; }


    public bool HasResultBundles => ResultBundles.Count > 0;

    public bool HasUnresultedTests => hasPendingWorkflowLevel(WorkflowStageType.ResultEntry);

    public bool HasUnvalidatedTests => hasPendingWorkflowLevel(WorkflowStageType.ResultValidation);

    public bool HasUnfinalizedTests => hasPendingWorkflowLevel(WorkflowStageType.ReportFinalization);

    public static ResultableLabOrder AssembleFrom(LabOrderFullDetailsSlice order)
    {
        var labOrd = new ResultableLabOrder
        {
            InvoiceId = order.InvoiceId,
            OrderId = order.OrderId,
            OrderDateTime = order.OrderDateTime,
            PatientName = order.FullName,
            Sex = (SexType)order.Sex,
            AgeDoBLabel = order.GetAgeOrDoBPrintLabel(),
            AgeDoBContent = order.GetAgeOrDoB(),
            ReferringPhysician = order.ReferrerName,
            WorkflowStage = (WorkflowStageType)order.WorkflowStage,
            CustomerUPIN = order.CustomerUPIN,
            CorporateClientName = order.CorporateClientName,
            CorporateClientUID = order.CorporateClientUID,
            AssociateLabName = order.AssociateLabName,
            AffiliateName = order.AffiliateName,
            AffiliateUPIN = order.AffiliateUPIN,
            AssociateLabAccessionId = order.AssociateLabAccessionId,
        };
        return labOrd;
    }

    private bool hasPendingWorkflowLevel(WorkflowStageType wfStage) => ResultBundles.Any(slice => slice.OrderedTests.Any(test => test.WorkflowStage < wfStage));
}