﻿//  * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
//  
//  Copyright (c) 2012, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
//  All rights reserved.
//  
//  Redistribution and use in source and binary forms, with or without
//  modification, are permitted provided that the following conditions are met:
//     * Redistributions of source code must retain the above copyright
//       notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above copyright
//       notice, this list of conditions and the following disclaimer in the
//       documentation and/or other materials provided with the distribution.
//     * Neither the name of the <organization> nor the
//       names of its contributors may be used to endorse or promote products
//       derived from this software without specific prior written permission.
//  
//  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
//  ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
//  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
//  DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
//  DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
//  (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
//  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
//  ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
//  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
//  SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
//  
//  $Id: DeliveryTimeEstimator.cs 1248 2014-03-30 12:41:12Z <EMAIL> $
//  
//  * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using LabMaestro.Shared;

namespace LabMaestro.Domain;

internal static class DeliveryTimeEstimator
{
    private static DateTime changeTime(DateTime value, decimal tm)
    {
        int hours, minutes = 0;
        SharedUtilities.DecimalToHourMinutes(tm, out hours, out minutes);
        return new DateTime(value.Year, value.Month, value.Day, hours, minutes, 0);
    }

    public static DateTime EstimateDeliveryTime(OrderableTestSlice ordTest, DateTime bookingTime)
    {
        var hoursToAdd = 0;
        var daysToAdd = 0;

        if (ordTest.HoursRequired > 0)
        {
            hoursToAdd = ordTest.HoursRequired;
            if (!ordTest.WorkHours.TimeFallsInSlab(bookingTime.AddHours(hoursToAdd)))
                //daysToAdd += 1;
                hoursToAdd = ordTest.WorkHours.GetHoursDelta(hoursToAdd, bookingTime);
        }

        if (ordTest.DaysRequired > 0)
            if (ordTest.DaysRequired > daysToAdd)
                daysToAdd = ordTest.DaysRequired - daysToAdd;

        var delivery = bookingTime.AddDays(daysToAdd).AddHours(hoursToAdd);

        if (ordTest.MorningSlab.TimeFallsInSlab(delivery))
            return ordTest.MorningSlab.GetDeliveryDateTime(delivery);

        if (ordTest.NoonSlab.TimeFallsInSlab(delivery))
            return ordTest.NoonSlab.GetDeliveryDateTime(delivery);

        if (ordTest.EveningSlab.TimeFallsInSlab(delivery))
            return ordTest.EveningSlab.GetDeliveryDateTime(delivery);

        // fall-back to evening delivery hours
        return ordTest.EveningSlab.GetDeliveryDateTime(delivery);
    }
}