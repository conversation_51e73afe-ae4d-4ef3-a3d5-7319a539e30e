﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ReferrerInfo.cs 1440 2014-10-03 04:25:17Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System.Collections.Generic;
using CuttingEdge.Conditions;

namespace LabMaestro.Domain;

public class ReferrerInfo
{
    public ReferrerInfo()
    {
        Invoices = new List<ReferralInvoiceInfo>();
        ReferralSubCategories = new ReferralSubCategories();
        SuppressNetReferral = false;
    }

    public ReferralSubCategories ReferralSubCategories { get; private set; }

    public string ReferrerName { get; set; }

    public int ReferrerId { get; set; }

    public decimal GrossReferralTotal { get; set; }

    public decimal NetReferralTotal { get; set; }

    public decimal GrossBillTotal { get; set; }

    public decimal DiscountTotal { get; set; }

    public decimal NetBillTotal { get; set; }

    public decimal PaidTotal { get; set; }

    public decimal DueTotal { get; set; }

    public int NumInvoices { get; set; }

    public List<ReferralInvoiceInfo> Invoices { get; }

    public bool SuppressNetReferral { get; set; }

    public static ReferrerInfo AssembleFrom(EligibleReferrerSlice slice)
    {
        return new ReferrerInfo
        {
            ReferrerName = slice.FullName,
            ReferrerId = slice.Id,
            SuppressNetReferral = slice.SuppressNetReferral
        };
    }

    public void CalculateGrossReferral(IReferralEligibleLabTestsCatalog catalog)
    {
        foreach (var invoice in Invoices)
        {
            var testIds = OrderedTestsRepository.GetReferralEligibleOrderedTestIds(invoice.InvoiceId);

            var grossTotal = 0m;
            foreach (var testId in testIds)
            {
                string groupName, refModeLabel;
                decimal listPrice;
                var grossReferral = catalog.CalculateGrossReferralAmount((short)testId,
                    out listPrice,
                    out groupName,
                    out refModeLabel);
                invoice.ReferralSubCategories.AddOrUpdateCategory(groupName,
                    listPrice,
                    grossReferral,
                    refModeLabel);
                grossTotal += grossReferral;
            }

            invoice.GrossPayable = grossTotal;

            if (invoice.DueAmount > 0)
            {
                // TODO: update net referral
            }
            else
            {
                invoice.NetPayable = grossTotal;
            }
        }
    }

    public ReferralInvoiceInfo ProcessInvoice(ReferralEligibleLabOrderSlice slice)
    {
        Condition.Requires(slice).IsNotNull();
        Condition.Requires(slice.ReferrerId).IsEqualTo(ReferrerId);

        var invoice = ReferralInvoiceInfo.AssembleFrom(slice);
        GrossBillTotal += invoice.GrossPayable;
        NetBillTotal += invoice.NetPayable;
        DueTotal += invoice.DueAmount;
        DiscountTotal += invoice.DiscountAmount;
        PaidTotal += invoice.PaidAmount;
        NumInvoices++;
        Invoices.Add(invoice);
        return invoice;
    }

    public bool HasInvoices()
    {
        return NumInvoices > 0;
    }
}