﻿//  * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
//  
//  Copyright (c) 2012, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
//  All rights reserved.
//  
//  Redistribution and use in source and binary forms, with or without
//  modification, are permitted provided that the following conditions are met:
//     * Redistributions of source code must retain the above copyright
//       notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above copyright
//       notice, this list of conditions and the following disclaimer in the
//       documentation and/or other materials provided with the distribution.
//     * Neither the name of the <organization> nor the
//       names of its contributors may be used to endorse or promote products
//       derived from this software without specific prior written permission.
//  
//  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
//  ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
//  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
//  DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
//  DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
//  (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
//  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
//  ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
//  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
//  SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
//  
//  $Id: ResultBundleSlice.cs 1248 2014-03-30 12:41:12Z <EMAIL> $
//  
//  * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System.Collections.Generic;
using AutoMapper;
using LabMaestro.Shared;

namespace LabMaestro.Domain;

public sealed class ResultBundleSlice
{
    public ResultBundleSlice()
    {
        OrderedTests = new List<OrderedTestSlice>();
    }

    public string DisplayTitle { get; set; }

    public string ResultNotes { get; set; }

    public long Id { get; set; }

    public short LabId { get; set; }

    public int? ReportHeaderId { get; set; }

    public WorkflowStageType WorkFlowStage { get; set; }

    public TestResultType TestResultType { get; set; }

    public TATRankingType TATRank { get; set; }

    public List<OrderedTestSlice> OrderedTests { get; private set; }

    public WorkflowStageType PostOrderEntryWorkflowStage { get; set; }

    public WorkflowStageType PostResultEntryWorkflowStage { get; set; }

    public WorkflowStageType PostResultVerificationWorkflowStage { get; set; }

    public WorkflowStageType PostResultFinalizationWorkflowStage { get; set; }

    public WorkflowStageType PostReportCollationWorkflowStage { get; set; }

    private static WorkflowStageType sanitizeWorkflowStage(byte? value, WorkflowStageType wfDefault)
    {
        if (value != null) return (WorkflowStageType)value;
        return wfDefault;
    }

    public static ResultBundleSlice AssembleFrom(ResultBundlesForInvoice bundle)
    {
        Mapper
            .CreateMap<ResultBundlesForInvoice, ResultBundleSlice>()
            .ForMember(dest => dest.WorkFlowStage, opt => opt.MapFrom(x => (WorkflowStageType)x.WorkflowStage))
            .ForMember(dest => dest.PostOrderEntryWorkflowStage,
                opt => opt.MapFrom(
                    x => sanitizeWorkflowStage(x.PostOrderEntryWorkflowStage, WorkflowStageType.OrderEntry)))
            .ForMember(dest => dest.PostResultEntryWorkflowStage,
                opt => opt.MapFrom(
                    x => sanitizeWorkflowStage(x.PostResultEntryWorkflowStage, WorkflowStageType.ResultEntry)))
            .ForMember(dest => dest.PostReportCollationWorkflowStage,
                opt => opt.MapFrom(
                    x => sanitizeWorkflowStage(x.PostReportCollationWorkflowStage, WorkflowStageType.ReportCollation)))
            .ForMember(dest => dest.PostResultVerificationWorkflowStage,
                opt => opt.MapFrom(
                    x => sanitizeWorkflowStage(x.PostResultVerificationWorkflowStage,
                        WorkflowStageType.ResultValidation)))
            .ForMember(dest => dest.PostResultFinalizationWorkflowStage,
                opt => opt.MapFrom(
                    x => sanitizeWorkflowStage(x.PostResultFinalizationWorkflowStage,
                        WorkflowStageType.ReportFinalization)))
            .ForMember(dest => dest.TestResultType, opt => opt.MapFrom(x => (TestResultType)x.TestResultType))
            .ForMember(dest => dest.TATRank, opt => opt.MapFrom(x => (TATRankingType)x.TATRank))
            .ForMember(dest => dest.ResultNotes,
                opt => opt.MapFrom(x => CompressionUtils.DecompressToString(x.ResultNotes)));

        var dto = Mapper.Map<ResultBundlesForInvoice, ResultBundleSlice>(bundle);
        return dto;
    }
}