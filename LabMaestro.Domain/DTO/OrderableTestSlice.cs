﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: OrderableTestSlice.cs 1248 2014-03-30 12:41:12Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using LabMaestro.Shared;

namespace LabMaestro.Domain;

/// <summary>
///     This class represents a restricted view of the test catalog. Helpful for ordering/modifying tests in a lab order.
/// </summary>
public class OrderableTestSlice : ICloneable
{
    /// <summary>
    ///     Default constructor for child classes
    /// </summary>
    public OrderableTestSlice() : this(null)
    {
    }

    public OrderableTestSlice(OrderableTestsSlice dto)
    {
        BillableItems = new List<BillableItemSlice>();
        if (dto != null)
        {
            TestId = dto.TestId;
            ShortName = dto.ShortName;
            CanonicalName = dto.CanonicalName;
            ListPrice = dto.ListPrice;

            PerformingLabId = dto.PerformingLabId;
            PerformingLabName = dto.PerformingLabName;
            PerformingLabDisplayName = dto.PerformingLabDisplayName;
            ReqPrintCanonicalTestName = dto.ReqPrintCanonicalTestName;

            DiscountMode = dto.DiscountMode != null ? (IncentiveType)dto.DiscountMode : IncentiveType.None;
            DiscountAmount = dto.DiscountAmount ?? 0;
            DiscountPercent = dto.DiscountPercent ?? 0;
            MaxApplicableDiscount = calculateMaxDiscount();

            TATRanking = dto.TATRank.HasValue ? (TATRankingType)dto.TATRank.Value : TATRankingType.Regular;
            DaysRequired = dto.DaysRequired ?? 0;
            HoursRequired = dto.HoursRequired ?? 0;

            MorningSlab = new DeliveryTimeSlab(dto.MorningSlabBegin ?? 0,
                dto.MorningSlabEnd ?? 0,
                dto.MorningDeliveryHour ?? 0);

            NoonSlab = new DeliveryTimeSlab(dto.NoonSlabBegin ?? 0,
                dto.NoonSlabEnd ?? 0,
                dto.NoonDeliveryHour ?? 0);

            EveningSlab = new DeliveryTimeSlab(dto.EveningSlabBegin ?? 0,
                dto.EveningSlabEnd ?? 0,
                dto.EveningDeliveryHour ?? 0);

            WorkHours = new DeliveryTimeSlab(dto.MorningSlabBegin ?? 0,
                dto.EveningSlabEnd ?? 0,
                dto.EveningDeliveryHour ?? 0);
        }
    }

    public short TestId { get; private set; }
    public string ShortName { get; }
    public string CanonicalName { get; private set; }
    public decimal ListPrice { get; }
    public short PerformingLabId { get; private set; }
    public string PerformingLabName { get; private set; }
    public string PerformingLabDisplayName { get; }
    public bool ReqPrintCanonicalTestName { get; private set; }
    public IncentiveType DiscountMode { get; }
    public double DiscountPercent { get; }
    public decimal DiscountAmount { get; }
    public decimal MaxApplicableDiscount { get; private set; }
    public TATRankingType TATRanking { get; private set; }
    public DateTime? DeliveryTime { get; set; }
    public List<BillableItemSlice> BillableItems { get; }

    public short DaysRequired { get; private set; }
    public short HoursRequired { get; private set; }
    public DeliveryTimeSlab MorningSlab { get; set; }
    public DeliveryTimeSlab NoonSlab { get; set; }
    public DeliveryTimeSlab EveningSlab { get; set; }
    public DeliveryTimeSlab WorkHours { get; set; }

    #region ICloneable Members

    object ICloneable.Clone()
    {
        return Clone();
    }

    #endregion

    private decimal calculateMaxDiscount()
    {
        switch (DiscountMode)
        {
            case IncentiveType.Percentage:
                var fract = (decimal)(DiscountPercent / 100);
                return decimal.Multiply(ListPrice, fract);
            case IncentiveType.FlatRate:
                return DiscountAmount;
        }

        return 0;
    }

    public void AddItem(BillableItemSlice item)
    {
        foreach (var slice in BillableItems)
            if (slice.Id == item.Id)
            {
                slice.Quantity += item.Quantity;
                return;
            }

        BillableItems.Add(item);
    }

    public override string ToString()
    {
        var sb = new StringBuilder();
        sb.Append(ShortName + " @" + PerformingLabDisplayName + " [");

        foreach (var item in BillableItems) sb.Append(item + ", ");
        sb.Append("]");
        return sb.ToString();
    }

    public virtual DateTime EstimateDeliveryTime(DateTime bookingTime)
    {
        DeliveryTime = DeliveryTimeEstimator.EstimateDeliveryTime(this, bookingTime);
        return (DateTime)DeliveryTime;
    }

    public OrderableTestSlice Clone()
    {
        var clone = MemberwiseClone() as OrderableTestSlice;
        clone.MorningSlab = MorningSlab.Clone();
        clone.NoonSlab = NoonSlab.Clone();
        clone.EveningSlab = EveningSlab.Clone();
        clone.WorkHours = WorkHours.Clone();

        //if (BillableItems.Count > 0) clone.BillableItems.AddRange(BillableItems);

        return clone;
    }

    public decimal CalculateEffectivePrice()
    {
        var biPrice = BillableItems.Sum(bi => bi.LineTotal);
        return ListPrice + biPrice;
    }
}