//  * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
//  
//  Copyright (c) 2012, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
//  All rights reserved.
//  
//  Redistribution and use in source and binary forms, with or without
//  modification, are permitted provided that the following conditions are met:
//     * Redistributions of source code must retain the above copyright
//       notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above copyright
//       notice, this list of conditions and the following disclaimer in the
//       documentation and/or other materials provided with the distribution.
//     * Neither the name of the <organization> nor the
//       names of its contributors may be used to endorse or promote products
//       derived from this software without specific prior written permission.
//  
//  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
//  ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
//  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
//  DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
//  DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
//  (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
//  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
//  ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
//  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
//  SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
//  
//  $Id: ILabOrderContextInfo.cs 1248 2014-03-30 12:41:12Z <EMAIL> $
//  
//  * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using LabMaestro.Shared;

namespace LabMaestro.Domain;

public interface ILabOrderContextInfo
{
    long InvoiceId { get; set; }
    string OrderId { get; set; }
    int? ReferrerId { get; set; }
    string Title { get; set; }
    string FirstName { get; set; }
    string LastName { get; set; }
    string PhoneNumber { get; set; }
    string OrderNotes { get; set; }
    string DiscountNotes { get; set; }
    bool IsReferrerUnknown { get; set; }
    bool DisallowReferral { get; set; }
    string ReferrerCustomName { get; set; }
    bool IsCancelled { get; set; }
    SexType Sex { get; set; }
    string Age { get; set; }
    DateTime? DoB { get; set; }
    DateTime OrderDateTime { get; set; }
    WorkflowStageType WorkflowStage { get; set; }
    string OrderingUserName { get; set; }
    List<OrderableTestSlice> OrderedTests { get; }
    List<BillableItemSlice> OrderedBillableItems { get; }
    short? AffiliateId { get; set; }
    short? AssociateLabId { get; set; }
    string AssociateLabAccessionId { get; set; }
    short? CorporateClientId { get; set; }
    long? CustomerId { get; set; }
    bool IsHidden { get; set; }
    bool IsNonFungibleOrder { get; set; }
}