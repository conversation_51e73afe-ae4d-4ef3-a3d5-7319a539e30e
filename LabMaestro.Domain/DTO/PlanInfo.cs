﻿using System;

namespace LabMaestro.Domain;

public sealed class PlanInfo
{
    public PlanInfo(Plan plan)
    {
        Id = plan.Id;
        Name = plan.Name;
        ListPrice = plan.ListPrice;
        DurationMonths = plan.DurationMonths;
    }

    public decimal ListPrice { get; set; }
    public string Name { get; set; }
    public short Id { get; set; }
    public short DurationMonths { get; set; }
    public override string ToString() => Name;
    public DateTime StartDate => DateTime.Today;
    public DateTime ExpiryDate => DateTime.Today.AddMonths(DurationMonths).AddDays(-1);
}