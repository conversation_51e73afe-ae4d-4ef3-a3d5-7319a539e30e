﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: NewLabOrderContextDto.cs 1461 2014-10-12 12:45:41Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.IO;
using IdeaBlade.EntityModel;
using Newtonsoft.Json;

namespace LabMaestro.Domain;

[Serializable]
public sealed class NewLabOrderContextDto : IKnownType
{
    public int WorkShiftId { get; set; }
    public int? UserIpAddress { get; set; }
    public short OrderingUserId { get; set; }

    public string Title { get; set; }
    public string FirstName { get; set; }
    public string LastName { get; set; }
    public byte Sex { get; set; }
    public string Age { get; set; }
    public DateTime? DoB { get; set; }
    public string PhoneNumber { get; set; }
    public string EmailAddress { get; set; }
    public bool EmailTestResults { get; set; }

    public bool IsReferrerUnknown { get; set; }
    public int? ReferrerId { get; set; }
    public bool DisallowReferral { get; set; }
    public string ReferrerCustomName { get; set; }

    public string OrderNotes { get; set; }

    public byte WorkflowStage { get; set; }
    public byte ResultsReleaseFlag { get; set; }

    public short? AffiliateId { get; set; }
    public short? CorporateClientId { get; set; }

    public short? HealthPackageId { get; set; }

    public bool IsExternalSubOrder { get; set; }

    //public int? RequestingLabId { get; set; }
    //public string SubOrderTrackingId { get; set; }
    public short? AssociateLabId { get; set; }
    public string AssociateLabAccessionId { get; set; }

    public bool IsHidden { get; set; }
    public bool IsNonFungibleOrder { get; set; }

    public string WebAccessToken { get; set; }

    //public long? RegisteredMemberId { get; set; }
    public long? CustomerId { get; set; }

    public byte MirrorFlag { get; set; }

    // invoice master
    public byte PaymentStatus { get; set; }
    public decimal GrossPayable { get; set; }
    public decimal DiscountAmount { get; set; }
    public decimal TaxAmount { get; set; }
    public decimal SurchargeAmount { get; set; }
    public decimal NetPayable { get; set; }
    public decimal PaidAmount { get; set; }
    public decimal DueAmount { get; set; }
    public decimal RefundAmount { get; set; }

    public List<NewTransactionDto> Transactions { get; set; } = [];
    public List<NewOrderedBillableItemDto> OrderedItems { get; set; } = [];
    public List<NewOrderedTestDto> OrderedTests { get; set; } = [];

    public string ToJson(bool pretty)
    {
        var serializer = new JsonSerializer
        {
            NullValueHandling = NullValueHandling.Ignore
        };
        if (pretty) serializer.Formatting = Formatting.Indented;

        using var sw = new StringWriter();
        using var writer = new JsonTextWriter(sw);
        serializer.Serialize(writer, this);
        return sw.ToString();
    }
}

[Serializable]
public sealed class NewTransactionDto : IKnownType
{
    public short? AuthorizingUserId { get; set; }
    public byte TxType { get; set; }
    public byte TxFlag { get; set; }
    public decimal TxAmount { get; set; }
    public decimal NonCashAmount { get; set; }
    public byte PaymentMethod { get; set; }
    public string UserRemarks { get; set; }
    public string PaymentSource { get; set; }
    public string PaymentReference { get; set; }
}

[Serializable]
public sealed class NewOrderedTestDto : IKnownType
{
    public short LabTestId { get; set; }
    public decimal UnitPrice { get; set; }
    public byte WorkflowStage { get; set; }
    public DateTime? ResultsETA { get; set; }
}

[Serializable]
public sealed class NewOrderedBillableItemDto : IKnownType
{
    public short BillableItemId { get; set; }
    public decimal UnitPrice { get; set; }
    public short Quantity { get; set; }
}

/*

[LastModified] SMALLDATETIME NOT NULL,
[IsCancelled] BIT NOT NULL,
[MirrorFlag] TINYINT NOT NULL,
*
*
*
* inv master
[DateCreated]       SMALLDATETIME NOT NULL,
[]     TINYINT NOT NULL,
[]      MONEY NOT NULL,
[]    MONEY NOT NULL,
[]         MONEY NOT NULL,
[]   MONEY NOT NULL,
[]        MONEY NOT NULL,
[]        MONEY NOT NULL,
[]         MONEY NOT NULL,
[]      MONEY NOT NULL,
*
*
* inv tx

[InvoiceId] BIGINT NOT NULL,
[PerformingUserId] SMALLINT NULL,
[WorkShiftId] INT NULL,
[TxTime] SMALLDATETIME NOT NULL,
[UserIpAddress] INT NULL,
*
*
* ordtest
[InvoiceId] BIGINT NOT NULL,
[ResultBundleId] BIGINT NULL,
[IsCancelled] BIT NOT NULL,
[DateCreated] SMALLDATETIME NOT NULL,
[LastModified] SMALLDATETIME NOT NULL,

*
*
*
*
*
*
*
* ord bi
[Id] BIGINT IDENTITY(1, 1) NOT FOR REPLICATION NOT NULL,
[InvoiceId] BIGINT NOT NULL,
[DateCreated] SMALLDATETIME NOT NULL,
[IsCancelled] BIT NOT NULL,

*/