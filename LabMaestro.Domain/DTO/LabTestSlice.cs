﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id:$
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using LabMaestro.Shared;

namespace LabMaestro.Domain;

public sealed class LabTestSlice
{
    public LabTestSlice()
    {
        BillableItemLinks = new List<TestBillableItemLinkSlice>();
        DiscreteReportLineItemSlices = new DiscreteReportLineItemsList();
        ReqParameterSlices = new ReqParamsList();
    }

    public short Id { get; set; }
    public bool IsActive { get; set; }
    public string TestSKU { get; set; }
    public string ShortName { get; set; }
    public string CanonicalName { get; set; }
    public decimal ListPrice { get; set; }
    public decimal SubOrderPrice { get; set; }
    public decimal CostBasis { get; set; }
    public ReqSlipGenerationOptionType ReqSlipPrintOption { get; set; }
    public SortPriorityType ReportSortPriority { get; set; }
    public DateTime DateCreated { get; set; }
    public DateTime LastModified { get; set; }
    public SmartDate InactiveDate { get; set; }
    public string Mnemonics { get; set; }
    public string ReportLineGroupingTag { get; set; }
    public short PerformingLabId { get; set; }
    public short ResultingLabId { get; set; }
    public short DefaultTemplateId { get; set; }
    public short TATGroupId { get; set; }
    public short TemplateGroupId { get; set; }

    public List<TestBillableItemLinkSlice> BillableItemLinks { get; private set; }
    public DiscreteReportLineItemsList DiscreteReportLineItemSlices { get; private set; }
    public ReqParamsList ReqParameterSlices { get; private set; }

    private static LabTestSlice initializeChildItems(LabTestSlice slice)
    {
        var bilinks = BillableItemsRepository.GetAllLinkedBillableItemsForTest(slice.Id);
        slice.BillableItemLinks.AddRange(bilinks.Select(TestBillableItemLinkSlice.AssembleFrom));

        var reqParams = ReqParametersRepository.GetAllReqParametersForTest(slice.Id).OrderBy(x => x.SortOrder);
        slice.ReqParameterSlices.AddRange(reqParams.Select(ReqParameterSlice.AssembleFrom));

        var repItems =
            DiscreteResultsRepository.GetAllDiscreteReportLineItemsForTest(slice.Id).OrderBy(x => x.SortOrder);
        slice.DiscreteReportLineItemSlices.AddRange(repItems.Select(DiscreteReportLineItemSlice.AssembleFrom));

        return slice;
    }

    public static LabTestSlice AssembleFrom(LabTest test, bool initChild)
    {
        Mapper.CreateMap<LabTest, LabTestSlice>()
            //.ForMember(dest => dest.DateCreated, x => x.MapFrom(src => new SmartDate(src.DateCreated, true)))
            //.ForMember(dest => dest.LastModified, x => x.MapFrom(src => new SmartDate(src.LastModified, true)))
            .ForMember(dest => dest.InactiveDate, x => x.MapFrom(src => new SmartDate(src.InactiveDate, true)))
            .ForMember(dest => dest.ReqSlipPrintOption,
                x => x.MapFrom(src => (ReqSlipGenerationOptionType)src.ReqSlipPrintOption))
            .ForMember(dest => dest.ReportSortPriority,
                x => x.MapFrom(src => (SortPriorityType)src.ReportSortPriority));
        var slice = Mapper.Map<LabTest, LabTestSlice>(test);

        return initChild ? initializeChildItems(slice) : slice;
    }

    public void InitializeChildItems()
    {
        initializeChildItems(this);
    }

    public static LabTestSlice AssembleFrom(TestForPerformingLabSlice test, bool initChild)
    {
        Mapper.CreateMap<TestForPerformingLabSlice, LabTestSlice>()
            .ForMember(dest => dest.DateCreated, x => x.MapFrom(src => new SmartDate(src.DateCreated, true)))
            .ForMember(dest => dest.LastModified, x => x.MapFrom(src => new SmartDate(src.LastModified, true)))
            .ForMember(dest => dest.InactiveDate, x => x.MapFrom(src => new SmartDate(src.InactiveDate, true)))
            .ForMember(dest => dest.ReqSlipPrintOption,
                x => x.MapFrom(src => (ReqSlipGenerationOptionType)src.ReqSlipPrintOption))
            .ForMember(dest => dest.ReportSortPriority,
                x => x.MapFrom(src => (SortPriorityType)src.ReportSortPriority));
        var slice = Mapper.Map<TestForPerformingLabSlice, LabTestSlice>(test);

        return initChild ? initializeChildItems(slice) : slice;
    }
}