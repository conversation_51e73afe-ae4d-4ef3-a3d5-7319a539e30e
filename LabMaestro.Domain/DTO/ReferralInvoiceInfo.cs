﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ReferralInvoiceInfo.cs 1440 2014-10-03 04:25:17Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Linq;

namespace LabMaestro.Domain;

public sealed class ReferralInvoiceInfo
{
    public ReferralInvoiceInfo()
    {
        ReferralSubCategories = new ReferralSubCategories();
    }

    public decimal DueAmount { get; set; }

    public decimal DiscountAmount { get; set; }

    public string PatientName { get; set; }

    public decimal PaidAmount { get; set; }

    public decimal GrossPayable { get; set; }

    public decimal NetPayable { get; set; }

    public long InvoiceId { get; set; }

    public int ReferrerId { get; set; }

    public string OrderId { get; set; }

    public string ReferrerName { get; set; }

    public DateTime OrderDateTime { get; set; }

    public decimal GrossReferral { get; set; }

    public decimal NetReferral { get; private set; }

    public ReferralSubCategories ReferralSubCategories { get; }

    public static ReferralInvoiceInfo AssembleFrom(ReferralEligibleLabOrderSlice slice)
    {
        var dto = new ReferralInvoiceInfo
        {
            DiscountAmount = slice.DiscountAmount,
            DueAmount = slice.DueAmount,
            OrderDateTime = slice.OrderDateTime,
            OrderId = slice.OrderId,
            InvoiceId = slice.InvoiceId,
            NetPayable = slice.NetPayable,
            GrossPayable = slice.GrossPayable,
            PaidAmount = slice.PaidAmount,
            PatientName = slice.PatientName,
            ReferrerId = slice.ReferrerId ?? 0,
            ReferrerName = slice.ReferrerName
        };
        return dto;
    }

    public void CalculateNetReferral(bool suppressCalculation)
    {
        if (suppressCalculation)
        {
            NetReferral = GrossReferral;
            return;
        }

        NetReferral = 0m;

        // Deduct the discount amount (if any) from the gross referral
        var refAfterDisc = DiscountAmount > 0m ? GrossReferral - DiscountAmount : GrossReferral;
        refAfterDisc = refAfterDisc < 0m ? 0m : refAfterDisc; // <- disallow negative balance

        // If the order is fully paid
        if (DueAmount == 0m)
        {
            NetReferral = refAfterDisc;
            return;
        }

        NetReferral = 0m; // disallow referral if the order is not paid in full
        return;

        // We sum up test prices to obtain the actual invoice amount.
        // This will eliminate all billable items
        var actualBill = ReferralSubCategories.Items.Sum(r => r.BillAmount);

        var actualDue = actualBill - PaidAmount;
        if (actualDue > 0m)
        {
            var percentPaid = PaidAmount * 100m / actualBill;
            var refAdjusted = percentPaid * refAfterDisc / 100m;
            NetReferral = decimal.Ceiling(refAdjusted);
            return;
        }

        NetReferral = refAfterDisc; // fallback
    }
}