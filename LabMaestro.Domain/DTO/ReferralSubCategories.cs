﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: ReferralSubCategories.cs 1101 2013-12-03 07:15:32Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System.Collections.Generic;
using System.Linq;

namespace LabMaestro.Domain;

public sealed class ReferralSubCategories
{
    private readonly Dictionary<string, ReferralSubCategoryInfo> _items;

    public ReferralSubCategories()
    {
        _items = new Dictionary<string, ReferralSubCategoryInfo>();
    }

    public List<ReferralSubCategoryInfo> Items
    {
        get { return _items.Values.ToList(); }
    }

    public int NumTestsTotal
    {
        get { return _items.Values.Sum(x => x.NumTests); }
    }

    public void AddOrUpdateCategory(string category, decimal price, decimal referral, string refModeLabel)
    {
        var key = category.ToUpper();
        if (!_items.ContainsKey(key))
            _items.Add(key, new ReferralSubCategoryInfo { CategoryName = category, ReferralModeLabel = refModeLabel });
        _items[key].AddAmounts(price, referral);
    }

    public void AddOrUpdateCategory(string category, decimal price, decimal referral, int numTests, string refModeLabel)
    {
        var key = category.ToUpper();
        if (!_items.ContainsKey(key))
            _items.Add(key, new ReferralSubCategoryInfo { CategoryName = category, ReferralModeLabel = refModeLabel });
        _items[key].AddAmounts(price, referral, numTests);
    }
}