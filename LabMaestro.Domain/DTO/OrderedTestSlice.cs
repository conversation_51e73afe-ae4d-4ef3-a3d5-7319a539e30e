// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: OrderedTestSlice.cs 1510 2014-11-18 11:23:05Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System.Collections.Generic;
using LabMaestro.Shared;

namespace LabMaestro.Domain;

public class OrderedTestSlice
{
    public OrderedTestSlice()
    {
        ResultItems = new List<DiscreteResultLineItemSlice>();
    }

    public long OrderedTestId { get; set; }

    public short LabTestId { get; set; }

    public SmartDate ResultETA { get; set; }

    public List<DiscreteResultLineItemSlice> ResultItems { get; private set; }

    public TemplateResultSlice TemplateResult { get; set; }

    public WorkflowStageType WorkflowStage { get; set; }

    public string TestName { get; set; }

    public SortPriorityType ReportSortPriority { get; set; }

    public static OrderedTestSlice AssembleFrom(ActiveTestsInResultBundle src)
    {
        var dto = new OrderedTestSlice
        {
            OrderedTestId = src.OrderedTestId,
            LabTestId = src.LabTestId,
            WorkflowStage = (WorkflowStageType)src.WorkflowStage,
            TestName = src.TestName,
            ReportSortPriority = (SortPriorityType)src.ReportSortPriority,
            ResultETA = new SmartDate(src.ResultsETA, true)
        };
        return dto;
        /*
        Mapper
            .CreateMap<ActiveTestsInResultBundle, OrderedTestSlice>()
            .ForMember(dest => dest.ResultETA, x => x.MapFrom(src => new SmartDate(src.ResultsETA, true)))
            .ForMember(dest => dest.WorkflowStage, x => x.MapFrom(src => (WorkflowStageType) src.WorkflowStage))
            .ForMember(dest => dest.ReportSortPriority,
                       x => x.MapFrom(src => (SortPriorityType) src.ReportSortPriority));
        var slice = Mapper.Map<ActiveTestsInResultBundle, OrderedTestSlice>(test);
        return slice;
        */
    }
}