﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: OrderedTestWithWorkflowSlice.cs 1248 2014-03-30 12:41:12Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using LabMaestro.Shared;

namespace LabMaestro.Domain;

/// <summary>
///     This class is used by LabOrderEditor to keep track of modified tests in a lab order
/// </summary>
public sealed class OrderedTestWithWorkflowSlice : OrderableTestSlice
{
    public OrderedTestWithWorkflowSlice(long orderedTestId)
        : this(orderedTestId, OrderedTestsRepository.GetTestDetailsForOrderedTest(orderedTestId))
    {
    }

    public OrderedTestWithWorkflowSlice(long orderedTestId, OrderableTestsSlice dto)
        : base(dto)
    {
        PerformingLabPostOrderEntryWorkflowStage = dto.PostOrderEntryWorkflowStage != null
            ? (WorkflowStageType)dto.PostOrderEntryWorkflowStage
            : WorkflowStageType.Unknown;
        PendingModification = PendingModificationType.None;
        OrderedTestId = orderedTestId;
        loadOrderedTestDetails();
        loadBillableItems();
    }

    public long OrderedTestId { get; set; }
    public bool OrderedTestIsCancelled { get; set; }
    public decimal OrderedTestUnitPrice { get; private set; }
    public SmartDate OrderedTestETA { get; private set; }
    public DateTime OrderedTestLastModified { get; private set; }
    public PendingModificationType PendingModification { get; set; }

    public long? ResultBundleId { get; set; }
    public bool ResultBundleIsActive { get; set; }

    public WorkflowStageType OrderedTestWorkflowStage { get; set; }
    public WorkflowStageType ResultBundleWorkflowStage { get; set; }
    public WorkflowStageType PerformingLabPostOrderEntryWorkflowStage { get; set; }

    public string PendingOperationReason { get; set; }

    private void loadBillableItems()
    {
        var slice = OrderableTestSliceCatalogRepository.FindById(TestId);
        if (slice != null)
            foreach (var bi in slice.BillableItems)
                BillableItems.Add(new BillableItemSlice
                {
                    Id = bi.Id,
                    IsActive = bi.IsActive,
                    IsCancelled = bi.IsCancelled,
                    LineTotal = bi.LineTotal,
                    Name = bi.Name,
                    OptimizationLevel = bi.OptimizationLevel,
                    OrderedItemId = bi.OrderedItemId,
                    Quantity = bi.Quantity,
                    UnitPrice = bi.UnitPrice
                });
    }

    public new OrderedTestWithWorkflowSlice Clone()
    {
        var clone = MemberwiseClone() as OrderedTestWithWorkflowSlice;
        clone.MorningSlab = MorningSlab.Clone();
        clone.NoonSlab = NoonSlab.Clone();
        clone.EveningSlab = EveningSlab.Clone();
        clone.WorkHours = WorkHours.Clone();

        //if (BillableItems.Count > 0) clone.BillableItems.AddRange(BillableItems);

        return clone;
    }

    public decimal GetPrice()
    {
        return OrderedTestUnitPrice == 0m ? ListPrice : OrderedTestUnitPrice;
    }

    private void loadOrderedTestDetails()
    {
        ResultBundleIsActive = false;
        OrderedTestWorkflowStage = WorkflowStageType.Unknown;
        ResultBundleWorkflowStage = WorkflowStageType.Unknown;

        if (OrderedTestId > 0)
        {
            PerformingLabPostOrderEntryWorkflowStage = WorkflowStageType.Unknown;

            var ordTest = OrderedTestsRepository.FindById(OrderedTestId);
            // ?? raise error if not found ??
            if (ordTest != null)
            {
                OrderedTestIsCancelled = ordTest.IsCancelled;
                OrderedTestWorkflowStage = (WorkflowStageType)ordTest.WorkflowStage;
                OrderedTestETA = new SmartDate(ordTest.ResultsETA);
                OrderedTestUnitPrice = ordTest.UnitPrice;
                OrderedTestLastModified = ordTest.LastModified;

                ResultBundleId = ordTest.ResultBundleId;
                if (ordTest.ResultBundle != null)
                {
                    ResultBundleWorkflowStage = (WorkflowStageType)ordTest.ResultBundle.WorkflowStage;
                    ResultBundleIsActive = ordTest.ResultBundle.IsActive;
                }

                if (ordTest.LabTest.PerformingLab.PostOrderEntryWorkflowStage != null)
                    PerformingLabPostOrderEntryWorkflowStage =
                        (WorkflowStageType)ordTest.LabTest.PerformingLab.PostOrderEntryWorkflowStage;
            }
        }
        else
        {
            OrderedTestLastModified = DateTime.Now;
        }
    }

    public void UpdateResultBundleStatusFromDatabase()
    {
        if (ResultBundleId.HasValue)
        {
            var bundle = ResultBundlesRepository.FindById((long)ResultBundleId);
            ResultBundleIsActive = bundle.IsActive;
            ResultBundleWorkflowStage = (WorkflowStageType)bundle.WorkflowStage;
            OrderedTestWorkflowStage = (WorkflowStageType)bundle.WorkflowStage;
        }
    }

    public bool TestCanBeCancelled(bool updateStatus)
    {
        if (updateStatus) UpdateResultBundleStatusFromDatabase();
        return OrderedTestWorkflowStage == PerformingLabPostOrderEntryWorkflowStage;
    }

    public override DateTime EstimateDeliveryTime(DateTime bookingTime)
    {
        base.EstimateDeliveryTime(bookingTime);
        OrderedTestETA = new SmartDate(DeliveryTime);
        return (DateTime)DeliveryTime;
    }
}