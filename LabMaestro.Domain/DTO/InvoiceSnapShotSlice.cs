﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: InvoiceSnapShotSlice.cs 1499 2014-11-11 04:22:26Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System.Runtime.Serialization;
using IdeaBlade.EntityModel;

namespace LabMaestro.Domain;

[DataContract(Name = "InvoiceSnapShotSlice", Namespace = "http://labmaestro.com")]
public sealed class InvoiceSnapShotSlice : IKnownType
{
    [DataMember] public decimal GrossPayable { get; set; }

    [DataMember] public decimal DiscountAmount { get; set; }

    [DataMember] public decimal TaxAmount { get; set; }

    [DataMember] public decimal SurchargeAmount { get; set; }

    [DataMember] public decimal NetPayable { get; set; }

    [DataMember] public decimal PaidAmount { get; set; }

    [DataMember] public decimal DueAmount { get; set; }

    [DataMember] public decimal RefundAmount { get; set; }

    public void AdjustFinances(bool updatePayment = false)
    {
        NetPayable = GrossPayable - DiscountAmount;
        DueAmount = NetPayable - PaidAmount;
        if (updatePayment) PaidAmount = PaidAmount - RefundAmount;
    }

    public void Reset()
    {
        GrossPayable = 0;
        DiscountAmount = 0;
        TaxAmount = 0;
        SurchargeAmount = 0;
        NetPayable = 0;
        PaidAmount = 0;
        DueAmount = 0;
        RefundAmount = 0;
    }

    public void AppendFrom(InvoiceSnapShotSlice source)
    {
        GrossPayable += source.GrossPayable;
        DiscountAmount += source.DiscountAmount;
        TaxAmount += source.TaxAmount;
        SurchargeAmount += source.SurchargeAmount;
        NetPayable += source.NetPayable;
        PaidAmount += source.PaidAmount;
        DueAmount += source.DueAmount;
        RefundAmount += source.RefundAmount;
    }
}