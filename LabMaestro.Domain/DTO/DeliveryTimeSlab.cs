﻿//  * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
//  
//  Copyright (c) 2012, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
//  All rights reserved.
//  
//  Redistribution and use in source and binary forms, with or without
//  modification, are permitted provided that the following conditions are met:
//     * Redistributions of source code must retain the above copyright
//       notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above copyright
//       notice, this list of conditions and the following disclaimer in the
//       documentation and/or other materials provided with the distribution.
//     * Neither the name of the <organization> nor the
//       names of its contributors may be used to endorse or promote products
//       derived from this software without specific prior written permission.
//  
//  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
//  ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
//  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
//  DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
//  DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
//  (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
//  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
//  ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
//  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
//  SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
//  
//  $Id: DeliveryTimeSlab.cs 1248 2014-03-30 12:41:12Z <EMAIL> $
//  
//  * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using LabMaestro.Shared;

namespace LabMaestro.Domain;

public sealed class DeliveryTimeSlab : ICloneable
{
    public DeliveryTimeSlab(double start, double end, double delivery)
    {
        SlabStart = start;
        SlabEnd = end;
        DeliveryTime = delivery;
    }

    public double SlabStart { get; }

    public double SlabEnd { get; }

    public double DeliveryTime { get; }

    #region ICloneable Members

    object ICloneable.Clone()
    {
        return Clone();
    }

    #endregion

    public DateTime GetDeliveryDateTime(DateTime date)
    {
        int hours, minutes = 0;
        SharedUtilities.DoubleToHourMinutes(DeliveryTime, out hours, out minutes);
        return new DateTime(date.Year, date.Month, date.Day, hours, minutes, 0);
    }

    public int GetHoursDelta(int hoursToAdd, DateTime bookingTime)
    {
        int endHours, minutes, startHours = 0;
        SharedUtilities.DoubleToHourMinutes(SlabEnd, out endHours, out minutes);
        var day = DateTime.Today;
        var eodToday = new DateTime(day.Year,
            day.Month,
            day.Day,
            endHours,
            minutes,
            0);

        SharedUtilities.DoubleToHourMinutes(SlabStart, out startHours, out minutes);
        day = day.AddDays(1);
        var startTomorrow = new DateTime(day.Year,
            day.Month,
            day.Day,
            startHours,
            minutes,
            0);
        var timeDiff = startTomorrow.Subtract(eodToday);
        var hours = endHours - bookingTime.Hour;
        if (timeDiff.Hours > 0)
        {
        }

        return timeDiff.Hours + (hoursToAdd - hours);
    }

    public bool TimeFallsInSlab(DateTime dt)
    {
        int hourStart, minStart, hourEnd, minEnd = 0;
        SharedUtilities.DoubleToHourMinutes(SlabStart, out hourStart, out minStart);
        SharedUtilities.DoubleToHourMinutes(SlabEnd, out hourEnd, out minEnd);

        if (dt.Hour >= hourStart && dt.Hour <= hourEnd)
        {
            if (minStart > 0 && minEnd > 0)
                return dt.Minute >= minStart && dt.Minute <= minEnd;
            if (minStart > 0)
                return dt.Minute >= minStart;
            if (minEnd > 0) return dt.Minute <= minEnd;
            return true;
        }

        return false;
    }

    public DeliveryTimeSlab Clone()
    {
        return MemberwiseClone() as DeliveryTimeSlab;
    }
}