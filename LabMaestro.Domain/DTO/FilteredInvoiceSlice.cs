﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: FilteredInvoiceSlice.cs 1495 2014-11-10 15:10:16Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Runtime.Serialization;
using IdeaBlade.EntityModel;
using LabMaestro.Shared;

namespace LabMaestro.Domain;

[DataContract(Name = "FilteredInvoiceSlice", Namespace = "http://labmaestro.com")]
public sealed class FilteredInvoiceSlice : IKnownType
{
    [DataMember] public long InvoiceId { get; set; }

    [DataMember] public string OrderId { get; set; }

    [DataMember] public DateTime OrderDateTime { get; set; }

    [DataMember] public bool IsCancelled { get; set; }

    [DataMember] public bool IsExternalSubOrder { get; set; }

    [DataMember] public bool IsReferrerUnknown { get; set; }

    [DataMember] public bool DisallowReferral { get; set; }

    [IgnoreDataMember] public bool ResetIfCancelled { get; set; } = true;

    [DataMember] public string PatientName { get; set; }

    [DataMember] public string ReferrerName { get; set; }

    [DataMember] public string OrderedTestNames { get; set; }

    [DataMember] public string DiscountRemarks { get; set; }

    [DataMember] public InvoiceSnapShotSlice CurrentInvoiceSnapshot { get; private set; } = new();

    [DataMember] public InvoiceSnapShotSlice PrimalInvoiceSnapshot { get; private set; } = new();

    public void LimitStrings()
    {
        PatientName = SharedUtilities.Truncate(PatientName, 25);
        ReferrerName = SharedUtilities.Truncate(ReferrerName, 26);
        OrderedTestNames = SharedUtilities.Truncate(OrderedTestNames, 28);
        DiscountRemarks = SharedUtilities.Truncate(DiscountRemarks, 16);
    }

    public void UpdatePrimalSnapshot(PrimalInvoiceSlice? primalInvoice = null)
    {
        if (ResetIfCancelled && IsCancelled)
        {
            PrimalInvoiceSnapshot.Reset();
            CurrentInvoiceSnapshot.Reset();
        }
        else
        {
            var txDiscount = InvoiceMasterRepository.GetFirstInvoiceTransactionInShiftByTypeFlag(
                InvoiceId,
                InvoiceTransactionType.CashDiscount,
                TransactionFlag.InitialOperation);

            if (primalInvoice != null)
            {
                // TODO: How to retrieve the Initial Gross Bill?
                PrimalInvoiceSnapshot.GrossPayable = primalInvoice.GrossPayable;
                PrimalInvoiceSnapshot.TaxAmount = primalInvoice.TaxAmount;
                PrimalInvoiceSnapshot.SurchargeAmount = primalInvoice.SurchargeAmount;
                PrimalInvoiceSnapshot.DiscountAmount = primalInvoice.DiscountAmount;
                PrimalInvoiceSnapshot.PaidAmount = primalInvoice.PaidAmount;
                PrimalInvoiceSnapshot.DueAmount = primalInvoice.DueAmount;
                PrimalInvoiceSnapshot.NetPayable = primalInvoice.NetPayable;
                PrimalInvoiceSnapshot.RefundAmount = primalInvoice.RefundAmount;
            }
            else
            {
                var txPayment = InvoiceMasterRepository.GetFirstInvoiceTransactionInShiftByTypeFlag(
                    InvoiceId,
                    InvoiceTransactionType.Payment,
                    TransactionFlag.InitialOperation);
                PrimalInvoiceSnapshot.GrossPayable = CurrentInvoiceSnapshot.GrossPayable;
                PrimalInvoiceSnapshot.TaxAmount = CurrentInvoiceSnapshot.TaxAmount;
                PrimalInvoiceSnapshot.SurchargeAmount = CurrentInvoiceSnapshot.SurchargeAmount;
                PrimalInvoiceSnapshot.DiscountAmount = txDiscount != null ? txDiscount.TxAmount : 0m;
                PrimalInvoiceSnapshot.PaidAmount = txPayment != null ? txPayment.TxAmount : 0m;
                PrimalInvoiceSnapshot.RefundAmount = CurrentInvoiceSnapshot.RefundAmount;
            }

            DiscountRemarks = txDiscount != null ? txDiscount.UserRemarks : "";
            PrimalInvoiceSnapshot.AdjustFinances(true);
        }
    }

    public void UpdateCurrentSnapshot(InvoiceMaster invoice)
    {
        if (ResetIfCancelled && IsCancelled)
        {
            PrimalInvoiceSnapshot.Reset();
            CurrentInvoiceSnapshot.Reset();
        }
        else
        {
            var txDiscount = InvoiceMasterRepository.GetFirstInvoiceTransactionInShiftByTypeFlag(
                InvoiceId,
                InvoiceTransactionType.CashDiscount,
                TransactionFlag.InitialOperation);

            if (invoice != null)
            {
                CurrentInvoiceSnapshot.GrossPayable = invoice.GrossPayable;
                CurrentInvoiceSnapshot.NetPayable = invoice.NetPayable;
                CurrentInvoiceSnapshot.DiscountAmount = invoice.DiscountAmount;
                CurrentInvoiceSnapshot.DueAmount = invoice.DueAmount;
                CurrentInvoiceSnapshot.PaidAmount = invoice.PaidAmount;
                CurrentInvoiceSnapshot.RefundAmount = invoice.RefundAmount;
                CurrentInvoiceSnapshot.SurchargeAmount = invoice.SurchargeAmount;
                CurrentInvoiceSnapshot.TaxAmount = invoice.TaxAmount;
            }

            CurrentInvoiceSnapshot.AdjustFinances();
            DiscountRemarks = txDiscount != null ? txDiscount.UserRemarks : "";
        }
    }

    public void UpdateOrderedTestNames(short labId)
    {
        var list = OrderedTestsRepository.GetOrderedTestNamesByPerformingLab(InvoiceId, labId);
        if (list is { Count: > 0 }) OrderedTestNames = string.Join(",", list);
    }

    public void SwitchPrimalToCurrentSnapshot() => PrimalInvoiceSnapshot = CurrentInvoiceSnapshot;
}