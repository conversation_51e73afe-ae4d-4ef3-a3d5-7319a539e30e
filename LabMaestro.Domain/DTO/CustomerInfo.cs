﻿using System;
using System.Linq;
using System.Text.RegularExpressions;
using LabMaestro.Shared;

namespace LabMaestro.Domain;

public sealed class CustomerInfo
{
    public long Id { get; set; } = -1;
    public bool IsActive { get; set; }
    public string UPIN { get; set; }
    public string Login { get; set; }
    public string FullName => string.Join(" ", [FirstName, LastName]).Trim();
    public string? Title { get; set; }
    public string FirstName { get; set; }
    public string? LastName { get; set; }
    public string? Age { get; set; }
    public string Phone { get; set; }
    public string? Email { get; set; }
    public DateTime? DoB { get; set; }
    public SexType Sex { get; set; }
    public short? CorporateClientId { get; set; } = null;
    public long? SubscriptionId { get; set; } = null;
    public long? InvoiceId { get; set; } = null;
    public int NumOrders { get; set; }
    public decimal CLV { get; set; }
    public DateTime EnrolledOn { get; set; }
    public short? EnrollingUserId { get; set; }
    public bool HasDoB() => DoB.HasValue;
    public bool HasCorporateClient() => CorporateClientId.HasValue;
    public void SetUPIN(string upin) => UPIN = upin;
    public string GetName() => FirstName;

    public static CustomerInfo AssembleFrom(Customer customer) =>
        new()
        {
            Id = customer.Id,
            IsActive = customer.IsActive,
            UPIN = customer.UPIN,
            Login = customer.Login,
            Title = customer.Title,
            FirstName = customer.FirstName,
            LastName = customer.LastName,
            Age = customer.Age,
            Phone = customer.Phone,
            Email = customer.Email,
            DoB = customer.DoB,
            Sex = (SexType)customer.Sex,
            CorporateClientId = customer.CorporateClientId,
            NumOrders = customer.NumOrders,
            CLV = customer.CLV,
            EnrolledOn = customer.EnrolledOn,
            EnrollingUserId = customer.EnrollingUserId,
        };

    string sanitizeName(string s)
    {
        s = Regex.Replace(s.Trim(), "[^A-Za-z]", "");
        return s.ToLowerInvariant();
    }

    string GetYoBString() => DoB.HasValue ? DoB.Value.Year.ToString() : string.Empty;

    public void AutoGenerateLoginName()
    {
        var fname = sanitizeName(FirstName);
        var lname = sanitizeName(LastName);

        if (!string.IsNullOrEmpty(fname))
        {
            if (fname.Length > 4)
            {
                Login = fname;
                return;
            }

            if (DoB.HasValue)
            {
                Login = $"{fname}{GetYoBString()}";
                return;
            }
        }
        else if (!string.IsNullOrEmpty(lname))
        {
            if (lname.Length > 4)
            {
                Login = lname;
                return;
            }

            if (DoB.HasValue)
            {
                Login = $"{lname}{GetYoBString()}";
                return;
            }
        }
        else if (!string.IsNullOrEmpty(Phone))
        {
            Login = SharedUtilities.RandomStringSafe(4) + Phone.Substring(Phone.Length - 4);
            return;
        }
        else if (!string.IsNullOrEmpty(Email))
        {
            Login = Email.Split(['@']).ElementAtOrDefault(0).ToUpperInvariant() + GetYoBString();
            return;
        }

        Login = SharedUtilities.RandomStringSafe(6) + GetYoBString();
    }
}