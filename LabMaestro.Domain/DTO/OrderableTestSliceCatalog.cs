﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: OrderableTestSliceCatalog.cs 1248 2014-03-30 12:41:12Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System.Collections.Generic;
using System.Linq;
using C1.LiveLinq;
using C1.LiveLinq.Collections;
using LabMaestro.Shared;

namespace LabMaestro.Domain;

/// <summary>
///     Container of OrderableTestSlice Allows searching of tests by test name, mnemonics Caches the search results for
///     improved performance
/// </summary>
public class OrderableTestSliceCatalog : IndexedCollection<OrderableTestSlice>
{
    private const string CacheKeyPrefixMnemonic = @"OrderableTestSliceCatalog_mnemonic_";
    private const string CacheKeyPrefixTestName = @"OrderableTestSliceCatalog_test_";

    public void LoadFromDatabase()
    {
    }

    public OrderableTestSlice FindById(short testId)
    {
        return this.AsIndexed().SingleOrDefault(x => x.TestId == testId);
    }

    internal OrderableTestSlice AddSlice(OrderableTestsSlice catalog)
    {
        var slice = new OrderableTestSlice(catalog);
        Add(slice);
        return slice;
    }

    public List<OrderableTestSlice> FindTestsByName(string name)
    {
        name = name.Trim().ToLowerInvariant();
        var key = CacheKeyPrefixTestName + name;

        var foundTests = (List<OrderableTestSlice>)AppCache.Get(key);
        if (foundTests == null || foundTests?.Count == 0)
        {
            foundTests = this.AsIndexed()
                .Where(x => StringMatch(x.ShortName, name))
                .ToList();

            if (foundTests != null && foundTests.Count > 0)
                AppCache.Store(key, foundTests);
        }

        return foundTests;
    }

    public OrderableTestSlice FindTestByName(string name)
    {
        name = name.Trim();
        var foundTest = this.AsIndexed().SingleOrDefault(x => string.CompareOrdinal(x.ShortName, name) == 0);
        return foundTest;
    }

    //public IEnumerable<OrderableTestSlice> FindTestsByMnemonic(string mnemonic)
    //{
    //    mnemonic = mnemonic.Trim().ToLowerInvariant();
    //    var key = CacheKeyPrefixMnemonic + mnemonic;

    //    var foundTests = (List<OrderableTestSlice>)_cacheManager.GetData(key);
    //    if (foundTests == null)
    //    {
    //        foundTests = this.AsParallel()
    //            .Where(s => s.Mnemonics.Any(m => StringMatch(m, mnemonic)))
    //            .ToList();

    //        _cacheManager.Add(key, foundTests);
    //    }

    //    return foundTests;
    //}

    private static bool StringMatch(string catalogItem, string searchItem)
    {
        return string.CompareOrdinal(catalogItem, searchItem) == 0 ||
               catalogItem.StartsWith(searchItem) || catalogItem.Contains(searchItem);
    }
}