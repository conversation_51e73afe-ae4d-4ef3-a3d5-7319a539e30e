// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: BillableItemSlice.cs 723 2013-07-03 06:56:29Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using AutoMapper;
using LabMaestro.Shared;

namespace LabMaestro.Domain;

public sealed class BillableItemSlice
{
    public short Id { get; set; } = -1;

    public string Name { get; set; }

    public decimal UnitPrice { get; set; } = 0m;

    public decimal LineTotal { get; set; } = 0m;

    public short Quantity { get; set; } = 0;

    public bool IsActive { get; set; } = false;

    public bool IsCancelled { get; set; } = false;

    public long OrderedItemId { get; set; } = -1;

    public BillableItemOptimizationLevelType OptimizationLevel { get; set; }

    public static BillableItemSlice AssembleFrom(OrderedBillableItemInInvoiceSlice item)
    {
        Mapper.CreateMap<OrderedBillableItemInInvoiceSlice, BillableItemSlice>()
            .ForMember(dest => dest.Id, x => x.MapFrom(src => src.BillableItemId));
        return Mapper.Map<OrderedBillableItemInInvoiceSlice, BillableItemSlice>(item);
    }

    public static BillableItemSlice AssembleFrom(BillableItem item)
    {
        Mapper.CreateMap<BillableItem, BillableItemSlice>();
        return Mapper.Map<BillableItem, BillableItemSlice>(item);
    }

    public static BillableItemSlice AssembleFrom(OrderableBillableItemsSlice item)
    {
        Mapper.CreateMap<OrderableBillableItemsSlice, BillableItemSlice>();
        return Mapper.Map<OrderableBillableItemsSlice, BillableItemSlice>(item);
    }

    public static BillableItemSlice AssembleFrom(BillableItemSlice source) =>
        new()
        {
            Id = source.Id,
            Name = source.Name,
            UnitPrice = source.UnitPrice,
            Quantity = source.Quantity,
            OptimizationLevel = source.OptimizationLevel,
            LineTotal = source.UnitPrice * source.Quantity
        };

    public static BillableItemSlice AssembleFrom(OrderableTestsWithBillableItemsSlice dto) =>
        new()
        {
            Id = dto.ItemId,
            Name = dto.ItemName,
            UnitPrice = dto.UnitPrice,
            Quantity = dto.Quantity,
            OptimizationLevel = (BillableItemOptimizationLevelType)dto.OptimizationLevel,
            LineTotal = dto.UnitPrice * dto.Quantity
        };

    public override string ToString() => $"{Name} x {Quantity.ToString()} @ {OptimizationLevel.ToString()}";
}