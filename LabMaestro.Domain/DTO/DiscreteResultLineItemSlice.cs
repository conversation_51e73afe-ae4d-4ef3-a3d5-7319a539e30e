// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2012, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//  * Redistributions of source code must retain the above copyright
//    notice, this list of conditions and the following disclaimer.
//  * Redistributions in binary form must reproduce the above copyright
//    notice, this list of conditions and the following disclaimer in the
//    documentation and/or other materials provided with the distribution.
//  * Neither the name of the <organization> nor the
//    names of its contributors may be used to endorse or promote products
//    derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: DiscreteResultLineItemSlice.cs 516 2013-04-17 08:03:02Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using LabMaestro.Shared;

namespace LabMaestro.Domain;

public sealed class DiscreteResultLineItemSlice
{
    public DiscreteResultLineItemSlice(long id, OrderedTestSlice testSlice, bool isResultable, int sortOrder,
        int indentLevel, string param, string units, string refRange)
    {
        IndentLevel = indentLevel;
        SortOrder = sortOrder;
        IsResultable = isResultable;
        Id = id;
        OrderedTest = testSlice;
        Units = units;
        RawParameter = param;
        ReferenceRange = refRange;
        HashOriginal = calcCrc32();
        IsMetadataDirty = false;
    }

    public long Id { get; private set; }

    //public long OrderedTestId { get; private set; }
    //public long ResultBundleId { get; private set; }

    public bool IsResultable { get; private set; }

    public int SortOrder { get; private set; }

    public int IndentLevel { get; }

    public bool IsMetadataDirty { get; private set; }

    public uint HashOriginal { get; }

    public uint HashCurrent
    {
        get { return calcCrc32(); }
    }

    public string Parameter
    {
        get { return getFormattedParameter(); }
    }

    public string RawParameter { get; private set; }

    public string Units { get; private set; }
    public string Result { get; set; }

    public string ReferenceRange { get; private set; }

    public string Flag { get; private set; }

    public OrderedTestSlice OrderedTest { get; private set; }

    private uint calcCrc32()
    {
        return SharedUtilities.CalcCrc32Hash(RawParameter + Units + ReferenceRange);
    }

    private string getFormattedParameter()
    {
        return RawParameter.PadLeft(IndentLevel * 2 + RawParameter.Length);
    }

    public void Update(string param, string unit, string refrange)
    {
        RawParameter = param;
        Units = unit;
        ReferenceRange = refrange;
        IsMetadataDirty = HashOriginal != calcCrc32();
    }

    public static DiscreteResultLineItemSlice AssembleFrom(GetDiscreteResultLineItemsForOrderedTest item,
        OrderedTestSlice testSlice)
    {
        var result = new DiscreteResultLineItemSlice(item.LineItemId,
                testSlice,
                item.IsResultableItem,
                item.SortOrder,
                item.IndentLevel,
                item.Parameter,
                item.Units,
                item.ReferenceRange)
            { Result = item.Result, Flag = item.Flag };
        return result;
    }
}