// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: WorkShift.cs 1376 2014-06-20 13:54:04Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using CuttingEdge.Conditions;
using LabMaestro.Shared;
using IbEm = IdeaBlade.EntityModel;

namespace LabMaestro.Domain;

// The IdeaBlade DevForce EDM Designer Extension generates this class once 
// and will not overwrite any changes you make.  You can place your custom 
// application-specific business logic in this file. Generated: 5/19/2012 12:48:57 AM
public partial class WorkShift
{
    public static bool VerifyShiftIsValidAndActive(WorkShift shift)
    {
        if (shift == null)
            return false;

        if (shift.EntityAspect.EntityState == IbEm.EntityState.Detached ||
            shift.EntityAspect.EntityState == IbEm.EntityState.Deleted)
            return false;

        var hasValidUser = shift.User != null;
        if (!hasValidUser)
            hasValidUser = shift.UserId > 0;

        return !shift.IsClosed && hasValidUser;
    }

    internal void RegisterTransaction(InvoiceTransactionType txType, decimal txAmount, decimal nonCashAmount)
    {
        if (IsClosed)
            throw new InvalidOperationException("Financial re-adjustment is not permitted on finalized shifts!");

        var cash = 0m;
        var cashless = 0m;
        var discount = 0m;
        var discountRebate = 0m;
        var refund = 0m;
        switch (txType)
        {
            case InvoiceTransactionType.Payment:
                cash += txAmount;
                cashless += nonCashAmount;
                break;
            case InvoiceTransactionType.Refund:
                refund += txAmount;
                //receive -= amount;
                break;
            case InvoiceTransactionType.CashDiscount:
                discount += txAmount;
                break;
            case InvoiceTransactionType.DiscountRebate:
                discountRebate += txAmount;
                break;
        }

        ReceiveAmount += cash;
        NonCashAmount += cashless;
        DiscountAmount += discount;
        DiscountRebateAmount += discountRebate;
        RefundAmount += refund;
        FinalBalance = AdditionalBalance + cash - refund;

        //TODO: NumOrders is outdated. Introduce List<long> property to track changes?
        // It will be Clear()-ed on ReconcileShiftFinances()
    }

    public void ReconcileShiftFinances()
    {
        if (IsClosed)
            throw new InvalidOperationException("Financial re-adjustment is not permitted on finalized shifts!");

        var transactions = InvoiceTransactionsRepository.GetTransactionsInShiftDetailedEx(Id);
        var cash = 0m;
        var cashLess = 0m;
        var discount = 0m;
        var rebate = 0m;
        var refund = 0m;
        var uniqueOrders = new List<long>();

        foreach (var tx in transactions)
        {
            //if (PatientLabOrder.VerifyOrderIsValidAndActive(PatientLabOrdersRepository.FindById(tx.InvoiceId))){}
            if (!uniqueOrders.Contains(tx.InvoiceId)) uniqueOrders.Add(tx.InvoiceId);

            switch ((InvoiceTransactionType)tx.TxType)
            {
                case InvoiceTransactionType.Payment:
                    cash += tx.TxAmount;
                    cashLess += tx.NonCashAmount;
                    break;
                case InvoiceTransactionType.Refund:
                    refund += tx.TxAmount;
                    //receive -= tx.TxAmount;
                    break;
                case InvoiceTransactionType.CashDiscount:
                    discount += tx.TxAmount;
                    break;
                case InvoiceTransactionType.DiscountRebate:
                    rebate += tx.TxAmount;
                    break;
            }
        }

        NumOrders = (short)uniqueOrders.Count;
        ReceiveAmount = cash;
        NonCashAmount = cashLess;
        DiscountAmount = discount;
        DiscountRebateAmount = rebate;
        RefundAmount = refund;
        FinalBalance = AdditionalBalance + cash - refund;

        Condition.Ensures(FinalBalance).IsGreaterOrEqual(0m);
    }

    /*
    public IEnumerable<ShiftInvoiceSummary> FetchAdjustedInvoiceSummariesInShift()
    {
        var transactionsList = new IndexedCollection<SimpleInvoiceTransactionInfo>();
        transactionsList.BeginUpdate();
        transactionsList.AddRange(InvoiceTransactionsRepository.GetTransactionsInShift(Id));
        transactionsList.EndUpdate();
        var invoiceSummaries = InvoiceMasterRepository.GetInvoiceSummariesInShift(Id);
        foreach (var summary in invoiceSummaries)
        {
            var tmp = summary;
            var transactions = transactionsList.Where(x => x.InvoiceId == tmp.InvoiceId);

            var payment = 0m;
            var discount = 0m;
            decimal rebate = 0m;
            var refund = 0m;
            foreach (var tx in transactions)
            {
                switch ((InvoiceTransactionType)tx.TxType)
                {
                    case InvoiceTransactionType.Payment:
                        payment += tx.TxAmount;
                        break;
                    case InvoiceTransactionType.Refund:
                        refund += tx.TxAmount;
                        //receive -= tx.TxAmount;
                        break;
                    case InvoiceTransactionType.CashDiscount:
                        discount += tx.TxAmount;
                        break;
                    case InvoiceTransactionType.DiscountRebate:
                        rebate += tx.TxAmount;
                        break;
                }
            }

            summary.PaidAmount = payment;
            summary.DiscountAmount = discount - rebate;
            summary.RefundAmount = refund;
        }

        return invoiceSummaries;
    }
    */
}