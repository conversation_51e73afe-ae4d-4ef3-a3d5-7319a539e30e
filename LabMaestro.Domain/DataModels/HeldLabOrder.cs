// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: HeldLabOrder.cs 1378 2014-06-20 14:44:36Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

//using Newtonsoft.Json;

using System.Collections.Generic;

namespace LabMaestro.Domain;

// The IdeaBlade DevForce EDM Designer Extension generates this class once 
// and will not overwrite any changes you make.  You can place your custom 
// application-specific business logic in this file. Generated: 5/19/2012 12:48:57 AM
public partial class HeldLabOrder
{
    public HeldLabOrder()
    {
        OrderedTestSlices = new List<OrderableTestSlice>();
    }

    public List<OrderableTestSlice> OrderedTestSlices { get; private set; }
    /*
    internal void Deserialize()
    {
        OrderedTestSlices.Clear();
        var orderDto = JsonConvert.DeserializeObject<HeldLabOrderDto>(OrderedTests);
        if (orderDto != null)
        {
            foreach (var testId in orderDto.TestIdList)
            {
                var test = OrderableTestSliceCatalogRepository.FindById(testId);
                if (test != null)
                    OrderedTestSlices.Add(test);
            }
        }
    }

    internal void Serialize()
    {
        var orderDto = new HeldLabOrderDto();

        foreach (var test in OrderedTestSlices)
        {
            orderDto.TestIdList.Add(test.TestId);
        }

        OrderedTests = JsonConvert.SerializeObject(orderDto);
    }
    */
}

internal class HeldLabOrderDto
{
    internal HeldLabOrderDto()
    {
        TestIdList = new List<short>();
    }

    //[JsonProperty("TestIdList")]
    internal List<short> TestIdList { get; private set; }
}