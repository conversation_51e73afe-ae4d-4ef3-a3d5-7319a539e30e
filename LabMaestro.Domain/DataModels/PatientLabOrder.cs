//  * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
//  
//  Copyright (c) 2012, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
//  All rights reserved.
//  
//  Redistribution and use in source and binary forms, with or without
//  modification, are permitted provided that the following conditions are met:
//     * Redistributions of source code must retain the above copyright
//       notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above copyright
//       notice, this list of conditions and the following disclaimer in the
//       documentation and/or other materials provided with the distribution.
//     * Neither the name of the <organization> nor the
//       names of its contributors may be used to endorse or promote products
//       derived from this software without specific prior written permission.
//  
//  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
//  ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
//  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
//  DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
//  DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
//  (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
//  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
//  ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
//  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
//  SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
//  
//  $Id: PatientLabOrder.cs 1248 2014-03-30 12:41:12Z <EMAIL> $
//  
//  * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.IO;
using System.Linq;
using CuttingEdge.Conditions;
using LabMaestro.Shared;
using IbEm = IdeaBlade.EntityModel;

namespace LabMaestro.Domain;

// The IdeaBlade DevForce EDM Designer Extension generates this class once 
// and will not overwrite any changes you make.  You can place your custom 
// application-specific business logic in this file. Generated: 5/19/2012 12:48:57 AM
public partial class PatientLabOrder : IbEm.Entity
{
    private SmartDate _smartDoB;

    public SmartDate SmartDoB
    {
        get
        {
            ensureSmartDoB();
            return _smartDoB;
        }
    }

    private void ensureSmartDoB()
    {
        if (_smartDoB == null) _smartDoB = new SmartDate(DoB, true);
    }

    public static bool VerifyOrderIsValidAndActive(PatientLabOrder order)
    {
        return order != null
               && order.EntityAspect.EntityState != IbEm.EntityState.Detached
               && order.EntityAspect.EntityState != IbEm.EntityState.Deleted
               && !order.IsCancelled;
    }

    public OrderedTest OrderLabTest(short testId)
    {
        Condition.Requires(testId).IsGreaterThan(0);
        return OrderLabTest(LabTestsRepository.FindById(testId));
    }

    public OrderedTest OrderLabTest(LabTest test)
    {
        Condition
            .Requires(test)
            .IsNotNull();

        if (OrderedTests.Any(x => x.LabTestId == test.Id)) throw new Exception(@"The test had already been ordered");

        if (!LabTestsRepository.TestIsOrderable(test.Id))
            throw new InvalidDataException("The desired test is either deleted, inactive or non-orderable");

        var orderedTest = OrderedTestsRepository.OrderNewTest(this, test.Id);
        OrderedTests.Add(orderedTest);

        //AuditTrailRepository.LogLabOrderEvent(this, AuditEventType.ordNewTestOrdered, WorkflowStageType.OrderEntry, orderedTest);

        return orderedTest;
    }

    public OrderedBillableItem AdjustOrAddBillableItem(short itemId, short qty, bool cancelled)
    {
        var item = OrderedBillableItems.SingleOrDefault(x => x.BillableItemId == itemId);
        if (item != null)
        {
            item.IsCancelled = cancelled;
            item.Quantity = qty;
            return item;
        }

        var bi = BillableItemsRepository.FindActiveItemById(itemId);
        return AddBillableItem(bi, qty, cancelled);
    }

    public OrderedBillableItem AddBillableItem(BillableItem bi, short qty, bool cancelled)
    {
        Condition
            .Requires(bi)
            .IsNotNull();
        //.Evaluate(x => x.EntityAspect.EntityState == EntityState.AllButDetached);

        var item = OrderedBillableItemsRepository.OrderItem(this, bi, qty, cancelled);
        OrderedBillableItems.Add(item);
        return item;
    }

    public void CancelLabTest(OrderedTest test)
    {
        Condition
            .Requires(test)
            .IsNotNull();
        //.Evaluate(x => x.EntityAspect.EntityState == EntityState.AllButDetached);
        test.CancelTest();
        AuditTrailRepository.LogLabOrderEvent(this, AuditEventType.ordTestCanceled, test: test);
    }

    internal void AdjustInvoice()
    {
        var gross = OrderedTests.Where(x => !x.IsCancelled).Sum(x => x.UnitPrice);

        //TODO: What if discount is greater than gross? net will be a negative amount
        //TODO: Calculate tax amount
        InvoiceMaster.GrossPayable = gross;

        /*var net = gross - InvoiceMaster.DiscountAmount - InvoiceMaster.TaxAmnount;
        InvoiceMaster.NetPayable = net;
        InvoiceMaster.DueAmount = net - InvoiceMaster.PaidAmount;*/
        InvoiceMaster.ReconcileInvoiceFinances(true);
    }

    public string GetAgeOrDoBPrintLabel() => SmartDoB.IsEmpty ? "Age" : "DoB";

    public string GetAgeOrDoB() => SmartDoB.IsEmpty ? Age : SharedUtilities.DateToStringOnlySlash(SmartDoB.Date);
    //return (DoB != null) ? SharedUtilities.DateToStringOnlySlash((DateTime) DoB) : Age;

    public string GetReferringPhysicianName()
    {
        if (!string.IsNullOrEmpty(ReferrerCustomName)) return ReferrerCustomName;
        return !IsReferrerUnknown && Referrer != null ? Referrer.FullName : string.Empty;
    }

    public string GetOrderingUserDisplayName() => User != null ? User.DisplayName : string.Empty;

    /// <summary>
    ///     Determines if the lab order is from a past date.
    /// </summary>
    /// <param name="currentDate">
    ///     The current date. Specify DateTime.MinValue to fetch the
    ///     current time from the server
    /// </param>
    public bool LabOrderIsFromPastDate(DateTime currentDate)
    {
        if (currentDate == DateTime.MinValue)
            currentDate = AppSysRepository.GetServerTime();

        var diff = currentDate.Subtract(OrderDateTime);
        return diff.Days > 0;
        //return (OrderDateTime < currentDate);
    }
}