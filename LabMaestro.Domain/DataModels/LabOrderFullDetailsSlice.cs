﻿using LabMaestro.Shared;
using IbEm = IdeaBlade.EntityModel;

namespace LabMaestro.Domain
{
    public partial class LabOrderFullDetailsSlice : IbEm.ComplexObject
    {
        private SmartDate _smartDoB;

        public SmartDate SmartDoB
        {
            get
            {
                ensureSmartDoB();
                return _smartDoB;
            }
        }

        private void ensureSmartDoB()
        {
            if (_smartDoB == null) 
                _smartDoB = new SmartDate(DoB, true);
        }

        public string GetAgeOrDoBPrintLabel() => SmartDoB.IsEmpty ? "Age" : "DoB";

        public string GetAgeOrDoB() => SmartDoB.IsEmpty ? Age : SharedUtilities.FullDateToStringOnlySlash(SmartDoB.Date);

    }
}
