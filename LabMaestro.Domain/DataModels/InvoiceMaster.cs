// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2013, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: InvoiceMaster.cs 830 2013-07-18 08:15:23Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using CuttingEdge.Conditions;
using LabMaestro.Shared;
using IbEm = IdeaBlade.EntityModel;

namespace LabMaestro.Domain;

// The IdeaBlade DevForce EDM Designer Extension generates this class once 
// and will not overwrite any changes you make.  You can place your custom 
// application-specific business logic in this file. Generated: 5/19/2012 12:48:57 AM
public partial class InvoiceMaster : IbEm.Entity
{
    public InvoiceMaster(PatientLabOrder order) : this() => PatientLabOrder = order;

    public InvoiceMaster()
    {
        DiscountAmount = 0m;
        DueAmount = 0m;
        GrossPayable = 0m;
        NetPayable = 0m;
        TaxAmount = 0m;
        RefundAmount = 0m;
        PaymentStatus = (byte)PaymentStatusType.UnPaid;
    }

    /// <summary>
    ///     This method checks if the: * associated lab order is valid * shift is valid and open
    /// </summary>
    private void PreOperativeValidation(WorkShift shift)
    {
        Condition
            .Requires(PatientLabOrder)
            .IsNotNull()
            .Evaluate(x => PatientLabOrder.VerifyOrderIsValidAndActive(x));
        Condition
            .Requires(shift)
            .IsNotNull()
            .Evaluate(x => WorkShift.VerifyShiftIsValidAndActive(x));
    }

    private void performInvoiceTransaction(
        WorkShift shift,
        InvoiceTransactionType txType,
        TransactionFlag txFlag,
        decimal txAmount,
        string note,
        short? authorizingUserId,
        WorkflowStageType workflowStage,
        PaymentMethod method = PaymentMethod.Cash,
        decimal nonCashAmount = 0m,
        string? pmtSrc = null,
        string? pmtRef = null
    )
    {
        InvoiceTransaction tx;
        if (method != PaymentMethod.Cash && txType == InvoiceTransactionType.Payment)
        {
            // non-cash payment
            tx = InvoiceTransactionsRepository.CreateNewTransactionEx(
                this,
                shift.UserId,
                authorizingUserId,
                shift.Id, txType,
                txFlag, txAmount, note,
                AuditTrailRepository.CurrentIpAddress,
                nonCashAmount,
                method,
                pmtSrc,
                pmtRef
            );
        }
        else
        {
            tx = InvoiceTransactionsRepository.CreateNewTransaction(this, shift.UserId, authorizingUserId,
                shift.Id, txType,
                txFlag, txAmount, note,
                AuditTrailRepository.CurrentIpAddress);
        }

        InvoiceTransactions.Add(tx);

        shift.RegisterTransaction(txType, txAmount, nonCashAmount);

        // attach user, shift
        // audit trail it
        var evType = txType switch
        {
            InvoiceTransactionType.Refund => AuditEventType.finRefundAuthorized,
            InvoiceTransactionType.Payment => AuditEventType.finPaymentReceived,
            InvoiceTransactionType.CashDiscount => AuditEventType.finDiscountIssued,
            InvoiceTransactionType.DiscountRebate => AuditEventType.finDiscountRebateIssued,
            _ => AuditEventType.None
        };

        // It is the callers responsibility to update the audit log
        AuditTrailRepository.LogFinancialEvent(PatientLabOrder, evType, txAmount + nonCashAmount, workflowStage);

        //InvoiceMasterRepository.Save();

        // adjust finances
        //ReconcileInvoiceFinances(true);
    }

    public void IssueRefund(WorkShift shift, decimal amount, TransactionFlag txFlag, string note,
        short authorizingUserId, WorkflowStageType workflowStage)
    {
        PreOperativeValidation(shift);

        Condition
            .Requires(amount)
            .IsGreaterThan(0m, "Amount must be a positive number")
            .IsLessOrEqual(PaidAmount, "Refund amount must be less than or equal to the paid amount");

        performInvoiceTransaction(shift, InvoiceTransactionType.Refund, txFlag, amount, note, authorizingUserId,
            workflowStage);
    }

    public void IssuePayment(WorkShift shift, decimal amount, TransactionFlag txFlag, string note,
        WorkflowStageType workflowStage)
    {
        PreOperativeValidation(shift);

        /* Business logic:
         * amount must be > 0
         * amount must be less than or equal to DueAmount
         */
        Condition
            .Requires(amount)
            .IsGreaterOrEqual(0m, "Amount must be a positive number")
            .IsLessOrEqual(DueAmount, "Payment amount must be less than or equal to the due amount");

        performInvoiceTransaction(shift, InvoiceTransactionType.Payment, txFlag, amount, note, null, workflowStage);
    }

    public void IssuePaymentEx(WorkShift shift, decimal txAmount, TransactionFlag txFlag, string note,
        WorkflowStageType workflowStage,
        decimal nonCashAmount,
        PaymentMethod method,
        string? pmtSrc = null,
        string? pmtRef = null
    )
    {
        PreOperativeValidation(shift);

        var amount = txAmount + nonCashAmount;
        Condition
            .Requires(amount)
            .IsGreaterOrEqual(0m, "Amount must be a positive number")
            .IsLessOrEqual(DueAmount, "Payment amount must be less than or equal to the due amount");

        performInvoiceTransaction(
            shift,
            InvoiceTransactionType.Payment,
            txFlag,
            txAmount,
            note,
            null,
            workflowStage,
            method,
            nonCashAmount,
            pmtSrc,
            pmtRef
        );
    }

    public void IssueDiscount(WorkShift shift, decimal amount, TransactionFlag txFlag, string note,
        WorkflowStageType workflowStage)
    {
        PreOperativeValidation(shift);

        Condition
            .Requires(amount)
            .IsGreaterThan(0m, "Amount must be a positive number");

        performInvoiceTransaction(shift, InvoiceTransactionType.CashDiscount, txFlag, amount, note, null,
            workflowStage);
    }

    public void IssueDiscountRebate(WorkShift shift, decimal amount, TransactionFlag txFlag, string note,
        WorkflowStageType workflowStage)
    {
        PreOperativeValidation(shift);

        Condition
            .Requires(amount)
            .IsGreaterThan(0m, "Amount must be a positive number");

        performInvoiceTransaction(shift, InvoiceTransactionType.DiscountRebate, txFlag, amount, note, null,
            workflowStage);
    }

    public void ReconcileInvoiceFinances(bool updatePaymentStatus)
    {
        decimal paid = 0;
        decimal discount = 0;
        decimal discountRebate = 0;
        decimal refund = 0;

        var transactions = DomainManager.EntityManager.SP_GetTransactionsInInvoiceDetailedEx(InvoiceId);

        foreach (var tx in transactions)
            switch ((InvoiceTransactionType)tx.TxType)
            {
                case InvoiceTransactionType.Payment:
                    paid += tx.TxAmount + tx.NonCashAmount;
                    break;
                case InvoiceTransactionType.Refund:
                    refund += tx.TxAmount;
                    break;
                case InvoiceTransactionType.CashDiscount:
                    discount += tx.TxAmount;
                    break;
                case InvoiceTransactionType.DiscountRebate:
                    discountRebate += tx.TxAmount;
                    break;
            }

        RefundAmount = refund;
        PaidAmount = paid - refund;
        DiscountAmount = discount - discountRebate;
        NetPayable = GrossPayable - DiscountAmount - TaxAmount;
        DueAmount = NetPayable - PaidAmount;

        if (updatePaymentStatus)
            AdjustPaymentStatus();
    }

    public void AdjustPaymentStatus()
    {
        if (DueAmount == 0 && PaidAmount == NetPayable)
            PaymentStatus = (byte)PaymentStatusType.PaidInFull;
        else if (DueAmount == NetPayable)
            PaymentStatus = (byte)PaymentStatusType.UnPaid;
        else if (PaidAmount > NetPayable)
            PaymentStatus = (byte)PaymentStatusType.OverPaid;
        else
            PaymentStatus = (byte)PaymentStatusType.PartiallyPaid;
    }

    public bool IsInvoicePaidInFull() => PaymentStatus == (byte)PaymentStatusType.PaidInFull && DueAmount == 0m;
}