﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2014, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//    * Neither the name of the <organization> nor the
//      names of its contributors may be used to endorse or promote products
//      derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id: MainForm.cs 1421 2014-10-01 05:57:39Z <EMAIL> $
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Windows.Forms;
using LabMaestro.BulkSms.Core;

namespace LabMaestro.BulkSms.ControlPanel
{
    public partial class MainForm : Form
    {
        public MainForm()
        {
            InitializeComponent();
        }

        private void loadConfig()
        {
            var qr = new SmsQueueRunner(false);
            var conf = qr.ServiceConfig;
            edThreads.Value = conf.MaxThreadCount;
            txtRootFolder.Text = conf.SmsQueue.RootFolder;
            txtOriginator.Text = conf.SmsQueue.SmsOriginator;
            txtPassword.Text = conf.SmsQueue.SmsPassword;
            txtUsername.Text = conf.SmsQueue.SmsUsername;
            txtUri.Text = conf.SmsQueue.SmsGateway;
        }

        private void saveConfig()
        {
            var qr = new SmsQueueRunner(false);
            var conf = qr.ServiceConfig;

            conf.MaxThreadCount = (int) edThreads.Value;
            conf.SmsQueue.RootFolder = txtRootFolder.Text.Trim();
            conf.SmsQueue.SmsGateway = txtUri.Text.Trim();
            conf.SmsQueue.SmsUsername = txtUsername.Text.Trim();
            conf.SmsQueue.SmsPassword = txtPassword.Text.Trim();
            conf.SmsQueue.SmsOriginator = txtOriginator.Text.Trim();
            conf.Save(Utilities.DefaultConfigFilename);
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            saveConfig();
        }

        private void btnLoad_Click(object sender, EventArgs e)
        {
            loadConfig();
        }

        private void btnFolder_Click(object sender, EventArgs e)
        {
            var folder = txtRootFolder.Text.Trim();
            if (string.IsNullOrEmpty(folder))
                folder = Utilities.AssemblyDirectory;
            folderBrowserDialog.SelectedPath = folder;
            if (folderBrowserDialog.ShowDialog(this) == DialogResult.OK)
            {
                txtRootFolder.Text = folderBrowserDialog.SelectedPath;
            }
        }

        private void btnDirsCreate_Click(object sender, EventArgs e)
        {
            var _qr = new SmsQueueRunner(false);
            _qr.SetRootFolderIfNotSet();
            _qr.CreateDirectoryStructure();
        }
    }
}