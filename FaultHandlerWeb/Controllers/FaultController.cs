﻿using System.Net;
using System.Web.Mvc;
using LabMaestro.ServiceDAL;
using LabMaestro.Shared.AppFaults;

namespace FaultHandlerWeb.Controllers
{
    public class FaultController : Controller
    {
        [HttpGet]
        public ActionResult Index()
        {
            return new ContentResult
            {
                Content = "faults here"
            };
        }

        [HttpPost]
        public ActionResult PostAppFault(AppFaultData data)
        {
            try
            {
                using var db = DataAccessFactory.GetClient();
                db.InsertAppFaultData(data);
            }
            catch
            {
                // yawn
            }

            return new HttpStatusCodeResult(HttpStatusCode.NoContent);
        }
    }
}