﻿using FaultHandlerWeb;
using FaultHandlerWeb.Properties;
using Hangfire;
using LabMaestro.ServiceDAL;
using Microsoft.Owin;
using Owin;

[assembly: OwinStartup(typeof(Startup))]

namespace FaultHandlerWeb
{
    public class Startup
    {
        public void Configuration(IAppBuilder app)
        {
            DataAccessFactory.SetConnectionString(Settings.Default.serviceDb);
            
            GlobalConfiguration.Configuration
                .UseSqlServerStorage(Settings.Default.jobsDb);

            app.UseHangfireDashboard("");
        }
    }
}