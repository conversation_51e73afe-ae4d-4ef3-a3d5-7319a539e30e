﻿// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
// 
// Copyright (c) 2015, Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
// * Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the distribution.
// * Neither the name of the <organization> nor the
//   names of its contributors may be used to endorse or promote products
//   derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// 
// $Id$
// 
// Last modified: 2015.09.01 1:28 PM
// 
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

using System;
using System.Collections.Generic;
using System.Configuration;
using System.Timers;
using LabMaestro.ServiceDAL;
using LabMaestro.ServiceDAL.Model;
using LabMaestro.Shared;

namespace LISIntegrator
{
    internal class Program
    {
        private const int MINUTE = 60*1000;

        private static void Main(string[] args)
        {
            var interval = 5;
            int.TryParse(ConfigurationManager.AppSettings["PollInterval"], out interval);
            Log(string.Format("Interval: {0}", interval));
            var timer = new Timer {AutoReset = true, Interval = interval*MINUTE};
            timer.Elapsed += DoWork;
            timer.Start();
            
            DoWork(null, null);
            Console.ReadLine();
            timer.Stop();
        }

        private static void Log(string msg)
        {
            Console.WriteLine("[{0}] {1}", DateTime.Now.ToString("T"), msg);
        }

        private static void DoWork(object source, ElapsedEventArgs e)
        {
            Log("Running");
            var csMaestro = ConfigurationManager
                .ConnectionStrings["LabMaestro_Sql"]
                .ConnectionString;

            var csLis = ConfigurationManager
                .ConnectionStrings["LabMIS_Sql"]
                .ConnectionString;

            var dtFrom = SharedUtilities.AbsoluteStart(DateTime.Today);
            var dtTo = SharedUtilities.AbsoluteEnd(DateTime.Today);
            List<LisLabOrderInfo> lisOrders = null;
            using (var db = DataAccessFactory.GetClient(csMaestro))
            {
                lisOrders = db.LisGetLabOrderInfoByDateRange(dtFrom, dtTo);
            }

            Log(string.Format("Got {0} orders", lisOrders.Count));

            using (var db = DataAccessFactory.GetClient(csLis))
            {
                foreach (var order in lisOrders)
                {
                    db.LisInsertInvoiceMaster(order);
                    db.LisInsertInvoiceDetail(order);
                }
            }

            Log("Done");
        }
    }
}